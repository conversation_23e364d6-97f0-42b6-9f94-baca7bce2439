{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/role-management.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction RoleBasedSettingsComponent_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function RoleBasedSettingsComponent_button_13_Template_button_click_0_listener() {\n      const section_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.setActiveSection(section_r2.id));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const section_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r2.activeSection === section_r2.id);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(section_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(section_r2.title);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(setting_r4.description);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"input\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleBasedSettingsComponent_div_15_div_7_div_6_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(setting_r4.value, $event) || (setting_r4.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function RoleBasedSettingsComponent_div_15_div_7_div_6_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSettingChange(setting_r4, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"label\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", setting_r4.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", setting_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", setting_r4.id);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_select_7_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r7.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r7.label, \" \");\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_select_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"select\", 36);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleBasedSettingsComponent_div_15_div_7_select_7_Template_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(setting_r4.value, $event) || (setting_r4.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function RoleBasedSettingsComponent_div_15_div_7_select_7_Template_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSettingChange(setting_r4, $event));\n    });\n    i0.ɵɵtemplate(1, RoleBasedSettingsComponent_div_15_div_7_select_7_option_1_Template, 2, 2, \"option\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"id\", setting_r4.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", setting_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", setting_r4.options);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_input_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleBasedSettingsComponent_div_15_div_7_input_8_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(setting_r4.value, $event) || (setting_r4.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function RoleBasedSettingsComponent_div_15_div_7_input_8_Template_input_input_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSettingChange(setting_r4, $event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"id\", setting_r4.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", setting_r4.value);\n    i0.ɵɵproperty(\"placeholder\", setting_r4.placeholder);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"input\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleBasedSettingsComponent_div_15_div_7_div_9_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(setting_r4.value, $event) || (setting_r4.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function RoleBasedSettingsComponent_div_15_div_7_div_9_Template_input_input_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSettingChange(setting_r4, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 42);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", setting_r4.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", setting_r4.value);\n    i0.ɵɵproperty(\"min\", setting_r4.min)(\"max\", setting_r4.max)(\"step\", setting_r4.step);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(setting_r4.value);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_input_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleBasedSettingsComponent_div_15_div_7_input_10_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(setting_r4.value, $event) || (setting_r4.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function RoleBasedSettingsComponent_div_15_div_7_input_10_Template_input_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSettingChange(setting_r4, $event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"id\", setting_r4.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", setting_r4.value);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_div_11_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getFileName(setting_r4.value));\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"input\", 45);\n    i0.ɵɵlistener(\"change\", function RoleBasedSettingsComponent_div_15_div_7_div_11_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onFileChange(setting_r4, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 46);\n    i0.ɵɵelement(3, \"i\", 47);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Choose File\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, RoleBasedSettingsComponent_div_15_div_7_div_11_span_6_Template, 2, 1, \"span\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", setting_r4.id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", setting_r4.id);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", setting_r4.value);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_textarea_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"textarea\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RoleBasedSettingsComponent_div_15_div_7_textarea_12_Template_textarea_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(setting_r4.value, $event) || (setting_r4.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function RoleBasedSettingsComponent_div_15_div_7_textarea_12_Template_textarea_input_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const setting_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSettingChange(setting_r4, $event));\n    });\n    i0.ɵɵtext(1, \"                \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"id\", setting_r4.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", setting_r4.value);\n    i0.ɵɵproperty(\"placeholder\", setting_r4.placeholder);\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21)(2, \"label\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, RoleBasedSettingsComponent_div_15_div_7_p_4_Template, 2, 1, \"p\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 24);\n    i0.ɵɵtemplate(6, RoleBasedSettingsComponent_div_15_div_7_div_6_Template, 3, 3, \"div\", 25)(7, RoleBasedSettingsComponent_div_15_div_7_select_7_Template, 2, 3, \"select\", 26)(8, RoleBasedSettingsComponent_div_15_div_7_input_8_Template, 1, 3, \"input\", 27)(9, RoleBasedSettingsComponent_div_15_div_7_div_9_Template, 4, 6, \"div\", 28)(10, RoleBasedSettingsComponent_div_15_div_7_input_10_Template, 1, 2, \"input\", 29)(11, RoleBasedSettingsComponent_div_15_div_7_div_11_Template, 7, 3, \"div\", 30)(12, RoleBasedSettingsComponent_div_15_div_7_textarea_12_Template, 2, 3, \"textarea\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const setting_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", setting_r4.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(setting_r4.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", setting_r4.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", setting_r4.type === \"toggle\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", setting_r4.type === \"select\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", setting_r4.type === \"input\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", setting_r4.type === \"range\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", setting_r4.type === \"color\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", setting_r4.type === \"file\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", setting_r4.type === \"textarea\");\n  }\n}\nfunction RoleBasedSettingsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"h2\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 18);\n    i0.ɵɵtemplate(7, RoleBasedSettingsComponent_div_15_div_7_Template, 13, 10, \"div\", 19);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const section_r13 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r2.activeSection === section_r13.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(section_r13.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(section_r13.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", section_r13.settings);\n  }\n}\nexport class RoleBasedSettingsComponent {\n  constructor(roleManagementService) {\n    this.roleManagementService = roleManagementService;\n    this.currentRole = null;\n    this.roleConfig = null;\n    this.activeSection = 'profile';\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    if (this.currentRole) {\n      this.roleConfig = this.roleManagementService.getRoleConfig(this.currentRole);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  getAvailableSections() {\n    if (!this.currentRole) return [];\n    const baseSections = [{\n      id: 'profile',\n      title: 'Profile',\n      icon: 'fas fa-user',\n      description: 'Manage your personal profile information',\n      settings: [{\n        id: 'displayName',\n        type: 'input',\n        label: 'Display Name',\n        description: 'Your name as it appears to others',\n        value: '',\n        placeholder: 'Enter your display name'\n      }, {\n        id: 'bio',\n        type: 'textarea',\n        label: 'Bio',\n        description: 'Tell others about yourself',\n        value: '',\n        placeholder: 'Write a short bio...'\n      }, {\n        id: 'avatar',\n        type: 'file',\n        label: 'Profile Picture',\n        description: 'Upload a profile picture',\n        value: null\n      }, {\n        id: 'publicProfile',\n        type: 'toggle',\n        label: 'Public Profile',\n        description: 'Make your profile visible to everyone',\n        value: true\n      }]\n    }, {\n      id: 'notifications',\n      title: 'Notifications',\n      icon: 'fas fa-bell',\n      description: 'Configure your notification preferences',\n      settings: [{\n        id: 'emailNotifications',\n        type: 'toggle',\n        label: 'Email Notifications',\n        description: 'Receive notifications via email',\n        value: true\n      }, {\n        id: 'pushNotifications',\n        type: 'toggle',\n        label: 'Push Notifications',\n        description: 'Receive push notifications in browser',\n        value: true\n      }, {\n        id: 'notificationFrequency',\n        type: 'select',\n        label: 'Notification Frequency',\n        description: 'How often to receive notifications',\n        value: 'immediate',\n        options: [{\n          label: 'Immediate',\n          value: 'immediate'\n        }, {\n          label: 'Hourly',\n          value: 'hourly'\n        }, {\n          label: 'Daily',\n          value: 'daily'\n        }, {\n          label: 'Weekly',\n          value: 'weekly'\n        }]\n      }]\n    }, {\n      id: 'privacy',\n      title: 'Privacy',\n      icon: 'fas fa-shield-alt',\n      description: 'Control your privacy and security settings',\n      settings: [{\n        id: 'profileVisibility',\n        type: 'select',\n        label: 'Profile Visibility',\n        description: 'Who can see your profile',\n        value: 'team',\n        options: [{\n          label: 'Everyone',\n          value: 'public'\n        }, {\n          label: 'Team Members',\n          value: 'team'\n        }, {\n          label: 'Department Only',\n          value: 'department'\n        }, {\n          label: 'Private',\n          value: 'private'\n        }]\n      }, {\n        id: 'showOnlineStatus',\n        type: 'toggle',\n        label: 'Show Online Status',\n        description: 'Let others see when you\\'re online',\n        value: true\n      }, {\n        id: 'allowDirectMessages',\n        type: 'toggle',\n        label: 'Allow Direct Messages',\n        description: 'Allow others to send you direct messages',\n        value: true\n      }]\n    }];\n    // Add role-specific sections\n    const roleSpecificSections = this.getRoleSpecificSections();\n    return [...baseSections, ...roleSpecificSections];\n  }\n  getRoleSpecificSections() {\n    if (!this.currentRole) return [];\n    const sections = [];\n    // Admin-specific settings\n    if (this.currentRole === 'super_admin' || this.currentRole === 'admin') {\n      sections.push({\n        id: 'system',\n        title: 'System',\n        icon: 'fas fa-cogs',\n        description: 'System-wide configuration settings',\n        requiredPermission: 'system.manage',\n        settings: [{\n          id: 'maintenanceMode',\n          type: 'toggle',\n          label: 'Maintenance Mode',\n          description: 'Enable maintenance mode for the system',\n          value: false\n        }, {\n          id: 'userRegistration',\n          type: 'toggle',\n          label: 'User Registration',\n          description: 'Allow new user registrations',\n          value: true\n        }, {\n          id: 'sessionTimeout',\n          type: 'range',\n          label: 'Session Timeout (minutes)',\n          description: 'Automatic logout after inactivity',\n          value: 30,\n          min: 5,\n          max: 480,\n          step: 5\n        }]\n      });\n    }\n    // Manager-specific settings\n    if (this.roleManagementService.isManager(this.currentRole)) {\n      sections.push({\n        id: 'team',\n        title: 'Team Management',\n        icon: 'fas fa-users',\n        description: 'Manage your team settings and preferences',\n        settings: [{\n          id: 'teamVisibility',\n          type: 'select',\n          label: 'Team Visibility',\n          description: 'Who can see your team information',\n          value: 'department',\n          options: [{\n            label: 'Public',\n            value: 'public'\n          }, {\n            label: 'Department',\n            value: 'department'\n          }, {\n            label: 'Team Only',\n            value: 'team'\n          }]\n        }, {\n          id: 'autoAssignTasks',\n          type: 'toggle',\n          label: 'Auto-assign Tasks',\n          description: 'Automatically assign tasks to team members',\n          value: false\n        }, {\n          id: 'teamReportFrequency',\n          type: 'select',\n          label: 'Team Report Frequency',\n          description: 'How often to generate team reports',\n          value: 'weekly',\n          options: [{\n            label: 'Daily',\n            value: 'daily'\n          }, {\n            label: 'Weekly',\n            value: 'weekly'\n          }, {\n            label: 'Monthly',\n            value: 'monthly'\n          }]\n        }]\n      });\n    }\n    // Department-specific settings\n    const departmentSections = this.getDepartmentSpecificSections();\n    sections.push(...departmentSections);\n    return sections;\n  }\n  getDepartmentSpecificSections() {\n    if (!this.roleConfig) return [];\n    const sections = [];\n    switch (this.roleConfig.department) {\n      case 'sales':\n        sections.push({\n          id: 'sales',\n          title: 'Sales Settings',\n          icon: 'fas fa-chart-line',\n          description: 'Configure sales-specific preferences',\n          settings: [{\n            id: 'salesTarget',\n            type: 'input',\n            label: 'Monthly Sales Target',\n            description: 'Your monthly sales target amount',\n            value: '',\n            placeholder: 'Enter target amount'\n          }, {\n            id: 'commissionRate',\n            type: 'range',\n            label: 'Commission Rate (%)',\n            description: 'Your commission rate percentage',\n            value: 5,\n            min: 0,\n            max: 20,\n            step: 0.5\n          }, {\n            id: 'followUpReminders',\n            type: 'toggle',\n            label: 'Follow-up Reminders',\n            description: 'Get reminders for customer follow-ups',\n            value: true\n          }]\n        });\n        break;\n      case 'marketing':\n        sections.push({\n          id: 'marketing',\n          title: 'Marketing Settings',\n          icon: 'fas fa-bullhorn',\n          description: 'Configure marketing-specific preferences',\n          settings: [{\n            id: 'campaignBudget',\n            type: 'input',\n            label: 'Monthly Campaign Budget',\n            description: 'Your monthly campaign budget',\n            value: '',\n            placeholder: 'Enter budget amount'\n          }, {\n            id: 'contentApproval',\n            type: 'toggle',\n            label: 'Content Auto-approval',\n            description: 'Automatically approve content you create',\n            value: false\n          }, {\n            id: 'socialPlatforms',\n            type: 'select',\n            label: 'Primary Social Platform',\n            description: 'Your primary social media platform',\n            value: 'instagram',\n            options: [{\n              label: 'Instagram',\n              value: 'instagram'\n            }, {\n              label: 'Facebook',\n              value: 'facebook'\n            }, {\n              label: 'Twitter',\n              value: 'twitter'\n            }, {\n              label: 'LinkedIn',\n              value: 'linkedin'\n            }]\n          }]\n        });\n        break;\n      case 'support':\n        sections.push({\n          id: 'support',\n          title: 'Support Settings',\n          icon: 'fas fa-headset',\n          description: 'Configure support-specific preferences',\n          settings: [{\n            id: 'ticketAutoAssign',\n            type: 'toggle',\n            label: 'Auto-assign Tickets',\n            description: 'Automatically assign tickets to you',\n            value: true\n          }, {\n            id: 'responseTimeTarget',\n            type: 'range',\n            label: 'Response Time Target (hours)',\n            description: 'Your target response time for tickets',\n            value: 2,\n            min: 1,\n            max: 24,\n            step: 1\n          }, {\n            id: 'escalationThreshold',\n            type: 'range',\n            label: 'Escalation Threshold (hours)',\n            description: 'Auto-escalate tickets after this time',\n            value: 8,\n            min: 1,\n            max: 48,\n            step: 1\n          }]\n        });\n        break;\n    }\n    return sections;\n  }\n  setActiveSection(sectionId) {\n    this.activeSection = sectionId;\n  }\n  onSettingChange(setting, event) {\n    const value = event.target ? event.target.value : event;\n    setting.value = setting.type === 'toggle' ? event.target.checked : value;\n    if (setting.onChange) {\n      setting.onChange(setting.value);\n    }\n  }\n  onFileChange(setting, event) {\n    const file = event.target.files[0];\n    if (file) {\n      setting.value = file;\n      if (setting.onChange) {\n        setting.onChange(file);\n      }\n    }\n  }\n  getFileName(file) {\n    return file ? file.name : '';\n  }\n  resetToDefaults() {\n    // Reset all settings to default values\n    console.log('Resetting to defaults...');\n  }\n  saveSettings() {\n    // Save all settings\n    console.log('Saving settings...');\n    // Show success message\n    this.showSuccessMessage('Settings saved successfully!');\n  }\n  showSuccessMessage(message) {\n    // Implementation for showing success message\n    console.log(message);\n  }\n  static {\n    this.ɵfac = function RoleBasedSettingsComponent_Factory(t) {\n      return new (t || RoleBasedSettingsComponent)(i0.ɵɵdirectiveInject(i1.RoleManagementService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RoleBasedSettingsComponent,\n      selectors: [[\"app-role-based-settings\"]],\n      inputs: {\n        currentRole: \"currentRole\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 23,\n      vars: 11,\n      consts: [[1, \"role-based-settings\"], [1, \"settings-header\"], [1, \"header-content\"], [1, \"role-indicator\"], [1, \"header-text\"], [1, \"settings-navigation\"], [1, \"nav-tabs\"], [\"class\", \"nav-tab\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"settings-content\"], [\"class\", \"settings-section\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"settings-actions\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-save\"], [1, \"nav-tab\", 3, \"click\"], [1, \"settings-section\"], [1, \"section-header\"], [1, \"settings-grid\"], [\"class\", \"setting-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"setting-item\"], [1, \"setting-info\"], [1, \"setting-label\", 3, \"for\"], [\"class\", \"setting-description\", 4, \"ngIf\"], [1, \"setting-control\"], [\"class\", \"toggle-switch\", 4, \"ngIf\"], [\"class\", \"select-input\", 3, \"id\", \"ngModel\", \"ngModelChange\", \"change\", 4, \"ngIf\"], [\"type\", \"text\", \"class\", \"text-input\", 3, \"id\", \"ngModel\", \"placeholder\", \"ngModelChange\", \"input\", 4, \"ngIf\"], [\"class\", \"range-container\", 4, \"ngIf\"], [\"type\", \"color\", \"class\", \"color-input\", 3, \"id\", \"ngModel\", \"ngModelChange\", \"change\", 4, \"ngIf\"], [\"class\", \"file-upload\", 4, \"ngIf\"], [\"class\", \"textarea-input\", \"rows\", \"4\", 3, \"id\", \"ngModel\", \"placeholder\", \"ngModelChange\", \"input\", 4, \"ngIf\"], [1, \"setting-description\"], [1, \"toggle-switch\"], [\"type\", \"checkbox\", 1, \"toggle-input\", 3, \"ngModelChange\", \"change\", \"id\", \"ngModel\"], [1, \"toggle-label\", 3, \"for\"], [1, \"select-input\", 3, \"ngModelChange\", \"change\", \"id\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [\"type\", \"text\", 1, \"text-input\", 3, \"ngModelChange\", \"input\", \"id\", \"ngModel\", \"placeholder\"], [1, \"range-container\"], [\"type\", \"range\", 1, \"range-input\", 3, \"ngModelChange\", \"input\", \"id\", \"ngModel\", \"min\", \"max\", \"step\"], [1, \"range-value\"], [\"type\", \"color\", 1, \"color-input\", 3, \"ngModelChange\", \"change\", \"id\", \"ngModel\"], [1, \"file-upload\"], [\"type\", \"file\", \"hidden\", \"\", 1, \"file-input\", 3, \"change\", \"id\"], [1, \"file-label\", 3, \"for\"], [1, \"fas\", \"fa-upload\"], [\"class\", \"file-name\", 4, \"ngIf\"], [1, \"file-name\"], [\"rows\", \"4\", 1, \"textarea-input\", 3, \"ngModelChange\", \"input\", \"id\", \"ngModel\", \"placeholder\"]],\n      template: function RoleBasedSettingsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"i\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"h1\");\n          i0.ɵɵtext(7, \"Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\");\n          i0.ɵɵtext(9);\n          i0.ɵɵpipe(10, \"titlecase\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(11, \"div\", 5)(12, \"div\", 6);\n          i0.ɵɵtemplate(13, RoleBasedSettingsComponent_button_13_Template, 4, 5, \"button\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 8);\n          i0.ɵɵtemplate(15, RoleBasedSettingsComponent_div_15_Template, 8, 5, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 10)(17, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function RoleBasedSettingsComponent_Template_button_click_17_listener() {\n            return ctx.resetToDefaults();\n          });\n          i0.ɵɵelement(18, \"i\", 12);\n          i0.ɵɵtext(19, \" Reset to Defaults \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function RoleBasedSettingsComponent_Template_button_click_20_listener() {\n            return ctx.saveSettings();\n          });\n          i0.ɵɵelement(21, \"i\", 14);\n          i0.ɵɵtext(22, \" Save Changes \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-role\", ctx.currentRole);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleProp(\"background\", ctx.roleConfig == null ? null : ctx.roleConfig.color);\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.roleConfig == null ? null : ctx.roleConfig.icon);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate2(\"\", ctx.roleConfig == null ? null : ctx.roleConfig.displayName, \" - \", i0.ɵɵpipeBind1(10, 9, ctx.roleConfig == null ? null : ctx.roleConfig.department), \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getAvailableSections());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getAvailableSections());\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.TitleCasePipe, FormsModule, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.RangeValueAccessor, i3.CheckboxControlValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\".role-based-settings[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 2rem;\\n  background: #fafafa;\\n  min-height: 100vh;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 2rem;\\n  margin-bottom: 2rem;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .role-indicator[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 1.5rem;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: #262626;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.25rem 0 0 0;\\n  color: #8e8e8e;\\n  font-size: 1rem;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 1rem;\\n  margin-bottom: 2rem;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  overflow-x: auto;\\n  padding-bottom: 0.5rem;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 4px;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 2px;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 2px;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1.5rem;\\n  border: none;\\n  background: #f8f9fa;\\n  color: #6c757d;\\n  border-radius: 12px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  white-space: nowrap;\\n  font-weight: 500;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-tab.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%]   .nav-tabs[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%] {\\n  display: none;\\n  background: white;\\n  border-radius: 16px;\\n  padding: 2rem;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section.active[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: #262626;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #8e8e8e;\\n  font-size: 0.95rem;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 2rem;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr auto;\\n  gap: 1rem;\\n  align-items: start;\\n  padding: 1.5rem;\\n  border: 1px solid #f0f0f0;\\n  border-radius: 12px;\\n  transition: all 0.3s ease;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]:hover {\\n  border-color: #e0e0e0;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\\n}\\n@media (max-width: 768px) {\\n  .role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 1rem;\\n  }\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .setting-info[_ngcontent-%COMP%]   .setting-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  color: #262626;\\n  margin-bottom: 0.25rem;\\n  font-size: 0.95rem;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .setting-info[_ngcontent-%COMP%]   .setting-description[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #8e8e8e;\\n  font-size: 0.85rem;\\n  line-height: 1.4;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .setting-control[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n}\\n@media (max-width: 768px) {\\n  .role-based-settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%]   .settings-grid[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .setting-control[_ngcontent-%COMP%] {\\n    min-width: auto;\\n    width: 100%;\\n  }\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 50px;\\n  height: 24px;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   .toggle-input[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  width: 0;\\n  height: 0;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   .toggle-input[_ngcontent-%COMP%]:checked    + .toggle-label[_ngcontent-%COMP%] {\\n  background-color: #4CAF50;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   .toggle-input[_ngcontent-%COMP%]:checked    + .toggle-label[_ngcontent-%COMP%]:before {\\n  transform: translateX(26px);\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   .toggle-label[_ngcontent-%COMP%] {\\n  position: absolute;\\n  cursor: pointer;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: #ccc;\\n  border-radius: 24px;\\n  transition: 0.3s;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   .toggle-label[_ngcontent-%COMP%]:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  height: 18px;\\n  width: 18px;\\n  left: 3px;\\n  bottom: 3px;\\n  background-color: white;\\n  border-radius: 50%;\\n  transition: 0.3s;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .select-input[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .text-input[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .textarea-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.75rem;\\n  border: 1px solid #ddd;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n  transition: all 0.3s ease;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .select-input[_ngcontent-%COMP%]:focus, .role-based-settings[_ngcontent-%COMP%]   .text-input[_ngcontent-%COMP%]:focus, .role-based-settings[_ngcontent-%COMP%]   .textarea-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #667eea;\\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .range-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .range-container[_ngcontent-%COMP%]   .range-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 6px;\\n  border-radius: 3px;\\n  background: #ddd;\\n  outline: none;\\n  -webkit-appearance: none;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .range-container[_ngcontent-%COMP%]   .range-input[_ngcontent-%COMP%]::-webkit-slider-thumb {\\n  appearance: none;\\n  width: 18px;\\n  height: 18px;\\n  border-radius: 50%;\\n  background: #667eea;\\n  cursor: pointer;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .range-container[_ngcontent-%COMP%]   .range-input[_ngcontent-%COMP%]::-moz-range-thumb {\\n  width: 18px;\\n  height: 18px;\\n  border-radius: 50%;\\n  background: #667eea;\\n  cursor: pointer;\\n  border: none;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .range-container[_ngcontent-%COMP%]   .range-value[_ngcontent-%COMP%] {\\n  min-width: 40px;\\n  text-align: center;\\n  font-weight: 600;\\n  color: #667eea;\\n  background: #f8f9ff;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 6px;\\n  font-size: 0.85rem;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .color-input[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 40px;\\n  border: none;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .color-input[_ngcontent-%COMP%]::-webkit-color-swatch-wrapper {\\n  padding: 0;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .color-input[_ngcontent-%COMP%]::-webkit-color-swatch {\\n  border: none;\\n  border-radius: 8px;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1rem;\\n  background: #f8f9fa;\\n  border: 1px dashed #ddd;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  font-size: 0.9rem;\\n  color: #6c757d;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-label[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  border-color: #adb5bd;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #28a745;\\n  font-weight: 500;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 1rem;\\n  margin-top: 2rem;\\n  padding: 1.5rem;\\n  background: white;\\n  border-radius: 16px;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n}\\n@media (max-width: 768px) {\\n  .role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1.5rem;\\n  border: none;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  font-size: 0.9rem;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #6c757d;\\n  border: 1px solid #dee2e6;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);\\n}\\n.role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n\\n@media (prefers-color-scheme: dark) {\\n  .role-based-settings[_ngcontent-%COMP%] {\\n    background: #121212;\\n  }\\n  .role-based-settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .settings-navigation[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .settings-section[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .settings-actions[_ngcontent-%COMP%] {\\n    background: #1e1e1e;\\n    color: #ffffff;\\n  }\\n  .role-based-settings[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .setting-label[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .role-based-settings[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .setting-description[_ngcontent-%COMP%] {\\n    color: #b3b3b3;\\n  }\\n  .role-based-settings[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%] {\\n    border-color: #333;\\n  }\\n  .role-based-settings[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]:hover {\\n    border-color: #444;\\n  }\\n  .role-based-settings[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%] {\\n    background: #2a2a2a;\\n    color: #b3b3b3;\\n  }\\n  .role-based-settings[_ngcontent-%COMP%]   .nav-tab[_ngcontent-%COMP%]:hover {\\n    background: #333;\\n    color: #ffffff;\\n  }\\n  .role-based-settings[_ngcontent-%COMP%]   .select-input[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .text-input[_ngcontent-%COMP%], .role-based-settings[_ngcontent-%COMP%]   .textarea-input[_ngcontent-%COMP%] {\\n    background: #2a2a2a;\\n    border-color: #444;\\n    color: #ffffff;\\n  }\\n  .role-based-settings[_ngcontent-%COMP%]   .select-input[_ngcontent-%COMP%]:focus, .role-based-settings[_ngcontent-%COMP%]   .text-input[_ngcontent-%COMP%]:focus, .role-based-settings[_ngcontent-%COMP%]   .textarea-input[_ngcontent-%COMP%]:focus {\\n    border-color: #667eea;\\n  }\\n  .role-based-settings[_ngcontent-%COMP%]   .file-label[_ngcontent-%COMP%] {\\n    background: #2a2a2a;\\n    border-color: #444;\\n    color: #b3b3b3;\\n  }\\n  .role-based-settings[_ngcontent-%COMP%]   .file-label[_ngcontent-%COMP%]:hover {\\n    background: #333;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "Subject", "i0", "ɵɵelementStart", "ɵɵlistener", "RoleBasedSettingsComponent_button_13_Template_button_click_0_listener", "section_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "setActiveSection", "id", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "activeSection", "ɵɵadvance", "ɵɵclassMap", "icon", "ɵɵtextInterpolate", "title", "setting_r4", "description", "ɵɵtwoWayListener", "RoleBasedSettingsComponent_div_15_div_7_div_6_Template_input_ngModelChange_1_listener", "$event", "_r5", "ɵɵtwoWayBindingSet", "value", "RoleBasedSettingsComponent_div_15_div_7_div_6_Template_input_change_1_listener", "onSettingChange", "ɵɵproperty", "ɵɵtwoWayProperty", "option_r7", "ɵɵtextInterpolate1", "label", "RoleBasedSettingsComponent_div_15_div_7_select_7_Template_select_ngModelChange_0_listener", "_r6", "RoleBasedSettingsComponent_div_15_div_7_select_7_Template_select_change_0_listener", "ɵɵtemplate", "RoleBasedSettingsComponent_div_15_div_7_select_7_option_1_Template", "options", "RoleBasedSettingsComponent_div_15_div_7_input_8_Template_input_ngModelChange_0_listener", "_r8", "RoleBasedSettingsComponent_div_15_div_7_input_8_Template_input_input_0_listener", "placeholder", "RoleBasedSettingsComponent_div_15_div_7_div_9_Template_input_ngModelChange_1_listener", "_r9", "RoleBasedSettingsComponent_div_15_div_7_div_9_Template_input_input_1_listener", "min", "max", "step", "RoleBasedSettingsComponent_div_15_div_7_input_10_Template_input_ngModelChange_0_listener", "_r10", "RoleBasedSettingsComponent_div_15_div_7_input_10_Template_input_change_0_listener", "getFileName", "RoleBasedSettingsComponent_div_15_div_7_div_11_Template_input_change_1_listener", "_r11", "onFileChange", "RoleBasedSettingsComponent_div_15_div_7_div_11_span_6_Template", "RoleBasedSettingsComponent_div_15_div_7_textarea_12_Template_textarea_ngModelChange_0_listener", "_r12", "RoleBasedSettingsComponent_div_15_div_7_textarea_12_Template_textarea_input_0_listener", "RoleBasedSettingsComponent_div_15_div_7_p_4_Template", "RoleBasedSettingsComponent_div_15_div_7_div_6_Template", "RoleBasedSettingsComponent_div_15_div_7_select_7_Template", "RoleBasedSettingsComponent_div_15_div_7_input_8_Template", "RoleBasedSettingsComponent_div_15_div_7_div_9_Template", "RoleBasedSettingsComponent_div_15_div_7_input_10_Template", "RoleBasedSettingsComponent_div_15_div_7_div_11_Template", "RoleBasedSettingsComponent_div_15_div_7_textarea_12_Template", "type", "RoleBasedSettingsComponent_div_15_div_7_Template", "section_r13", "settings", "RoleBasedSettingsComponent", "constructor", "roleManagementService", "currentRole", "roleConfig", "destroy$", "ngOnInit", "getRoleConfig", "ngOnDestroy", "next", "complete", "getAvailableSections", "baseSections", "roleSpecificSections", "getRoleSpecificSections", "sections", "push", "requiredPermission", "is<PERSON>anager", "departmentSections", "getDepartmentSpecificSections", "department", "sectionId", "setting", "event", "target", "checked", "onChange", "file", "files", "name", "resetToDefaults", "console", "log", "saveSettings", "showSuccessMessage", "message", "ɵɵdirectiveInject", "i1", "RoleManagementService", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RoleBasedSettingsComponent_Template", "rf", "ctx", "RoleBasedSettingsComponent_button_13_Template", "RoleBasedSettingsComponent_div_15_Template", "RoleBasedSettingsComponent_Template_button_click_17_listener", "RoleBasedSettingsComponent_Template_button_click_20_listener", "ɵɵstyleProp", "color", "ɵɵtextInterpolate2", "displayName", "ɵɵpipeBind1", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "TitleCasePipe", "i3", "NgSelectOption", "ɵNgSelectMultipleOption", "DefaultValueAccessor", "RangeValueAccessor", "CheckboxControlValueAccessor", "SelectControlValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\shared\\components\\role-based-settings\\role-based-settings.component.ts"], "sourcesContent": ["import { Component, Input, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport { RoleManagementService, UserRole, RoleConfig } from '../../../core/services/role-management.service';\n\nexport interface SettingsSection {\n  id: string;\n  title: string;\n  icon: string;\n  description: string;\n  requiredPermission?: string;\n  settings: SettingsItem[];\n}\n\nexport interface SettingsItem {\n  id: string;\n  type: 'toggle' | 'select' | 'input' | 'range' | 'color' | 'file' | 'textarea';\n  label: string;\n  description?: string;\n  value: any;\n  options?: { label: string; value: any }[];\n  min?: number;\n  max?: number;\n  step?: number;\n  placeholder?: string;\n  validation?: {\n    required?: boolean;\n    pattern?: string;\n    minLength?: number;\n    maxLength?: number;\n  };\n  onChange?: (value: any) => void;\n}\n\n@Component({\n  selector: 'app-role-based-settings',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"role-based-settings\" [attr.data-role]=\"currentRole\">\n      <!-- Settings Header -->\n      <div class=\"settings-header\">\n        <div class=\"header-content\">\n          <div class=\"role-indicator\" [style.background]=\"roleConfig?.color\">\n            <i [class]=\"roleConfig?.icon\"></i>\n          </div>\n          <div class=\"header-text\">\n            <h1>Settings</h1>\n            <p>{{ roleConfig?.displayName }} - {{ roleConfig?.department | titlecase }}</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Settings Navigation -->\n      <div class=\"settings-navigation\">\n        <div class=\"nav-tabs\">\n          <button \n            *ngFor=\"let section of getAvailableSections()\" \n            class=\"nav-tab\"\n            [class.active]=\"activeSection === section.id\"\n            (click)=\"setActiveSection(section.id)\">\n            <i [class]=\"section.icon\"></i>\n            <span>{{ section.title }}</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Settings Content -->\n      <div class=\"settings-content\">\n        <div *ngFor=\"let section of getAvailableSections()\" \n             class=\"settings-section\"\n             [class.active]=\"activeSection === section.id\">\n          \n          <div class=\"section-header\">\n            <h2>{{ section.title }}</h2>\n            <p>{{ section.description }}</p>\n          </div>\n\n          <div class=\"settings-grid\">\n            <div *ngFor=\"let setting of section.settings\" class=\"setting-item\">\n              <div class=\"setting-info\">\n                <label [for]=\"setting.id\" class=\"setting-label\">{{ setting.label }}</label>\n                <p *ngIf=\"setting.description\" class=\"setting-description\">{{ setting.description }}</p>\n              </div>\n\n              <div class=\"setting-control\">\n                <!-- Toggle Switch -->\n                <div *ngIf=\"setting.type === 'toggle'\" class=\"toggle-switch\">\n                  <input \n                    type=\"checkbox\" \n                    [id]=\"setting.id\"\n                    [(ngModel)]=\"setting.value\"\n                    (change)=\"onSettingChange(setting, $event)\"\n                    class=\"toggle-input\">\n                  <label [for]=\"setting.id\" class=\"toggle-label\"></label>\n                </div>\n\n                <!-- Select Dropdown -->\n                <select \n                  *ngIf=\"setting.type === 'select'\"\n                  [id]=\"setting.id\"\n                  [(ngModel)]=\"setting.value\"\n                  (change)=\"onSettingChange(setting, $event)\"\n                  class=\"select-input\">\n                  <option *ngFor=\"let option of setting.options\" [value]=\"option.value\">\n                    {{ option.label }}\n                  </option>\n                </select>\n\n                <!-- Text Input -->\n                <input \n                  *ngIf=\"setting.type === 'input'\"\n                  type=\"text\"\n                  [id]=\"setting.id\"\n                  [(ngModel)]=\"setting.value\"\n                  (input)=\"onSettingChange(setting, $event)\"\n                  [placeholder]=\"setting.placeholder\"\n                  class=\"text-input\">\n\n                <!-- Range Slider -->\n                <div *ngIf=\"setting.type === 'range'\" class=\"range-container\">\n                  <input \n                    type=\"range\"\n                    [id]=\"setting.id\"\n                    [(ngModel)]=\"setting.value\"\n                    (input)=\"onSettingChange(setting, $event)\"\n                    [min]=\"setting.min\"\n                    [max]=\"setting.max\"\n                    [step]=\"setting.step\"\n                    class=\"range-input\">\n                  <span class=\"range-value\">{{ setting.value }}</span>\n                </div>\n\n                <!-- Color Picker -->\n                <input \n                  *ngIf=\"setting.type === 'color'\"\n                  type=\"color\"\n                  [id]=\"setting.id\"\n                  [(ngModel)]=\"setting.value\"\n                  (change)=\"onSettingChange(setting, $event)\"\n                  class=\"color-input\">\n\n                <!-- File Upload -->\n                <div *ngIf=\"setting.type === 'file'\" class=\"file-upload\">\n                  <input \n                    type=\"file\"\n                    [id]=\"setting.id\"\n                    (change)=\"onFileChange(setting, $event)\"\n                    class=\"file-input\"\n                    hidden>\n                  <label [for]=\"setting.id\" class=\"file-label\">\n                    <i class=\"fas fa-upload\"></i>\n                    <span>Choose File</span>\n                  </label>\n                  <span *ngIf=\"setting.value\" class=\"file-name\">{{ getFileName(setting.value) }}</span>\n                </div>\n\n                <!-- Textarea -->\n                <textarea \n                  *ngIf=\"setting.type === 'textarea'\"\n                  [id]=\"setting.id\"\n                  [(ngModel)]=\"setting.value\"\n                  (input)=\"onSettingChange(setting, $event)\"\n                  [placeholder]=\"setting.placeholder\"\n                  class=\"textarea-input\"\n                  rows=\"4\">\n                </textarea>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Settings Actions -->\n      <div class=\"settings-actions\">\n        <button class=\"btn btn-secondary\" (click)=\"resetToDefaults()\">\n          <i class=\"fas fa-undo\"></i>\n          Reset to Defaults\n        </button>\n        <button class=\"btn btn-primary\" (click)=\"saveSettings()\">\n          <i class=\"fas fa-save\"></i>\n          Save Changes\n        </button>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./role-based-settings.component.scss']\n})\nexport class RoleBasedSettingsComponent implements OnInit, OnDestroy {\n  @Input() currentRole: UserRole | null = null;\n  \n  roleConfig: RoleConfig | null = null;\n  activeSection: string = 'profile';\n  \n  private destroy$ = new Subject<void>();\n\n  constructor(private roleManagementService: RoleManagementService) {}\n\n  ngOnInit() {\n    if (this.currentRole) {\n      this.roleConfig = this.roleManagementService.getRoleConfig(this.currentRole);\n    }\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  getAvailableSections(): SettingsSection[] {\n    if (!this.currentRole) return [];\n\n    const baseSections: SettingsSection[] = [\n      {\n        id: 'profile',\n        title: 'Profile',\n        icon: 'fas fa-user',\n        description: 'Manage your personal profile information',\n        settings: [\n          {\n            id: 'displayName',\n            type: 'input',\n            label: 'Display Name',\n            description: 'Your name as it appears to others',\n            value: '',\n            placeholder: 'Enter your display name'\n          },\n          {\n            id: 'bio',\n            type: 'textarea',\n            label: 'Bio',\n            description: 'Tell others about yourself',\n            value: '',\n            placeholder: 'Write a short bio...'\n          },\n          {\n            id: 'avatar',\n            type: 'file',\n            label: 'Profile Picture',\n            description: 'Upload a profile picture',\n            value: null\n          },\n          {\n            id: 'publicProfile',\n            type: 'toggle',\n            label: 'Public Profile',\n            description: 'Make your profile visible to everyone',\n            value: true\n          }\n        ]\n      },\n      {\n        id: 'notifications',\n        title: 'Notifications',\n        icon: 'fas fa-bell',\n        description: 'Configure your notification preferences',\n        settings: [\n          {\n            id: 'emailNotifications',\n            type: 'toggle',\n            label: 'Email Notifications',\n            description: 'Receive notifications via email',\n            value: true\n          },\n          {\n            id: 'pushNotifications',\n            type: 'toggle',\n            label: 'Push Notifications',\n            description: 'Receive push notifications in browser',\n            value: true\n          },\n          {\n            id: 'notificationFrequency',\n            type: 'select',\n            label: 'Notification Frequency',\n            description: 'How often to receive notifications',\n            value: 'immediate',\n            options: [\n              { label: 'Immediate', value: 'immediate' },\n              { label: 'Hourly', value: 'hourly' },\n              { label: 'Daily', value: 'daily' },\n              { label: 'Weekly', value: 'weekly' }\n            ]\n          }\n        ]\n      },\n      {\n        id: 'privacy',\n        title: 'Privacy',\n        icon: 'fas fa-shield-alt',\n        description: 'Control your privacy and security settings',\n        settings: [\n          {\n            id: 'profileVisibility',\n            type: 'select',\n            label: 'Profile Visibility',\n            description: 'Who can see your profile',\n            value: 'team',\n            options: [\n              { label: 'Everyone', value: 'public' },\n              { label: 'Team Members', value: 'team' },\n              { label: 'Department Only', value: 'department' },\n              { label: 'Private', value: 'private' }\n            ]\n          },\n          {\n            id: 'showOnlineStatus',\n            type: 'toggle',\n            label: 'Show Online Status',\n            description: 'Let others see when you\\'re online',\n            value: true\n          },\n          {\n            id: 'allowDirectMessages',\n            type: 'toggle',\n            label: 'Allow Direct Messages',\n            description: 'Allow others to send you direct messages',\n            value: true\n          }\n        ]\n      }\n    ];\n\n    // Add role-specific sections\n    const roleSpecificSections = this.getRoleSpecificSections();\n    return [...baseSections, ...roleSpecificSections];\n  }\n\n  getRoleSpecificSections(): SettingsSection[] {\n    if (!this.currentRole) return [];\n\n    const sections: SettingsSection[] = [];\n\n    // Admin-specific settings\n    if (this.currentRole === 'super_admin' || this.currentRole === 'admin') {\n      sections.push({\n        id: 'system',\n        title: 'System',\n        icon: 'fas fa-cogs',\n        description: 'System-wide configuration settings',\n        requiredPermission: 'system.manage',\n        settings: [\n          {\n            id: 'maintenanceMode',\n            type: 'toggle',\n            label: 'Maintenance Mode',\n            description: 'Enable maintenance mode for the system',\n            value: false\n          },\n          {\n            id: 'userRegistration',\n            type: 'toggle',\n            label: 'User Registration',\n            description: 'Allow new user registrations',\n            value: true\n          },\n          {\n            id: 'sessionTimeout',\n            type: 'range',\n            label: 'Session Timeout (minutes)',\n            description: 'Automatic logout after inactivity',\n            value: 30,\n            min: 5,\n            max: 480,\n            step: 5\n          }\n        ]\n      });\n    }\n\n    // Manager-specific settings\n    if (this.roleManagementService.isManager(this.currentRole)) {\n      sections.push({\n        id: 'team',\n        title: 'Team Management',\n        icon: 'fas fa-users',\n        description: 'Manage your team settings and preferences',\n        settings: [\n          {\n            id: 'teamVisibility',\n            type: 'select',\n            label: 'Team Visibility',\n            description: 'Who can see your team information',\n            value: 'department',\n            options: [\n              { label: 'Public', value: 'public' },\n              { label: 'Department', value: 'department' },\n              { label: 'Team Only', value: 'team' }\n            ]\n          },\n          {\n            id: 'autoAssignTasks',\n            type: 'toggle',\n            label: 'Auto-assign Tasks',\n            description: 'Automatically assign tasks to team members',\n            value: false\n          },\n          {\n            id: 'teamReportFrequency',\n            type: 'select',\n            label: 'Team Report Frequency',\n            description: 'How often to generate team reports',\n            value: 'weekly',\n            options: [\n              { label: 'Daily', value: 'daily' },\n              { label: 'Weekly', value: 'weekly' },\n              { label: 'Monthly', value: 'monthly' }\n            ]\n          }\n        ]\n      });\n    }\n\n    // Department-specific settings\n    const departmentSections = this.getDepartmentSpecificSections();\n    sections.push(...departmentSections);\n\n    return sections;\n  }\n\n  getDepartmentSpecificSections(): SettingsSection[] {\n    if (!this.roleConfig) return [];\n\n    const sections: SettingsSection[] = [];\n\n    switch (this.roleConfig.department) {\n      case 'sales':\n        sections.push({\n          id: 'sales',\n          title: 'Sales Settings',\n          icon: 'fas fa-chart-line',\n          description: 'Configure sales-specific preferences',\n          settings: [\n            {\n              id: 'salesTarget',\n              type: 'input',\n              label: 'Monthly Sales Target',\n              description: 'Your monthly sales target amount',\n              value: '',\n              placeholder: 'Enter target amount'\n            },\n            {\n              id: 'commissionRate',\n              type: 'range',\n              label: 'Commission Rate (%)',\n              description: 'Your commission rate percentage',\n              value: 5,\n              min: 0,\n              max: 20,\n              step: 0.5\n            },\n            {\n              id: 'followUpReminders',\n              type: 'toggle',\n              label: 'Follow-up Reminders',\n              description: 'Get reminders for customer follow-ups',\n              value: true\n            }\n          ]\n        });\n        break;\n\n      case 'marketing':\n        sections.push({\n          id: 'marketing',\n          title: 'Marketing Settings',\n          icon: 'fas fa-bullhorn',\n          description: 'Configure marketing-specific preferences',\n          settings: [\n            {\n              id: 'campaignBudget',\n              type: 'input',\n              label: 'Monthly Campaign Budget',\n              description: 'Your monthly campaign budget',\n              value: '',\n              placeholder: 'Enter budget amount'\n            },\n            {\n              id: 'contentApproval',\n              type: 'toggle',\n              label: 'Content Auto-approval',\n              description: 'Automatically approve content you create',\n              value: false\n            },\n            {\n              id: 'socialPlatforms',\n              type: 'select',\n              label: 'Primary Social Platform',\n              description: 'Your primary social media platform',\n              value: 'instagram',\n              options: [\n                { label: 'Instagram', value: 'instagram' },\n                { label: 'Facebook', value: 'facebook' },\n                { label: 'Twitter', value: 'twitter' },\n                { label: 'LinkedIn', value: 'linkedin' }\n              ]\n            }\n          ]\n        });\n        break;\n\n      case 'support':\n        sections.push({\n          id: 'support',\n          title: 'Support Settings',\n          icon: 'fas fa-headset',\n          description: 'Configure support-specific preferences',\n          settings: [\n            {\n              id: 'ticketAutoAssign',\n              type: 'toggle',\n              label: 'Auto-assign Tickets',\n              description: 'Automatically assign tickets to you',\n              value: true\n            },\n            {\n              id: 'responseTimeTarget',\n              type: 'range',\n              label: 'Response Time Target (hours)',\n              description: 'Your target response time for tickets',\n              value: 2,\n              min: 1,\n              max: 24,\n              step: 1\n            },\n            {\n              id: 'escalationThreshold',\n              type: 'range',\n              label: 'Escalation Threshold (hours)',\n              description: 'Auto-escalate tickets after this time',\n              value: 8,\n              min: 1,\n              max: 48,\n              step: 1\n            }\n          ]\n        });\n        break;\n    }\n\n    return sections;\n  }\n\n  setActiveSection(sectionId: string): void {\n    this.activeSection = sectionId;\n  }\n\n  onSettingChange(setting: SettingsItem, event: any): void {\n    const value = event.target ? event.target.value : event;\n    setting.value = setting.type === 'toggle' ? event.target.checked : value;\n    \n    if (setting.onChange) {\n      setting.onChange(setting.value);\n    }\n  }\n\n  onFileChange(setting: SettingsItem, event: any): void {\n    const file = event.target.files[0];\n    if (file) {\n      setting.value = file;\n      if (setting.onChange) {\n        setting.onChange(file);\n      }\n    }\n  }\n\n  getFileName(file: File): string {\n    return file ? file.name : '';\n  }\n\n  resetToDefaults(): void {\n    // Reset all settings to default values\n    console.log('Resetting to defaults...');\n  }\n\n  saveSettings(): void {\n    // Save all settings\n    console.log('Saving settings...');\n\n    // Show success message\n    this.showSuccessMessage('Settings saved successfully!');\n  }\n\n  private showSuccessMessage(message: string): void {\n    // Implementation for showing success message\n    console.log(message);\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,OAAO,QAAmB,MAAM;;;;;;;;IAsD/BC,EAAA,CAAAC,cAAA,iBAIyC;IAAvCD,EAAA,CAAAE,UAAA,mBAAAC,sEAAA;MAAA,MAAAC,UAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,gBAAA,CAAAP,UAAA,CAAAQ,EAAA,CAA4B;IAAA,EAAC;IACtCZ,EAAA,CAAAa,SAAA,QAA8B;IAC9Bb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAc,MAAA,GAAmB;IAC3Bd,EAD2B,CAAAe,YAAA,EAAO,EACzB;;;;;IAJPf,EAAA,CAAAgB,WAAA,WAAAR,MAAA,CAAAS,aAAA,KAAAb,UAAA,CAAAQ,EAAA,CAA6C;IAE1CZ,EAAA,CAAAkB,SAAA,EAAsB;IAAtBlB,EAAA,CAAAmB,UAAA,CAAAf,UAAA,CAAAgB,IAAA,CAAsB;IACnBpB,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAAqB,iBAAA,CAAAjB,UAAA,CAAAkB,KAAA,CAAmB;;;;;IAoBrBtB,EAAA,CAAAC,cAAA,YAA2D;IAAAD,EAAA,CAAAc,MAAA,GAAyB;IAAAd,EAAA,CAAAe,YAAA,EAAI;;;;IAA7Bf,EAAA,CAAAkB,SAAA,EAAyB;IAAzBlB,EAAA,CAAAqB,iBAAA,CAAAE,UAAA,CAAAC,WAAA,CAAyB;;;;;;IAMlFxB,EADF,CAAAC,cAAA,cAA6D,gBAMpC;IAFrBD,EAAA,CAAAyB,gBAAA,2BAAAC,sFAAAC,MAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAAuB,GAAA;MAAA,MAAAL,UAAA,GAAAvB,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAAP,EAAA,CAAA6B,kBAAA,CAAAN,UAAA,CAAAO,KAAA,EAAAH,MAAA,MAAAJ,UAAA,CAAAO,KAAA,GAAAH,MAAA;MAAA,OAAA3B,EAAA,CAAAU,WAAA,CAAAiB,MAAA;IAAA,EAA2B;IAC3B3B,EAAA,CAAAE,UAAA,oBAAA6B,+EAAAJ,MAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAAuB,GAAA;MAAA,MAAAL,UAAA,GAAAvB,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAAwB,eAAA,CAAAT,UAAA,EAAAI,MAAA,CAAgC;IAAA,EAAC;IAJ7C3B,EAAA,CAAAe,YAAA,EAKuB;IACvBf,EAAA,CAAAa,SAAA,gBAAuD;IACzDb,EAAA,CAAAe,YAAA,EAAM;;;;IALFf,EAAA,CAAAkB,SAAA,EAAiB;IAAjBlB,EAAA,CAAAiC,UAAA,OAAAV,UAAA,CAAAX,EAAA,CAAiB;IACjBZ,EAAA,CAAAkC,gBAAA,YAAAX,UAAA,CAAAO,KAAA,CAA2B;IAGtB9B,EAAA,CAAAkB,SAAA,EAAkB;IAAlBlB,EAAA,CAAAiC,UAAA,QAAAV,UAAA,CAAAX,EAAA,CAAkB;;;;;IAUzBZ,EAAA,CAAAC,cAAA,iBAAsE;IACpED,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAe,YAAA,EAAS;;;;IAFsCf,EAAA,CAAAiC,UAAA,UAAAE,SAAA,CAAAL,KAAA,CAAsB;IACnE9B,EAAA,CAAAkB,SAAA,EACF;IADElB,EAAA,CAAAoC,kBAAA,MAAAD,SAAA,CAAAE,KAAA,MACF;;;;;;IARFrC,EAAA,CAAAC,cAAA,iBAKuB;IAFrBD,EAAA,CAAAyB,gBAAA,2BAAAa,0FAAAX,MAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAAkC,GAAA;MAAA,MAAAhB,UAAA,GAAAvB,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAAP,EAAA,CAAA6B,kBAAA,CAAAN,UAAA,CAAAO,KAAA,EAAAH,MAAA,MAAAJ,UAAA,CAAAO,KAAA,GAAAH,MAAA;MAAA,OAAA3B,EAAA,CAAAU,WAAA,CAAAiB,MAAA;IAAA,EAA2B;IAC3B3B,EAAA,CAAAE,UAAA,oBAAAsC,mFAAAb,MAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAAkC,GAAA;MAAA,MAAAhB,UAAA,GAAAvB,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAAwB,eAAA,CAAAT,UAAA,EAAAI,MAAA,CAAgC;IAAA,EAAC;IAE3C3B,EAAA,CAAAyC,UAAA,IAAAC,kEAAA,qBAAsE;IAGxE1C,EAAA,CAAAe,YAAA,EAAS;;;;IAPPf,EAAA,CAAAiC,UAAA,OAAAV,UAAA,CAAAX,EAAA,CAAiB;IACjBZ,EAAA,CAAAkC,gBAAA,YAAAX,UAAA,CAAAO,KAAA,CAA2B;IAGA9B,EAAA,CAAAkB,SAAA,EAAkB;IAAlBlB,EAAA,CAAAiC,UAAA,YAAAV,UAAA,CAAAoB,OAAA,CAAkB;;;;;;IAM/C3C,EAAA,CAAAC,cAAA,gBAOqB;IAHnBD,EAAA,CAAAyB,gBAAA,2BAAAmB,wFAAAjB,MAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAAwC,GAAA;MAAA,MAAAtB,UAAA,GAAAvB,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAAP,EAAA,CAAA6B,kBAAA,CAAAN,UAAA,CAAAO,KAAA,EAAAH,MAAA,MAAAJ,UAAA,CAAAO,KAAA,GAAAH,MAAA;MAAA,OAAA3B,EAAA,CAAAU,WAAA,CAAAiB,MAAA;IAAA,EAA2B;IAC3B3B,EAAA,CAAAE,UAAA,mBAAA4C,gFAAAnB,MAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAAwC,GAAA;MAAA,MAAAtB,UAAA,GAAAvB,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAwB,eAAA,CAAAT,UAAA,EAAAI,MAAA,CAAgC;IAAA,EAAC;IAL5C3B,EAAA,CAAAe,YAAA,EAOqB;;;;IAJnBf,EAAA,CAAAiC,UAAA,OAAAV,UAAA,CAAAX,EAAA,CAAiB;IACjBZ,EAAA,CAAAkC,gBAAA,YAAAX,UAAA,CAAAO,KAAA,CAA2B;IAE3B9B,EAAA,CAAAiC,UAAA,gBAAAV,UAAA,CAAAwB,WAAA,CAAmC;;;;;;IAKnC/C,EADF,CAAAC,cAAA,cAA8D,gBAStC;IALpBD,EAAA,CAAAyB,gBAAA,2BAAAuB,sFAAArB,MAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAA4C,GAAA;MAAA,MAAA1B,UAAA,GAAAvB,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAAP,EAAA,CAAA6B,kBAAA,CAAAN,UAAA,CAAAO,KAAA,EAAAH,MAAA,MAAAJ,UAAA,CAAAO,KAAA,GAAAH,MAAA;MAAA,OAAA3B,EAAA,CAAAU,WAAA,CAAAiB,MAAA;IAAA,EAA2B;IAC3B3B,EAAA,CAAAE,UAAA,mBAAAgD,8EAAAvB,MAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAA4C,GAAA;MAAA,MAAA1B,UAAA,GAAAvB,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAwB,eAAA,CAAAT,UAAA,EAAAI,MAAA,CAAgC;IAAA,EAAC;IAJ5C3B,EAAA,CAAAe,YAAA,EAQsB;IACtBf,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAc,MAAA,GAAmB;IAC/Cd,EAD+C,CAAAe,YAAA,EAAO,EAChD;;;;IARFf,EAAA,CAAAkB,SAAA,EAAiB;IAAjBlB,EAAA,CAAAiC,UAAA,OAAAV,UAAA,CAAAX,EAAA,CAAiB;IACjBZ,EAAA,CAAAkC,gBAAA,YAAAX,UAAA,CAAAO,KAAA,CAA2B;IAI3B9B,EAFA,CAAAiC,UAAA,QAAAV,UAAA,CAAA4B,GAAA,CAAmB,QAAA5B,UAAA,CAAA6B,GAAA,CACA,SAAA7B,UAAA,CAAA8B,IAAA,CACE;IAEGrD,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAAqB,iBAAA,CAAAE,UAAA,CAAAO,KAAA,CAAmB;;;;;;IAI/C9B,EAAA,CAAAC,cAAA,gBAMsB;IAFpBD,EAAA,CAAAyB,gBAAA,2BAAA6B,yFAAA3B,MAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAAkD,IAAA;MAAA,MAAAhC,UAAA,GAAAvB,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAAP,EAAA,CAAA6B,kBAAA,CAAAN,UAAA,CAAAO,KAAA,EAAAH,MAAA,MAAAJ,UAAA,CAAAO,KAAA,GAAAH,MAAA;MAAA,OAAA3B,EAAA,CAAAU,WAAA,CAAAiB,MAAA;IAAA,EAA2B;IAC3B3B,EAAA,CAAAE,UAAA,oBAAAsD,kFAAA7B,MAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAAkD,IAAA;MAAA,MAAAhC,UAAA,GAAAvB,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAAwB,eAAA,CAAAT,UAAA,EAAAI,MAAA,CAAgC;IAAA,EAAC;IAL7C3B,EAAA,CAAAe,YAAA,EAMsB;;;;IAHpBf,EAAA,CAAAiC,UAAA,OAAAV,UAAA,CAAAX,EAAA,CAAiB;IACjBZ,EAAA,CAAAkC,gBAAA,YAAAX,UAAA,CAAAO,KAAA,CAA2B;;;;;IAgB3B9B,EAAA,CAAAC,cAAA,eAA8C;IAAAD,EAAA,CAAAc,MAAA,GAAgC;IAAAd,EAAA,CAAAe,YAAA,EAAO;;;;;IAAvCf,EAAA,CAAAkB,SAAA,EAAgC;IAAhClB,EAAA,CAAAqB,iBAAA,CAAAb,MAAA,CAAAiD,WAAA,CAAAlC,UAAA,CAAAO,KAAA,EAAgC;;;;;;IAV9E9B,EADF,CAAAC,cAAA,cAAyD,gBAM9C;IAFPD,EAAA,CAAAE,UAAA,oBAAAwD,gFAAA/B,MAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAAsD,IAAA;MAAA,MAAApC,UAAA,GAAAvB,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAAoD,YAAA,CAAArC,UAAA,EAAAI,MAAA,CAA6B;IAAA,EAAC;IAH1C3B,EAAA,CAAAe,YAAA,EAKS;IACTf,EAAA,CAAAC,cAAA,gBAA6C;IAC3CD,EAAA,CAAAa,SAAA,YAA6B;IAC7Bb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAc,MAAA,kBAAW;IACnBd,EADmB,CAAAe,YAAA,EAAO,EAClB;IACRf,EAAA,CAAAyC,UAAA,IAAAoB,8DAAA,mBAA8C;IAChD7D,EAAA,CAAAe,YAAA,EAAM;;;;IATFf,EAAA,CAAAkB,SAAA,EAAiB;IAAjBlB,EAAA,CAAAiC,UAAA,OAAAV,UAAA,CAAAX,EAAA,CAAiB;IAIZZ,EAAA,CAAAkB,SAAA,EAAkB;IAAlBlB,EAAA,CAAAiC,UAAA,QAAAV,UAAA,CAAAX,EAAA,CAAkB;IAIlBZ,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAAiC,UAAA,SAAAV,UAAA,CAAAO,KAAA,CAAmB;;;;;;IAI5B9B,EAAA,CAAAC,cAAA,mBAOW;IAJTD,EAAA,CAAAyB,gBAAA,2BAAAqC,+FAAAnC,MAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAA0D,IAAA;MAAA,MAAAxC,UAAA,GAAAvB,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAAP,EAAA,CAAA6B,kBAAA,CAAAN,UAAA,CAAAO,KAAA,EAAAH,MAAA,MAAAJ,UAAA,CAAAO,KAAA,GAAAH,MAAA;MAAA,OAAA3B,EAAA,CAAAU,WAAA,CAAAiB,MAAA;IAAA,EAA2B;IAC3B3B,EAAA,CAAAE,UAAA,mBAAA8D,uFAAArC,MAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAA0D,IAAA;MAAA,MAAAxC,UAAA,GAAAvB,EAAA,CAAAS,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAwB,eAAA,CAAAT,UAAA,EAAAI,MAAA,CAAgC;IAAA,EAAC;IAI5C3B,EAAA,CAAAc,MAAA;IAAAd,EAAA,CAAAe,YAAA,EAAW;;;;IANTf,EAAA,CAAAiC,UAAA,OAAAV,UAAA,CAAAX,EAAA,CAAiB;IACjBZ,EAAA,CAAAkC,gBAAA,YAAAX,UAAA,CAAAO,KAAA,CAA2B;IAE3B9B,EAAA,CAAAiC,UAAA,gBAAAV,UAAA,CAAAwB,WAAA,CAAmC;;;;;IAlFrC/C,EAFJ,CAAAC,cAAA,cAAmE,cACvC,gBACwB;IAAAD,EAAA,CAAAc,MAAA,GAAmB;IAAAd,EAAA,CAAAe,YAAA,EAAQ;IAC3Ef,EAAA,CAAAyC,UAAA,IAAAwB,oDAAA,gBAA2D;IAC7DjE,EAAA,CAAAe,YAAA,EAAM;IAENf,EAAA,CAAAC,cAAA,cAA6B;IAyE3BD,EAvEA,CAAAyC,UAAA,IAAAyB,sDAAA,kBAA6D,IAAAC,yDAAA,qBAgBtC,IAAAC,wDAAA,oBAcF,IAAAC,sDAAA,kBAGyC,KAAAC,yDAAA,oBAoBxC,KAAAC,uDAAA,kBAGmC,KAAAC,4DAAA,uBAsB9C;IAGfxE,EADE,CAAAe,YAAA,EAAM,EACF;;;;IAvFKf,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAAiC,UAAA,QAAAV,UAAA,CAAAX,EAAA,CAAkB;IAAuBZ,EAAA,CAAAkB,SAAA,EAAmB;IAAnBlB,EAAA,CAAAqB,iBAAA,CAAAE,UAAA,CAAAc,KAAA,CAAmB;IAC/DrC,EAAA,CAAAkB,SAAA,EAAyB;IAAzBlB,EAAA,CAAAiC,UAAA,SAAAV,UAAA,CAAAC,WAAA,CAAyB;IAKvBxB,EAAA,CAAAkB,SAAA,GAA+B;IAA/BlB,EAAA,CAAAiC,UAAA,SAAAV,UAAA,CAAAkD,IAAA,cAA+B;IAYlCzE,EAAA,CAAAkB,SAAA,EAA+B;IAA/BlB,EAAA,CAAAiC,UAAA,SAAAV,UAAA,CAAAkD,IAAA,cAA+B;IAY/BzE,EAAA,CAAAkB,SAAA,EAA8B;IAA9BlB,EAAA,CAAAiC,UAAA,SAAAV,UAAA,CAAAkD,IAAA,aAA8B;IAS3BzE,EAAA,CAAAkB,SAAA,EAA8B;IAA9BlB,EAAA,CAAAiC,UAAA,SAAAV,UAAA,CAAAkD,IAAA,aAA8B;IAejCzE,EAAA,CAAAkB,SAAA,EAA8B;IAA9BlB,EAAA,CAAAiC,UAAA,SAAAV,UAAA,CAAAkD,IAAA,aAA8B;IAQ3BzE,EAAA,CAAAkB,SAAA,EAA6B;IAA7BlB,EAAA,CAAAiC,UAAA,SAAAV,UAAA,CAAAkD,IAAA,YAA6B;IAgBhCzE,EAAA,CAAAkB,SAAA,EAAiC;IAAjClB,EAAA,CAAAiC,UAAA,SAAAV,UAAA,CAAAkD,IAAA,gBAAiC;;;;;IArFxCzE,EALJ,CAAAC,cAAA,cAEmD,cAErB,SACtB;IAAAD,EAAA,CAAAc,MAAA,GAAmB;IAAAd,EAAA,CAAAe,YAAA,EAAK;IAC5Bf,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAc,MAAA,GAAyB;IAC9Bd,EAD8B,CAAAe,YAAA,EAAI,EAC5B;IAENf,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAyC,UAAA,IAAAiC,gDAAA,oBAAmE;IA2FvE1E,EADE,CAAAe,YAAA,EAAM,EACF;;;;;IAnGDf,EAAA,CAAAgB,WAAA,WAAAR,MAAA,CAAAS,aAAA,KAAA0D,WAAA,CAAA/D,EAAA,CAA6C;IAG1CZ,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAAqB,iBAAA,CAAAsD,WAAA,CAAArD,KAAA,CAAmB;IACpBtB,EAAA,CAAAkB,SAAA,GAAyB;IAAzBlB,EAAA,CAAAqB,iBAAA,CAAAsD,WAAA,CAAAnD,WAAA,CAAyB;IAIHxB,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAAiC,UAAA,YAAA0C,WAAA,CAAAC,QAAA,CAAmB;;;AA6GxD,OAAM,MAAOC,0BAA0B;EAQrCC,YAAoBC,qBAA4C;IAA5C,KAAAA,qBAAqB,GAArBA,qBAAqB;IAPhC,KAAAC,WAAW,GAAoB,IAAI;IAE5C,KAAAC,UAAU,GAAsB,IAAI;IACpC,KAAAhE,aAAa,GAAW,SAAS;IAEzB,KAAAiE,QAAQ,GAAG,IAAInF,OAAO,EAAQ;EAE6B;EAEnEoF,QAAQA,CAAA;IACN,IAAI,IAAI,CAACH,WAAW,EAAE;MACpB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACF,qBAAqB,CAACK,aAAa,CAAC,IAAI,CAACJ,WAAW,CAAC;;EAEhF;EAEAK,WAAWA,CAAA;IACT,IAAI,CAACH,QAAQ,CAACI,IAAI,EAAE;IACpB,IAAI,CAACJ,QAAQ,CAACK,QAAQ,EAAE;EAC1B;EAEAC,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACR,WAAW,EAAE,OAAO,EAAE;IAEhC,MAAMS,YAAY,GAAsB,CACtC;MACE7E,EAAE,EAAE,SAAS;MACbU,KAAK,EAAE,SAAS;MAChBF,IAAI,EAAE,aAAa;MACnBI,WAAW,EAAE,0CAA0C;MACvDoD,QAAQ,EAAE,CACR;QACEhE,EAAE,EAAE,aAAa;QACjB6D,IAAI,EAAE,OAAO;QACbpC,KAAK,EAAE,cAAc;QACrBb,WAAW,EAAE,mCAAmC;QAChDM,KAAK,EAAE,EAAE;QACTiB,WAAW,EAAE;OACd,EACD;QACEnC,EAAE,EAAE,KAAK;QACT6D,IAAI,EAAE,UAAU;QAChBpC,KAAK,EAAE,KAAK;QACZb,WAAW,EAAE,4BAA4B;QACzCM,KAAK,EAAE,EAAE;QACTiB,WAAW,EAAE;OACd,EACD;QACEnC,EAAE,EAAE,QAAQ;QACZ6D,IAAI,EAAE,MAAM;QACZpC,KAAK,EAAE,iBAAiB;QACxBb,WAAW,EAAE,0BAA0B;QACvCM,KAAK,EAAE;OACR,EACD;QACElB,EAAE,EAAE,eAAe;QACnB6D,IAAI,EAAE,QAAQ;QACdpC,KAAK,EAAE,gBAAgB;QACvBb,WAAW,EAAE,uCAAuC;QACpDM,KAAK,EAAE;OACR;KAEJ,EACD;MACElB,EAAE,EAAE,eAAe;MACnBU,KAAK,EAAE,eAAe;MACtBF,IAAI,EAAE,aAAa;MACnBI,WAAW,EAAE,yCAAyC;MACtDoD,QAAQ,EAAE,CACR;QACEhE,EAAE,EAAE,oBAAoB;QACxB6D,IAAI,EAAE,QAAQ;QACdpC,KAAK,EAAE,qBAAqB;QAC5Bb,WAAW,EAAE,iCAAiC;QAC9CM,KAAK,EAAE;OACR,EACD;QACElB,EAAE,EAAE,mBAAmB;QACvB6D,IAAI,EAAE,QAAQ;QACdpC,KAAK,EAAE,oBAAoB;QAC3Bb,WAAW,EAAE,uCAAuC;QACpDM,KAAK,EAAE;OACR,EACD;QACElB,EAAE,EAAE,uBAAuB;QAC3B6D,IAAI,EAAE,QAAQ;QACdpC,KAAK,EAAE,wBAAwB;QAC/Bb,WAAW,EAAE,oCAAoC;QACjDM,KAAK,EAAE,WAAW;QAClBa,OAAO,EAAE,CACP;UAAEN,KAAK,EAAE,WAAW;UAAEP,KAAK,EAAE;QAAW,CAAE,EAC1C;UAAEO,KAAK,EAAE,QAAQ;UAAEP,KAAK,EAAE;QAAQ,CAAE,EACpC;UAAEO,KAAK,EAAE,OAAO;UAAEP,KAAK,EAAE;QAAO,CAAE,EAClC;UAAEO,KAAK,EAAE,QAAQ;UAAEP,KAAK,EAAE;QAAQ,CAAE;OAEvC;KAEJ,EACD;MACElB,EAAE,EAAE,SAAS;MACbU,KAAK,EAAE,SAAS;MAChBF,IAAI,EAAE,mBAAmB;MACzBI,WAAW,EAAE,4CAA4C;MACzDoD,QAAQ,EAAE,CACR;QACEhE,EAAE,EAAE,mBAAmB;QACvB6D,IAAI,EAAE,QAAQ;QACdpC,KAAK,EAAE,oBAAoB;QAC3Bb,WAAW,EAAE,0BAA0B;QACvCM,KAAK,EAAE,MAAM;QACba,OAAO,EAAE,CACP;UAAEN,KAAK,EAAE,UAAU;UAAEP,KAAK,EAAE;QAAQ,CAAE,EACtC;UAAEO,KAAK,EAAE,cAAc;UAAEP,KAAK,EAAE;QAAM,CAAE,EACxC;UAAEO,KAAK,EAAE,iBAAiB;UAAEP,KAAK,EAAE;QAAY,CAAE,EACjD;UAAEO,KAAK,EAAE,SAAS;UAAEP,KAAK,EAAE;QAAS,CAAE;OAEzC,EACD;QACElB,EAAE,EAAE,kBAAkB;QACtB6D,IAAI,EAAE,QAAQ;QACdpC,KAAK,EAAE,oBAAoB;QAC3Bb,WAAW,EAAE,oCAAoC;QACjDM,KAAK,EAAE;OACR,EACD;QACElB,EAAE,EAAE,qBAAqB;QACzB6D,IAAI,EAAE,QAAQ;QACdpC,KAAK,EAAE,uBAAuB;QAC9Bb,WAAW,EAAE,0CAA0C;QACvDM,KAAK,EAAE;OACR;KAEJ,CACF;IAED;IACA,MAAM4D,oBAAoB,GAAG,IAAI,CAACC,uBAAuB,EAAE;IAC3D,OAAO,CAAC,GAAGF,YAAY,EAAE,GAAGC,oBAAoB,CAAC;EACnD;EAEAC,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACX,WAAW,EAAE,OAAO,EAAE;IAEhC,MAAMY,QAAQ,GAAsB,EAAE;IAEtC;IACA,IAAI,IAAI,CAACZ,WAAW,KAAK,aAAa,IAAI,IAAI,CAACA,WAAW,KAAK,OAAO,EAAE;MACtEY,QAAQ,CAACC,IAAI,CAAC;QACZjF,EAAE,EAAE,QAAQ;QACZU,KAAK,EAAE,QAAQ;QACfF,IAAI,EAAE,aAAa;QACnBI,WAAW,EAAE,oCAAoC;QACjDsE,kBAAkB,EAAE,eAAe;QACnClB,QAAQ,EAAE,CACR;UACEhE,EAAE,EAAE,iBAAiB;UACrB6D,IAAI,EAAE,QAAQ;UACdpC,KAAK,EAAE,kBAAkB;UACzBb,WAAW,EAAE,wCAAwC;UACrDM,KAAK,EAAE;SACR,EACD;UACElB,EAAE,EAAE,kBAAkB;UACtB6D,IAAI,EAAE,QAAQ;UACdpC,KAAK,EAAE,mBAAmB;UAC1Bb,WAAW,EAAE,8BAA8B;UAC3CM,KAAK,EAAE;SACR,EACD;UACElB,EAAE,EAAE,gBAAgB;UACpB6D,IAAI,EAAE,OAAO;UACbpC,KAAK,EAAE,2BAA2B;UAClCb,WAAW,EAAE,mCAAmC;UAChDM,KAAK,EAAE,EAAE;UACTqB,GAAG,EAAE,CAAC;UACNC,GAAG,EAAE,GAAG;UACRC,IAAI,EAAE;SACP;OAEJ,CAAC;;IAGJ;IACA,IAAI,IAAI,CAAC0B,qBAAqB,CAACgB,SAAS,CAAC,IAAI,CAACf,WAAW,CAAC,EAAE;MAC1DY,QAAQ,CAACC,IAAI,CAAC;QACZjF,EAAE,EAAE,MAAM;QACVU,KAAK,EAAE,iBAAiB;QACxBF,IAAI,EAAE,cAAc;QACpBI,WAAW,EAAE,2CAA2C;QACxDoD,QAAQ,EAAE,CACR;UACEhE,EAAE,EAAE,gBAAgB;UACpB6D,IAAI,EAAE,QAAQ;UACdpC,KAAK,EAAE,iBAAiB;UACxBb,WAAW,EAAE,mCAAmC;UAChDM,KAAK,EAAE,YAAY;UACnBa,OAAO,EAAE,CACP;YAAEN,KAAK,EAAE,QAAQ;YAAEP,KAAK,EAAE;UAAQ,CAAE,EACpC;YAAEO,KAAK,EAAE,YAAY;YAAEP,KAAK,EAAE;UAAY,CAAE,EAC5C;YAAEO,KAAK,EAAE,WAAW;YAAEP,KAAK,EAAE;UAAM,CAAE;SAExC,EACD;UACElB,EAAE,EAAE,iBAAiB;UACrB6D,IAAI,EAAE,QAAQ;UACdpC,KAAK,EAAE,mBAAmB;UAC1Bb,WAAW,EAAE,4CAA4C;UACzDM,KAAK,EAAE;SACR,EACD;UACElB,EAAE,EAAE,qBAAqB;UACzB6D,IAAI,EAAE,QAAQ;UACdpC,KAAK,EAAE,uBAAuB;UAC9Bb,WAAW,EAAE,oCAAoC;UACjDM,KAAK,EAAE,QAAQ;UACfa,OAAO,EAAE,CACP;YAAEN,KAAK,EAAE,OAAO;YAAEP,KAAK,EAAE;UAAO,CAAE,EAClC;YAAEO,KAAK,EAAE,QAAQ;YAAEP,KAAK,EAAE;UAAQ,CAAE,EACpC;YAAEO,KAAK,EAAE,SAAS;YAAEP,KAAK,EAAE;UAAS,CAAE;SAEzC;OAEJ,CAAC;;IAGJ;IACA,MAAMkE,kBAAkB,GAAG,IAAI,CAACC,6BAA6B,EAAE;IAC/DL,QAAQ,CAACC,IAAI,CAAC,GAAGG,kBAAkB,CAAC;IAEpC,OAAOJ,QAAQ;EACjB;EAEAK,6BAA6BA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAAChB,UAAU,EAAE,OAAO,EAAE;IAE/B,MAAMW,QAAQ,GAAsB,EAAE;IAEtC,QAAQ,IAAI,CAACX,UAAU,CAACiB,UAAU;MAChC,KAAK,OAAO;QACVN,QAAQ,CAACC,IAAI,CAAC;UACZjF,EAAE,EAAE,OAAO;UACXU,KAAK,EAAE,gBAAgB;UACvBF,IAAI,EAAE,mBAAmB;UACzBI,WAAW,EAAE,sCAAsC;UACnDoD,QAAQ,EAAE,CACR;YACEhE,EAAE,EAAE,aAAa;YACjB6D,IAAI,EAAE,OAAO;YACbpC,KAAK,EAAE,sBAAsB;YAC7Bb,WAAW,EAAE,kCAAkC;YAC/CM,KAAK,EAAE,EAAE;YACTiB,WAAW,EAAE;WACd,EACD;YACEnC,EAAE,EAAE,gBAAgB;YACpB6D,IAAI,EAAE,OAAO;YACbpC,KAAK,EAAE,qBAAqB;YAC5Bb,WAAW,EAAE,iCAAiC;YAC9CM,KAAK,EAAE,CAAC;YACRqB,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE,EAAE;YACPC,IAAI,EAAE;WACP,EACD;YACEzC,EAAE,EAAE,mBAAmB;YACvB6D,IAAI,EAAE,QAAQ;YACdpC,KAAK,EAAE,qBAAqB;YAC5Bb,WAAW,EAAE,uCAAuC;YACpDM,KAAK,EAAE;WACR;SAEJ,CAAC;QACF;MAEF,KAAK,WAAW;QACd8D,QAAQ,CAACC,IAAI,CAAC;UACZjF,EAAE,EAAE,WAAW;UACfU,KAAK,EAAE,oBAAoB;UAC3BF,IAAI,EAAE,iBAAiB;UACvBI,WAAW,EAAE,0CAA0C;UACvDoD,QAAQ,EAAE,CACR;YACEhE,EAAE,EAAE,gBAAgB;YACpB6D,IAAI,EAAE,OAAO;YACbpC,KAAK,EAAE,yBAAyB;YAChCb,WAAW,EAAE,8BAA8B;YAC3CM,KAAK,EAAE,EAAE;YACTiB,WAAW,EAAE;WACd,EACD;YACEnC,EAAE,EAAE,iBAAiB;YACrB6D,IAAI,EAAE,QAAQ;YACdpC,KAAK,EAAE,uBAAuB;YAC9Bb,WAAW,EAAE,0CAA0C;YACvDM,KAAK,EAAE;WACR,EACD;YACElB,EAAE,EAAE,iBAAiB;YACrB6D,IAAI,EAAE,QAAQ;YACdpC,KAAK,EAAE,yBAAyB;YAChCb,WAAW,EAAE,oCAAoC;YACjDM,KAAK,EAAE,WAAW;YAClBa,OAAO,EAAE,CACP;cAAEN,KAAK,EAAE,WAAW;cAAEP,KAAK,EAAE;YAAW,CAAE,EAC1C;cAAEO,KAAK,EAAE,UAAU;cAAEP,KAAK,EAAE;YAAU,CAAE,EACxC;cAAEO,KAAK,EAAE,SAAS;cAAEP,KAAK,EAAE;YAAS,CAAE,EACtC;cAAEO,KAAK,EAAE,UAAU;cAAEP,KAAK,EAAE;YAAU,CAAE;WAE3C;SAEJ,CAAC;QACF;MAEF,KAAK,SAAS;QACZ8D,QAAQ,CAACC,IAAI,CAAC;UACZjF,EAAE,EAAE,SAAS;UACbU,KAAK,EAAE,kBAAkB;UACzBF,IAAI,EAAE,gBAAgB;UACtBI,WAAW,EAAE,wCAAwC;UACrDoD,QAAQ,EAAE,CACR;YACEhE,EAAE,EAAE,kBAAkB;YACtB6D,IAAI,EAAE,QAAQ;YACdpC,KAAK,EAAE,qBAAqB;YAC5Bb,WAAW,EAAE,qCAAqC;YAClDM,KAAK,EAAE;WACR,EACD;YACElB,EAAE,EAAE,oBAAoB;YACxB6D,IAAI,EAAE,OAAO;YACbpC,KAAK,EAAE,8BAA8B;YACrCb,WAAW,EAAE,uCAAuC;YACpDM,KAAK,EAAE,CAAC;YACRqB,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE,EAAE;YACPC,IAAI,EAAE;WACP,EACD;YACEzC,EAAE,EAAE,qBAAqB;YACzB6D,IAAI,EAAE,OAAO;YACbpC,KAAK,EAAE,8BAA8B;YACrCb,WAAW,EAAE,uCAAuC;YACpDM,KAAK,EAAE,CAAC;YACRqB,GAAG,EAAE,CAAC;YACNC,GAAG,EAAE,EAAE;YACPC,IAAI,EAAE;WACP;SAEJ,CAAC;QACF;;IAGJ,OAAOuC,QAAQ;EACjB;EAEAjF,gBAAgBA,CAACwF,SAAiB;IAChC,IAAI,CAAClF,aAAa,GAAGkF,SAAS;EAChC;EAEAnE,eAAeA,CAACoE,OAAqB,EAAEC,KAAU;IAC/C,MAAMvE,KAAK,GAAGuE,KAAK,CAACC,MAAM,GAAGD,KAAK,CAACC,MAAM,CAACxE,KAAK,GAAGuE,KAAK;IACvDD,OAAO,CAACtE,KAAK,GAAGsE,OAAO,CAAC3B,IAAI,KAAK,QAAQ,GAAG4B,KAAK,CAACC,MAAM,CAACC,OAAO,GAAGzE,KAAK;IAExE,IAAIsE,OAAO,CAACI,QAAQ,EAAE;MACpBJ,OAAO,CAACI,QAAQ,CAACJ,OAAO,CAACtE,KAAK,CAAC;;EAEnC;EAEA8B,YAAYA,CAACwC,OAAqB,EAAEC,KAAU;IAC5C,MAAMI,IAAI,GAAGJ,KAAK,CAACC,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;IAClC,IAAID,IAAI,EAAE;MACRL,OAAO,CAACtE,KAAK,GAAG2E,IAAI;MACpB,IAAIL,OAAO,CAACI,QAAQ,EAAE;QACpBJ,OAAO,CAACI,QAAQ,CAACC,IAAI,CAAC;;;EAG5B;EAEAhD,WAAWA,CAACgD,IAAU;IACpB,OAAOA,IAAI,GAAGA,IAAI,CAACE,IAAI,GAAG,EAAE;EAC9B;EAEAC,eAAeA,CAAA;IACb;IACAC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACzC;EAEAC,YAAYA,CAAA;IACV;IACAF,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IAEjC;IACA,IAAI,CAACE,kBAAkB,CAAC,8BAA8B,CAAC;EACzD;EAEQA,kBAAkBA,CAACC,OAAe;IACxC;IACAJ,OAAO,CAACC,GAAG,CAACG,OAAO,CAAC;EACtB;;;uBA9YWpC,0BAA0B,EAAA7E,EAAA,CAAAkH,iBAAA,CAAAC,EAAA,CAAAC,qBAAA;IAAA;EAAA;;;YAA1BvC,0BAA0B;MAAAwC,SAAA;MAAAC,MAAA;QAAAtC,WAAA;MAAA;MAAAuC,UAAA;MAAAC,QAAA,GAAAxH,EAAA,CAAAyH,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjJ7B/H,EAJN,CAAAC,cAAA,aAAgE,aAEjC,aACC,aACyC;UACjED,EAAA,CAAAa,SAAA,QAAkC;UACpCb,EAAA,CAAAe,YAAA,EAAM;UAEJf,EADF,CAAAC,cAAA,aAAyB,SACnB;UAAAD,EAAA,CAAAc,MAAA,eAAQ;UAAAd,EAAA,CAAAe,YAAA,EAAK;UACjBf,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAc,MAAA,GAAwE;;UAGjFd,EAHiF,CAAAe,YAAA,EAAI,EAC3E,EACF,EACF;UAIJf,EADF,CAAAC,cAAA,cAAiC,cACT;UACpBD,EAAA,CAAAyC,UAAA,KAAAwF,6CAAA,oBAIyC;UAK7CjI,EADE,CAAAe,YAAA,EAAM,EACF;UAGNf,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAyC,UAAA,KAAAyF,0CAAA,iBAEmD;UAoGrDlI,EAAA,CAAAe,YAAA,EAAM;UAIJf,EADF,CAAAC,cAAA,eAA8B,kBACkC;UAA5BD,EAAA,CAAAE,UAAA,mBAAAiI,6DAAA;YAAA,OAASH,GAAA,CAAApB,eAAA,EAAiB;UAAA,EAAC;UAC3D5G,EAAA,CAAAa,SAAA,aAA2B;UAC3Bb,EAAA,CAAAc,MAAA,2BACF;UAAAd,EAAA,CAAAe,YAAA,EAAS;UACTf,EAAA,CAAAC,cAAA,kBAAyD;UAAzBD,EAAA,CAAAE,UAAA,mBAAAkI,6DAAA;YAAA,OAASJ,GAAA,CAAAjB,YAAA,EAAc;UAAA,EAAC;UACtD/G,EAAA,CAAAa,SAAA,aAA2B;UAC3Bb,EAAA,CAAAc,MAAA,sBACF;UAEJd,EAFI,CAAAe,YAAA,EAAS,EACL,EACF;;;;UA7I4Bf,EAAA,CAAAkB,SAAA,GAAsC;UAAtClB,EAAA,CAAAqI,WAAA,eAAAL,GAAA,CAAA/C,UAAA,kBAAA+C,GAAA,CAAA/C,UAAA,CAAAqD,KAAA,CAAsC;UAC7DtI,EAAA,CAAAkB,SAAA,EAA0B;UAA1BlB,EAAA,CAAAmB,UAAA,CAAA6G,GAAA,CAAA/C,UAAA,kBAAA+C,GAAA,CAAA/C,UAAA,CAAA7D,IAAA,CAA0B;UAI1BpB,EAAA,CAAAkB,SAAA,GAAwE;UAAxElB,EAAA,CAAAuI,kBAAA,KAAAP,GAAA,CAAA/C,UAAA,kBAAA+C,GAAA,CAAA/C,UAAA,CAAAuD,WAAA,SAAAxI,EAAA,CAAAyI,WAAA,QAAAT,GAAA,CAAA/C,UAAA,kBAAA+C,GAAA,CAAA/C,UAAA,CAAAiB,UAAA,MAAwE;UASvDlG,EAAA,CAAAkB,SAAA,GAAyB;UAAzBlB,EAAA,CAAAiC,UAAA,YAAA+F,GAAA,CAAAxC,oBAAA,GAAyB;UAYxBxF,EAAA,CAAAkB,SAAA,GAAyB;UAAzBlB,EAAA,CAAAiC,UAAA,YAAA+F,GAAA,CAAAxC,oBAAA,GAAyB;;;qBAhC9C3F,YAAY,EAAA6I,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,aAAA,EAAE/I,WAAW,EAAAgJ,EAAA,CAAAC,cAAA,EAAAD,EAAA,CAAAE,uBAAA,EAAAF,EAAA,CAAAG,oBAAA,EAAAH,EAAA,CAAAI,kBAAA,EAAAJ,EAAA,CAAAK,4BAAA,EAAAL,EAAA,CAAAM,0BAAA,EAAAN,EAAA,CAAAO,eAAA,EAAAP,EAAA,CAAAQ,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}