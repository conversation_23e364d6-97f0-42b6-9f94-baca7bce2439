import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { io, Socket } from 'socket.io-client';
import { environment } from '../../../environments/environment';
import { AuthService } from './auth.service';

export interface RealTimeEvent {
  type: string;
  data: any;
  timestamp: Date;
}

@Injectable({
  providedIn: 'root'
})
export class RealtimeService {
  private socket: Socket | null = null;
  private connected = new BehaviorSubject<boolean>(false);
  private events = new BehaviorSubject<RealTimeEvent | null>(null);

  // Observables
  public connected$ = this.connected.asObservable();
  public events$ = this.events.asObservable();

  // Specific event subjects
  private cartUpdates = new BehaviorSubject<any>(null);
  private wishlistUpdates = new BehaviorSubject<any>(null);
  private postUpdates = new BehaviorSubject<any>(null);
  private storyUpdates = new BehaviorSubject<any>(null);
  private productUpdates = new BehaviorSubject<any>(null);

  // Specific observables
  public cartUpdates$ = this.cartUpdates.asObservable();
  public wishlistUpdates$ = this.wishlistUpdates.asObservable();
  public postUpdates$ = this.postUpdates.asObservable();
  public storyUpdates$ = this.storyUpdates.asObservable();
  public productUpdates$ = this.productUpdates.asObservable();

  constructor(private authService: AuthService) {
    // Auto-connect when user is authenticated
    this.authService.isAuthenticated$.subscribe(isAuth => {
      if (isAuth) {
        this.connect();
      } else {
        this.disconnect();
      }
    });
  }

  connect(): void {
    if (this.socket?.connected) {
      return;
    }

    const token = localStorage.getItem('token');
    
    this.socket = io(environment.socketUrl, {
      auth: {
        token: token
      },
      transports: ['websocket', 'polling']
    });

    this.setupEventListeners();
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.connected.next(false);
    }
  }

  private setupEventListeners(): void {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('🔌 Connected to real-time server');
      this.connected.next(true);
    });

    this.socket.on('disconnect', () => {
      console.log('🔌 Disconnected from real-time server');
      this.connected.next(false);
    });

    this.socket.on('connect_error', (error) => {
      console.error('🚨 Socket connection error:', error);
      this.connected.next(false);
    });

    // Cart events
    this.socket.on('cart:updated', (data) => {
      console.log('🛒 Cart updated:', data);
      this.cartUpdates.next(data);
      this.emitEvent('cart:updated', data);
    });

    // Wishlist events
    this.socket.on('wishlist:updated', (data) => {
      console.log('❤️ Wishlist updated:', data);
      this.wishlistUpdates.next(data);
      this.emitEvent('wishlist:updated', data);
    });

    // Post events
    this.socket.on('post:liked', (data) => {
      console.log('👍 Post liked:', data);
      this.postUpdates.next({ type: 'liked', ...data });
      this.emitEvent('post:liked', data);
    });

    this.socket.on('post:commented', (data) => {
      console.log('💬 Post commented:', data);
      this.postUpdates.next({ type: 'commented', ...data });
      this.emitEvent('post:commented', data);
    });

    // Story events
    this.socket.on('story:viewed', (data) => {
      console.log('👁️ Story viewed:', data);
      this.storyUpdates.next({ type: 'viewed', ...data });
      this.emitEvent('story:viewed', data);
    });

    // Product events
    this.socket.on('product:viewed', (data) => {
      console.log('👀 Product viewed:', data);
      this.productUpdates.next({ type: 'viewed', ...data });
      this.emitEvent('product:viewed', data);
    });
  }

  private emitEvent(type: string, data: any): void {
    this.events.next({
      type,
      data,
      timestamp: new Date()
    });
  }

  // Emit events to server
  emitCartAdd(item: any): void {
    this.socket?.emit('cart:add', item);
  }

  emitCartRemove(itemId: string): void {
    this.socket?.emit('cart:remove', { itemId });
  }

  emitCartUpdate(item: any): void {
    this.socket?.emit('cart:update', item);
  }

  emitWishlistAdd(item: any): void {
    this.socket?.emit('wishlist:add', item);
  }

  emitWishlistRemove(itemId: string): void {
    this.socket?.emit('wishlist:remove', { itemId });
  }

  emitPostLike(postId: string): void {
    this.socket?.emit('post:like', { postId });
  }

  emitPostComment(postId: string, comment: any): void {
    this.socket?.emit('post:comment', { postId, comment });
  }

  emitStoryView(storyId: string, storyOwnerId: string): void {
    this.socket?.emit('story:view', { storyId, storyOwnerId });
  }

  emitProductView(productId: string): void {
    this.socket?.emit('product:view', { productId });
  }

  // Room management
  joinProductRoom(productId: string): void {
    this.socket?.emit('join:product', productId);
  }

  leaveProductRoom(productId: string): void {
    this.socket?.emit('leave:product', productId);
  }

  joinPostRoom(postId: string): void {
    this.socket?.emit('join:post', postId);
  }

  leavePostRoom(postId: string): void {
    this.socket?.emit('leave:post', postId);
  }

  // Utility methods
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  getConnectionStatus(): Observable<boolean> {
    return this.connected$;
  }

  // Subscribe to specific event types
  onCartUpdate(): Observable<any> {
    return this.cartUpdates$;
  }

  onWishlistUpdate(): Observable<any> {
    return this.wishlistUpdates$;
  }

  onPostUpdate(): Observable<any> {
    return this.postUpdates$;
  }

  onStoryUpdate(): Observable<any> {
    return this.storyUpdates$;
  }

  onProductUpdate(): Observable<any> {
    return this.productUpdates$;
  }
}
