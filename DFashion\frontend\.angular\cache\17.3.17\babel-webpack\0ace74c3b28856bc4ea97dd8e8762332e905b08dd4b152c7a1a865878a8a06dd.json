{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host, f as getElement, d as createEvent } from './index-a1a47f01.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nimport { p as isEndSide } from './helpers-be245865.js';\nimport { f as findClosestIonContent, d as disableContentScrollY, r as resetContentScrollY } from './index-f3946ac1.js';\nimport { w as watchForOptions } from './watch-options-c2911ace.js';\nimport './index-9b0d46f4.js';\nconst itemOptionIosCss = \":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit)}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.button-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-padding-start:0.7em;padding-inline-start:0.7em;-webkit-padding-end:0.7em;padding-inline-end:0.7em;padding-top:0;padding-bottom:0;display:inline-block;position:relative;width:100%;height:100%;border:0;outline:none;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}.button-inner{display:-ms-flexbox;display:flex;-ms-flex-flow:column nowrap;flex-flow:column nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.horizontal-wrapper{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%}::slotted(*){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:5px;margin-inline-end:5px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:5px;margin-inline-start:5px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}::slotted([slot=icon-only]){padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:0;margin-bottom:0;min-width:0.9em;font-size:1.8em}:host(.item-option-expandable){-ms-flex-negative:0;flex-shrink:0;-webkit-transition-duration:0;transition-duration:0;-webkit-transition-property:none;transition-property:none;-webkit-transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1);transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1)}:host(.item-option-disabled){pointer-events:none}:host(.item-option-disabled) .button-native{cursor:default;opacity:0.5;pointer-events:none}:host{font-size:clamp(16px, 1rem, 35.2px)}:host(.ion-activated){background:var(--ion-color-primary-shade, #3171e0)}:host(.ion-color.ion-activated){background:var(--ion-color-shade)}\";\nconst IonItemOptionIosStyle0 = itemOptionIosCss;\nconst itemOptionMdCss = \":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit)}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.button-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-padding-start:0.7em;padding-inline-start:0.7em;-webkit-padding-end:0.7em;padding-inline-end:0.7em;padding-top:0;padding-bottom:0;display:inline-block;position:relative;width:100%;height:100%;border:0;outline:none;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}.button-inner{display:-ms-flexbox;display:flex;-ms-flex-flow:column nowrap;flex-flow:column nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.horizontal-wrapper{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%}::slotted(*){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:5px;margin-inline-end:5px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:5px;margin-inline-start:5px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}::slotted([slot=icon-only]){padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:0;margin-bottom:0;min-width:0.9em;font-size:1.8em}:host(.item-option-expandable){-ms-flex-negative:0;flex-shrink:0;-webkit-transition-duration:0;transition-duration:0;-webkit-transition-property:none;transition-property:none;-webkit-transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1);transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1)}:host(.item-option-disabled){pointer-events:none}:host(.item-option-disabled) .button-native{cursor:default;opacity:0.5;pointer-events:none}:host{font-size:0.875rem;font-weight:500;text-transform:uppercase}\";\nconst IonItemOptionMdStyle0 = itemOptionMdCss;\nconst ItemOption = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.onClick = ev => {\n      const el = ev.target.closest('ion-item-option');\n      if (el) {\n        ev.preventDefault();\n      }\n    };\n    this.color = undefined;\n    this.disabled = false;\n    this.download = undefined;\n    this.expandable = false;\n    this.href = undefined;\n    this.rel = undefined;\n    this.target = undefined;\n    this.type = 'button';\n  }\n  render() {\n    const {\n      disabled,\n      expandable,\n      href\n    } = this;\n    const TagType = href === undefined ? 'button' : 'a';\n    const mode = getIonMode(this);\n    const attrs = TagType === 'button' ? {\n      type: this.type\n    } : {\n      download: this.download,\n      href: this.href,\n      target: this.target\n    };\n    return h(Host, {\n      key: '763c3a7571b143d1068d85103ccab403bc48abae',\n      onClick: this.onClick,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'item-option-disabled': disabled,\n        'item-option-expandable': expandable,\n        'ion-activatable': true\n      })\n    }, h(TagType, Object.assign({\n      key: 'cb199c2ccd38abaad3460f184af3093bf08546cc'\n    }, attrs, {\n      class: \"button-native\",\n      part: \"native\",\n      disabled: disabled\n    }), h(\"span\", {\n      key: 'f3ce9f1d343890c6f55f2609127f1e5113a2eedf',\n      class: \"button-inner\"\n    }, h(\"slot\", {\n      key: 'cd9434883c0bdb4129fb6f49970d49710653a09a',\n      name: \"top\"\n    }), h(\"div\", {\n      key: '764529c5f4b3d82105ce55885e8f121a91e8bc4a',\n      class: \"horizontal-wrapper\"\n    }, h(\"slot\", {\n      key: '5bbd7b9ed9f35c8bf422c3134a1a097e174ad6df',\n      name: \"start\"\n    }), h(\"slot\", {\n      key: '1e70a781cdf4ffcefb1dea70abe43655d7857c4b',\n      name: \"icon-only\"\n    }), h(\"slot\", {\n      key: 'c3205e9b1577a56786c10a8b5b420010b5fe53fc'\n    }), h(\"slot\", {\n      key: '6bae6c98cd8d8526a203af47ca8e83753e1e1cb6',\n      name: \"end\"\n    })), h(\"slot\", {\n      key: '466cc32cdf9cbbdbb58e4b29144215cf2984c0d6',\n      name: \"bottom\"\n    })), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: 'b5c54b801008b307ca8f718a41101be3e8d1d938'\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nItemOption.style = {\n  ios: IonItemOptionIosStyle0,\n  md: IonItemOptionMdStyle0\n};\nconst itemOptionsIosCss = \"ion-item-options{top:0;right:0;-ms-flex-pack:end;justify-content:flex-end;display:none;position:absolute;height:100%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1}:host-context([dir=rtl]) ion-item-options{-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] ion-item-options{-ms-flex-pack:start;justify-content:flex-start}[dir=rtl] ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){ion-item-options:dir(rtl){-ms-flex-pack:start;justify-content:flex-start}ion-item-options:dir(rtl):not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}}.item-options-start{right:auto;left:0;-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) .item-options-start{-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] .item-options-start{-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){.item-options-start:dir(rtl){-ms-flex-pack:end;justify-content:flex-end}}[dir=ltr] .item-options-start ion-item-option:first-child,[dir=rtl] .item-options-start ion-item-option:last-child{padding-left:var(--ion-safe-area-left)}[dir=ltr] .item-options-end ion-item-option:last-child,[dir=rtl] .item-options-end ion-item-option:first-child{padding-right:var(--ion-safe-area-right)}:host-context([dir=rtl]) .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}[dir=rtl] .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}@supports selector(:dir(rtl)){.item-sliding-active-slide:dir(rtl).item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}}.item-sliding-active-slide ion-item-options{display:-ms-flexbox;display:flex;visibility:hidden}.item-sliding-active-slide.item-sliding-active-options-start .item-options-start,.item-sliding-active-slide.item-sliding-active-options-end ion-item-options:not(.item-options-start){width:100%;visibility:visible}.item-options-ios{border-bottom-width:0;border-bottom-style:solid;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)))}.item-options-ios.item-options-end{border-bottom-width:0.55px}.list-ios-lines-none .item-options-ios{border-bottom-width:0}.list-ios-lines-full .item-options-ios,.list-ios-lines-inset .item-options-ios.item-options-end{border-bottom-width:0.55px}\";\nconst IonItemOptionsIosStyle0 = itemOptionsIosCss;\nconst itemOptionsMdCss = \"ion-item-options{top:0;right:0;-ms-flex-pack:end;justify-content:flex-end;display:none;position:absolute;height:100%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1}:host-context([dir=rtl]) ion-item-options{-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] ion-item-options{-ms-flex-pack:start;justify-content:flex-start}[dir=rtl] ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){ion-item-options:dir(rtl){-ms-flex-pack:start;justify-content:flex-start}ion-item-options:dir(rtl):not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}}.item-options-start{right:auto;left:0;-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) .item-options-start{-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] .item-options-start{-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){.item-options-start:dir(rtl){-ms-flex-pack:end;justify-content:flex-end}}[dir=ltr] .item-options-start ion-item-option:first-child,[dir=rtl] .item-options-start ion-item-option:last-child{padding-left:var(--ion-safe-area-left)}[dir=ltr] .item-options-end ion-item-option:last-child,[dir=rtl] .item-options-end ion-item-option:first-child{padding-right:var(--ion-safe-area-right)}:host-context([dir=rtl]) .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}[dir=rtl] .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}@supports selector(:dir(rtl)){.item-sliding-active-slide:dir(rtl).item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}}.item-sliding-active-slide ion-item-options{display:-ms-flexbox;display:flex;visibility:hidden}.item-sliding-active-slide.item-sliding-active-options-start .item-options-start,.item-sliding-active-slide.item-sliding-active-options-end ion-item-options:not(.item-options-start){width:100%;visibility:visible}.item-options-md{border-bottom-width:0;border-bottom-style:solid;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))))}.list-md-lines-none .item-options-md{border-bottom-width:0}.list-md-lines-full .item-options-md,.list-md-lines-inset .item-options-md.item-options-end{border-bottom-width:1px}\";\nconst IonItemOptionsMdStyle0 = itemOptionsMdCss;\nconst ItemOptions = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionSwipe = createEvent(this, \"ionSwipe\", 7);\n    this.side = 'end';\n  }\n  /** @internal */\n  fireSwipeEvent() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.ionSwipe.emit({\n        side: _this.side\n      });\n    })();\n  }\n  render() {\n    const mode = getIonMode(this);\n    const isEnd = isEndSide(this.side);\n    return h(Host, {\n      key: '3dca0415ec2942ac8e87a057e26bcb290a892f65',\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`item-options-${mode}`]: true,\n        /**\n         * Note: The \"start\" and \"end\" terms refer to the\n         * direction ion-item-option instances within ion-item-options flow.\n         * They do not refer to how ion-item-options flows within ion-item-sliding.\n         * As a result, \"item-options-start\" means the ion-item-options container\n         * always appears on the left, and \"item-options-end\" means the ion-item-options\n         * container always appears on the right.\n         */\n        'item-options-start': !isEnd,\n        'item-options-end': isEnd\n      }\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nItemOptions.style = {\n  ios: IonItemOptionsIosStyle0,\n  md: IonItemOptionsMdStyle0\n};\nconst itemSlidingCss = \"ion-item-sliding{display:block;position:relative;width:100%;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}ion-item-sliding .item{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.item-sliding-active-slide .item{position:relative;-webkit-transition:-webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:-webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1), -webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);opacity:1;z-index:2;pointer-events:none;will-change:transform}.item-sliding-closing ion-item-options{pointer-events:none}.item-sliding-active-swipe-end .item-options-end .item-option-expandable{padding-left:100%;-ms-flex-order:1;order:1;-webkit-transition-duration:0.6s;transition-duration:0.6s;-webkit-transition-property:padding-left;transition-property:padding-left}:host-context([dir=rtl]) .item-sliding-active-swipe-end .item-options-end .item-option-expandable{-ms-flex-order:-1;order:-1}[dir=rtl] .item-sliding-active-swipe-end .item-options-end .item-option-expandable{-ms-flex-order:-1;order:-1}@supports selector(:dir(rtl)){.item-sliding-active-swipe-end .item-options-end .item-option-expandable:dir(rtl){-ms-flex-order:-1;order:-1}}.item-sliding-active-swipe-start .item-options-start .item-option-expandable{padding-right:100%;-ms-flex-order:-1;order:-1;-webkit-transition-duration:0.6s;transition-duration:0.6s;-webkit-transition-property:padding-right;transition-property:padding-right}:host-context([dir=rtl]) .item-sliding-active-swipe-start .item-options-start .item-option-expandable{-ms-flex-order:1;order:1}[dir=rtl] .item-sliding-active-swipe-start .item-options-start .item-option-expandable{-ms-flex-order:1;order:1}@supports selector(:dir(rtl)){.item-sliding-active-swipe-start .item-options-start .item-option-expandable:dir(rtl){-ms-flex-order:1;order:1}}\";\nconst IonItemSlidingStyle0 = itemSlidingCss;\nconst SWIPE_MARGIN = 30;\nconst ELASTIC_FACTOR = 0.55;\nlet openSlidingItem;\nconst ItemSliding = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionDrag = createEvent(this, \"ionDrag\", 7);\n    this.item = null;\n    this.openAmount = 0;\n    this.initialOpenAmount = 0;\n    this.optsWidthRightSide = 0;\n    this.optsWidthLeftSide = 0;\n    this.sides = 0 /* ItemSide.None */;\n    this.optsDirty = true;\n    this.contentEl = null;\n    this.initialContentScrollY = true;\n    this.state = 2 /* SlidingState.Disabled */;\n    this.disabled = false;\n  }\n  disabledChanged() {\n    if (this.gesture) {\n      this.gesture.enable(!this.disabled);\n    }\n  }\n  connectedCallback() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        el\n      } = _this2;\n      _this2.item = el.querySelector('ion-item');\n      _this2.contentEl = findClosestIonContent(el);\n      /**\n       * The MutationObserver needs to be added before we\n       * call updateOptions below otherwise we may miss\n       * ion-item-option elements that are added to the DOM\n       * while updateOptions is running and before the MutationObserver\n       * has been initialized.\n       */\n      _this2.mutationObserver = watchForOptions(el, 'ion-item-option', /*#__PURE__*/_asyncToGenerator(function* () {\n        yield _this2.updateOptions();\n      }));\n      yield _this2.updateOptions();\n      _this2.gesture = (yield import('./index-2cf77112.js')).createGesture({\n        el,\n        gestureName: 'item-swipe',\n        gesturePriority: 100,\n        threshold: 5,\n        canStart: ev => _this2.canStart(ev),\n        onStart: () => _this2.onStart(),\n        onMove: ev => _this2.onMove(ev),\n        onEnd: ev => _this2.onEnd(ev)\n      });\n      _this2.disabledChanged();\n    })();\n  }\n  disconnectedCallback() {\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n    this.item = null;\n    this.leftOptions = this.rightOptions = undefined;\n    if (openSlidingItem === this.el) {\n      openSlidingItem = undefined;\n    }\n    if (this.mutationObserver) {\n      this.mutationObserver.disconnect();\n      this.mutationObserver = undefined;\n    }\n  }\n  /**\n   * Get the amount the item is open in pixels.\n   */\n  getOpenAmount() {\n    return Promise.resolve(this.openAmount);\n  }\n  /**\n   * Get the ratio of the open amount of the item compared to the width of the options.\n   * If the number returned is positive, then the options on the right side are open.\n   * If the number returned is negative, then the options on the left side are open.\n   * If the absolute value of the number is greater than 1, the item is open more than\n   * the width of the options.\n   */\n  getSlidingRatio() {\n    return Promise.resolve(this.getSlidingRatioSync());\n  }\n  /**\n   * Open the sliding item.\n   *\n   * @param side The side of the options to open. If a side is not provided, it will open the first set of options it finds within the item.\n   */\n  open(side) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      var _a;\n      /**\n       * It is possible for the item to be added to the DOM\n       * after the item-sliding component was created. As a result,\n       * if this.item is null, then we should attempt to\n       * query for the ion-item again.\n       * However, if the item is already defined then\n       * we do not query for it again.\n       */\n      const item = _this3.item = (_a = _this3.item) !== null && _a !== void 0 ? _a : _this3.el.querySelector('ion-item');\n      if (item === null) {\n        return;\n      }\n      const optionsToOpen = _this3.getOptions(side);\n      if (!optionsToOpen) {\n        return;\n      }\n      /**\n       * If side is not set, we need to infer the side\n       * so we know which direction to move the options\n       */\n      if (side === undefined) {\n        side = optionsToOpen === _this3.leftOptions ? 'start' : 'end';\n      }\n      // In RTL we want to switch the sides\n      side = isEndSide(side) ? 'end' : 'start';\n      const isStartOpen = _this3.openAmount < 0;\n      const isEndOpen = _this3.openAmount > 0;\n      /**\n       * If a side is open and a user tries to\n       * re-open the same side, we should not do anything\n       */\n      if (isStartOpen && optionsToOpen === _this3.leftOptions) {\n        return;\n      }\n      if (isEndOpen && optionsToOpen === _this3.rightOptions) {\n        return;\n      }\n      _this3.closeOpened();\n      _this3.state = 4 /* SlidingState.Enabled */;\n      requestAnimationFrame(() => {\n        _this3.calculateOptsWidth();\n        const width = side === 'end' ? _this3.optsWidthRightSide : -_this3.optsWidthLeftSide;\n        openSlidingItem = _this3.el;\n        _this3.setOpenAmount(width, false);\n        _this3.state = side === 'end' ? 8 /* SlidingState.End */ : 16 /* SlidingState.Start */;\n      });\n    })();\n  }\n  /**\n   * Close the sliding item. Items can also be closed from the [List](./list).\n   */\n  close() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      _this4.setOpenAmount(0, true);\n    })();\n  }\n  /**\n   * Close all of the sliding items in the list. Items can also be closed from the [List](./list).\n   */\n  closeOpened() {\n    return _asyncToGenerator(function* () {\n      if (openSlidingItem !== undefined) {\n        openSlidingItem.close();\n        openSlidingItem = undefined;\n        return true;\n      }\n      return false;\n    })();\n  }\n  /**\n   * Given an optional side, return the ion-item-options element.\n   *\n   * @param side This side of the options to get. If a side is not provided it will\n   * return the first one available.\n   */\n  getOptions(side) {\n    if (side === undefined) {\n      return this.leftOptions || this.rightOptions;\n    } else if (side === 'start') {\n      return this.leftOptions;\n    } else {\n      return this.rightOptions;\n    }\n  }\n  updateOptions() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const options = _this5.el.querySelectorAll('ion-item-options');\n      let sides = 0;\n      // Reset left and right options in case they were removed\n      _this5.leftOptions = _this5.rightOptions = undefined;\n      for (let i = 0; i < options.length; i++) {\n        const item = options.item(i);\n        /**\n         * We cannot use the componentOnReady helper\n         * util here since we need to wait for all of these items\n         * to be ready before we set `this.sides` and `this.optsDirty`.\n         */\n        // eslint-disable-next-line custom-rules/no-component-on-ready-method\n        const option = item.componentOnReady !== undefined ? yield item.componentOnReady() : item;\n        const side = isEndSide(option.side) ? 'end' : 'start';\n        if (side === 'start') {\n          _this5.leftOptions = option;\n          sides |= 1 /* ItemSide.Start */;\n        } else {\n          _this5.rightOptions = option;\n          sides |= 2 /* ItemSide.End */;\n        }\n      }\n      _this5.optsDirty = true;\n      _this5.sides = sides;\n    })();\n  }\n  canStart(gesture) {\n    /**\n     * If very close to start of the screen\n     * do not open left side so swipe to go\n     * back will still work.\n     */\n    const rtl = document.dir === 'rtl';\n    const atEdge = rtl ? window.innerWidth - gesture.startX < 15 : gesture.startX < 15;\n    if (atEdge) {\n      return false;\n    }\n    const selected = openSlidingItem;\n    if (selected && selected !== this.el) {\n      this.closeOpened();\n    }\n    return !!(this.rightOptions || this.leftOptions);\n  }\n  onStart() {\n    /**\n     * We need to query for the ion-item\n     * every time the gesture starts. Developers\n     * may toggle ion-item elements via *ngIf.\n     */\n    this.item = this.el.querySelector('ion-item');\n    const {\n      contentEl\n    } = this;\n    if (contentEl) {\n      this.initialContentScrollY = disableContentScrollY(contentEl);\n    }\n    openSlidingItem = this.el;\n    if (this.tmr !== undefined) {\n      clearTimeout(this.tmr);\n      this.tmr = undefined;\n    }\n    if (this.openAmount === 0) {\n      this.optsDirty = true;\n      this.state = 4 /* SlidingState.Enabled */;\n    }\n    this.initialOpenAmount = this.openAmount;\n    if (this.item) {\n      this.item.style.transition = 'none';\n    }\n  }\n  onMove(gesture) {\n    if (this.optsDirty) {\n      this.calculateOptsWidth();\n    }\n    let openAmount = this.initialOpenAmount - gesture.deltaX;\n    switch (this.sides) {\n      case 2 /* ItemSide.End */:\n        openAmount = Math.max(0, openAmount);\n        break;\n      case 1 /* ItemSide.Start */:\n        openAmount = Math.min(0, openAmount);\n        break;\n      case 3 /* ItemSide.Both */:\n        break;\n      case 0 /* ItemSide.None */:\n        return;\n      default:\n        console.warn('invalid ItemSideFlags value', this.sides);\n        break;\n    }\n    let optsWidth;\n    if (openAmount > this.optsWidthRightSide) {\n      optsWidth = this.optsWidthRightSide;\n      openAmount = optsWidth + (openAmount - optsWidth) * ELASTIC_FACTOR;\n    } else if (openAmount < -this.optsWidthLeftSide) {\n      optsWidth = -this.optsWidthLeftSide;\n      openAmount = optsWidth + (openAmount - optsWidth) * ELASTIC_FACTOR;\n    }\n    this.setOpenAmount(openAmount, false);\n  }\n  onEnd(gesture) {\n    const {\n      contentEl,\n      initialContentScrollY\n    } = this;\n    if (contentEl) {\n      resetContentScrollY(contentEl, initialContentScrollY);\n    }\n    const velocity = gesture.velocityX;\n    let restingPoint = this.openAmount > 0 ? this.optsWidthRightSide : -this.optsWidthLeftSide;\n    // Check if the drag didn't clear the buttons mid-point\n    // and we aren't moving fast enough to swipe open\n    const isResetDirection = this.openAmount > 0 === !(velocity < 0);\n    const isMovingFast = Math.abs(velocity) > 0.3;\n    const isOnCloseZone = Math.abs(this.openAmount) < Math.abs(restingPoint / 2);\n    if (swipeShouldReset(isResetDirection, isMovingFast, isOnCloseZone)) {\n      restingPoint = 0;\n    }\n    const state = this.state;\n    this.setOpenAmount(restingPoint, true);\n    if ((state & 32 /* SlidingState.SwipeEnd */) !== 0 && this.rightOptions) {\n      this.rightOptions.fireSwipeEvent();\n    } else if ((state & 64 /* SlidingState.SwipeStart */) !== 0 && this.leftOptions) {\n      this.leftOptions.fireSwipeEvent();\n    }\n  }\n  calculateOptsWidth() {\n    this.optsWidthRightSide = 0;\n    if (this.rightOptions) {\n      this.rightOptions.style.display = 'flex';\n      this.optsWidthRightSide = this.rightOptions.offsetWidth;\n      this.rightOptions.style.display = '';\n    }\n    this.optsWidthLeftSide = 0;\n    if (this.leftOptions) {\n      this.leftOptions.style.display = 'flex';\n      this.optsWidthLeftSide = this.leftOptions.offsetWidth;\n      this.leftOptions.style.display = '';\n    }\n    this.optsDirty = false;\n  }\n  setOpenAmount(openAmount, isFinal) {\n    if (this.tmr !== undefined) {\n      clearTimeout(this.tmr);\n      this.tmr = undefined;\n    }\n    if (!this.item) {\n      return;\n    }\n    const {\n      el\n    } = this;\n    const style = this.item.style;\n    this.openAmount = openAmount;\n    if (isFinal) {\n      style.transition = '';\n    }\n    if (openAmount > 0) {\n      this.state = openAmount >= this.optsWidthRightSide + SWIPE_MARGIN ? 8 /* SlidingState.End */ | 32 /* SlidingState.SwipeEnd */ : 8 /* SlidingState.End */;\n    } else if (openAmount < 0) {\n      this.state = openAmount <= -this.optsWidthLeftSide - SWIPE_MARGIN ? 16 /* SlidingState.Start */ | 64 /* SlidingState.SwipeStart */ : 16 /* SlidingState.Start */;\n    } else {\n      /**\n       * The sliding options should not be\n       * clickable while the item is closing.\n       */\n      el.classList.add('item-sliding-closing');\n      /**\n       * Item sliding cannot be interrupted\n       * while closing the item. If it did,\n       * it would allow the item to get into an\n       * inconsistent state where multiple\n       * items are then open at the same time.\n       */\n      if (this.gesture) {\n        this.gesture.enable(false);\n      }\n      this.tmr = setTimeout(() => {\n        this.state = 2 /* SlidingState.Disabled */;\n        this.tmr = undefined;\n        if (this.gesture) {\n          this.gesture.enable(!this.disabled);\n        }\n        el.classList.remove('item-sliding-closing');\n      }, 600);\n      openSlidingItem = undefined;\n      style.transform = '';\n      return;\n    }\n    style.transform = `translate3d(${-openAmount}px,0,0)`;\n    this.ionDrag.emit({\n      amount: openAmount,\n      ratio: this.getSlidingRatioSync()\n    });\n  }\n  getSlidingRatioSync() {\n    if (this.openAmount > 0) {\n      return this.openAmount / this.optsWidthRightSide;\n    } else if (this.openAmount < 0) {\n      return this.openAmount / this.optsWidthLeftSide;\n    } else {\n      return 0;\n    }\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '7f191e38bf717e6ccb246aa7b9fbd29d01e64677',\n      class: {\n        [mode]: true,\n        'item-sliding-active-slide': this.state !== 2 /* SlidingState.Disabled */,\n        'item-sliding-active-options-end': (this.state & 8 /* SlidingState.End */) !== 0,\n        'item-sliding-active-options-start': (this.state & 16 /* SlidingState.Start */) !== 0,\n        'item-sliding-active-swipe-end': (this.state & 32 /* SlidingState.SwipeEnd */) !== 0,\n        'item-sliding-active-swipe-start': (this.state & 64 /* SlidingState.SwipeStart */) !== 0\n      }\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"disabled\": [\"disabledChanged\"]\n    };\n  }\n};\nconst swipeShouldReset = (isResetDirection, isMovingFast, isOnResetZone) => {\n  // The logic required to know when the sliding item should close (openAmount=0)\n  // depends on three booleans (isResetDirection, isMovingFast, isOnResetZone)\n  // and it ended up being too complicated to be written manually without errors\n  // so the truth table is attached below: (0=false, 1=true)\n  // isResetDirection | isMovingFast | isOnResetZone || shouldClose\n  //         0        |       0      |       0       ||    0\n  //         0        |       0      |       1       ||    1\n  //         0        |       1      |       0       ||    0\n  //         0        |       1      |       1       ||    0\n  //         1        |       0      |       0       ||    0\n  //         1        |       0      |       1       ||    1\n  //         1        |       1      |       0       ||    1\n  //         1        |       1      |       1       ||    1\n  // The resulting expression was generated by resolving the K-map (Karnaugh map):\n  return !isMovingFast && isOnResetZone || isResetDirection && isMovingFast;\n};\nItemSliding.style = IonItemSlidingStyle0;\nexport { ItemOption as ion_item_option, ItemOptions as ion_item_options, ItemSliding as ion_item_sliding };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}