.swiper.swiper-cube {
  overflow: visible;
}
.swiper-cube ::slotted(swiper-slide) {
  pointer-events: none;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  z-index: 1;
  visibility: hidden;
  transform-origin: 0 0;
  width: 100%;
  height: 100%;
}
.swiper-cube ::slotted(swiper-slide) ::slotted(swiper-slide) {
  pointer-events: none;
}
.swiper-cube.swiper-rtl ::slotted(swiper-slide) {
  transform-origin: 100% 0;
}
.swiper-cube ::slotted(.swiper-slide-active),
.swiper-cube ::slotted(.swiper-slide-active) ::slotted(.swiper-slide-active) {
  pointer-events: auto;
}
.swiper-cube ::slotted(.swiper-slide-active),
.swiper-cube ::slotted(.swiper-slide-next),
.swiper-cube ::slotted(.swiper-slide-prev) {
  pointer-events: auto;
  visibility: visible;
}
.swiper-cube .swiper-cube-shadow {
  position: absolute;
  left: 0;
  bottom: 0px;
  width: 100%;
  height: 100%;
  opacity: 0.6;
  z-index: 0;
}
.swiper-cube .swiper-cube-shadow:before {
  content: '';
  background: #000;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  filter: blur(50px);
}
.swiper-cube ::slotted(.swiper-slide-next) + ::slotted(swiper-slide) {
  pointer-events: auto;
  visibility: visible;
}
/* Cube slide shadows start *//* Cube slide shadows end */
