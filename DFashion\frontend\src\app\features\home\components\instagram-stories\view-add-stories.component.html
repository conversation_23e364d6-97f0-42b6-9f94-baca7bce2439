<div class="stories-container" #storiesContainer>
  <!-- Stories Header -->
  <div class="stories-header">
    <h3 class="stories-title">Stories</h3>
    <button class="create-story-btn"
            (click)="onAdd()"
            type="button"
            aria-label="Create new story"
            title="Create a new story">
      <i class="fas fa-plus" aria-hidden="true"></i>
      <span>Create</span>
    </button>
  </div>

  <!-- Stories Slider with Navigation -->
  <div class="stories-slider-wrapper">
    <!-- Navigation Arrow Left -->
    <button class="nav-arrow nav-arrow-left"
            (click)="scrollLeft()"
            [disabled]="!canScrollLeft"
            [class.hidden]="!showNavArrows"
            type="button"
            aria-label="Scroll stories left"
            title="Previous stories">
      <i class="fas fa-chevron-left" aria-hidden="true"></i>
    </button>

    <!-- Custom Stories Slider -->
    <div class="stories-slider" #storiesSlider>
      <div class="stories-track" #storiesTrack [style.transform]="'translateX(' + translateX + 'px)'">
        <!-- Add Story Button -->
        <div class="story-item add-story-item" (click)="onAdd()">
          <div class="story-avatar-container">
            <div class="story-avatar add-avatar">
              <div class="story-avatar-inner">
                <img
                  class="story-avatar-img"
                  [src]="currentUser?.avatar || 'assets/default-avatar.png'"
                  alt="Your Story"
                />
              </div>
              <!-- Plus button positioned outside the circle like Instagram -->
              <div class="add-story-plus-btn">
                <i class="fas fa-plus"></i>
              </div>
            </div>
          </div>
          <div class="story-username">Your Story</div>
        </div>

        <!-- Existing Stories -->
        <div class="story-item"
             *ngFor="let story of stories; let i = index"
             (click)="openStory(story, i)">
          <div class="story-avatar-container">
            <div class="story-avatar" [class.has-products]="story.products && story.products.length > 0">
              <div class="story-avatar-inner">
                <img
                  class="story-avatar-img"
                  [src]="story.user.avatar"
                  [alt]="story.user.username"
                />
              </div>
              <!-- Shopping bag indicator positioned outside the circle like Instagram -->
              <div class="shopping-bag-indicator"
                   *ngIf="story.products && story.products.length > 0"
                   title="Shoppable content">
                <i class="fas fa-shopping-bag"></i>
              </div>
            </div>
          </div>
          <div class="story-username">{{ story.user.username }}</div>
          <!-- Product count badge -->
          <div class="product-count-badge"
               *ngIf="story.products && story.products.length > 0">
            {{ story.products.length }} item{{ story.products.length > 1 ? 's' : '' }}
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Arrow Right -->
    <button class="nav-arrow nav-arrow-right"
            (click)="scrollRight()"
            [disabled]="!canScrollRight"
            [class.hidden]="!showNavArrows"
            type="button"
            aria-label="Scroll stories right"
            title="Next stories">
      <i class="fas fa-chevron-right" aria-hidden="true"></i>
    </button>
  </div>
</div>

<!-- Fullscreen Stories Viewer (shown when isOpen is true) -->
<div class="stories-overlay" *ngIf="isOpen">
  <div class="stories-content" #feedCover>
    <!-- Progress Bars -->
    <div class="progress-container">
      <div class="progress-bar"
           *ngFor="let story of stories; let i = index"
           [class.active]="i === currentStoryIndex"
           [class.completed]="i < currentStoryIndex">
        <div class="progress-fill"
             [style.width.%]="getProgressWidth(i)"></div>
      </div>
    </div>

    <!-- Story Header -->
    <div class="story-header">
      <img class="story-header-avatar" [src]="getCurrentStory().user.avatar" alt="Avatar"/>
      <div class="story-header-info">
        <span class="username">{{ getCurrentStory().user.username }}</span>
        <span class="time">{{ getTimeAgo(getCurrentStory().createdAt) }}</span>
      </div>
      <button (click)="closeStories()" class="close-btn">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Story Media -->
    <div class="story-media"
         (click)="onStoryClick($event)"
         (touchstart)="onTouchStart($event)"
         (touchmove)="onTouchMove($event)"
         (touchend)="onTouchEnd($event)">

      <!-- Image Story -->
      <img *ngIf="getCurrentStory().mediaType === 'image'"
           [src]="getCurrentStory().mediaUrl"
           class="story-image"/>

      <!-- Video Story -->
      <video *ngIf="getCurrentStory().mediaType === 'video'"
             class="story-video"
             [src]="getCurrentStory().mediaUrl"
             autoplay muted #storyVideo></video>

      <!-- Middle Click Area for Product/Category Navigation -->
      <div class="story-middle-click-area"
           (click)="handleMiddleAreaClick($event)"
           *ngIf="getCurrentStory()?.linkedContent"
           title="View linked {{ getCurrentStory()?.linkedContent?.type }}">
        <div class="middle-click-indicator">
          <i class="fas fa-external-link-alt"></i>
          <span>{{ getLinkedContentText() }}</span>
        </div>
      </div>

      <!-- Product Tags -->
      <div class="product-tags" *ngIf="showProductTags && getCurrentStory()?.products">
        <div class="product-tag"
             *ngFor="let productTag of getCurrentStory()?.products"
             [style.left.%]="productTag.position?.x || 50"
             [style.top.%]="productTag.position?.y || 50"
             (click)="productTag.product && openProductDetails(productTag.product); $event.stopPropagation()">
          <div class="product-tag-dot"></div>
          <div class="product-tag-info">
            <img [src]="productTag.product?.images?.[0]?.url || '/assets/images/product-placeholder.jpg'"
                 [alt]="productTag.product?.name || 'Product'"
                 class="product-tag-image">
            <div class="product-tag-details">
              <span class="product-tag-name">{{ productTag.product?.name || 'Product' }}</span>
              <span class="product-tag-price">${{ productTag.product?.price || 0 }}</span>
              <div class="product-tag-actions">
                <button class="product-action-btn cart-btn"
                        (click)="productTag.product && addToCart(productTag.product); $event.stopPropagation()"
                        type="button"
                        title="Add to Cart">
                  <i class="fas fa-shopping-cart"></i>
                </button>
                <button class="product-action-btn wishlist-btn"
                        (click)="productTag.product && addToWishlist(productTag.product); $event.stopPropagation()"
                        type="button"
                        title="Add to Wishlist">
                  <i class="fas fa-heart"></i>
                </button>
                <button class="product-action-btn buy-btn"
                        (click)="productTag.product && buyNow(productTag.product); $event.stopPropagation()"
                        type="button"
                        title="Buy Now">
                  <i class="fas fa-bolt"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Shopping Bag Button -->
      <button class="shopping-bag-btn"
              *ngIf="hasProducts()"
              (click)="toggleProductTags(); $event.stopPropagation()"
              [class.active]="showProductTags"
              type="button"
              [attr.aria-label]="'View ' + getProductCount() + ' product' + (getProductCount() > 1 ? 's' : '')"
              [title]="'View ' + getProductCount() + ' product' + (getProductCount() > 1 ? 's' : '')">
        <i class="fas fa-shopping-bag" aria-hidden="true"></i>
        <span class="product-count" aria-hidden="true">{{ getProductCount() }}</span>
      </button>
    </div>

    <!-- Story Actions -->
    <div class="story-actions">
      <button class="action-btn like-btn"
              (click)="toggleLike()"
              [class.liked]="isLiked"
              type="button"
              [attr.aria-label]="isLiked ? 'Unlike story' : 'Like story'"
              [title]="isLiked ? 'Unlike story' : 'Like story'">
        <i class="fas fa-heart" aria-hidden="true"></i>
      </button>
      <button class="action-btn share-btn"
              (click)="shareStory()"
              type="button"
              aria-label="Share story"
              title="Share story">
        <i class="fas fa-share" aria-hidden="true"></i>
      </button>
      <button class="action-btn save-btn"
              (click)="saveStory()"
              type="button"
              aria-label="Save story"
              title="Save story">
        <i class="fas fa-bookmark" aria-hidden="true"></i>
      </button>
    </div>

    <!-- Navigation Arrows -->
    <button class="story-nav-btn story-nav-prev"
            (click)="previousStory()"
            *ngIf="currentStoryIndex > 0">
      <i class="fas fa-chevron-left"></i>
    </button>
    <button class="story-nav-btn story-nav-next"
            (click)="nextStory()"
            *ngIf="currentStoryIndex < stories.length - 1">
      <i class="fas fa-chevron-right"></i>
    </button>
  </div>
</div>
