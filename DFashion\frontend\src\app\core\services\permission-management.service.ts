import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { RoleManagementService, UserRole, Permission } from './role-management.service';

export interface PermissionCheck {
  module: string;
  action: string;
  scope?: 'global' | 'department' | 'team' | 'self';
  resourceId?: string;
}

export interface UIPermission {
  component: string;
  action: string;
  condition?: (user: any) => boolean;
}

export interface FeatureFlag {
  name: string;
  enabled: boolean;
  roles?: UserRole[];
  departments?: string[];
  condition?: (user: any) => boolean;
}

@Injectable({
  providedIn: 'root'
})
export class PermissionManagementService {
  private currentUserSubject = new BehaviorSubject<any>(null);
  private permissionCacheSubject = new BehaviorSubject<Map<string, boolean>>(new Map());
  
  public currentUser$ = this.currentUserSubject.asObservable();
  public permissionCache$ = this.permissionCacheSubject.asObservable();

  // Feature flags for different functionalities
  private featureFlags: FeatureFlag[] = [
    {
      name: 'advanced_analytics',
      enabled: true,
      roles: ['super_admin', 'admin', 'sales_manager', 'marketing_manager']
    },
    {
      name: 'team_management',
      enabled: true,
      condition: (user) => this.roleManagementService.isManager(user.role)
    },
    {
      name: 'financial_reports',
      enabled: true,
      roles: ['super_admin', 'admin', 'account_manager', 'accountant']
    },
    {
      name: 'user_management',
      enabled: true,
      roles: ['super_admin', 'admin']
    },
    {
      name: 'system_settings',
      enabled: true,
      roles: ['super_admin', 'admin']
    },
    {
      name: 'content_moderation',
      enabled: true,
      roles: ['super_admin', 'admin', 'content_manager']
    },
    {
      name: 'vendor_management',
      enabled: true,
      roles: ['super_admin', 'admin', 'vendor_manager']
    },
    {
      name: 'support_escalation',
      enabled: true,
      roles: ['super_admin', 'admin', 'support_manager']
    }
  ];

  // UI component permissions
  private uiPermissions: UIPermission[] = [
    { component: 'admin_panel', action: 'view', condition: (user) => ['super_admin', 'admin'].includes(user.role) },
    { component: 'user_list', action: 'view', condition: (user) => this.hasPermission(user.role, 'users', 'read') },
    { component: 'user_create', action: 'view', condition: (user) => this.hasPermission(user.role, 'users', 'create') },
    { component: 'user_edit', action: 'view', condition: (user) => this.hasPermission(user.role, 'users', 'update') },
    { component: 'user_delete', action: 'view', condition: (user) => this.hasPermission(user.role, 'users', 'delete') },
    { component: 'team_dashboard', action: 'view', condition: (user) => this.roleManagementService.isManager(user.role) },
    { component: 'financial_dashboard', action: 'view', condition: (user) => ['account_manager', 'accountant'].includes(user.role) },
    { component: 'sales_reports', action: 'view', condition: (user) => user.role.includes('sales') },
    { component: 'marketing_campaigns', action: 'view', condition: (user) => user.role.includes('marketing') },
    { component: 'support_tickets', action: 'view', condition: (user) => user.role.includes('support') },
    { component: 'content_editor', action: 'view', condition: (user) => ['content_manager', 'marketing_manager', 'marketing_executive'].includes(user.role) },
    { component: 'vendor_contracts', action: 'view', condition: (user) => user.role === 'vendor_manager' }
  ];

  constructor(private roleManagementService: RoleManagementService) {}

  setCurrentUser(user: any): void {
    this.currentUserSubject.next(user);
    this.clearPermissionCache();
  }

  getCurrentUser(): any {
    return this.currentUserSubject.value;
  }

  /**
   * Check if current user has specific permission
   */
  hasPermission(role: UserRole, module: string, action: string, scope?: string): boolean {
    const cacheKey = `${role}_${module}_${action}_${scope || 'default'}`;
    const cache = this.permissionCacheSubject.value;
    
    if (cache.has(cacheKey)) {
      return cache.get(cacheKey)!;
    }

    const hasAccess = this.roleManagementService.hasPermission(role, module, action);
    
    // Cache the result
    cache.set(cacheKey, hasAccess);
    this.permissionCacheSubject.next(cache);
    
    return hasAccess;
  }

  /**
   * Check multiple permissions at once
   */
  hasAnyPermission(role: UserRole, checks: PermissionCheck[]): boolean {
    return checks.some(check => 
      this.hasPermission(role, check.module, check.action, check.scope)
    );
  }

  /**
   * Check if all permissions are granted
   */
  hasAllPermissions(role: UserRole, checks: PermissionCheck[]): boolean {
    return checks.every(check => 
      this.hasPermission(role, check.module, check.action, check.scope)
    );
  }

  /**
   * Get accessible modules for current user
   */
  getAccessibleModules(role: UserRole): string[] {
    return this.roleManagementService.getAccessibleModules(role);
  }

  /**
   * Check if user can access specific UI component
   */
  canAccessComponent(componentName: string, action: string = 'view'): Observable<boolean> {
    return this.currentUser$.pipe(
      map(user => {
        if (!user) return false;
        
        const permission = this.uiPermissions.find(p => 
          p.component === componentName && p.action === action
        );
        
        if (!permission) return false;
        
        return permission.condition ? permission.condition(user) : true;
      })
    );
  }

  /**
   * Check if feature flag is enabled for current user
   */
  isFeatureEnabled(featureName: string): Observable<boolean> {
    return this.currentUser$.pipe(
      map(user => {
        if (!user) return false;
        
        const feature = this.featureFlags.find(f => f.name === featureName);
        if (!feature || !feature.enabled) return false;
        
        // Check role-based access
        if (feature.roles && !feature.roles.includes(user.role)) {
          return false;
        }
        
        // Check department-based access
        if (feature.departments && !feature.departments.includes(user.department)) {
          return false;
        }
        
        // Check custom condition
        if (feature.condition && !feature.condition(user)) {
          return false;
        }
        
        return true;
      })
    );
  }

  /**
   * Get all enabled features for current user
   */
  getEnabledFeatures(): Observable<string[]> {
    return this.currentUser$.pipe(
      map(user => {
        if (!user) return [];
        
        return this.featureFlags
          .filter(feature => {
            if (!feature.enabled) return false;
            
            if (feature.roles && !feature.roles.includes(user.role)) {
              return false;
            }
            
            if (feature.departments && !feature.departments.includes(user.department)) {
              return false;
            }
            
            if (feature.condition && !feature.condition(user)) {
              return false;
            }
            
            return true;
          })
          .map(feature => feature.name);
      })
    );
  }

  /**
   * Check if user can manage other users
   */
  canManageUsers(targetRole?: UserRole): Observable<boolean> {
    return this.currentUser$.pipe(
      map(user => {
        if (!user) return false;
        
        // Super admin can manage everyone
        if (user.role === 'super_admin') return true;
        
        // Admin can manage non-admin users
        if (user.role === 'admin' && targetRole !== 'super_admin') return true;
        
        // Managers can manage their subordinates
        if (this.roleManagementService.isManager(user.role) && targetRole) {
          const subordinates = this.roleManagementService.getSubordinateRoles(user.role);
          return subordinates.includes(targetRole);
        }
        
        return false;
      })
    );
  }

  /**
   * Get permission scope for user
   */
  getPermissionScope(role: UserRole, module: string): string {
    const config = this.roleManagementService.getRoleConfig(role);
    const permission = config.permissions.find(p => p.module === module || p.module === '*');
    
    return permission?.scope || 'self';
  }

  /**
   * Check if user can access resource based on scope
   */
  canAccessResource(resourceOwnerId: string, resourceDepartment?: string): Observable<boolean> {
    return this.currentUser$.pipe(
      map(user => {
        if (!user) return false;
        
        // Super admin has global access
        if (user.role === 'super_admin') return true;
        
        // Check if user owns the resource
        if (user.id === resourceOwnerId) return true;
        
        // Check department-level access
        if (resourceDepartment && user.department === resourceDepartment) {
          const scope = this.getPermissionScope(user.role, 'resources');
          return ['global', 'department'].includes(scope);
        }
        
        // Check team-level access for managers
        if (this.roleManagementService.isManager(user.role)) {
          const scope = this.getPermissionScope(user.role, 'resources');
          return ['global', 'department', 'team'].includes(scope);
        }
        
        return false;
      })
    );
  }

  /**
   * Get filtered navigation items based on permissions
   */
  getFilteredNavigation(navigationItems: any[]): Observable<any[]> {
    return this.currentUser$.pipe(
      map(user => {
        if (!user) return [];
        
        return navigationItems.filter(item => {
          if (!item.permission) return true;
          
          const [module, action] = item.permission.split('.');
          return this.hasPermission(user.role, module, action);
        });
      })
    );
  }

  /**
   * Clear permission cache
   */
  clearPermissionCache(): void {
    this.permissionCacheSubject.next(new Map());
  }

  /**
   * Enable/disable feature flag
   */
  setFeatureFlag(featureName: string, enabled: boolean): void {
    const feature = this.featureFlags.find(f => f.name === featureName);
    if (feature) {
      feature.enabled = enabled;
    }
  }

  /**
   * Add new UI permission
   */
  addUIPermission(permission: UIPermission): void {
    this.uiPermissions.push(permission);
  }

  /**
   * Remove UI permission
   */
  removeUIPermission(componentName: string, action: string): void {
    const index = this.uiPermissions.findIndex(p => 
      p.component === componentName && p.action === action
    );
    if (index > -1) {
      this.uiPermissions.splice(index, 1);
    }
  }

  /**
   * Get all permissions for debugging
   */
  getAllPermissions(): any {
    return {
      featureFlags: this.featureFlags,
      uiPermissions: this.uiPermissions,
      currentUser: this.getCurrentUser(),
      permissionCache: Array.from(this.permissionCacheSubject.value.entries())
    };
  }
}
