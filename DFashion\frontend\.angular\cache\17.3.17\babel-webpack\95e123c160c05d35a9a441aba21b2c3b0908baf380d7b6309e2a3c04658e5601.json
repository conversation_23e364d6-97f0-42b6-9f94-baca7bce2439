{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let EmailService = /*#__PURE__*/(() => {\n  class EmailService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = environment.apiUrl;\n    }\n    sendBillEmail(formData) {\n      return this.http.post(`${this.apiUrl}/email/send-bill`, formData);\n    }\n    sendPasswordResetEmail(email) {\n      return this.http.post(`${this.apiUrl}/email/forgot-password`, {\n        email\n      });\n    }\n    sendOrderConfirmationEmail(orderData) {\n      const emailData = {\n        type: 'order_confirmation',\n        to: orderData.customerEmail,\n        subject: 'Order Confirmation - DFashion',\n        data: {\n          customerName: orderData.customerName,\n          orderId: orderData.orderId,\n          items: orderData.items,\n          total: orderData.total,\n          orderDate: orderData.orderDate\n        }\n      };\n      return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);\n    }\n    sendPaymentSuccessEmail(paymentData) {\n      const emailData = {\n        type: 'payment_success',\n        to: paymentData.customerEmail,\n        subject: 'Payment Successful - DFashion',\n        data: {\n          customerName: paymentData.customerName,\n          orderId: paymentData.orderId,\n          paymentId: paymentData.paymentId,\n          amount: paymentData.amount,\n          paymentMethod: paymentData.paymentMethod,\n          paymentDate: new Date()\n        }\n      };\n      return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);\n    }\n    sendPaymentFailedEmail(paymentData) {\n      const emailData = {\n        type: 'payment_failed',\n        to: paymentData.customerEmail,\n        subject: 'Payment Failed - DFashion',\n        data: {\n          customerName: paymentData.customerName,\n          orderId: paymentData.orderId,\n          amount: paymentData.amount,\n          failureReason: paymentData.failureReason || 'Payment processing failed',\n          retryLink: `${environment.frontendUrl}/checkout/${paymentData.orderId}`\n        }\n      };\n      return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);\n    }\n    sendOrderShippedEmail(orderData) {\n      const emailData = {\n        type: 'order_shipped',\n        to: orderData.customerEmail,\n        subject: 'Your Order has been Shipped - DFashion',\n        data: {\n          customerName: orderData.customerName,\n          orderId: orderData.orderId,\n          trackingNumber: orderData.trackingNumber,\n          estimatedDelivery: orderData.estimatedDelivery,\n          shippingAddress: orderData.shippingAddress\n        }\n      };\n      return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);\n    }\n    sendOrderDeliveredEmail(orderData) {\n      const emailData = {\n        type: 'order_delivered',\n        to: orderData.customerEmail,\n        subject: 'Order Delivered - DFashion',\n        data: {\n          customerName: orderData.customerName,\n          orderId: orderData.orderId,\n          deliveryDate: orderData.deliveryDate,\n          feedbackLink: `${environment.frontendUrl}/feedback/${orderData.orderId}`\n        }\n      };\n      return this.http.post(`${this.apiUrl}/email/send-notification`, emailData);\n    }\n    // Email templates for different notification types\n    getEmailTemplate(type) {\n      const templates = {\n        order_confirmation: {\n          subject: 'Order Confirmation - DFashion',\n          template: `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;\">\n              <h1 style=\"color: white; margin: 0;\">DFashion</h1>\n              <p style=\"color: white; margin: 5px 0;\">Order Confirmation</p>\n            </div>\n            <div style=\"padding: 20px; background: #f8f9fa;\">\n              <h2>Hi {{customerName}},</h2>\n              <p>Thank you for your order! We're excited to confirm that we've received your order.</p>\n              <div style=\"background: white; padding: 15px; border-radius: 8px; margin: 20px 0;\">\n                <h3>Order Details:</h3>\n                <p><strong>Order ID:</strong> {{orderId}}</p>\n                <p><strong>Order Date:</strong> {{orderDate}}</p>\n                <p><strong>Total Amount:</strong> ₹{{total}}</p>\n              </div>\n              <p>We'll send you another email when your order ships.</p>\n              <p>Best regards,<br>DFashion Team</p>\n            </div>\n          </div>\n        `\n        },\n        payment_success: {\n          subject: 'Payment Successful - DFashion',\n          template: `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <div style=\"background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); padding: 20px; text-align: center;\">\n              <h1 style=\"color: white; margin: 0;\">Payment Successful!</h1>\n            </div>\n            <div style=\"padding: 20px; background: #f8f9fa;\">\n              <h2>Hi {{customerName}},</h2>\n              <p>Your payment has been processed successfully!</p>\n              <div style=\"background: white; padding: 15px; border-radius: 8px; margin: 20px 0;\">\n                <h3>Payment Details:</h3>\n                <p><strong>Payment ID:</strong> {{paymentId}}</p>\n                <p><strong>Order ID:</strong> {{orderId}}</p>\n                <p><strong>Amount:</strong> ₹{{amount}}</p>\n                <p><strong>Payment Method:</strong> {{paymentMethod}}</p>\n              </div>\n              <p>Your order is now being processed and will be shipped soon.</p>\n              <p>Best regards,<br>DFashion Team</p>\n            </div>\n          </div>\n        `\n        },\n        payment_failed: {\n          subject: 'Payment Failed - DFashion',\n          template: `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <div style=\"background: linear-gradient(135deg, #f5576c 0%, #f093fb 100%); padding: 20px; text-align: center;\">\n              <h1 style=\"color: white; margin: 0;\">Payment Failed</h1>\n            </div>\n            <div style=\"padding: 20px; background: #f8f9fa;\">\n              <h2>Hi {{customerName}},</h2>\n              <p>We're sorry, but your payment could not be processed.</p>\n              <div style=\"background: white; padding: 15px; border-radius: 8px; margin: 20px 0;\">\n                <h3>Order Details:</h3>\n                <p><strong>Order ID:</strong> {{orderId}}</p>\n                <p><strong>Amount:</strong> ₹{{amount}}</p>\n                <p><strong>Reason:</strong> {{failureReason}}</p>\n              </div>\n              <p>Please try again or contact our support team for assistance.</p>\n              <a href=\"{{retryLink}}\" style=\"background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0;\">Retry Payment</a>\n              <p>Best regards,<br>DFashion Team</p>\n            </div>\n          </div>\n        `\n        }\n      };\n      return templates[type] || null;\n    }\n    // Utility method to replace template variables\n    processTemplate(template, data) {\n      let processedTemplate = template;\n      Object.keys(data).forEach(key => {\n        const regex = new RegExp(`{{${key}}}`, 'g');\n        processedTemplate = processedTemplate.replace(regex, data[key]);\n      });\n      return processedTemplate;\n    }\n    static {\n      this.ɵfac = function EmailService_Factory(t) {\n        return new (t || EmailService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: EmailService,\n        factory: EmailService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return EmailService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}