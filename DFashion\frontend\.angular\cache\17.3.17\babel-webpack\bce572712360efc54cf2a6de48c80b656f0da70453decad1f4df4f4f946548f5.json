{"ast": null, "code": "export const authRoutes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  loadComponent: () => import('./pages/login/login.component').then(m => m.LoginComponent)\n}, {\n  path: 'register',\n  loadComponent: () => import('./pages/register/register.component').then(m => m.RegisterComponent)\n}];", "map": {"version": 3, "names": ["authRoutes", "path", "redirectTo", "pathMatch", "loadComponent", "then", "m", "LoginComponent", "RegisterComponent"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\auth\\auth.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const authRoutes: Routes = [\n  {\n    path: '',\n    redirectTo: 'login',\n    pathMatch: 'full'\n  },\n  {\n    path: 'login',\n    loadComponent: () => import('./pages/login/login.component').then(m => m.LoginComponent)\n  },\n  {\n    path: 'register',\n    loadComponent: () => import('./pages/register/register.component').then(m => m.RegisterComponent)\n  }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,UAAU,GAAW,CAChC;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,OAAO;EACbG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc;CACxF,EACD;EACEN,IAAI,EAAE,UAAU;EAChBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,iBAAiB;CACjG,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}