{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport { SlickCarouselModule } from 'ngx-slick-carousel'; // ✅ Add this\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"ngx-slick-carousel\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nconst _c2 = [\"slickModal\"];\nconst _c3 = [\"storiesSlider\"];\nconst _c4 = () => [1, 2, 3, 4, 5];\nfunction ViewAddStoriesComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"div\", 7)(2, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_div_1_Template, 3, 0, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c4));\n  }\n}\nfunction ViewAddStoriesComponent_div_2_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_div_12_Template_div_click_0_listener() {\n      const i_r4 = i0.ɵɵrestoreView(_r3).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openStories(i_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 12)(2, \"div\", 22)(3, \"div\", 14);\n    i0.ɵɵelement(4, \"img\", 23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 17);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r5 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", story_r5.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", story_r5.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r5.user.username);\n  }\n}\nfunction ViewAddStoriesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"ngx-slick-carousel\", 10, 0)(3, \"div\", 11);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_Template_div_click_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAdd());\n    });\n    i0.ɵɵelementStart(4, \"div\", 12)(5, \"div\", 13)(6, \"div\", 14);\n    i0.ɵɵelement(7, \"img\", 15);\n    i0.ɵɵelementStart(8, \"span\", 16);\n    i0.ɵɵtext(9, \"+\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"div\", 17);\n    i0.ɵɵtext(11, \"Your Story\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, ViewAddStoriesComponent_div_2_div_12_Template, 7, 3, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const slickModal_r6 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(slickModal_r6.slickPrev());\n    });\n    i0.ɵɵtext(14, \"\\u2190\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const slickModal_r6 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(slickModal_r6.slickNext());\n    });\n    i0.ɵɵtext(16, \"\\u2192\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"config\", ctx_r1.slickConfig);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.avatar) || \"assets/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.visibleStories);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canScrollStoriesLeft);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canScrollStoriesRight);\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, authService) {\n    this.router = router;\n    this.http = http;\n    this.authService = authService;\n    this.currentUser = null;\n    this.slideConfig = {\n      slidesToShow: 5,\n      slidesToScroll: 2,\n      infinite: false,\n      arrows: true,\n      nextArrow: '<button class=\"slick-arrow right\">&#8594;</button>',\n      prevArrow: '<button class=\"slick-arrow left\">&#8592;</button>',\n      responsive: [{\n        breakpoint: 768,\n        settings: {\n          slidesToShow: 4\n        }\n      }, {\n        breakpoint: 480,\n        settings: {\n          slidesToShow: 3\n        }\n      }]\n    };\n    this.stories = [];\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    this.subscriptions = [];\n    this.canScrollStoriesLeft = false;\n    this.canScrollStoriesRight = false;\n    // Handler for Add Story button\n    // Modal state\n    this.showAddModal = false;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    // Reel recording state\n    this.isRecording = false;\n    this.recordedChunks = [];\n    this.mediaRecorder = null;\n    this.videoStream = null;\n    this.reelPreviewUrl = null;\n    this.isUploadingReel = false;\n    this.newReelCaption = '';\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.isUploadingStory = false;\n  }\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  // --- Slider Arrow Logic ---\n  scrollStoriesLeft() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({\n        left: -200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  scrollStoriesRight() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({\n        left: 200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  updateStoriesArrows() {\n    if (this.storiesSlider) {\n      const el = this.storiesSlider.nativeElement;\n      this.canScrollStoriesLeft = el.scrollLeft > 0;\n      this.canScrollStoriesRight = el.scrollLeft < el.scrollWidth - el.clientWidth - 1;\n    }\n  }\n  ngAfterViewInit() {\n    setTimeout(() => this.updateStoriesArrows(), 500);\n  }\n  // --- Story Logic (unchanged) ---\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(this.http.get(`${environment.apiUrl}/stories`).subscribe({\n      next: response => {\n        if (response.success && response.storyGroups) {\n          this.stories = response.storyGroups;\n          console.log('stories', this.stories);\n        } else {\n          this.loadFallbackStories();\n        }\n        this.isLoadingStories = false;\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.loadFallbackStories();\n        this.isLoadingStories = false;\n      }\n    }));\n  }\n  loadFallbackStories() {\n    this.stories = [\n      // ... (same as before, omitted for brevity)\n    ];\n  }\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n  onTouchEnd(event) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n  updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = dragDelta / window.innerWidth * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  update() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' ? this.currentIndex + 1 : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.update());\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  setupEventListeners() {}\n  removeEventListeners() {}\n  getCurrentStory() {\n    return this.stories[this.currentIndex];\n  }\n  getStoryProgress() {\n    return (this.currentIndex + 1) / this.stories.length * 100;\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  startCameraForReel() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.videoStream = yield navigator.mediaDevices.getUserMedia({\n          video: true,\n          audio: true\n        });\n        const video = document.getElementById('reel-video');\n        if (video) {\n          video.srcObject = _this.videoStream;\n          video.play();\n        }\n      } catch (err) {\n        alert('Could not access camera.');\n        _this.showAddReelModal = false;\n      }\n    })();\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new window.MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = e => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, {\n        type: 'video/webm'\n      });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  submitNewReel() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.reelPreviewUrl) return;\n      _this2.isUploadingReel = true;\n      try {\n        const blob = new Blob(_this2.recordedChunks, {\n          type: 'video/webm'\n        });\n        const formData = new FormData();\n        formData.append('media', blob, 'reel.webm');\n        const uploadRes = yield _this2.http.post('/api/stories/upload', formData).toPromise();\n        const reelPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this2.newReelCaption,\n          isReel: true\n        };\n        yield _this2.http.post('/api/stories', reelPayload).toPromise();\n        _this2.showAddReelModal = false;\n        _this2.loadStories();\n      } catch (err) {\n        alert('Failed to upload reel.');\n      } finally {\n        _this2.isUploadingReel = false;\n        _this2.cleanupReelStream();\n      }\n    })();\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  submitNewStory() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.newStoryFile) return;\n      _this3.isUploadingStory = true;\n      try {\n        const uploadForm = new FormData();\n        uploadForm.append('media', _this3.newStoryFile);\n        const uploadRes = yield _this3.http.post('/api/stories/upload', uploadForm).toPromise();\n        const storyPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this3.newStoryCaption\n        };\n        yield _this3.http.post('/api/stories', storyPayload).toPromise();\n        _this3.showAddStoryModal = false;\n        _this3.loadStories();\n      } catch (err) {\n        alert('Failed to upload story.');\n      } finally {\n        _this3.isUploadingStory = false;\n      }\n    })();\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n  get visibleStories() {\n    if (Array.isArray(this.stories) && this.stories.length > 0 && typeof this.stories[0].stories !== 'undefined') {\n      // Grouped structure: { user, stories: Story[] }[]\n      return this.stories.flatMap(group => Array.isArray(group.stories) ? group.stories : []).filter(s => s.isActive);\n    } else if (Array.isArray(this.stories)) {\n      // Flat array: Story[]\n      return this.stories.filter(s => s.isActive);\n    }\n    return [];\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.slickModal = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[\"slickModal\", \"slick-carousel\"], [1, \"stories-container\"], [\"class\", \"stories-loading\", 4, \"ngIf\"], [\"class\", \"stories-slider-wrapper\", 4, \"ngIf\"], [1, \"stories-loading\"], [\"class\", \"story-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-skeleton\"], [1, \"skeleton-avatar\"], [1, \"skeleton-name\"], [1, \"stories-slider-wrapper\"], [1, \"stories-slider\", 3, \"config\"], [1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"story-avatar\", \"add-story-avatar\"], [1, \"story-avatar-inner\"], [\"alt\", \"Your Story\", 1, \"story-avatar-img\", 3, \"src\"], [1, \"add-story-plus\"], [1, \"story-username\"], [\"class\", \"story-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"arrow\", \"left\", 3, \"click\", \"disabled\"], [1, \"arrow\", \"right\", 3, \"click\", \"disabled\"], [1, \"story-item\", 3, \"click\"], [1, \"story-avatar\"], [1, \"story-avatar-img\", 3, \"src\", \"alt\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_Template, 2, 2, \"div\", 2)(2, ViewAddStoriesComponent_div_2_Template, 17, 5, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingStories);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, FormsModule, SlickCarouselModule, i5.SlickCarouselComponent],\n      styles: [\".stories-slider-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 24px;\\n  position: relative;\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  overflow-x: auto;\\n  scroll-behavior: smooth;\\n  gap: 15px;\\n  padding: 12px 0;\\n  width: 100%;\\n}\\n\\n.story-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  flex: 0 0 auto;\\n  width: 78px;\\n  cursor: pointer;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  padding: 2px;\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.add-story-avatar[_ngcontent-%COMP%] {\\n  background: #dbdbdb !important;\\n  position: relative;\\n}\\n\\n.story-avatar-inner[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  border: 2px solid white;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.story-avatar-img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  display: block;\\n}\\n\\n.add-story-plus[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  right: 0;\\n  background: #0095f6;\\n  color: #fff;\\n  border-radius: 50%;\\n  width: 22px;\\n  height: 22px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 1.2rem;\\n  border: 2px solid #fff;\\n  font-weight: bold;\\n}\\n\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin-top: 5px;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  max-width: 100%;\\n  text-align: center;\\n}\\n\\n.arrow[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 2rem;\\n  cursor: pointer;\\n  padding: 0 8px;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.arrow[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n\\n@media (max-width: 614px) {\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    margin-bottom: 8px;\\n  }\\n  .stories-slider[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    padding: 8px 0;\\n  }\\n  .story-item[_ngcontent-%COMP%] {\\n    width: 60px;\\n  }\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .story-avatar-inner[_ngcontent-%COMP%] {\\n    border-width: 1.5px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "environment", "SlickCarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "ViewAddStoriesComponent_div_1_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c4", "ɵɵlistener", "ViewAddStoriesComponent_div_2_div_12_Template_div_click_0_listener", "i_r4", "ɵɵrestoreView", "_r3", "index", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "openStories", "ɵɵtext", "story_r5", "user", "avatar", "ɵɵsanitizeUrl", "username", "ɵɵtextInterpolate", "ViewAddStoriesComponent_div_2_Template_div_click_3_listener", "_r1", "onAdd", "ViewAddStoriesComponent_div_2_div_12_Template", "ViewAddStoriesComponent_div_2_Template_button_click_13_listener", "slickModal_r6", "ɵɵreference", "slick<PERSON>rev", "ViewAddStoriesComponent_div_2_Template_button_click_15_listener", "slickNext", "slickConfig", "currentUser", "visibleStories", "canScrollStoriesLeft", "canScrollStoriesRight", "ViewAddStoriesComponent", "constructor", "router", "http", "authService", "slideConfig", "slidesToShow", "slidesToScroll", "infinite", "arrows", "nextArrow", "prevArrow", "responsive", "breakpoint", "settings", "stories", "isLoadingStories", "currentIndex", "isOpen", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "subscriptions", "showAddModal", "showAddStoryModal", "showAddReelModal", "showPermissionModal", "showCameraOrGallery", "permissionDenied", "isRecording", "recordedChunks", "mediaRecorder", "videoStream", "reelPreviewUrl", "isUploadingReel", "newReelCaption", "newStoryFile", "newStoryCaption", "isUploadingStory", "ngOnInit", "loadStories", "setupEventListeners", "currentUser$", "subscribe", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "scrollStoriesLeft", "storiesSlider", "nativeElement", "scrollBy", "left", "behavior", "setTimeout", "updateStoriesArrows", "scrollStoriesRight", "el", "scrollLeft", "scrollWidth", "clientWidth", "ngAfterViewInit", "push", "get", "apiUrl", "next", "response", "success", "storyGroups", "console", "log", "loadFallbackStories", "error", "showStory", "document", "body", "style", "overflow", "closeStories", "pauseAllVideos", "storiesContainer", "classList", "add", "remove", "transform", "nextStory", "length", "update", "previousStory", "handleKeydown", "event", "key", "onStoryClick", "clickX", "clientX", "windowWidth", "window", "innerWidth", "onTouchStart", "touches", "onTouchMove", "updateDragPosition", "onTouchEnd", "<PERSON><PERSON><PERSON><PERSON>", "threshold", "Math", "abs", "newIndex", "requestAnimationFrame", "videos", "querySelectorAll", "video", "pause", "getCurrentStory", "getStoryProgress", "getTimeAgo", "dateString", "now", "Date", "date", "diffInMinutes", "floor", "getTime", "diffInHours", "diffInDays", "formatNumber", "num", "toFixed", "toString", "formatPrice", "price", "Intl", "NumberFormat", "currency", "minimumFractionDigits", "format", "viewProduct", "product", "navigate", "_id", "hasProducts", "story", "products", "getStoryProducts", "onAddStory", "onAddReel", "startCameraForReel", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "audio", "getElementById", "srcObject", "play", "err", "alert", "startRecording", "MediaRecorder", "ondataavailable", "e", "data", "size", "onstop", "blob", "Blob", "type", "URL", "createObjectURL", "start", "stopRecording", "stop", "submitNewReel", "_this2", "formData", "FormData", "append", "uploadRes", "post", "to<PERSON>romise", "reelPayload", "media", "url", "caption", "isReel", "cleanupReelStream", "getTracks", "track", "closeAddReelModal", "handlePermissionResponse", "allow", "openCamera", "input", "setAttribute", "click", "openGallery", "removeAttribute", "onStoryFileSelected", "file", "target", "files", "submitNewStory", "_this3", "uploadForm", "storyPayload", "closeAddStoryModal", "Array", "isArray", "flatMap", "group", "filter", "s", "isActive", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "AuthService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_keydown_HostBindingHandler", "$event", "ɵɵresolveDocument", "ViewAddStoriesComponent_div_1_Template", "ViewAddStoriesComponent_div_2_Template", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "SlickCarouselComponent", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.html"], "sourcesContent": ["import { Compo<PERSON>, On<PERSON>nit, On<PERSON><PERSON>roy, ElementRef, ViewChild, HostListener } \nfrom '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { AuthService } from 'src/app/core/services/auth.service';\nimport { SlickCarouselModule } from 'ngx-slick-carousel'; // ✅ Add this\nimport { User } from 'src/app/core/models/user.model';\n\n\ninterface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, FormsModule, SlickCarouselModule ],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n  @ViewChild('slickModal', { static: false }) slickModal: any;\n\n\n  currentUser: any = null;\nslideConfig = {\n  slidesToShow: 5,\n  slidesToScroll: 2,\n  infinite: false,\n  arrows: true,\n  nextArrow: '<button class=\"slick-arrow right\">&#8594;</button>',\n  prevArrow: '<button class=\"slick-arrow left\">&#8592;</button>',\n  responsive: [\n    {\n      breakpoint: 768,\n      settings: {\n        slidesToShow: 4\n      }\n    },\n    {\n      breakpoint: 480,\n      settings: {\n        slidesToShow: 3\n      }\n    }\n  ]\n};\n\n  stories: Story[] = [];\n  isLoadingStories = true;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n  private subscriptions: Subscription[] = [];\n\n  // For slider arrows\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef<HTMLDivElement>;\n  canScrollStoriesLeft = false;\n  canScrollStoriesRight = false;\n\n  constructor(\n    private router: Router,\n    private http: HttpClient,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  // --- Slider Arrow Logic ---\n  scrollStoriesLeft() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({ left: -200, behavior: 'smooth' });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  scrollStoriesRight() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({ left: 200, behavior: 'smooth' });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  updateStoriesArrows() {\n    if (this.storiesSlider) {\n      const el = this.storiesSlider.nativeElement;\n      this.canScrollStoriesLeft = el.scrollLeft > 0;\n      this.canScrollStoriesRight = el.scrollLeft < el.scrollWidth - el.clientWidth - 1;\n    }\n  }\n  ngAfterViewInit() {\n    setTimeout(() => this.updateStoriesArrows(), 500);\n  }\n\n  // --- Story Logic (unchanged) ---\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(\n      this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({\n        next: (response) => {\n          if (response.success && response.storyGroups) {\n            this.stories = response.storyGroups;\n            console.log('stories',this.stories)\n          } else {\n            this.loadFallbackStories();\n          }\n          this.isLoadingStories = false;\n        },\n        error: (error) => {\n          console.error('Error loading stories:', error);\n          this.loadFallbackStories();\n          this.isLoadingStories = false;\n        }\n      })\n    );\n  }\n\n  loadFallbackStories() {\n    this.stories = [\n      // ... (same as before, omitted for brevity)\n    ];\n  }\n\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index: number) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n  onTouchEnd(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n  private updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = (dragDelta / window.innerWidth) * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  private update() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' \n          ? this.currentIndex + 1 \n          : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.update());\n  }\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach((video: any) => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  private setupEventListeners() {}\n  private removeEventListeners() {}\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex];\n  }\n  getStoryProgress(): number {\n    return ((this.currentIndex + 1) / this.stories.length) * 100;\n  }\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product: any) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts(): any[] {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  // Handler for Add Story button\n  // Modal state\n  showAddModal = false;\n  showAddStoryModal = false;\n  showAddReelModal = false;\n  showPermissionModal = false;\n  showCameraOrGallery = false;\n  permissionDenied = false;\n  // Reel recording state\n  isRecording = false;\n  recordedChunks: Blob[] = [];\n  mediaRecorder: any = null;\n  videoStream: MediaStream | null = null;\n  reelPreviewUrl: string | null = null;\n  isUploadingReel = false;\n  newReelCaption = '';\n  newStoryFile: File | null = null;\n  newStoryCaption = '';\n  isUploadingStory = false;\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  async startCameraForReel() {\n    try {\n      this.videoStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });\n      const video: any = document.getElementById('reel-video');\n      if (video) {\n        video.srcObject = this.videoStream;\n        video.play();\n      }\n    } catch (err) {\n      alert('Could not access camera.');\n      this.showAddReelModal = false;\n    }\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new (window as any).MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = (e: any) => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  async submitNewReel() {\n    if (!this.reelPreviewUrl) return;\n    this.isUploadingReel = true;\n    try {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      const formData = new FormData();\n      formData.append('media', blob, 'reel.webm');\n      const uploadRes: any = await this.http.post('/api/stories/upload', formData).toPromise();\n      const reelPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newReelCaption,\n        isReel: true\n      };\n      await this.http.post('/api/stories', reelPayload).toPromise();\n      this.showAddReelModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload reel.');\n    } finally {\n      this.isUploadingReel = false;\n      this.cleanupReelStream();\n    }\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow: boolean) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event: any) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  async submitNewStory() {\n    if (!this.newStoryFile) return;\n    this.isUploadingStory = true;\n    try {\n      const uploadForm = new FormData();\n      uploadForm.append('media', this.newStoryFile);\n      const uploadRes: any = await this.http.post('/api/stories/upload', uploadForm).toPromise();\n      const storyPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newStoryCaption\n      };\n      await this.http.post('/api/stories', storyPayload).toPromise();\n      this.showAddStoryModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload story.');\n    } finally {\n      this.isUploadingStory = false;\n    }\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n\n \nget visibleStories(): Story[] {\n  if (\n    Array.isArray(this.stories) &&\n    this.stories.length > 0 &&\n    typeof (this.stories[0] as any).stories !== 'undefined'\n  ) {\n    // Grouped structure: { user, stories: Story[] }[]\n    return (this.stories as any[])\n      .flatMap(group => Array.isArray(group.stories) ? group.stories : [])\n      .filter((s: Story) => s.isActive);\n  } else if (Array.isArray(this.stories)) {\n    // Flat array: Story[]\n    return (this.stories as Story[]).filter(s => s.isActive);\n  }\n  return [];\n}\n\n\n}", "<!-- Instagram-style Stories Bar -->\n<div class=\"stories-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoadingStories\" class=\"stories-loading\">\n    <div *ngFor=\"let item of [1,2,3,4,5]\" class=\"story-skeleton\">\n      <div class=\"skeleton-avatar\"></div>\n      <div class=\"skeleton-name\"></div>\n    </div>\n  </div>\n\n  <!-- Stories Slider with Arrows -->\n  <!-- <div class=\"stories-slider-wrapper\" *ngIf=\"!isLoadingStories\">\n    <button class=\"arrow left\" (click)=\"scrollStoriesLeft()\" [disabled]=\"!canScrollStoriesLeft\">&#8592;</button>\n    <div class=\"stories-slider\" #storiesSlider>\n    \n      <div class=\"story-item add-story-item\" (click)=\"onAdd()\">\n        <div class=\"story-avatar-container\">\n          <div class=\"story-avatar add-story-avatar\">\n            <div class=\"story-avatar-inner\">\n              <img class=\"story-avatar-img\" [src]=\"currentUser?.avatar || 'assets/default-avatar.png'\" alt=\"Your Story\">\n              <span class=\"add-story-plus\">+</span>\n            </div>\n          </div>\n        </div>\n        <div class=\"story-username\">Your Story</div>\n      </div>\n     \n      <div\n        *ngFor=\"let story of visibleStories; let i = index\"\n        class=\"story-item\"\n        (click)=\"openStories(i)\">\n        <div class=\"story-avatar-container\">\n          <div class=\"story-avatar\">\n            <div class=\"story-avatar-inner\">\n              <img class=\"story-avatar-img\" [src]=\"story.user.avatar\" [alt]=\"story.user.username\">\n            </div>\n          </div>\n        </div>\n        <div class=\"story-username\">{{ story.user.username }}</div>\n      </div>\n    </div>\n    <button class=\"arrow right\" (click)=\"scrollStoriesRight()\" [disabled]=\"!canScrollStoriesRight\">&#8594;</button>\n  </div> -->\n<div class=\"stories-slider-wrapper\" *ngIf=\"!isLoadingStories\">\n  <ngx-slick-carousel class=\"stories-slider\"\n                      #slickModal=\"slick-carousel\"\n                      [config]=\"slickConfig\">\n    <!-- Add Story Button -->\n    <div class=\"story-item add-story-item\" (click)=\"onAdd()\">\n      <div class=\"story-avatar-container\">\n        <div class=\"story-avatar add-story-avatar\">\n          <div class=\"story-avatar-inner\">\n            <img class=\"story-avatar-img\" [src]=\"currentUser?.avatar || 'assets/default-avatar.png'\" alt=\"Your Story\">\n            <span class=\"add-story-plus\">+</span>\n          </div>\n        </div>\n      </div>\n      <div class=\"story-username\">Your Story</div>\n    </div>\n    <!-- Other Stories -->\n    <div\n      *ngFor=\"let story of visibleStories; let i = index\"\n      class=\"story-item\"\n      (click)=\"openStories(i)\">\n      <div class=\"story-avatar-container\">\n        <div class=\"story-avatar\">\n          <div class=\"story-avatar-inner\">\n            <img class=\"story-avatar-img\" [src]=\"story.user.avatar\" [alt]=\"story.user.username\">\n          </div>\n        </div>\n      </div>\n      <div class=\"story-username\">{{ story.user.username }}</div>\n    </div>\n  </ngx-slick-carousel>\n  <!-- Optional: Custom arrows (if you want to control them outside the slider) -->\n  <button class=\"arrow left\" (click)=\"slickModal.slickPrev()\" [disabled]=\"!canScrollStoriesLeft\">&#8592;</button>\n  <button class=\"arrow right\" (click)=\"slickModal.slickNext()\" [disabled]=\"!canScrollStoriesRight\">&#8594;</button>\n</div>\n</div>\n"], "mappings": ";AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,WAAW,QAAQ,8BAA8B;AAE1D,SAASC,mBAAmB,QAAQ,oBAAoB,CAAC,CAAC;;;;;;;;;;;;;;ICLtDC,EAAA,CAAAC,cAAA,aAA6D;IAE3DD,EADA,CAAAE,SAAA,aAAmC,aACF;IACnCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAI,UAAA,IAAAC,4CAAA,iBAA6D;IAI/DL,EAAA,CAAAG,YAAA,EAAM;;;IAJkBH,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAc;;;;;;IAwDpCT,EAAA,CAAAC,cAAA,cAG2B;IAAzBD,EAAA,CAAAU,UAAA,mBAAAC,mEAAA;MAAA,MAAAC,IAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,IAAA,CAAc;IAAA,EAAC;IAGpBZ,EAFJ,CAAAC,cAAA,cAAoC,cACR,cACQ;IAC9BD,EAAA,CAAAE,SAAA,cAAoF;IAG1FF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAoB,MAAA,GAAyB;IACvDpB,EADuD,CAAAG,YAAA,EAAM,EACvD;;;;IALgCH,EAAA,CAAAM,SAAA,GAAyB;IAACN,EAA1B,CAAAO,UAAA,QAAAc,QAAA,CAAAC,IAAA,CAAAC,MAAA,EAAAvB,EAAA,CAAAwB,aAAA,CAAyB,QAAAH,QAAA,CAAAC,IAAA,CAAAG,QAAA,CAA4B;IAI7DzB,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAA0B,iBAAA,CAAAL,QAAA,CAAAC,IAAA,CAAAG,QAAA,CAAyB;;;;;;IAvBvDzB,EALJ,CAAAC,cAAA,aAA8D,gCAGjB,cAEgB;IAAlBD,EAAA,CAAAU,UAAA,mBAAAiB,4DAAA;MAAA3B,EAAA,CAAAa,aAAA,CAAAe,GAAA;MAAA,MAAAZ,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAa,KAAA,EAAO;IAAA,EAAC;IAGlD7B,EAFJ,CAAAC,cAAA,cAAoC,cACS,cACT;IAC9BD,EAAA,CAAAE,SAAA,cAA0G;IAC1GF,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAoB,MAAA,QAAC;IAGpCpB,EAHoC,CAAAG,YAAA,EAAO,EACjC,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAoB,MAAA,kBAAU;IACxCpB,EADwC,CAAAG,YAAA,EAAM,EACxC;IAENH,EAAA,CAAAI,UAAA,KAAA0B,6CAAA,kBAG2B;IAU7B9B,EAAA,CAAAG,YAAA,EAAqB;IAErBH,EAAA,CAAAC,cAAA,kBAA+F;IAApED,EAAA,CAAAU,UAAA,mBAAAqB,gEAAA;MAAA/B,EAAA,CAAAa,aAAA,CAAAe,GAAA;MAAA,MAAAI,aAAA,GAAAhC,EAAA,CAAAiC,WAAA;MAAA,OAAAjC,EAAA,CAAAkB,WAAA,CAASc,aAAA,CAAAE,SAAA,EAAsB;IAAA,EAAC;IAAoClC,EAAA,CAAAoB,MAAA,cAAO;IAAApB,EAAA,CAAAG,YAAA,EAAS;IAC/GH,EAAA,CAAAC,cAAA,kBAAiG;IAArED,EAAA,CAAAU,UAAA,mBAAAyB,gEAAA;MAAAnC,EAAA,CAAAa,aAAA,CAAAe,GAAA;MAAA,MAAAI,aAAA,GAAAhC,EAAA,CAAAiC,WAAA;MAAA,OAAAjC,EAAA,CAAAkB,WAAA,CAASc,aAAA,CAAAI,SAAA,EAAsB;IAAA,EAAC;IAAqCpC,EAAA,CAAAoB,MAAA,cAAO;IAC1GpB,EAD0G,CAAAG,YAAA,EAAS,EAC7G;;;;IA/BgBH,EAAA,CAAAM,SAAA,EAAsB;IAAtBN,EAAA,CAAAO,UAAA,WAAAS,MAAA,CAAAqB,WAAA,CAAsB;IAMFrC,EAAA,CAAAM,SAAA,GAA0D;IAA1DN,EAAA,CAAAO,UAAA,SAAAS,MAAA,CAAAsB,WAAA,kBAAAtB,MAAA,CAAAsB,WAAA,CAAAf,MAAA,kCAAAvB,EAAA,CAAAwB,aAAA,CAA0D;IAS5ExB,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAO,UAAA,YAAAS,MAAA,CAAAuB,cAAA,CAAmB;IAcmBvC,EAAA,CAAAM,SAAA,EAAkC;IAAlCN,EAAA,CAAAO,UAAA,cAAAS,MAAA,CAAAwB,oBAAA,CAAkC;IACjCxC,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAO,UAAA,cAAAS,MAAA,CAAAyB,qBAAA,CAAmC;;;ADjClG,OAAM,MAAOC,uBAAuB;EAoDlCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB;IAFxB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAjDrB,KAAAR,WAAW,GAAQ,IAAI;IACzB,KAAAS,WAAW,GAAG;MACZC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE,oDAAoD;MAC/DC,SAAS,EAAE,mDAAmD;MAC9DC,UAAU,EAAE,CACV;QACEC,UAAU,EAAE,GAAG;QACfC,QAAQ,EAAE;UACRR,YAAY,EAAE;;OAEjB,EACD;QACEO,UAAU,EAAE,GAAG;QACfC,QAAQ,EAAE;UACRR,YAAY,EAAE;;OAEjB;KAEJ;IAEC,KAAAS,OAAO,GAAY,EAAE;IACrB,KAAAC,gBAAgB,GAAG,IAAI;IAEvB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IACjD,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IACX,KAAAC,aAAa,GAAmB,EAAE;IAI1C,KAAA/B,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,qBAAqB,GAAG,KAAK;IA8P7B;IACA;IACA,KAAA+B,YAAY,GAAG,KAAK;IACpB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,gBAAgB,GAAG,KAAK;IACxB;IACA,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,cAAc,GAAkB,IAAI;IACpC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,YAAY,GAAgB,IAAI;IAChC,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,gBAAgB,GAAG,KAAK;EA1QrB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAAC5C,WAAW,CAAC6C,YAAY,CAACC,SAAS,CAACtE,IAAI,IAAG;MAC7C,IAAI,CAACgB,WAAW,GAAGhB,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAuE,WAAWA,CAAA;IACT,IAAI,CAACtB,aAAa,CAACuB,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEA;EACAC,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,QAAQ,CAAC;QAAEC,IAAI,EAAE,CAAC,GAAG;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAC7EC,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EACAC,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACP,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,QAAQ,CAAC;QAAEC,IAAI,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAC5EC,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EACAA,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACN,aAAa,EAAE;MACtB,MAAMQ,EAAE,GAAG,IAAI,CAACR,aAAa,CAACC,aAAa;MAC3C,IAAI,CAAC5D,oBAAoB,GAAGmE,EAAE,CAACC,UAAU,GAAG,CAAC;MAC7C,IAAI,CAACnE,qBAAqB,GAAGkE,EAAE,CAACC,UAAU,GAAGD,EAAE,CAACE,WAAW,GAAGF,EAAE,CAACG,WAAW,GAAG,CAAC;;EAEpF;EACAC,eAAeA,CAAA;IACbP,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;EACnD;EAEA;EACAhB,WAAWA,CAAA;IACT,IAAI,CAAC/B,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACa,aAAa,CAACyC,IAAI,CACrB,IAAI,CAACnE,IAAI,CAACoE,GAAG,CAAM,GAAGnH,WAAW,CAACoH,MAAM,UAAU,CAAC,CAACtB,SAAS,CAAC;MAC5DuB,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,EAAE;UAC5C,IAAI,CAAC7D,OAAO,GAAG2D,QAAQ,CAACE,WAAW;UACnCC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAC,IAAI,CAAC/D,OAAO,CAAC;SACpC,MAAM;UACL,IAAI,CAACgE,mBAAmB,EAAE;;QAE5B,IAAI,CAAC/D,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACDgE,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACD,mBAAmB,EAAE;QAC1B,IAAI,CAAC/D,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC,CACH;EACH;EAEA+D,mBAAmBA,CAAA;IACjB,IAAI,CAAChE,OAAO,GAAG;MACb;IAAA,CACD;EACH;EAEAtC,WAAWA,CAACJ,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAAC4C,YAAY,GAAG5C,KAAK;IACzB,IAAI,CAAC6C,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC+D,SAAS,CAAC5G,KAAK,CAAC;IACrB6G,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EACAC,YAAYA,CAAA;IACV,IAAI,CAACpE,MAAM,GAAG,KAAK;IACnB,IAAI,CAACqE,cAAc,EAAE;IACrBL,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IACrC,IAAI,IAAI,CAACG,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC9B,aAAa,CAAC+B,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAEhE5B,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAAC0B,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAAC9B,aAAa,CAAC+B,SAAS,CAACE,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EACAV,SAASA,CAAC5G,KAAa;IACrB,IAAI,CAAC4C,YAAY,GAAG5C,KAAK;IACzB,IAAI,CAACgD,OAAO,GAAG,CAAC;IAChB,IAAI,IAAI,CAACmE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC9B,aAAa,CAAC0B,KAAK,CAACQ,SAAS,GAAG,mBAAmB;;EAE7E;EACAC,SAASA,CAAA;IACP,IAAI,IAAI,CAAC5E,YAAY,GAAG,IAAI,CAACF,OAAO,CAAC+E,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAACxE,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC4E,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACT,YAAY,EAAE;;EAEvB;EACAU,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC/E,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACK,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC4E,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACT,YAAY,EAAE;;EAEvB;EAEAW,aAAaA,CAACC,KAAoB;IAChC,IAAI,CAAC,IAAI,CAAChF,MAAM,EAAE;IAClB,QAAQgF,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACH,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACH,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACP,YAAY,EAAE;QACnB;;EAEN;EACAc,YAAYA,CAACF,KAAiB;IAC5B,IAAI,IAAI,CAAC/E,UAAU,EAAE;IACrB,MAAMkF,MAAM,GAAGH,KAAK,CAACI,OAAO;IAC5B,MAAMC,WAAW,GAAGC,MAAM,CAACC,UAAU;IACrC,IAAIJ,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACP,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACH,SAAS,EAAE;;EAEpB;EACAa,YAAYA,CAACR,KAAiB;IAC5B,IAAI,CAAC9E,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAG0E,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACL,OAAO;IAC1C,IAAI,CAAC7E,YAAY,GAAG,IAAI,CAACD,UAAU;EACrC;EACAoF,WAAWA,CAACV,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAAC9E,UAAU,EAAE;IACtB,IAAI,CAACK,YAAY,GAAGyE,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACL,OAAO;IAC5C,IAAI,CAACO,kBAAkB,EAAE;EAC3B;EACAC,UAAUA,CAACZ,KAAiB;IAC1B,IAAI,CAAC,IAAI,CAAC9E,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,GAAG,KAAK;IACvB,MAAM2F,SAAS,GAAG,IAAI,CAACtF,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,MAAMwF,SAAS,GAAGR,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC/E,0BAA0B;IACrE,IAAIuF,IAAI,CAACC,GAAG,CAACH,SAAS,CAAC,GAAGC,SAAS,EAAE;MACnC,IAAID,SAAS,GAAG,CAAC,EAAE;QACjB,IAAI,CAACf,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACH,SAAS,EAAE;;KAEnB,MAAM;MACL,IAAI,CAACvE,aAAa,GAAG,CAAC;MACtB,IAAI,CAACH,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC4E,MAAM,EAAE;;EAEjB;EACQc,kBAAkBA,CAAA;IACxB,MAAME,SAAS,GAAG,IAAI,CAACtF,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,IAAI,CAACH,OAAO,GAAI0F,SAAS,GAAGP,MAAM,CAACC,UAAU,GAAI,EAAE;IACnD,IAAI,IAAI,CAACjB,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC9B,aAAa,CAAC0B,KAAK,CAACQ,SAAS,GACjD,6BAA6B,IAAI,CAACvE,OAAO,MAAM;;EAErD;EACQ0E,MAAMA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC5E,UAAU,EAAE;IACtB,IAAI,CAACE,OAAO,IAAI,CAAC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,OAAO,IAAI,IAAI,CAACO,eAAe;IAC1E,IAAIqF,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC7F,OAAO,GAAG,IAAI,CAACC,aAAa,CAAC,GAAG,GAAG,EAAE;MACrD,IAAI,CAACD,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MACvB,IAAI,IAAI,CAACI,eAAe,EAAE;QACxB,MAAM4F,QAAQ,GAAG,IAAI,CAAC5F,eAAe,KAAK,SAAS,GAC/C,IAAI,CAACN,YAAY,GAAG,CAAC,GACrB,IAAI,CAACA,YAAY,GAAG,CAAC;QACzB,IAAI,CAACgE,SAAS,CAACkC,QAAQ,CAAC;QACxB,IAAI,CAAC5F,eAAe,GAAG,IAAI;;MAE7B;;IAEF,IAAI,IAAI,CAACiE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC9B,aAAa,CAAC0B,KAAK,CAACQ,SAAS,GACjD,6BAA6B,IAAI,CAACvE,OAAO,MAAM;;IAEnD+F,qBAAqB,CAAC,MAAM,IAAI,CAACrB,MAAM,EAAE,CAAC;EAC5C;EACQR,cAAcA,CAAA;IACpB,MAAM8B,MAAM,GAAGnC,QAAQ,CAACoC,gBAAgB,CAAC,eAAe,CAAC;IACzDD,MAAM,CAACjE,OAAO,CAAEmE,KAAU,IAAI;MAC5B,IAAIA,KAAK,CAACC,KAAK,EAAE;QACfD,KAAK,CAACC,KAAK,EAAE;;IAEjB,CAAC,CAAC;EACJ;EACQxE,mBAAmBA,CAAA,GAAI;EACvBO,oBAAoBA,CAAA,GAAI;EAChCkE,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC1G,OAAO,CAAC,IAAI,CAACE,YAAY,CAAC;EACxC;EACAyG,gBAAgBA,CAAA;IACd,OAAQ,CAAC,IAAI,CAACzG,YAAY,GAAG,CAAC,IAAI,IAAI,CAACF,OAAO,CAAC+E,MAAM,GAAI,GAAG;EAC9D;EACA6B,UAAUA,CAACC,UAAkB;IAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMI,aAAa,GAAGf,IAAI,CAACgB,KAAK,CAAC,CAACJ,GAAG,CAACK,OAAO,EAAE,GAAGH,IAAI,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAChF,IAAIF,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAClD,MAAMG,WAAW,GAAGlB,IAAI,CAACgB,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIG,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAC9C,MAAMC,UAAU,GAAGnB,IAAI,CAACgB,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EACAC,YAAYA,CAACC,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EACAC,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCxD,KAAK,EAAE,UAAU;MACjByD,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EAClB;EACAM,WAAWA,CAACC,OAAY;IACtB,IAAI,CAAC/I,MAAM,CAACgJ,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACE,GAAG,CAAC,CAAC;EACjD;EACAC,WAAWA,CAAA;IACT,MAAMC,KAAK,GAAG,IAAI,CAAC5B,eAAe,EAAE;IACpC,OAAO,CAAC,EAAE4B,KAAK,IAAIA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACxD,MAAM,GAAG,CAAC,CAAC;EACjE;EACAyD,gBAAgBA,CAAA;IACd,MAAMF,KAAK,GAAG,IAAI,CAAC5B,eAAe,EAAE;IACpC,OAAO4B,KAAK,EAAEC,QAAQ,IAAI,EAAE;EAC9B;EAoBAnK,KAAKA,CAAA;IACH,IAAI,CAAC2C,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACQ,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;EAC1B;EACA8G,UAAUA,CAAA;IACR,IAAI,CAAC1H,YAAY,GAAG,KAAK;IACzB,IAAI,CAACG,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACJ,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACG,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACS,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;EAC3B;EACA6G,SAASA,CAAA;IACP,IAAI,CAAC3H,YAAY,GAAG,KAAK;IACzB,IAAI,CAACE,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACQ,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;IACxBoB,UAAU,CAAC,MAAM,IAAI,CAAC4F,kBAAkB,EAAE,EAAE,GAAG,CAAC;EAClD;EACMA,kBAAkBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,IAAI;QACFD,KAAI,CAACpH,WAAW,SAASsH,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UAAExC,KAAK,EAAE,IAAI;UAAEyC,KAAK,EAAE;QAAI,CAAE,CAAC;QAC1F,MAAMzC,KAAK,GAAQrC,QAAQ,CAAC+E,cAAc,CAAC,YAAY,CAAC;QACxD,IAAI1C,KAAK,EAAE;UACTA,KAAK,CAAC2C,SAAS,GAAGP,KAAI,CAACpH,WAAW;UAClCgF,KAAK,CAAC4C,IAAI,EAAE;;OAEf,CAAC,OAAOC,GAAG,EAAE;QACZC,KAAK,CAAC,0BAA0B,CAAC;QACjCV,KAAI,CAAC3H,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACAsI,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC/H,WAAW,EAAE;IACvB,IAAI,CAACF,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,aAAa,GAAG,IAAKkE,MAAc,CAAC+D,aAAa,CAAC,IAAI,CAAChI,WAAW,CAAC;IACxE,IAAI,CAACD,aAAa,CAACkI,eAAe,GAAIC,CAAM,IAAI;MAC9C,IAAIA,CAAC,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE,IAAI,CAACtI,cAAc,CAACiC,IAAI,CAACmG,CAAC,CAACC,IAAI,CAAC;IACvD,CAAC;IACD,IAAI,CAACpI,aAAa,CAACsI,MAAM,GAAG,MAAK;MAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACzI,cAAc,EAAE;QAAE0I,IAAI,EAAE;MAAY,CAAE,CAAC;MAClE,IAAI,CAACvI,cAAc,GAAGwI,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACjD,CAAC;IACD,IAAI,CAACvI,aAAa,CAAC4I,KAAK,EAAE;IAC1B,IAAI,CAAC9I,WAAW,GAAG,IAAI;EACzB;EACA+I,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC7I,aAAa,IAAI,IAAI,CAACF,WAAW,EAAE;MAC1C,IAAI,CAACE,aAAa,CAAC8I,IAAI,EAAE;MACzB,IAAI,CAAChJ,WAAW,GAAG,KAAK;;EAE5B;EACMiJ,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA1B,iBAAA;MACjB,IAAI,CAAC0B,MAAI,CAAC9I,cAAc,EAAE;MAC1B8I,MAAI,CAAC7I,eAAe,GAAG,IAAI;MAC3B,IAAI;QACF,MAAMoI,IAAI,GAAG,IAAIC,IAAI,CAACQ,MAAI,CAACjJ,cAAc,EAAE;UAAE0I,IAAI,EAAE;QAAY,CAAE,CAAC;QAClE,MAAMQ,QAAQ,GAAG,IAAIC,QAAQ,EAAE;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEZ,IAAI,EAAE,WAAW,CAAC;QAC3C,MAAMa,SAAS,SAAcJ,MAAI,CAACnL,IAAI,CAACwL,IAAI,CAAC,qBAAqB,EAAEJ,QAAQ,CAAC,CAACK,SAAS,EAAE;QACxF,MAAMC,WAAW,GAAG;UAClBC,KAAK,EAAE;YACLf,IAAI,EAAEW,SAAS,CAACX,IAAI;YACpBgB,GAAG,EAAEL,SAAS,CAACK;WAChB;UACDC,OAAO,EAAEV,MAAI,CAAC5I,cAAc;UAC5BuJ,MAAM,EAAE;SACT;QACD,MAAMX,MAAI,CAACnL,IAAI,CAACwL,IAAI,CAAC,cAAc,EAAEE,WAAW,CAAC,CAACD,SAAS,EAAE;QAC7DN,MAAI,CAACtJ,gBAAgB,GAAG,KAAK;QAC7BsJ,MAAI,CAACvI,WAAW,EAAE;OACnB,CAAC,OAAOqH,GAAG,EAAE;QACZC,KAAK,CAAC,wBAAwB,CAAC;OAChC,SAAS;QACRiB,MAAI,CAAC7I,eAAe,GAAG,KAAK;QAC5B6I,MAAI,CAACY,iBAAiB,EAAE;;IACzB;EACH;EACAA,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC3J,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC4J,SAAS,EAAE,CAAC/I,OAAO,CAACgJ,KAAK,IAAIA,KAAK,CAAChB,IAAI,EAAE,CAAC;MAC3D,IAAI,CAAC7I,WAAW,GAAG,IAAI;;IAEzB,IAAI,CAACD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACF,WAAW,GAAG,KAAK;IACxB,IAAI,CAACI,cAAc,GAAG,IAAI;EAC5B;EACA6J,iBAAiBA,CAAA;IACf,IAAI,CAACrK,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACkK,iBAAiB,EAAE;EAC1B;EACAI,wBAAwBA,CAACC,KAAc;IACrC,IAAI,CAACtK,mBAAmB,GAAG,KAAK;IAChC,IAAIsK,KAAK,EAAE;MACT,IAAI,CAACrK,mBAAmB,GAAG,IAAI;KAChC,MAAM;MACL,IAAI,CAACC,gBAAgB,GAAG,IAAI;;EAEhC;EACAqK,UAAUA,CAAA;IACR,IAAI,CAACtK,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,iBAAiB,GAAG,IAAI;IAC7B+B,UAAU,CAAC,MAAK;MACd,MAAM2I,KAAK,GAAQvH,QAAQ,CAAC+E,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIwC,KAAK,EAAE;QACTA,KAAK,CAACC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC;QAC5CD,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACAC,WAAWA,CAAA;IACT,IAAI,CAAC1K,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,iBAAiB,GAAG,IAAI;IAC7B+B,UAAU,CAAC,MAAK;MACd,MAAM2I,KAAK,GAAQvH,QAAQ,CAAC+E,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIwC,KAAK,EAAE;QACTA,KAAK,CAACI,eAAe,CAAC,SAAS,CAAC;QAChCJ,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACAG,mBAAmBA,CAAC5G,KAAU;IAC5B,MAAM6G,IAAI,GAAG7G,KAAK,CAAC8G,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAACpK,YAAY,GAAGoK,IAAI;;EAE5B;EACMG,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAvD,iBAAA;MAClB,IAAI,CAACuD,MAAI,CAACxK,YAAY,EAAE;MACxBwK,MAAI,CAACtK,gBAAgB,GAAG,IAAI;MAC5B,IAAI;QACF,MAAMuK,UAAU,GAAG,IAAI5B,QAAQ,EAAE;QACjC4B,UAAU,CAAC3B,MAAM,CAAC,OAAO,EAAE0B,MAAI,CAACxK,YAAY,CAAC;QAC7C,MAAM+I,SAAS,SAAcyB,MAAI,CAAChN,IAAI,CAACwL,IAAI,CAAC,qBAAqB,EAAEyB,UAAU,CAAC,CAACxB,SAAS,EAAE;QAC1F,MAAMyB,YAAY,GAAG;UACnBvB,KAAK,EAAE;YACLf,IAAI,EAAEW,SAAS,CAACX,IAAI;YACpBgB,GAAG,EAAEL,SAAS,CAACK;WAChB;UACDC,OAAO,EAAEmB,MAAI,CAACvK;SACf;QACD,MAAMuK,MAAI,CAAChN,IAAI,CAACwL,IAAI,CAAC,cAAc,EAAE0B,YAAY,CAAC,CAACzB,SAAS,EAAE;QAC9DuB,MAAI,CAACpL,iBAAiB,GAAG,KAAK;QAC9BoL,MAAI,CAACpK,WAAW,EAAE;OACnB,CAAC,OAAOqH,GAAG,EAAE;QACZC,KAAK,CAAC,yBAAyB,CAAC;OACjC,SAAS;QACR8C,MAAI,CAACtK,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACAyK,kBAAkBA,CAAA;IAChB,IAAI,CAACvL,iBAAiB,GAAG,KAAK;EAChC;EAGF,IAAIlC,cAAcA,CAAA;IAChB,IACE0N,KAAK,CAACC,OAAO,CAAC,IAAI,CAACzM,OAAO,CAAC,IAC3B,IAAI,CAACA,OAAO,CAAC+E,MAAM,GAAG,CAAC,IACvB,OAAQ,IAAI,CAAC/E,OAAO,CAAC,CAAC,CAAS,CAACA,OAAO,KAAK,WAAW,EACvD;MACA;MACA,OAAQ,IAAI,CAACA,OAAiB,CAC3B0M,OAAO,CAACC,KAAK,IAAIH,KAAK,CAACC,OAAO,CAACE,KAAK,CAAC3M,OAAO,CAAC,GAAG2M,KAAK,CAAC3M,OAAO,GAAG,EAAE,CAAC,CACnE4M,MAAM,CAAEC,CAAQ,IAAKA,CAAC,CAACC,QAAQ,CAAC;KACpC,MAAM,IAAIN,KAAK,CAACC,OAAO,CAAC,IAAI,CAACzM,OAAO,CAAC,EAAE;MACtC;MACA,OAAQ,IAAI,CAACA,OAAmB,CAAC4M,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC;;IAE1D,OAAO,EAAE;EACX;;;uBAtfa7N,uBAAuB,EAAA1C,EAAA,CAAAwQ,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA1Q,EAAA,CAAAwQ,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA5Q,EAAA,CAAAwQ,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAvBpO,uBAAuB;MAAAqO,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UAAvBlR,EAAA,CAAAU,UAAA,qBAAA0Q,mDAAAC,MAAA;YAAA,OAAAF,GAAA,CAAAxI,aAAA,CAAA0I,MAAA,CAAqB;UAAA,UAAArR,EAAA,CAAAsR,iBAAA,CAAE;;;;;;;;;;UC1CpCtR,EAAA,CAAAC,cAAA,aAA+B;UA0C/BD,EAxCE,CAAAI,UAAA,IAAAmR,sCAAA,iBAAsD,IAAAC,sCAAA,kBAwCM;UAmC9DxR,EAAA,CAAAG,YAAA,EAAM;;;UA3EEH,EAAA,CAAAM,SAAA,EAAsB;UAAtBN,EAAA,CAAAO,UAAA,SAAA4Q,GAAA,CAAAzN,gBAAA,CAAsB;UAwCO1D,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAO,UAAA,UAAA4Q,GAAA,CAAAzN,gBAAA,CAAuB;;;qBDJhD9D,YAAY,EAAA6R,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE9R,WAAW,EAAEE,mBAAmB,EAAA6R,EAAA,CAAAC,sBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}