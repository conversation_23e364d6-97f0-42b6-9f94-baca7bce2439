{"ast": null, "code": "import { Observable, BehaviorSubject } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let RecommendationService = /*#__PURE__*/(() => {\n  class RecommendationService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = 'http://localhost:5000/api';\n      this.userAnalytics$ = new BehaviorSubject(null);\n    }\n    // Suggested for You - Personalized Recommendations\n    getSuggestedProducts(userId, limit = 10) {\n      // For demo purposes, return fallback data immediately to avoid API calls\n      console.log('🎯 Loading suggested products (offline mode)');\n      return this.getFallbackSuggestedProducts(limit);\n      /* API version - uncomment when backend is available\n      const params = new URLSearchParams();\n      if (userId) params.append('userId', userId);\n      params.append('limit', limit.toString());\n           return this.http.get<any>(`${this.apiUrl}/recommendations/suggested?${params.toString()}`)\n        .pipe(\n          map(response => response.success ? response.data : []),\n          catchError(error => {\n            console.error('Error fetching suggested products:', error);\n            return this.getFallbackSuggestedProducts(limit);\n          })\n        );\n      */\n    }\n    // Trending Products - Based on Analytics\n    getTrendingProducts(category, limit = 10) {\n      // For demo purposes, return fallback data immediately to avoid API calls\n      console.log('📈 Loading trending products (offline mode)');\n      return this.getFallbackTrendingProducts(limit);\n      /* API version - uncomment when backend is available\n      const params = new URLSearchParams();\n      if (category) params.append('category', category);\n      params.append('limit', limit.toString());\n           return this.http.get<any>(`${this.apiUrl}/recommendations/trending?${params.toString()}`)\n        .pipe(\n          map(response => response.success ? response.data : []),\n          catchError(error => {\n            console.error('Error fetching trending products:', error);\n            return this.getFallbackTrendingProducts(limit);\n          })\n        );\n      */\n    }\n    // Similar Products - Based on Product\n    getSimilarProducts(productId, limit = 6) {\n      return this.http.get(`${this.apiUrl}/recommendations/similar/${productId}?limit=${limit}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n        console.error('Error fetching similar products:', error);\n        return this.getFallbackSimilarProducts(limit);\n      }));\n    }\n    // Recently Viewed Products\n    getRecentlyViewed(userId, limit = 8) {\n      return this.http.get(`${this.apiUrl}/recommendations/recent/${userId}?limit=${limit}`).pipe(map(response => response.success ? response.data : []), catchError(error => {\n        console.error('Error fetching recently viewed:', error);\n        return this.getFallbackRecentProducts(limit);\n      }));\n    }\n    // Track User Behavior for Analytics\n    trackProductView(productId, category, duration = 0) {\n      return this.http.post(`${this.apiUrl}/analytics/track-view`, {\n        productId,\n        category,\n        duration,\n        timestamp: new Date()\n      }).pipe(catchError(error => {\n        console.error('Error tracking product view:', error);\n        return [];\n      }));\n    }\n    trackSearch(query, category, resultsClicked = 0) {\n      return this.http.post(`${this.apiUrl}/analytics/track-search`, {\n        query,\n        category,\n        resultsClicked,\n        timestamp: new Date()\n      }).pipe(catchError(error => {\n        console.error('Error tracking search:', error);\n        return [];\n      }));\n    }\n    trackPurchase(productId, category, price) {\n      return this.http.post(`${this.apiUrl}/analytics/track-purchase`, {\n        productId,\n        category,\n        price,\n        timestamp: new Date()\n      }).pipe(catchError(error => {\n        console.error('Error tracking purchase:', error);\n        return [];\n      }));\n    }\n    // User Analytics\n    getUserAnalytics(userId) {\n      return this.http.get(`${this.apiUrl}/analytics/user/${userId}`).pipe(map(response => response.success ? response.data : this.getDefaultAnalytics(userId)), catchError(error => {\n        console.error('Error fetching user analytics:', error);\n        return [this.getDefaultAnalytics(userId)];\n      }));\n    }\n    // Category-based Recommendations\n    getCategoryRecommendations(category, limit = 8) {\n      // For demo purposes, return fallback data immediately to avoid API calls\n      console.log(`🏷️ Loading ${category} recommendations (offline mode)`);\n      return this.getFallbackCategoryProducts(category, limit);\n      /* API version - uncomment when backend is available\n      return this.http.get<any>(`${this.apiUrl}/recommendations/category/${category}?limit=${limit}`)\n        .pipe(\n          map(response => response.success ? response.data : []),\n          catchError(error => {\n            console.error('Error fetching category recommendations:', error);\n            return this.getFallbackCategoryProducts(category, limit);\n          })\n        );\n      */\n    }\n    // Fallback methods for offline/error scenarios\n    getFallbackSuggestedProducts(limit) {\n      console.log('❌ No suggested products available from API');\n      return new Observable(observer => {\n        observer.next([]);\n        observer.complete();\n      });\n    }\n    getFallbackTrendingProducts(limit) {\n      console.log('❌ No trending products available from API');\n      return new Observable(observer => {\n        observer.next([]);\n        observer.complete();\n      });\n    }\n    getFallbackSimilarProducts(limit) {\n      return this.getFallbackSuggestedProducts(limit);\n    }\n    getFallbackRecentProducts(limit) {\n      return this.getFallbackSuggestedProducts(limit);\n    }\n    getFallbackCategoryProducts(category, limit) {\n      return this.getFallbackSuggestedProducts(limit);\n    }\n    getDefaultAnalytics(userId) {\n      return {\n        userId,\n        viewHistory: [],\n        searchHistory: [],\n        purchaseHistory: [],\n        wishlistItems: [],\n        cartItems: [],\n        preferredCategories: ['women', 'men', 'accessories'],\n        priceRange: {\n          min: 500,\n          max: 5000\n        },\n        brandPreferences: []\n      };\n    }\n    // Update user analytics locally\n    updateUserAnalytics(analytics) {\n      this.userAnalytics$.next(analytics);\n    }\n    // Get current user analytics\n    getCurrentUserAnalytics() {\n      return this.userAnalytics$.asObservable();\n    }\n    static {\n      this.ɵfac = function RecommendationService_Factory(t) {\n        return new (t || RecommendationService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: RecommendationService,\n        factory: RecommendationService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return RecommendationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}