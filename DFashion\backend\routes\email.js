const express = require('express');
const router = express.Router();
const nodemailer = require('nodemailer');
const fs = require('fs');
const mongoose = require('mongoose');
const multer = require('multer');
const path = require('path');
const crypto = require('crypto');

// Import models (you'll need to create these)
// const User = require('../models/User');
// const BillEmail = require('../models/BillEmail');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/bills/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    cb(null, 'bill_' + Date.now() + path.extname(file.originalname));
  }
});

const upload = multer({ storage: storage });

// Configure nodemailer
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: '<EMAIL>', // Replace with your email
    pass: 'umkl llnz kgkp nmpj' // Replace with your app password
  }
});

// Send Bill Email
router.post('/send-bill', upload.single('billpdf'), async (req, res) => {
  try {
    console.log("Request body:", req.body);
    console.log("Uploaded file:", req.file);

    const { userId, name, email, message, paymentType } = req.body;

    // Validate required fields
    if (!email || !name) {
      return res.status(400).json({
        success: false,
        message: 'Email and name are required'
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No bill file uploaded'
      });
    }

    // Create email bill record (if you have the model)
    /*
    const emailBill = new BillEmail({
      _id: new mongoose.Types.ObjectId(),
      userId: userId,
      name: name,
      email: email,
      message: message || 'Thank you for your purchase! Please find your invoice attached.',
      billpdf: req.file.path,
      paymentType: JSON.parse(paymentType || '{}')
    });

    await emailBill.save();
    */

    // Prepare email options
    const mailOptions = {
      from: '<EMAIL>',
      to: email,
      subject: 'Your DFashion Invoice - Order Confirmation',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0;">DFashion</h1>
            <p style="color: white; margin: 5px 0;">Thank you for your purchase!</p>
          </div>
          <div style="padding: 20px; background: #f8f9fa;">
            <h2>Hi ${name},</h2>
            <p>Thank you for shopping with DFashion! Your order has been confirmed and your invoice is attached.</p>
            <p>${message || 'We hope you love your new items!'}</p>
            <div style="background: white; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h3>Order Details:</h3>
              <p>Please find your detailed invoice attached to this email.</p>
            </div>
            <p>If you have any questions about your order, please don't hesitate to contact us.</p>
            <p>Best regards,<br>The DFashion Team</p>
          </div>
          <div style="background: #e9ecef; padding: 15px; text-align: center; font-size: 12px; color: #6c757d;">
            <p>© 2024 DFashion. All rights reserved.</p>
            <p>For support, email <NAME_EMAIL></p>
          </div>
        </div>
      `,
      attachments: [
        {
          filename: req.file.originalname,
          path: req.file.path
        }
      ]
    };

    // Send email
    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent:', info.response);

    // Delete the file after sending
    setTimeout(() => {
      fs.unlink(req.file.path, (err) => {
        if (err) {
          console.error('Error deleting file:', err);
        } else {
          console.log('Bill file deleted successfully');
        }
      });
    }, 5000); // Delete after 5 seconds

    res.status(200).json({
      success: true,
      message: 'Bill sent successfully via email',
      data: {
        messageId: info.messageId,
        email: email
      }
    });

  } catch (error) {
    console.error('Error sending bill email:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send bill email',
      error: error.message
    });
  }
});

// Send Forgot Password Email
router.post('/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;
    console.log("Forgot password request for:", email);

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    // Find user by email (if you have User model)
    /*
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    */

    // Generate reset token
    const token = crypto.randomBytes(32).toString('hex');
    const encodedEmail = Buffer.from(email).toString('base64');

    // Set token and expiration (if you have User model)
    /*
    user.resetPasswordToken = token;
    user.resetPasswordExpires = Date.now() + 3600000; // 1 hour
    await user.save();
    */

    // Create reset link
    const resetLink = `http://localhost:4200/reset-password/${token}+${encodedEmail}`;

    const mailOptions = {
      from: '<EMAIL>',
      to: email,
      subject: 'Password Reset - DFashion',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0;">DFashion</h1>
            <p style="color: white; margin: 5px 0;">Password Reset Request</p>
          </div>
          <div style="padding: 20px; background: #f8f9fa;">
            <h2>Password Reset</h2>
            <p>You are receiving this email because you (or someone else) have requested a password reset for your account.</p>
            <p>Please click on the following link, or paste this into your browser to complete the process:</p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetLink}" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Reset Password</a>
            </div>
            <p>If you did not request this, please ignore this email and your password will remain unchanged.</p>
            <p><strong>This link will expire in 1 hour.</strong></p>
          </div>
          <div style="background: #e9ecef; padding: 15px; text-align: center; font-size: 12px; color: #6c757d;">
            <p>© 2024 DFashion. All rights reserved.</p>
          </div>
        </div>
      `
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Password reset email sent:', info.response);

    res.status(200).json({
      success: true,
      message: "Password reset email sent. Please check your inbox or spam folder."
    });

  } catch (error) {
    console.error('Error sending password reset email:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send password reset email',
      error: error.message
    });
  }
});

// Send Notification Email
router.post('/send-notification', async (req, res) => {
  try {
    const { type, to, subject, data } = req.body;

    if (!to || !type) {
      return res.status(400).json({
        success: false,
        message: 'Email and notification type are required'
      });
    }

    let emailTemplate = '';
    let emailSubject = subject;

    // Generate email content based on type
    switch (type) {
      case 'order_confirmation':
        emailSubject = 'Order Confirmation - DFashion';
        emailTemplate = generateOrderConfirmationEmail(data);
        break;
      case 'payment_success':
        emailSubject = 'Payment Successful - DFashion';
        emailTemplate = generatePaymentSuccessEmail(data);
        break;
      case 'payment_failed':
        emailSubject = 'Payment Failed - DFashion';
        emailTemplate = generatePaymentFailedEmail(data);
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid notification type'
        });
    }

    const mailOptions = {
      from: '<EMAIL>',
      to: to,
      subject: emailSubject,
      html: emailTemplate
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Notification email sent:', info.response);

    res.status(200).json({
      success: true,
      message: 'Notification sent successfully',
      data: {
        messageId: info.messageId,
        type: type
      }
    });

  } catch (error) {
    console.error('Error sending notification:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send notification',
      error: error.message
    });
  }
});

// Email template generators
function generateOrderConfirmationEmail(data) {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center;">
        <h1 style="color: white; margin: 0;">DFashion</h1>
        <p style="color: white; margin: 5px 0;">Order Confirmation</p>
      </div>
      <div style="padding: 20px; background: #f8f9fa;">
        <h2>Hi ${data.customerName},</h2>
        <p>Thank you for your order! We're excited to confirm that we've received your order.</p>
        <div style="background: white; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h3>Order Details:</h3>
          <p><strong>Order ID:</strong> ${data.orderId}</p>
          <p><strong>Order Date:</strong> ${new Date(data.orderDate).toLocaleDateString()}</p>
          <p><strong>Total Amount:</strong> ₹${data.total}</p>
        </div>
        <p>We'll send you another email when your order ships.</p>
        <p>Best regards,<br>DFashion Team</p>
      </div>
    </div>
  `;
}

function generatePaymentSuccessEmail(data) {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); padding: 20px; text-align: center;">
        <h1 style="color: white; margin: 0;">Payment Successful!</h1>
      </div>
      <div style="padding: 20px; background: #f8f9fa;">
        <h2>Hi ${data.customerName},</h2>
        <p>Your payment has been processed successfully!</p>
        <div style="background: white; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h3>Payment Details:</h3>
          <p><strong>Payment ID:</strong> ${data.paymentId}</p>
          <p><strong>Order ID:</strong> ${data.orderId}</p>
          <p><strong>Amount:</strong> ₹${data.amount}</p>
          <p><strong>Payment Method:</strong> ${data.paymentMethod}</p>
        </div>
        <p>Your order is now being processed and will be shipped soon.</p>
        <p>Best regards,<br>DFashion Team</p>
      </div>
    </div>
  `;
}

function generatePaymentFailedEmail(data) {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background: linear-gradient(135deg, #f5576c 0%, #f093fb 100%); padding: 20px; text-align: center;">
        <h1 style="color: white; margin: 0;">Payment Failed</h1>
      </div>
      <div style="padding: 20px; background: #f8f9fa;">
        <h2>Hi ${data.customerName},</h2>
        <p>We're sorry, but your payment could not be processed.</p>
        <div style="background: white; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h3>Order Details:</h3>
          <p><strong>Order ID:</strong> ${data.orderId}</p>
          <p><strong>Amount:</strong> ₹${data.amount}</p>
          <p><strong>Reason:</strong> ${data.failureReason}</p>
        </div>
        <p>Please try again or contact our support team for assistance.</p>
        <div style="text-align: center; margin: 20px 0;">
          <a href="${data.retryLink}" style="background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">Retry Payment</a>
        </div>
        <p>Best regards,<br>DFashion Team</p>
      </div>
    </div>
  `;
}

module.exports = router;
