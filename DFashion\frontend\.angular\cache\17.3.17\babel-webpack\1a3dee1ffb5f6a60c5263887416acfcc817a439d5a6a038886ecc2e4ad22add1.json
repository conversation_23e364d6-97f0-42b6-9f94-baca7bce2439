{"ast": null, "code": "import { AuthGuard } from '../../core/guards/auth.guard';\nexport const vendorRoutes = [{\n  path: '',\n  loadComponent: () => import('./pages/dashboard/vendor-dashboard.component').then(m => m.VendorDashboardComponent),\n  canActivate: [AuthGuard],\n  title: 'Vendor Dashboard - DFashion'\n}, {\n  path: 'products',\n  loadComponent: () => import('./pages/products/vendor-products.component').then(m => m.VendorProductsComponent),\n  canActivate: [AuthGuard],\n  title: 'My Products - DFashion'\n}, {\n  path: 'products/create',\n  loadComponent: () => import('./pages/products/create-product.component').then(m => m.CreateProductComponent),\n  canActivate: [AuthGuard],\n  title: 'Create Product - DFashion'\n}, {\n  path: 'posts',\n  loadComponent: () => import('./pages/posts/vendor-posts.component').then(m => m.VendorPostsComponent),\n  canActivate: [AuthGuard],\n  title: 'My Posts - DFashion'\n}, {\n  path: 'posts/create',\n  loadComponent: () => import('./pages/posts/create-post.component').then(m => m.CreatePostComponent),\n  canActivate: [AuthGuard],\n  title: 'Create Post - DFashion'\n}, {\n  path: 'stories',\n  loadComponent: () => import('./pages/stories/vendor-stories.component').then(m => m.VendorStoriesComponent),\n  canActivate: [AuthGuard],\n  title: 'My Stories - DFashion'\n}, {\n  path: 'stories/create',\n  loadComponent: () => import('./pages/stories/create-story.component').then(m => m.CreateStoryComponent),\n  canActivate: [AuthGuard],\n  title: 'Create Story - DFashion'\n}, {\n  path: 'orders',\n  loadComponent: () => import('./pages/orders/vendor-orders.component').then(m => m.VendorOrdersComponent),\n  canActivate: [AuthGuard],\n  title: 'Orders - DFashion'\n}, {\n  path: 'analytics',\n  loadComponent: () => import('./pages/analytics/vendor-analytics.component').then(m => m.VendorAnalyticsComponent),\n  canActivate: [AuthGuard],\n  title: 'Analytics - DFashion'\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}