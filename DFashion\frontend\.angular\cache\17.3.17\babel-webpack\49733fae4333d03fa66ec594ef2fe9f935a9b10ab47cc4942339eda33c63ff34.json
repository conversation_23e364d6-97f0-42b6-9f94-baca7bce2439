{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, i as forceUpdate, h, H as Host, f as getElement, d as createEvent } from './index-a1a47f01.js';\nimport { k as inheritAttributes, r as raf } from './helpers-be245865.js';\nimport { p as printIonWarning, a as printIonError } from './index-9b0d46f4.js';\nimport { h as hostContext, c as createColorClasses, o as openURL } from './theme-01f3f29c.js';\nimport { o as chevronForward } from './index-f7dc70ba.js';\nimport { b as getIonMode, c as config } from './ionic-global-94f25d1b.js';\nconst itemIosCss = \":host{--border-radius:0px;--border-width:0px;--border-style:solid;--padding-top:0px;--padding-bottom:0px;--padding-end:0px;--padding-start:0px;--inner-border-width:0px;--inner-padding-top:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;--inner-padding-end:0px;--inner-box-shadow:none;--show-full-highlight:0;--show-inset-highlight:0;--detail-icon-color:initial;--detail-icon-font-size:1.25em;--detail-icon-opacity:0.25;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:initial;text-decoration:none;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color:not(.item-fill-solid):not(.item-fill-outline)) .item-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.ion-color:not(.item-fill-solid):not(.item-fill-outline)) .item-native,:host(.ion-color:not(.item-fill-solid):not(.item-fill-outline)) .item-inner{border-color:var(--ion-color-shade)}:host(.ion-activated) .item-native{color:var(--color-activated)}:host(.ion-activated) .item-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.ion-color.ion-activated) .item-native{color:var(--ion-color-contrast)}:host(.ion-focused) .item-native{color:var(--color-focused)}:host(.ion-focused) .item-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-color.ion-focused) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .item-native::after{background:var(--ion-color-contrast)}@media (any-hover: hover){:host(.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--color-hover)}:host(.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--ion-color-contrast)}}:host(.item-has-interactive-control){cursor:pointer}:host(.item-interactive-disabled:not(.item-multiple-inputs)){cursor:default;pointer-events:none}:host(.item-disabled){cursor:default;opacity:0.3;pointer-events:none}.item-native{border-radius:var(--border-radius);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;min-height:var(--min-height);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);overflow:inherit;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}[dir=rtl] .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){.item-native:dir(rtl){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}.item-native::-moz-focus-inner{border:0}.item-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:var(--transition);transition:var(--transition);z-index:-1}button,a{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-user-drag:none}.item-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);-webkit-box-shadow:var(--inner-box-shadow);box-shadow:var(--inner-box-shadow);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-bottom{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:0;padding-bottom:0;padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--inner-padding-end) + var(--ion-safe-area-right, 0px));display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host-context([dir=rtl]) .item-bottom{padding-left:calc(var(--inner-padding-end) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px))}[dir=rtl] .item-bottom{padding-left:calc(var(--inner-padding-end) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px))}@supports selector(:dir(rtl)){.item-bottom:dir(rtl){padding-left:calc(var(--inner-padding-end) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px))}}.item-detail-icon{-webkit-margin-start:calc(var(--inner-padding-end) / 2);margin-inline-start:calc(var(--inner-padding-end) / 2);-webkit-margin-end:-6px;margin-inline-end:-6px;color:var(--detail-icon-color);font-size:var(--detail-icon-font-size);opacity:var(--detail-icon-opacity)}::slotted(ion-icon){font-size:1.6em}::slotted(ion-button){--margin-top:0;--margin-bottom:0;--margin-start:0;--margin-end:0;z-index:1}::slotted(ion-label:not([slot=end])){-ms-flex:1;flex:1;width:-webkit-min-content;width:-moz-min-content;width:min-content;max-width:100%}:host(.item-input){-ms-flex-align:center;align-items:center}.input-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.item-label-stacked),:host(.item-label-floating){-ms-flex-align:start;align-items:start}:host(.item-label-stacked) .input-wrapper,:host(.item-label-floating) .input-wrapper{-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column}.item-highlight,.item-inner-highlight{left:0;right:0;top:0;bottom:0;border-radius:inherit;position:absolute;width:100%;height:100%;-webkit-transform:scaleX(0);transform:scaleX(0);-webkit-transition:border-bottom-width 200ms, -webkit-transform 200ms;transition:border-bottom-width 200ms, -webkit-transform 200ms;transition:transform 200ms, border-bottom-width 200ms;transition:transform 200ms, border-bottom-width 200ms, -webkit-transform 200ms;z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.item-interactive.ion-focused),:host(.item-interactive.item-has-focus),:host(.item-interactive.ion-touched.ion-invalid){--full-highlight-height:calc(var(--highlight-height) * var(--show-full-highlight));--inset-highlight-height:calc(var(--highlight-height) * var(--show-inset-highlight))}:host(.ion-focused) .item-highlight,:host(.ion-focused) .item-inner-highlight,:host(.item-has-focus) .item-highlight,:host(.item-has-focus) .item-inner-highlight{-webkit-transform:scaleX(1);transform:scaleX(1);border-style:var(--border-style);border-color:var(--highlight-background)}:host(.ion-focused) .item-highlight,:host(.item-has-focus) .item-highlight{border-width:var(--full-highlight-height);opacity:var(--show-full-highlight)}:host(.ion-focused) .item-inner-highlight,:host(.item-has-focus) .item-inner-highlight{border-bottom-width:var(--inset-highlight-height);opacity:var(--show-inset-highlight)}:host(.ion-focused.item-fill-solid) .item-highlight,:host(.item-has-focus.item-fill-solid) .item-highlight{border-width:calc(var(--full-highlight-height) - 1px)}:host(.ion-focused) .item-inner-highlight,:host(.ion-focused:not(.item-fill-outline)) .item-highlight,:host(.item-has-focus) .item-inner-highlight,:host(.item-has-focus:not(.item-fill-outline)) .item-highlight{border-top:none;border-right:none;border-left:none}:host(.item-interactive.ion-focused),:host(.item-interactive.item-has-focus){--highlight-background:var(--highlight-color-focused)}:host(.item-interactive.ion-valid){--highlight-background:var(--highlight-color-valid)}:host(.item-interactive.ion-invalid){--highlight-background:var(--highlight-color-invalid)}:host(.item-interactive.ion-invalid) ::slotted([slot=helper]){display:none}::slotted([slot=error]){display:none;color:var(--highlight-color-invalid)}:host(.item-interactive.ion-invalid) ::slotted([slot=error]){display:block}:host(:not(.item-label)) ::slotted(ion-select.legacy-select){--padding-start:0;max-width:none}:host(.item-label-stacked) ::slotted(ion-select.legacy-select),:host(.item-label-floating) ::slotted(ion-select.legacy-select){--padding-top:8px;--padding-bottom:8px;--padding-start:0;-ms-flex-item-align:stretch;align-self:stretch;width:100%;max-width:100%}:host(:not(.item-label)) ::slotted(ion-datetime){--padding-start:0}:host(.item-label-stacked) ::slotted(ion-datetime),:host(.item-label-floating) ::slotted(ion-datetime){--padding-start:0;width:100%}:host(.item-multiple-inputs) ::slotted(ion-checkbox),:host(.item-multiple-inputs) ::slotted(ion-datetime),:host(.item-multiple-inputs) ::slotted(ion-radio),:host(.item-multiple-inputs) ::slotted(ion-select.legacy-select){position:relative}:host(.item-textarea){-ms-flex-align:stretch;align-items:stretch}::slotted(ion-reorder[slot]){margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}:host(.item-fill-solid) ::slotted([slot=start]),:host(.item-fill-solid) ::slotted([slot=end]),:host(.item-fill-outline) ::slotted([slot=start]),:host(.item-fill-outline) ::slotted([slot=end]){-ms-flex-item-align:center;align-self:center}::slotted([slot=helper]),::slotted([slot=error]),.item-counter{padding-top:5px;font-size:0.75rem;z-index:1}.item-counter{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, #737373);white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}@media (prefers-reduced-motion: reduce){.item-highlight,.item-inner-highlight{-webkit-transition:none;transition:none}}:host{--min-height:44px;--transition:background-color 200ms linear, opacity 200ms linear;--padding-start:16px;--inner-padding-end:16px;--inner-border-width:0px 0px 0.55px 0px;--background:var(--ion-item-background, var(--ion-background-color, #fff));--background-activated:var(--ion-text-color, #000);--background-focused:var(--ion-text-color, #000);--background-hover:currentColor;--background-activated-opacity:.12;--background-focused-opacity:.15;--background-hover-opacity:.04;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));--color:var(--ion-item-color, var(--ion-text-color, #000));--highlight-height:0px;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--bottom-padding-start:0px;font-size:1rem}:host(.ion-activated){--transition:none}:host(.ion-color.ion-focused) .item-native::after{background:#000;opacity:0.15}:host(.ion-color.ion-activated) .item-native::after{background:#000;opacity:0.12}:host(.item-interactive){--show-full-highlight:0;--show-inset-highlight:1}:host(.item-lines-full){--border-width:0px 0px 0.55px 0px;--show-full-highlight:1;--show-inset-highlight:0}:host(.item-lines-inset){--inner-border-width:0px 0px 0.55px 0px;--show-full-highlight:0;--show-inset-highlight:1}:host(.item-lines-inset),:host(.item-lines-none){--border-width:0px;--show-full-highlight:0}:host(.item-lines-full),:host(.item-lines-none){--inner-border-width:0px;--show-inset-highlight:0}.item-highlight,.item-inner-highlight{-webkit-transition:none;transition:none}:host(.item-has-focus) .item-inner-highlight,:host(.item-has-focus) .item-highlight{border-top:none;border-right:none;border-left:none}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:2px;margin-bottom:2px}::slotted(ion-icon[slot=start]),::slotted(ion-icon[slot=end]){margin-top:7px;margin-bottom:7px}::slotted(ion-toggle[slot=start]),::slotted(ion-toggle[slot=end]){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host(.item-label-stacked) ::slotted([slot=end]),:host(.item-label-floating) ::slotted([slot=end]){margin-top:7px;margin-bottom:7px}::slotted(.button-small){--padding-top:1px;--padding-bottom:1px;--padding-start:.5em;--padding-end:.5em;min-height:24px;font-size:0.8125rem}::slotted(ion-avatar){width:36px;height:36px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:8px;margin-bottom:8px}:host(.item-radio) ::slotted(ion-label),:host(.item-toggle) ::slotted(ion-label){-webkit-margin-start:0px;margin-inline-start:0px}::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:10px;margin-bottom:10px}:host(.item-label-floating),:host(.item-label-stacked){--min-height:68px}:host(.item-label-stacked) ::slotted(ion-select.legacy-select),:host(.item-label-floating) ::slotted(ion-select.legacy-select){--padding-top:8px;--padding-bottom:8px;--padding-start:0px}:host(.item-label-fixed) ::slotted(ion-select.legacy-select),:host(.item-label-fixed) ::slotted(ion-datetime){--padding-start:0}\";\nconst IonItemIosStyle0 = itemIosCss;\nconst itemMdCss = \":host{--border-radius:0px;--border-width:0px;--border-style:solid;--padding-top:0px;--padding-bottom:0px;--padding-end:0px;--padding-start:0px;--inner-border-width:0px;--inner-padding-top:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;--inner-padding-end:0px;--inner-box-shadow:none;--show-full-highlight:0;--show-inset-highlight:0;--detail-icon-color:initial;--detail-icon-font-size:1.25em;--detail-icon-opacity:0.25;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:initial;text-decoration:none;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color:not(.item-fill-solid):not(.item-fill-outline)) .item-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.ion-color:not(.item-fill-solid):not(.item-fill-outline)) .item-native,:host(.ion-color:not(.item-fill-solid):not(.item-fill-outline)) .item-inner{border-color:var(--ion-color-shade)}:host(.ion-activated) .item-native{color:var(--color-activated)}:host(.ion-activated) .item-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.ion-color.ion-activated) .item-native{color:var(--ion-color-contrast)}:host(.ion-focused) .item-native{color:var(--color-focused)}:host(.ion-focused) .item-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-color.ion-focused) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .item-native::after{background:var(--ion-color-contrast)}@media (any-hover: hover){:host(.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--color-hover)}:host(.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--ion-color-contrast)}}:host(.item-has-interactive-control){cursor:pointer}:host(.item-interactive-disabled:not(.item-multiple-inputs)){cursor:default;pointer-events:none}:host(.item-disabled){cursor:default;opacity:0.3;pointer-events:none}.item-native{border-radius:var(--border-radius);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;min-height:var(--min-height);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);overflow:inherit;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}[dir=rtl] .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){.item-native:dir(rtl){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}.item-native::-moz-focus-inner{border:0}.item-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:var(--transition);transition:var(--transition);z-index:-1}button,a{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-user-drag:none}.item-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);-webkit-box-shadow:var(--inner-box-shadow);box-shadow:var(--inner-box-shadow);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-bottom{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:0;padding-bottom:0;padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--inner-padding-end) + var(--ion-safe-area-right, 0px));display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host-context([dir=rtl]) .item-bottom{padding-left:calc(var(--inner-padding-end) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px))}[dir=rtl] .item-bottom{padding-left:calc(var(--inner-padding-end) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px))}@supports selector(:dir(rtl)){.item-bottom:dir(rtl){padding-left:calc(var(--inner-padding-end) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px))}}.item-detail-icon{-webkit-margin-start:calc(var(--inner-padding-end) / 2);margin-inline-start:calc(var(--inner-padding-end) / 2);-webkit-margin-end:-6px;margin-inline-end:-6px;color:var(--detail-icon-color);font-size:var(--detail-icon-font-size);opacity:var(--detail-icon-opacity)}::slotted(ion-icon){font-size:1.6em}::slotted(ion-button){--margin-top:0;--margin-bottom:0;--margin-start:0;--margin-end:0;z-index:1}::slotted(ion-label:not([slot=end])){-ms-flex:1;flex:1;width:-webkit-min-content;width:-moz-min-content;width:min-content;max-width:100%}:host(.item-input){-ms-flex-align:center;align-items:center}.input-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.item-label-stacked),:host(.item-label-floating){-ms-flex-align:start;align-items:start}:host(.item-label-stacked) .input-wrapper,:host(.item-label-floating) .input-wrapper{-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column}.item-highlight,.item-inner-highlight{left:0;right:0;top:0;bottom:0;border-radius:inherit;position:absolute;width:100%;height:100%;-webkit-transform:scaleX(0);transform:scaleX(0);-webkit-transition:border-bottom-width 200ms, -webkit-transform 200ms;transition:border-bottom-width 200ms, -webkit-transform 200ms;transition:transform 200ms, border-bottom-width 200ms;transition:transform 200ms, border-bottom-width 200ms, -webkit-transform 200ms;z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.item-interactive.ion-focused),:host(.item-interactive.item-has-focus),:host(.item-interactive.ion-touched.ion-invalid){--full-highlight-height:calc(var(--highlight-height) * var(--show-full-highlight));--inset-highlight-height:calc(var(--highlight-height) * var(--show-inset-highlight))}:host(.ion-focused) .item-highlight,:host(.ion-focused) .item-inner-highlight,:host(.item-has-focus) .item-highlight,:host(.item-has-focus) .item-inner-highlight{-webkit-transform:scaleX(1);transform:scaleX(1);border-style:var(--border-style);border-color:var(--highlight-background)}:host(.ion-focused) .item-highlight,:host(.item-has-focus) .item-highlight{border-width:var(--full-highlight-height);opacity:var(--show-full-highlight)}:host(.ion-focused) .item-inner-highlight,:host(.item-has-focus) .item-inner-highlight{border-bottom-width:var(--inset-highlight-height);opacity:var(--show-inset-highlight)}:host(.ion-focused.item-fill-solid) .item-highlight,:host(.item-has-focus.item-fill-solid) .item-highlight{border-width:calc(var(--full-highlight-height) - 1px)}:host(.ion-focused) .item-inner-highlight,:host(.ion-focused:not(.item-fill-outline)) .item-highlight,:host(.item-has-focus) .item-inner-highlight,:host(.item-has-focus:not(.item-fill-outline)) .item-highlight{border-top:none;border-right:none;border-left:none}:host(.item-interactive.ion-focused),:host(.item-interactive.item-has-focus){--highlight-background:var(--highlight-color-focused)}:host(.item-interactive.ion-valid){--highlight-background:var(--highlight-color-valid)}:host(.item-interactive.ion-invalid){--highlight-background:var(--highlight-color-invalid)}:host(.item-interactive.ion-invalid) ::slotted([slot=helper]){display:none}::slotted([slot=error]){display:none;color:var(--highlight-color-invalid)}:host(.item-interactive.ion-invalid) ::slotted([slot=error]){display:block}:host(:not(.item-label)) ::slotted(ion-select.legacy-select){--padding-start:0;max-width:none}:host(.item-label-stacked) ::slotted(ion-select.legacy-select),:host(.item-label-floating) ::slotted(ion-select.legacy-select){--padding-top:8px;--padding-bottom:8px;--padding-start:0;-ms-flex-item-align:stretch;align-self:stretch;width:100%;max-width:100%}:host(:not(.item-label)) ::slotted(ion-datetime){--padding-start:0}:host(.item-label-stacked) ::slotted(ion-datetime),:host(.item-label-floating) ::slotted(ion-datetime){--padding-start:0;width:100%}:host(.item-multiple-inputs) ::slotted(ion-checkbox),:host(.item-multiple-inputs) ::slotted(ion-datetime),:host(.item-multiple-inputs) ::slotted(ion-radio),:host(.item-multiple-inputs) ::slotted(ion-select.legacy-select){position:relative}:host(.item-textarea){-ms-flex-align:stretch;align-items:stretch}::slotted(ion-reorder[slot]){margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}:host(.item-fill-solid) ::slotted([slot=start]),:host(.item-fill-solid) ::slotted([slot=end]),:host(.item-fill-outline) ::slotted([slot=start]),:host(.item-fill-outline) ::slotted([slot=end]){-ms-flex-item-align:center;align-self:center}::slotted([slot=helper]),::slotted([slot=error]),.item-counter{padding-top:5px;font-size:0.75rem;z-index:1}.item-counter{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, #737373);white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}@media (prefers-reduced-motion: reduce){.item-highlight,.item-inner-highlight{-webkit-transition:none;transition:none}}:host{--min-height:48px;--background:var(--ion-item-background, var(--ion-background-color, #fff));--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));--color:var(--ion-item-color, var(--ion-text-color, #000));--transition:opacity 15ms linear, background-color 15ms linear;--padding-start:16px;--inner-padding-end:16px;--inner-border-width:0 0 1px 0;--highlight-height:1px;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);font-size:1rem;font-weight:normal;text-transform:none}:host(.item-fill-outline){--highlight-height:2px}:host(.item-fill-none.item-interactive.ion-focus) .item-highlight,:host(.item-fill-none.item-interactive.item-has-focus) .item-highlight,:host(.item-fill-none.item-interactive.ion-touched.ion-invalid) .item-highlight{-webkit-transform:scaleX(1);transform:scaleX(1);border-width:0 0 var(--full-highlight-height) 0;border-style:var(--border-style);border-color:var(--highlight-background)}:host(.item-fill-none.item-interactive.ion-focus) .item-native,:host(.item-fill-none.item-interactive.item-has-focus) .item-native,:host(.item-fill-none.item-interactive.ion-touched.ion-invalid) .item-native{border-bottom-color:var(--highlight-background)}:host(.item-fill-outline.item-interactive.ion-focus) .item-highlight,:host(.item-fill-outline.item-interactive.item-has-focus) .item-highlight{-webkit-transform:scaleX(1);transform:scaleX(1)}:host(.item-fill-outline.item-interactive.ion-focus) .item-highlight,:host(.item-fill-outline.item-interactive.item-has-focus) .item-highlight,:host(.item-fill-outline.item-interactive.ion-touched.ion-invalid) .item-highlight{border-width:var(--full-highlight-height);border-style:var(--border-style);border-color:var(--highlight-background)}:host(.item-fill-outline.item-interactive.ion-touched.ion-invalid) .item-native{border-color:var(--highlight-background)}:host(.item-fill-solid.item-interactive.ion-focus) .item-highlight,:host(.item-fill-solid.item-interactive.item-has-focus) .item-highlight,:host(.item-fill-solid.item-interactive.ion-touched.ion-invalid) .item-highlight{-webkit-transform:scaleX(1);transform:scaleX(1);border-width:0 0 var(--full-highlight-height) 0;border-style:var(--border-style);border-color:var(--highlight-background)}:host(.item-fill-solid.item-interactive.ion-focus) .item-native,:host(.item-fill-solid.item-interactive.item-has-focus) .item-native,:host(.item-fill-solid.item-interactive.ion-touched.ion-invalid) .item-native{border-bottom-color:var(--highlight-background)}:host(.ion-color.ion-activated) .item-native::after{background:transparent}:host(.item-has-focus) .item-native{caret-color:var(--highlight-background)}:host(.item-interactive){--border-width:0 0 1px 0;--inner-border-width:0;--show-full-highlight:1;--show-inset-highlight:0}:host(.item-lines-full){--border-width:0 0 1px 0;--show-full-highlight:1;--show-inset-highlight:0}:host(.item-lines-inset){--inner-border-width:0 0 1px 0;--show-full-highlight:0;--show-inset-highlight:1}:host(.item-lines-inset),:host(.item-lines-none){--border-width:0;--show-full-highlight:0}:host(.item-lines-full),:host(.item-lines-none){--inner-border-width:0;--show-inset-highlight:0}:host(.item-fill-outline) .item-highlight{--position-offset:calc(-1 * var(--border-width));top:var(--position-offset);width:calc(100% + 2 * var(--border-width));height:calc(100% + 2 * var(--border-width));-webkit-transition:none;transition:none}@supports (inset-inline-start: 0){:host(.item-fill-outline) .item-highlight{inset-inline-start:var(--position-offset)}}@supports not (inset-inline-start: 0){:host(.item-fill-outline) .item-highlight{left:var(--position-offset)}:host-context([dir=rtl]):host(.item-fill-outline) .item-highlight,:host-context([dir=rtl]).item-fill-outline .item-highlight{left:unset;right:unset;right:var(--position-offset)}@supports selector(:dir(rtl)){:host(.item-fill-outline:dir(rtl)) .item-highlight{left:unset;right:unset;right:var(--position-offset)}}}:host(.item-fill-outline.ion-focused) .item-native,:host(.item-fill-outline.item-has-focus) .item-native{border-color:transparent}:host(.item-multi-line) ::slotted([slot=start]),:host(.item-multi-line) ::slotted([slot=end]){margin-top:16px;margin-bottom:16px;-ms-flex-item-align:start;align-self:flex-start}::slotted([slot=start]){-webkit-margin-end:32px;margin-inline-end:32px}::slotted([slot=end]){-webkit-margin-start:32px;margin-inline-start:32px}:host(.item-fill-solid) ::slotted([slot=start]),:host(.item-fill-solid) ::slotted([slot=end]),:host(.item-fill-outline) ::slotted([slot=start]),:host(.item-fill-outline) ::slotted([slot=end]){-ms-flex-item-align:center;align-self:center}::slotted(ion-icon){color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);font-size:1.5em}:host(.ion-color:not(.item-fill-solid):not(.item-fill-outline)) ::slotted(ion-icon){color:var(--ion-color-contrast)}::slotted(ion-icon[slot]){margin-top:12px;margin-bottom:12px}::slotted(ion-icon[slot=start]){-webkit-margin-end:32px;margin-inline-end:32px}::slotted(ion-icon[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}:host(.item-fill-solid) ::slotted(ion-icon[slot=start]),:host(.item-fill-outline) ::slotted(ion-icon[slot=start]){-webkit-margin-end:8px;margin-inline-end:8px}::slotted(ion-toggle[slot=start]),::slotted(ion-toggle[slot=end]){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}::slotted(ion-note){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-ms-flex-item-align:start;align-self:flex-start;font-size:0.6875rem}::slotted(ion-note[slot]:not([slot=helper]):not([slot=error])){padding-left:0;padding-right:0;padding-top:18px;padding-bottom:10px}::slotted(ion-note[slot=start]){-webkit-padding-end:16px;padding-inline-end:16px}::slotted(ion-note[slot=end]){-webkit-padding-start:16px;padding-inline-start:16px}::slotted(ion-avatar){width:40px;height:40px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar),::slotted(ion-thumbnail){margin-top:8px;margin-bottom:8px}::slotted(ion-avatar[slot=start]),::slotted(ion-thumbnail[slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:10px;margin-bottom:10px}:host(.item-label-stacked) ::slotted([slot=end]),:host(.item-label-floating) ::slotted([slot=end]){margin-top:7px;margin-bottom:7px}:host(.item-label-fixed) ::slotted(ion-select.legacy-select),:host(.item-label-fixed) ::slotted(ion-datetime){--padding-start:8px}:host(.item-toggle) ::slotted(ion-label),:host(.item-radio) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0}::slotted(.button-small){--padding-top:2px;--padding-bottom:2px;--padding-start:.6em;--padding-end:.6em;min-height:25px;font-size:0.75rem}:host(.item-label-floating),:host(.item-label-stacked){--min-height:55px}:host(.item-label-stacked) ::slotted(ion-select.legacy-select),:host(.item-label-floating) ::slotted(ion-select.legacy-select){--padding-top:8px;--padding-bottom:8px;--padding-start:0}:host(.ion-focused:not(.ion-color)) ::slotted(.label-stacked),:host(.ion-focused:not(.ion-color)) ::slotted(.label-floating),:host(.item-has-focus:not(.ion-color)) ::slotted(.label-stacked),:host(.item-has-focus:not(.ion-color)) ::slotted(.label-floating){color:var(--ion-color-primary, #3880ff)}:host(.ion-color){--highlight-color-focused:var(--ion-color-contrast)}:host(.item-label-color){--highlight-color-focused:var(--ion-color-base)}:host(.item-fill-solid.ion-color),:host(.item-fill-outline.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.item-fill-solid){--background:var(--ion-color-step-50, #f2f2f2);--background-hover:var(--ion-color-step-100, #e6e6e6);--background-focused:var(--ion-color-step-150, #d9d9d9);--border-width:0 0 1px 0;--inner-border-width:0;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}:host-context([dir=rtl]):host(.item-fill-solid),:host-context([dir=rtl]).item-fill-solid{border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}@supports selector(:dir(rtl)){:host(.item-fill-solid:dir(rtl)){border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}}:host(.item-fill-solid) .item-native{--border-color:var(--ion-color-step-500, gray)}:host(.item-fill-solid.ion-focused) .item-native,:host(.item-fill-solid.item-has-focus) .item-native{--background:var(--background-focused)}:host(.item-fill-solid.item-shape-round){border-top-left-radius:16px;border-top-right-radius:16px;border-bottom-right-radius:0;border-bottom-left-radius:0}:host-context([dir=rtl]):host(.item-fill-solid.item-shape-round),:host-context([dir=rtl]).item-fill-solid.item-shape-round{border-top-left-radius:16px;border-top-right-radius:16px;border-bottom-right-radius:0;border-bottom-left-radius:0}@supports selector(:dir(rtl)){:host(.item-fill-solid.item-shape-round:dir(rtl)){border-top-left-radius:16px;border-top-right-radius:16px;border-bottom-right-radius:0;border-bottom-left-radius:0}}@media (any-hover: hover){:host(.item-fill-solid:hover) .item-native{--background:var(--background-hover);--border-color:var(--ion-color-step-750, #404040)}}:host(.item-fill-outline){--ripple-color:transparent;--background-focused:transparent;--background-hover:transparent;--border-color:var(--ion-color-step-500, gray);--border-width:1px;border:none;overflow:visible}:host(.item-fill-outline) .item-native{--native-padding-left:16px;border-radius:4px}:host(.item-fill-outline.item-shape-round) .item-native{--inner-padding-start:16px;border-radius:28px}:host(.item-fill-outline.item-shape-round) .item-bottom{-webkit-padding-start:32px;padding-inline-start:32px}:host(.item-fill-outline.item-label-floating.ion-focused) .item-native ::slotted(ion-input:not(:first-child)),:host(.item-fill-outline.item-label-floating.ion-focused) .item-native ::slotted(ion-textarea:not(:first-child)),:host(.item-fill-outline.item-label-floating.item-has-focus) .item-native ::slotted(ion-input:not(:first-child)),:host(.item-fill-outline.item-label-floating.item-has-focus) .item-native ::slotted(ion-textarea:not(:first-child)),:host(.item-fill-outline.item-label-floating.item-has-value) .item-native ::slotted(ion-input:not(:first-child)),:host(.item-fill-outline.item-label-floating.item-has-value) .item-native ::slotted(ion-textarea:not(:first-child)){-webkit-transform:translateY(-14px);transform:translateY(-14px)}@media (any-hover: hover){:host(.item-fill-outline:hover) .item-native{--border-color:var(--ion-color-step-750, #404040)}}.item-counter{letter-spacing:0.0333333333em}\";\nconst IonItemMdStyle0 = itemMdCss;\nconst Item = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.labelColorStyles = {};\n    this.itemStyles = new Map();\n    this.inheritedAriaAttributes = {};\n    this.multipleInputs = false;\n    this.focusable = true;\n    this.color = undefined;\n    this.button = false;\n    this.detail = undefined;\n    this.detailIcon = chevronForward;\n    this.disabled = false;\n    this.download = undefined;\n    this.fill = undefined;\n    this.shape = undefined;\n    this.href = undefined;\n    this.rel = undefined;\n    this.lines = undefined;\n    this.counter = false;\n    this.routerAnimation = undefined;\n    this.routerDirection = 'forward';\n    this.target = undefined;\n    this.type = 'button';\n    this.counterFormatter = undefined;\n    this.counterString = undefined;\n  }\n  buttonChanged() {\n    // Update the focusable option when the button option is changed\n    this.focusable = this.isFocusable();\n  }\n  counterFormatterChanged() {\n    this.updateCounterOutput(this.getFirstInput());\n  }\n  handleIonInput(ev) {\n    if (this.counter && ev.target === this.getFirstInput()) {\n      this.updateCounterOutput(ev.target);\n    }\n  }\n  labelColorChanged(ev) {\n    const {\n      color\n    } = this;\n    // There will be a conflict with item color if\n    // we apply the label color to item, so we ignore\n    // the label color if the user sets a color on item\n    if (color === undefined) {\n      this.labelColorStyles = ev.detail;\n    }\n  }\n  itemStyle(ev) {\n    ev.stopPropagation();\n    const tagName = ev.target.tagName;\n    const updatedStyles = ev.detail;\n    const newStyles = {};\n    const childStyles = this.itemStyles.get(tagName) || {};\n    let hasStyleChange = false;\n    Object.keys(updatedStyles).forEach(key => {\n      if (updatedStyles[key]) {\n        const itemKey = `item-${key}`;\n        if (!childStyles[itemKey]) {\n          hasStyleChange = true;\n        }\n        newStyles[itemKey] = true;\n      }\n    });\n    if (!hasStyleChange && Object.keys(newStyles).length !== Object.keys(childStyles).length) {\n      hasStyleChange = true;\n    }\n    if (hasStyleChange) {\n      this.itemStyles.set(tagName, newStyles);\n      forceUpdate(this);\n    }\n  }\n  connectedCallback() {\n    if (this.counter) {\n      this.updateCounterOutput(this.getFirstInput());\n    }\n    this.hasStartEl();\n  }\n  componentWillLoad() {\n    this.inheritedAriaAttributes = inheritAttributes(this.el, ['aria-label']);\n  }\n  componentDidLoad() {\n    const {\n      el,\n      counter,\n      counterFormatter,\n      fill,\n      shape\n    } = this;\n    const hasHelperSlot = el.querySelector('[slot=\"helper\"]') !== null;\n    if (hasHelperSlot) {\n      printIonWarning('The \"helper\" slot has been deprecated in favor of using the \"helperText\" property on ion-input or ion-textarea.', el);\n    }\n    const hasErrorSlot = el.querySelector('[slot=\"error\"]') !== null;\n    if (hasErrorSlot) {\n      printIonWarning('The \"error\" slot has been deprecated in favor of using the \"errorText\" property on ion-input or ion-textarea.', el);\n    }\n    if (counter === true) {\n      printIonWarning('The \"counter\" property has been deprecated in favor of using the \"counter\" property on ion-input or ion-textarea.', el);\n    }\n    if (counterFormatter !== undefined) {\n      printIonWarning('The \"counterFormatter\" property has been deprecated in favor of using the \"counterFormatter\" property on ion-input or ion-textarea.', el);\n    }\n    if (fill !== undefined) {\n      printIonWarning('The \"fill\" property has been deprecated in favor of using the \"fill\" property on ion-input or ion-textarea.', el);\n    }\n    if (shape !== undefined) {\n      printIonWarning('The \"shape\" property has been deprecated in favor of using the \"shape\" property on ion-input or ion-textarea.', el);\n    }\n    raf(() => {\n      this.setMultipleInputs();\n      this.focusable = this.isFocusable();\n    });\n  }\n  // If the item contains multiple clickable elements and/or inputs, then the item\n  // should not have a clickable input cover over the entire item to prevent\n  // interfering with their individual click events\n  setMultipleInputs() {\n    // The following elements have a clickable cover that is relative to the entire item\n    const covers = this.el.querySelectorAll('ion-checkbox, ion-datetime, ion-select, ion-radio');\n    // The following elements can accept focus alongside the previous elements\n    // therefore if these elements are also a child of item, we don't want the\n    // input cover on top of those interfering with their clicks\n    const inputs = this.el.querySelectorAll('ion-input, ion-range, ion-searchbar, ion-segment, ion-textarea, ion-toggle');\n    // The following elements should also stay clickable when an input with cover is present\n    const clickables = this.el.querySelectorAll('ion-anchor, ion-button, a, button');\n    // Check for multiple inputs to change the position of the input cover to relative\n    // for all of the covered inputs above\n    this.multipleInputs = covers.length + inputs.length > 1 || covers.length + clickables.length > 1 || covers.length > 0 && this.isClickable();\n  }\n  // If the item contains an input including a checkbox, datetime, select, or radio\n  // then the item will have a clickable input cover that covers the item\n  // that should get the hover, focused and activated states UNLESS it has multiple\n  // inputs, then those need to individually get each click\n  hasCover() {\n    const inputs = this.el.querySelectorAll('ion-checkbox, ion-datetime, ion-select, ion-radio');\n    return inputs.length === 1 && !this.multipleInputs;\n  }\n  // If the item has an href or button property it will render a native\n  // anchor or button that is clickable\n  isClickable() {\n    return this.href !== undefined || this.button;\n  }\n  canActivate() {\n    return this.isClickable() || this.hasCover();\n  }\n  isFocusable() {\n    const focusableChild = this.el.querySelector('.ion-focusable');\n    return this.canActivate() || focusableChild !== null;\n  }\n  getFirstInput() {\n    const inputs = this.el.querySelectorAll('ion-input, ion-textarea');\n    return inputs[0];\n  }\n  updateCounterOutput(inputEl) {\n    var _a, _b;\n    const {\n      counter,\n      counterFormatter,\n      defaultCounterFormatter\n    } = this;\n    if (counter && !this.multipleInputs && (inputEl === null || inputEl === void 0 ? void 0 : inputEl.maxlength) !== undefined) {\n      const length = (_b = (_a = inputEl === null || inputEl === void 0 ? void 0 : inputEl.value) === null || _a === void 0 ? void 0 : _a.toString().length) !== null && _b !== void 0 ? _b : 0;\n      if (counterFormatter === undefined) {\n        this.counterString = defaultCounterFormatter(length, inputEl.maxlength);\n      } else {\n        try {\n          this.counterString = counterFormatter(length, inputEl.maxlength);\n        } catch (e) {\n          printIonError('Exception in provided `counterFormatter`.', e);\n          // Fallback to the default counter formatter when an exception happens\n          this.counterString = defaultCounterFormatter(length, inputEl.maxlength);\n        }\n      }\n    }\n  }\n  defaultCounterFormatter(length, maxlength) {\n    return `${length} / ${maxlength}`;\n  }\n  hasStartEl() {\n    const startEl = this.el.querySelector('[slot=\"start\"]');\n    if (startEl !== null) {\n      this.el.classList.add('item-has-start-slot');\n    }\n  }\n  getFirstInteractive() {\n    const controls = this.el.querySelectorAll('ion-toggle:not([disabled]), ion-checkbox:not([disabled]), ion-radio:not([disabled]), ion-select:not([disabled])');\n    return controls[0];\n  }\n  render() {\n    const {\n      counterString,\n      detail,\n      detailIcon,\n      download,\n      fill,\n      labelColorStyles,\n      lines,\n      disabled,\n      href,\n      rel,\n      shape,\n      target,\n      routerAnimation,\n      routerDirection,\n      inheritedAriaAttributes,\n      multipleInputs\n    } = this;\n    const childStyles = {};\n    const mode = getIonMode(this);\n    const clickable = this.isClickable();\n    const canActivate = this.canActivate();\n    const TagType = clickable ? href === undefined ? 'button' : 'a' : 'div';\n    const attrs = TagType === 'button' ? {\n      type: this.type\n    } : {\n      download,\n      href,\n      rel,\n      target\n    };\n    let clickFn = {};\n    const firstInteractive = this.getFirstInteractive();\n    // Only set onClick if the item is clickable to prevent screen\n    // readers from reading all items as clickable\n    if (clickable || firstInteractive !== undefined && !multipleInputs) {\n      clickFn = {\n        onClick: ev => {\n          if (clickable) {\n            openURL(href, ev, routerDirection, routerAnimation);\n          }\n          if (firstInteractive !== undefined && !multipleInputs) {\n            const path = ev.composedPath();\n            const target = path[0];\n            if (ev.isTrusted) {\n              /**\n               * Dispatches a click event to the first interactive element,\n               * when it is the result of a user clicking on the item.\n               *\n               * We check if the click target is in the shadow root,\n               * which means the user clicked on the .item-native or\n               * .item-inner padding.\n               */\n              const clickedWithinShadowRoot = this.el.shadowRoot.contains(target);\n              if (clickedWithinShadowRoot) {\n                firstInteractive.click();\n              }\n            }\n          }\n        }\n      };\n    }\n    const showDetail = detail !== undefined ? detail : mode === 'ios' && clickable;\n    this.itemStyles.forEach(value => {\n      Object.assign(childStyles, value);\n    });\n    const ariaDisabled = disabled || childStyles['item-interactive-disabled'] ? 'true' : null;\n    const fillValue = fill || 'none';\n    const inList = hostContext('ion-list', this.el) && !hostContext('ion-radio-group', this.el);\n    return h(Host, {\n      key: '077c9ab04985292f79c30691151d2778a26285fb',\n      \"aria-disabled\": ariaDisabled,\n      class: Object.assign(Object.assign(Object.assign({}, childStyles), labelColorStyles), createColorClasses(this.color, {\n        item: true,\n        [mode]: true,\n        'item-lines-default': lines === undefined,\n        [`item-lines-${lines}`]: lines !== undefined,\n        [`item-fill-${fillValue}`]: true,\n        [`item-shape-${shape}`]: shape !== undefined,\n        'item-has-interactive-control': firstInteractive !== undefined,\n        'item-disabled': disabled,\n        'in-list': inList,\n        'item-multiple-inputs': this.multipleInputs,\n        'ion-activatable': canActivate,\n        'ion-focusable': this.focusable,\n        'item-rtl': document.dir === 'rtl'\n      })),\n      role: inList ? 'listitem' : null\n    }, h(TagType, Object.assign({\n      key: '08bb75c85d0584e9fb7f461e9e06b9b651ef3515'\n    }, attrs, inheritedAriaAttributes, {\n      class: \"item-native\",\n      part: \"native\",\n      disabled: disabled\n    }, clickFn), h(\"slot\", {\n      key: '3f742b07c4a58eadedf675be4878a3e00070750e',\n      name: \"start\"\n    }), h(\"div\", {\n      key: '0160b8963434f3107856a2df2fb96f1abb4fdbfe',\n      class: \"item-inner\"\n    }, h(\"div\", {\n      key: '9439b21474443341f06a651d7c44aef90bd4c591',\n      class: \"input-wrapper\"\n    }, h(\"slot\", {\n      key: '8034e1337a6c950b7725bc9aa46d55de980827bf'\n    })), h(\"slot\", {\n      key: '690de6d3dd3a238ab7a2d3dfea69daa6647e5134',\n      name: \"end\"\n    }), showDetail && h(\"ion-icon\", {\n      key: '6f386abbde5897fca7aabc850645986e16315213',\n      icon: detailIcon,\n      lazy: false,\n      class: \"item-detail-icon\",\n      part: \"detail-icon\",\n      \"aria-hidden\": \"true\",\n      \"flip-rtl\": detailIcon === chevronForward\n    }), h(\"div\", {\n      key: '4bb8444a5097c032d79f2083f60429b9057e3a5c',\n      class: \"item-inner-highlight\"\n    })), canActivate && mode === 'md' && h(\"ion-ripple-effect\", {\n      key: '31c319e2335efbb92d7dea110a66f347764f2b44'\n    }), h(\"div\", {\n      key: '0fc128b44582af7c8b542c9e346ef88e40fea148',\n      class: \"item-highlight\"\n    })), h(\"div\", {\n      key: '7b84c0a74d148a7fa6af449b6bd06bb45530edf5',\n      class: \"item-bottom\"\n    }, h(\"slot\", {\n      key: '97563a615efd81b21014c41a25989c71ba3ae2cd',\n      name: \"error\"\n    }), h(\"slot\", {\n      key: 'd84a4bb21ed3f13af82f78104ffb06d2b079738e',\n      name: \"helper\"\n    }), counterString && h(\"ion-note\", {\n      key: '34eaa7798c50d2f88afca0dcb1278df05a1800e6',\n      class: \"item-counter\"\n    }, counterString)));\n  }\n  static get delegatesFocus() {\n    return true;\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"button\": [\"buttonChanged\"],\n      \"counterFormatter\": [\"counterFormatterChanged\"]\n    };\n  }\n};\nItem.style = {\n  ios: IonItemIosStyle0,\n  md: IonItemMdStyle0\n};\nconst itemDividerIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--inner-padding-top:0px;--inner-padding-end:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);overflow:hidden;z-index:100;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.item-divider-sticky){position:-webkit-sticky;position:sticky;top:0}.item-divider-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border:0;overflow:hidden}:host-context([dir=rtl]) .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-divider-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-divider-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:hidden}:host{--background:var(--ion-color-step-100, #e6e6e6);--color:var(--ion-color-step-850, #262626);--padding-start:16px;--inner-padding-end:8px;border-radius:0;position:relative;min-height:28px;font-size:1.0625rem;font-weight:600}:host([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:2px;margin-bottom:2px}::slotted(ion-icon[slot=start]),::slotted(ion-icon[slot=end]){margin-top:7px;margin-bottom:7px}::slotted(h1){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px}::slotted(h2){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px}::slotted(h3),::slotted(h4),::slotted(h5),::slotted(h6){margin-left:0;margin-right:0;margin-top:0;margin-bottom:3px}::slotted(p){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.4);font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}::slotted(h2:last-child) ::slotted(h3:last-child),::slotted(h4:last-child),::slotted(h5:last-child),::slotted(h6:last-child),::slotted(p:last-child){margin-bottom:0}\";\nconst IonItemDividerIosStyle0 = itemDividerIosCss;\nconst itemDividerMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--inner-padding-top:0px;--inner-padding-end:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);overflow:hidden;z-index:100;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.item-divider-sticky){position:-webkit-sticky;position:sticky;top:0}.item-divider-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border:0;overflow:hidden}:host-context([dir=rtl]) .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-divider-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-divider-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:hidden}:host{--background:var(--ion-background-color, #fff);--color:var(--ion-color-step-400, #999999);--padding-start:16px;--inner-padding-end:16px;min-height:30px;border-bottom:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));font-size:0.875rem}::slotted([slot=start]){-webkit-margin-end:32px;margin-inline-end:32px}::slotted([slot=end]){-webkit-margin-start:32px;margin-inline-start:32px}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:13px;margin-bottom:10px}::slotted(ion-icon){color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);font-size:1.7142857143em}:host(.ion-color) ::slotted(ion-icon){color:var(--ion-color-contrast)}::slotted(ion-icon[slot]){margin-top:12px;margin-bottom:12px}::slotted(ion-icon[slot=start]){-webkit-margin-end:32px;margin-inline-end:32px}::slotted(ion-icon[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-note){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-ms-flex-item-align:start;align-self:flex-start;font-size:0.6875rem}::slotted(ion-note[slot]){padding-left:0;padding-right:0;padding-top:18px;padding-bottom:10px}::slotted(ion-note[slot=start]){-webkit-padding-end:16px;padding-inline-end:16px}::slotted(ion-note[slot=end]){-webkit-padding-start:16px;padding-inline-start:16px}::slotted(ion-avatar){width:40px;height:40px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar),::slotted(ion-thumbnail){margin-top:8px;margin-bottom:8px}::slotted(ion-avatar[slot=start]),::slotted(ion-thumbnail[slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(h1){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px}::slotted(h2){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px}::slotted(h3,h4,h5,h6){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px}::slotted(p){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;color:var(--ion-color-step-600, #666666);font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}\";\nconst IonItemDividerMdStyle0 = itemDividerMdCss;\nconst ItemDivider = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.color = undefined;\n    this.sticky = false;\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '5727179159ef2a8879f55435265003e0ec72df3f',\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'item-divider-sticky': this.sticky,\n        item: true\n      })\n    }, h(\"slot\", {\n      key: 'bb7df137e60ca3fa9a50c612e30fbb3ee4c818ad',\n      name: \"start\"\n    }), h(\"div\", {\n      key: '6a25a01271957cfdd8e8dfb6ef76e1eb710380f2',\n      class: \"item-divider-inner\"\n    }, h(\"div\", {\n      key: '554ba681b0f346ed0af03232f8b2e6ca399877d9',\n      class: \"item-divider-wrapper\"\n    }, h(\"slot\", {\n      key: 'f98e20a01f09d0a2e19b7351eb1b4028881a07ab'\n    })), h(\"slot\", {\n      key: '755643b5b8d3463af41b3d0805871073a34386a3',\n      name: \"end\"\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nItemDivider.style = {\n  ios: IonItemDividerIosStyle0,\n  md: IonItemDividerMdStyle0\n};\nconst itemGroupIosCss = \"ion-item-group{display:block}\";\nconst IonItemGroupIosStyle0 = itemGroupIosCss;\nconst itemGroupMdCss = \"ion-item-group{display:block}\";\nconst IonItemGroupMdStyle0 = itemGroupMdCss;\nconst ItemGroup = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '5778fb7e9c6791874b4ff14f0babdae715e322e7',\n      role: \"group\",\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`item-group-${mode}`]: true,\n        item: true\n      }\n    });\n  }\n};\nItemGroup.style = {\n  ios: IonItemGroupIosStyle0,\n  md: IonItemGroupMdStyle0\n};\nconst labelIosCss = \".item.sc-ion-label-ios-h,.item .sc-ion-label-ios-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.item-legacy.sc-ion-label-ios-h,.item-legacy .sc-ion-label-ios-h{white-space:nowrap;overflow:hidden}.ion-color.sc-ion-label-ios-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-ios-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-ios-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-ios-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-ios-h,.item-input .sc-ion-label-ios-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-ios-h,.item-textarea .sc-ion-label-ios-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-ios-h,.item-skeleton-text .sc-ion-label-ios-h{overflow:hidden}.label-fixed.sc-ion-label-ios-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-ios-h,.label-floating.sc-ion-label-ios-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-ios-h{-webkit-transition:none;transition:none}.sc-ion-label-ios-s h1,.sc-ion-label-ios-s h2,.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-ios-h{font-size:0.875rem;line-height:1.5}.label-stacked.sc-ion-label-ios-h{margin-bottom:4px;font-size:0.875rem}.label-floating.sc-ion-label-ios-h{margin-bottom:0;-webkit-transform:translate(0, 29px);transform:translate(0, 29px);-webkit-transform-origin:left top;transform-origin:left top;-webkit-transition:-webkit-transform 150ms ease-in-out;transition:-webkit-transform 150ms ease-in-out;transition:transform 150ms ease-in-out;transition:transform 150ms ease-in-out, -webkit-transform 150ms ease-in-out}[dir=rtl].sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl] .sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl].label-floating.sc-ion-label-ios-h,[dir=rtl] .label-floating.sc-ion-label-ios-h{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.label-floating.sc-ion-label-ios-h:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.item-textarea.label-floating.sc-ion-label-ios-h,.item-textarea .label-floating.sc-ion-label-ios-h{-webkit-transform:translate(0, 28px);transform:translate(0, 28px)}.item-has-focus.label-floating.sc-ion-label-ios-h,.item-has-focus .label-floating.sc-ion-label-ios-h,.item-has-placeholder.sc-ion-label-ios-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-ios-h,.item-has-value.label-floating.sc-ion-label-ios-h,.item-has-value .label-floating.sc-ion-label-ios-h{-webkit-transform:scale(0.82);transform:scale(0.82)}.sc-ion-label-ios-s h1{margin-left:0;margin-right:0;margin-top:3px;margin-bottom:2px;font-size:1.375rem;font-weight:normal}.sc-ion-label-ios-s h2{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.0625rem;font-weight:normal}.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{margin-left:0;margin-right:0;margin-top:0;margin-bottom:3px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-ios-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}.sc-ion-label-ios-s>p{color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.4)}.sc-ion-label-ios-h.in-item-color.sc-ion-label-ios-s>p{color:inherit}.sc-ion-label-ios-s h2:last-child,.sc-ion-label-ios-s h3:last-child,.sc-ion-label-ios-s h4:last-child,.sc-ion-label-ios-s h5:last-child,.sc-ion-label-ios-s h6:last-child,.sc-ion-label-ios-s p:last-child{margin-bottom:0}\";\nconst IonLabelIosStyle0 = labelIosCss;\nconst labelMdCss = \".item.sc-ion-label-md-h,.item .sc-ion-label-md-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.item-legacy.sc-ion-label-md-h,.item-legacy .sc-ion-label-md-h{white-space:nowrap;overflow:hidden}.ion-color.sc-ion-label-md-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-md-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-md-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-md-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-md-h,.item-input .sc-ion-label-md-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-md-h,.item-textarea .sc-ion-label-md-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-md-h,.item-skeleton-text .sc-ion-label-md-h{overflow:hidden}.label-fixed.sc-ion-label-md-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-md-h{-webkit-transition:none;transition:none}.sc-ion-label-md-s h1,.sc-ion-label-md-s h2,.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-md-h{line-height:1.5}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:top left;transform-origin:top left}.label-stacked.label-rtl.sc-ion-label-md-h,.label-floating.label-rtl.sc-ion-label-md-h{-webkit-transform-origin:top right;transform-origin:top right}.label-stacked.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.label-floating.sc-ion-label-md-h{-webkit-transform:translateY(96%);transform:translateY(96%);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1)}.ion-focused.label-floating.sc-ion-label-md-h,.ion-focused .label-floating.sc-ion-label-md-h,.item-has-focus.label-floating.sc-ion-label-md-h,.item-has-focus .label-floating.sc-ion-label-md-h,.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h,.item-has-value.label-floating.sc-ion-label-md-h,.item-has-value .label-floating.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75)}.item-fill-outline.ion-focused.label-floating.sc-ion-label-md-h,.item-fill-outline.ion-focused .label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-focus.label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-focus .label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating,.item-fill-outline.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-value.label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-value .label-floating.sc-ion-label-md-h{-webkit-transform:translateY(-6px) scale(0.75);transform:translateY(-6px) scale(0.75);position:relative;max-width:-webkit-min-content;max-width:-moz-min-content;max-width:min-content;background-color:var(--ion-item-background, var(--ion-background-color, #fff));overflow:visible;z-index:3}.item-fill-outline.ion-focused.label-floating.sc-ion-label-md-h::before,.item-fill-outline.ion-focused .label-floating.sc-ion-label-md-h::before,.item-fill-outline.ion-focused.label-floating.sc-ion-label-md-h::after,.item-fill-outline.ion-focused .label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-focus.label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-focus .label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-focus.label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-focus .label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating::before,.item-fill-outline.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating::after,.item-fill-outline.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-value.label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-value .label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-value.label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-value .label-floating.sc-ion-label-md-h::after{position:absolute;width:4px;height:100%;background-color:var(--ion-item-background, var(--ion-background-color, #fff));content:\\\"\\\"}.item-fill-outline.ion-focused.label-floating.sc-ion-label-md-h::before,.item-fill-outline.ion-focused .label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-focus.label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-focus .label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating::before,.item-fill-outline.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-value.label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-value .label-floating.sc-ion-label-md-h::before{left:calc(-1 * 4px)}.item-fill-outline.ion-focused.label-floating.sc-ion-label-md-h::after,.item-fill-outline.ion-focused .label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-focus.label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-focus .label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating::after,.item-fill-outline.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-value.label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-value .label-floating.sc-ion-label-md-h::after{right:calc(-1 * 4px)}.item-fill-outline.ion-focused.item-has-start-slot.label-floating.sc-ion-label-md-h,.item-fill-outline.ion-focused.item-has-start-slot .label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-focus.item-has-start-slot.label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-focus.item-has-start-slot .label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-placeholder.sc-ion-label-md-h:not(.item-input).item-has-start-slot.label-floating,.item-fill-outline.item-has-placeholder:not(.item-input).item-has-start-slot .label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-value.item-has-start-slot.label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-value.item-has-start-slot .label-floating.sc-ion-label-md-h{-webkit-transform:translateX(-32px) translateY(-6px) scale(0.75);transform:translateX(-32px) translateY(-6px) scale(0.75)}.item-fill-outline.ion-focused.item-has-start-slot.label-floating.label-rtl.sc-ion-label-md-h,.item-fill-outline.ion-focused.item-has-start-slot .label-floating.label-rtl.sc-ion-label-md-h,.item-fill-outline.item-has-focus.item-has-start-slot.label-floating.label-rtl.sc-ion-label-md-h,.item-fill-outline.item-has-focus.item-has-start-slot .label-floating.label-rtl.sc-ion-label-md-h,.item-fill-outline.item-has-placeholder.sc-ion-label-md-h:not(.item-input).item-has-start-slot.label-floating.label-rtl,.item-fill-outline.item-has-placeholder:not(.item-input).item-has-start-slot .label-floating.label-rtl.sc-ion-label-md-h,.item-fill-outline.item-has-value.item-has-start-slot.label-floating.label-rtl.sc-ion-label-md-h,.item-fill-outline.item-has-value.item-has-start-slot .label-floating.label-rtl.sc-ion-label-md-h{-webkit-transform:translateX(calc(-1 * -32px)) translateY(-6px) scale(0.75);transform:translateX(calc(-1 * -32px)) translateY(-6px) scale(0.75)}.ion-focused.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-primary, #3880ff)}.ion-focused.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-contrast)}.item-fill-solid.ion-focused.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-solid.ion-focused.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-solid.ion-focused.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-fill-solid.ion-focused.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.ion-focused.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.ion-focused.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.ion-focused.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.ion-focused.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color),.item-fill-solid.item-has-focus.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-solid.item-has-focus.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-solid.item-has-focus.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-fill-solid.item-has-focus.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.item-has-focus.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.item-has-focus.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.item-has-focus.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.item-has-focus.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-base)}.ion-invalid.ion-touched.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--highlight-color-invalid)}.sc-ion-label-md-s h1{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.5rem;font-weight:normal}.sc-ion-label-md-s h2{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:1rem;font-weight:normal}.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-md-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:1.25rem;text-overflow:inherit;overflow:inherit}.sc-ion-label-md-s>p{color:var(--ion-color-step-600, #666666)}.sc-ion-label-md-h.in-item-color.sc-ion-label-md-s>p{color:inherit}\";\nconst IonLabelMdStyle0 = labelMdCss;\nconst Label = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionColor = createEvent(this, \"ionColor\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.inRange = false;\n    this.color = undefined;\n    this.position = undefined;\n    this.noAnimate = false;\n  }\n  componentWillLoad() {\n    this.inRange = !!this.el.closest('ion-range');\n    this.noAnimate = this.position === 'floating';\n    this.emitStyle();\n    this.emitColor();\n  }\n  componentDidLoad() {\n    if (this.noAnimate) {\n      setTimeout(() => {\n        this.noAnimate = false;\n      }, 1000);\n    }\n  }\n  colorChanged() {\n    this.emitColor();\n  }\n  positionChanged() {\n    this.emitStyle();\n  }\n  emitColor() {\n    const {\n      color\n    } = this;\n    this.ionColor.emit({\n      'item-label-color': color !== undefined,\n      [`ion-color-${color}`]: color !== undefined\n    });\n  }\n  emitStyle() {\n    const {\n      inRange,\n      position\n    } = this;\n    // If the label is inside of a range we don't want\n    // to override the classes added by the label that\n    // is a direct child of the item\n    if (!inRange) {\n      this.ionStyle.emit({\n        label: true,\n        [`label-${position}`]: position !== undefined\n      });\n    }\n  }\n  render() {\n    const position = this.position;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '72ad4ba5c1137ae0130e421346668e436ea53bf8',\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'in-item-color': hostContext('ion-item.ion-color', this.el),\n        [`label-${position}`]: position !== undefined,\n        [`label-no-animate`]: this.noAnimate,\n        'label-rtl': document.dir === 'rtl'\n      })\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"color\": [\"colorChanged\"],\n      \"position\": [\"positionChanged\"]\n    };\n  }\n};\nLabel.style = {\n  ios: IonLabelIosStyle0,\n  md: IonLabelMdStyle0\n};\nconst listIosCss = \"ion-list{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;contain:content;list-style-type:none}ion-list.list-inset{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:hidden}.list-ios{background:var(--ion-item-background, var(--ion-background-color, #fff))}.list-ios.list-inset{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px;border-radius:10px}.list-ios.list-inset ion-item:only-child,.list-ios.list-inset ion-item:not(:only-of-type):last-of-type,.list-ios.list-inset ion-item-sliding:last-of-type ion-item{--border-width:0;--inner-border-width:0}.list-ios.list-inset+ion-list.list-inset{margin-top:0}.list-ios-lines-none .item-lines-default{--inner-border-width:0px;--border-width:0px}.list-ios-lines-full .item-lines-default{--inner-border-width:0px;--border-width:0 0 0.55px 0}.list-ios-lines-inset .item-lines-default{--inner-border-width:0 0 0.55px 0;--border-width:0px}ion-card .list-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\nconst IonListIosStyle0 = listIosCss;\nconst listMdCss = \"ion-list{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;contain:content;list-style-type:none}ion-list.list-inset{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:hidden}.list-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:8px;padding-bottom:8px;background:var(--ion-item-background, var(--ion-background-color, #fff))}@supports (inset-inline-start: 0){.list-md>.input:last-child::after{inset-inline-start:0}}@supports not (inset-inline-start: 0){.list-md>.input:last-child::after{left:0}:host-context([dir=rtl]) .list-md>.input:last-child::after{left:unset;right:unset;right:0}[dir=rtl] .list-md>.input:last-child::after{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.list-md>.input:last-child::after:dir(rtl){left:unset;right:unset;right:0}}}.list-md.list-inset{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px;border-radius:2px}.list-md.list-inset ion-item:not(:only-of-type):last-of-type,.list-md.list-inset ion-item-sliding:last-of-type ion-item{--border-width:0;--inner-border-width:0}.list-md.list-inset ion-item:only-child{--border-width:0;--inner-border-width:0}.list-md.list-inset+ion-list.list-inset{margin-top:0}.list-md-lines-none .item-lines-default{--inner-border-width:0px;--border-width:0px}.list-md-lines-full .item-lines-default{--inner-border-width:0px;--border-width:0 0 1px 0}.list-md-lines-inset .item-lines-default{--inner-border-width:0 0 1px 0;--border-width:0px}ion-card .list-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\nconst IonListMdStyle0 = listMdCss;\nconst List = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.lines = undefined;\n    this.inset = false;\n  }\n  /**\n   * If `ion-item-sliding` are used inside the list, this method closes\n   * any open sliding item.\n   *\n   * Returns `true` if an actual `ion-item-sliding` is closed.\n   */\n  closeSlidingItems() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const item = _this.el.querySelector('ion-item-sliding');\n      if (item === null || item === void 0 ? void 0 : item.closeOpened) {\n        return item.closeOpened();\n      }\n      return false;\n    })();\n  }\n  render() {\n    const mode = getIonMode(this);\n    const {\n      lines,\n      inset\n    } = this;\n    return h(Host, {\n      key: '3df401155114c7a39c81f201bf8a181d07e8d4c8',\n      role: \"list\",\n      class: {\n        [mode]: true,\n        // Used internally for styling\n        [`list-${mode}`]: true,\n        'list-inset': inset,\n        [`list-lines-${lines}`]: lines !== undefined,\n        [`list-${mode}-lines-${lines}`]: lines !== undefined\n      }\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nList.style = {\n  ios: IonListIosStyle0,\n  md: IonListMdStyle0\n};\nconst listHeaderIosCss = \":host{--border-style:solid;--border-width:0;--inner-border-width:0;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:40px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);color:var(--color);overflow:hidden}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.list-header-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex:1 1 auto;flex:1 1 auto}:host(.list-header-lines-inset),:host(.list-header-lines-none){--border-width:0}:host(.list-header-lines-full),:host(.list-header-lines-none){--inner-border-width:0}:host{--background:transparent;--color:var(--ion-color-step-850, #262626);--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));padding-right:var(--ion-safe-area-right);padding-left:calc(var(--ion-safe-area-left, 0px) + 16px);position:relative;-ms-flex-align:end;align-items:flex-end;font-size:min(1.375rem, 56.1px);font-weight:700;letter-spacing:0}:host-context([dir=rtl]){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}}::slotted(ion-button),::slotted(ion-label){margin-top:29px;margin-bottom:6px}::slotted(ion-button){--padding-top:0;--padding-bottom:0;-webkit-margin-start:3px;margin-inline-start:3px;-webkit-margin-end:3px;margin-inline-end:3px;min-height:1.4em}:host(.list-header-lines-full){--border-width:0 0 0.55px 0}:host(.list-header-lines-inset){--inner-border-width:0 0 0.55px 0}\";\nconst IonListHeaderIosStyle0 = listHeaderIosCss;\nconst listHeaderMdCss = \":host{--border-style:solid;--border-width:0;--inner-border-width:0;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:40px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);color:var(--color);overflow:hidden}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.list-header-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex:1 1 auto;flex:1 1 auto}:host(.list-header-lines-inset),:host(.list-header-lines-none){--border-width:0}:host(.list-header-lines-full),:host(.list-header-lines-none){--inner-border-width:0}:host{--background:transparent;--color:var(--ion-text-color, #000);--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));padding-right:var(--ion-safe-area-right);padding-left:calc(var(--ion-safe-area-left, 0px) + 16px);min-height:45px;font-size:0.875rem}:host-context([dir=rtl]){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}}:host(.list-header-lines-full){--border-width:0 0 1px 0}:host(.list-header-lines-inset){--inner-border-width:0 0 1px 0}\";\nconst IonListHeaderMdStyle0 = listHeaderMdCss;\nconst ListHeader = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.color = undefined;\n    this.lines = undefined;\n  }\n  render() {\n    const {\n      lines\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'e5fabb3ae91e6fe47c89273d1d2dba5902f77f94',\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        [`list-header-lines-${lines}`]: lines !== undefined\n      })\n    }, h(\"div\", {\n      key: 'a9cfdaa436267fbabb0d618c3932849c1b77fbd2',\n      class: \"list-header-inner\"\n    }, h(\"slot\", {\n      key: '8ed806fd58f8f2265c5bf466886086e88ada93cc'\n    })));\n  }\n};\nListHeader.style = {\n  ios: IonListHeaderIosStyle0,\n  md: IonListHeaderMdStyle0\n};\nconst noteIosCss = \":host{color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-base)}:host{--color:var(--ion-color-step-350, #a6a6a6);font-size:max(14px, 1rem)}\";\nconst IonNoteIosStyle0 = noteIosCss;\nconst noteMdCss = \":host{color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-base)}:host{--color:var(--ion-color-step-600, #666666);font-size:0.875rem}\";\nconst IonNoteMdStyle0 = noteMdCss;\nconst Note = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.color = undefined;\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '79a17a318ec6e8326c9741b4a9bb4598acdc225e',\n      class: createColorClasses(this.color, {\n        [mode]: true\n      })\n    }, h(\"slot\", {\n      key: '5adeaccfabb4bee7b84ea5c5de804bd255b29255'\n    }));\n  }\n};\nNote.style = {\n  ios: IonNoteIosStyle0,\n  md: IonNoteMdStyle0\n};\nconst skeletonTextCss = \":host{--background:rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065);border-radius:var(--border-radius, inherit);display:block;width:100%;height:inherit;margin-top:4px;margin-bottom:4px;background:var(--background);line-height:10px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;pointer-events:none}span{display:inline-block}:host(.in-media){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;height:100%}:host(.skeleton-text-animated){position:relative;background:-webkit-gradient(linear, left top, right top, color-stop(8%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065)), color-stop(18%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.135)), color-stop(33%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065)));background:linear-gradient(to right, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065) 8%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.135) 18%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065) 33%);background-size:800px 104px;-webkit-animation-duration:1s;animation-duration:1s;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-name:shimmer;animation-name:shimmer;-webkit-animation-timing-function:linear;animation-timing-function:linear}@-webkit-keyframes shimmer{0%{background-position:-400px 0}100%{background-position:400px 0}}@keyframes shimmer{0%{background-position:-400px 0}100%{background-position:400px 0}}\";\nconst IonSkeletonTextStyle0 = skeletonTextCss;\nconst SkeletonText = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.animated = false;\n  }\n  componentWillLoad() {\n    this.emitStyle();\n  }\n  emitStyle() {\n    // The emitted property is used by item in order\n    // to add the item-skeleton-text class which applies\n    // overflow: hidden to its label\n    const style = {\n      'skeleton-text': true\n    };\n    this.ionStyle.emit(style);\n  }\n  render() {\n    const animated = this.animated && config.getBoolean('animated', true);\n    const inMedia = hostContext('ion-avatar', this.el) || hostContext('ion-thumbnail', this.el);\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '4dab0fd2de666de12ad8f6dc6ed1e1de0be67ddd',\n      class: {\n        [mode]: true,\n        'skeleton-text-animated': animated,\n        'in-media': inMedia\n      }\n    }, h(\"span\", {\n      key: 'f8f908ec24d65e63b14d9a54640a5f18f0fa8fa5'\n    }, \"\\u00A0\"));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nSkeletonText.style = IonSkeletonTextStyle0;\nexport { Item as ion_item, ItemDivider as ion_item_divider, ItemGroup as ion_item_group, Label as ion_label, List as ion_list, ListHeader as ion_list_header, Note as ion_note, SkeletonText as ion_skeleton_text };", "map": {"version": 3, "names": ["r", "registerInstance", "i", "forceUpdate", "h", "H", "Host", "f", "getElement", "d", "createEvent", "k", "inheritAttributes", "raf", "p", "printIonWarning", "a", "printIonError", "hostContext", "c", "createColorClasses", "o", "openURL", "chevronForward", "b", "getIonMode", "config", "itemIosCss", "IonItemIosStyle0", "itemMdCss", "IonItemMdStyle0", "<PERSON><PERSON>", "constructor", "hostRef", "labelColorStyles", "itemStyles", "Map", "inheritedAriaAttributes", "multipleInputs", "focusable", "color", "undefined", "button", "detail", "detailIcon", "disabled", "download", "fill", "shape", "href", "rel", "lines", "counter", "routerAnimation", "routerDirection", "target", "type", "counterFormatter", "counterString", "buttonChanged", "isFocusable", "counterFormatterChanged", "updateCounterOutput", "getFirstInput", "handleIonInput", "ev", "labelColorChanged", "itemStyle", "stopPropagation", "tagName", "updatedStyles", "newStyles", "childStyles", "get", "hasStyleChange", "Object", "keys", "for<PERSON>ach", "key", "itemKey", "length", "set", "connectedCallback", "hasStartEl", "componentWillLoad", "el", "componentDidLoad", "hasHelperSlot", "querySelector", "hasErrorSlot", "setMultipleInputs", "covers", "querySelectorAll", "inputs", "clickables", "isClickable", "hasCover", "canActivate", "focus<PERSON><PERSON><PERSON><PERSON>", "inputEl", "_a", "_b", "defaultCounterFormatter", "maxlength", "value", "toString", "e", "startEl", "classList", "add", "getFirstInteractive", "controls", "render", "mode", "clickable", "TagType", "attrs", "clickFn", "firstInteractive", "onClick", "path", "<PERSON><PERSON><PERSON>", "isTrusted", "clickedWithinShadowRoot", "shadowRoot", "contains", "click", "showDetail", "assign", "ariaDisabled", "fillValue", "inList", "class", "item", "document", "dir", "role", "part", "name", "icon", "lazy", "delegatesFocus", "watchers", "style", "ios", "md", "itemDividerIosCss", "IonItemDividerIosStyle0", "itemDividerMdCss", "IonItemDividerMdStyle0", "ItemDivider", "sticky", "itemGroupIosCss", "IonItemGroupIosStyle0", "itemGroupMdCss", "IonItemGroupMdStyle0", "ItemGroup", "labelIosCss", "IonLabelIosStyle0", "labelMdCss", "IonLabelMdStyle0", "Label", "ionColor", "ionStyle", "inRange", "position", "noAnimate", "closest", "emitStyle", "emitColor", "setTimeout", "colorChanged", "positionChanged", "emit", "label", "listIosCss", "IonListIosStyle0", "listMdCss", "IonListMdStyle0", "List", "inset", "closeSlidingItems", "_this", "_asyncToGenerator", "closeOpened", "listHeaderIosCss", "IonListHeaderIosStyle0", "listHeaderMdCss", "IonListHeaderMdStyle0", "ListHeader", "noteIosCss", "IonNoteIosStyle0", "noteMdCss", "IonNoteMdStyle0", "Note", "skeletonTextCss", "IonSkeletonTextStyle0", "SkeletonText", "animated", "getBoolean", "inMedia", "ion_item", "ion_item_divider", "ion_item_group", "ion_label", "ion_list", "ion_list_header", "ion_note", "ion_skeleton_text"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/ion-item_8.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, i as forceUpdate, h, H as Host, f as getElement, d as createEvent } from './index-a1a47f01.js';\nimport { k as inheritAttributes, r as raf } from './helpers-be245865.js';\nimport { p as printIonWarning, a as printIonError } from './index-9b0d46f4.js';\nimport { h as hostContext, c as createColorClasses, o as openURL } from './theme-01f3f29c.js';\nimport { o as chevronForward } from './index-f7dc70ba.js';\nimport { b as getIonMode, c as config } from './ionic-global-94f25d1b.js';\n\nconst itemIosCss = \":host{--border-radius:0px;--border-width:0px;--border-style:solid;--padding-top:0px;--padding-bottom:0px;--padding-end:0px;--padding-start:0px;--inner-border-width:0px;--inner-padding-top:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;--inner-padding-end:0px;--inner-box-shadow:none;--show-full-highlight:0;--show-inset-highlight:0;--detail-icon-color:initial;--detail-icon-font-size:1.25em;--detail-icon-opacity:0.25;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:initial;text-decoration:none;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color:not(.item-fill-solid):not(.item-fill-outline)) .item-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.ion-color:not(.item-fill-solid):not(.item-fill-outline)) .item-native,:host(.ion-color:not(.item-fill-solid):not(.item-fill-outline)) .item-inner{border-color:var(--ion-color-shade)}:host(.ion-activated) .item-native{color:var(--color-activated)}:host(.ion-activated) .item-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.ion-color.ion-activated) .item-native{color:var(--ion-color-contrast)}:host(.ion-focused) .item-native{color:var(--color-focused)}:host(.ion-focused) .item-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-color.ion-focused) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .item-native::after{background:var(--ion-color-contrast)}@media (any-hover: hover){:host(.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--color-hover)}:host(.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--ion-color-contrast)}}:host(.item-has-interactive-control){cursor:pointer}:host(.item-interactive-disabled:not(.item-multiple-inputs)){cursor:default;pointer-events:none}:host(.item-disabled){cursor:default;opacity:0.3;pointer-events:none}.item-native{border-radius:var(--border-radius);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;min-height:var(--min-height);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);overflow:inherit;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}[dir=rtl] .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){.item-native:dir(rtl){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}.item-native::-moz-focus-inner{border:0}.item-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:var(--transition);transition:var(--transition);z-index:-1}button,a{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-user-drag:none}.item-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);-webkit-box-shadow:var(--inner-box-shadow);box-shadow:var(--inner-box-shadow);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-bottom{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:0;padding-bottom:0;padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--inner-padding-end) + var(--ion-safe-area-right, 0px));display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host-context([dir=rtl]) .item-bottom{padding-left:calc(var(--inner-padding-end) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px))}[dir=rtl] .item-bottom{padding-left:calc(var(--inner-padding-end) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px))}@supports selector(:dir(rtl)){.item-bottom:dir(rtl){padding-left:calc(var(--inner-padding-end) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px))}}.item-detail-icon{-webkit-margin-start:calc(var(--inner-padding-end) / 2);margin-inline-start:calc(var(--inner-padding-end) / 2);-webkit-margin-end:-6px;margin-inline-end:-6px;color:var(--detail-icon-color);font-size:var(--detail-icon-font-size);opacity:var(--detail-icon-opacity)}::slotted(ion-icon){font-size:1.6em}::slotted(ion-button){--margin-top:0;--margin-bottom:0;--margin-start:0;--margin-end:0;z-index:1}::slotted(ion-label:not([slot=end])){-ms-flex:1;flex:1;width:-webkit-min-content;width:-moz-min-content;width:min-content;max-width:100%}:host(.item-input){-ms-flex-align:center;align-items:center}.input-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.item-label-stacked),:host(.item-label-floating){-ms-flex-align:start;align-items:start}:host(.item-label-stacked) .input-wrapper,:host(.item-label-floating) .input-wrapper{-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column}.item-highlight,.item-inner-highlight{left:0;right:0;top:0;bottom:0;border-radius:inherit;position:absolute;width:100%;height:100%;-webkit-transform:scaleX(0);transform:scaleX(0);-webkit-transition:border-bottom-width 200ms, -webkit-transform 200ms;transition:border-bottom-width 200ms, -webkit-transform 200ms;transition:transform 200ms, border-bottom-width 200ms;transition:transform 200ms, border-bottom-width 200ms, -webkit-transform 200ms;z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.item-interactive.ion-focused),:host(.item-interactive.item-has-focus),:host(.item-interactive.ion-touched.ion-invalid){--full-highlight-height:calc(var(--highlight-height) * var(--show-full-highlight));--inset-highlight-height:calc(var(--highlight-height) * var(--show-inset-highlight))}:host(.ion-focused) .item-highlight,:host(.ion-focused) .item-inner-highlight,:host(.item-has-focus) .item-highlight,:host(.item-has-focus) .item-inner-highlight{-webkit-transform:scaleX(1);transform:scaleX(1);border-style:var(--border-style);border-color:var(--highlight-background)}:host(.ion-focused) .item-highlight,:host(.item-has-focus) .item-highlight{border-width:var(--full-highlight-height);opacity:var(--show-full-highlight)}:host(.ion-focused) .item-inner-highlight,:host(.item-has-focus) .item-inner-highlight{border-bottom-width:var(--inset-highlight-height);opacity:var(--show-inset-highlight)}:host(.ion-focused.item-fill-solid) .item-highlight,:host(.item-has-focus.item-fill-solid) .item-highlight{border-width:calc(var(--full-highlight-height) - 1px)}:host(.ion-focused) .item-inner-highlight,:host(.ion-focused:not(.item-fill-outline)) .item-highlight,:host(.item-has-focus) .item-inner-highlight,:host(.item-has-focus:not(.item-fill-outline)) .item-highlight{border-top:none;border-right:none;border-left:none}:host(.item-interactive.ion-focused),:host(.item-interactive.item-has-focus){--highlight-background:var(--highlight-color-focused)}:host(.item-interactive.ion-valid){--highlight-background:var(--highlight-color-valid)}:host(.item-interactive.ion-invalid){--highlight-background:var(--highlight-color-invalid)}:host(.item-interactive.ion-invalid) ::slotted([slot=helper]){display:none}::slotted([slot=error]){display:none;color:var(--highlight-color-invalid)}:host(.item-interactive.ion-invalid) ::slotted([slot=error]){display:block}:host(:not(.item-label)) ::slotted(ion-select.legacy-select){--padding-start:0;max-width:none}:host(.item-label-stacked) ::slotted(ion-select.legacy-select),:host(.item-label-floating) ::slotted(ion-select.legacy-select){--padding-top:8px;--padding-bottom:8px;--padding-start:0;-ms-flex-item-align:stretch;align-self:stretch;width:100%;max-width:100%}:host(:not(.item-label)) ::slotted(ion-datetime){--padding-start:0}:host(.item-label-stacked) ::slotted(ion-datetime),:host(.item-label-floating) ::slotted(ion-datetime){--padding-start:0;width:100%}:host(.item-multiple-inputs) ::slotted(ion-checkbox),:host(.item-multiple-inputs) ::slotted(ion-datetime),:host(.item-multiple-inputs) ::slotted(ion-radio),:host(.item-multiple-inputs) ::slotted(ion-select.legacy-select){position:relative}:host(.item-textarea){-ms-flex-align:stretch;align-items:stretch}::slotted(ion-reorder[slot]){margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}:host(.item-fill-solid) ::slotted([slot=start]),:host(.item-fill-solid) ::slotted([slot=end]),:host(.item-fill-outline) ::slotted([slot=start]),:host(.item-fill-outline) ::slotted([slot=end]){-ms-flex-item-align:center;align-self:center}::slotted([slot=helper]),::slotted([slot=error]),.item-counter{padding-top:5px;font-size:0.75rem;z-index:1}.item-counter{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, #737373);white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}@media (prefers-reduced-motion: reduce){.item-highlight,.item-inner-highlight{-webkit-transition:none;transition:none}}:host{--min-height:44px;--transition:background-color 200ms linear, opacity 200ms linear;--padding-start:16px;--inner-padding-end:16px;--inner-border-width:0px 0px 0.55px 0px;--background:var(--ion-item-background, var(--ion-background-color, #fff));--background-activated:var(--ion-text-color, #000);--background-focused:var(--ion-text-color, #000);--background-hover:currentColor;--background-activated-opacity:.12;--background-focused-opacity:.15;--background-hover-opacity:.04;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));--color:var(--ion-item-color, var(--ion-text-color, #000));--highlight-height:0px;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--bottom-padding-start:0px;font-size:1rem}:host(.ion-activated){--transition:none}:host(.ion-color.ion-focused) .item-native::after{background:#000;opacity:0.15}:host(.ion-color.ion-activated) .item-native::after{background:#000;opacity:0.12}:host(.item-interactive){--show-full-highlight:0;--show-inset-highlight:1}:host(.item-lines-full){--border-width:0px 0px 0.55px 0px;--show-full-highlight:1;--show-inset-highlight:0}:host(.item-lines-inset){--inner-border-width:0px 0px 0.55px 0px;--show-full-highlight:0;--show-inset-highlight:1}:host(.item-lines-inset),:host(.item-lines-none){--border-width:0px;--show-full-highlight:0}:host(.item-lines-full),:host(.item-lines-none){--inner-border-width:0px;--show-inset-highlight:0}.item-highlight,.item-inner-highlight{-webkit-transition:none;transition:none}:host(.item-has-focus) .item-inner-highlight,:host(.item-has-focus) .item-highlight{border-top:none;border-right:none;border-left:none}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:2px;margin-bottom:2px}::slotted(ion-icon[slot=start]),::slotted(ion-icon[slot=end]){margin-top:7px;margin-bottom:7px}::slotted(ion-toggle[slot=start]),::slotted(ion-toggle[slot=end]){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host(.item-label-stacked) ::slotted([slot=end]),:host(.item-label-floating) ::slotted([slot=end]){margin-top:7px;margin-bottom:7px}::slotted(.button-small){--padding-top:1px;--padding-bottom:1px;--padding-start:.5em;--padding-end:.5em;min-height:24px;font-size:0.8125rem}::slotted(ion-avatar){width:36px;height:36px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:8px;margin-bottom:8px}:host(.item-radio) ::slotted(ion-label),:host(.item-toggle) ::slotted(ion-label){-webkit-margin-start:0px;margin-inline-start:0px}::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:10px;margin-bottom:10px}:host(.item-label-floating),:host(.item-label-stacked){--min-height:68px}:host(.item-label-stacked) ::slotted(ion-select.legacy-select),:host(.item-label-floating) ::slotted(ion-select.legacy-select){--padding-top:8px;--padding-bottom:8px;--padding-start:0px}:host(.item-label-fixed) ::slotted(ion-select.legacy-select),:host(.item-label-fixed) ::slotted(ion-datetime){--padding-start:0}\";\nconst IonItemIosStyle0 = itemIosCss;\n\nconst itemMdCss = \":host{--border-radius:0px;--border-width:0px;--border-style:solid;--padding-top:0px;--padding-bottom:0px;--padding-end:0px;--padding-start:0px;--inner-border-width:0px;--inner-padding-top:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;--inner-padding-end:0px;--inner-box-shadow:none;--show-full-highlight:0;--show-inset-highlight:0;--detail-icon-color:initial;--detail-icon-font-size:1.25em;--detail-icon-opacity:0.25;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:initial;text-decoration:none;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color:not(.item-fill-solid):not(.item-fill-outline)) .item-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.ion-color:not(.item-fill-solid):not(.item-fill-outline)) .item-native,:host(.ion-color:not(.item-fill-solid):not(.item-fill-outline)) .item-inner{border-color:var(--ion-color-shade)}:host(.ion-activated) .item-native{color:var(--color-activated)}:host(.ion-activated) .item-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.ion-color.ion-activated) .item-native{color:var(--ion-color-contrast)}:host(.ion-focused) .item-native{color:var(--color-focused)}:host(.ion-focused) .item-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-color.ion-focused) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .item-native::after{background:var(--ion-color-contrast)}@media (any-hover: hover){:host(.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--color-hover)}:host(.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--ion-color-contrast)}}:host(.item-has-interactive-control){cursor:pointer}:host(.item-interactive-disabled:not(.item-multiple-inputs)){cursor:default;pointer-events:none}:host(.item-disabled){cursor:default;opacity:0.3;pointer-events:none}.item-native{border-radius:var(--border-radius);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;min-height:var(--min-height);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);overflow:inherit;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}[dir=rtl] .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){.item-native:dir(rtl){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}.item-native::-moz-focus-inner{border:0}.item-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0;-webkit-transition:var(--transition);transition:var(--transition);z-index:-1}button,a{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-user-drag:none}.item-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);-webkit-box-shadow:var(--inner-box-shadow);box-shadow:var(--inner-box-shadow);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-bottom{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:0;padding-bottom:0;padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--inner-padding-end) + var(--ion-safe-area-right, 0px));display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host-context([dir=rtl]) .item-bottom{padding-left:calc(var(--inner-padding-end) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px))}[dir=rtl] .item-bottom{padding-left:calc(var(--inner-padding-end) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px))}@supports selector(:dir(rtl)){.item-bottom:dir(rtl){padding-left:calc(var(--inner-padding-end) + var(--ion-safe-area-left, 0px));padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px))}}.item-detail-icon{-webkit-margin-start:calc(var(--inner-padding-end) / 2);margin-inline-start:calc(var(--inner-padding-end) / 2);-webkit-margin-end:-6px;margin-inline-end:-6px;color:var(--detail-icon-color);font-size:var(--detail-icon-font-size);opacity:var(--detail-icon-opacity)}::slotted(ion-icon){font-size:1.6em}::slotted(ion-button){--margin-top:0;--margin-bottom:0;--margin-start:0;--margin-end:0;z-index:1}::slotted(ion-label:not([slot=end])){-ms-flex:1;flex:1;width:-webkit-min-content;width:-moz-min-content;width:min-content;max-width:100%}:host(.item-input){-ms-flex-align:center;align-items:center}.input-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.item-label-stacked),:host(.item-label-floating){-ms-flex-align:start;align-items:start}:host(.item-label-stacked) .input-wrapper,:host(.item-label-floating) .input-wrapper{-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column}.item-highlight,.item-inner-highlight{left:0;right:0;top:0;bottom:0;border-radius:inherit;position:absolute;width:100%;height:100%;-webkit-transform:scaleX(0);transform:scaleX(0);-webkit-transition:border-bottom-width 200ms, -webkit-transform 200ms;transition:border-bottom-width 200ms, -webkit-transform 200ms;transition:transform 200ms, border-bottom-width 200ms;transition:transform 200ms, border-bottom-width 200ms, -webkit-transform 200ms;z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.item-interactive.ion-focused),:host(.item-interactive.item-has-focus),:host(.item-interactive.ion-touched.ion-invalid){--full-highlight-height:calc(var(--highlight-height) * var(--show-full-highlight));--inset-highlight-height:calc(var(--highlight-height) * var(--show-inset-highlight))}:host(.ion-focused) .item-highlight,:host(.ion-focused) .item-inner-highlight,:host(.item-has-focus) .item-highlight,:host(.item-has-focus) .item-inner-highlight{-webkit-transform:scaleX(1);transform:scaleX(1);border-style:var(--border-style);border-color:var(--highlight-background)}:host(.ion-focused) .item-highlight,:host(.item-has-focus) .item-highlight{border-width:var(--full-highlight-height);opacity:var(--show-full-highlight)}:host(.ion-focused) .item-inner-highlight,:host(.item-has-focus) .item-inner-highlight{border-bottom-width:var(--inset-highlight-height);opacity:var(--show-inset-highlight)}:host(.ion-focused.item-fill-solid) .item-highlight,:host(.item-has-focus.item-fill-solid) .item-highlight{border-width:calc(var(--full-highlight-height) - 1px)}:host(.ion-focused) .item-inner-highlight,:host(.ion-focused:not(.item-fill-outline)) .item-highlight,:host(.item-has-focus) .item-inner-highlight,:host(.item-has-focus:not(.item-fill-outline)) .item-highlight{border-top:none;border-right:none;border-left:none}:host(.item-interactive.ion-focused),:host(.item-interactive.item-has-focus){--highlight-background:var(--highlight-color-focused)}:host(.item-interactive.ion-valid){--highlight-background:var(--highlight-color-valid)}:host(.item-interactive.ion-invalid){--highlight-background:var(--highlight-color-invalid)}:host(.item-interactive.ion-invalid) ::slotted([slot=helper]){display:none}::slotted([slot=error]){display:none;color:var(--highlight-color-invalid)}:host(.item-interactive.ion-invalid) ::slotted([slot=error]){display:block}:host(:not(.item-label)) ::slotted(ion-select.legacy-select){--padding-start:0;max-width:none}:host(.item-label-stacked) ::slotted(ion-select.legacy-select),:host(.item-label-floating) ::slotted(ion-select.legacy-select){--padding-top:8px;--padding-bottom:8px;--padding-start:0;-ms-flex-item-align:stretch;align-self:stretch;width:100%;max-width:100%}:host(:not(.item-label)) ::slotted(ion-datetime){--padding-start:0}:host(.item-label-stacked) ::slotted(ion-datetime),:host(.item-label-floating) ::slotted(ion-datetime){--padding-start:0;width:100%}:host(.item-multiple-inputs) ::slotted(ion-checkbox),:host(.item-multiple-inputs) ::slotted(ion-datetime),:host(.item-multiple-inputs) ::slotted(ion-radio),:host(.item-multiple-inputs) ::slotted(ion-select.legacy-select){position:relative}:host(.item-textarea){-ms-flex-align:stretch;align-items:stretch}::slotted(ion-reorder[slot]){margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}:host(.item-fill-solid) ::slotted([slot=start]),:host(.item-fill-solid) ::slotted([slot=end]),:host(.item-fill-outline) ::slotted([slot=start]),:host(.item-fill-outline) ::slotted([slot=end]){-ms-flex-item-align:center;align-self:center}::slotted([slot=helper]),::slotted([slot=error]),.item-counter{padding-top:5px;font-size:0.75rem;z-index:1}.item-counter{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, #737373);white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}@media (prefers-reduced-motion: reduce){.item-highlight,.item-inner-highlight{-webkit-transition:none;transition:none}}:host{--min-height:48px;--background:var(--ion-item-background, var(--ion-background-color, #fff));--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));--color:var(--ion-item-color, var(--ion-text-color, #000));--transition:opacity 15ms linear, background-color 15ms linear;--padding-start:16px;--inner-padding-end:16px;--inner-border-width:0 0 1px 0;--highlight-height:1px;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);font-size:1rem;font-weight:normal;text-transform:none}:host(.item-fill-outline){--highlight-height:2px}:host(.item-fill-none.item-interactive.ion-focus) .item-highlight,:host(.item-fill-none.item-interactive.item-has-focus) .item-highlight,:host(.item-fill-none.item-interactive.ion-touched.ion-invalid) .item-highlight{-webkit-transform:scaleX(1);transform:scaleX(1);border-width:0 0 var(--full-highlight-height) 0;border-style:var(--border-style);border-color:var(--highlight-background)}:host(.item-fill-none.item-interactive.ion-focus) .item-native,:host(.item-fill-none.item-interactive.item-has-focus) .item-native,:host(.item-fill-none.item-interactive.ion-touched.ion-invalid) .item-native{border-bottom-color:var(--highlight-background)}:host(.item-fill-outline.item-interactive.ion-focus) .item-highlight,:host(.item-fill-outline.item-interactive.item-has-focus) .item-highlight{-webkit-transform:scaleX(1);transform:scaleX(1)}:host(.item-fill-outline.item-interactive.ion-focus) .item-highlight,:host(.item-fill-outline.item-interactive.item-has-focus) .item-highlight,:host(.item-fill-outline.item-interactive.ion-touched.ion-invalid) .item-highlight{border-width:var(--full-highlight-height);border-style:var(--border-style);border-color:var(--highlight-background)}:host(.item-fill-outline.item-interactive.ion-touched.ion-invalid) .item-native{border-color:var(--highlight-background)}:host(.item-fill-solid.item-interactive.ion-focus) .item-highlight,:host(.item-fill-solid.item-interactive.item-has-focus) .item-highlight,:host(.item-fill-solid.item-interactive.ion-touched.ion-invalid) .item-highlight{-webkit-transform:scaleX(1);transform:scaleX(1);border-width:0 0 var(--full-highlight-height) 0;border-style:var(--border-style);border-color:var(--highlight-background)}:host(.item-fill-solid.item-interactive.ion-focus) .item-native,:host(.item-fill-solid.item-interactive.item-has-focus) .item-native,:host(.item-fill-solid.item-interactive.ion-touched.ion-invalid) .item-native{border-bottom-color:var(--highlight-background)}:host(.ion-color.ion-activated) .item-native::after{background:transparent}:host(.item-has-focus) .item-native{caret-color:var(--highlight-background)}:host(.item-interactive){--border-width:0 0 1px 0;--inner-border-width:0;--show-full-highlight:1;--show-inset-highlight:0}:host(.item-lines-full){--border-width:0 0 1px 0;--show-full-highlight:1;--show-inset-highlight:0}:host(.item-lines-inset){--inner-border-width:0 0 1px 0;--show-full-highlight:0;--show-inset-highlight:1}:host(.item-lines-inset),:host(.item-lines-none){--border-width:0;--show-full-highlight:0}:host(.item-lines-full),:host(.item-lines-none){--inner-border-width:0;--show-inset-highlight:0}:host(.item-fill-outline) .item-highlight{--position-offset:calc(-1 * var(--border-width));top:var(--position-offset);width:calc(100% + 2 * var(--border-width));height:calc(100% + 2 * var(--border-width));-webkit-transition:none;transition:none}@supports (inset-inline-start: 0){:host(.item-fill-outline) .item-highlight{inset-inline-start:var(--position-offset)}}@supports not (inset-inline-start: 0){:host(.item-fill-outline) .item-highlight{left:var(--position-offset)}:host-context([dir=rtl]):host(.item-fill-outline) .item-highlight,:host-context([dir=rtl]).item-fill-outline .item-highlight{left:unset;right:unset;right:var(--position-offset)}@supports selector(:dir(rtl)){:host(.item-fill-outline:dir(rtl)) .item-highlight{left:unset;right:unset;right:var(--position-offset)}}}:host(.item-fill-outline.ion-focused) .item-native,:host(.item-fill-outline.item-has-focus) .item-native{border-color:transparent}:host(.item-multi-line) ::slotted([slot=start]),:host(.item-multi-line) ::slotted([slot=end]){margin-top:16px;margin-bottom:16px;-ms-flex-item-align:start;align-self:flex-start}::slotted([slot=start]){-webkit-margin-end:32px;margin-inline-end:32px}::slotted([slot=end]){-webkit-margin-start:32px;margin-inline-start:32px}:host(.item-fill-solid) ::slotted([slot=start]),:host(.item-fill-solid) ::slotted([slot=end]),:host(.item-fill-outline) ::slotted([slot=start]),:host(.item-fill-outline) ::slotted([slot=end]){-ms-flex-item-align:center;align-self:center}::slotted(ion-icon){color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);font-size:1.5em}:host(.ion-color:not(.item-fill-solid):not(.item-fill-outline)) ::slotted(ion-icon){color:var(--ion-color-contrast)}::slotted(ion-icon[slot]){margin-top:12px;margin-bottom:12px}::slotted(ion-icon[slot=start]){-webkit-margin-end:32px;margin-inline-end:32px}::slotted(ion-icon[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}:host(.item-fill-solid) ::slotted(ion-icon[slot=start]),:host(.item-fill-outline) ::slotted(ion-icon[slot=start]){-webkit-margin-end:8px;margin-inline-end:8px}::slotted(ion-toggle[slot=start]),::slotted(ion-toggle[slot=end]){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}::slotted(ion-note){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-ms-flex-item-align:start;align-self:flex-start;font-size:0.6875rem}::slotted(ion-note[slot]:not([slot=helper]):not([slot=error])){padding-left:0;padding-right:0;padding-top:18px;padding-bottom:10px}::slotted(ion-note[slot=start]){-webkit-padding-end:16px;padding-inline-end:16px}::slotted(ion-note[slot=end]){-webkit-padding-start:16px;padding-inline-start:16px}::slotted(ion-avatar){width:40px;height:40px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar),::slotted(ion-thumbnail){margin-top:8px;margin-bottom:8px}::slotted(ion-avatar[slot=start]),::slotted(ion-thumbnail[slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:10px;margin-bottom:10px}:host(.item-label-stacked) ::slotted([slot=end]),:host(.item-label-floating) ::slotted([slot=end]){margin-top:7px;margin-bottom:7px}:host(.item-label-fixed) ::slotted(ion-select.legacy-select),:host(.item-label-fixed) ::slotted(ion-datetime){--padding-start:8px}:host(.item-toggle) ::slotted(ion-label),:host(.item-radio) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0}::slotted(.button-small){--padding-top:2px;--padding-bottom:2px;--padding-start:.6em;--padding-end:.6em;min-height:25px;font-size:0.75rem}:host(.item-label-floating),:host(.item-label-stacked){--min-height:55px}:host(.item-label-stacked) ::slotted(ion-select.legacy-select),:host(.item-label-floating) ::slotted(ion-select.legacy-select){--padding-top:8px;--padding-bottom:8px;--padding-start:0}:host(.ion-focused:not(.ion-color)) ::slotted(.label-stacked),:host(.ion-focused:not(.ion-color)) ::slotted(.label-floating),:host(.item-has-focus:not(.ion-color)) ::slotted(.label-stacked),:host(.item-has-focus:not(.ion-color)) ::slotted(.label-floating){color:var(--ion-color-primary, #3880ff)}:host(.ion-color){--highlight-color-focused:var(--ion-color-contrast)}:host(.item-label-color){--highlight-color-focused:var(--ion-color-base)}:host(.item-fill-solid.ion-color),:host(.item-fill-outline.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.item-fill-solid){--background:var(--ion-color-step-50, #f2f2f2);--background-hover:var(--ion-color-step-100, #e6e6e6);--background-focused:var(--ion-color-step-150, #d9d9d9);--border-width:0 0 1px 0;--inner-border-width:0;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}:host-context([dir=rtl]):host(.item-fill-solid),:host-context([dir=rtl]).item-fill-solid{border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}@supports selector(:dir(rtl)){:host(.item-fill-solid:dir(rtl)){border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}}:host(.item-fill-solid) .item-native{--border-color:var(--ion-color-step-500, gray)}:host(.item-fill-solid.ion-focused) .item-native,:host(.item-fill-solid.item-has-focus) .item-native{--background:var(--background-focused)}:host(.item-fill-solid.item-shape-round){border-top-left-radius:16px;border-top-right-radius:16px;border-bottom-right-radius:0;border-bottom-left-radius:0}:host-context([dir=rtl]):host(.item-fill-solid.item-shape-round),:host-context([dir=rtl]).item-fill-solid.item-shape-round{border-top-left-radius:16px;border-top-right-radius:16px;border-bottom-right-radius:0;border-bottom-left-radius:0}@supports selector(:dir(rtl)){:host(.item-fill-solid.item-shape-round:dir(rtl)){border-top-left-radius:16px;border-top-right-radius:16px;border-bottom-right-radius:0;border-bottom-left-radius:0}}@media (any-hover: hover){:host(.item-fill-solid:hover) .item-native{--background:var(--background-hover);--border-color:var(--ion-color-step-750, #404040)}}:host(.item-fill-outline){--ripple-color:transparent;--background-focused:transparent;--background-hover:transparent;--border-color:var(--ion-color-step-500, gray);--border-width:1px;border:none;overflow:visible}:host(.item-fill-outline) .item-native{--native-padding-left:16px;border-radius:4px}:host(.item-fill-outline.item-shape-round) .item-native{--inner-padding-start:16px;border-radius:28px}:host(.item-fill-outline.item-shape-round) .item-bottom{-webkit-padding-start:32px;padding-inline-start:32px}:host(.item-fill-outline.item-label-floating.ion-focused) .item-native ::slotted(ion-input:not(:first-child)),:host(.item-fill-outline.item-label-floating.ion-focused) .item-native ::slotted(ion-textarea:not(:first-child)),:host(.item-fill-outline.item-label-floating.item-has-focus) .item-native ::slotted(ion-input:not(:first-child)),:host(.item-fill-outline.item-label-floating.item-has-focus) .item-native ::slotted(ion-textarea:not(:first-child)),:host(.item-fill-outline.item-label-floating.item-has-value) .item-native ::slotted(ion-input:not(:first-child)),:host(.item-fill-outline.item-label-floating.item-has-value) .item-native ::slotted(ion-textarea:not(:first-child)){-webkit-transform:translateY(-14px);transform:translateY(-14px)}@media (any-hover: hover){:host(.item-fill-outline:hover) .item-native{--border-color:var(--ion-color-step-750, #404040)}}.item-counter{letter-spacing:0.0333333333em}\";\nconst IonItemMdStyle0 = itemMdCss;\n\nconst Item = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.labelColorStyles = {};\n        this.itemStyles = new Map();\n        this.inheritedAriaAttributes = {};\n        this.multipleInputs = false;\n        this.focusable = true;\n        this.color = undefined;\n        this.button = false;\n        this.detail = undefined;\n        this.detailIcon = chevronForward;\n        this.disabled = false;\n        this.download = undefined;\n        this.fill = undefined;\n        this.shape = undefined;\n        this.href = undefined;\n        this.rel = undefined;\n        this.lines = undefined;\n        this.counter = false;\n        this.routerAnimation = undefined;\n        this.routerDirection = 'forward';\n        this.target = undefined;\n        this.type = 'button';\n        this.counterFormatter = undefined;\n        this.counterString = undefined;\n    }\n    buttonChanged() {\n        // Update the focusable option when the button option is changed\n        this.focusable = this.isFocusable();\n    }\n    counterFormatterChanged() {\n        this.updateCounterOutput(this.getFirstInput());\n    }\n    handleIonInput(ev) {\n        if (this.counter && ev.target === this.getFirstInput()) {\n            this.updateCounterOutput(ev.target);\n        }\n    }\n    labelColorChanged(ev) {\n        const { color } = this;\n        // There will be a conflict with item color if\n        // we apply the label color to item, so we ignore\n        // the label color if the user sets a color on item\n        if (color === undefined) {\n            this.labelColorStyles = ev.detail;\n        }\n    }\n    itemStyle(ev) {\n        ev.stopPropagation();\n        const tagName = ev.target.tagName;\n        const updatedStyles = ev.detail;\n        const newStyles = {};\n        const childStyles = this.itemStyles.get(tagName) || {};\n        let hasStyleChange = false;\n        Object.keys(updatedStyles).forEach((key) => {\n            if (updatedStyles[key]) {\n                const itemKey = `item-${key}`;\n                if (!childStyles[itemKey]) {\n                    hasStyleChange = true;\n                }\n                newStyles[itemKey] = true;\n            }\n        });\n        if (!hasStyleChange && Object.keys(newStyles).length !== Object.keys(childStyles).length) {\n            hasStyleChange = true;\n        }\n        if (hasStyleChange) {\n            this.itemStyles.set(tagName, newStyles);\n            forceUpdate(this);\n        }\n    }\n    connectedCallback() {\n        if (this.counter) {\n            this.updateCounterOutput(this.getFirstInput());\n        }\n        this.hasStartEl();\n    }\n    componentWillLoad() {\n        this.inheritedAriaAttributes = inheritAttributes(this.el, ['aria-label']);\n    }\n    componentDidLoad() {\n        const { el, counter, counterFormatter, fill, shape } = this;\n        const hasHelperSlot = el.querySelector('[slot=\"helper\"]') !== null;\n        if (hasHelperSlot) {\n            printIonWarning('The \"helper\" slot has been deprecated in favor of using the \"helperText\" property on ion-input or ion-textarea.', el);\n        }\n        const hasErrorSlot = el.querySelector('[slot=\"error\"]') !== null;\n        if (hasErrorSlot) {\n            printIonWarning('The \"error\" slot has been deprecated in favor of using the \"errorText\" property on ion-input or ion-textarea.', el);\n        }\n        if (counter === true) {\n            printIonWarning('The \"counter\" property has been deprecated in favor of using the \"counter\" property on ion-input or ion-textarea.', el);\n        }\n        if (counterFormatter !== undefined) {\n            printIonWarning('The \"counterFormatter\" property has been deprecated in favor of using the \"counterFormatter\" property on ion-input or ion-textarea.', el);\n        }\n        if (fill !== undefined) {\n            printIonWarning('The \"fill\" property has been deprecated in favor of using the \"fill\" property on ion-input or ion-textarea.', el);\n        }\n        if (shape !== undefined) {\n            printIonWarning('The \"shape\" property has been deprecated in favor of using the \"shape\" property on ion-input or ion-textarea.', el);\n        }\n        raf(() => {\n            this.setMultipleInputs();\n            this.focusable = this.isFocusable();\n        });\n    }\n    // If the item contains multiple clickable elements and/or inputs, then the item\n    // should not have a clickable input cover over the entire item to prevent\n    // interfering with their individual click events\n    setMultipleInputs() {\n        // The following elements have a clickable cover that is relative to the entire item\n        const covers = this.el.querySelectorAll('ion-checkbox, ion-datetime, ion-select, ion-radio');\n        // The following elements can accept focus alongside the previous elements\n        // therefore if these elements are also a child of item, we don't want the\n        // input cover on top of those interfering with their clicks\n        const inputs = this.el.querySelectorAll('ion-input, ion-range, ion-searchbar, ion-segment, ion-textarea, ion-toggle');\n        // The following elements should also stay clickable when an input with cover is present\n        const clickables = this.el.querySelectorAll('ion-anchor, ion-button, a, button');\n        // Check for multiple inputs to change the position of the input cover to relative\n        // for all of the covered inputs above\n        this.multipleInputs =\n            covers.length + inputs.length > 1 ||\n                covers.length + clickables.length > 1 ||\n                (covers.length > 0 && this.isClickable());\n    }\n    // If the item contains an input including a checkbox, datetime, select, or radio\n    // then the item will have a clickable input cover that covers the item\n    // that should get the hover, focused and activated states UNLESS it has multiple\n    // inputs, then those need to individually get each click\n    hasCover() {\n        const inputs = this.el.querySelectorAll('ion-checkbox, ion-datetime, ion-select, ion-radio');\n        return inputs.length === 1 && !this.multipleInputs;\n    }\n    // If the item has an href or button property it will render a native\n    // anchor or button that is clickable\n    isClickable() {\n        return this.href !== undefined || this.button;\n    }\n    canActivate() {\n        return this.isClickable() || this.hasCover();\n    }\n    isFocusable() {\n        const focusableChild = this.el.querySelector('.ion-focusable');\n        return this.canActivate() || focusableChild !== null;\n    }\n    getFirstInput() {\n        const inputs = this.el.querySelectorAll('ion-input, ion-textarea');\n        return inputs[0];\n    }\n    updateCounterOutput(inputEl) {\n        var _a, _b;\n        const { counter, counterFormatter, defaultCounterFormatter } = this;\n        if (counter && !this.multipleInputs && (inputEl === null || inputEl === void 0 ? void 0 : inputEl.maxlength) !== undefined) {\n            const length = (_b = (_a = inputEl === null || inputEl === void 0 ? void 0 : inputEl.value) === null || _a === void 0 ? void 0 : _a.toString().length) !== null && _b !== void 0 ? _b : 0;\n            if (counterFormatter === undefined) {\n                this.counterString = defaultCounterFormatter(length, inputEl.maxlength);\n            }\n            else {\n                try {\n                    this.counterString = counterFormatter(length, inputEl.maxlength);\n                }\n                catch (e) {\n                    printIonError('Exception in provided `counterFormatter`.', e);\n                    // Fallback to the default counter formatter when an exception happens\n                    this.counterString = defaultCounterFormatter(length, inputEl.maxlength);\n                }\n            }\n        }\n    }\n    defaultCounterFormatter(length, maxlength) {\n        return `${length} / ${maxlength}`;\n    }\n    hasStartEl() {\n        const startEl = this.el.querySelector('[slot=\"start\"]');\n        if (startEl !== null) {\n            this.el.classList.add('item-has-start-slot');\n        }\n    }\n    getFirstInteractive() {\n        const controls = this.el.querySelectorAll('ion-toggle:not([disabled]), ion-checkbox:not([disabled]), ion-radio:not([disabled]), ion-select:not([disabled])');\n        return controls[0];\n    }\n    render() {\n        const { counterString, detail, detailIcon, download, fill, labelColorStyles, lines, disabled, href, rel, shape, target, routerAnimation, routerDirection, inheritedAriaAttributes, multipleInputs, } = this;\n        const childStyles = {};\n        const mode = getIonMode(this);\n        const clickable = this.isClickable();\n        const canActivate = this.canActivate();\n        const TagType = clickable ? (href === undefined ? 'button' : 'a') : 'div';\n        const attrs = TagType === 'button'\n            ? { type: this.type }\n            : {\n                download,\n                href,\n                rel,\n                target,\n            };\n        let clickFn = {};\n        const firstInteractive = this.getFirstInteractive();\n        // Only set onClick if the item is clickable to prevent screen\n        // readers from reading all items as clickable\n        if (clickable || (firstInteractive !== undefined && !multipleInputs)) {\n            clickFn = {\n                onClick: (ev) => {\n                    if (clickable) {\n                        openURL(href, ev, routerDirection, routerAnimation);\n                    }\n                    if (firstInteractive !== undefined && !multipleInputs) {\n                        const path = ev.composedPath();\n                        const target = path[0];\n                        if (ev.isTrusted) {\n                            /**\n                             * Dispatches a click event to the first interactive element,\n                             * when it is the result of a user clicking on the item.\n                             *\n                             * We check if the click target is in the shadow root,\n                             * which means the user clicked on the .item-native or\n                             * .item-inner padding.\n                             */\n                            const clickedWithinShadowRoot = this.el.shadowRoot.contains(target);\n                            if (clickedWithinShadowRoot) {\n                                firstInteractive.click();\n                            }\n                        }\n                    }\n                },\n            };\n        }\n        const showDetail = detail !== undefined ? detail : mode === 'ios' && clickable;\n        this.itemStyles.forEach((value) => {\n            Object.assign(childStyles, value);\n        });\n        const ariaDisabled = disabled || childStyles['item-interactive-disabled'] ? 'true' : null;\n        const fillValue = fill || 'none';\n        const inList = hostContext('ion-list', this.el) && !hostContext('ion-radio-group', this.el);\n        return (h(Host, { key: '077c9ab04985292f79c30691151d2778a26285fb', \"aria-disabled\": ariaDisabled, class: Object.assign(Object.assign(Object.assign({}, childStyles), labelColorStyles), createColorClasses(this.color, {\n                item: true,\n                [mode]: true,\n                'item-lines-default': lines === undefined,\n                [`item-lines-${lines}`]: lines !== undefined,\n                [`item-fill-${fillValue}`]: true,\n                [`item-shape-${shape}`]: shape !== undefined,\n                'item-has-interactive-control': firstInteractive !== undefined,\n                'item-disabled': disabled,\n                'in-list': inList,\n                'item-multiple-inputs': this.multipleInputs,\n                'ion-activatable': canActivate,\n                'ion-focusable': this.focusable,\n                'item-rtl': document.dir === 'rtl',\n            })), role: inList ? 'listitem' : null }, h(TagType, Object.assign({ key: '08bb75c85d0584e9fb7f461e9e06b9b651ef3515' }, attrs, inheritedAriaAttributes, { class: \"item-native\", part: \"native\", disabled: disabled }, clickFn), h(\"slot\", { key: '3f742b07c4a58eadedf675be4878a3e00070750e', name: \"start\" }), h(\"div\", { key: '0160b8963434f3107856a2df2fb96f1abb4fdbfe', class: \"item-inner\" }, h(\"div\", { key: '9439b21474443341f06a651d7c44aef90bd4c591', class: \"input-wrapper\" }, h(\"slot\", { key: '8034e1337a6c950b7725bc9aa46d55de980827bf' })), h(\"slot\", { key: '690de6d3dd3a238ab7a2d3dfea69daa6647e5134', name: \"end\" }), showDetail && (h(\"ion-icon\", { key: '6f386abbde5897fca7aabc850645986e16315213', icon: detailIcon, lazy: false, class: \"item-detail-icon\", part: \"detail-icon\", \"aria-hidden\": \"true\", \"flip-rtl\": detailIcon === chevronForward })), h(\"div\", { key: '4bb8444a5097c032d79f2083f60429b9057e3a5c', class: \"item-inner-highlight\" })), canActivate && mode === 'md' && h(\"ion-ripple-effect\", { key: '31c319e2335efbb92d7dea110a66f347764f2b44' }), h(\"div\", { key: '0fc128b44582af7c8b542c9e346ef88e40fea148', class: \"item-highlight\" })), h(\"div\", { key: '7b84c0a74d148a7fa6af449b6bd06bb45530edf5', class: \"item-bottom\" }, h(\"slot\", { key: '97563a615efd81b21014c41a25989c71ba3ae2cd', name: \"error\" }), h(\"slot\", { key: 'd84a4bb21ed3f13af82f78104ffb06d2b079738e', name: \"helper\" }), counterString && h(\"ion-note\", { key: '34eaa7798c50d2f88afca0dcb1278df05a1800e6', class: \"item-counter\" }, counterString))));\n    }\n    static get delegatesFocus() { return true; }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"button\": [\"buttonChanged\"],\n        \"counterFormatter\": [\"counterFormatterChanged\"]\n    }; }\n};\nItem.style = {\n    ios: IonItemIosStyle0,\n    md: IonItemMdStyle0\n};\n\nconst itemDividerIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--inner-padding-top:0px;--inner-padding-end:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);overflow:hidden;z-index:100;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.item-divider-sticky){position:-webkit-sticky;position:sticky;top:0}.item-divider-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border:0;overflow:hidden}:host-context([dir=rtl]) .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-divider-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-divider-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:hidden}:host{--background:var(--ion-color-step-100, #e6e6e6);--color:var(--ion-color-step-850, #262626);--padding-start:16px;--inner-padding-end:8px;border-radius:0;position:relative;min-height:28px;font-size:1.0625rem;font-weight:600}:host([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:2px;margin-bottom:2px}::slotted(ion-icon[slot=start]),::slotted(ion-icon[slot=end]){margin-top:7px;margin-bottom:7px}::slotted(h1){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px}::slotted(h2){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px}::slotted(h3),::slotted(h4),::slotted(h5),::slotted(h6){margin-left:0;margin-right:0;margin-top:0;margin-bottom:3px}::slotted(p){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.4);font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}::slotted(h2:last-child) ::slotted(h3:last-child),::slotted(h4:last-child),::slotted(h5:last-child),::slotted(h6:last-child),::slotted(p:last-child){margin-bottom:0}\";\nconst IonItemDividerIosStyle0 = itemDividerIosCss;\n\nconst itemDividerMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--inner-padding-top:0px;--inner-padding-end:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);overflow:hidden;z-index:100;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.item-divider-sticky){position:-webkit-sticky;position:sticky;top:0}.item-divider-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border:0;overflow:hidden}:host-context([dir=rtl]) .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-divider-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-divider-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-divider-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:hidden}:host{--background:var(--ion-background-color, #fff);--color:var(--ion-color-step-400, #999999);--padding-start:16px;--inner-padding-end:16px;min-height:30px;border-bottom:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));font-size:0.875rem}::slotted([slot=start]){-webkit-margin-end:32px;margin-inline-end:32px}::slotted([slot=end]){-webkit-margin-start:32px;margin-inline-start:32px}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:13px;margin-bottom:10px}::slotted(ion-icon){color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);font-size:1.7142857143em}:host(.ion-color) ::slotted(ion-icon){color:var(--ion-color-contrast)}::slotted(ion-icon[slot]){margin-top:12px;margin-bottom:12px}::slotted(ion-icon[slot=start]){-webkit-margin-end:32px;margin-inline-end:32px}::slotted(ion-icon[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-note){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-ms-flex-item-align:start;align-self:flex-start;font-size:0.6875rem}::slotted(ion-note[slot]){padding-left:0;padding-right:0;padding-top:18px;padding-bottom:10px}::slotted(ion-note[slot=start]){-webkit-padding-end:16px;padding-inline-end:16px}::slotted(ion-note[slot=end]){-webkit-padding-start:16px;padding-inline-start:16px}::slotted(ion-avatar){width:40px;height:40px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar),::slotted(ion-thumbnail){margin-top:8px;margin-bottom:8px}::slotted(ion-avatar[slot=start]),::slotted(ion-thumbnail[slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(h1){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px}::slotted(h2){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px}::slotted(h3,h4,h5,h6){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px}::slotted(p){margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;color:var(--ion-color-step-600, #666666);font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}\";\nconst IonItemDividerMdStyle0 = itemDividerMdCss;\n\nconst ItemDivider = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.color = undefined;\n        this.sticky = false;\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '5727179159ef2a8879f55435265003e0ec72df3f', class: createColorClasses(this.color, {\n                [mode]: true,\n                'item-divider-sticky': this.sticky,\n                item: true,\n            }) }, h(\"slot\", { key: 'bb7df137e60ca3fa9a50c612e30fbb3ee4c818ad', name: \"start\" }), h(\"div\", { key: '6a25a01271957cfdd8e8dfb6ef76e1eb710380f2', class: \"item-divider-inner\" }, h(\"div\", { key: '554ba681b0f346ed0af03232f8b2e6ca399877d9', class: \"item-divider-wrapper\" }, h(\"slot\", { key: 'f98e20a01f09d0a2e19b7351eb1b4028881a07ab' })), h(\"slot\", { key: '755643b5b8d3463af41b3d0805871073a34386a3', name: \"end\" }))));\n    }\n    get el() { return getElement(this); }\n};\nItemDivider.style = {\n    ios: IonItemDividerIosStyle0,\n    md: IonItemDividerMdStyle0\n};\n\nconst itemGroupIosCss = \"ion-item-group{display:block}\";\nconst IonItemGroupIosStyle0 = itemGroupIosCss;\n\nconst itemGroupMdCss = \"ion-item-group{display:block}\";\nconst IonItemGroupMdStyle0 = itemGroupMdCss;\n\nconst ItemGroup = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '5778fb7e9c6791874b4ff14f0babdae715e322e7', role: \"group\", class: {\n                [mode]: true,\n                // Used internally for styling\n                [`item-group-${mode}`]: true,\n                item: true,\n            } }));\n    }\n};\nItemGroup.style = {\n    ios: IonItemGroupIosStyle0,\n    md: IonItemGroupMdStyle0\n};\n\nconst labelIosCss = \".item.sc-ion-label-ios-h,.item .sc-ion-label-ios-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.item-legacy.sc-ion-label-ios-h,.item-legacy .sc-ion-label-ios-h{white-space:nowrap;overflow:hidden}.ion-color.sc-ion-label-ios-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-ios-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-ios-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-ios-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-ios-h,.item-input .sc-ion-label-ios-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-ios-h,.item-textarea .sc-ion-label-ios-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-ios-h,.item-skeleton-text .sc-ion-label-ios-h{overflow:hidden}.label-fixed.sc-ion-label-ios-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-ios-h,.label-floating.sc-ion-label-ios-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-ios-h{-webkit-transition:none;transition:none}.sc-ion-label-ios-s h1,.sc-ion-label-ios-s h2,.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-ios-h{font-size:0.875rem;line-height:1.5}.label-stacked.sc-ion-label-ios-h{margin-bottom:4px;font-size:0.875rem}.label-floating.sc-ion-label-ios-h{margin-bottom:0;-webkit-transform:translate(0, 29px);transform:translate(0, 29px);-webkit-transform-origin:left top;transform-origin:left top;-webkit-transition:-webkit-transform 150ms ease-in-out;transition:-webkit-transform 150ms ease-in-out;transition:transform 150ms ease-in-out;transition:transform 150ms ease-in-out, -webkit-transform 150ms ease-in-out}[dir=rtl].sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl] .sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl].label-floating.sc-ion-label-ios-h,[dir=rtl] .label-floating.sc-ion-label-ios-h{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.label-floating.sc-ion-label-ios-h:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.item-textarea.label-floating.sc-ion-label-ios-h,.item-textarea .label-floating.sc-ion-label-ios-h{-webkit-transform:translate(0, 28px);transform:translate(0, 28px)}.item-has-focus.label-floating.sc-ion-label-ios-h,.item-has-focus .label-floating.sc-ion-label-ios-h,.item-has-placeholder.sc-ion-label-ios-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-ios-h,.item-has-value.label-floating.sc-ion-label-ios-h,.item-has-value .label-floating.sc-ion-label-ios-h{-webkit-transform:scale(0.82);transform:scale(0.82)}.sc-ion-label-ios-s h1{margin-left:0;margin-right:0;margin-top:3px;margin-bottom:2px;font-size:1.375rem;font-weight:normal}.sc-ion-label-ios-s h2{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.0625rem;font-weight:normal}.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{margin-left:0;margin-right:0;margin-top:0;margin-bottom:3px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-ios-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}.sc-ion-label-ios-s>p{color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.4)}.sc-ion-label-ios-h.in-item-color.sc-ion-label-ios-s>p{color:inherit}.sc-ion-label-ios-s h2:last-child,.sc-ion-label-ios-s h3:last-child,.sc-ion-label-ios-s h4:last-child,.sc-ion-label-ios-s h5:last-child,.sc-ion-label-ios-s h6:last-child,.sc-ion-label-ios-s p:last-child{margin-bottom:0}\";\nconst IonLabelIosStyle0 = labelIosCss;\n\nconst labelMdCss = \".item.sc-ion-label-md-h,.item .sc-ion-label-md-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.item-legacy.sc-ion-label-md-h,.item-legacy .sc-ion-label-md-h{white-space:nowrap;overflow:hidden}.ion-color.sc-ion-label-md-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-md-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-md-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-md-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-md-h,.item-input .sc-ion-label-md-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-md-h,.item-textarea .sc-ion-label-md-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-md-h,.item-skeleton-text .sc-ion-label-md-h{overflow:hidden}.label-fixed.sc-ion-label-md-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-md-h{-webkit-transition:none;transition:none}.sc-ion-label-md-s h1,.sc-ion-label-md-s h2,.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-md-h{line-height:1.5}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:top left;transform-origin:top left}.label-stacked.label-rtl.sc-ion-label-md-h,.label-floating.label-rtl.sc-ion-label-md-h{-webkit-transform-origin:top right;transform-origin:top right}.label-stacked.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.label-floating.sc-ion-label-md-h{-webkit-transform:translateY(96%);transform:translateY(96%);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1)}.ion-focused.label-floating.sc-ion-label-md-h,.ion-focused .label-floating.sc-ion-label-md-h,.item-has-focus.label-floating.sc-ion-label-md-h,.item-has-focus .label-floating.sc-ion-label-md-h,.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h,.item-has-value.label-floating.sc-ion-label-md-h,.item-has-value .label-floating.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75)}.item-fill-outline.ion-focused.label-floating.sc-ion-label-md-h,.item-fill-outline.ion-focused .label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-focus.label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-focus .label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating,.item-fill-outline.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-value.label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-value .label-floating.sc-ion-label-md-h{-webkit-transform:translateY(-6px) scale(0.75);transform:translateY(-6px) scale(0.75);position:relative;max-width:-webkit-min-content;max-width:-moz-min-content;max-width:min-content;background-color:var(--ion-item-background, var(--ion-background-color, #fff));overflow:visible;z-index:3}.item-fill-outline.ion-focused.label-floating.sc-ion-label-md-h::before,.item-fill-outline.ion-focused .label-floating.sc-ion-label-md-h::before,.item-fill-outline.ion-focused.label-floating.sc-ion-label-md-h::after,.item-fill-outline.ion-focused .label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-focus.label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-focus .label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-focus.label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-focus .label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating::before,.item-fill-outline.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating::after,.item-fill-outline.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-value.label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-value .label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-value.label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-value .label-floating.sc-ion-label-md-h::after{position:absolute;width:4px;height:100%;background-color:var(--ion-item-background, var(--ion-background-color, #fff));content:\\\"\\\"}.item-fill-outline.ion-focused.label-floating.sc-ion-label-md-h::before,.item-fill-outline.ion-focused .label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-focus.label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-focus .label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating::before,.item-fill-outline.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-value.label-floating.sc-ion-label-md-h::before,.item-fill-outline.item-has-value .label-floating.sc-ion-label-md-h::before{left:calc(-1 * 4px)}.item-fill-outline.ion-focused.label-floating.sc-ion-label-md-h::after,.item-fill-outline.ion-focused .label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-focus.label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-focus .label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating::after,.item-fill-outline.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-value.label-floating.sc-ion-label-md-h::after,.item-fill-outline.item-has-value .label-floating.sc-ion-label-md-h::after{right:calc(-1 * 4px)}.item-fill-outline.ion-focused.item-has-start-slot.label-floating.sc-ion-label-md-h,.item-fill-outline.ion-focused.item-has-start-slot .label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-focus.item-has-start-slot.label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-focus.item-has-start-slot .label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-placeholder.sc-ion-label-md-h:not(.item-input).item-has-start-slot.label-floating,.item-fill-outline.item-has-placeholder:not(.item-input).item-has-start-slot .label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-value.item-has-start-slot.label-floating.sc-ion-label-md-h,.item-fill-outline.item-has-value.item-has-start-slot .label-floating.sc-ion-label-md-h{-webkit-transform:translateX(-32px) translateY(-6px) scale(0.75);transform:translateX(-32px) translateY(-6px) scale(0.75)}.item-fill-outline.ion-focused.item-has-start-slot.label-floating.label-rtl.sc-ion-label-md-h,.item-fill-outline.ion-focused.item-has-start-slot .label-floating.label-rtl.sc-ion-label-md-h,.item-fill-outline.item-has-focus.item-has-start-slot.label-floating.label-rtl.sc-ion-label-md-h,.item-fill-outline.item-has-focus.item-has-start-slot .label-floating.label-rtl.sc-ion-label-md-h,.item-fill-outline.item-has-placeholder.sc-ion-label-md-h:not(.item-input).item-has-start-slot.label-floating.label-rtl,.item-fill-outline.item-has-placeholder:not(.item-input).item-has-start-slot .label-floating.label-rtl.sc-ion-label-md-h,.item-fill-outline.item-has-value.item-has-start-slot.label-floating.label-rtl.sc-ion-label-md-h,.item-fill-outline.item-has-value.item-has-start-slot .label-floating.label-rtl.sc-ion-label-md-h{-webkit-transform:translateX(calc(-1 * -32px)) translateY(-6px) scale(0.75);transform:translateX(calc(-1 * -32px)) translateY(-6px) scale(0.75)}.ion-focused.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-primary, #3880ff)}.ion-focused.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-contrast)}.item-fill-solid.ion-focused.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-solid.ion-focused.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-solid.ion-focused.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-fill-solid.ion-focused.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.ion-focused.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.ion-focused.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.ion-focused.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.ion-focused.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color),.item-fill-solid.item-has-focus.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-solid.item-has-focus.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-solid.item-has-focus.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-fill-solid.item-has-focus.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.item-has-focus.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.item-has-focus.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.item-has-focus.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-fill-outline.item-has-focus.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-base)}.ion-invalid.ion-touched.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--highlight-color-invalid)}.sc-ion-label-md-s h1{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.5rem;font-weight:normal}.sc-ion-label-md-s h2{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:1rem;font-weight:normal}.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-md-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:1.25rem;text-overflow:inherit;overflow:inherit}.sc-ion-label-md-s>p{color:var(--ion-color-step-600, #666666)}.sc-ion-label-md-h.in-item-color.sc-ion-label-md-s>p{color:inherit}\";\nconst IonLabelMdStyle0 = labelMdCss;\n\nconst Label = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionColor = createEvent(this, \"ionColor\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.inRange = false;\n        this.color = undefined;\n        this.position = undefined;\n        this.noAnimate = false;\n    }\n    componentWillLoad() {\n        this.inRange = !!this.el.closest('ion-range');\n        this.noAnimate = this.position === 'floating';\n        this.emitStyle();\n        this.emitColor();\n    }\n    componentDidLoad() {\n        if (this.noAnimate) {\n            setTimeout(() => {\n                this.noAnimate = false;\n            }, 1000);\n        }\n    }\n    colorChanged() {\n        this.emitColor();\n    }\n    positionChanged() {\n        this.emitStyle();\n    }\n    emitColor() {\n        const { color } = this;\n        this.ionColor.emit({\n            'item-label-color': color !== undefined,\n            [`ion-color-${color}`]: color !== undefined,\n        });\n    }\n    emitStyle() {\n        const { inRange, position } = this;\n        // If the label is inside of a range we don't want\n        // to override the classes added by the label that\n        // is a direct child of the item\n        if (!inRange) {\n            this.ionStyle.emit({\n                label: true,\n                [`label-${position}`]: position !== undefined,\n            });\n        }\n    }\n    render() {\n        const position = this.position;\n        const mode = getIonMode(this);\n        return (h(Host, { key: '72ad4ba5c1137ae0130e421346668e436ea53bf8', class: createColorClasses(this.color, {\n                [mode]: true,\n                'in-item-color': hostContext('ion-item.ion-color', this.el),\n                [`label-${position}`]: position !== undefined,\n                [`label-no-animate`]: this.noAnimate,\n                'label-rtl': document.dir === 'rtl',\n            }) }));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"color\": [\"colorChanged\"],\n        \"position\": [\"positionChanged\"]\n    }; }\n};\nLabel.style = {\n    ios: IonLabelIosStyle0,\n    md: IonLabelMdStyle0\n};\n\nconst listIosCss = \"ion-list{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;contain:content;list-style-type:none}ion-list.list-inset{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:hidden}.list-ios{background:var(--ion-item-background, var(--ion-background-color, #fff))}.list-ios.list-inset{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px;border-radius:10px}.list-ios.list-inset ion-item:only-child,.list-ios.list-inset ion-item:not(:only-of-type):last-of-type,.list-ios.list-inset ion-item-sliding:last-of-type ion-item{--border-width:0;--inner-border-width:0}.list-ios.list-inset+ion-list.list-inset{margin-top:0}.list-ios-lines-none .item-lines-default{--inner-border-width:0px;--border-width:0px}.list-ios-lines-full .item-lines-default{--inner-border-width:0px;--border-width:0 0 0.55px 0}.list-ios-lines-inset .item-lines-default{--inner-border-width:0 0 0.55px 0;--border-width:0px}ion-card .list-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\nconst IonListIosStyle0 = listIosCss;\n\nconst listMdCss = \"ion-list{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;contain:content;list-style-type:none}ion-list.list-inset{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:hidden}.list-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:8px;padding-bottom:8px;background:var(--ion-item-background, var(--ion-background-color, #fff))}@supports (inset-inline-start: 0){.list-md>.input:last-child::after{inset-inline-start:0}}@supports not (inset-inline-start: 0){.list-md>.input:last-child::after{left:0}:host-context([dir=rtl]) .list-md>.input:last-child::after{left:unset;right:unset;right:0}[dir=rtl] .list-md>.input:last-child::after{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.list-md>.input:last-child::after:dir(rtl){left:unset;right:unset;right:0}}}.list-md.list-inset{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px;border-radius:2px}.list-md.list-inset ion-item:not(:only-of-type):last-of-type,.list-md.list-inset ion-item-sliding:last-of-type ion-item{--border-width:0;--inner-border-width:0}.list-md.list-inset ion-item:only-child{--border-width:0;--inner-border-width:0}.list-md.list-inset+ion-list.list-inset{margin-top:0}.list-md-lines-none .item-lines-default{--inner-border-width:0px;--border-width:0px}.list-md-lines-full .item-lines-default{--inner-border-width:0px;--border-width:0 0 1px 0}.list-md-lines-inset .item-lines-default{--inner-border-width:0 0 1px 0;--border-width:0px}ion-card .list-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\nconst IonListMdStyle0 = listMdCss;\n\nconst List = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.lines = undefined;\n        this.inset = false;\n    }\n    /**\n     * If `ion-item-sliding` are used inside the list, this method closes\n     * any open sliding item.\n     *\n     * Returns `true` if an actual `ion-item-sliding` is closed.\n     */\n    async closeSlidingItems() {\n        const item = this.el.querySelector('ion-item-sliding');\n        if (item === null || item === void 0 ? void 0 : item.closeOpened) {\n            return item.closeOpened();\n        }\n        return false;\n    }\n    render() {\n        const mode = getIonMode(this);\n        const { lines, inset } = this;\n        return (h(Host, { key: '3df401155114c7a39c81f201bf8a181d07e8d4c8', role: \"list\", class: {\n                [mode]: true,\n                // Used internally for styling\n                [`list-${mode}`]: true,\n                'list-inset': inset,\n                [`list-lines-${lines}`]: lines !== undefined,\n                [`list-${mode}-lines-${lines}`]: lines !== undefined,\n            } }));\n    }\n    get el() { return getElement(this); }\n};\nList.style = {\n    ios: IonListIosStyle0,\n    md: IonListMdStyle0\n};\n\nconst listHeaderIosCss = \":host{--border-style:solid;--border-width:0;--inner-border-width:0;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:40px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);color:var(--color);overflow:hidden}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.list-header-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex:1 1 auto;flex:1 1 auto}:host(.list-header-lines-inset),:host(.list-header-lines-none){--border-width:0}:host(.list-header-lines-full),:host(.list-header-lines-none){--inner-border-width:0}:host{--background:transparent;--color:var(--ion-color-step-850, #262626);--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));padding-right:var(--ion-safe-area-right);padding-left:calc(var(--ion-safe-area-left, 0px) + 16px);position:relative;-ms-flex-align:end;align-items:flex-end;font-size:min(1.375rem, 56.1px);font-weight:700;letter-spacing:0}:host-context([dir=rtl]){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}}::slotted(ion-button),::slotted(ion-label){margin-top:29px;margin-bottom:6px}::slotted(ion-button){--padding-top:0;--padding-bottom:0;-webkit-margin-start:3px;margin-inline-start:3px;-webkit-margin-end:3px;margin-inline-end:3px;min-height:1.4em}:host(.list-header-lines-full){--border-width:0 0 0.55px 0}:host(.list-header-lines-inset){--inner-border-width:0 0 0.55px 0}\";\nconst IonListHeaderIosStyle0 = listHeaderIosCss;\n\nconst listHeaderMdCss = \":host{--border-style:solid;--border-width:0;--inner-border-width:0;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:40px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);color:var(--color);overflow:hidden}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.list-header-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex:1 1 auto;flex:1 1 auto}:host(.list-header-lines-inset),:host(.list-header-lines-none){--border-width:0}:host(.list-header-lines-full),:host(.list-header-lines-none){--inner-border-width:0}:host{--background:transparent;--color:var(--ion-text-color, #000);--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));padding-right:var(--ion-safe-area-right);padding-left:calc(var(--ion-safe-area-left, 0px) + 16px);min-height:45px;font-size:0.875rem}:host-context([dir=rtl]){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}@supports selector(:dir(rtl)){:host(:dir(rtl)){padding-right:calc(var(--ion-safe-area-right, 0px) + 16px);padding-left:var(--ion-safe-area-left)}}:host(.list-header-lines-full){--border-width:0 0 1px 0}:host(.list-header-lines-inset){--inner-border-width:0 0 1px 0}\";\nconst IonListHeaderMdStyle0 = listHeaderMdCss;\n\nconst ListHeader = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.color = undefined;\n        this.lines = undefined;\n    }\n    render() {\n        const { lines } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'e5fabb3ae91e6fe47c89273d1d2dba5902f77f94', class: createColorClasses(this.color, {\n                [mode]: true,\n                [`list-header-lines-${lines}`]: lines !== undefined,\n            }) }, h(\"div\", { key: 'a9cfdaa436267fbabb0d618c3932849c1b77fbd2', class: \"list-header-inner\" }, h(\"slot\", { key: '8ed806fd58f8f2265c5bf466886086e88ada93cc' }))));\n    }\n};\nListHeader.style = {\n    ios: IonListHeaderIosStyle0,\n    md: IonListHeaderMdStyle0\n};\n\nconst noteIosCss = \":host{color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-base)}:host{--color:var(--ion-color-step-350, #a6a6a6);font-size:max(14px, 1rem)}\";\nconst IonNoteIosStyle0 = noteIosCss;\n\nconst noteMdCss = \":host{color:var(--color);font-family:var(--ion-font-family, inherit);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-base)}:host{--color:var(--ion-color-step-600, #666666);font-size:0.875rem}\";\nconst IonNoteMdStyle0 = noteMdCss;\n\nconst Note = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.color = undefined;\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '79a17a318ec6e8326c9741b4a9bb4598acdc225e', class: createColorClasses(this.color, {\n                [mode]: true,\n            }) }, h(\"slot\", { key: '5adeaccfabb4bee7b84ea5c5de804bd255b29255' })));\n    }\n};\nNote.style = {\n    ios: IonNoteIosStyle0,\n    md: IonNoteMdStyle0\n};\n\nconst skeletonTextCss = \":host{--background:rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065);border-radius:var(--border-radius, inherit);display:block;width:100%;height:inherit;margin-top:4px;margin-bottom:4px;background:var(--background);line-height:10px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;pointer-events:none}span{display:inline-block}:host(.in-media){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;height:100%}:host(.skeleton-text-animated){position:relative;background:-webkit-gradient(linear, left top, right top, color-stop(8%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065)), color-stop(18%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.135)), color-stop(33%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065)));background:linear-gradient(to right, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065) 8%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.135) 18%, rgba(var(--background-rgb, var(--ion-text-color-rgb, 0, 0, 0)), 0.065) 33%);background-size:800px 104px;-webkit-animation-duration:1s;animation-duration:1s;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-name:shimmer;animation-name:shimmer;-webkit-animation-timing-function:linear;animation-timing-function:linear}@-webkit-keyframes shimmer{0%{background-position:-400px 0}100%{background-position:400px 0}}@keyframes shimmer{0%{background-position:-400px 0}100%{background-position:400px 0}}\";\nconst IonSkeletonTextStyle0 = skeletonTextCss;\n\nconst SkeletonText = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.animated = false;\n    }\n    componentWillLoad() {\n        this.emitStyle();\n    }\n    emitStyle() {\n        // The emitted property is used by item in order\n        // to add the item-skeleton-text class which applies\n        // overflow: hidden to its label\n        const style = {\n            'skeleton-text': true,\n        };\n        this.ionStyle.emit(style);\n    }\n    render() {\n        const animated = this.animated && config.getBoolean('animated', true);\n        const inMedia = hostContext('ion-avatar', this.el) || hostContext('ion-thumbnail', this.el);\n        const mode = getIonMode(this);\n        return (h(Host, { key: '4dab0fd2de666de12ad8f6dc6ed1e1de0be67ddd', class: {\n                [mode]: true,\n                'skeleton-text-animated': animated,\n                'in-media': inMedia,\n            } }, h(\"span\", { key: 'f8f908ec24d65e63b14d9a54640a5f18f0fa8fa5' }, \"\\u00A0\")));\n    }\n    get el() { return getElement(this); }\n};\nSkeletonText.style = IonSkeletonTextStyle0;\n\nexport { Item as ion_item, ItemDivider as ion_item_divider, ItemGroup as ion_item_group, Label as ion_label, List as ion_list, ListHeader as ion_list_header, Note as ion_note, SkeletonText as ion_skeleton_text };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,WAAW,QAAQ,qBAAqB;AAC9H,SAASC,CAAC,IAAIC,iBAAiB,EAAEZ,CAAC,IAAIa,GAAG,QAAQ,uBAAuB;AACxE,SAASC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,aAAa,QAAQ,qBAAqB;AAC9E,SAASb,CAAC,IAAIc,WAAW,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,OAAO,QAAQ,qBAAqB;AAC7F,SAASD,CAAC,IAAIE,cAAc,QAAQ,qBAAqB;AACzD,SAASC,CAAC,IAAIC,UAAU,EAAEN,CAAC,IAAIO,MAAM,QAAQ,4BAA4B;AAEzE,MAAMC,UAAU,GAAG,yldAAyld;AAC5md,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,SAAS,GAAG,snsBAAsnsB;AACxosB,MAAMC,eAAe,GAAGD,SAAS;AAEjC,MAAME,IAAI,GAAG,MAAM;EACfC,WAAWA,CAACC,OAAO,EAAE;IACjBhC,gBAAgB,CAAC,IAAI,EAAEgC,OAAO,CAAC;IAC/B,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACC,uBAAuB,GAAG,CAAC,CAAC;IACjC,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,MAAM,GAAGF,SAAS;IACvB,IAAI,CAACG,UAAU,GAAGrB,cAAc;IAChC,IAAI,CAACsB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAGL,SAAS;IACzB,IAAI,CAACM,IAAI,GAAGN,SAAS;IACrB,IAAI,CAACO,KAAK,GAAGP,SAAS;IACtB,IAAI,CAACQ,IAAI,GAAGR,SAAS;IACrB,IAAI,CAACS,GAAG,GAAGT,SAAS;IACpB,IAAI,CAACU,KAAK,GAAGV,SAAS;IACtB,IAAI,CAACW,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,eAAe,GAAGZ,SAAS;IAChC,IAAI,CAACa,eAAe,GAAG,SAAS;IAChC,IAAI,CAACC,MAAM,GAAGd,SAAS;IACvB,IAAI,CAACe,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACC,gBAAgB,GAAGhB,SAAS;IACjC,IAAI,CAACiB,aAAa,GAAGjB,SAAS;EAClC;EACAkB,aAAaA,CAAA,EAAG;IACZ;IACA,IAAI,CAACpB,SAAS,GAAG,IAAI,CAACqB,WAAW,CAAC,CAAC;EACvC;EACAC,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC;EAClD;EACAC,cAAcA,CAACC,EAAE,EAAE;IACf,IAAI,IAAI,CAACb,OAAO,IAAIa,EAAE,CAACV,MAAM,KAAK,IAAI,CAACQ,aAAa,CAAC,CAAC,EAAE;MACpD,IAAI,CAACD,mBAAmB,CAACG,EAAE,CAACV,MAAM,CAAC;IACvC;EACJ;EACAW,iBAAiBA,CAACD,EAAE,EAAE;IAClB,MAAM;MAAEzB;IAAM,CAAC,GAAG,IAAI;IACtB;IACA;IACA;IACA,IAAIA,KAAK,KAAKC,SAAS,EAAE;MACrB,IAAI,CAACP,gBAAgB,GAAG+B,EAAE,CAACtB,MAAM;IACrC;EACJ;EACAwB,SAASA,CAACF,EAAE,EAAE;IACVA,EAAE,CAACG,eAAe,CAAC,CAAC;IACpB,MAAMC,OAAO,GAAGJ,EAAE,CAACV,MAAM,CAACc,OAAO;IACjC,MAAMC,aAAa,GAAGL,EAAE,CAACtB,MAAM;IAC/B,MAAM4B,SAAS,GAAG,CAAC,CAAC;IACpB,MAAMC,WAAW,GAAG,IAAI,CAACrC,UAAU,CAACsC,GAAG,CAACJ,OAAO,CAAC,IAAI,CAAC,CAAC;IACtD,IAAIK,cAAc,GAAG,KAAK;IAC1BC,MAAM,CAACC,IAAI,CAACN,aAAa,CAAC,CAACO,OAAO,CAAEC,GAAG,IAAK;MACxC,IAAIR,aAAa,CAACQ,GAAG,CAAC,EAAE;QACpB,MAAMC,OAAO,GAAG,QAAQD,GAAG,EAAE;QAC7B,IAAI,CAACN,WAAW,CAACO,OAAO,CAAC,EAAE;UACvBL,cAAc,GAAG,IAAI;QACzB;QACAH,SAAS,CAACQ,OAAO,CAAC,GAAG,IAAI;MAC7B;IACJ,CAAC,CAAC;IACF,IAAI,CAACL,cAAc,IAAIC,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAACS,MAAM,KAAKL,MAAM,CAACC,IAAI,CAACJ,WAAW,CAAC,CAACQ,MAAM,EAAE;MACtFN,cAAc,GAAG,IAAI;IACzB;IACA,IAAIA,cAAc,EAAE;MAChB,IAAI,CAACvC,UAAU,CAAC8C,GAAG,CAACZ,OAAO,EAAEE,SAAS,CAAC;MACvCpE,WAAW,CAAC,IAAI,CAAC;IACrB;EACJ;EACA+E,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC9B,OAAO,EAAE;MACd,IAAI,CAACU,mBAAmB,CAAC,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC;IAClD;IACA,IAAI,CAACoB,UAAU,CAAC,CAAC;EACrB;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC/C,uBAAuB,GAAGzB,iBAAiB,CAAC,IAAI,CAACyE,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC;EAC7E;EACAC,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAED,EAAE;MAAEjC,OAAO;MAAEK,gBAAgB;MAAEV,IAAI;MAAEC;IAAM,CAAC,GAAG,IAAI;IAC3D,MAAMuC,aAAa,GAAGF,EAAE,CAACG,aAAa,CAAC,iBAAiB,CAAC,KAAK,IAAI;IAClE,IAAID,aAAa,EAAE;MACfxE,eAAe,CAAC,iHAAiH,EAAEsE,EAAE,CAAC;IAC1I;IACA,MAAMI,YAAY,GAAGJ,EAAE,CAACG,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI;IAChE,IAAIC,YAAY,EAAE;MACd1E,eAAe,CAAC,+GAA+G,EAAEsE,EAAE,CAAC;IACxI;IACA,IAAIjC,OAAO,KAAK,IAAI,EAAE;MAClBrC,eAAe,CAAC,mHAAmH,EAAEsE,EAAE,CAAC;IAC5I;IACA,IAAI5B,gBAAgB,KAAKhB,SAAS,EAAE;MAChC1B,eAAe,CAAC,qIAAqI,EAAEsE,EAAE,CAAC;IAC9J;IACA,IAAItC,IAAI,KAAKN,SAAS,EAAE;MACpB1B,eAAe,CAAC,6GAA6G,EAAEsE,EAAE,CAAC;IACtI;IACA,IAAIrC,KAAK,KAAKP,SAAS,EAAE;MACrB1B,eAAe,CAAC,+GAA+G,EAAEsE,EAAE,CAAC;IACxI;IACAxE,GAAG,CAAC,MAAM;MACN,IAAI,CAAC6E,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACnD,SAAS,GAAG,IAAI,CAACqB,WAAW,CAAC,CAAC;IACvC,CAAC,CAAC;EACN;EACA;EACA;EACA;EACA8B,iBAAiBA,CAAA,EAAG;IAChB;IACA,MAAMC,MAAM,GAAG,IAAI,CAACN,EAAE,CAACO,gBAAgB,CAAC,mDAAmD,CAAC;IAC5F;IACA;IACA;IACA,MAAMC,MAAM,GAAG,IAAI,CAACR,EAAE,CAACO,gBAAgB,CAAC,4EAA4E,CAAC;IACrH;IACA,MAAME,UAAU,GAAG,IAAI,CAACT,EAAE,CAACO,gBAAgB,CAAC,mCAAmC,CAAC;IAChF;IACA;IACA,IAAI,CAACtD,cAAc,GACfqD,MAAM,CAACX,MAAM,GAAGa,MAAM,CAACb,MAAM,GAAG,CAAC,IAC7BW,MAAM,CAACX,MAAM,GAAGc,UAAU,CAACd,MAAM,GAAG,CAAC,IACpCW,MAAM,CAACX,MAAM,GAAG,CAAC,IAAI,IAAI,CAACe,WAAW,CAAC,CAAE;EACrD;EACA;EACA;EACA;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,MAAMH,MAAM,GAAG,IAAI,CAACR,EAAE,CAACO,gBAAgB,CAAC,mDAAmD,CAAC;IAC5F,OAAOC,MAAM,CAACb,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC1C,cAAc;EACtD;EACA;EACA;EACAyD,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC9C,IAAI,KAAKR,SAAS,IAAI,IAAI,CAACC,MAAM;EACjD;EACAuD,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACF,WAAW,CAAC,CAAC,IAAI,IAAI,CAACC,QAAQ,CAAC,CAAC;EAChD;EACApC,WAAWA,CAAA,EAAG;IACV,MAAMsC,cAAc,GAAG,IAAI,CAACb,EAAE,CAACG,aAAa,CAAC,gBAAgB,CAAC;IAC9D,OAAO,IAAI,CAACS,WAAW,CAAC,CAAC,IAAIC,cAAc,KAAK,IAAI;EACxD;EACAnC,aAAaA,CAAA,EAAG;IACZ,MAAM8B,MAAM,GAAG,IAAI,CAACR,EAAE,CAACO,gBAAgB,CAAC,yBAAyB,CAAC;IAClE,OAAOC,MAAM,CAAC,CAAC,CAAC;EACpB;EACA/B,mBAAmBA,CAACqC,OAAO,EAAE;IACzB,IAAIC,EAAE,EAAEC,EAAE;IACV,MAAM;MAAEjD,OAAO;MAAEK,gBAAgB;MAAE6C;IAAwB,CAAC,GAAG,IAAI;IACnE,IAAIlD,OAAO,IAAI,CAAC,IAAI,CAACd,cAAc,IAAI,CAAC6D,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACI,SAAS,MAAM9D,SAAS,EAAE;MACxH,MAAMuC,MAAM,GAAG,CAACqB,EAAE,GAAG,CAACD,EAAE,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACK,KAAK,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,QAAQ,CAAC,CAAC,CAACzB,MAAM,MAAM,IAAI,IAAIqB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;MACzL,IAAI5C,gBAAgB,KAAKhB,SAAS,EAAE;QAChC,IAAI,CAACiB,aAAa,GAAG4C,uBAAuB,CAACtB,MAAM,EAAEmB,OAAO,CAACI,SAAS,CAAC;MAC3E,CAAC,MACI;QACD,IAAI;UACA,IAAI,CAAC7C,aAAa,GAAGD,gBAAgB,CAACuB,MAAM,EAAEmB,OAAO,CAACI,SAAS,CAAC;QACpE,CAAC,CACD,OAAOG,CAAC,EAAE;UACNzF,aAAa,CAAC,2CAA2C,EAAEyF,CAAC,CAAC;UAC7D;UACA,IAAI,CAAChD,aAAa,GAAG4C,uBAAuB,CAACtB,MAAM,EAAEmB,OAAO,CAACI,SAAS,CAAC;QAC3E;MACJ;IACJ;EACJ;EACAD,uBAAuBA,CAACtB,MAAM,EAAEuB,SAAS,EAAE;IACvC,OAAO,GAAGvB,MAAM,MAAMuB,SAAS,EAAE;EACrC;EACApB,UAAUA,CAAA,EAAG;IACT,MAAMwB,OAAO,GAAG,IAAI,CAACtB,EAAE,CAACG,aAAa,CAAC,gBAAgB,CAAC;IACvD,IAAImB,OAAO,KAAK,IAAI,EAAE;MAClB,IAAI,CAACtB,EAAE,CAACuB,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAChD;EACJ;EACAC,mBAAmBA,CAAA,EAAG;IAClB,MAAMC,QAAQ,GAAG,IAAI,CAAC1B,EAAE,CAACO,gBAAgB,CAAC,iHAAiH,CAAC;IAC5J,OAAOmB,QAAQ,CAAC,CAAC,CAAC;EACtB;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEtD,aAAa;MAAEf,MAAM;MAAEC,UAAU;MAAEE,QAAQ;MAAEC,IAAI;MAAEb,gBAAgB;MAAEiB,KAAK;MAAEN,QAAQ;MAAEI,IAAI;MAAEC,GAAG;MAAEF,KAAK;MAAEO,MAAM;MAAEF,eAAe;MAAEC,eAAe;MAAEjB,uBAAuB;MAAEC;IAAgB,CAAC,GAAG,IAAI;IAC3M,MAAMkC,WAAW,GAAG,CAAC,CAAC;IACtB,MAAMyC,IAAI,GAAGxF,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMyF,SAAS,GAAG,IAAI,CAACnB,WAAW,CAAC,CAAC;IACpC,MAAME,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC,CAAC;IACtC,MAAMkB,OAAO,GAAGD,SAAS,GAAIjE,IAAI,KAAKR,SAAS,GAAG,QAAQ,GAAG,GAAG,GAAI,KAAK;IACzE,MAAM2E,KAAK,GAAGD,OAAO,KAAK,QAAQ,GAC5B;MAAE3D,IAAI,EAAE,IAAI,CAACA;IAAK,CAAC,GACnB;MACEV,QAAQ;MACRG,IAAI;MACJC,GAAG;MACHK;IACJ,CAAC;IACL,IAAI8D,OAAO,GAAG,CAAC,CAAC;IAChB,MAAMC,gBAAgB,GAAG,IAAI,CAACR,mBAAmB,CAAC,CAAC;IACnD;IACA;IACA,IAAII,SAAS,IAAKI,gBAAgB,KAAK7E,SAAS,IAAI,CAACH,cAAe,EAAE;MAClE+E,OAAO,GAAG;QACNE,OAAO,EAAGtD,EAAE,IAAK;UACb,IAAIiD,SAAS,EAAE;YACX5F,OAAO,CAAC2B,IAAI,EAAEgB,EAAE,EAAEX,eAAe,EAAED,eAAe,CAAC;UACvD;UACA,IAAIiE,gBAAgB,KAAK7E,SAAS,IAAI,CAACH,cAAc,EAAE;YACnD,MAAMkF,IAAI,GAAGvD,EAAE,CAACwD,YAAY,CAAC,CAAC;YAC9B,MAAMlE,MAAM,GAAGiE,IAAI,CAAC,CAAC,CAAC;YACtB,IAAIvD,EAAE,CAACyD,SAAS,EAAE;cACd;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;cAC4B,MAAMC,uBAAuB,GAAG,IAAI,CAACtC,EAAE,CAACuC,UAAU,CAACC,QAAQ,CAACtE,MAAM,CAAC;cACnE,IAAIoE,uBAAuB,EAAE;gBACzBL,gBAAgB,CAACQ,KAAK,CAAC,CAAC;cAC5B;YACJ;UACJ;QACJ;MACJ,CAAC;IACL;IACA,MAAMC,UAAU,GAAGpF,MAAM,KAAKF,SAAS,GAAGE,MAAM,GAAGsE,IAAI,KAAK,KAAK,IAAIC,SAAS;IAC9E,IAAI,CAAC/E,UAAU,CAAC0C,OAAO,CAAE2B,KAAK,IAAK;MAC/B7B,MAAM,CAACqD,MAAM,CAACxD,WAAW,EAAEgC,KAAK,CAAC;IACrC,CAAC,CAAC;IACF,MAAMyB,YAAY,GAAGpF,QAAQ,IAAI2B,WAAW,CAAC,2BAA2B,CAAC,GAAG,MAAM,GAAG,IAAI;IACzF,MAAM0D,SAAS,GAAGnF,IAAI,IAAI,MAAM;IAChC,MAAMoF,MAAM,GAAGjH,WAAW,CAAC,UAAU,EAAE,IAAI,CAACmE,EAAE,CAAC,IAAI,CAACnE,WAAW,CAAC,iBAAiB,EAAE,IAAI,CAACmE,EAAE,CAAC;IAC3F,OAAQjF,CAAC,CAACE,IAAI,EAAE;MAAEwE,GAAG,EAAE,0CAA0C;MAAE,eAAe,EAAEmD,YAAY;MAAEG,KAAK,EAAEzD,MAAM,CAACqD,MAAM,CAACrD,MAAM,CAACqD,MAAM,CAACrD,MAAM,CAACqD,MAAM,CAAC,CAAC,CAAC,EAAExD,WAAW,CAAC,EAAEtC,gBAAgB,CAAC,EAAEd,kBAAkB,CAAC,IAAI,CAACoB,KAAK,EAAE;QAC/M6F,IAAI,EAAE,IAAI;QACV,CAACpB,IAAI,GAAG,IAAI;QACZ,oBAAoB,EAAE9D,KAAK,KAAKV,SAAS;QACzC,CAAC,cAAcU,KAAK,EAAE,GAAGA,KAAK,KAAKV,SAAS;QAC5C,CAAC,aAAayF,SAAS,EAAE,GAAG,IAAI;QAChC,CAAC,cAAclF,KAAK,EAAE,GAAGA,KAAK,KAAKP,SAAS;QAC5C,8BAA8B,EAAE6E,gBAAgB,KAAK7E,SAAS;QAC9D,eAAe,EAAEI,QAAQ;QACzB,SAAS,EAAEsF,MAAM;QACjB,sBAAsB,EAAE,IAAI,CAAC7F,cAAc;QAC3C,iBAAiB,EAAE2D,WAAW;QAC9B,eAAe,EAAE,IAAI,CAAC1D,SAAS;QAC/B,UAAU,EAAE+F,QAAQ,CAACC,GAAG,KAAK;MACjC,CAAC,CAAC,CAAC;MAAEC,IAAI,EAAEL,MAAM,GAAG,UAAU,GAAG;IAAK,CAAC,EAAE/H,CAAC,CAAC+G,OAAO,EAAExC,MAAM,CAACqD,MAAM,CAAC;MAAElD,GAAG,EAAE;IAA2C,CAAC,EAAEsC,KAAK,EAAE/E,uBAAuB,EAAE;MAAE+F,KAAK,EAAE,aAAa;MAAEK,IAAI,EAAE,QAAQ;MAAE5F,QAAQ,EAAEA;IAAS,CAAC,EAAEwE,OAAO,CAAC,EAAEjH,CAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAE4D,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAEtI,CAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEsD,KAAK,EAAE;IAAa,CAAC,EAAEhI,CAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEsD,KAAK,EAAE;IAAgB,CAAC,EAAEhI,CAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAE1E,CAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAE4D,IAAI,EAAE;IAAM,CAAC,CAAC,EAAEX,UAAU,IAAK3H,CAAC,CAAC,UAAU,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAE6D,IAAI,EAAE/F,UAAU;MAAEgG,IAAI,EAAE,KAAK;MAAER,KAAK,EAAE,kBAAkB;MAAEK,IAAI,EAAE,aAAa;MAAE,aAAa,EAAE,MAAM;MAAE,UAAU,EAAE7F,UAAU,KAAKrB;IAAe,CAAC,CAAE,EAAEnB,CAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEsD,KAAK,EAAE;IAAuB,CAAC,CAAC,CAAC,EAAEnC,WAAW,IAAIgB,IAAI,KAAK,IAAI,IAAI7G,CAAC,CAAC,mBAAmB,EAAE;MAAE0E,GAAG,EAAE;IAA2C,CAAC,CAAC,EAAE1E,CAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEsD,KAAK,EAAE;IAAiB,CAAC,CAAC,CAAC,EAAEhI,CAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEsD,KAAK,EAAE;IAAc,CAAC,EAAEhI,CAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAE4D,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAEtI,CAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAE4D,IAAI,EAAE;IAAS,CAAC,CAAC,EAAEhF,aAAa,IAAItD,CAAC,CAAC,UAAU,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEsD,KAAK,EAAE;IAAe,CAAC,EAAE1E,aAAa,CAAC,CAAC,CAAC;EACr+C;EACA,WAAWmF,cAAcA,CAAA,EAAG;IAAE,OAAO,IAAI;EAAE;EAC3C,IAAIxD,EAAEA,CAAA,EAAG;IAAE,OAAO7E,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWsI,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,QAAQ,EAAE,CAAC,eAAe,CAAC;MAC3B,kBAAkB,EAAE,CAAC,yBAAyB;IAClD,CAAC;EAAE;AACP,CAAC;AACD/G,IAAI,CAACgH,KAAK,GAAG;EACTC,GAAG,EAAEpH,gBAAgB;EACrBqH,EAAE,EAAEnH;AACR,CAAC;AAED,MAAMoH,iBAAiB,GAAG,wgHAAwgH;AACliH,MAAMC,uBAAuB,GAAGD,iBAAiB;AAEjD,MAAME,gBAAgB,GAAG,kiJAAkiJ;AAC3jJ,MAAMC,sBAAsB,GAAGD,gBAAgB;AAE/C,MAAME,WAAW,GAAG,MAAM;EACtBtH,WAAWA,CAACC,OAAO,EAAE;IACjBhC,gBAAgB,CAAC,IAAI,EAAEgC,OAAO,CAAC;IAC/B,IAAI,CAACO,KAAK,GAAGC,SAAS;IACtB,IAAI,CAAC8G,MAAM,GAAG,KAAK;EACvB;EACAvC,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGxF,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQrB,CAAC,CAACE,IAAI,EAAE;MAAEwE,GAAG,EAAE,0CAA0C;MAAEsD,KAAK,EAAEhH,kBAAkB,CAAC,IAAI,CAACoB,KAAK,EAAE;QACjG,CAACyE,IAAI,GAAG,IAAI;QACZ,qBAAqB,EAAE,IAAI,CAACsC,MAAM;QAClClB,IAAI,EAAE;MACV,CAAC;IAAE,CAAC,EAAEjI,CAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAE4D,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAEtI,CAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEsD,KAAK,EAAE;IAAqB,CAAC,EAAEhI,CAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEsD,KAAK,EAAE;IAAuB,CAAC,EAAEhI,CAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAE1E,CAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAE4D,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,CAAC;EACna;EACA,IAAIrD,EAAEA,CAAA,EAAG;IAAE,OAAO7E,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD8I,WAAW,CAACP,KAAK,GAAG;EAChBC,GAAG,EAAEG,uBAAuB;EAC5BF,EAAE,EAAEI;AACR,CAAC;AAED,MAAMG,eAAe,GAAG,+BAA+B;AACvD,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,cAAc,GAAG,+BAA+B;AACtD,MAAMC,oBAAoB,GAAGD,cAAc;AAE3C,MAAME,SAAS,GAAG,MAAM;EACpB5H,WAAWA,CAACC,OAAO,EAAE;IACjBhC,gBAAgB,CAAC,IAAI,EAAEgC,OAAO,CAAC;EACnC;EACA+E,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGxF,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQrB,CAAC,CAACE,IAAI,EAAE;MAAEwE,GAAG,EAAE,0CAA0C;MAAE0D,IAAI,EAAE,OAAO;MAAEJ,KAAK,EAAE;QACjF,CAACnB,IAAI,GAAG,IAAI;QACZ;QACA,CAAC,cAAcA,IAAI,EAAE,GAAG,IAAI;QAC5BoB,IAAI,EAAE;MACV;IAAE,CAAC,CAAC;EACZ;AACJ,CAAC;AACDuB,SAAS,CAACb,KAAK,GAAG;EACdC,GAAG,EAAES,qBAAqB;EAC1BR,EAAE,EAAEU;AACR,CAAC;AAED,MAAME,WAAW,GAAG,08HAA08H;AAC99H,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,UAAU,GAAG,0iYAA0iY;AAC7jY,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,KAAK,GAAG,MAAM;EAChBjI,WAAWA,CAACC,OAAO,EAAE;IACjBhC,gBAAgB,CAAC,IAAI,EAAEgC,OAAO,CAAC;IAC/B,IAAI,CAACiI,QAAQ,GAAGxJ,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACyJ,QAAQ,GAAGzJ,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC0J,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC5H,KAAK,GAAGC,SAAS;IACtB,IAAI,CAAC4H,QAAQ,GAAG5H,SAAS;IACzB,IAAI,CAAC6H,SAAS,GAAG,KAAK;EAC1B;EACAlF,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACgF,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC/E,EAAE,CAACkF,OAAO,CAAC,WAAW,CAAC;IAC7C,IAAI,CAACD,SAAS,GAAG,IAAI,CAACD,QAAQ,KAAK,UAAU;IAC7C,IAAI,CAACG,SAAS,CAAC,CAAC;IAChB,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAnF,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACgF,SAAS,EAAE;MAChBI,UAAU,CAAC,MAAM;QACb,IAAI,CAACJ,SAAS,GAAG,KAAK;MAC1B,CAAC,EAAE,IAAI,CAAC;IACZ;EACJ;EACAK,YAAYA,CAAA,EAAG;IACX,IAAI,CAACF,SAAS,CAAC,CAAC;EACpB;EACAG,eAAeA,CAAA,EAAG;IACd,IAAI,CAACJ,SAAS,CAAC,CAAC;EACpB;EACAC,SAASA,CAAA,EAAG;IACR,MAAM;MAAEjI;IAAM,CAAC,GAAG,IAAI;IACtB,IAAI,CAAC0H,QAAQ,CAACW,IAAI,CAAC;MACf,kBAAkB,EAAErI,KAAK,KAAKC,SAAS;MACvC,CAAC,aAAaD,KAAK,EAAE,GAAGA,KAAK,KAAKC;IACtC,CAAC,CAAC;EACN;EACA+H,SAASA,CAAA,EAAG;IACR,MAAM;MAAEJ,OAAO;MAAEC;IAAS,CAAC,GAAG,IAAI;IAClC;IACA;IACA;IACA,IAAI,CAACD,OAAO,EAAE;MACV,IAAI,CAACD,QAAQ,CAACU,IAAI,CAAC;QACfC,KAAK,EAAE,IAAI;QACX,CAAC,SAAST,QAAQ,EAAE,GAAGA,QAAQ,KAAK5H;MACxC,CAAC,CAAC;IACN;EACJ;EACAuE,MAAMA,CAAA,EAAG;IACL,MAAMqD,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMpD,IAAI,GAAGxF,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQrB,CAAC,CAACE,IAAI,EAAE;MAAEwE,GAAG,EAAE,0CAA0C;MAAEsD,KAAK,EAAEhH,kBAAkB,CAAC,IAAI,CAACoB,KAAK,EAAE;QACjG,CAACyE,IAAI,GAAG,IAAI;QACZ,eAAe,EAAE/F,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAACmE,EAAE,CAAC;QAC3D,CAAC,SAASgF,QAAQ,EAAE,GAAGA,QAAQ,KAAK5H,SAAS;QAC7C,CAAC,kBAAkB,GAAG,IAAI,CAAC6H,SAAS;QACpC,WAAW,EAAEhC,QAAQ,CAACC,GAAG,KAAK;MAClC,CAAC;IAAE,CAAC,CAAC;EACb;EACA,IAAIlD,EAAEA,CAAA,EAAG;IAAE,OAAO7E,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWsI,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,UAAU,EAAE,CAAC,iBAAiB;IAClC,CAAC;EAAE;AACP,CAAC;AACDmB,KAAK,CAAClB,KAAK,GAAG;EACVC,GAAG,EAAEc,iBAAiB;EACtBb,EAAE,EAAEe;AACR,CAAC;AAED,MAAMe,UAAU,GAAG,knCAAknC;AACroC,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,SAAS,GAAG,6rDAA6rD;AAC/sD,MAAMC,eAAe,GAAGD,SAAS;AAEjC,MAAME,IAAI,GAAG,MAAM;EACfnJ,WAAWA,CAACC,OAAO,EAAE;IACjBhC,gBAAgB,CAAC,IAAI,EAAEgC,OAAO,CAAC;IAC/B,IAAI,CAACkB,KAAK,GAAGV,SAAS;IACtB,IAAI,CAAC2I,KAAK,GAAG,KAAK;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;EACUC,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,MAAMlD,IAAI,GAAGiD,KAAI,CAACjG,EAAE,CAACG,aAAa,CAAC,kBAAkB,CAAC;MACtD,IAAI6C,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACmD,WAAW,EAAE;QAC9D,OAAOnD,IAAI,CAACmD,WAAW,CAAC,CAAC;MAC7B;MACA,OAAO,KAAK;IAAC;EACjB;EACAxE,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGxF,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAE0B,KAAK;MAAEiI;IAAM,CAAC,GAAG,IAAI;IAC7B,OAAQhL,CAAC,CAACE,IAAI,EAAE;MAAEwE,GAAG,EAAE,0CAA0C;MAAE0D,IAAI,EAAE,MAAM;MAAEJ,KAAK,EAAE;QAChF,CAACnB,IAAI,GAAG,IAAI;QACZ;QACA,CAAC,QAAQA,IAAI,EAAE,GAAG,IAAI;QACtB,YAAY,EAAEmE,KAAK;QACnB,CAAC,cAAcjI,KAAK,EAAE,GAAGA,KAAK,KAAKV,SAAS;QAC5C,CAAC,QAAQwE,IAAI,UAAU9D,KAAK,EAAE,GAAGA,KAAK,KAAKV;MAC/C;IAAE,CAAC,CAAC;EACZ;EACA,IAAI4C,EAAEA,CAAA,EAAG;IAAE,OAAO7E,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD2K,IAAI,CAACpC,KAAK,GAAG;EACTC,GAAG,EAAEgC,gBAAgB;EACrB/B,EAAE,EAAEiC;AACR,CAAC;AAED,MAAMO,gBAAgB,GAAG,+xEAA+xE;AACxzE,MAAMC,sBAAsB,GAAGD,gBAAgB;AAE/C,MAAME,eAAe,GAAG,i9DAAi9D;AACz+D,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,UAAU,GAAG,MAAM;EACrB7J,WAAWA,CAACC,OAAO,EAAE;IACjBhC,gBAAgB,CAAC,IAAI,EAAEgC,OAAO,CAAC;IAC/B,IAAI,CAACO,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACU,KAAK,GAAGV,SAAS;EAC1B;EACAuE,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE7D;IAAM,CAAC,GAAG,IAAI;IACtB,MAAM8D,IAAI,GAAGxF,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQrB,CAAC,CAACE,IAAI,EAAE;MAAEwE,GAAG,EAAE,0CAA0C;MAAEsD,KAAK,EAAEhH,kBAAkB,CAAC,IAAI,CAACoB,KAAK,EAAE;QACjG,CAACyE,IAAI,GAAG,IAAI;QACZ,CAAC,qBAAqB9D,KAAK,EAAE,GAAGA,KAAK,KAAKV;MAC9C,CAAC;IAAE,CAAC,EAAErC,CAAC,CAAC,KAAK,EAAE;MAAE0E,GAAG,EAAE,0CAA0C;MAAEsD,KAAK,EAAE;IAAoB,CAAC,EAAEhI,CAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EACxK;AACJ,CAAC;AACD+G,UAAU,CAAC9C,KAAK,GAAG;EACfC,GAAG,EAAE0C,sBAAsB;EAC3BzC,EAAE,EAAE2C;AACR,CAAC;AAED,MAAME,UAAU,GAAG,oPAAoP;AACvQ,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,SAAS,GAAG,6OAA6O;AAC/P,MAAMC,eAAe,GAAGD,SAAS;AAEjC,MAAME,IAAI,GAAG,MAAM;EACflK,WAAWA,CAACC,OAAO,EAAE;IACjBhC,gBAAgB,CAAC,IAAI,EAAEgC,OAAO,CAAC;IAC/B,IAAI,CAACO,KAAK,GAAGC,SAAS;EAC1B;EACAuE,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGxF,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQrB,CAAC,CAACE,IAAI,EAAE;MAAEwE,GAAG,EAAE,0CAA0C;MAAEsD,KAAK,EAAEhH,kBAAkB,CAAC,IAAI,CAACoB,KAAK,EAAE;QACjG,CAACyE,IAAI,GAAG;MACZ,CAAC;IAAE,CAAC,EAAE7G,CAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC7E;AACJ,CAAC;AACDoH,IAAI,CAACnD,KAAK,GAAG;EACTC,GAAG,EAAE+C,gBAAgB;EACrB9C,EAAE,EAAEgD;AACR,CAAC;AAED,MAAME,eAAe,GAAG,umDAAumD;AAC/nD,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,YAAY,GAAG,MAAM;EACvBrK,WAAWA,CAACC,OAAO,EAAE;IACjBhC,gBAAgB,CAAC,IAAI,EAAEgC,OAAO,CAAC;IAC/B,IAAI,CAACkI,QAAQ,GAAGzJ,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC4L,QAAQ,GAAG,KAAK;EACzB;EACAlH,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACoF,SAAS,CAAC,CAAC;EACpB;EACAA,SAASA,CAAA,EAAG;IACR;IACA;IACA;IACA,MAAMzB,KAAK,GAAG;MACV,eAAe,EAAE;IACrB,CAAC;IACD,IAAI,CAACoB,QAAQ,CAACU,IAAI,CAAC9B,KAAK,CAAC;EAC7B;EACA/B,MAAMA,CAAA,EAAG;IACL,MAAMsF,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI5K,MAAM,CAAC6K,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC;IACrE,MAAMC,OAAO,GAAGtL,WAAW,CAAC,YAAY,EAAE,IAAI,CAACmE,EAAE,CAAC,IAAInE,WAAW,CAAC,eAAe,EAAE,IAAI,CAACmE,EAAE,CAAC;IAC3F,MAAM4B,IAAI,GAAGxF,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQrB,CAAC,CAACE,IAAI,EAAE;MAAEwE,GAAG,EAAE,0CAA0C;MAAEsD,KAAK,EAAE;QAClE,CAACnB,IAAI,GAAG,IAAI;QACZ,wBAAwB,EAAEqF,QAAQ;QAClC,UAAU,EAAEE;MAChB;IAAE,CAAC,EAAEpM,CAAC,CAAC,MAAM,EAAE;MAAE0E,GAAG,EAAE;IAA2C,CAAC,EAAE,QAAQ,CAAC,CAAC;EACtF;EACA,IAAIO,EAAEA,CAAA,EAAG;IAAE,OAAO7E,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD6L,YAAY,CAACtD,KAAK,GAAGqD,qBAAqB;AAE1C,SAASrK,IAAI,IAAI0K,QAAQ,EAAEnD,WAAW,IAAIoD,gBAAgB,EAAE9C,SAAS,IAAI+C,cAAc,EAAE1C,KAAK,IAAI2C,SAAS,EAAEzB,IAAI,IAAI0B,QAAQ,EAAEhB,UAAU,IAAIiB,eAAe,EAAEZ,IAAI,IAAIa,QAAQ,EAAEV,YAAY,IAAIW,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}