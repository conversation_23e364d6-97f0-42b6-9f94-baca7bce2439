import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';
import { environment } from 'src/environments/environment';

interface Product {
  _id: string;
  name: string;
  price: number;
  originalPrice?: number;
  discount?: number;
  images: Array<{ url: string; alt?: string; isPrimary?: boolean }>;
  vendor: {
    _id: string;
    username: string;
    fullName: string;
    avatar: string;
    isInfluencer: boolean;
  };
  createdBy?: {
    _id: string;
    username: string;
    fullName: string;
    avatar: string;
    isInfluencer: boolean;
  };
  analytics: {
    views: number;
    likes: number;
    shares: number;
    purchases: number;
  };
  rating?: {
    average: number;
    count: number;
  };
}

@Component({
  selector: 'app-trending-products',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './trending-products.component.html',
  styleUrls: ['./trending-products.component.scss']
})
export class TrendingProductsComponent implements OnInit, OnDestroy {
  products: Product[] = [];
  isLoading = true;
  error: string | null = null;
  currentPage = 1;
  totalPages = 1;
  hasMore = false;

  private subscriptions: Subscription[] = [];

  constructor(
    public router: Router,
    private http: HttpClient
  ) {}

  ngOnInit() {
    this.loadTrendingProducts();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadTrendingProducts(page: number = 1) {
    this.isLoading = true;
    this.error = null;

    this.subscriptions.push(
      this.http.get<any>(`${environment.apiUrl}/products/trending?page=${page}&limit=12`).subscribe({
        next: (response) => {
          if (response.success) {
            if (page === 1) {
              this.products = response.products;
            } else {
              this.products = [...this.products, ...response.products];
            }
            
            this.currentPage = response.pagination.page;
            this.totalPages = response.pagination.pages;
            this.hasMore = this.currentPage < this.totalPages;
          } else {
            this.loadFallbackProducts();
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading trending products:', error);
          if (page === 1) {
            this.loadFallbackProducts();
          }
          this.error = 'Failed to load trending products';
          this.isLoading = false;
        }
      })
    );
  }

  loadFallbackProducts() {
    this.products = [
      {
        _id: '1',
        name: 'Vintage Denim Jacket',
        price: 89.99,
        originalPrice: 129.99,
        discount: 31,
        images: [{ url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400', isPrimary: true }],
        vendor: {
          _id: '1',
          username: 'fashionista_maya',
          fullName: 'Maya Rodriguez',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150',
          isInfluencer: true
        },
        analytics: { views: 25420, likes: 1892, shares: 356, purchases: 534 },
        rating: { average: 4.8, count: 256 }
      },
      {
        _id: '2',
        name: 'Silk Slip Dress',
        price: 159.99,
        originalPrice: 199.99,
        discount: 20,
        images: [{ url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400', isPrimary: true }],
        vendor: {
          _id: '2',
          username: 'style_guru_alex',
          fullName: 'Alex Chen',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
          isInfluencer: true
        },
        analytics: { views: 18890, likes: 2205, shares: 489, purchases: 367 },
        rating: { average: 4.6, count: 189 }
      },
      {
        _id: '3',
        name: 'Designer Sneakers',
        price: 199.99,
        images: [{ url: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400', isPrimary: true }],
        vendor: {
          _id: '4',
          username: 'streetstyle_mike',
          fullName: 'Mike Thompson',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
          isInfluencer: true
        },
        analytics: { views: 22945, likes: 1567, shares: 289, purchases: 423 },
        rating: { average: 4.9, count: 167 }
      }
    ];
  }

  loadMore() {
    if (this.hasMore && !this.isLoading) {
      this.loadTrendingProducts(this.currentPage + 1);
    }
  }

  viewProduct(product: Product) {
    this.router.navigate(['/product', product._id]);
  }

  viewVendor(vendor: any) {
    this.router.navigate(['/vendor', vendor.username]);
  }

  addToCart(product: Product, event: Event) {
    event.stopPropagation();
    // TODO: Implement add to cart functionality
    console.log('Add to cart:', product);
  }

  toggleWishlist(product: Product, event: Event) {
    event.stopPropagation();
    // TODO: Implement wishlist functionality
    console.log('Toggle wishlist:', product);
  }

  shareProduct(product: Product, event: Event) {
    event.stopPropagation();
    // TODO: Implement share functionality
    console.log('Share product:', product);
  }

  getDiscountPercentage(product: Product): number {
    if (product.originalPrice && product.originalPrice > product.price) {
      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);
    }
    return product.discount || 0;
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  }

  formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  retry() {
    this.loadTrendingProducts(1);
  }

  trackByProductId(index: number, product: Product): string {
    return product._id;
  }
}
