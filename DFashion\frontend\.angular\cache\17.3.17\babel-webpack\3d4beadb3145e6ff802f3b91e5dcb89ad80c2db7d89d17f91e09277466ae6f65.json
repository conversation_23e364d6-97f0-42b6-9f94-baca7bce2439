{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./role-management.service\";\nexport class PermissionManagementService {\n  constructor(roleManagementService) {\n    this.roleManagementService = roleManagementService;\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.permissionCacheSubject = new BehaviorSubject(new Map());\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.permissionCache$ = this.permissionCacheSubject.asObservable();\n    // Feature flags for different functionalities\n    this.featureFlags = [{\n      name: 'advanced_analytics',\n      enabled: true,\n      roles: ['super_admin', 'admin', 'sales_manager', 'marketing_manager']\n    }, {\n      name: 'team_management',\n      enabled: true,\n      condition: user => this.roleManagementService.isManager(user.role)\n    }, {\n      name: 'financial_reports',\n      enabled: true,\n      roles: ['super_admin', 'admin', 'account_manager', 'accountant']\n    }, {\n      name: 'user_management',\n      enabled: true,\n      roles: ['super_admin', 'admin']\n    }, {\n      name: 'system_settings',\n      enabled: true,\n      roles: ['super_admin', 'admin']\n    }, {\n      name: 'content_moderation',\n      enabled: true,\n      roles: ['super_admin', 'admin', 'content_manager']\n    }, {\n      name: 'vendor_management',\n      enabled: true,\n      roles: ['super_admin', 'admin', 'vendor_manager']\n    }, {\n      name: 'support_escalation',\n      enabled: true,\n      roles: ['super_admin', 'admin', 'support_manager']\n    }];\n    // UI component permissions\n    this.uiPermissions = [{\n      component: 'admin_panel',\n      action: 'view',\n      condition: user => ['super_admin', 'admin'].includes(user.role)\n    }, {\n      component: 'user_list',\n      action: 'view',\n      condition: user => this.hasPermission(user.role, 'users', 'read')\n    }, {\n      component: 'user_create',\n      action: 'view',\n      condition: user => this.hasPermission(user.role, 'users', 'create')\n    }, {\n      component: 'user_edit',\n      action: 'view',\n      condition: user => this.hasPermission(user.role, 'users', 'update')\n    }, {\n      component: 'user_delete',\n      action: 'view',\n      condition: user => this.hasPermission(user.role, 'users', 'delete')\n    }, {\n      component: 'team_dashboard',\n      action: 'view',\n      condition: user => this.roleManagementService.isManager(user.role)\n    }, {\n      component: 'financial_dashboard',\n      action: 'view',\n      condition: user => ['account_manager', 'accountant'].includes(user.role)\n    }, {\n      component: 'sales_reports',\n      action: 'view',\n      condition: user => user.role.includes('sales')\n    }, {\n      component: 'marketing_campaigns',\n      action: 'view',\n      condition: user => user.role.includes('marketing')\n    }, {\n      component: 'support_tickets',\n      action: 'view',\n      condition: user => user.role.includes('support')\n    }, {\n      component: 'content_editor',\n      action: 'view',\n      condition: user => ['content_manager', 'marketing_manager', 'marketing_executive'].includes(user.role)\n    }, {\n      component: 'vendor_contracts',\n      action: 'view',\n      condition: user => user.role === 'vendor_manager'\n    }];\n  }\n  setCurrentUser(user) {\n    this.currentUserSubject.next(user);\n    this.clearPermissionCache();\n  }\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  /**\n   * Check if current user has specific permission\n   */\n  hasPermission(role, module, action, scope) {\n    const cacheKey = `${role}_${module}_${action}_${scope || 'default'}`;\n    const cache = this.permissionCacheSubject.value;\n    if (cache.has(cacheKey)) {\n      return cache.get(cacheKey);\n    }\n    const hasAccess = this.roleManagementService.hasPermission(role, module, action);\n    // Cache the result\n    cache.set(cacheKey, hasAccess);\n    this.permissionCacheSubject.next(cache);\n    return hasAccess;\n  }\n  /**\n   * Check multiple permissions at once\n   */\n  hasAnyPermission(role, checks) {\n    return checks.some(check => this.hasPermission(role, check.module, check.action, check.scope));\n  }\n  /**\n   * Check if all permissions are granted\n   */\n  hasAllPermissions(role, checks) {\n    return checks.every(check => this.hasPermission(role, check.module, check.action, check.scope));\n  }\n  /**\n   * Get accessible modules for current user\n   */\n  getAccessibleModules(role) {\n    return this.roleManagementService.getAccessibleModules(role);\n  }\n  /**\n   * Check if user can access specific UI component\n   */\n  canAccessComponent(componentName, action = 'view') {\n    return this.currentUser$.pipe(map(user => {\n      if (!user) return false;\n      const permission = this.uiPermissions.find(p => p.component === componentName && p.action === action);\n      if (!permission) return false;\n      return permission.condition ? permission.condition(user) : true;\n    }));\n  }\n  /**\n   * Check if feature flag is enabled for current user\n   */\n  isFeatureEnabled(featureName) {\n    return this.currentUser$.pipe(map(user => {\n      if (!user) return false;\n      const feature = this.featureFlags.find(f => f.name === featureName);\n      if (!feature || !feature.enabled) return false;\n      // Check role-based access\n      if (feature.roles && !feature.roles.includes(user.role)) {\n        return false;\n      }\n      // Check department-based access\n      if (feature.departments && !feature.departments.includes(user.department)) {\n        return false;\n      }\n      // Check custom condition\n      if (feature.condition && !feature.condition(user)) {\n        return false;\n      }\n      return true;\n    }));\n  }\n  /**\n   * Get all enabled features for current user\n   */\n  getEnabledFeatures() {\n    return this.currentUser$.pipe(map(user => {\n      if (!user) return [];\n      return this.featureFlags.filter(feature => {\n        if (!feature.enabled) return false;\n        if (feature.roles && !feature.roles.includes(user.role)) {\n          return false;\n        }\n        if (feature.departments && !feature.departments.includes(user.department)) {\n          return false;\n        }\n        if (feature.condition && !feature.condition(user)) {\n          return false;\n        }\n        return true;\n      }).map(feature => feature.name);\n    }));\n  }\n  /**\n   * Check if user can manage other users\n   */\n  canManageUsers(targetRole) {\n    return this.currentUser$.pipe(map(user => {\n      if (!user) return false;\n      // Super admin can manage everyone\n      if (user.role === 'super_admin') return true;\n      // Admin can manage non-admin users\n      if (user.role === 'admin' && targetRole !== 'super_admin') return true;\n      // Managers can manage their subordinates\n      if (this.roleManagementService.isManager(user.role) && targetRole) {\n        const subordinates = this.roleManagementService.getSubordinateRoles(user.role);\n        return subordinates.includes(targetRole);\n      }\n      return false;\n    }));\n  }\n  /**\n   * Get permission scope for user\n   */\n  getPermissionScope(role, module) {\n    const config = this.roleManagementService.getRoleConfig(role);\n    const permission = config.permissions.find(p => p.module === module || p.module === '*');\n    return permission?.scope || 'self';\n  }\n  /**\n   * Check if user can access resource based on scope\n   */\n  canAccessResource(resourceOwnerId, resourceDepartment) {\n    return this.currentUser$.pipe(map(user => {\n      if (!user) return false;\n      // Super admin has global access\n      if (user.role === 'super_admin') return true;\n      // Check if user owns the resource\n      if (user.id === resourceOwnerId) return true;\n      // Check department-level access\n      if (resourceDepartment && user.department === resourceDepartment) {\n        const scope = this.getPermissionScope(user.role, 'resources');\n        return ['global', 'department'].includes(scope);\n      }\n      // Check team-level access for managers\n      if (this.roleManagementService.isManager(user.role)) {\n        const scope = this.getPermissionScope(user.role, 'resources');\n        return ['global', 'department', 'team'].includes(scope);\n      }\n      return false;\n    }));\n  }\n  /**\n   * Get filtered navigation items based on permissions\n   */\n  getFilteredNavigation(navigationItems) {\n    return this.currentUser$.pipe(map(user => {\n      if (!user) return [];\n      return navigationItems.filter(item => {\n        if (!item.permission) return true;\n        const [module, action] = item.permission.split('.');\n        return this.hasPermission(user.role, module, action);\n      });\n    }));\n  }\n  /**\n   * Clear permission cache\n   */\n  clearPermissionCache() {\n    this.permissionCacheSubject.next(new Map());\n  }\n  /**\n   * Enable/disable feature flag\n   */\n  setFeatureFlag(featureName, enabled) {\n    const feature = this.featureFlags.find(f => f.name === featureName);\n    if (feature) {\n      feature.enabled = enabled;\n    }\n  }\n  /**\n   * Add new UI permission\n   */\n  addUIPermission(permission) {\n    this.uiPermissions.push(permission);\n  }\n  /**\n   * Remove UI permission\n   */\n  removeUIPermission(componentName, action) {\n    const index = this.uiPermissions.findIndex(p => p.component === componentName && p.action === action);\n    if (index > -1) {\n      this.uiPermissions.splice(index, 1);\n    }\n  }\n  /**\n   * Get all permissions for debugging\n   */\n  getAllPermissions() {\n    return {\n      featureFlags: this.featureFlags,\n      uiPermissions: this.uiPermissions,\n      currentUser: this.getCurrentUser(),\n      permissionCache: Array.from(this.permissionCacheSubject.value.entries())\n    };\n  }\n  static {\n    this.ɵfac = function PermissionManagementService_Factory(t) {\n      return new (t || PermissionManagementService)(i0.ɵɵinject(i1.RoleManagementService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PermissionManagementService,\n      factory: PermissionManagementService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "map", "PermissionManagementService", "constructor", "roleManagementService", "currentUserSubject", "permissionCacheSubject", "Map", "currentUser$", "asObservable", "permissionCache$", "featureFlags", "name", "enabled", "roles", "condition", "user", "is<PERSON>anager", "role", "uiPermissions", "component", "action", "includes", "hasPermission", "setCurrentUser", "next", "clearPermissionCache", "getCurrentUser", "value", "module", "scope", "cache<PERSON>ey", "cache", "has", "get", "hasAccess", "set", "hasAnyPermission", "checks", "some", "check", "hasAllPermissions", "every", "getAccessibleModules", "canAccessComponent", "componentName", "pipe", "permission", "find", "p", "isFeatureEnabled", "featureName", "feature", "f", "departments", "department", "getEnabledFeatures", "filter", "canManageUsers", "targetRole", "subordinates", "getSubordinateRoles", "getPermissionScope", "config", "getRoleConfig", "permissions", "canAccessResource", "resourceOwnerId", "resourceDepartment", "id", "getFilteredNavigation", "navigationItems", "item", "split", "setFeatureFlag", "addUIPermission", "push", "removeUIPermission", "index", "findIndex", "splice", "getAllPermissions", "currentUser", "permissionCache", "Array", "from", "entries", "i0", "ɵɵinject", "i1", "RoleManagementService", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\core\\services\\permission-management.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable, of } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { RoleManagementService, UserRole, Permission } from './role-management.service';\n\nexport interface PermissionCheck {\n  module: string;\n  action: string;\n  scope?: 'global' | 'department' | 'team' | 'self';\n  resourceId?: string;\n}\n\nexport interface UIPermission {\n  component: string;\n  action: string;\n  condition?: (user: any) => boolean;\n}\n\nexport interface FeatureFlag {\n  name: string;\n  enabled: boolean;\n  roles?: UserRole[];\n  departments?: string[];\n  condition?: (user: any) => boolean;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class PermissionManagementService {\n  private currentUserSubject = new BehaviorSubject<any>(null);\n  private permissionCacheSubject = new BehaviorSubject<Map<string, boolean>>(new Map());\n  \n  public currentUser$ = this.currentUserSubject.asObservable();\n  public permissionCache$ = this.permissionCacheSubject.asObservable();\n\n  // Feature flags for different functionalities\n  private featureFlags: FeatureFlag[] = [\n    {\n      name: 'advanced_analytics',\n      enabled: true,\n      roles: ['super_admin', 'admin', 'sales_manager', 'marketing_manager']\n    },\n    {\n      name: 'team_management',\n      enabled: true,\n      condition: (user) => this.roleManagementService.isManager(user.role)\n    },\n    {\n      name: 'financial_reports',\n      enabled: true,\n      roles: ['super_admin', 'admin', 'account_manager', 'accountant']\n    },\n    {\n      name: 'user_management',\n      enabled: true,\n      roles: ['super_admin', 'admin']\n    },\n    {\n      name: 'system_settings',\n      enabled: true,\n      roles: ['super_admin', 'admin']\n    },\n    {\n      name: 'content_moderation',\n      enabled: true,\n      roles: ['super_admin', 'admin', 'content_manager']\n    },\n    {\n      name: 'vendor_management',\n      enabled: true,\n      roles: ['super_admin', 'admin', 'vendor_manager']\n    },\n    {\n      name: 'support_escalation',\n      enabled: true,\n      roles: ['super_admin', 'admin', 'support_manager']\n    }\n  ];\n\n  // UI component permissions\n  private uiPermissions: UIPermission[] = [\n    { component: 'admin_panel', action: 'view', condition: (user) => ['super_admin', 'admin'].includes(user.role) },\n    { component: 'user_list', action: 'view', condition: (user) => this.hasPermission(user.role, 'users', 'read') },\n    { component: 'user_create', action: 'view', condition: (user) => this.hasPermission(user.role, 'users', 'create') },\n    { component: 'user_edit', action: 'view', condition: (user) => this.hasPermission(user.role, 'users', 'update') },\n    { component: 'user_delete', action: 'view', condition: (user) => this.hasPermission(user.role, 'users', 'delete') },\n    { component: 'team_dashboard', action: 'view', condition: (user) => this.roleManagementService.isManager(user.role) },\n    { component: 'financial_dashboard', action: 'view', condition: (user) => ['account_manager', 'accountant'].includes(user.role) },\n    { component: 'sales_reports', action: 'view', condition: (user) => user.role.includes('sales') },\n    { component: 'marketing_campaigns', action: 'view', condition: (user) => user.role.includes('marketing') },\n    { component: 'support_tickets', action: 'view', condition: (user) => user.role.includes('support') },\n    { component: 'content_editor', action: 'view', condition: (user) => ['content_manager', 'marketing_manager', 'marketing_executive'].includes(user.role) },\n    { component: 'vendor_contracts', action: 'view', condition: (user) => user.role === 'vendor_manager' }\n  ];\n\n  constructor(private roleManagementService: RoleManagementService) {}\n\n  setCurrentUser(user: any): void {\n    this.currentUserSubject.next(user);\n    this.clearPermissionCache();\n  }\n\n  getCurrentUser(): any {\n    return this.currentUserSubject.value;\n  }\n\n  /**\n   * Check if current user has specific permission\n   */\n  hasPermission(role: UserRole, module: string, action: string, scope?: string): boolean {\n    const cacheKey = `${role}_${module}_${action}_${scope || 'default'}`;\n    const cache = this.permissionCacheSubject.value;\n    \n    if (cache.has(cacheKey)) {\n      return cache.get(cacheKey)!;\n    }\n\n    const hasAccess = this.roleManagementService.hasPermission(role, module, action);\n    \n    // Cache the result\n    cache.set(cacheKey, hasAccess);\n    this.permissionCacheSubject.next(cache);\n    \n    return hasAccess;\n  }\n\n  /**\n   * Check multiple permissions at once\n   */\n  hasAnyPermission(role: UserRole, checks: PermissionCheck[]): boolean {\n    return checks.some(check => \n      this.hasPermission(role, check.module, check.action, check.scope)\n    );\n  }\n\n  /**\n   * Check if all permissions are granted\n   */\n  hasAllPermissions(role: UserRole, checks: PermissionCheck[]): boolean {\n    return checks.every(check => \n      this.hasPermission(role, check.module, check.action, check.scope)\n    );\n  }\n\n  /**\n   * Get accessible modules for current user\n   */\n  getAccessibleModules(role: UserRole): string[] {\n    return this.roleManagementService.getAccessibleModules(role);\n  }\n\n  /**\n   * Check if user can access specific UI component\n   */\n  canAccessComponent(componentName: string, action: string = 'view'): Observable<boolean> {\n    return this.currentUser$.pipe(\n      map(user => {\n        if (!user) return false;\n        \n        const permission = this.uiPermissions.find(p => \n          p.component === componentName && p.action === action\n        );\n        \n        if (!permission) return false;\n        \n        return permission.condition ? permission.condition(user) : true;\n      })\n    );\n  }\n\n  /**\n   * Check if feature flag is enabled for current user\n   */\n  isFeatureEnabled(featureName: string): Observable<boolean> {\n    return this.currentUser$.pipe(\n      map(user => {\n        if (!user) return false;\n        \n        const feature = this.featureFlags.find(f => f.name === featureName);\n        if (!feature || !feature.enabled) return false;\n        \n        // Check role-based access\n        if (feature.roles && !feature.roles.includes(user.role)) {\n          return false;\n        }\n        \n        // Check department-based access\n        if (feature.departments && !feature.departments.includes(user.department)) {\n          return false;\n        }\n        \n        // Check custom condition\n        if (feature.condition && !feature.condition(user)) {\n          return false;\n        }\n        \n        return true;\n      })\n    );\n  }\n\n  /**\n   * Get all enabled features for current user\n   */\n  getEnabledFeatures(): Observable<string[]> {\n    return this.currentUser$.pipe(\n      map(user => {\n        if (!user) return [];\n        \n        return this.featureFlags\n          .filter(feature => {\n            if (!feature.enabled) return false;\n            \n            if (feature.roles && !feature.roles.includes(user.role)) {\n              return false;\n            }\n            \n            if (feature.departments && !feature.departments.includes(user.department)) {\n              return false;\n            }\n            \n            if (feature.condition && !feature.condition(user)) {\n              return false;\n            }\n            \n            return true;\n          })\n          .map(feature => feature.name);\n      })\n    );\n  }\n\n  /**\n   * Check if user can manage other users\n   */\n  canManageUsers(targetRole?: UserRole): Observable<boolean> {\n    return this.currentUser$.pipe(\n      map(user => {\n        if (!user) return false;\n        \n        // Super admin can manage everyone\n        if (user.role === 'super_admin') return true;\n        \n        // Admin can manage non-admin users\n        if (user.role === 'admin' && targetRole !== 'super_admin') return true;\n        \n        // Managers can manage their subordinates\n        if (this.roleManagementService.isManager(user.role) && targetRole) {\n          const subordinates = this.roleManagementService.getSubordinateRoles(user.role);\n          return subordinates.includes(targetRole);\n        }\n        \n        return false;\n      })\n    );\n  }\n\n  /**\n   * Get permission scope for user\n   */\n  getPermissionScope(role: UserRole, module: string): string {\n    const config = this.roleManagementService.getRoleConfig(role);\n    const permission = config.permissions.find(p => p.module === module || p.module === '*');\n    \n    return permission?.scope || 'self';\n  }\n\n  /**\n   * Check if user can access resource based on scope\n   */\n  canAccessResource(resourceOwnerId: string, resourceDepartment?: string): Observable<boolean> {\n    return this.currentUser$.pipe(\n      map(user => {\n        if (!user) return false;\n        \n        // Super admin has global access\n        if (user.role === 'super_admin') return true;\n        \n        // Check if user owns the resource\n        if (user.id === resourceOwnerId) return true;\n        \n        // Check department-level access\n        if (resourceDepartment && user.department === resourceDepartment) {\n          const scope = this.getPermissionScope(user.role, 'resources');\n          return ['global', 'department'].includes(scope);\n        }\n        \n        // Check team-level access for managers\n        if (this.roleManagementService.isManager(user.role)) {\n          const scope = this.getPermissionScope(user.role, 'resources');\n          return ['global', 'department', 'team'].includes(scope);\n        }\n        \n        return false;\n      })\n    );\n  }\n\n  /**\n   * Get filtered navigation items based on permissions\n   */\n  getFilteredNavigation(navigationItems: any[]): Observable<any[]> {\n    return this.currentUser$.pipe(\n      map(user => {\n        if (!user) return [];\n        \n        return navigationItems.filter(item => {\n          if (!item.permission) return true;\n          \n          const [module, action] = item.permission.split('.');\n          return this.hasPermission(user.role, module, action);\n        });\n      })\n    );\n  }\n\n  /**\n   * Clear permission cache\n   */\n  clearPermissionCache(): void {\n    this.permissionCacheSubject.next(new Map());\n  }\n\n  /**\n   * Enable/disable feature flag\n   */\n  setFeatureFlag(featureName: string, enabled: boolean): void {\n    const feature = this.featureFlags.find(f => f.name === featureName);\n    if (feature) {\n      feature.enabled = enabled;\n    }\n  }\n\n  /**\n   * Add new UI permission\n   */\n  addUIPermission(permission: UIPermission): void {\n    this.uiPermissions.push(permission);\n  }\n\n  /**\n   * Remove UI permission\n   */\n  removeUIPermission(componentName: string, action: string): void {\n    const index = this.uiPermissions.findIndex(p => \n      p.component === componentName && p.action === action\n    );\n    if (index > -1) {\n      this.uiPermissions.splice(index, 1);\n    }\n  }\n\n  /**\n   * Get all permissions for debugging\n   */\n  getAllPermissions(): any {\n    return {\n      featureFlags: this.featureFlags,\n      uiPermissions: this.uiPermissions,\n      currentUser: this.getCurrentUser(),\n      permissionCache: Array.from(this.permissionCacheSubject.value.entries())\n    };\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAwB,MAAM;AACtD,SAASC,GAAG,QAAQ,gBAAgB;;;AA2BpC,OAAM,MAAOC,2BAA2B;EAmEtCC,YAAoBC,qBAA4C;IAA5C,KAAAA,qBAAqB,GAArBA,qBAAqB;IAlEjC,KAAAC,kBAAkB,GAAG,IAAIL,eAAe,CAAM,IAAI,CAAC;IACnD,KAAAM,sBAAsB,GAAG,IAAIN,eAAe,CAAuB,IAAIO,GAAG,EAAE,CAAC;IAE9E,KAAAC,YAAY,GAAG,IAAI,CAACH,kBAAkB,CAACI,YAAY,EAAE;IACrD,KAAAC,gBAAgB,GAAG,IAAI,CAACJ,sBAAsB,CAACG,YAAY,EAAE;IAEpE;IACQ,KAAAE,YAAY,GAAkB,CACpC;MACEC,IAAI,EAAE,oBAAoB;MAC1BC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,CAAC,aAAa,EAAE,OAAO,EAAE,eAAe,EAAE,mBAAmB;KACrE,EACD;MACEF,IAAI,EAAE,iBAAiB;MACvBC,OAAO,EAAE,IAAI;MACbE,SAAS,EAAGC,IAAI,IAAK,IAAI,CAACZ,qBAAqB,CAACa,SAAS,CAACD,IAAI,CAACE,IAAI;KACpE,EACD;MACEN,IAAI,EAAE,mBAAmB;MACzBC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,CAAC,aAAa,EAAE,OAAO,EAAE,iBAAiB,EAAE,YAAY;KAChE,EACD;MACEF,IAAI,EAAE,iBAAiB;MACvBC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,CAAC,aAAa,EAAE,OAAO;KAC/B,EACD;MACEF,IAAI,EAAE,iBAAiB;MACvBC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,CAAC,aAAa,EAAE,OAAO;KAC/B,EACD;MACEF,IAAI,EAAE,oBAAoB;MAC1BC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,CAAC,aAAa,EAAE,OAAO,EAAE,iBAAiB;KAClD,EACD;MACEF,IAAI,EAAE,mBAAmB;MACzBC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,CAAC,aAAa,EAAE,OAAO,EAAE,gBAAgB;KACjD,EACD;MACEF,IAAI,EAAE,oBAAoB;MAC1BC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,CAAC,aAAa,EAAE,OAAO,EAAE,iBAAiB;KAClD,CACF;IAED;IACQ,KAAAK,aAAa,GAAmB,CACtC;MAAEC,SAAS,EAAE,aAAa;MAAEC,MAAM,EAAE,MAAM;MAAEN,SAAS,EAAGC,IAAI,IAAK,CAAC,aAAa,EAAE,OAAO,CAAC,CAACM,QAAQ,CAACN,IAAI,CAACE,IAAI;IAAC,CAAE,EAC/G;MAAEE,SAAS,EAAE,WAAW;MAAEC,MAAM,EAAE,MAAM;MAAEN,SAAS,EAAGC,IAAI,IAAK,IAAI,CAACO,aAAa,CAACP,IAAI,CAACE,IAAI,EAAE,OAAO,EAAE,MAAM;IAAC,CAAE,EAC/G;MAAEE,SAAS,EAAE,aAAa;MAAEC,MAAM,EAAE,MAAM;MAAEN,SAAS,EAAGC,IAAI,IAAK,IAAI,CAACO,aAAa,CAACP,IAAI,CAACE,IAAI,EAAE,OAAO,EAAE,QAAQ;IAAC,CAAE,EACnH;MAAEE,SAAS,EAAE,WAAW;MAAEC,MAAM,EAAE,MAAM;MAAEN,SAAS,EAAGC,IAAI,IAAK,IAAI,CAACO,aAAa,CAACP,IAAI,CAACE,IAAI,EAAE,OAAO,EAAE,QAAQ;IAAC,CAAE,EACjH;MAAEE,SAAS,EAAE,aAAa;MAAEC,MAAM,EAAE,MAAM;MAAEN,SAAS,EAAGC,IAAI,IAAK,IAAI,CAACO,aAAa,CAACP,IAAI,CAACE,IAAI,EAAE,OAAO,EAAE,QAAQ;IAAC,CAAE,EACnH;MAAEE,SAAS,EAAE,gBAAgB;MAAEC,MAAM,EAAE,MAAM;MAAEN,SAAS,EAAGC,IAAI,IAAK,IAAI,CAACZ,qBAAqB,CAACa,SAAS,CAACD,IAAI,CAACE,IAAI;IAAC,CAAE,EACrH;MAAEE,SAAS,EAAE,qBAAqB;MAAEC,MAAM,EAAE,MAAM;MAAEN,SAAS,EAAGC,IAAI,IAAK,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAACM,QAAQ,CAACN,IAAI,CAACE,IAAI;IAAC,CAAE,EAChI;MAAEE,SAAS,EAAE,eAAe;MAAEC,MAAM,EAAE,MAAM;MAAEN,SAAS,EAAGC,IAAI,IAAKA,IAAI,CAACE,IAAI,CAACI,QAAQ,CAAC,OAAO;IAAC,CAAE,EAChG;MAAEF,SAAS,EAAE,qBAAqB;MAAEC,MAAM,EAAE,MAAM;MAAEN,SAAS,EAAGC,IAAI,IAAKA,IAAI,CAACE,IAAI,CAACI,QAAQ,CAAC,WAAW;IAAC,CAAE,EAC1G;MAAEF,SAAS,EAAE,iBAAiB;MAAEC,MAAM,EAAE,MAAM;MAAEN,SAAS,EAAGC,IAAI,IAAKA,IAAI,CAACE,IAAI,CAACI,QAAQ,CAAC,SAAS;IAAC,CAAE,EACpG;MAAEF,SAAS,EAAE,gBAAgB;MAAEC,MAAM,EAAE,MAAM;MAAEN,SAAS,EAAGC,IAAI,IAAK,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,qBAAqB,CAAC,CAACM,QAAQ,CAACN,IAAI,CAACE,IAAI;IAAC,CAAE,EACzJ;MAAEE,SAAS,EAAE,kBAAkB;MAAEC,MAAM,EAAE,MAAM;MAAEN,SAAS,EAAGC,IAAI,IAAKA,IAAI,CAACE,IAAI,KAAK;IAAgB,CAAE,CACvG;EAEkE;EAEnEM,cAAcA,CAACR,IAAS;IACtB,IAAI,CAACX,kBAAkB,CAACoB,IAAI,CAACT,IAAI,CAAC;IAClC,IAAI,CAACU,oBAAoB,EAAE;EAC7B;EAEAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACtB,kBAAkB,CAACuB,KAAK;EACtC;EAEA;;;EAGAL,aAAaA,CAACL,IAAc,EAAEW,MAAc,EAAER,MAAc,EAAES,KAAc;IAC1E,MAAMC,QAAQ,GAAG,GAAGb,IAAI,IAAIW,MAAM,IAAIR,MAAM,IAAIS,KAAK,IAAI,SAAS,EAAE;IACpE,MAAME,KAAK,GAAG,IAAI,CAAC1B,sBAAsB,CAACsB,KAAK;IAE/C,IAAII,KAAK,CAACC,GAAG,CAACF,QAAQ,CAAC,EAAE;MACvB,OAAOC,KAAK,CAACE,GAAG,CAACH,QAAQ,CAAE;;IAG7B,MAAMI,SAAS,GAAG,IAAI,CAAC/B,qBAAqB,CAACmB,aAAa,CAACL,IAAI,EAAEW,MAAM,EAAER,MAAM,CAAC;IAEhF;IACAW,KAAK,CAACI,GAAG,CAACL,QAAQ,EAAEI,SAAS,CAAC;IAC9B,IAAI,CAAC7B,sBAAsB,CAACmB,IAAI,CAACO,KAAK,CAAC;IAEvC,OAAOG,SAAS;EAClB;EAEA;;;EAGAE,gBAAgBA,CAACnB,IAAc,EAAEoB,MAAyB;IACxD,OAAOA,MAAM,CAACC,IAAI,CAACC,KAAK,IACtB,IAAI,CAACjB,aAAa,CAACL,IAAI,EAAEsB,KAAK,CAACX,MAAM,EAAEW,KAAK,CAACnB,MAAM,EAAEmB,KAAK,CAACV,KAAK,CAAC,CAClE;EACH;EAEA;;;EAGAW,iBAAiBA,CAACvB,IAAc,EAAEoB,MAAyB;IACzD,OAAOA,MAAM,CAACI,KAAK,CAACF,KAAK,IACvB,IAAI,CAACjB,aAAa,CAACL,IAAI,EAAEsB,KAAK,CAACX,MAAM,EAAEW,KAAK,CAACnB,MAAM,EAAEmB,KAAK,CAACV,KAAK,CAAC,CAClE;EACH;EAEA;;;EAGAa,oBAAoBA,CAACzB,IAAc;IACjC,OAAO,IAAI,CAACd,qBAAqB,CAACuC,oBAAoB,CAACzB,IAAI,CAAC;EAC9D;EAEA;;;EAGA0B,kBAAkBA,CAACC,aAAqB,EAAExB,MAAA,GAAiB,MAAM;IAC/D,OAAO,IAAI,CAACb,YAAY,CAACsC,IAAI,CAC3B7C,GAAG,CAACe,IAAI,IAAG;MACT,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;MAEvB,MAAM+B,UAAU,GAAG,IAAI,CAAC5B,aAAa,CAAC6B,IAAI,CAACC,CAAC,IAC1CA,CAAC,CAAC7B,SAAS,KAAKyB,aAAa,IAAII,CAAC,CAAC5B,MAAM,KAAKA,MAAM,CACrD;MAED,IAAI,CAAC0B,UAAU,EAAE,OAAO,KAAK;MAE7B,OAAOA,UAAU,CAAChC,SAAS,GAAGgC,UAAU,CAAChC,SAAS,CAACC,IAAI,CAAC,GAAG,IAAI;IACjE,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAkC,gBAAgBA,CAACC,WAAmB;IAClC,OAAO,IAAI,CAAC3C,YAAY,CAACsC,IAAI,CAC3B7C,GAAG,CAACe,IAAI,IAAG;MACT,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;MAEvB,MAAMoC,OAAO,GAAG,IAAI,CAACzC,YAAY,CAACqC,IAAI,CAACK,CAAC,IAAIA,CAAC,CAACzC,IAAI,KAAKuC,WAAW,CAAC;MACnE,IAAI,CAACC,OAAO,IAAI,CAACA,OAAO,CAACvC,OAAO,EAAE,OAAO,KAAK;MAE9C;MACA,IAAIuC,OAAO,CAACtC,KAAK,IAAI,CAACsC,OAAO,CAACtC,KAAK,CAACQ,QAAQ,CAACN,IAAI,CAACE,IAAI,CAAC,EAAE;QACvD,OAAO,KAAK;;MAGd;MACA,IAAIkC,OAAO,CAACE,WAAW,IAAI,CAACF,OAAO,CAACE,WAAW,CAAChC,QAAQ,CAACN,IAAI,CAACuC,UAAU,CAAC,EAAE;QACzE,OAAO,KAAK;;MAGd;MACA,IAAIH,OAAO,CAACrC,SAAS,IAAI,CAACqC,OAAO,CAACrC,SAAS,CAACC,IAAI,CAAC,EAAE;QACjD,OAAO,KAAK;;MAGd,OAAO,IAAI;IACb,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAwC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAChD,YAAY,CAACsC,IAAI,CAC3B7C,GAAG,CAACe,IAAI,IAAG;MACT,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;MAEpB,OAAO,IAAI,CAACL,YAAY,CACrB8C,MAAM,CAACL,OAAO,IAAG;QAChB,IAAI,CAACA,OAAO,CAACvC,OAAO,EAAE,OAAO,KAAK;QAElC,IAAIuC,OAAO,CAACtC,KAAK,IAAI,CAACsC,OAAO,CAACtC,KAAK,CAACQ,QAAQ,CAACN,IAAI,CAACE,IAAI,CAAC,EAAE;UACvD,OAAO,KAAK;;QAGd,IAAIkC,OAAO,CAACE,WAAW,IAAI,CAACF,OAAO,CAACE,WAAW,CAAChC,QAAQ,CAACN,IAAI,CAACuC,UAAU,CAAC,EAAE;UACzE,OAAO,KAAK;;QAGd,IAAIH,OAAO,CAACrC,SAAS,IAAI,CAACqC,OAAO,CAACrC,SAAS,CAACC,IAAI,CAAC,EAAE;UACjD,OAAO,KAAK;;QAGd,OAAO,IAAI;MACb,CAAC,CAAC,CACDf,GAAG,CAACmD,OAAO,IAAIA,OAAO,CAACxC,IAAI,CAAC;IACjC,CAAC,CAAC,CACH;EACH;EAEA;;;EAGA8C,cAAcA,CAACC,UAAqB;IAClC,OAAO,IAAI,CAACnD,YAAY,CAACsC,IAAI,CAC3B7C,GAAG,CAACe,IAAI,IAAG;MACT,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;MAEvB;MACA,IAAIA,IAAI,CAACE,IAAI,KAAK,aAAa,EAAE,OAAO,IAAI;MAE5C;MACA,IAAIF,IAAI,CAACE,IAAI,KAAK,OAAO,IAAIyC,UAAU,KAAK,aAAa,EAAE,OAAO,IAAI;MAEtE;MACA,IAAI,IAAI,CAACvD,qBAAqB,CAACa,SAAS,CAACD,IAAI,CAACE,IAAI,CAAC,IAAIyC,UAAU,EAAE;QACjE,MAAMC,YAAY,GAAG,IAAI,CAACxD,qBAAqB,CAACyD,mBAAmB,CAAC7C,IAAI,CAACE,IAAI,CAAC;QAC9E,OAAO0C,YAAY,CAACtC,QAAQ,CAACqC,UAAU,CAAC;;MAG1C,OAAO,KAAK;IACd,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAG,kBAAkBA,CAAC5C,IAAc,EAAEW,MAAc;IAC/C,MAAMkC,MAAM,GAAG,IAAI,CAAC3D,qBAAqB,CAAC4D,aAAa,CAAC9C,IAAI,CAAC;IAC7D,MAAM6B,UAAU,GAAGgB,MAAM,CAACE,WAAW,CAACjB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpB,MAAM,KAAKA,MAAM,IAAIoB,CAAC,CAACpB,MAAM,KAAK,GAAG,CAAC;IAExF,OAAOkB,UAAU,EAAEjB,KAAK,IAAI,MAAM;EACpC;EAEA;;;EAGAoC,iBAAiBA,CAACC,eAAuB,EAAEC,kBAA2B;IACpE,OAAO,IAAI,CAAC5D,YAAY,CAACsC,IAAI,CAC3B7C,GAAG,CAACe,IAAI,IAAG;MACT,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;MAEvB;MACA,IAAIA,IAAI,CAACE,IAAI,KAAK,aAAa,EAAE,OAAO,IAAI;MAE5C;MACA,IAAIF,IAAI,CAACqD,EAAE,KAAKF,eAAe,EAAE,OAAO,IAAI;MAE5C;MACA,IAAIC,kBAAkB,IAAIpD,IAAI,CAACuC,UAAU,KAAKa,kBAAkB,EAAE;QAChE,MAAMtC,KAAK,GAAG,IAAI,CAACgC,kBAAkB,CAAC9C,IAAI,CAACE,IAAI,EAAE,WAAW,CAAC;QAC7D,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAC,CAACI,QAAQ,CAACQ,KAAK,CAAC;;MAGjD;MACA,IAAI,IAAI,CAAC1B,qBAAqB,CAACa,SAAS,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;QACnD,MAAMY,KAAK,GAAG,IAAI,CAACgC,kBAAkB,CAAC9C,IAAI,CAACE,IAAI,EAAE,WAAW,CAAC;QAC7D,OAAO,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,CAAC,CAACI,QAAQ,CAACQ,KAAK,CAAC;;MAGzD,OAAO,KAAK;IACd,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAwC,qBAAqBA,CAACC,eAAsB;IAC1C,OAAO,IAAI,CAAC/D,YAAY,CAACsC,IAAI,CAC3B7C,GAAG,CAACe,IAAI,IAAG;MACT,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;MAEpB,OAAOuD,eAAe,CAACd,MAAM,CAACe,IAAI,IAAG;QACnC,IAAI,CAACA,IAAI,CAACzB,UAAU,EAAE,OAAO,IAAI;QAEjC,MAAM,CAAClB,MAAM,EAAER,MAAM,CAAC,GAAGmD,IAAI,CAACzB,UAAU,CAAC0B,KAAK,CAAC,GAAG,CAAC;QACnD,OAAO,IAAI,CAAClD,aAAa,CAACP,IAAI,CAACE,IAAI,EAAEW,MAAM,EAAER,MAAM,CAAC;MACtD,CAAC,CAAC;IACJ,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAK,oBAAoBA,CAAA;IAClB,IAAI,CAACpB,sBAAsB,CAACmB,IAAI,CAAC,IAAIlB,GAAG,EAAE,CAAC;EAC7C;EAEA;;;EAGAmE,cAAcA,CAACvB,WAAmB,EAAEtC,OAAgB;IAClD,MAAMuC,OAAO,GAAG,IAAI,CAACzC,YAAY,CAACqC,IAAI,CAACK,CAAC,IAAIA,CAAC,CAACzC,IAAI,KAAKuC,WAAW,CAAC;IACnE,IAAIC,OAAO,EAAE;MACXA,OAAO,CAACvC,OAAO,GAAGA,OAAO;;EAE7B;EAEA;;;EAGA8D,eAAeA,CAAC5B,UAAwB;IACtC,IAAI,CAAC5B,aAAa,CAACyD,IAAI,CAAC7B,UAAU,CAAC;EACrC;EAEA;;;EAGA8B,kBAAkBA,CAAChC,aAAqB,EAAExB,MAAc;IACtD,MAAMyD,KAAK,GAAG,IAAI,CAAC3D,aAAa,CAAC4D,SAAS,CAAC9B,CAAC,IAC1CA,CAAC,CAAC7B,SAAS,KAAKyB,aAAa,IAAII,CAAC,CAAC5B,MAAM,KAAKA,MAAM,CACrD;IACD,IAAIyD,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC3D,aAAa,CAAC6D,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;;EAEvC;EAEA;;;EAGAG,iBAAiBA,CAAA;IACf,OAAO;MACLtE,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BQ,aAAa,EAAE,IAAI,CAACA,aAAa;MACjC+D,WAAW,EAAE,IAAI,CAACvD,cAAc,EAAE;MAClCwD,eAAe,EAAEC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/E,sBAAsB,CAACsB,KAAK,CAAC0D,OAAO,EAAE;KACxE;EACH;;;uBA9UWpF,2BAA2B,EAAAqF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,qBAAA;IAAA;EAAA;;;aAA3BxF,2BAA2B;MAAAyF,OAAA,EAA3BzF,2BAA2B,CAAA0F,IAAA;MAAAC,UAAA,EAF1B;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}