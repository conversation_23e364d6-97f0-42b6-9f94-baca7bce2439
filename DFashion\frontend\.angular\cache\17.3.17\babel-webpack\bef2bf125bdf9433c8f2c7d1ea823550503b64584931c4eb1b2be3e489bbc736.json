{"ast": null, "code": "import { BehaviorSubject, tap, catchError, throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.API_URL = 'http://localhost:5000/api';\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.isAuthenticatedSubject = new BehaviorSubject(false);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n  }\n  initializeAuth() {\n    const token = this.getToken();\n    if (token) {\n      this.getCurrentUser().subscribe({\n        next: response => {\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        },\n        error: () => {\n          // Clear invalid token without redirecting\n          this.clearAuth();\n        }\n      });\n    }\n  }\n  login(credentials) {\n    return this.http.post(`${this.API_URL}/auth/login`, credentials).pipe(tap(response => {\n      this.setToken(response.token);\n      this.currentUserSubject.next(response.user);\n      this.isAuthenticatedSubject.next(true);\n      // Trigger cart and wishlist refresh after successful login\n      this.refreshUserDataOnLogin();\n    }), catchError(error => {\n      console.error('Login error:', error);\n      return throwError(() => error);\n    }));\n  }\n  register(userData) {\n    return this.http.post(`${this.API_URL}/auth/register`, userData).pipe(tap(response => {\n      this.setToken(response.token);\n      this.currentUserSubject.next(response.user);\n      this.isAuthenticatedSubject.next(true);\n    }));\n  }\n  logout() {\n    this.clearAuth();\n    this.router.navigate(['/auth/login']);\n  }\n  clearAuth() {\n    localStorage.removeItem('token');\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    // Clear cart and wishlist data on logout\n    this.clearUserDataOnLogout();\n  }\n  // Method to refresh user data (cart, wishlist) after login\n  refreshUserDataOnLogin() {\n    // Use setTimeout to avoid circular dependency issues\n    setTimeout(() => {\n      try {\n        // Import services dynamically to avoid circular dependency\n        import('./cart.service').then(({\n          CartService\n        }) => {\n          const cartService = new CartService(this.http, null, null);\n          cartService.refreshCartOnLogin();\n        });\n        import('./wishlist-new.service').then(({\n          WishlistNewService\n        }) => {\n          const wishlistService = new WishlistNewService(this.http, this);\n          wishlistService.refreshWishlistOnLogin();\n        });\n      } catch (error) {\n        console.error('Error refreshing user data on login:', error);\n      }\n    }, 100);\n  }\n  // Method to clear user data on logout\n  clearUserDataOnLogout() {\n    setTimeout(() => {\n      try {\n        // Import services dynamically to avoid circular dependency\n        import('./cart.service').then(({\n          CartService\n        }) => {\n          const cartService = new CartService(this.http, null, null);\n          cartService.clearCartOnLogout();\n        });\n        import('./wishlist-new.service').then(({\n          WishlistNewService\n        }) => {\n          const wishlistService = new WishlistNewService(this.http, this);\n          wishlistService.clearWishlistOnLogout();\n        });\n      } catch (error) {\n        console.error('Error clearing user data on logout:', error);\n      }\n    }, 100);\n  }\n  getCurrentUser() {\n    return this.http.get(`${this.API_URL}/auth/me`);\n  }\n  getToken() {\n    return localStorage.getItem('token');\n  }\n  setToken(token) {\n    localStorage.setItem('token', token);\n  }\n  get currentUserValue() {\n    return this.currentUserSubject.value;\n  }\n  get isAuthenticated() {\n    return this.isAuthenticatedSubject.value;\n  }\n  isAdmin() {\n    const user = this.currentUserValue;\n    return user?.role === 'admin';\n  }\n  isVendor() {\n    const user = this.currentUserValue;\n    return user?.role === 'vendor';\n  }\n  isCustomer() {\n    const user = this.currentUserValue;\n    return user?.role === 'customer';\n  }\n  // Helper methods for checking authentication before actions\n  requireAuth(action = 'perform this action') {\n    if (!this.isAuthenticated) {\n      this.showLoginPrompt(action);\n      return false;\n    }\n    return true;\n  }\n  requireSuperAdminAuth(action = 'perform this action') {\n    if (!this.isAuthenticated) {\n      this.showLoginPrompt(action);\n      return false;\n    }\n    if (!this.isAdmin()) {\n      this.showRoleError('super admin', action);\n      return false;\n    }\n    return true;\n  }\n  requireCustomerAuth(action = 'perform this action') {\n    if (!this.isAuthenticated) {\n      this.showLoginPrompt(action);\n      return false;\n    }\n    if (!this.isCustomer()) {\n      this.showRoleError('customer', action);\n      return false;\n    }\n    return true;\n  }\n  showLoginPrompt(action) {\n    const message = `Please login to ${action}`;\n    if (confirm(`${message}. Would you like to login now?`)) {\n      this.router.navigate(['/auth/login'], {\n        queryParams: {\n          returnUrl: this.router.url\n        }\n      });\n    }\n  }\n  showRoleError(requiredRole, action) {\n    alert(`Only ${requiredRole}s can ${action}. Please login with a ${requiredRole} account.`);\n  }\n  // Social interaction methods with authentication checks\n  canLike() {\n    return this.requireCustomerAuth('like posts');\n  }\n  canComment() {\n    return this.requireCustomerAuth('comment on posts');\n  }\n  canAddToCart() {\n    return this.requireCustomerAuth('add items to cart');\n  }\n  canAddToWishlist() {\n    return this.requireCustomerAuth('add items to wishlist');\n  }\n  canBuy() {\n    return this.requireCustomerAuth('purchase items');\n  }\n  // Get auth headers for API calls\n  getAuthHeaders() {\n    const token = this.getToken();\n    return token ? {\n      'Authorization': `Bearer ${token}`\n    } : {};\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "catchError", "throwError", "AuthService", "constructor", "http", "router", "API_URL", "currentUserSubject", "isAuthenticatedSubject", "currentUser$", "asObservable", "isAuthenticated$", "initializeAuth", "token", "getToken", "getCurrentUser", "subscribe", "next", "response", "user", "error", "clearAuth", "login", "credentials", "post", "pipe", "setToken", "refreshUserDataOnLogin", "console", "register", "userData", "logout", "navigate", "localStorage", "removeItem", "clearUserDataOnLogout", "setTimeout", "then", "CartService", "cartService", "refreshCartOnLogin", "WishlistNewService", "wishlistService", "refreshWishlistOnLogin", "clearCartOnLogout", "clearWishlistOnLogout", "get", "getItem", "setItem", "currentUserValue", "value", "isAuthenticated", "isAdmin", "role", "isVendor", "isCustomer", "requireAuth", "action", "showLoginPrompt", "requireSuperAdminAuth", "showRoleError", "requireCustomerAuth", "message", "confirm", "queryParams", "returnUrl", "url", "requiredRole", "alert", "canLike", "canComment", "canAddToCart", "canAddToWishlist", "canBuy", "getAuthHeaders", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fahion\\DFashion\\frontend\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable, Inject, forwardRef } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { BehaviorSubject, Observable, tap, catchError, throwError, of } from 'rxjs';\nimport { Router } from '@angular/router';\n\nimport { User, LoginRequest, RegisterRequest, AuthResponse } from '../models/user.model';\nimport { environment } from '../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly API_URL = 'http://localhost:5000/api';\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);\n\n  public currentUser$ = this.currentUserSubject.asObservable();\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {}\n\n  initializeAuth(): void {\n    const token = this.getToken();\n    if (token) {\n      this.getCurrentUser().subscribe({\n        next: (response) => {\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        },\n        error: () => {\n          // Clear invalid token without redirecting\n          this.clearAuth();\n        }\n      });\n    }\n  }\n\n  login(credentials: LoginRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.API_URL}/auth/login`, credentials)\n      .pipe(\n        tap(response => {\n          this.setToken(response.token);\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n\n          // Trigger cart and wishlist refresh after successful login\n          this.refreshUserDataOnLogin();\n        }),\n        catchError(error => {\n          console.error('Login error:', error);\n          return throwError(() => error);\n        })\n      );\n  }\n\n\n\n\n\n  register(userData: RegisterRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.API_URL}/auth/register`, userData)\n      .pipe(\n        tap(response => {\n          this.setToken(response.token);\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        })\n      );\n  }\n\n\n\n  logout(): void {\n    this.clearAuth();\n    this.router.navigate(['/auth/login']);\n  }\n\n  private clearAuth(): void {\n    localStorage.removeItem('token');\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n\n    // Clear cart and wishlist data on logout\n    this.clearUserDataOnLogout();\n  }\n\n  // Method to refresh user data (cart, wishlist) after login\n  private refreshUserDataOnLogin(): void {\n    // Use setTimeout to avoid circular dependency issues\n    setTimeout(() => {\n      try {\n        // Import services dynamically to avoid circular dependency\n        import('./cart.service').then(({ CartService }) => {\n          const cartService = new CartService(this.http, null as any, null as any);\n          cartService.refreshCartOnLogin();\n        });\n\n        import('./wishlist-new.service').then(({ WishlistNewService }) => {\n          const wishlistService = new WishlistNewService(this.http, this);\n          wishlistService.refreshWishlistOnLogin();\n        });\n      } catch (error) {\n        console.error('Error refreshing user data on login:', error);\n      }\n    }, 100);\n  }\n\n  // Method to clear user data on logout\n  private clearUserDataOnLogout(): void {\n    setTimeout(() => {\n      try {\n        // Import services dynamically to avoid circular dependency\n        import('./cart.service').then(({ CartService }) => {\n          const cartService = new CartService(this.http, null as any, null as any);\n          cartService.clearCartOnLogout();\n        });\n\n        import('./wishlist-new.service').then(({ WishlistNewService }) => {\n          const wishlistService = new WishlistNewService(this.http, this);\n          wishlistService.clearWishlistOnLogout();\n        });\n      } catch (error) {\n        console.error('Error clearing user data on logout:', error);\n      }\n    }, 100);\n  }\n\n  getCurrentUser(): Observable<{ user: User }> {\n    return this.http.get<{ user: User }>(`${this.API_URL}/auth/me`);\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem('token');\n  }\n\n  private setToken(token: string): void {\n    localStorage.setItem('token', token);\n  }\n\n  get currentUserValue(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  get isAuthenticated(): boolean {\n    return this.isAuthenticatedSubject.value;\n  }\n\n  isAdmin(): boolean {\n    const user = this.currentUserValue;\n    return user?.role === 'admin';\n  }\n\n  isVendor(): boolean {\n    const user = this.currentUserValue;\n    return user?.role === 'vendor';\n  }\n\n  isCustomer(): boolean {\n    const user = this.currentUserValue;\n    return user?.role === 'customer';\n  }\n\n  // Helper methods for checking authentication before actions\n  requireAuth(action: string = 'perform this action'): boolean {\n    if (!this.isAuthenticated) {\n      this.showLoginPrompt(action);\n      return false;\n    }\n    return true;\n  }\n\n  requireSuperAdminAuth(action: string = 'perform this action'): boolean {\n    if (!this.isAuthenticated) {\n      this.showLoginPrompt(action);\n      return false;\n    }\n\n    if (!this.isAdmin()) {\n      this.showRoleError('super admin', action);\n      return false;\n    }\n\n    return true;\n  }\n\n  requireCustomerAuth(action: string = 'perform this action'): boolean {\n    if (!this.isAuthenticated) {\n      this.showLoginPrompt(action);\n      return false;\n    }\n\n    if (!this.isCustomer()) {\n      this.showRoleError('customer', action);\n      return false;\n    }\n\n    return true;\n  }\n\n  private showLoginPrompt(action: string): void {\n    const message = `Please login to ${action}`;\n    if (confirm(`${message}. Would you like to login now?`)) {\n      this.router.navigate(['/auth/login'], {\n        queryParams: { returnUrl: this.router.url }\n      });\n    }\n  }\n\n  private showRoleError(requiredRole: string, action: string): void {\n    alert(`Only ${requiredRole}s can ${action}. Please login with a ${requiredRole} account.`);\n  }\n\n  // Social interaction methods with authentication checks\n  canLike(): boolean {\n    return this.requireCustomerAuth('like posts');\n  }\n\n  canComment(): boolean {\n    return this.requireCustomerAuth('comment on posts');\n  }\n\n  canAddToCart(): boolean {\n    return this.requireCustomerAuth('add items to cart');\n  }\n\n  canAddToWishlist(): boolean {\n    return this.requireCustomerAuth('add items to wishlist');\n  }\n\n  canBuy(): boolean {\n    return this.requireCustomerAuth('purchase items');\n  }\n\n  // Get auth headers for API calls\n  getAuthHeaders(): { [key: string]: string } {\n    const token = this.getToken();\n    return token ? { 'Authorization': `Bearer ${token}` } : {};\n  }\n}\n"], "mappings": "AAEA,SAASA,eAAe,EAAcC,GAAG,EAAEC,UAAU,EAAEC,UAAU,QAAY,MAAM;;;;AASnF,OAAM,MAAOC,WAAW;EAQtBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IATC,KAAAC,OAAO,GAAG,2BAA2B;IAC9C,KAAAC,kBAAkB,GAAG,IAAIT,eAAe,CAAc,IAAI,CAAC;IAC3D,KAAAU,sBAAsB,GAAG,IAAIV,eAAe,CAAU,KAAK,CAAC;IAE7D,KAAAW,YAAY,GAAG,IAAI,CAACF,kBAAkB,CAACG,YAAY,EAAE;IACrD,KAAAC,gBAAgB,GAAG,IAAI,CAACH,sBAAsB,CAACE,YAAY,EAAE;EAKjE;EAEHE,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,IAAID,KAAK,EAAE;MACT,IAAI,CAACE,cAAc,EAAE,CAACC,SAAS,CAAC;QAC9BC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACX,kBAAkB,CAACU,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;UAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;QACxC,CAAC;QACDG,KAAK,EAAEA,CAAA,KAAK;UACV;UACA,IAAI,CAACC,SAAS,EAAE;QAClB;OACD,CAAC;;EAEN;EAEAC,KAAKA,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAACnB,IAAI,CAACoB,IAAI,CAAe,GAAG,IAAI,CAAClB,OAAO,aAAa,EAAEiB,WAAW,CAAC,CAC3EE,IAAI,CACH1B,GAAG,CAACmB,QAAQ,IAAG;MACb,IAAI,CAACQ,QAAQ,CAACR,QAAQ,CAACL,KAAK,CAAC;MAC7B,IAAI,CAACN,kBAAkB,CAACU,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;MAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;MAEtC;MACA,IAAI,CAACU,sBAAsB,EAAE;IAC/B,CAAC,CAAC,EACF3B,UAAU,CAACoB,KAAK,IAAG;MACjBQ,OAAO,CAACR,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,OAAOnB,UAAU,CAAC,MAAMmB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAMAS,QAAQA,CAACC,QAAyB;IAChC,OAAO,IAAI,CAAC1B,IAAI,CAACoB,IAAI,CAAe,GAAG,IAAI,CAAClB,OAAO,gBAAgB,EAAEwB,QAAQ,CAAC,CAC3EL,IAAI,CACH1B,GAAG,CAACmB,QAAQ,IAAG;MACb,IAAI,CAACQ,QAAQ,CAACR,QAAQ,CAACL,KAAK,CAAC;MAC7B,IAAI,CAACN,kBAAkB,CAACU,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;MAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;IACxC,CAAC,CAAC,CACH;EACL;EAIAc,MAAMA,CAAA;IACJ,IAAI,CAACV,SAAS,EAAE;IAChB,IAAI,CAAChB,MAAM,CAAC2B,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEQX,SAASA,CAAA;IACfY,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChC,IAAI,CAAC3B,kBAAkB,CAACU,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACT,sBAAsB,CAACS,IAAI,CAAC,KAAK,CAAC;IAEvC;IACA,IAAI,CAACkB,qBAAqB,EAAE;EAC9B;EAEA;EACQR,sBAAsBA,CAAA;IAC5B;IACAS,UAAU,CAAC,MAAK;MACd,IAAI;QACF;QACA,MAAM,CAAC,gBAAgB,CAAC,CAACC,IAAI,CAAC,CAAC;UAAEC;QAAW,CAAE,KAAI;UAChD,MAAMC,WAAW,GAAG,IAAID,WAAW,CAAC,IAAI,CAAClC,IAAI,EAAE,IAAW,EAAE,IAAW,CAAC;UACxEmC,WAAW,CAACC,kBAAkB,EAAE;QAClC,CAAC,CAAC;QAEF,MAAM,CAAC,wBAAwB,CAAC,CAACH,IAAI,CAAC,CAAC;UAAEI;QAAkB,CAAE,KAAI;UAC/D,MAAMC,eAAe,GAAG,IAAID,kBAAkB,CAAC,IAAI,CAACrC,IAAI,EAAE,IAAI,CAAC;UAC/DsC,eAAe,CAACC,sBAAsB,EAAE;QAC1C,CAAC,CAAC;OACH,CAAC,OAAOvB,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;;IAEhE,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACQe,qBAAqBA,CAAA;IAC3BC,UAAU,CAAC,MAAK;MACd,IAAI;QACF;QACA,MAAM,CAAC,gBAAgB,CAAC,CAACC,IAAI,CAAC,CAAC;UAAEC;QAAW,CAAE,KAAI;UAChD,MAAMC,WAAW,GAAG,IAAID,WAAW,CAAC,IAAI,CAAClC,IAAI,EAAE,IAAW,EAAE,IAAW,CAAC;UACxEmC,WAAW,CAACK,iBAAiB,EAAE;QACjC,CAAC,CAAC;QAEF,MAAM,CAAC,wBAAwB,CAAC,CAACP,IAAI,CAAC,CAAC;UAAEI;QAAkB,CAAE,KAAI;UAC/D,MAAMC,eAAe,GAAG,IAAID,kBAAkB,CAAC,IAAI,CAACrC,IAAI,EAAE,IAAI,CAAC;UAC/DsC,eAAe,CAACG,qBAAqB,EAAE;QACzC,CAAC,CAAC;OACH,CAAC,OAAOzB,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;;IAE/D,CAAC,EAAE,GAAG,CAAC;EACT;EAEAL,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACX,IAAI,CAAC0C,GAAG,CAAiB,GAAG,IAAI,CAACxC,OAAO,UAAU,CAAC;EACjE;EAEAQ,QAAQA,CAAA;IACN,OAAOmB,YAAY,CAACc,OAAO,CAAC,OAAO,CAAC;EACtC;EAEQrB,QAAQA,CAACb,KAAa;IAC5BoB,YAAY,CAACe,OAAO,CAAC,OAAO,EAAEnC,KAAK,CAAC;EACtC;EAEA,IAAIoC,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAC1C,kBAAkB,CAAC2C,KAAK;EACtC;EAEA,IAAIC,eAAeA,CAAA;IACjB,OAAO,IAAI,CAAC3C,sBAAsB,CAAC0C,KAAK;EAC1C;EAEAE,OAAOA,CAAA;IACL,MAAMjC,IAAI,GAAG,IAAI,CAAC8B,gBAAgB;IAClC,OAAO9B,IAAI,EAAEkC,IAAI,KAAK,OAAO;EAC/B;EAEAC,QAAQA,CAAA;IACN,MAAMnC,IAAI,GAAG,IAAI,CAAC8B,gBAAgB;IAClC,OAAO9B,IAAI,EAAEkC,IAAI,KAAK,QAAQ;EAChC;EAEAE,UAAUA,CAAA;IACR,MAAMpC,IAAI,GAAG,IAAI,CAAC8B,gBAAgB;IAClC,OAAO9B,IAAI,EAAEkC,IAAI,KAAK,UAAU;EAClC;EAEA;EACAG,WAAWA,CAACC,MAAA,GAAiB,qBAAqB;IAChD,IAAI,CAAC,IAAI,CAACN,eAAe,EAAE;MACzB,IAAI,CAACO,eAAe,CAACD,MAAM,CAAC;MAC5B,OAAO,KAAK;;IAEd,OAAO,IAAI;EACb;EAEAE,qBAAqBA,CAACF,MAAA,GAAiB,qBAAqB;IAC1D,IAAI,CAAC,IAAI,CAACN,eAAe,EAAE;MACzB,IAAI,CAACO,eAAe,CAACD,MAAM,CAAC;MAC5B,OAAO,KAAK;;IAGd,IAAI,CAAC,IAAI,CAACL,OAAO,EAAE,EAAE;MACnB,IAAI,CAACQ,aAAa,CAAC,aAAa,EAAEH,MAAM,CAAC;MACzC,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEAI,mBAAmBA,CAACJ,MAAA,GAAiB,qBAAqB;IACxD,IAAI,CAAC,IAAI,CAACN,eAAe,EAAE;MACzB,IAAI,CAACO,eAAe,CAACD,MAAM,CAAC;MAC5B,OAAO,KAAK;;IAGd,IAAI,CAAC,IAAI,CAACF,UAAU,EAAE,EAAE;MACtB,IAAI,CAACK,aAAa,CAAC,UAAU,EAAEH,MAAM,CAAC;MACtC,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEQC,eAAeA,CAACD,MAAc;IACpC,MAAMK,OAAO,GAAG,mBAAmBL,MAAM,EAAE;IAC3C,IAAIM,OAAO,CAAC,GAAGD,OAAO,gCAAgC,CAAC,EAAE;MACvD,IAAI,CAACzD,MAAM,CAAC2B,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;QACpCgC,WAAW,EAAE;UAAEC,SAAS,EAAE,IAAI,CAAC5D,MAAM,CAAC6D;QAAG;OAC1C,CAAC;;EAEN;EAEQN,aAAaA,CAACO,YAAoB,EAAEV,MAAc;IACxDW,KAAK,CAAC,QAAQD,YAAY,SAASV,MAAM,yBAAyBU,YAAY,WAAW,CAAC;EAC5F;EAEA;EACAE,OAAOA,CAAA;IACL,OAAO,IAAI,CAACR,mBAAmB,CAAC,YAAY,CAAC;EAC/C;EAEAS,UAAUA,CAAA;IACR,OAAO,IAAI,CAACT,mBAAmB,CAAC,kBAAkB,CAAC;EACrD;EAEAU,YAAYA,CAAA;IACV,OAAO,IAAI,CAACV,mBAAmB,CAAC,mBAAmB,CAAC;EACtD;EAEAW,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACX,mBAAmB,CAAC,uBAAuB,CAAC;EAC1D;EAEAY,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACZ,mBAAmB,CAAC,gBAAgB,CAAC;EACnD;EAEA;EACAa,cAAcA,CAAA;IACZ,MAAM7D,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,OAAOD,KAAK,GAAG;MAAE,eAAe,EAAE,UAAUA,KAAK;IAAE,CAAE,GAAG,EAAE;EAC5D;;;uBArOWX,WAAW,EAAAyE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAX9E,WAAW;MAAA+E,OAAA,EAAX/E,WAAW,CAAAgF,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}