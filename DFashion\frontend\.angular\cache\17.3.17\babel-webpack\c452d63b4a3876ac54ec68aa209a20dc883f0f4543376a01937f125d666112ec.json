{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { e as readTask, w as writeTask } from './index-a1a47f01.js';\nimport { f as findClosestIonContent, s as scrollToTop } from './index-f3946ac1.js';\nimport { c as componentOnReady } from './helpers-be245865.js';\nimport './index-9b0d46f4.js';\nconst startStatusTap = () => {\n  const win = window;\n  win.addEventListener('statusTap', () => {\n    readTask(() => {\n      const width = win.innerWidth;\n      const height = win.innerHeight;\n      const el = document.elementFromPoint(width / 2, height / 2);\n      if (!el) {\n        return;\n      }\n      const contentEl = findClosestIonContent(el);\n      if (contentEl) {\n        new Promise(resolve => componentOnReady(contentEl, resolve)).then(() => {\n          writeTask(/*#__PURE__*/_asyncToGenerator(function* () {\n            /**\n             * If scrolling and user taps status bar,\n             * only calling scrollToTop is not enough\n             * as engines like WebKit will jump the\n             * scroll position back down and complete\n             * any in-progress momentum scrolling.\n             */\n            contentEl.style.setProperty('--overflow', 'hidden');\n            yield scrollToTop(contentEl, 300);\n            contentEl.style.removeProperty('--overflow');\n          }));\n        });\n      }\n    });\n  });\n};\nexport { startStatusTap };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}