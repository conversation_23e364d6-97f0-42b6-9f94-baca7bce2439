{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = [\"storyVideo\"];\nconst _c1 = [\"storyThumbnails\"];\nfunction StoriesViewerComponent_div_0_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelement(1, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r4 === ctx_r1.currentIndex)(\"completed\", i_r4 < ctx_r1.currentIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getProgressWidth(i_r4), \"%\")(\"animation-duration\", story_r3.media.duration, \"s\");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_14_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_14_div_6_Template_div_click_0_listener() {\n      const i_r7 = i0.ɵɵrestoreView(_r6).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.jumpToStoryIndex(i_r7));\n    });\n    i0.ɵɵelement(1, \"img\", 48);\n    i0.ɵɵelementStart(2, \"div\", 49);\n    i0.ɵɵelement(3, \"img\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r8 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", i_r7 === ctx_r1.currentIndex)(\"viewed\", i_r7 < ctx_r1.currentIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getStoryThumbnail(story_r8), i0.ɵɵsanitizeUrl)(\"alt\", story_r8.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", story_r8.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", story_r8.user.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getThumbnailProgress(i_r7), \"%\");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40)(2, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_14_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.scrollStoriesLeft());\n    });\n    i0.ɵɵelement(3, \"i\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 43, 0);\n    i0.ɵɵtemplate(6, StoriesViewerComponent_div_0_div_14_div_6_Template, 5, 10, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_14_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.scrollStoriesRight());\n    });\n    i0.ɵɵelement(8, \"i\", 46);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canScrollLeft);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canScrollRight);\n  }\n}\nfunction StoriesViewerComponent_div_0_img_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 52, 1);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.media.url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.caption);\n  }\n}\nfunction StoriesViewerComponent_div_0_video_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"video\", 53, 2);\n    i0.ɵɵlistener(\"ended\", function StoriesViewerComponent_div_0_video_17_Template_video_ended_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.media.url, i0.ɵɵsanitizeUrl)(\"poster\", ctx_r1.currentStory.media.thumbnail, i0.ɵɵsanitizeUrl)(\"muted\", ctx_r1.isMuted)(\"autoplay\", true)(\"loop\", false);\n  }\n}\nfunction StoriesViewerComponent_div_0_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_19_Template_div_click_0_listener() {\n      const productTag_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showProductModal(productTag_r11.product));\n    });\n    i0.ɵɵelementStart(1, \"div\", 55);\n    i0.ɵɵelement(2, \"i\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 57)(4, \"span\", 58);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 59);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const productTag_r11 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", productTag_r11.position.x, \"%\")(\"top\", productTag_r11.position.y, \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(productTag_r11.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(8, 6, productTag_r11.product.price, \"1.0-0\"), \"\");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.currentStory.caption, \" \");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_24_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.buyNow());\n    });\n    i0.ɵɵelement(2, \"i\", 63);\n    i0.ɵɵtext(3, \" Buy Now \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_24_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart());\n    });\n    i0.ɵɵelement(5, \"i\", 65);\n    i0.ɵɵtext(6, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_24_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist());\n    });\n    i0.ɵɵelement(8, \"i\", 31);\n    i0.ɵɵtext(9, \" Wishlist \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StoriesViewerComponent_div_0_button_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_button_32_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleSound());\n    });\n    i0.ɵɵelement(1, \"i\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"fa-volume-up\", !ctx_r1.isMuted)(\"fa-volume-mute\", ctx_r1.isMuted);\n  }\n}\nfunction StoriesViewerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleStoryClick($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 7)(2, \"div\", 8);\n    i0.ɵɵelement(3, \"img\", 9);\n    i0.ɵɵelementStart(4, \"div\", 10)(5, \"span\", 11);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 12);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 13)(10, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStories());\n    });\n    i0.ɵɵelement(11, \"i\", 15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 16);\n    i0.ɵɵtemplate(13, StoriesViewerComponent_div_0_div_13_Template, 2, 8, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, StoriesViewerComponent_div_0_div_14_Template, 9, 3, \"div\", 18);\n    i0.ɵɵelementStart(15, \"div\", 19);\n    i0.ɵɵtemplate(16, StoriesViewerComponent_div_0_img_16_Template, 2, 2, \"img\", 20)(17, StoriesViewerComponent_div_0_video_17_Template, 2, 5, \"video\", 21);\n    i0.ɵɵelementStart(18, \"div\", 22);\n    i0.ɵɵtemplate(19, StoriesViewerComponent_div_0_div_19_Template, 9, 9, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, StoriesViewerComponent_div_0_div_20_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_21_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_22_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 27);\n    i0.ɵɵtemplate(24, StoriesViewerComponent_div_0_div_24_Template, 10, 0, \"div\", 28);\n    i0.ɵɵelementStart(25, \"div\", 29)(26, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleLike());\n    });\n    i0.ɵɵelement(27, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(29, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareStory());\n    });\n    i0.ɵɵelement(31, \"i\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(32, StoriesViewerComponent_div_0_button_32_Template, 2, 4, \"button\", 36);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.user.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentStory.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.currentStory.createdAt));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.stories.length > 5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"video\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentStory.products);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.caption);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.products.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isLiked);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"video\");\n  }\n}\nfunction StoriesViewerComponent_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 83);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, ctx_r1.selectedProduct.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction StoriesViewerComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 70);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 71)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelement(6, \"i\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 72);\n    i0.ɵɵelement(8, \"img\", 73);\n    i0.ɵɵelementStart(9, \"div\", 74)(10, \"p\", 75);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 76)(13, \"span\", 77);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, StoriesViewerComponent_div_1_span_16_Template, 3, 4, \"span\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 79)(18, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.buyProductNow());\n    });\n    i0.ɵɵtext(19, \"Buy Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addProductToCart());\n    });\n    i0.ɵɵtext(21, \"Add to Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addProductToWishlist());\n    });\n    i0.ɵɵtext(23, \"Add to Wishlist\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.selectedProduct.images[0] == null ? null : ctx_r1.selectedProduct.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.selectedProduct.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(15, 6, ctx_r1.selectedProduct.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedProduct.originalPrice);\n  }\n}\nfunction StoriesViewerComponent_div_2_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91);\n    i0.ɵɵelement(1, \"img\", 92);\n    i0.ɵɵelementStart(2, \"div\", 93)(3, \"span\", 94);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 95);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 96);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const comment_r16 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", comment_r16.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", comment_r16.user.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(comment_r16.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r16.text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(comment_r16.commentedAt));\n  }\n}\nfunction StoriesViewerComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 84);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeComments());\n    });\n    i0.ɵɵelementStart(1, \"div\", 70);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 71)(3, \"h3\");\n    i0.ɵɵtext(4, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeComments());\n    });\n    i0.ɵɵelement(6, \"i\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 85);\n    i0.ɵɵtemplate(8, StoriesViewerComponent_div_2_div_8_Template, 9, 5, \"div\", 86);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 87)(10, \"input\", 88);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StoriesViewerComponent_div_2_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newComment, $event) || (ctx_r1.newComment = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function StoriesViewerComponent_div_2_Template_input_keyup_enter_10_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addComment());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addComment());\n    });\n    i0.ɵɵelement(12, \"i\", 90);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.comments);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newComment);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.newComment.trim());\n  }\n}\nexport class StoriesViewerComponent {\n  constructor(router, route) {\n    this.router = router;\n    this.route = route;\n    this.stories = [];\n    this.currentIndex = 0;\n    this.isLiked = false;\n    this.isMuted = true;\n    this.selectedProduct = null;\n    this.showCommentsModal = false;\n    this.comments = [];\n    this.newComment = '';\n    // Navigation slider properties\n    this.canScrollLeft = false;\n    this.canScrollRight = false;\n    this.storyDuration = 15000; // 15 seconds default\n  }\n  ngOnInit() {\n    // Handle query parameters for story index\n    this.route.queryParams.subscribe(queryParams => {\n      if (queryParams['index']) {\n        this.currentIndex = parseInt(queryParams['index'], 10) || 0;\n      }\n    });\n    this.route.params.subscribe(params => {\n      if (params['userId']) {\n        this.loadUserStories(params['userId']);\n      } else {\n        this.loadStories();\n      }\n      if (params['storyId']) {\n        this.jumpToStory(params['storyId']);\n      }\n    });\n    // Add keyboard listeners for better UX\n    this.addKeyboardListeners();\n    this.addTouchListeners();\n  }\n  ngOnDestroy() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n    }\n  }\n  loadStories() {\n    // Load stories from real API\n    fetch('http://localhost:5000/api/stories').then(response => response.json()).then(data => {\n      if (data.success) {\n        this.stories = data.stories.filter(story => story.isActive);\n        if (this.stories.length > 0) {\n          // Use the index from query params or default to 0\n          const startIndex = Math.min(this.currentIndex, this.stories.length - 1);\n          this.currentIndex = startIndex;\n          this.currentStory = this.stories[startIndex];\n          this.startStoryTimer();\n        }\n      }\n    }).catch(error => {\n      console.error('Error loading stories:', error);\n      // Show error message to user\n      alert('Failed to load stories. Please try again.');\n    });\n  }\n  loadUserStories(userId) {\n    // Load specific user's stories from real API\n    fetch(`http://localhost:5000/api/stories/user/${userId}`).then(response => response.json()).then(data => {\n      if (data.success) {\n        this.stories = data.stories.filter(story => story.isActive);\n        if (this.stories.length > 0) {\n          this.currentStory = this.stories[0];\n          this.startStoryTimer();\n        }\n      }\n    }).catch(error => {\n      console.error('Error loading user stories:', error);\n    });\n  }\n  jumpToStory(storyId) {\n    const index = this.stories.findIndex(s => s._id === storyId);\n    if (index !== -1) {\n      this.currentIndex = index;\n      this.currentStory = this.stories[index];\n      this.startStoryTimer();\n    }\n  }\n  startStoryTimer() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n    }\n    const duration = this.currentStory.media.type === 'video' ? this.currentStory.media.duration * 1000 : this.storyDuration;\n    this.progressTimer = setTimeout(() => {\n      this.nextStory();\n    }, duration);\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.currentIndex++;\n      this.currentStory = this.stories[this.currentIndex];\n      this.startStoryTimer();\n      this.updateNavigationSlider();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.currentIndex--;\n      this.currentStory = this.stories[this.currentIndex];\n      this.startStoryTimer();\n      this.updateNavigationSlider();\n    }\n  }\n  jumpToStoryIndex(index) {\n    if (index >= 0 && index < this.stories.length && index !== this.currentIndex) {\n      this.currentIndex = index;\n      this.currentStory = this.stories[index];\n      this.startStoryTimer();\n      this.updateNavigationSlider();\n    }\n  }\n  handleStoryClick(event) {\n    const rect = event.target.getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n    if (clickX < width * 0.3) {\n      this.previousStory();\n    } else if (clickX > width * 0.7) {\n      this.nextStory();\n    }\n  }\n  getProgressWidth(index) {\n    if (index < this.currentIndex) return 100;\n    if (index > this.currentIndex) return 0;\n    return 0; // Will be animated by CSS\n  }\n  closeStories() {\n    // Navigate back to the previous page or home\n    if (window.history.length > 1) {\n      window.history.back();\n    } else {\n      this.router.navigate(['/social']);\n    }\n  }\n  // E-commerce actions\n  buyNow() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n  addToCart() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      // TODO: Add to cart via service\n      console.log('Add to cart from story:', product);\n    }\n  }\n  addToWishlist() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      // TODO: Add to wishlist via service\n      console.log('Add to wishlist from story:', product);\n    }\n  }\n  // Social actions\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    // TODO: Like/unlike story via API\n  }\n  openComments() {\n    this.showCommentsModal = true;\n    this.loadComments();\n  }\n  closeComments() {\n    this.showCommentsModal = false;\n  }\n  shareStory() {\n    // TODO: Implement share functionality\n    console.log('Share story:', this.currentStory);\n  }\n  toggleSound() {\n    this.isMuted = !this.isMuted;\n    if (this.storyVideo) {\n      this.storyVideo.nativeElement.muted = this.isMuted;\n    }\n  }\n  // Product modal\n  showProductModal(product) {\n    this.selectedProduct = product;\n  }\n  closeProductModal() {\n    this.selectedProduct = null;\n  }\n  buyProductNow() {\n    if (this.selectedProduct) {\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: this.selectedProduct._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n  addProductToCart() {\n    if (this.selectedProduct) {\n      // TODO: Add to cart via service\n      console.log('Add product to cart:', this.selectedProduct);\n      this.closeProductModal();\n    }\n  }\n  addProductToWishlist() {\n    if (this.selectedProduct) {\n      // TODO: Add to wishlist via service\n      console.log('Add product to wishlist:', this.selectedProduct);\n      this.closeProductModal();\n    }\n  }\n  // Comments\n  loadComments() {\n    // Load comments from API\n    this.comments = [];\n  }\n  addComment() {\n    if (this.newComment.trim()) {\n      // TODO: Add comment via API\n      console.log('Add comment:', this.newComment);\n      this.newComment = '';\n    }\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diffMs = now.getTime() - new Date(date).getTime();\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    if (diffHours < 1) return 'now';\n    if (diffHours < 24) return `${diffHours}h`;\n    return `${Math.floor(diffHours / 24)}d`;\n  }\n  // Keyboard and touch event listeners\n  addKeyboardListeners() {\n    document.addEventListener('keydown', event => {\n      if (event.key === 'ArrowLeft') {\n        this.previousStory();\n      } else if (event.key === 'ArrowRight') {\n        this.nextStory();\n      } else if (event.key === 'Escape') {\n        this.closeStories();\n      }\n    });\n  }\n  addTouchListeners() {\n    let startX = 0;\n    let startY = 0;\n    document.addEventListener('touchstart', event => {\n      startX = event.touches[0].clientX;\n      startY = event.touches[0].clientY;\n    });\n    document.addEventListener('touchend', event => {\n      const endX = event.changedTouches[0].clientX;\n      const endY = event.changedTouches[0].clientY;\n      const diffX = startX - endX;\n      const diffY = startY - endY;\n      // Horizontal swipe\n      if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {\n        if (diffX > 0) {\n          this.nextStory(); // Swipe left - next story\n        } else {\n          this.previousStory(); // Swipe right - previous story\n        }\n      }\n      // Vertical swipe down to close\n      else if (diffY < -100) {\n        this.closeStories();\n      }\n    });\n  }\n  static {\n    this.ɵfac = function StoriesViewerComponent_Factory(t) {\n      return new (t || StoriesViewerComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoriesViewerComponent,\n      selectors: [[\"app-stories-viewer\"]],\n      viewQuery: function StoriesViewerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storyVideo = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storyThumbnails = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 3,\n      consts: [[\"storyThumbnails\", \"\"], [\"storyMedia\", \"\"], [\"storyVideo\", \"\"], [\"class\", \"stories-viewer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"product-modal\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"comments-modal\", 3, \"click\", 4, \"ngIf\"], [1, \"stories-viewer\", 3, \"click\"], [1, \"story-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"username\"], [1, \"timestamp\"], [1, \"story-controls\"], [1, \"btn-close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"progress-container\"], [\"class\", \"progress-bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"story-navigation\", 4, \"ngIf\"], [1, \"story-content\"], [\"class\", \"story-media\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"story-media\", 3, \"src\", \"poster\", \"muted\", \"autoplay\", \"loop\", \"ended\", 4, \"ngIf\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"left\", \"top\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"story-caption\", 4, \"ngIf\"], [1, \"nav-area\", \"nav-prev\", 3, \"click\"], [1, \"nav-area\", \"nav-next\", 3, \"click\"], [1, \"story-actions\"], [\"class\", \"ecommerce-actions\", 4, \"ngIf\"], [1, \"social-actions\"], [1, \"social-btn\", \"like\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"social-btn\", \"comment\", 3, \"click\"], [1, \"fas\", \"fa-comment\"], [1, \"social-btn\", \"share\", 3, \"click\"], [1, \"fas\", \"fa-share\"], [\"class\", \"social-btn sound\", 3, \"click\", 4, \"ngIf\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"story-navigation\"], [1, \"nav-slider-container\"], [1, \"nav-slider-btn\", \"prev\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"story-thumbnails\"], [\"class\", \"story-thumbnail\", 3, \"active\", \"viewed\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-slider-btn\", \"next\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"story-thumbnail\", 3, \"click\"], [1, \"thumbnail-image\", 3, \"src\", \"alt\"], [1, \"thumbnail-overlay\"], [1, \"user-thumbnail-avatar\", 3, \"src\", \"alt\"], [1, \"thumbnail-progress\"], [1, \"story-media\", 3, \"src\", \"alt\"], [1, \"story-media\", 3, \"ended\", \"src\", \"poster\", \"muted\", \"autoplay\", \"loop\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"product-tag-info\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"story-caption\"], [1, \"ecommerce-actions\"], [1, \"action-btn\", \"buy-now\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [1, \"action-btn\", \"add-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"action-btn\", \"wishlist\", 3, \"click\"], [1, \"social-btn\", \"sound\", 3, \"click\"], [1, \"fas\"], [1, \"product-modal\", 3, \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"modal-header\"], [1, \"modal-body\"], [1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-details\"], [1, \"brand\"], [1, \"price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"modal-actions\"], [1, \"btn-primary\", 3, \"click\"], [1, \"btn-secondary\", 3, \"click\"], [1, \"btn-outline\", 3, \"click\"], [1, \"original-price\"], [1, \"comments-modal\", 3, \"click\"], [1, \"comments-list\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [1, \"comment-input\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [3, \"click\", \"disabled\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"comment\"], [1, \"comment-avatar\", 3, \"src\", \"alt\"], [1, \"comment-content\"], [1, \"comment-username\"], [1, \"comment-text\"], [1, \"comment-time\"]],\n      template: function StoriesViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, StoriesViewerComponent_div_0_Template, 33, 14, \"div\", 3)(1, StoriesViewerComponent_div_1_Template, 24, 9, \"div\", 4)(2, StoriesViewerComponent_div_2_Template, 13, 3, \"div\", 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStory);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedProduct);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showCommentsModal);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\".stories-viewer[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 1000;\\n  display: flex;\\n  flex-direction: column;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.story-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  position: relative;\\n  z-index: 10;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 2px solid #fff;\\n}\\n\\n.user-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  color: #fff;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n}\\n\\n.timestamp[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 0.8rem;\\n}\\n\\n.btn-close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 1.2rem;\\n  cursor: pointer;\\n  padding: 8px;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n  padding: 0 20px;\\n  position: absolute;\\n  top: 8px;\\n  left: 0;\\n  right: 0;\\n  z-index: 10;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 1px;\\n  overflow: hidden;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #fff;\\n  width: 0%;\\n  transition: width 0.1s ease;\\n}\\n\\n.progress-bar.active[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progress linear;\\n}\\n\\n.progress-bar.completed[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progress {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n\\n\\n.story-navigation[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 70px;\\n  left: 0;\\n  right: 0;\\n  z-index: 15;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.4) 0%, transparent 100%);\\n  padding: 12px 0;\\n}\\n\\n.nav-slider-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 0 16px;\\n}\\n\\n.nav-slider-btn[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border: none;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.2);\\n  color: #fff;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n\\n.nav-slider-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: scale(1.1);\\n}\\n\\n.nav-slider-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n\\n.story-thumbnails[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  overflow-x: auto;\\n  scroll-behavior: smooth;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n  flex: 1;\\n  padding: 4px 0;\\n}\\n\\n.story-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n.story-thumbnail[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n  border: 2px solid transparent;\\n}\\n\\n.story-thumbnail.active[_ngcontent-%COMP%] {\\n  border-color: #fff;\\n  transform: scale(1.1);\\n}\\n\\n.story-thumbnail.viewed[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n}\\n\\n.story-thumbnail[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.thumbnail-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.thumbnail-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -2px;\\n  right: -2px;\\n  width: 20px;\\n  height: 20px;\\n  border-radius: 50%;\\n  border: 2px solid #000;\\n  overflow: hidden;\\n}\\n\\n.user-thumbnail-avatar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.thumbnail-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  height: 3px;\\n  background: #fff;\\n  transition: width 0.3s ease;\\n  border-radius: 0 0 50px 50px;\\n}\\n\\n.story-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.story-media[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  max-height: 100%;\\n  object-fit: contain;\\n}\\n\\n.product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: all;\\n  cursor: pointer;\\n  transform: translate(-50%, -50%);\\n}\\n\\n.product-tag-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #333;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.product-tag-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 40px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(0, 0, 0, 0.8);\\n  color: #fff;\\n  padding: 8px 12px;\\n  border-radius: 8px;\\n  white-space: nowrap;\\n  font-size: 0.8rem;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.product-tag[_ngcontent-%COMP%]:hover   .product-tag-info[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: translate(-50%, -50%) scale(1);\\n  }\\n  50% {\\n    transform: translate(-50%, -50%) scale(1.1);\\n  }\\n}\\n.story-caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 120px;\\n  left: 20px;\\n  right: 20px;\\n  color: #fff;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n  background: rgba(0, 0, 0, 0.5);\\n  padding: 12px;\\n  border-radius: 8px;\\n}\\n\\n.nav-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 30%;\\n  cursor: pointer;\\n  z-index: 5;\\n}\\n\\n.nav-prev[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n\\n.nav-next[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n\\n.story-actions[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n}\\n\\n.ecommerce-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px;\\n  border: none;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  transition: all 0.2s ease;\\n}\\n\\n.buy-now[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n  color: #fff;\\n}\\n\\n.add-cart[_ngcontent-%COMP%] {\\n  background: #4ecdc4;\\n  color: #fff;\\n}\\n\\n.wishlist[_ngcontent-%COMP%] {\\n  background: #ff9ff3;\\n  color: #fff;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\\n}\\n\\n.social-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 24px;\\n}\\n\\n.social-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border: none;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.2);\\n  color: #fff;\\n  font-size: 1.1rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n}\\n\\n.social-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: scale(1.1);\\n}\\n\\n.social-btn.liked[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n  color: #fff;\\n}\\n\\n.product-modal[_ngcontent-%COMP%], .comments-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.8);\\n  z-index: 1100;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 12px;\\n  max-width: 400px;\\n  width: 100%;\\n  max-height: 80vh;\\n  overflow-y: auto;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n  border-bottom: 1px solid #eee;\\n}\\n\\n.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  object-fit: cover;\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.product-details[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.brand[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-bottom: 8px;\\n}\\n\\n.price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.current-price[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 700;\\n  color: #333;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #999;\\n  text-decoration: line-through;\\n}\\n\\n.modal-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%], .btn-outline[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  border: none;\\n  border-radius: 6px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: #fff;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: #6c757d;\\n  color: #fff;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: #007bff;\\n  border: 1px solid #007bff;\\n}\\n\\n.comments-list[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow-y: auto;\\n  margin-bottom: 16px;\\n}\\n\\n.comment[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n\\n.comment-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n}\\n\\n.comment-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.comment-username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  color: #333;\\n}\\n\\n.comment-text[_ngcontent-%COMP%] {\\n  margin: 4px 0;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n}\\n\\n.comment-time[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.comment-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  align-items: center;\\n  padding-top: 16px;\\n  border-top: 1px solid #eee;\\n}\\n\\n.comment-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 20px;\\n  font-size: 0.9rem;\\n}\\n\\n.comment-input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border: none;\\n  border-radius: 50%;\\n  background: #007bff;\\n  color: #fff;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.comment-input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .story-navigation[_ngcontent-%COMP%] {\\n    top: 60px;\\n    padding: 8px 0;\\n  }\\n  .nav-slider-container[_ngcontent-%COMP%] {\\n    padding: 0 12px;\\n    gap: 6px;\\n  }\\n  .nav-slider-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n    font-size: 0.8rem;\\n  }\\n  .story-thumbnails[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n  .story-thumbnail[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .thumbnail-overlay[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-navigation[_ngcontent-%COMP%] {\\n    top: 55px;\\n    padding: 6px 0;\\n  }\\n  .nav-slider-container[_ngcontent-%COMP%] {\\n    padding: 0 8px;\\n    gap: 4px;\\n  }\\n  .nav-slider-btn[_ngcontent-%COMP%] {\\n    width: 24px;\\n    height: 24px;\\n    font-size: 0.7rem;\\n  }\\n  .story-thumbnails[_ngcontent-%COMP%] {\\n    gap: 4px;\\n  }\\n  .story-thumbnail[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .thumbnail-overlay[_ngcontent-%COMP%] {\\n    width: 14px;\\n    height: 14px;\\n  }\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .story-header[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .user-avatar[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .username[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .timestamp[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n  .progress-container[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n    top: 6px;\\n  }\\n  .story-caption[_ngcontent-%COMP%] {\\n    bottom: 140px;\\n    left: 16px;\\n    right: 16px;\\n    font-size: 0.85rem;\\n    padding: 10px;\\n  }\\n  .story-actions[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n    margin-bottom: 20px;\\n  }\\n  .action-btn[_ngcontent-%COMP%] {\\n    padding: 14px;\\n    font-size: 1rem;\\n    min-height: 48px; \\n\\n  }\\n  .social-actions[_ngcontent-%COMP%] {\\n    gap: 20px;\\n  }\\n  .social-btn[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n    font-size: 1.2rem;\\n  }\\n  .product-tag-icon[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n    font-size: 0.9rem;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n    padding: 6px 10px;\\n  }\\n  .modal-content[_ngcontent-%COMP%] {\\n    margin: 10px;\\n    max-width: calc(100vw - 20px);\\n    max-height: calc(100vh - 20px);\\n  }\\n  .modal-body[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .product-image[_ngcontent-%COMP%] {\\n    height: 180px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-header[_ngcontent-%COMP%] {\\n    padding: 10px 12px;\\n  }\\n  .user-avatar[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .username[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .progress-container[_ngcontent-%COMP%] {\\n    padding: 0 12px;\\n    top: 4px;\\n  }\\n  .story-caption[_ngcontent-%COMP%] {\\n    bottom: 120px;\\n    left: 12px;\\n    right: 12px;\\n    font-size: 0.8rem;\\n    padding: 8px;\\n  }\\n  .story-actions[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    margin-bottom: 16px;\\n  }\\n  .action-btn[_ngcontent-%COMP%] {\\n    padding: 12px;\\n    font-size: 0.9rem;\\n    min-height: 44px;\\n  }\\n  .social-actions[_ngcontent-%COMP%] {\\n    gap: 16px;\\n  }\\n  .social-btn[_ngcontent-%COMP%] {\\n    width: 44px;\\n    height: 44px;\\n    font-size: 1.1rem;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .stories-viewer[_ngcontent-%COMP%] {\\n    max-width: 400px;\\n    margin: 0 auto;\\n    border-radius: 12px;\\n    overflow: hidden;\\n    height: 90vh;\\n    top: 5vh;\\n  }\\n  .story-media[_ngcontent-%COMP%] {\\n    border-radius: 8px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 12px;\\n  }\\n  .action-btn[_ngcontent-%COMP%] {\\n    padding: 10px 16px;\\n    font-size: 0.9rem;\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .stories-viewer[_ngcontent-%COMP%] {\\n    max-width: 450px;\\n    height: 85vh;\\n    top: 7.5vh;\\n  }\\n}\\n\\n\\n.nav-area[_ngcontent-%COMP%] {\\n  -webkit-tap-highlight-color: transparent;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.stories-viewer[_ngcontent-%COMP%]   *[_ngcontent-%COMP%] {\\n  -webkit-touch-callout: none;\\n  -webkit-user-select: none;\\n  user-select: none;\\n}\\n\\n\\n\\n.story-media[_ngcontent-%COMP%] {\\n  transition: opacity 0.3s ease;\\n}\\n\\n.story-media.loading[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n}\\n\\n\\n\\n.btn-close[_ngcontent-%COMP%]:focus, .action-btn[_ngcontent-%COMP%]:focus, .social-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #007bff;\\n  outline-offset: 2px;\\n}\\n\\n\\n\\n.stories-viewer[_ngcontent-%COMP%] {\\n  will-change: transform;\\n  transform: translateZ(0);\\n}\\n\\n.story-media[_ngcontent-%COMP%] {\\n  will-change: opacity;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  will-change: width;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵclassProp", "i_r4", "ctx_r1", "currentIndex", "ɵɵadvance", "ɵɵstyleProp", "getProgressWidth", "story_r3", "media", "duration", "ɵɵlistener", "StoriesViewerComponent_div_0_div_14_div_6_Template_div_click_0_listener", "i_r7", "ɵɵrestoreView", "_r6", "index", "ɵɵnextContext", "ɵɵresetView", "jumpToStoryIndex", "ɵɵproperty", "getStoryThumbnail", "story_r8", "ɵɵsanitizeUrl", "user", "username", "avatar", "fullName", "getThumbnailProgress", "StoriesViewerComponent_div_0_div_14_Template_button_click_2_listener", "_r5", "scrollStoriesLeft", "ɵɵtemplate", "StoriesViewerComponent_div_0_div_14_div_6_Template", "StoriesViewerComponent_div_0_div_14_Template_button_click_7_listener", "scrollStoriesRight", "canScrollLeft", "stories", "canScrollRight", "currentStory", "url", "caption", "StoriesViewerComponent_div_0_video_17_Template_video_ended_0_listener", "_r9", "nextStory", "thumbnail", "isMuted", "StoriesViewerComponent_div_0_div_19_Template_div_click_0_listener", "productTag_r11", "_r10", "$implicit", "showProductModal", "product", "ɵɵtext", "position", "x", "y", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "price", "StoriesViewerComponent_div_0_div_24_Template_button_click_1_listener", "_r12", "buyNow", "StoriesViewerComponent_div_0_div_24_Template_button_click_4_listener", "addToCart", "StoriesViewerComponent_div_0_div_24_Template_button_click_7_listener", "addToWishlist", "StoriesViewerComponent_div_0_button_32_Template_button_click_0_listener", "_r13", "toggleSound", "StoriesViewerComponent_div_0_Template_div_click_0_listener", "$event", "_r1", "handleStoryClick", "StoriesViewerComponent_div_0_Template_button_click_10_listener", "closeStories", "StoriesViewerComponent_div_0_div_13_Template", "StoriesViewerComponent_div_0_div_14_Template", "StoriesViewerComponent_div_0_img_16_Template", "StoriesViewerComponent_div_0_video_17_Template", "StoriesViewerComponent_div_0_div_19_Template", "StoriesViewerComponent_div_0_div_20_Template", "StoriesViewerComponent_div_0_Template_div_click_21_listener", "previousStory", "StoriesViewerComponent_div_0_Template_div_click_22_listener", "StoriesViewerComponent_div_0_div_24_Template", "StoriesViewerComponent_div_0_Template_button_click_26_listener", "toggleLike", "StoriesViewerComponent_div_0_Template_button_click_28_listener", "openComments", "StoriesViewerComponent_div_0_Template_button_click_30_listener", "shareStory", "StoriesViewerComponent_div_0_button_32_Template", "getTimeAgo", "createdAt", "length", "type", "products", "isLiked", "selectedProduct", "originalPrice", "StoriesViewerComponent_div_1_Template_div_click_0_listener", "_r14", "closeProductModal", "StoriesViewerComponent_div_1_Template_div_click_1_listener", "stopPropagation", "StoriesViewerComponent_div_1_Template_button_click_5_listener", "StoriesViewerComponent_div_1_span_16_Template", "StoriesViewerComponent_div_1_Template_button_click_18_listener", "buyProductNow", "StoriesViewerComponent_div_1_Template_button_click_20_listener", "addProductToCart", "StoriesViewerComponent_div_1_Template_button_click_22_listener", "addProductToWishlist", "images", "brand", "comment_r16", "text", "commentedAt", "StoriesViewerComponent_div_2_Template_div_click_0_listener", "_r15", "closeComments", "StoriesViewerComponent_div_2_Template_div_click_1_listener", "StoriesViewerComponent_div_2_Template_button_click_5_listener", "StoriesViewerComponent_div_2_div_8_Template", "ɵɵtwoWayListener", "StoriesViewerComponent_div_2_Template_input_ngModelChange_10_listener", "ɵɵtwoWayBindingSet", "newComment", "StoriesViewerComponent_div_2_Template_input_keyup_enter_10_listener", "addComment", "StoriesViewerComponent_div_2_Template_button_click_11_listener", "comments", "ɵɵtwoWayProperty", "trim", "StoriesViewerComponent", "constructor", "router", "route", "showCommentsModal", "storyDuration", "ngOnInit", "queryParams", "subscribe", "parseInt", "params", "loadUserStories", "loadStories", "jumpToStory", "addKeyboardListeners", "addTouchListeners", "ngOnDestroy", "progressTimer", "clearTimeout", "fetch", "then", "response", "json", "data", "success", "filter", "story", "isActive", "startIndex", "Math", "min", "startStoryTimer", "catch", "error", "console", "alert", "userId", "storyId", "findIndex", "s", "_id", "setTimeout", "updateNavigationSlider", "event", "rect", "target", "getBoundingClientRect", "clickX", "clientX", "left", "width", "window", "history", "back", "navigate", "productId", "source", "log", "loadComments", "storyVideo", "nativeElement", "muted", "date", "now", "Date", "diffMs", "getTime", "diffHours", "floor", "document", "addEventListener", "key", "startX", "startY", "touches", "clientY", "endX", "changedTouches", "endY", "diffX", "diffY", "abs", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "selectors", "viewQuery", "StoriesViewerComponent_Query", "rf", "ctx", "StoriesViewerComponent_div_0_Template", "StoriesViewerComponent_div_1_Template", "StoriesViewerComponent_div_2_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\stories\\stories-viewer.component.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>estroy, ViewChild, ElementRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\n\ninterface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar?: string;\n  };\n  media: {\n    type: 'image' | 'video';\n    url: string;\n    duration: number;\n    thumbnail?: string;\n  };\n  caption?: string;\n  products: {\n    _id: string;\n    product: {\n      _id: string;\n      name: string;\n      price: number;\n      originalPrice?: number;\n      images: { url: string; alt: string }[];\n      brand: string;\n    };\n    position: { x: number; y: number };\n  }[];\n  viewers: { user: string; viewedAt: Date }[];\n  isActive: boolean;\n  expiresAt: Date;\n  createdAt: Date;\n}\n\n@Component({\n  selector: 'app-stories-viewer',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"stories-viewer\" *ngIf=\"currentStory\" (click)=\"handleStoryClick($event)\">\n      <!-- Header -->\n      <div class=\"story-header\">\n        <div class=\"user-info\">\n          <img [src]=\"currentStory.user.avatar || '/assets/images/default-avatar.png'\" \n               [alt]=\"currentStory.user.fullName\" class=\"user-avatar\">\n          <div class=\"user-details\">\n            <span class=\"username\">{{ currentStory.user.username }}</span>\n            <span class=\"timestamp\">{{ getTimeAgo(currentStory.createdAt) }}</span>\n          </div>\n        </div>\n        \n        <div class=\"story-controls\">\n          <button class=\"btn-close\" (click)=\"closeStories()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n      </div>\n\n      <!-- Progress Bars -->\n      <div class=\"progress-container\">\n        <div class=\"progress-bar\"\n             *ngFor=\"let story of stories; let i = index\"\n             [class.active]=\"i === currentIndex\"\n             [class.completed]=\"i < currentIndex\">\n          <div class=\"progress-fill\"\n               [style.width.%]=\"getProgressWidth(i)\"\n               [style.animation-duration.s]=\"story.media.duration\"></div>\n        </div>\n      </div>\n\n      <!-- Story Navigation Slider (for many stories) -->\n      <div class=\"story-navigation\" *ngIf=\"stories.length > 5\">\n        <div class=\"nav-slider-container\">\n          <button class=\"nav-slider-btn prev\"\n                  (click)=\"scrollStoriesLeft()\"\n                  [disabled]=\"!canScrollLeft\">\n            <i class=\"fas fa-chevron-left\"></i>\n          </button>\n\n          <div class=\"story-thumbnails\" #storyThumbnails>\n            <div class=\"story-thumbnail\"\n                 *ngFor=\"let story of stories; let i = index\"\n                 [class.active]=\"i === currentIndex\"\n                 [class.viewed]=\"i < currentIndex\"\n                 (click)=\"jumpToStoryIndex(i)\">\n              <img [src]=\"getStoryThumbnail(story)\"\n                   [alt]=\"story.user.username\"\n                   class=\"thumbnail-image\">\n              <div class=\"thumbnail-overlay\">\n                <img [src]=\"story.user.avatar || '/assets/images/default-avatar.png'\"\n                     [alt]=\"story.user.fullName\"\n                     class=\"user-thumbnail-avatar\">\n              </div>\n              <div class=\"thumbnail-progress\"\n                   [style.width.%]=\"getThumbnailProgress(i)\"></div>\n            </div>\n          </div>\n\n          <button class=\"nav-slider-btn next\"\n                  (click)=\"scrollStoriesRight()\"\n                  [disabled]=\"!canScrollRight\">\n            <i class=\"fas fa-chevron-right\"></i>\n          </button>\n        </div>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story-content\">\n        <!-- Image Story -->\n        <img *ngIf=\"currentStory.media.type === 'image'\" \n             [src]=\"currentStory.media.url\" \n             [alt]=\"currentStory.caption\"\n             class=\"story-media\"\n             #storyMedia>\n\n        <!-- Video Story -->\n        <video *ngIf=\"currentStory.media.type === 'video'\"\n               [src]=\"currentStory.media.url\"\n               class=\"story-media\"\n               [poster]=\"currentStory.media.thumbnail\"\n               [muted]=\"isMuted\"\n               [autoplay]=\"true\"\n               [loop]=\"false\"\n               #storyVideo\n               (ended)=\"nextStory()\">\n        </video>\n\n        <!-- Product Tags -->\n        <div class=\"product-tags\">\n          <div class=\"product-tag\" \n               *ngFor=\"let productTag of currentStory.products\"\n               [style.left.%]=\"productTag.position.x\"\n               [style.top.%]=\"productTag.position.y\"\n               (click)=\"showProductModal(productTag.product)\">\n            <div class=\"product-tag-icon\">\n              <i class=\"fas fa-shopping-bag\"></i>\n            </div>\n            <div class=\"product-tag-info\">\n              <span class=\"product-name\">{{ productTag.product.name }}</span>\n              <span class=\"product-price\">₹{{ productTag.product.price | number:'1.0-0' }}</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Caption -->\n        <div class=\"story-caption\" *ngIf=\"currentStory.caption\">\n          {{ currentStory.caption }}\n        </div>\n      </div>\n\n      <!-- Navigation Areas -->\n      <div class=\"nav-area nav-prev\" (click)=\"previousStory()\"></div>\n      <div class=\"nav-area nav-next\" (click)=\"nextStory()\"></div>\n\n      <!-- Bottom Actions -->\n      <div class=\"story-actions\">\n        <!-- E-commerce Actions -->\n        <div class=\"ecommerce-actions\" *ngIf=\"currentStory.products.length > 0\">\n          <button class=\"action-btn buy-now\" (click)=\"buyNow()\">\n            <i class=\"fas fa-bolt\"></i>\n            Buy Now\n          </button>\n          <button class=\"action-btn add-cart\" (click)=\"addToCart()\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            Add to Cart\n          </button>\n          <button class=\"action-btn wishlist\" (click)=\"addToWishlist()\">\n            <i class=\"fas fa-heart\"></i>\n            Wishlist\n          </button>\n        </div>\n\n        <!-- Social Actions -->\n        <div class=\"social-actions\">\n          <button class=\"social-btn like\" [class.liked]=\"isLiked\" (click)=\"toggleLike()\">\n            <i class=\"fas fa-heart\"></i>\n          </button>\n          <button class=\"social-btn comment\" (click)=\"openComments()\">\n            <i class=\"fas fa-comment\"></i>\n          </button>\n          <button class=\"social-btn share\" (click)=\"shareStory()\">\n            <i class=\"fas fa-share\"></i>\n          </button>\n          <button class=\"social-btn sound\" (click)=\"toggleSound()\" *ngIf=\"currentStory.media.type === 'video'\">\n            <i class=\"fas\" [class.fa-volume-up]=\"!isMuted\" [class.fa-volume-mute]=\"isMuted\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Product Modal -->\n    <div class=\"product-modal\" *ngIf=\"selectedProduct\" (click)=\"closeProductModal()\">\n      <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n        <div class=\"modal-header\">\n          <h3>{{ selectedProduct.name }}</h3>\n          <button class=\"btn-close\" (click)=\"closeProductModal()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        \n        <div class=\"modal-body\">\n          <img [src]=\"selectedProduct.images[0]?.url\" [alt]=\"selectedProduct.name\" class=\"product-image\">\n          \n          <div class=\"product-details\">\n            <p class=\"brand\">{{ selectedProduct.brand }}</p>\n            <div class=\"price\">\n              <span class=\"current-price\">₹{{ selectedProduct.price | number:'1.0-0' }}</span>\n              <span class=\"original-price\" *ngIf=\"selectedProduct.originalPrice\">\n                ₹{{ selectedProduct.originalPrice | number:'1.0-0' }}\n              </span>\n            </div>\n          </div>\n          \n          <div class=\"modal-actions\">\n            <button class=\"btn-primary\" (click)=\"buyProductNow()\">Buy Now</button>\n            <button class=\"btn-secondary\" (click)=\"addProductToCart()\">Add to Cart</button>\n            <button class=\"btn-outline\" (click)=\"addProductToWishlist()\">Add to Wishlist</button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Comments Modal -->\n    <div class=\"comments-modal\" *ngIf=\"showCommentsModal\" (click)=\"closeComments()\">\n      <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n        <div class=\"modal-header\">\n          <h3>Comments</h3>\n          <button class=\"btn-close\" (click)=\"closeComments()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        \n        <div class=\"comments-list\">\n          <div class=\"comment\" *ngFor=\"let comment of comments\">\n            <img [src]=\"comment.user.avatar || '/assets/images/default-avatar.png'\" \n                 [alt]=\"comment.user.fullName\" class=\"comment-avatar\">\n            <div class=\"comment-content\">\n              <span class=\"comment-username\">{{ comment.user.username }}</span>\n              <p class=\"comment-text\">{{ comment.text }}</p>\n              <span class=\"comment-time\">{{ getTimeAgo(comment.commentedAt) }}</span>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"comment-input\">\n          <input type=\"text\" \n                 [(ngModel)]=\"newComment\" \n                 placeholder=\"Add a comment...\"\n                 (keyup.enter)=\"addComment()\">\n          <button (click)=\"addComment()\" [disabled]=\"!newComment.trim()\">\n            <i class=\"fas fa-paper-plane\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .stories-viewer {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: #000;\n      z-index: 1000;\n      display: flex;\n      flex-direction: column;\n      user-select: none;\n    }\n\n    .story-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 20px;\n      background: linear-gradient(180deg, rgba(0,0,0,0.6) 0%, transparent 100%);\n      position: relative;\n      z-index: 10;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .user-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      border: 2px solid #fff;\n    }\n\n    .user-details {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .username {\n      color: #fff;\n      font-weight: 600;\n      font-size: 0.9rem;\n    }\n\n    .timestamp {\n      color: rgba(255,255,255,0.7);\n      font-size: 0.8rem;\n    }\n\n    .btn-close {\n      background: none;\n      border: none;\n      color: #fff;\n      font-size: 1.2rem;\n      cursor: pointer;\n      padding: 8px;\n    }\n\n    .progress-container {\n      display: flex;\n      gap: 2px;\n      padding: 0 20px;\n      position: absolute;\n      top: 8px;\n      left: 0;\n      right: 0;\n      z-index: 10;\n    }\n\n    .progress-bar {\n      flex: 1;\n      height: 2px;\n      background: rgba(255,255,255,0.3);\n      border-radius: 1px;\n      overflow: hidden;\n    }\n\n    .progress-fill {\n      height: 100%;\n      background: #fff;\n      width: 0%;\n      transition: width 0.1s ease;\n    }\n\n    .progress-bar.active .progress-fill {\n      animation: progress linear;\n    }\n\n    .progress-bar.completed .progress-fill {\n      width: 100%;\n    }\n\n    @keyframes progress {\n      from { width: 0%; }\n      to { width: 100%; }\n    }\n\n    /* Story Navigation Slider */\n    .story-navigation {\n      position: absolute;\n      top: 70px;\n      left: 0;\n      right: 0;\n      z-index: 15;\n      background: linear-gradient(180deg, rgba(0,0,0,0.4) 0%, transparent 100%);\n      padding: 12px 0;\n    }\n\n    .nav-slider-container {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      padding: 0 16px;\n    }\n\n    .nav-slider-btn {\n      width: 32px;\n      height: 32px;\n      border: none;\n      border-radius: 50%;\n      background: rgba(255,255,255,0.2);\n      color: #fff;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      transition: all 0.2s ease;\n      flex-shrink: 0;\n    }\n\n    .nav-slider-btn:hover:not(:disabled) {\n      background: rgba(255,255,255,0.3);\n      transform: scale(1.1);\n    }\n\n    .nav-slider-btn:disabled {\n      opacity: 0.3;\n      cursor: not-allowed;\n    }\n\n    .story-thumbnails {\n      display: flex;\n      gap: 8px;\n      overflow-x: auto;\n      scroll-behavior: smooth;\n      scrollbar-width: none;\n      -ms-overflow-style: none;\n      flex: 1;\n      padding: 4px 0;\n    }\n\n    .story-thumbnails::-webkit-scrollbar {\n      display: none;\n    }\n\n    .story-thumbnail {\n      position: relative;\n      width: 48px;\n      height: 48px;\n      border-radius: 50%;\n      overflow: hidden;\n      cursor: pointer;\n      transition: all 0.2s ease;\n      flex-shrink: 0;\n      border: 2px solid transparent;\n    }\n\n    .story-thumbnail.active {\n      border-color: #fff;\n      transform: scale(1.1);\n    }\n\n    .story-thumbnail.viewed {\n      opacity: 0.6;\n    }\n\n    .story-thumbnail:hover {\n      transform: scale(1.05);\n    }\n\n    .thumbnail-image {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n\n    .thumbnail-overlay {\n      position: absolute;\n      bottom: -2px;\n      right: -2px;\n      width: 20px;\n      height: 20px;\n      border-radius: 50%;\n      border: 2px solid #000;\n      overflow: hidden;\n    }\n\n    .user-thumbnail-avatar {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n\n    .thumbnail-progress {\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      height: 3px;\n      background: #fff;\n      transition: width 0.3s ease;\n      border-radius: 0 0 50px 50px;\n    }\n\n    .story-content {\n      flex: 1;\n      position: relative;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .story-media {\n      max-width: 100%;\n      max-height: 100%;\n      object-fit: contain;\n    }\n\n    .product-tags {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      pointer-events: none;\n    }\n\n    .product-tag {\n      position: absolute;\n      pointer-events: all;\n      cursor: pointer;\n      transform: translate(-50%, -50%);\n    }\n\n    .product-tag-icon {\n      width: 32px;\n      height: 32px;\n      background: rgba(255,255,255,0.9);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #333;\n      animation: pulse 2s infinite;\n    }\n\n    .product-tag-info {\n      position: absolute;\n      top: 40px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0,0,0,0.8);\n      color: #fff;\n      padding: 8px 12px;\n      border-radius: 8px;\n      white-space: nowrap;\n      font-size: 0.8rem;\n      opacity: 0;\n      transition: opacity 0.3s ease;\n    }\n\n    .product-tag:hover .product-tag-info {\n      opacity: 1;\n    }\n\n    @keyframes pulse {\n      0%, 100% { transform: translate(-50%, -50%) scale(1); }\n      50% { transform: translate(-50%, -50%) scale(1.1); }\n    }\n\n    .story-caption {\n      position: absolute;\n      bottom: 120px;\n      left: 20px;\n      right: 20px;\n      color: #fff;\n      font-size: 0.9rem;\n      line-height: 1.4;\n      background: rgba(0,0,0,0.5);\n      padding: 12px;\n      border-radius: 8px;\n    }\n\n    .nav-area {\n      position: absolute;\n      top: 0;\n      bottom: 0;\n      width: 30%;\n      cursor: pointer;\n      z-index: 5;\n    }\n\n    .nav-prev {\n      left: 0;\n    }\n\n    .nav-next {\n      right: 0;\n    }\n\n    .story-actions {\n      padding: 20px;\n      background: linear-gradient(0deg, rgba(0,0,0,0.6) 0%, transparent 100%);\n    }\n\n    .ecommerce-actions {\n      display: flex;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n\n    .action-btn {\n      flex: 1;\n      padding: 12px;\n      border: none;\n      border-radius: 8px;\n      font-weight: 600;\n      font-size: 0.9rem;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      transition: all 0.2s ease;\n    }\n\n    .buy-now {\n      background: #ff6b6b;\n      color: #fff;\n    }\n\n    .add-cart {\n      background: #4ecdc4;\n      color: #fff;\n    }\n\n    .wishlist {\n      background: #ff9ff3;\n      color: #fff;\n    }\n\n    .action-btn:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 4px 12px rgba(0,0,0,0.3);\n    }\n\n    .social-actions {\n      display: flex;\n      justify-content: center;\n      gap: 24px;\n    }\n\n    .social-btn {\n      width: 44px;\n      height: 44px;\n      border: none;\n      border-radius: 50%;\n      background: rgba(255,255,255,0.2);\n      color: #fff;\n      font-size: 1.1rem;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      transition: all 0.2s ease;\n    }\n\n    .social-btn:hover {\n      background: rgba(255,255,255,0.3);\n      transform: scale(1.1);\n    }\n\n    .social-btn.liked {\n      background: #ff6b6b;\n      color: #fff;\n    }\n\n    .product-modal, .comments-modal {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: rgba(0,0,0,0.8);\n      z-index: 1100;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n    }\n\n    .modal-content {\n      background: #fff;\n      border-radius: 12px;\n      max-width: 400px;\n      width: 100%;\n      max-height: 80vh;\n      overflow-y: auto;\n    }\n\n    .modal-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 20px;\n      border-bottom: 1px solid #eee;\n    }\n\n    .modal-header h3 {\n      margin: 0;\n      font-size: 1.1rem;\n      font-weight: 600;\n    }\n\n    .modal-body {\n      padding: 20px;\n    }\n\n    .product-image {\n      width: 100%;\n      height: 200px;\n      object-fit: cover;\n      border-radius: 8px;\n      margin-bottom: 16px;\n    }\n\n    .product-details {\n      margin-bottom: 20px;\n    }\n\n    .brand {\n      color: #666;\n      font-size: 0.9rem;\n      margin-bottom: 8px;\n    }\n\n    .price {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .current-price {\n      font-size: 1.2rem;\n      font-weight: 700;\n      color: #333;\n    }\n\n    .original-price {\n      font-size: 0.9rem;\n      color: #999;\n      text-decoration: line-through;\n    }\n\n    .modal-actions {\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n    }\n\n    .btn-primary, .btn-secondary, .btn-outline {\n      padding: 12px;\n      border: none;\n      border-radius: 6px;\n      font-weight: 600;\n      cursor: pointer;\n      transition: all 0.2s ease;\n    }\n\n    .btn-primary {\n      background: #007bff;\n      color: #fff;\n    }\n\n    .btn-secondary {\n      background: #6c757d;\n      color: #fff;\n    }\n\n    .btn-outline {\n      background: transparent;\n      color: #007bff;\n      border: 1px solid #007bff;\n    }\n\n    .comments-list {\n      max-height: 300px;\n      overflow-y: auto;\n      margin-bottom: 16px;\n    }\n\n    .comment {\n      display: flex;\n      gap: 12px;\n      margin-bottom: 16px;\n    }\n\n    .comment-avatar {\n      width: 32px;\n      height: 32px;\n      border-radius: 50%;\n    }\n\n    .comment-content {\n      flex: 1;\n    }\n\n    .comment-username {\n      font-weight: 600;\n      font-size: 0.9rem;\n      color: #333;\n    }\n\n    .comment-text {\n      margin: 4px 0;\n      font-size: 0.9rem;\n      line-height: 1.4;\n    }\n\n    .comment-time {\n      font-size: 0.8rem;\n      color: #666;\n    }\n\n    .comment-input {\n      display: flex;\n      gap: 8px;\n      align-items: center;\n      padding-top: 16px;\n      border-top: 1px solid #eee;\n    }\n\n    .comment-input input {\n      flex: 1;\n      padding: 8px 12px;\n      border: 1px solid #ddd;\n      border-radius: 20px;\n      font-size: 0.9rem;\n    }\n\n    .comment-input button {\n      width: 36px;\n      height: 36px;\n      border: none;\n      border-radius: 50%;\n      background: #007bff;\n      color: #fff;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .comment-input button:disabled {\n      background: #ccc;\n      cursor: not-allowed;\n    }\n\n    /* Responsive Navigation Slider */\n    @media (max-width: 768px) {\n      .story-navigation {\n        top: 60px;\n        padding: 8px 0;\n      }\n\n      .nav-slider-container {\n        padding: 0 12px;\n        gap: 6px;\n      }\n\n      .nav-slider-btn {\n        width: 28px;\n        height: 28px;\n        font-size: 0.8rem;\n      }\n\n      .story-thumbnails {\n        gap: 6px;\n      }\n\n      .story-thumbnail {\n        width: 40px;\n        height: 40px;\n      }\n\n      .thumbnail-overlay {\n        width: 16px;\n        height: 16px;\n      }\n    }\n\n    @media (max-width: 480px) {\n      .story-navigation {\n        top: 55px;\n        padding: 6px 0;\n      }\n\n      .nav-slider-container {\n        padding: 0 8px;\n        gap: 4px;\n      }\n\n      .nav-slider-btn {\n        width: 24px;\n        height: 24px;\n        font-size: 0.7rem;\n      }\n\n      .story-thumbnails {\n        gap: 4px;\n      }\n\n      .story-thumbnail {\n        width: 36px;\n        height: 36px;\n      }\n\n      .thumbnail-overlay {\n        width: 14px;\n        height: 14px;\n      }\n    }\n\n    /* Enhanced Responsive Design */\n    @media (max-width: 768px) {\n      .story-header {\n        padding: 12px 16px;\n      }\n\n      .user-avatar {\n        width: 36px;\n        height: 36px;\n      }\n\n      .username {\n        font-size: 0.85rem;\n      }\n\n      .timestamp {\n        font-size: 0.75rem;\n      }\n\n      .progress-container {\n        padding: 0 16px;\n        top: 6px;\n      }\n\n      .story-caption {\n        bottom: 140px;\n        left: 16px;\n        right: 16px;\n        font-size: 0.85rem;\n        padding: 10px;\n      }\n\n      .story-actions {\n        padding: 16px;\n      }\n\n      .ecommerce-actions {\n        flex-direction: column;\n        gap: 10px;\n        margin-bottom: 20px;\n      }\n\n      .action-btn {\n        padding: 14px;\n        font-size: 1rem;\n        min-height: 48px; /* Touch-friendly */\n      }\n\n      .social-actions {\n        gap: 20px;\n      }\n\n      .social-btn {\n        width: 48px;\n        height: 48px;\n        font-size: 1.2rem;\n      }\n\n      .product-tag-icon {\n        width: 28px;\n        height: 28px;\n        font-size: 0.9rem;\n      }\n\n      .product-tag-info {\n        font-size: 0.75rem;\n        padding: 6px 10px;\n      }\n\n      .modal-content {\n        margin: 10px;\n        max-width: calc(100vw - 20px);\n        max-height: calc(100vh - 20px);\n      }\n\n      .modal-body {\n        padding: 16px;\n      }\n\n      .product-image {\n        height: 180px;\n      }\n    }\n\n    @media (max-width: 480px) {\n      .story-header {\n        padding: 10px 12px;\n      }\n\n      .user-avatar {\n        width: 32px;\n        height: 32px;\n      }\n\n      .username {\n        font-size: 0.8rem;\n      }\n\n      .progress-container {\n        padding: 0 12px;\n        top: 4px;\n      }\n\n      .story-caption {\n        bottom: 120px;\n        left: 12px;\n        right: 12px;\n        font-size: 0.8rem;\n        padding: 8px;\n      }\n\n      .story-actions {\n        padding: 12px;\n      }\n\n      .ecommerce-actions {\n        gap: 8px;\n        margin-bottom: 16px;\n      }\n\n      .action-btn {\n        padding: 12px;\n        font-size: 0.9rem;\n        min-height: 44px;\n      }\n\n      .social-actions {\n        gap: 16px;\n      }\n\n      .social-btn {\n        width: 44px;\n        height: 44px;\n        font-size: 1.1rem;\n      }\n    }\n\n    @media (min-width: 769px) {\n      .stories-viewer {\n        max-width: 400px;\n        margin: 0 auto;\n        border-radius: 12px;\n        overflow: hidden;\n        height: 90vh;\n        top: 5vh;\n      }\n\n      .story-media {\n        border-radius: 8px;\n      }\n\n      .ecommerce-actions {\n        flex-direction: row;\n        gap: 12px;\n      }\n\n      .action-btn {\n        padding: 10px 16px;\n        font-size: 0.9rem;\n      }\n    }\n\n    @media (min-width: 1024px) {\n      .stories-viewer {\n        max-width: 450px;\n        height: 85vh;\n        top: 7.5vh;\n      }\n    }\n\n    /* Touch and Gesture Improvements */\n    .nav-area {\n      -webkit-tap-highlight-color: transparent;\n      user-select: none;\n    }\n\n    .stories-viewer * {\n      -webkit-touch-callout: none;\n      -webkit-user-select: none;\n      -khtml-user-select: none;\n      -moz-user-select: none;\n      -ms-user-select: none;\n      user-select: none;\n    }\n\n    /* Loading States */\n    .story-media {\n      transition: opacity 0.3s ease;\n    }\n\n    .story-media.loading {\n      opacity: 0.5;\n    }\n\n    /* Accessibility Improvements */\n    .btn-close:focus,\n    .action-btn:focus,\n    .social-btn:focus {\n      outline: 2px solid #007bff;\n      outline-offset: 2px;\n    }\n\n    /* Performance Optimizations */\n    .stories-viewer {\n      will-change: transform;\n      transform: translateZ(0);\n    }\n\n    .story-media {\n      will-change: opacity;\n    }\n\n    .progress-fill {\n      will-change: width;\n    }\n  `]\n})\nexport class StoriesViewerComponent implements OnInit, OnDestroy {\n  @ViewChild('storyVideo') storyVideo!: ElementRef<HTMLVideoElement>;\n  @ViewChild('storyThumbnails') storyThumbnails!: ElementRef<HTMLDivElement>;\n\n  stories: Story[] = [];\n  currentIndex = 0;\n  currentStory!: Story;\n  isLiked = false;\n  isMuted = true;\n  selectedProduct: any = null;\n  showCommentsModal = false;\n  comments: any[] = [];\n  newComment = '';\n\n  // Navigation slider properties\n  canScrollLeft = false;\n  canScrollRight = false;\n\n  private progressTimer: any;\n  private storyDuration = 15000; // 15 seconds default\n\n  constructor(\n    private router: Router,\n    private route: ActivatedRoute\n  ) {}\n\n  ngOnInit() {\n    // Handle query parameters for story index\n    this.route.queryParams.subscribe(queryParams => {\n      if (queryParams['index']) {\n        this.currentIndex = parseInt(queryParams['index'], 10) || 0;\n      }\n    });\n\n    this.route.params.subscribe(params => {\n      if (params['userId']) {\n        this.loadUserStories(params['userId']);\n      } else {\n        this.loadStories();\n      }\n      if (params['storyId']) {\n        this.jumpToStory(params['storyId']);\n      }\n    });\n\n    // Add keyboard listeners for better UX\n    this.addKeyboardListeners();\n    this.addTouchListeners();\n  }\n\n  ngOnDestroy() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n    }\n  }\n\n  loadStories() {\n    // Load stories from real API\n    fetch('http://localhost:5000/api/stories')\n      .then(response => response.json())\n      .then(data => {\n        if (data.success) {\n          this.stories = data.stories.filter((story: any) => story.isActive);\n          if (this.stories.length > 0) {\n            // Use the index from query params or default to 0\n            const startIndex = Math.min(this.currentIndex, this.stories.length - 1);\n            this.currentIndex = startIndex;\n            this.currentStory = this.stories[startIndex];\n            this.startStoryTimer();\n          }\n        }\n      })\n      .catch(error => {\n        console.error('Error loading stories:', error);\n        // Show error message to user\n        alert('Failed to load stories. Please try again.');\n      });\n  }\n\n  loadUserStories(userId: string) {\n    // Load specific user's stories from real API\n    fetch(`http://localhost:5000/api/stories/user/${userId}`)\n      .then(response => response.json())\n      .then(data => {\n        if (data.success) {\n          this.stories = data.stories.filter((story: any) => story.isActive);\n          if (this.stories.length > 0) {\n            this.currentStory = this.stories[0];\n            this.startStoryTimer();\n          }\n        }\n      })\n      .catch(error => {\n        console.error('Error loading user stories:', error);\n      });\n  }\n\n  jumpToStory(storyId: string) {\n    const index = this.stories.findIndex(s => s._id === storyId);\n    if (index !== -1) {\n      this.currentIndex = index;\n      this.currentStory = this.stories[index];\n      this.startStoryTimer();\n    }\n  }\n\n  startStoryTimer() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n    }\n    \n    const duration = this.currentStory.media.type === 'video' \n      ? this.currentStory.media.duration * 1000 \n      : this.storyDuration;\n    \n    this.progressTimer = setTimeout(() => {\n      this.nextStory();\n    }, duration);\n  }\n\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.currentIndex++;\n      this.currentStory = this.stories[this.currentIndex];\n      this.startStoryTimer();\n      this.updateNavigationSlider();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.currentIndex--;\n      this.currentStory = this.stories[this.currentIndex];\n      this.startStoryTimer();\n      this.updateNavigationSlider();\n    }\n  }\n\n  jumpToStoryIndex(index: number) {\n    if (index >= 0 && index < this.stories.length && index !== this.currentIndex) {\n      this.currentIndex = index;\n      this.currentStory = this.stories[index];\n      this.startStoryTimer();\n      this.updateNavigationSlider();\n    }\n  }\n\n  handleStoryClick(event: MouseEvent) {\n    const rect = (event.target as HTMLElement).getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n    \n    if (clickX < width * 0.3) {\n      this.previousStory();\n    } else if (clickX > width * 0.7) {\n      this.nextStory();\n    }\n  }\n\n  getProgressWidth(index: number): number {\n    if (index < this.currentIndex) return 100;\n    if (index > this.currentIndex) return 0;\n    return 0; // Will be animated by CSS\n  }\n\n  closeStories() {\n    // Navigate back to the previous page or home\n    if (window.history.length > 1) {\n      window.history.back();\n    } else {\n      this.router.navigate(['/social']);\n    }\n  }\n\n  // E-commerce actions\n  buyNow() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      this.router.navigate(['/checkout'], { \n        queryParams: { productId: product._id, source: 'story' } \n      });\n    }\n  }\n\n  addToCart() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      // TODO: Add to cart via service\n      console.log('Add to cart from story:', product);\n    }\n  }\n\n  addToWishlist() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      // TODO: Add to wishlist via service\n      console.log('Add to wishlist from story:', product);\n    }\n  }\n\n  // Social actions\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    // TODO: Like/unlike story via API\n  }\n\n  openComments() {\n    this.showCommentsModal = true;\n    this.loadComments();\n  }\n\n  closeComments() {\n    this.showCommentsModal = false;\n  }\n\n  shareStory() {\n    // TODO: Implement share functionality\n    console.log('Share story:', this.currentStory);\n  }\n\n  toggleSound() {\n    this.isMuted = !this.isMuted;\n    if (this.storyVideo) {\n      this.storyVideo.nativeElement.muted = this.isMuted;\n    }\n  }\n\n  // Product modal\n  showProductModal(product: any) {\n    this.selectedProduct = product;\n  }\n\n  closeProductModal() {\n    this.selectedProduct = null;\n  }\n\n  buyProductNow() {\n    if (this.selectedProduct) {\n      this.router.navigate(['/checkout'], { \n        queryParams: { productId: this.selectedProduct._id, source: 'story' } \n      });\n    }\n  }\n\n  addProductToCart() {\n    if (this.selectedProduct) {\n      // TODO: Add to cart via service\n      console.log('Add product to cart:', this.selectedProduct);\n      this.closeProductModal();\n    }\n  }\n\n  addProductToWishlist() {\n    if (this.selectedProduct) {\n      // TODO: Add to wishlist via service\n      console.log('Add product to wishlist:', this.selectedProduct);\n      this.closeProductModal();\n    }\n  }\n\n  // Comments\n  loadComments() {\n    // Load comments from API\n    this.comments = [];\n  }\n\n  addComment() {\n    if (this.newComment.trim()) {\n      // TODO: Add comment via API\n      console.log('Add comment:', this.newComment);\n      this.newComment = '';\n    }\n  }\n\n  getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diffMs = now.getTime() - new Date(date).getTime();\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    \n    if (diffHours < 1) return 'now';\n    if (diffHours < 24) return `${diffHours}h`;\n    return `${Math.floor(diffHours / 24)}d`;\n  }\n\n\n\n  // Keyboard and touch event listeners\n  addKeyboardListeners() {\n    document.addEventListener('keydown', (event) => {\n      if (event.key === 'ArrowLeft') {\n        this.previousStory();\n      } else if (event.key === 'ArrowRight') {\n        this.nextStory();\n      } else if (event.key === 'Escape') {\n        this.closeStories();\n      }\n    });\n  }\n\n  addTouchListeners() {\n    let startX = 0;\n    let startY = 0;\n\n    document.addEventListener('touchstart', (event) => {\n      startX = event.touches[0].clientX;\n      startY = event.touches[0].clientY;\n    });\n\n    document.addEventListener('touchend', (event) => {\n      const endX = event.changedTouches[0].clientX;\n      const endY = event.changedTouches[0].clientY;\n      const diffX = startX - endX;\n      const diffY = startY - endY;\n\n      // Horizontal swipe\n      if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {\n        if (diffX > 0) {\n          this.nextStory(); // Swipe left - next story\n        } else {\n          this.previousStory(); // Swipe right - previous story\n        }\n      }\n      // Vertical swipe down to close\n      else if (diffY < -100) {\n        this.closeStories();\n      }\n    });\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;IA6DpCC,EAAA,CAAAC,cAAA,cAG0C;IACxCD,EAAA,CAAAE,SAAA,cAE+D;IACjEF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAJDH,EADA,CAAAI,WAAA,WAAAC,IAAA,KAAAC,MAAA,CAAAC,YAAA,CAAmC,cAAAF,IAAA,GAAAC,MAAA,CAAAC,YAAA,CACC;IAElCP,EAAA,CAAAQ,SAAA,EAAqC;IACrCR,EADA,CAAAS,WAAA,UAAAH,MAAA,CAAAI,gBAAA,CAAAL,IAAA,OAAqC,uBAAAM,QAAA,CAAAC,KAAA,CAAAC,QAAA,MACc;;;;;;IActDb,EAAA,CAAAC,cAAA,cAImC;IAA9BD,EAAA,CAAAc,UAAA,mBAAAC,wEAAA;MAAA,MAAAC,IAAA,GAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAgB,gBAAA,CAAAN,IAAA,CAAmB;IAAA,EAAC;IAChChB,EAAA,CAAAE,SAAA,cAE6B;IAC7BF,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAE,SAAA,cAEmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,SAAA,cACqD;IACvDF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAZDH,EADA,CAAAI,WAAA,WAAAY,IAAA,KAAAV,MAAA,CAAAC,YAAA,CAAmC,WAAAS,IAAA,GAAAV,MAAA,CAAAC,YAAA,CACF;IAE/BP,EAAA,CAAAQ,SAAA,EAAgC;IAChCR,EADA,CAAAuB,UAAA,QAAAjB,MAAA,CAAAkB,iBAAA,CAAAC,QAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAgC,QAAAD,QAAA,CAAAE,IAAA,CAAAC,QAAA,CACL;IAGzB5B,EAAA,CAAAQ,SAAA,GAAgE;IAChER,EADA,CAAAuB,UAAA,QAAAE,QAAA,CAAAE,IAAA,CAAAE,MAAA,yCAAA7B,EAAA,CAAA0B,aAAA,CAAgE,QAAAD,QAAA,CAAAE,IAAA,CAAAG,QAAA,CACrC;IAI7B9B,EAAA,CAAAQ,SAAA,EAAyC;IAAzCR,EAAA,CAAAS,WAAA,UAAAH,MAAA,CAAAyB,oBAAA,CAAAf,IAAA,OAAyC;;;;;;IArBlDhB,EAFJ,CAAAC,cAAA,cAAyD,cACrB,iBAGI;IAD5BD,EAAA,CAAAc,UAAA,mBAAAkB,qEAAA;MAAAhC,EAAA,CAAAiB,aAAA,CAAAgB,GAAA;MAAA,MAAA3B,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAA4B,iBAAA,EAAmB;IAAA,EAAC;IAEnClC,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,iBAA+C;IAC7CD,EAAA,CAAAmC,UAAA,IAAAC,kDAAA,mBAImC;IAYrCpC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,iBAEqC;IAD7BD,EAAA,CAAAc,UAAA,mBAAAuB,qEAAA;MAAArC,EAAA,CAAAiB,aAAA,CAAAgB,GAAA;MAAA,MAAA3B,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAgC,kBAAA,EAAoB;IAAA,EAAC;IAEpCtC,EAAA,CAAAE,SAAA,YAAoC;IAG1CF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IA7BMH,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAAuB,UAAA,cAAAjB,MAAA,CAAAiC,aAAA,CAA2B;IAMVvC,EAAA,CAAAQ,SAAA,GAAY;IAAZR,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAAkC,OAAA,CAAY;IAmB7BxC,EAAA,CAAAQ,SAAA,EAA4B;IAA5BR,EAAA,CAAAuB,UAAA,cAAAjB,MAAA,CAAAmC,cAAA,CAA4B;;;;;IAStCzC,EAAA,CAAAE,SAAA,iBAIiB;;;;IAFZF,EADA,CAAAuB,UAAA,QAAAjB,MAAA,CAAAoC,YAAA,CAAA9B,KAAA,CAAA+B,GAAA,EAAA3C,EAAA,CAAA0B,aAAA,CAA8B,QAAApB,MAAA,CAAAoC,YAAA,CAAAE,OAAA,CACF;;;;;;IAKjC5C,EAAA,CAAAC,cAAA,mBAQ6B;IAAtBD,EAAA,CAAAc,UAAA,mBAAA+B,sEAAA;MAAA7C,EAAA,CAAAiB,aAAA,CAAA6B,GAAA;MAAA,MAAAxC,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAyC,SAAA,EAAW;IAAA,EAAC;IAC5B/C,EAAA,CAAAG,YAAA,EAAQ;;;;IAHDH,EALA,CAAAuB,UAAA,QAAAjB,MAAA,CAAAoC,YAAA,CAAA9B,KAAA,CAAA+B,GAAA,EAAA3C,EAAA,CAAA0B,aAAA,CAA8B,WAAApB,MAAA,CAAAoC,YAAA,CAAA9B,KAAA,CAAAoC,SAAA,EAAAhD,EAAA,CAAA0B,aAAA,CAES,UAAApB,MAAA,CAAA2C,OAAA,CACtB,kBACA,eACH;;;;;;IAOnBjD,EAAA,CAAAC,cAAA,cAIoD;IAA/CD,EAAA,CAAAc,UAAA,mBAAAoC,kEAAA;MAAA,MAAAC,cAAA,GAAAnD,EAAA,CAAAiB,aAAA,CAAAmC,IAAA,EAAAC,SAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAgD,gBAAA,CAAAH,cAAA,CAAAI,OAAA,CAAoC;IAAA,EAAC;IACjDvD,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA8B,eACD;IAAAD,EAAA,CAAAwD,MAAA,GAA6B;IAAAxD,EAAA,CAAAG,YAAA,EAAO;IAC/DH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAwD,MAAA,GAAgD;;IAEhFxD,EAFgF,CAAAG,YAAA,EAAO,EAC/E,EACF;;;;IATDH,EADA,CAAAS,WAAA,SAAA0C,cAAA,CAAAM,QAAA,CAAAC,CAAA,MAAsC,QAAAP,cAAA,CAAAM,QAAA,CAAAE,CAAA,MACD;IAMX3D,EAAA,CAAAQ,SAAA,GAA6B;IAA7BR,EAAA,CAAA4D,iBAAA,CAAAT,cAAA,CAAAI,OAAA,CAAAM,IAAA,CAA6B;IAC5B7D,EAAA,CAAAQ,SAAA,GAAgD;IAAhDR,EAAA,CAAA8D,kBAAA,WAAA9D,EAAA,CAAA+D,WAAA,OAAAZ,cAAA,CAAAI,OAAA,CAAAS,KAAA,eAAgD;;;;;IAMlFhE,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAwD,MAAA,GACF;IAAAxD,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAA8D,kBAAA,MAAAxD,MAAA,CAAAoC,YAAA,CAAAE,OAAA,MACF;;;;;;IAWE5C,EADF,CAAAC,cAAA,cAAwE,iBAChB;IAAnBD,EAAA,CAAAc,UAAA,mBAAAmD,qEAAA;MAAAjE,EAAA,CAAAiB,aAAA,CAAAiD,IAAA;MAAA,MAAA5D,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAA6D,MAAA,EAAQ;IAAA,EAAC;IACnDnE,EAAA,CAAAE,SAAA,YAA2B;IAC3BF,EAAA,CAAAwD,MAAA,gBACF;IAAAxD,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA0D;IAAtBD,EAAA,CAAAc,UAAA,mBAAAsD,qEAAA;MAAApE,EAAA,CAAAiB,aAAA,CAAAiD,IAAA;MAAA,MAAA5D,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAA+D,SAAA,EAAW;IAAA,EAAC;IACvDrE,EAAA,CAAAE,SAAA,YAAoC;IACpCF,EAAA,CAAAwD,MAAA,oBACF;IAAAxD,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA8D;IAA1BD,EAAA,CAAAc,UAAA,mBAAAwD,qEAAA;MAAAtE,EAAA,CAAAiB,aAAA,CAAAiD,IAAA;MAAA,MAAA5D,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAiE,aAAA,EAAe;IAAA,EAAC;IAC3DvE,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAwD,MAAA,iBACF;IACFxD,EADE,CAAAG,YAAA,EAAS,EACL;;;;;;IAaJH,EAAA,CAAAC,cAAA,iBAAqG;IAApED,EAAA,CAAAc,UAAA,mBAAA0D,wEAAA;MAAAxE,EAAA,CAAAiB,aAAA,CAAAwD,IAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAoE,WAAA,EAAa;IAAA,EAAC;IACtD1E,EAAA,CAAAE,SAAA,YAAoF;IACtFF,EAAA,CAAAG,YAAA,EAAS;;;;IADQH,EAAA,CAAAQ,SAAA,EAA+B;IAACR,EAAhC,CAAAI,WAAA,kBAAAE,MAAA,CAAA2C,OAAA,CAA+B,mBAAA3C,MAAA,CAAA2C,OAAA,CAAiC;;;;;;IAjJvFjD,EAAA,CAAAC,cAAA,aAAoF;IAAnCD,EAAA,CAAAc,UAAA,mBAAA6D,2DAAAC,MAAA;MAAA5E,EAAA,CAAAiB,aAAA,CAAA4D,GAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAwE,gBAAA,CAAAF,MAAA,CAAwB;IAAA,EAAC;IAG/E5E,EADF,CAAAC,cAAA,aAA0B,aACD;IACrBD,EAAA,CAAAE,SAAA,aAC4D;IAE1DF,EADF,CAAAC,cAAA,cAA0B,eACD;IAAAD,EAAA,CAAAwD,MAAA,GAAgC;IAAAxD,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAwD,MAAA,GAAwC;IAEpExD,EAFoE,CAAAG,YAAA,EAAO,EACnE,EACF;IAGJH,EADF,CAAAC,cAAA,cAA4B,kBACyB;IAAzBD,EAAA,CAAAc,UAAA,mBAAAiE,+DAAA;MAAA/E,EAAA,CAAAiB,aAAA,CAAA4D,GAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAA0E,YAAA,EAAc;IAAA,EAAC;IAChDhF,EAAA,CAAAE,SAAA,aAA4B;IAGlCF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGNH,EAAA,CAAAC,cAAA,eAAgC;IAC9BD,EAAA,CAAAmC,UAAA,KAAA8C,4CAAA,kBAG0C;IAK5CjF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAmC,UAAA,KAAA+C,4CAAA,kBAAyD;IAoCzDlF,EAAA,CAAAC,cAAA,eAA2B;IASzBD,EAPA,CAAAmC,UAAA,KAAAgD,4CAAA,kBAIiB,KAAAC,8CAAA,oBAWY;IAI7BpF,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAmC,UAAA,KAAAkD,4CAAA,kBAIoD;IAStDrF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAmC,UAAA,KAAAmD,4CAAA,kBAAwD;IAG1DtF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAyD;IAA1BD,EAAA,CAAAc,UAAA,mBAAAyE,4DAAA;MAAAvF,EAAA,CAAAiB,aAAA,CAAA4D,GAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAkF,aAAA,EAAe;IAAA,EAAC;IAACxF,EAAA,CAAAG,YAAA,EAAM;IAC/DH,EAAA,CAAAC,cAAA,eAAqD;IAAtBD,EAAA,CAAAc,UAAA,mBAAA2E,4DAAA;MAAAzF,EAAA,CAAAiB,aAAA,CAAA4D,GAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAyC,SAAA,EAAW;IAAA,EAAC;IAAC/C,EAAA,CAAAG,YAAA,EAAM;IAG3DH,EAAA,CAAAC,cAAA,eAA2B;IAEzBD,EAAA,CAAAmC,UAAA,KAAAuD,4CAAA,mBAAwE;IAiBtE1F,EADF,CAAAC,cAAA,eAA4B,kBACqD;IAAvBD,EAAA,CAAAc,UAAA,mBAAA6E,+DAAA;MAAA3F,EAAA,CAAAiB,aAAA,CAAA4D,GAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAsF,UAAA,EAAY;IAAA,EAAC;IAC5E5F,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4D;IAAzBD,EAAA,CAAAc,UAAA,mBAAA+E,+DAAA;MAAA7F,EAAA,CAAAiB,aAAA,CAAA4D,GAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAwF,YAAA,EAAc;IAAA,EAAC;IACzD9F,EAAA,CAAAE,SAAA,aAA8B;IAChCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAwD;IAAvBD,EAAA,CAAAc,UAAA,mBAAAiF,+DAAA;MAAA/F,EAAA,CAAAiB,aAAA,CAAA4D,GAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAA0F,UAAA,EAAY;IAAA,EAAC;IACrDhG,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAmC,UAAA,KAAA8D,+CAAA,qBAAqG;IAK3GjG,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAjJKH,EAAA,CAAAQ,SAAA,GAAuE;IACvER,EADA,CAAAuB,UAAA,QAAAjB,MAAA,CAAAoC,YAAA,CAAAf,IAAA,CAAAE,MAAA,yCAAA7B,EAAA,CAAA0B,aAAA,CAAuE,QAAApB,MAAA,CAAAoC,YAAA,CAAAf,IAAA,CAAAG,QAAA,CACrC;IAEd9B,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAA4D,iBAAA,CAAAtD,MAAA,CAAAoC,YAAA,CAAAf,IAAA,CAAAC,QAAA,CAAgC;IAC/B5B,EAAA,CAAAQ,SAAA,GAAwC;IAAxCR,EAAA,CAAA4D,iBAAA,CAAAtD,MAAA,CAAA4F,UAAA,CAAA5F,MAAA,CAAAoC,YAAA,CAAAyD,SAAA,EAAwC;IAc7CnG,EAAA,CAAAQ,SAAA,GAAY;IAAZR,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAAkC,OAAA,CAAY;IAUNxC,EAAA,CAAAQ,SAAA,EAAwB;IAAxBR,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAkC,OAAA,CAAA4D,MAAA,KAAwB;IAsC/CpG,EAAA,CAAAQ,SAAA,GAAyC;IAAzCR,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAoC,YAAA,CAAA9B,KAAA,CAAAyF,IAAA,aAAyC;IAOvCrG,EAAA,CAAAQ,SAAA,EAAyC;IAAzCR,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAoC,YAAA,CAAA9B,KAAA,CAAAyF,IAAA,aAAyC;IAcnBrG,EAAA,CAAAQ,SAAA,GAAwB;IAAxBR,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAAoC,YAAA,CAAA4D,QAAA,CAAwB;IAe1BtG,EAAA,CAAAQ,SAAA,EAA0B;IAA1BR,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAoC,YAAA,CAAAE,OAAA,CAA0B;IAYtB5C,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAoC,YAAA,CAAA4D,QAAA,CAAAF,MAAA,KAAsC;IAiBpCpG,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAI,WAAA,UAAAE,MAAA,CAAAiG,OAAA,CAAuB;IASGvG,EAAA,CAAAQ,SAAA,GAAyC;IAAzCR,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAoC,YAAA,CAAA9B,KAAA,CAAAyF,IAAA,aAAyC;;;;;IAwB/FrG,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAwD,MAAA,GACF;;IAAAxD,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAA8D,kBAAA,YAAA9D,EAAA,CAAA+D,WAAA,OAAAzD,MAAA,CAAAkG,eAAA,CAAAC,aAAA,gBACF;;;;;;IAlBVzG,EAAA,CAAAC,cAAA,cAAiF;IAA9BD,EAAA,CAAAc,UAAA,mBAAA4F,2DAAA;MAAA1G,EAAA,CAAAiB,aAAA,CAAA0F,IAAA;MAAA,MAAArG,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAsG,iBAAA,EAAmB;IAAA,EAAC;IAC9E5G,EAAA,CAAAC,cAAA,cAA8D;IAAnCD,EAAA,CAAAc,UAAA,mBAAA+F,2DAAAjC,MAAA;MAAA5E,EAAA,CAAAiB,aAAA,CAAA0F,IAAA;MAAA,OAAA3G,EAAA,CAAAqB,WAAA,CAASuD,MAAA,CAAAkC,eAAA,EAAwB;IAAA,EAAC;IAEzD9G,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAwD,MAAA,GAA0B;IAAAxD,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,iBAAwD;IAA9BD,EAAA,CAAAc,UAAA,mBAAAiG,8DAAA;MAAA/G,EAAA,CAAAiB,aAAA,CAAA0F,IAAA;MAAA,MAAArG,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAsG,iBAAA,EAAmB;IAAA,EAAC;IACrD5G,EAAA,CAAAE,SAAA,YAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAENH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,SAAA,cAA+F;IAG7FF,EADF,CAAAC,cAAA,cAA6B,aACV;IAAAD,EAAA,CAAAwD,MAAA,IAA2B;IAAAxD,EAAA,CAAAG,YAAA,EAAI;IAE9CH,EADF,CAAAC,cAAA,eAAmB,gBACW;IAAAD,EAAA,CAAAwD,MAAA,IAA6C;;IAAAxD,EAAA,CAAAG,YAAA,EAAO;IAChFH,EAAA,CAAAmC,UAAA,KAAA6E,6CAAA,mBAAmE;IAIvEhH,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAA2B,kBAC6B;IAA1BD,EAAA,CAAAc,UAAA,mBAAAmG,+DAAA;MAAAjH,EAAA,CAAAiB,aAAA,CAAA0F,IAAA;MAAA,MAAArG,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAA4G,aAAA,EAAe;IAAA,EAAC;IAAClH,EAAA,CAAAwD,MAAA,eAAO;IAAAxD,EAAA,CAAAG,YAAA,EAAS;IACtEH,EAAA,CAAAC,cAAA,kBAA2D;IAA7BD,EAAA,CAAAc,UAAA,mBAAAqG,+DAAA;MAAAnH,EAAA,CAAAiB,aAAA,CAAA0F,IAAA;MAAA,MAAArG,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAA8G,gBAAA,EAAkB;IAAA,EAAC;IAACpH,EAAA,CAAAwD,MAAA,mBAAW;IAAAxD,EAAA,CAAAG,YAAA,EAAS;IAC/EH,EAAA,CAAAC,cAAA,kBAA6D;IAAjCD,EAAA,CAAAc,UAAA,mBAAAuG,+DAAA;MAAArH,EAAA,CAAAiB,aAAA,CAAA0F,IAAA;MAAA,MAAArG,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAgH,oBAAA,EAAsB;IAAA,EAAC;IAACtH,EAAA,CAAAwD,MAAA,uBAAe;IAIpFxD,EAJoF,CAAAG,YAAA,EAAS,EACjF,EACF,EACF,EACF;;;;IA1BIH,EAAA,CAAAQ,SAAA,GAA0B;IAA1BR,EAAA,CAAA4D,iBAAA,CAAAtD,MAAA,CAAAkG,eAAA,CAAA3C,IAAA,CAA0B;IAOzB7D,EAAA,CAAAQ,SAAA,GAAsC;IAACR,EAAvC,CAAAuB,UAAA,QAAAjB,MAAA,CAAAkG,eAAA,CAAAe,MAAA,qBAAAjH,MAAA,CAAAkG,eAAA,CAAAe,MAAA,IAAA5E,GAAA,EAAA3C,EAAA,CAAA0B,aAAA,CAAsC,QAAApB,MAAA,CAAAkG,eAAA,CAAA3C,IAAA,CAA6B;IAGrD7D,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAA4D,iBAAA,CAAAtD,MAAA,CAAAkG,eAAA,CAAAgB,KAAA,CAA2B;IAEdxH,EAAA,CAAAQ,SAAA,GAA6C;IAA7CR,EAAA,CAAA8D,kBAAA,WAAA9D,EAAA,CAAA+D,WAAA,QAAAzD,MAAA,CAAAkG,eAAA,CAAAxC,KAAA,eAA6C;IAC3ChE,EAAA,CAAAQ,SAAA,GAAmC;IAAnCR,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAkG,eAAA,CAAAC,aAAA,CAAmC;;;;;IA0BrEzG,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAE,SAAA,cAC0D;IAExDF,EADF,CAAAC,cAAA,cAA6B,eACI;IAAAD,EAAA,CAAAwD,MAAA,GAA2B;IAAAxD,EAAA,CAAAG,YAAA,EAAO;IACjEH,EAAA,CAAAC,cAAA,YAAwB;IAAAD,EAAA,CAAAwD,MAAA,GAAkB;IAAAxD,EAAA,CAAAG,YAAA,EAAI;IAC9CH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAwD,MAAA,GAAqC;IAEpExD,EAFoE,CAAAG,YAAA,EAAO,EACnE,EACF;;;;;IAPCH,EAAA,CAAAQ,SAAA,EAAkE;IAClER,EADA,CAAAuB,UAAA,QAAAkG,WAAA,CAAA9F,IAAA,CAAAE,MAAA,yCAAA7B,EAAA,CAAA0B,aAAA,CAAkE,QAAA+F,WAAA,CAAA9F,IAAA,CAAAG,QAAA,CACrC;IAED9B,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAA4D,iBAAA,CAAA6D,WAAA,CAAA9F,IAAA,CAAAC,QAAA,CAA2B;IAClC5B,EAAA,CAAAQ,SAAA,GAAkB;IAAlBR,EAAA,CAAA4D,iBAAA,CAAA6D,WAAA,CAAAC,IAAA,CAAkB;IACf1H,EAAA,CAAAQ,SAAA,GAAqC;IAArCR,EAAA,CAAA4D,iBAAA,CAAAtD,MAAA,CAAA4F,UAAA,CAAAuB,WAAA,CAAAE,WAAA,EAAqC;;;;;;IAhB1E3H,EAAA,CAAAC,cAAA,cAAgF;IAA1BD,EAAA,CAAAc,UAAA,mBAAA8G,2DAAA;MAAA5H,EAAA,CAAAiB,aAAA,CAAA4G,IAAA;MAAA,MAAAvH,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAwH,aAAA,EAAe;IAAA,EAAC;IAC7E9H,EAAA,CAAAC,cAAA,cAA8D;IAAnCD,EAAA,CAAAc,UAAA,mBAAAiH,2DAAAnD,MAAA;MAAA5E,EAAA,CAAAiB,aAAA,CAAA4G,IAAA;MAAA,OAAA7H,EAAA,CAAAqB,WAAA,CAASuD,MAAA,CAAAkC,eAAA,EAAwB;IAAA,EAAC;IAEzD9G,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAwD,MAAA,eAAQ;IAAAxD,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,iBAAoD;IAA1BD,EAAA,CAAAc,UAAA,mBAAAkH,8DAAA;MAAAhI,EAAA,CAAAiB,aAAA,CAAA4G,IAAA;MAAA,MAAAvH,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAwH,aAAA,EAAe;IAAA,EAAC;IACjD9H,EAAA,CAAAE,SAAA,YAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAENH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAmC,UAAA,IAAA8F,2CAAA,kBAAsD;IASxDjI,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,cAA2B,iBAIW;IAF7BD,EAAA,CAAAkI,gBAAA,2BAAAC,sEAAAvD,MAAA;MAAA5E,EAAA,CAAAiB,aAAA,CAAA4G,IAAA;MAAA,MAAAvH,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAoI,kBAAA,CAAA9H,MAAA,CAAA+H,UAAA,EAAAzD,MAAA,MAAAtE,MAAA,CAAA+H,UAAA,GAAAzD,MAAA;MAAA,OAAA5E,EAAA,CAAAqB,WAAA,CAAAuD,MAAA;IAAA,EAAwB;IAExB5E,EAAA,CAAAc,UAAA,yBAAAwH,oEAAA;MAAAtI,EAAA,CAAAiB,aAAA,CAAA4G,IAAA;MAAA,MAAAvH,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAef,MAAA,CAAAiI,UAAA,EAAY;IAAA,EAAC;IAHnCvI,EAAA,CAAAG,YAAA,EAGoC;IACpCH,EAAA,CAAAC,cAAA,kBAA+D;IAAvDD,EAAA,CAAAc,UAAA,mBAAA0H,+DAAA;MAAAxI,EAAA,CAAAiB,aAAA,CAAA4G,IAAA;MAAA,MAAAvH,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAiI,UAAA,EAAY;IAAA,EAAC;IAC5BvI,EAAA,CAAAE,SAAA,aAAkC;IAI1CF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IArByCH,EAAA,CAAAQ,SAAA,GAAW;IAAXR,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAAmI,QAAA,CAAW;IAa7CzI,EAAA,CAAAQ,SAAA,GAAwB;IAAxBR,EAAA,CAAA0I,gBAAA,YAAApI,MAAA,CAAA+H,UAAA,CAAwB;IAGArI,EAAA,CAAAQ,SAAA,EAA+B;IAA/BR,EAAA,CAAAuB,UAAA,cAAAjB,MAAA,CAAA+H,UAAA,CAAAM,IAAA,GAA+B;;;AA41BxE,OAAM,MAAOC,sBAAsB;EAqBjCC,YACUC,MAAc,EACdC,KAAqB;IADrB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IAnBf,KAAAvG,OAAO,GAAY,EAAE;IACrB,KAAAjC,YAAY,GAAG,CAAC;IAEhB,KAAAgG,OAAO,GAAG,KAAK;IACf,KAAAtD,OAAO,GAAG,IAAI;IACd,KAAAuD,eAAe,GAAQ,IAAI;IAC3B,KAAAwC,iBAAiB,GAAG,KAAK;IACzB,KAAAP,QAAQ,GAAU,EAAE;IACpB,KAAAJ,UAAU,GAAG,EAAE;IAEf;IACA,KAAA9F,aAAa,GAAG,KAAK;IACrB,KAAAE,cAAc,GAAG,KAAK;IAGd,KAAAwG,aAAa,GAAG,KAAK,CAAC,CAAC;EAK5B;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACH,KAAK,CAACI,WAAW,CAACC,SAAS,CAACD,WAAW,IAAG;MAC7C,IAAIA,WAAW,CAAC,OAAO,CAAC,EAAE;QACxB,IAAI,CAAC5I,YAAY,GAAG8I,QAAQ,CAACF,WAAW,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;;IAE/D,CAAC,CAAC;IAEF,IAAI,CAACJ,KAAK,CAACO,MAAM,CAACF,SAAS,CAACE,MAAM,IAAG;MACnC,IAAIA,MAAM,CAAC,QAAQ,CAAC,EAAE;QACpB,IAAI,CAACC,eAAe,CAACD,MAAM,CAAC,QAAQ,CAAC,CAAC;OACvC,MAAM;QACL,IAAI,CAACE,WAAW,EAAE;;MAEpB,IAAIF,MAAM,CAAC,SAAS,CAAC,EAAE;QACrB,IAAI,CAACG,WAAW,CAACH,MAAM,CAAC,SAAS,CAAC,CAAC;;IAEvC,CAAC,CAAC;IAEF;IACA,IAAI,CAACI,oBAAoB,EAAE;IAC3B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAEAL,WAAWA,CAAA;IACT;IACAO,KAAK,CAAC,mCAAmC,CAAC,CACvCC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAG;MACX,IAAIA,IAAI,CAACC,OAAO,EAAE;QAChB,IAAI,CAAC5H,OAAO,GAAG2H,IAAI,CAAC3H,OAAO,CAAC6H,MAAM,CAAEC,KAAU,IAAKA,KAAK,CAACC,QAAQ,CAAC;QAClE,IAAI,IAAI,CAAC/H,OAAO,CAAC4D,MAAM,GAAG,CAAC,EAAE;UAC3B;UACA,MAAMoE,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACnK,YAAY,EAAE,IAAI,CAACiC,OAAO,CAAC4D,MAAM,GAAG,CAAC,CAAC;UACvE,IAAI,CAAC7F,YAAY,GAAGiK,UAAU;UAC9B,IAAI,CAAC9H,YAAY,GAAG,IAAI,CAACF,OAAO,CAACgI,UAAU,CAAC;UAC5C,IAAI,CAACG,eAAe,EAAE;;;IAG5B,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C;MACAE,KAAK,CAAC,2CAA2C,CAAC;IACpD,CAAC,CAAC;EACN;EAEAxB,eAAeA,CAACyB,MAAc;IAC5B;IACAjB,KAAK,CAAC,0CAA0CiB,MAAM,EAAE,CAAC,CACtDhB,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAG;MACX,IAAIA,IAAI,CAACC,OAAO,EAAE;QAChB,IAAI,CAAC5H,OAAO,GAAG2H,IAAI,CAAC3H,OAAO,CAAC6H,MAAM,CAAEC,KAAU,IAAKA,KAAK,CAACC,QAAQ,CAAC;QAClE,IAAI,IAAI,CAAC/H,OAAO,CAAC4D,MAAM,GAAG,CAAC,EAAE;UAC3B,IAAI,CAAC1D,YAAY,GAAG,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC;UACnC,IAAI,CAACmI,eAAe,EAAE;;;IAG5B,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,CAAC;EACN;EAEApB,WAAWA,CAACwB,OAAe;IACzB,MAAM9J,KAAK,GAAG,IAAI,CAACqB,OAAO,CAAC0I,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKH,OAAO,CAAC;IAC5D,IAAI9J,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAACZ,YAAY,GAAGY,KAAK;MACzB,IAAI,CAACuB,YAAY,GAAG,IAAI,CAACF,OAAO,CAACrB,KAAK,CAAC;MACvC,IAAI,CAACwJ,eAAe,EAAE;;EAE1B;EAEAA,eAAeA,CAAA;IACb,IAAI,IAAI,CAACd,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAGlC,MAAMhJ,QAAQ,GAAG,IAAI,CAAC6B,YAAY,CAAC9B,KAAK,CAACyF,IAAI,KAAK,OAAO,GACrD,IAAI,CAAC3D,YAAY,CAAC9B,KAAK,CAACC,QAAQ,GAAG,IAAI,GACvC,IAAI,CAACoI,aAAa;IAEtB,IAAI,CAACY,aAAa,GAAGwB,UAAU,CAAC,MAAK;MACnC,IAAI,CAACtI,SAAS,EAAE;IAClB,CAAC,EAAElC,QAAQ,CAAC;EACd;EAEAkC,SAASA,CAAA;IACP,IAAI,IAAI,CAACxC,YAAY,GAAG,IAAI,CAACiC,OAAO,CAAC4D,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAAC7F,YAAY,EAAE;MACnB,IAAI,CAACmC,YAAY,GAAG,IAAI,CAACF,OAAO,CAAC,IAAI,CAACjC,YAAY,CAAC;MACnD,IAAI,CAACoK,eAAe,EAAE;MACtB,IAAI,CAACW,sBAAsB,EAAE;KAC9B,MAAM;MACL,IAAI,CAACtG,YAAY,EAAE;;EAEvB;EAEAQ,aAAaA,CAAA;IACX,IAAI,IAAI,CAACjF,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAACmC,YAAY,GAAG,IAAI,CAACF,OAAO,CAAC,IAAI,CAACjC,YAAY,CAAC;MACnD,IAAI,CAACoK,eAAe,EAAE;MACtB,IAAI,CAACW,sBAAsB,EAAE;;EAEjC;EAEAhK,gBAAgBA,CAACH,KAAa;IAC5B,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACqB,OAAO,CAAC4D,MAAM,IAAIjF,KAAK,KAAK,IAAI,CAACZ,YAAY,EAAE;MAC5E,IAAI,CAACA,YAAY,GAAGY,KAAK;MACzB,IAAI,CAACuB,YAAY,GAAG,IAAI,CAACF,OAAO,CAACrB,KAAK,CAAC;MACvC,IAAI,CAACwJ,eAAe,EAAE;MACtB,IAAI,CAACW,sBAAsB,EAAE;;EAEjC;EAEAxG,gBAAgBA,CAACyG,KAAiB;IAChC,MAAMC,IAAI,GAAID,KAAK,CAACE,MAAsB,CAACC,qBAAqB,EAAE;IAClE,MAAMC,MAAM,GAAGJ,KAAK,CAACK,OAAO,GAAGJ,IAAI,CAACK,IAAI;IACxC,MAAMC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAExB,IAAIH,MAAM,GAAGG,KAAK,GAAG,GAAG,EAAE;MACxB,IAAI,CAACtG,aAAa,EAAE;KACrB,MAAM,IAAImG,MAAM,GAAGG,KAAK,GAAG,GAAG,EAAE;MAC/B,IAAI,CAAC/I,SAAS,EAAE;;EAEpB;EAEArC,gBAAgBA,CAACS,KAAa;IAC5B,IAAIA,KAAK,GAAG,IAAI,CAACZ,YAAY,EAAE,OAAO,GAAG;IACzC,IAAIY,KAAK,GAAG,IAAI,CAACZ,YAAY,EAAE,OAAO,CAAC;IACvC,OAAO,CAAC,CAAC,CAAC;EACZ;EAEAyE,YAAYA,CAAA;IACV;IACA,IAAI+G,MAAM,CAACC,OAAO,CAAC5F,MAAM,GAAG,CAAC,EAAE;MAC7B2F,MAAM,CAACC,OAAO,CAACC,IAAI,EAAE;KACtB,MAAM;MACL,IAAI,CAACnD,MAAM,CAACoD,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;;EAErC;EAEA;EACA/H,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACzB,YAAY,CAAC4D,QAAQ,CAACF,MAAM,GAAG,CAAC,EAAE;MACzC,MAAM7C,OAAO,GAAG,IAAI,CAACb,YAAY,CAAC4D,QAAQ,CAAC,CAAC,CAAC,CAAC/C,OAAO;MACrD,IAAI,CAACuF,MAAM,CAACoD,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClC/C,WAAW,EAAE;UAAEgD,SAAS,EAAE5I,OAAO,CAAC6H,GAAG;UAAEgB,MAAM,EAAE;QAAO;OACvD,CAAC;;EAEN;EAEA/H,SAASA,CAAA;IACP,IAAI,IAAI,CAAC3B,YAAY,CAAC4D,QAAQ,CAACF,MAAM,GAAG,CAAC,EAAE;MACzC,MAAM7C,OAAO,GAAG,IAAI,CAACb,YAAY,CAAC4D,QAAQ,CAAC,CAAC,CAAC,CAAC/C,OAAO;MACrD;MACAuH,OAAO,CAACuB,GAAG,CAAC,yBAAyB,EAAE9I,OAAO,CAAC;;EAEnD;EAEAgB,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC7B,YAAY,CAAC4D,QAAQ,CAACF,MAAM,GAAG,CAAC,EAAE;MACzC,MAAM7C,OAAO,GAAG,IAAI,CAACb,YAAY,CAAC4D,QAAQ,CAAC,CAAC,CAAC,CAAC/C,OAAO;MACrD;MACAuH,OAAO,CAACuB,GAAG,CAAC,6BAA6B,EAAE9I,OAAO,CAAC;;EAEvD;EAEA;EACAqC,UAAUA,CAAA;IACR,IAAI,CAACW,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B;EACF;EAEAT,YAAYA,CAAA;IACV,IAAI,CAACkD,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACsD,YAAY,EAAE;EACrB;EAEAxE,aAAaA,CAAA;IACX,IAAI,CAACkB,iBAAiB,GAAG,KAAK;EAChC;EAEAhD,UAAUA,CAAA;IACR;IACA8E,OAAO,CAACuB,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC3J,YAAY,CAAC;EAChD;EAEAgC,WAAWA,CAAA;IACT,IAAI,CAACzB,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B,IAAI,IAAI,CAACsJ,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI,CAACxJ,OAAO;;EAEtD;EAEA;EACAK,gBAAgBA,CAACC,OAAY;IAC3B,IAAI,CAACiD,eAAe,GAAGjD,OAAO;EAChC;EAEAqD,iBAAiBA,CAAA;IACf,IAAI,CAACJ,eAAe,GAAG,IAAI;EAC7B;EAEAU,aAAaA,CAAA;IACX,IAAI,IAAI,CAACV,eAAe,EAAE;MACxB,IAAI,CAACsC,MAAM,CAACoD,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClC/C,WAAW,EAAE;UAAEgD,SAAS,EAAE,IAAI,CAAC3F,eAAe,CAAC4E,GAAG;UAAEgB,MAAM,EAAE;QAAO;OACpE,CAAC;;EAEN;EAEAhF,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACZ,eAAe,EAAE;MACxB;MACAsE,OAAO,CAACuB,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC7F,eAAe,CAAC;MACzD,IAAI,CAACI,iBAAiB,EAAE;;EAE5B;EAEAU,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACd,eAAe,EAAE;MACxB;MACAsE,OAAO,CAACuB,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC7F,eAAe,CAAC;MAC7D,IAAI,CAACI,iBAAiB,EAAE;;EAE5B;EAEA;EACA0F,YAAYA,CAAA;IACV;IACA,IAAI,CAAC7D,QAAQ,GAAG,EAAE;EACpB;EAEAF,UAAUA,CAAA;IACR,IAAI,IAAI,CAACF,UAAU,CAACM,IAAI,EAAE,EAAE;MAC1B;MACAmC,OAAO,CAACuB,GAAG,CAAC,cAAc,EAAE,IAAI,CAAChE,UAAU,CAAC;MAC5C,IAAI,CAACA,UAAU,GAAG,EAAE;;EAExB;EAEAnC,UAAUA,CAACwG,IAAU;IACnB,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,MAAM,GAAGF,GAAG,CAACG,OAAO,EAAE,GAAG,IAAIF,IAAI,CAACF,IAAI,CAAC,CAACI,OAAO,EAAE;IACvD,MAAMC,SAAS,GAAGtC,IAAI,CAACuC,KAAK,CAACH,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEvD,IAAIE,SAAS,GAAG,CAAC,EAAE,OAAO,KAAK;IAC/B,IAAIA,SAAS,GAAG,EAAE,EAAE,OAAO,GAAGA,SAAS,GAAG;IAC1C,OAAO,GAAGtC,IAAI,CAACuC,KAAK,CAACD,SAAS,GAAG,EAAE,CAAC,GAAG;EACzC;EAIA;EACArD,oBAAoBA,CAAA;IAClBuD,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAG3B,KAAK,IAAI;MAC7C,IAAIA,KAAK,CAAC4B,GAAG,KAAK,WAAW,EAAE;QAC7B,IAAI,CAAC3H,aAAa,EAAE;OACrB,MAAM,IAAI+F,KAAK,CAAC4B,GAAG,KAAK,YAAY,EAAE;QACrC,IAAI,CAACpK,SAAS,EAAE;OACjB,MAAM,IAAIwI,KAAK,CAAC4B,GAAG,KAAK,QAAQ,EAAE;QACjC,IAAI,CAACnI,YAAY,EAAE;;IAEvB,CAAC,CAAC;EACJ;EAEA2E,iBAAiBA,CAAA;IACf,IAAIyD,MAAM,GAAG,CAAC;IACd,IAAIC,MAAM,GAAG,CAAC;IAEdJ,QAAQ,CAACC,gBAAgB,CAAC,YAAY,EAAG3B,KAAK,IAAI;MAChD6B,MAAM,GAAG7B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAAC1B,OAAO;MACjCyB,MAAM,GAAG9B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IACnC,CAAC,CAAC;IAEFN,QAAQ,CAACC,gBAAgB,CAAC,UAAU,EAAG3B,KAAK,IAAI;MAC9C,MAAMiC,IAAI,GAAGjC,KAAK,CAACkC,cAAc,CAAC,CAAC,CAAC,CAAC7B,OAAO;MAC5C,MAAM8B,IAAI,GAAGnC,KAAK,CAACkC,cAAc,CAAC,CAAC,CAAC,CAACF,OAAO;MAC5C,MAAMI,KAAK,GAAGP,MAAM,GAAGI,IAAI;MAC3B,MAAMI,KAAK,GAAGP,MAAM,GAAGK,IAAI;MAE3B;MACA,IAAIjD,IAAI,CAACoD,GAAG,CAACF,KAAK,CAAC,GAAGlD,IAAI,CAACoD,GAAG,CAACD,KAAK,CAAC,IAAInD,IAAI,CAACoD,GAAG,CAACF,KAAK,CAAC,GAAG,EAAE,EAAE;QAC7D,IAAIA,KAAK,GAAG,CAAC,EAAE;UACb,IAAI,CAAC5K,SAAS,EAAE,CAAC,CAAC;SACnB,MAAM;UACL,IAAI,CAACyC,aAAa,EAAE,CAAC,CAAC;;;MAG1B;MAAA,KACK,IAAIoI,KAAK,GAAG,CAAC,GAAG,EAAE;QACrB,IAAI,CAAC5I,YAAY,EAAE;;IAEvB,CAAC,CAAC;EACJ;;;uBAzUW4D,sBAAsB,EAAA5I,EAAA,CAAA8N,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAhO,EAAA,CAAA8N,iBAAA,CAAAC,EAAA,CAAAE,cAAA;IAAA;EAAA;;;YAAtBrF,sBAAsB;MAAAsF,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;UAt3B/BrO,EAxLA,CAAAmC,UAAA,IAAAoM,qCAAA,mBAAoF,IAAAC,qCAAA,kBAwJH,IAAAC,qCAAA,kBAgCD;;;UAxLnDzO,EAAA,CAAAuB,UAAA,SAAA+M,GAAA,CAAA5L,YAAA,CAAkB;UAwJnB1C,EAAA,CAAAQ,SAAA,EAAqB;UAArBR,EAAA,CAAAuB,UAAA,SAAA+M,GAAA,CAAA9H,eAAA,CAAqB;UAgCpBxG,EAAA,CAAAQ,SAAA,EAAuB;UAAvBR,EAAA,CAAAuB,UAAA,SAAA+M,GAAA,CAAAtF,iBAAA,CAAuB;;;qBA1L5ClJ,YAAY,EAAA4O,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAE9O,WAAW,EAAA+O,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}