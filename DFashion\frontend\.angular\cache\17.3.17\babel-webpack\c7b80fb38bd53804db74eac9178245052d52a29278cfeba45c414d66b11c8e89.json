{"ast": null, "code": "import { BehaviorSubject, tap } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.API_URL = environment.apiUrl;\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.isAuthenticatedSubject = new BehaviorSubject(false);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n  }\n  initializeAuth() {\n    const token = this.getToken();\n    if (token) {\n      this.getCurrentUser().subscribe({\n        next: response => {\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        },\n        error: () => {\n          this.logout();\n        }\n      });\n    }\n  }\n  login(credentials) {\n    return this.http.post(`${this.API_URL}/auth/login`, credentials).pipe(tap(response => {\n      this.setToken(response.token);\n      this.currentUserSubject.next(response.user);\n      this.isAuthenticatedSubject.next(true);\n    }));\n  }\n  register(userData) {\n    return this.http.post(`${this.API_URL}/auth/register`, userData).pipe(tap(response => {\n      this.setToken(response.token);\n      this.currentUserSubject.next(response.user);\n      this.isAuthenticatedSubject.next(true);\n    }));\n  }\n  logout() {\n    localStorage.removeItem('token');\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.router.navigate(['/auth/login']);\n  }\n  getCurrentUser() {\n    return this.http.get(`${this.API_URL}/auth/me`);\n  }\n  getToken() {\n    return localStorage.getItem('token');\n  }\n  setToken(token) {\n    localStorage.setItem('token', token);\n  }\n  get currentUserValue() {\n    return this.currentUserSubject.value;\n  }\n  get isAuthenticated() {\n    return this.isAuthenticatedSubject.value;\n  }\n  isAdmin() {\n    const user = this.currentUserValue;\n    return user?.role === 'admin';\n  }\n  isVendor() {\n    const user = this.currentUserValue;\n    return user?.role === 'vendor';\n  }\n  isCustomer() {\n    const user = this.currentUserValue;\n    return user?.role === 'customer';\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "environment", "AuthService", "constructor", "http", "router", "API_URL", "apiUrl", "currentUserSubject", "isAuthenticatedSubject", "currentUser$", "asObservable", "isAuthenticated$", "initializeAuth", "token", "getToken", "getCurrentUser", "subscribe", "next", "response", "user", "error", "logout", "login", "credentials", "post", "pipe", "setToken", "register", "userData", "localStorage", "removeItem", "navigate", "get", "getItem", "setItem", "currentUserValue", "value", "isAuthenticated", "isAdmin", "role", "isVendor", "isCustomer", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["E:\\DFashion\\frontend\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { BehaviorSubject, Observable, tap } from 'rxjs';\nimport { Router } from '@angular/router';\n\nimport { User, LoginRequest, RegisterRequest, AuthResponse } from '../models/user.model';\nimport { environment } from '../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly API_URL = environment.apiUrl;\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);\n\n  public currentUser$ = this.currentUserSubject.asObservable();\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {}\n\n  initializeAuth(): void {\n    const token = this.getToken();\n    if (token) {\n      this.getCurrentUser().subscribe({\n        next: (response) => {\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        },\n        error: () => {\n          this.logout();\n        }\n      });\n    }\n  }\n\n  login(credentials: LoginRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.API_URL}/auth/login`, credentials)\n      .pipe(\n        tap(response => {\n          this.setToken(response.token);\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        })\n      );\n  }\n\n  register(userData: RegisterRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.API_URL}/auth/register`, userData)\n      .pipe(\n        tap(response => {\n          this.setToken(response.token);\n          this.currentUserSubject.next(response.user);\n          this.isAuthenticatedSubject.next(true);\n        })\n      );\n  }\n\n  logout(): void {\n    localStorage.removeItem('token');\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.router.navigate(['/auth/login']);\n  }\n\n  getCurrentUser(): Observable<{ user: User }> {\n    return this.http.get<{ user: User }>(`${this.API_URL}/auth/me`);\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem('token');\n  }\n\n  private setToken(token: string): void {\n    localStorage.setItem('token', token);\n  }\n\n  get currentUserValue(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  get isAuthenticated(): boolean {\n    return this.isAuthenticatedSubject.value;\n  }\n\n  isAdmin(): boolean {\n    const user = this.currentUserValue;\n    return user?.role === 'admin';\n  }\n\n  isVendor(): boolean {\n    const user = this.currentUserValue;\n    return user?.role === 'vendor';\n  }\n\n  isCustomer(): boolean {\n    const user = this.currentUserValue;\n    return user?.role === 'customer';\n  }\n}\n"], "mappings": "AAEA,SAASA,eAAe,EAAcC,GAAG,QAAQ,MAAM;AAIvD,SAASC,WAAW,QAAQ,mCAAmC;;;;AAK/D,OAAM,MAAOC,WAAW;EAQtBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IATC,KAAAC,OAAO,GAAGL,WAAW,CAACM,MAAM;IACrC,KAAAC,kBAAkB,GAAG,IAAIT,eAAe,CAAc,IAAI,CAAC;IAC3D,KAAAU,sBAAsB,GAAG,IAAIV,eAAe,CAAU,KAAK,CAAC;IAE7D,KAAAW,YAAY,GAAG,IAAI,CAACF,kBAAkB,CAACG,YAAY,EAAE;IACrD,KAAAC,gBAAgB,GAAG,IAAI,CAACH,sBAAsB,CAACE,YAAY,EAAE;EAKjE;EAEHE,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,IAAID,KAAK,EAAE;MACT,IAAI,CAACE,cAAc,EAAE,CAACC,SAAS,CAAC;QAC9BC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACX,kBAAkB,CAACU,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;UAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;QACxC,CAAC;QACDG,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACC,MAAM,EAAE;QACf;OACD,CAAC;;EAEN;EAEAC,KAAKA,CAACC,WAAyB;IAC7B,OAAO,IAAI,CAACpB,IAAI,CAACqB,IAAI,CAAe,GAAG,IAAI,CAACnB,OAAO,aAAa,EAAEkB,WAAW,CAAC,CAC3EE,IAAI,CACH1B,GAAG,CAACmB,QAAQ,IAAG;MACb,IAAI,CAACQ,QAAQ,CAACR,QAAQ,CAACL,KAAK,CAAC;MAC7B,IAAI,CAACN,kBAAkB,CAACU,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;MAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;IACxC,CAAC,CAAC,CACH;EACL;EAEAU,QAAQA,CAACC,QAAyB;IAChC,OAAO,IAAI,CAACzB,IAAI,CAACqB,IAAI,CAAe,GAAG,IAAI,CAACnB,OAAO,gBAAgB,EAAEuB,QAAQ,CAAC,CAC3EH,IAAI,CACH1B,GAAG,CAACmB,QAAQ,IAAG;MACb,IAAI,CAACQ,QAAQ,CAACR,QAAQ,CAACL,KAAK,CAAC;MAC7B,IAAI,CAACN,kBAAkB,CAACU,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;MAC3C,IAAI,CAACX,sBAAsB,CAACS,IAAI,CAAC,IAAI,CAAC;IACxC,CAAC,CAAC,CACH;EACL;EAEAI,MAAMA,CAAA;IACJQ,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChC,IAAI,CAACvB,kBAAkB,CAACU,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACT,sBAAsB,CAACS,IAAI,CAAC,KAAK,CAAC;IACvC,IAAI,CAACb,MAAM,CAAC2B,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAhB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACZ,IAAI,CAAC6B,GAAG,CAAiB,GAAG,IAAI,CAAC3B,OAAO,UAAU,CAAC;EACjE;EAEAS,QAAQA,CAAA;IACN,OAAOe,YAAY,CAACI,OAAO,CAAC,OAAO,CAAC;EACtC;EAEQP,QAAQA,CAACb,KAAa;IAC5BgB,YAAY,CAACK,OAAO,CAAC,OAAO,EAAErB,KAAK,CAAC;EACtC;EAEA,IAAIsB,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAC5B,kBAAkB,CAAC6B,KAAK;EACtC;EAEA,IAAIC,eAAeA,CAAA;IACjB,OAAO,IAAI,CAAC7B,sBAAsB,CAAC4B,KAAK;EAC1C;EAEAE,OAAOA,CAAA;IACL,MAAMnB,IAAI,GAAG,IAAI,CAACgB,gBAAgB;IAClC,OAAOhB,IAAI,EAAEoB,IAAI,KAAK,OAAO;EAC/B;EAEAC,QAAQA,CAAA;IACN,MAAMrB,IAAI,GAAG,IAAI,CAACgB,gBAAgB;IAClC,OAAOhB,IAAI,EAAEoB,IAAI,KAAK,QAAQ;EAChC;EAEAE,UAAUA,CAAA;IACR,MAAMtB,IAAI,GAAG,IAAI,CAACgB,gBAAgB;IAClC,OAAOhB,IAAI,EAAEoB,IAAI,KAAK,UAAU;EAClC;;;uBA1FWtC,WAAW,EAAAyC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAX9C,WAAW;MAAA+C,OAAA,EAAX/C,WAAW,CAAAgD,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}