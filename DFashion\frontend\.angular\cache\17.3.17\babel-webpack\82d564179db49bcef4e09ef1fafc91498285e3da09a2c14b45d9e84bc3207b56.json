{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/notification.service\";\nimport * as i2 from \"@angular/common\";\nfunction NotificationComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function NotificationComponent_div_1_Template_button_click_6_listener() {\n      const notification_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.close(notification_r2.id));\n    });\n    i0.ɵɵtext(7, \" \\u00D7 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"p\", 6);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notification_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(\"notification-\" + notification_r2.type);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r2.getIcon(notification_r2.type));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r2.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(notification_r2.message);\n  }\n}\nexport let NotificationComponent = /*#__PURE__*/(() => {\n  class NotificationComponent {\n    constructor(notificationService) {\n      this.notificationService = notificationService;\n      this.notifications = [];\n    }\n    ngOnInit() {\n      this.notificationService.notifications$.subscribe(notifications => {\n        this.notifications = notifications;\n      });\n    }\n    getIcon(type) {\n      switch (type) {\n        case 'success':\n          return 'fas fa-check-circle';\n        case 'error':\n          return 'fas fa-exclamation-circle';\n        case 'warning':\n          return 'fas fa-exclamation-triangle';\n        case 'info':\n        default:\n          return 'fas fa-info-circle';\n      }\n    }\n    close(id) {\n      this.notificationService.remove(id);\n    }\n    static {\n      this.ɵfac = function NotificationComponent_Factory(t) {\n        return new (t || NotificationComponent)(i0.ɵɵdirectiveInject(i1.NotificationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: NotificationComponent,\n        selectors: [[\"app-notification\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 2,\n        vars: 1,\n        consts: [[1, \"notifications-container\"], [\"class\", \"notification\", 3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"notification\"], [1, \"notification-content\"], [1, \"notification-header\"], [\"aria-label\", \"Close notification\", 1, \"close-btn\", 3, \"click\"], [1, \"notification-message\"]],\n        template: function NotificationComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, NotificationComponent_div_1_Template, 10, 6, \"div\", 1);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.notifications);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf],\n        styles: [\".notifications-container[_ngcontent-%COMP%]{position:fixed;top:80px;right:20px;z-index:10000;max-width:400px}.notification[_ngcontent-%COMP%]{background:#fff;border-radius:8px;box-shadow:0 4px 12px #00000026;margin-bottom:12px;overflow:hidden;animation:_ngcontent-%COMP%_slideIn .3s ease-out}@keyframes _ngcontent-%COMP%_slideIn{0%{transform:translate(100%);opacity:0}to{transform:translate(0);opacity:1}}.notification-success[_ngcontent-%COMP%]{border-left:4px solid #10b981}.notification-error[_ngcontent-%COMP%]{border-left:4px solid #ef4444}.notification-info[_ngcontent-%COMP%]{border-left:4px solid #3b82f6}.notification-warning[_ngcontent-%COMP%]{border-left:4px solid #f59e0b}.notification-content[_ngcontent-%COMP%]{padding:16px}.notification-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:8px}.notification-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.notification-success[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#10b981}.notification-error[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ef4444}.notification-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#3b82f6}.notification-warning[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#f59e0b}.notification-header[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{flex:1;font-size:14px;font-weight:600}.close-btn[_ngcontent-%COMP%]{background:none;border:none;font-size:18px;cursor:pointer;color:#6b7280;padding:0;width:20px;height:20px;display:flex;align-items:center;justify-content:center}.close-btn[_ngcontent-%COMP%]:hover{color:#374151}.notification-message[_ngcontent-%COMP%]{font-size:13px;color:#6b7280;margin:0;line-height:1.4}@media (max-width: 768px){.notifications-container[_ngcontent-%COMP%]{left:20px;right:20px;max-width:none}}\"]\n      });\n    }\n  }\n  return NotificationComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}