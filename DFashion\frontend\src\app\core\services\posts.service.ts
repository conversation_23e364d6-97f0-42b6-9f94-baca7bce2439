import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

export interface PostUser {
  _id: string;
  username: string;
  fullName: string;
  avatar: string;
  socialStats?: {
    followers: number;
    following: number;
    posts: number;
  };
}

export interface PostProduct {
  _id: string;
  product: {
    _id: string;
    name: string;
    price: number;
    images: Array<{ url: string; alt?: string }>;
    brand?: string;
  };
  position: {
    x: number;
    y: number;
  };
}

export interface PostComment {
  _id: string;
  user: PostUser;
  text: string;
  createdAt: string;
}

export interface PostLike {
  _id: string;
  user: PostUser;
  createdAt: string;
}

export interface InstagramPost {
  _id: string;
  user: PostUser;
  caption: string;
  media: Array<{
    type: 'image' | 'video';
    url: string;
    thumbnail?: string;
  }>;
  mediaUrl: string; // Primary media URL for compatibility
  mediaType: 'image' | 'video';
  location?: string;
  likes: PostLike[];
  comments: PostComment[];
  saves: any[];
  products: PostProduct[];
  analytics: {
    views: number;
    likes: number;
    comments: number;
    shares: number;
  };
  isLiked: boolean;
  isSaved: boolean;
  showProductTags?: boolean; // For UI state
  createdAt: string;
  updatedAt: string;
}

export interface PostsResponse {
  success: boolean;
  posts: InstagramPost[];
  pagination: {
    current: number;
    pages: number;
    total: number;
  };
}

@Injectable({
  providedIn: 'root'
})
export class PostsService {
  private readonly API_URL = 'http://localhost:5000/api';

  constructor(private http: HttpClient) {}

  // Get all posts (Instagram feed)
  getPosts(page: number = 1, limit: number = 10): Observable<PostsResponse> {
    return this.http.get<PostsResponse>(`${this.API_URL}/posts?page=${page}&limit=${limit}`).pipe(
      map(response => {
        // Transform posts to match Instagram format
        if (response.success && response.posts) {
          response.posts = response.posts.map(post => this.transformPost(post));
        }
        return response;
      }),
      catchError(error => {
        console.error('Error fetching posts:', error);
        return of({
          success: false,
          posts: [],
          pagination: { current: 1, pages: 0, total: 0 }
        });
      })
    );
  }

  // Get single post by ID
  getPost(id: string): Observable<{ success: boolean; post: InstagramPost | null }> {
    return this.http.get<{ success: boolean; post: any }>(`${this.API_URL}/posts/${id}`).pipe(
      map(response => {
        if (response.success && response.post) {
          response.post = this.transformPost(response.post);
        }
        return response;
      }),
      catchError(error => {
        console.error('Error fetching post:', error);
        return of({ success: false, post: null });
      })
    );
  }

  // Create new post
  createPost(postData: any): Observable<{ success: boolean; post?: InstagramPost; message?: string }> {
    return this.http.post<any>(`${this.API_URL}/posts`, postData).pipe(
      map(response => {
        if (response.post) {
          response.post = this.transformPost(response.post);
        }
        return { success: true, post: response.post, message: response.message };
      }),
      catchError(error => {
        console.error('Error creating post:', error);
        return of({ success: false, message: 'Failed to create post' });
      })
    );
  }

  // Like/unlike post
  toggleLike(postId: string): Observable<{ success: boolean; message?: string; likesCount?: number }> {
    return this.http.post<any>(`${this.API_URL}/posts/${postId}/like`, {}).pipe(
      map(response => ({
        success: true,
        message: response.message,
        likesCount: response.likesCount
      })),
      catchError(error => {
        console.error('Error toggling like:', error);
        return of({ success: false, message: 'Failed to toggle like' });
      })
    );
  }

  // Add comment to post
  addComment(postId: string, text: string): Observable<{ success: boolean; comment?: PostComment; message?: string }> {
    return this.http.post<any>(`${this.API_URL}/posts/${postId}/comment`, { text }).pipe(
      map(response => ({
        success: true,
        comment: response.comment,
        message: response.message
      })),
      catchError(error => {
        console.error('Error adding comment:', error);
        return of({ success: false, message: 'Failed to add comment' });
      })
    );
  }

  // Transform backend post to Instagram format
  private transformPost(post: any): InstagramPost {
    return {
      _id: post._id,
      user: {
        _id: post.user._id,
        username: post.user.username,
        fullName: post.user.fullName,
        avatar: post.user.avatar || '/assets/images/default-avatar.jpg',
        socialStats: post.user.socialStats
      },
      caption: post.caption || '',
      media: post.media || [{ type: post.mediaType || 'image', url: post.mediaUrl }],
      mediaUrl: post.mediaUrl || (post.media && post.media[0] ? post.media[0].url : ''),
      mediaType: post.mediaType || 'image',
      location: post.location,
      likes: post.likes || [],
      comments: post.comments || [],
      saves: post.saves || [],
      products: this.transformProducts(post.products || []),
      analytics: {
        views: post.analytics?.views || 0,
        likes: post.analytics?.likes || post.likes?.length || 0,
        comments: post.analytics?.comments || post.comments?.length || 0,
        shares: post.analytics?.shares || 0
      },
      isLiked: post.isLiked || false,
      isSaved: post.isSaved || false,
      createdAt: post.createdAt,
      updatedAt: post.updatedAt
    };
  }

  // Transform products for enhanced navigation
  private transformProducts(products: any[]): PostProduct[] {
    return products.map(productTag => ({
      _id: productTag._id,
      product: {
        _id: productTag.product._id,
        name: productTag.product.name,
        price: productTag.product.price,
        images: productTag.product.images || [],
        brand: productTag.product.brand
      },
      position: productTag.position || { x: 50, y: 50 }
    }));
  }

  // Get trending posts
  getTrendingPosts(limit: number = 10): Observable<{ success: boolean; posts: InstagramPost[] }> {
    return this.http.get<any>(`${this.API_URL}/posts?sortBy=likes&limit=${limit}`).pipe(
      map(response => {
        if (response.success && response.posts) {
          response.posts = response.posts.map((post: any) => this.transformPost(post));
        }
        return {
          success: response.success,
          posts: response.posts || []
        };
      }),
      catchError(error => {
        console.error('Error fetching trending posts:', error);
        return of({ success: false, posts: [] });
      })
    );
  }

  // Get user posts
  getUserPosts(userId: string, page: number = 1, limit: number = 10): Observable<PostsResponse> {
    return this.http.get<PostsResponse>(`${this.API_URL}/posts?userId=${userId}&page=${page}&limit=${limit}`).pipe(
      map(response => {
        if (response.success && response.posts) {
          response.posts = response.posts.map(post => this.transformPost(post));
        }
        return response;
      }),
      catchError(error => {
        console.error('Error fetching user posts:', error);
        return of({
          success: false,
          posts: [],
          pagination: { current: 1, pages: 0, total: 0 }
        });
      })
    );
  }
}
