{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/shop-data.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction QuickLinksComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"h2\", 9);\n    i0.ɵɵelement(3, \"i\", 10);\n    i0.ɵɵtext(4, \" Quick Links \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 11);\n    i0.ɵɵtext(6, \"Shop by category - fast and easy\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction QuickLinksComponent_div_3_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const link_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(link_r2.description);\n  }\n}\nfunction QuickLinksComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵlistener(\"click\", function QuickLinksComponent_div_3_Template_div_click_0_listener() {\n      const link_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToLink(link_r2));\n    })(\"keydown.enter\", function QuickLinksComponent_div_3_Template_div_keydown_enter_0_listener() {\n      const link_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToLink(link_r2));\n    })(\"keydown.space\", function QuickLinksComponent_div_3_Template_div_keydown_space_0_listener() {\n      const link_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToLink(link_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 13);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"h3\", 15);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, QuickLinksComponent_div_3_p_6_Template, 2, 1, \"p\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 17);\n    i0.ɵɵelement(8, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const link_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"background\", ctx_r2.getCardBackground(link_r2.color));\n    i0.ɵɵattribute(\"aria-label\", \"Navigate to \" + link_r2.title);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", link_r2.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(link_r2.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(link_r2.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isCompact);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background\", link_r2.color);\n  }\n}\nfunction QuickLinksComponent_div_4_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function QuickLinksComponent_div_4_button_4_Template_button_click_0_listener() {\n      const category_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToCategory(category_r5.slug));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 26);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r5 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"border-color\", category_r5.color)(\"color\", category_r5.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(category_r5.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", category_r5.productCount, \")\");\n  }\n}\nfunction QuickLinksComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"h3\", 22);\n    i0.ɵɵtext(2, \"Popular Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23);\n    i0.ɵɵtemplate(4, QuickLinksComponent_div_4_button_4_Template, 6, 8, \"button\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.popularCategories);\n  }\n}\nfunction QuickLinksComponent_div_5_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function QuickLinksComponent_div_5_button_4_Template_button_click_0_listener() {\n      const action_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.performAction(action_r7.action));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const action_r7 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"background\", action_r7.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(action_r7.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r7.label);\n  }\n}\nfunction QuickLinksComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"h3\", 28);\n    i0.ɵɵtext(2, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtemplate(4, QuickLinksComponent_div_5_button_4_Template, 4, 5, \"button\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.quickActions);\n  }\n}\nfunction QuickLinksComponent_div_6_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function QuickLinksComponent_div_6_div_4_Template_div_click_0_listener() {\n      const collection_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToCollection(collection_r9.slug));\n    });\n    i0.ɵɵelementStart(1, \"div\", 37);\n    i0.ɵɵelement(2, \"img\", 38);\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"div\", 40)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const collection_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", collection_r9.image, i0.ɵɵsanitizeUrl)(\"alt\", collection_r9.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(collection_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", collection_r9.itemCount, \" items\");\n  }\n}\nfunction QuickLinksComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"h3\", 33);\n    i0.ɵɵtext(2, \"Featured Collections\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 34);\n    i0.ɵɵtemplate(4, QuickLinksComponent_div_6_div_4_Template, 9, 4, \"div\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.featuredCollections);\n  }\n}\nexport class QuickLinksComponent {\n  constructor(shopDataService, router) {\n    this.shopDataService = shopDataService;\n    this.router = router;\n    this.showHeader = true;\n    this.isCompact = false;\n    this.maxLinks = 8;\n    this.showPopularCategories = true;\n    this.showQuickActions = true;\n    this.showFeaturedCollections = true;\n    this.quickLinks = [];\n    this.popularCategories = [];\n    this.quickActions = [];\n    this.featuredCollections = [];\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.loadQuickLinks();\n    this.initializePopularCategories();\n    this.initializeQuickActions();\n    this.initializeFeaturedCollections();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadQuickLinks() {\n    this.shopDataService.quickLinks$.pipe(takeUntil(this.destroy$)).subscribe(links => {\n      this.quickLinks = links.filter(link => link.featured).sort((a, b) => a.order - b.order).slice(0, this.maxLinks);\n    });\n  }\n  navigateToLink(link) {\n    this.trackLinkClick(link);\n    this.router.navigate([link.route]);\n  }\n  navigateToCategory(categorySlug) {\n    this.router.navigate(['/shop/category', categorySlug]);\n  }\n  navigateToCollection(collectionSlug) {\n    this.router.navigate(['/shop/collection', collectionSlug]);\n  }\n  performAction(action) {\n    switch (action) {\n      case 'search':\n        this.router.navigate(['/search']);\n        break;\n      case 'wishlist':\n        this.router.navigate(['/wishlist']);\n        break;\n      case 'cart':\n        this.router.navigate(['/cart']);\n        break;\n      case 'orders':\n        this.router.navigate(['/orders']);\n        break;\n      case 'offers':\n        this.router.navigate(['/offers']);\n        break;\n      case 'support':\n        this.router.navigate(['/support']);\n        break;\n      default:\n        console.log('Action not implemented:', action);\n    }\n  }\n  getCardBackground(color) {\n    return `linear-gradient(135deg, ${color}10 0%, ${color}20 100%)`;\n  }\n  trackByLinkId(index, link) {\n    return link.id;\n  }\n  initializePopularCategories() {\n    this.popularCategories = [{\n      name: 'Men\\'s Fashion',\n      slug: 'men',\n      icon: 'fas fa-male',\n      color: '#3498db',\n      productCount: 1250\n    }, {\n      name: 'Women\\'s Fashion',\n      slug: 'women',\n      icon: 'fas fa-female',\n      color: '#e91e63',\n      productCount: 1890\n    }, {\n      name: 'Footwear',\n      slug: 'footwear',\n      icon: 'fas fa-shoe-prints',\n      color: '#ff9800',\n      productCount: 567\n    }, {\n      name: 'Accessories',\n      slug: 'accessories',\n      icon: 'fas fa-gem',\n      color: '#9c27b0',\n      productCount: 423\n    }, {\n      name: 'Ethnic Wear',\n      slug: 'ethnic',\n      icon: 'fas fa-star-and-crescent',\n      color: '#ff5722',\n      productCount: 789\n    }, {\n      name: 'Sports & Fitness',\n      slug: 'sports',\n      icon: 'fas fa-dumbbell',\n      color: '#4caf50',\n      productCount: 345\n    }];\n  }\n  initializeQuickActions() {\n    this.quickActions = [{\n      label: 'Search',\n      icon: 'fas fa-search',\n      action: 'search',\n      color: '#667eea'\n    }, {\n      label: 'Wishlist',\n      icon: 'fas fa-heart',\n      action: 'wishlist',\n      color: '#e91e63'\n    }, {\n      label: 'Cart',\n      icon: 'fas fa-shopping-cart',\n      action: 'cart',\n      color: '#4caf50'\n    }, {\n      label: 'Orders',\n      icon: 'fas fa-box',\n      action: 'orders',\n      color: '#ff9800'\n    }, {\n      label: 'Offers',\n      icon: 'fas fa-tags',\n      action: 'offers',\n      color: '#f44336'\n    }, {\n      label: 'Support',\n      icon: 'fas fa-headset',\n      action: 'support',\n      color: '#00bcd4'\n    }];\n  }\n  initializeFeaturedCollections() {\n    this.featuredCollections = [{\n      name: 'Summer Collection',\n      slug: 'summer-2024',\n      image: 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?w=300&h=200&fit=crop',\n      itemCount: 156\n    }, {\n      name: 'Ethnic Elegance',\n      slug: 'ethnic-elegance',\n      image: 'https://images.unsplash.com/photo-1622470953794-aa9c70b0fb9d?w=300&h=200&fit=crop',\n      itemCount: 89\n    }, {\n      name: 'Casual Comfort',\n      slug: 'casual-comfort',\n      image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300&h=200&fit=crop',\n      itemCount: 234\n    }, {\n      name: 'Formal Wear',\n      slug: 'formal-wear',\n      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop',\n      itemCount: 123\n    }];\n  }\n  trackLinkClick(link) {\n    if (typeof window.gtag !== 'undefined') {\n      window.gtag('event', 'quick_link_click', {\n        link_title: link.title,\n        link_id: link.id,\n        link_category: link.category || 'general',\n        event_category: 'navigation'\n      });\n    }\n    // Also track in console for development\n    console.log('Quick link clicked:', link.title, '→', link.route);\n  }\n  static {\n    this.ɵfac = function QuickLinksComponent_Factory(t) {\n      return new (t || QuickLinksComponent)(i0.ɵɵdirectiveInject(i1.ShopDataService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: QuickLinksComponent,\n      selectors: [[\"app-quick-links\"]],\n      inputs: {\n        showHeader: \"showHeader\",\n        isCompact: \"isCompact\",\n        maxLinks: \"maxLinks\",\n        showPopularCategories: \"showPopularCategories\",\n        showQuickActions: \"showQuickActions\",\n        showFeaturedCollections: \"showFeaturedCollections\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 8,\n      consts: [[1, \"quick-links-section\"], [\"class\", \"section-header\", 4, \"ngIf\"], [1, \"quick-links-grid\"], [\"class\", \"quick-link-card\", \"tabindex\", \"0\", 3, \"background\", \"click\", \"keydown.enter\", \"keydown.space\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"popular-categories\", 4, \"ngIf\"], [\"class\", \"quick-actions\", 4, \"ngIf\"], [\"class\", \"featured-collections\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [1, \"fas\", \"fa-bolt\"], [1, \"section-subtitle\"], [\"tabindex\", \"0\", 1, \"quick-link-card\", 3, \"click\", \"keydown.enter\", \"keydown.space\"], [1, \"link-icon\"], [1, \"link-content\"], [1, \"link-title\"], [\"class\", \"link-description\", 4, \"ngIf\"], [1, \"link-arrow\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"hover-effect\"], [1, \"link-description\"], [1, \"popular-categories\"], [1, \"categories-title\"], [1, \"categories-list\"], [\"class\", \"category-chip\", 3, \"border-color\", \"color\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"category-chip\", 3, \"click\"], [1, \"product-count\"], [1, \"quick-actions\"], [1, \"actions-title\"], [1, \"actions-grid\"], [\"class\", \"action-btn\", 3, \"background\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"action-btn\", 3, \"click\"], [1, \"featured-collections\"], [1, \"collections-title\"], [1, \"collections-scroll\"], [\"class\", \"collection-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"collection-card\", 3, \"click\"], [1, \"collection-image\"], [\"loading\", \"lazy\", 3, \"src\", \"alt\"], [1, \"collection-overlay\"], [1, \"collection-info\"]],\n      template: function QuickLinksComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, QuickLinksComponent_div_1_Template, 7, 0, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵtemplate(3, QuickLinksComponent_div_3_Template, 10, 11, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, QuickLinksComponent_div_4_Template, 5, 1, \"div\", 4)(5, QuickLinksComponent_div_5_Template, 5, 1, \"div\", 5)(6, QuickLinksComponent_div_6_Template, 5, 1, \"div\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showHeader);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"compact\", ctx.isCompact);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.quickLinks)(\"ngForTrackBy\", ctx.trackByLinkId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showPopularCategories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showQuickActions);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showFeaturedCollections);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf],\n      styles: [\".quick-links-section[_ngcontent-%COMP%] {\\n  padding: 2rem 0;\\n  background: white;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 2rem;\\n  padding: 0 1rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: #262626;\\n  margin: 0 0 0.5rem 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n  font-size: 1.8rem;\\n}\\n@media (max-width: 768px) {\\n  .quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 1rem;\\n  padding: 0 1rem;\\n  margin-bottom: 2rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid.compact[_ngcontent-%COMP%] {\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 0.75rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid.compact[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid.compact[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  font-size: 1.2rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid.compact[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-title[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n@media (max-width: 768px) {\\n  .quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n    gap: 0.75rem;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 1fr;\\n    gap: 0.5rem;\\n  }\\n  .quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%] {\\n    min-height: auto;\\n  }\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 1.5rem;\\n  border-radius: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  overflow: hidden;\\n  border: 1px solid rgba(0, 0, 0, 0.05);\\n  min-height: 120px;\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]:hover   .link-arrow[_ngcontent-%COMP%] {\\n  transform: translateX(5px);\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]:hover   .hover-effect[_ngcontent-%COMP%] {\\n  opacity: 0.1;\\n  transform: scale(1.1);\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]:hover   .link-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]:focus {\\n  outline: 3px solid rgba(102, 126, 234, 0.3);\\n  outline-offset: 2px;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 1.5rem;\\n  background: rgba(255, 255, 255, 0.8);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]   .link-title[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #262626;\\n  margin: 0 0 0.25rem 0;\\n  line-height: 1.2;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-content[_ngcontent-%COMP%]   .link-description[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #8e8e8e;\\n  margin: 0;\\n  line-height: 1.3;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-arrow[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 0.9rem;\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .hover-effect[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  opacity: 0;\\n  transition: all 0.3s ease;\\n  border-radius: 16px;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  padding: 0 1rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%]   .categories-title[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  color: #262626;\\n  margin: 0 0 1rem 0;\\n  text-align: center;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.75rem;\\n  justify-content: center;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-chip[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1rem;\\n  background: white;\\n  border: 2px solid;\\n  border-radius: 25px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  font-weight: 500;\\n  font-size: 0.85rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-chip[_ngcontent-%COMP%]:hover {\\n  background: currentColor;\\n  color: white !important;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-chip[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-chip[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  opacity: 0.8;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  padding: 0 1rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-title[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  color: #262626;\\n  margin: 0 0 1rem 0;\\n  text-align: center;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  gap: 1rem;\\n}\\n@media (max-width: 768px) {\\n  .quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(3, 1fr);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 1rem;\\n  border: none;\\n  border-radius: 12px;\\n  color: white;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  font-weight: 600;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%] {\\n  padding: 0 1rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-title[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  color: #262626;\\n  margin: 0 0 1rem 0;\\n  text-align: center;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  overflow-x: auto;\\n  padding-bottom: 1rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 6px;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a1a1a1;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n  height: 150px;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]:hover   .collection-overlay[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.6);\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  position: relative;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.4);\\n  display: flex;\\n  align-items: flex-end;\\n  padding: 1rem;\\n  transition: all 0.3s ease;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%]   .collections-scroll[_ngcontent-%COMP%]   .collection-card[_ngcontent-%COMP%]   .collection-image[_ngcontent-%COMP%]   .collection-overlay[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.8rem;\\n  opacity: 0.9;\\n}\\n\\n@media (prefers-color-scheme: dark) {\\n  .quick-links-section[_ngcontent-%COMP%] {\\n    background: #121212;\\n  }\\n  .quick-links-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .categories-title[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .actions-title[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .collections-title[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%);\\n    border-color: #333;\\n  }\\n  .quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-title[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .quick-links-section[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.1);\\n  }\\n  .quick-links-section[_ngcontent-%COMP%]   .category-chip[_ngcontent-%COMP%] {\\n    background: #1e1e1e;\\n  }\\n  .quick-links-section[_ngcontent-%COMP%]   .category-chip[_ngcontent-%COMP%]:hover {\\n    background: currentColor !important;\\n  }\\n  .quick-links-section[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\\n  }\\n  .quick-links-section[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .quick-links-section[_ngcontent-%COMP%] {\\n    padding: 1rem 0;\\n  }\\n  .quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n  .quick-links-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .popular-categories[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .featured-collections[_ngcontent-%COMP%] {\\n    margin-bottom: 1.5rem;\\n  }\\n  .quick-links-section[_ngcontent-%COMP%]   .categories-title[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .actions-title[_ngcontent-%COMP%], .quick-links-section[_ngcontent-%COMP%]   .collections-title[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "link_r2", "description", "ɵɵlistener", "QuickLinksComponent_div_3_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "navigateToLink", "QuickLinksComponent_div_3_Template_div_keydown_enter_0_listener", "QuickLinksComponent_div_3_Template_div_keydown_space_0_listener", "ɵɵtemplate", "QuickLinksComponent_div_3_p_6_Template", "ɵɵstyleProp", "getCardBackground", "color", "ɵɵclassMap", "icon", "title", "ɵɵproperty", "isCompact", "QuickLinksComponent_div_4_button_4_Template_button_click_0_listener", "category_r5", "_r4", "navigateToCategory", "slug", "name", "ɵɵtextInterpolate1", "productCount", "QuickLinksComponent_div_4_button_4_Template", "popularCategories", "QuickLinksComponent_div_5_button_4_Template_button_click_0_listener", "action_r7", "_r6", "performAction", "action", "label", "QuickLinksComponent_div_5_button_4_Template", "quickActions", "QuickLinksComponent_div_6_div_4_Template_div_click_0_listener", "collection_r9", "_r8", "navigateToCollection", "image", "ɵɵsanitizeUrl", "itemCount", "QuickLinksComponent_div_6_div_4_Template", "featuredCollections", "QuickLinksComponent", "constructor", "shopDataService", "router", "showHeader", "maxLinks", "showPopularCategories", "showQuickActions", "showFeaturedCollections", "quickLinks", "destroy$", "ngOnInit", "loadQuickLinks", "initializePopularCategories", "initializeQuickActions", "initializeFeaturedCollections", "ngOnDestroy", "next", "complete", "quickLinks$", "pipe", "subscribe", "links", "filter", "link", "featured", "sort", "a", "b", "order", "slice", "trackLinkClick", "navigate", "route", "categorySlug", "collectionSlug", "console", "log", "trackByLinkId", "index", "id", "window", "gtag", "link_title", "link_id", "link_category", "category", "event_category", "ɵɵdirectiveInject", "i1", "ShopDataService", "i2", "Router", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "QuickLinksComponent_Template", "rf", "ctx", "QuickLinksComponent_div_1_Template", "QuickLinksComponent_div_3_Template", "QuickLinksComponent_div_4_Template", "QuickLinksComponent_div_5_Template", "QuickLinksComponent_div_6_Template", "ɵɵclassProp", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\shared\\components\\quick-links\\quick-links.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subject, takeUntil } from 'rxjs';\nimport { ShopDataService, QuickLink } from '../../../core/services/shop-data.service';\n\n@Component({\n  selector: 'app-quick-links',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"quick-links-section\">\n      <!-- Section Header -->\n      <div class=\"section-header\" *ngIf=\"showHeader\">\n        <div class=\"header-content\">\n          <h2 class=\"section-title\">\n            <i class=\"fas fa-bolt\"></i>\n            Quick Links\n          </h2>\n          <p class=\"section-subtitle\">Shop by category - fast and easy</p>\n        </div>\n      </div>\n\n      <!-- Quick Links Grid -->\n      <div class=\"quick-links-grid\" [class.compact]=\"isCompact\">\n        <div \n          *ngFor=\"let link of quickLinks; trackBy: trackByLinkId\" \n          class=\"quick-link-card\"\n          [style.background]=\"getCardBackground(link.color)\"\n          (click)=\"navigateToLink(link)\"\n          [attr.aria-label]=\"'Navigate to ' + link.title\"\n          tabindex=\"0\"\n          (keydown.enter)=\"navigateToLink(link)\"\n          (keydown.space)=\"navigateToLink(link)\">\n          \n          <!-- Link Icon -->\n          <div class=\"link-icon\" [style.color]=\"link.color\">\n            <i [class]=\"link.icon\"></i>\n          </div>\n          \n          <!-- Link Content -->\n          <div class=\"link-content\">\n            <h3 class=\"link-title\">{{ link.title }}</h3>\n            <p class=\"link-description\" *ngIf=\"!isCompact\">{{ link.description }}</p>\n          </div>\n          \n          <!-- Arrow Icon -->\n          <div class=\"link-arrow\">\n            <i class=\"fas fa-chevron-right\"></i>\n          </div>\n          \n          <!-- Hover Effect -->\n          <div class=\"hover-effect\" [style.background]=\"link.color\"></div>\n        </div>\n      </div>\n\n      <!-- Popular Categories -->\n      <div class=\"popular-categories\" *ngIf=\"showPopularCategories\">\n        <h3 class=\"categories-title\">Popular Categories</h3>\n        <div class=\"categories-list\">\n          <button \n            *ngFor=\"let category of popularCategories\"\n            class=\"category-chip\"\n            (click)=\"navigateToCategory(category.slug)\"\n            [style.border-color]=\"category.color\"\n            [style.color]=\"category.color\">\n            <i [class]=\"category.icon\"></i>\n            <span>{{ category.name }}</span>\n            <span class=\"product-count\">({{ category.productCount }})</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Quick Actions -->\n      <div class=\"quick-actions\" *ngIf=\"showQuickActions\">\n        <h3 class=\"actions-title\">Quick Actions</h3>\n        <div class=\"actions-grid\">\n          <button \n            *ngFor=\"let action of quickActions\"\n            class=\"action-btn\"\n            (click)=\"performAction(action.action)\"\n            [style.background]=\"action.color\">\n            <i [class]=\"action.icon\"></i>\n            <span>{{ action.label }}</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Featured Collections -->\n      <div class=\"featured-collections\" *ngIf=\"showFeaturedCollections\">\n        <h3 class=\"collections-title\">Featured Collections</h3>\n        <div class=\"collections-scroll\">\n          <div \n            *ngFor=\"let collection of featuredCollections\"\n            class=\"collection-card\"\n            (click)=\"navigateToCollection(collection.slug)\">\n            <div class=\"collection-image\">\n              <img [src]=\"collection.image\" [alt]=\"collection.name\" loading=\"lazy\">\n              <div class=\"collection-overlay\">\n                <div class=\"collection-info\">\n                  <h4>{{ collection.name }}</h4>\n                  <p>{{ collection.itemCount }} items</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./quick-links.component.scss']\n})\nexport class QuickLinksComponent implements OnInit, OnDestroy {\n  @Input() showHeader: boolean = true;\n  @Input() isCompact: boolean = false;\n  @Input() maxLinks: number = 8;\n  @Input() showPopularCategories: boolean = true;\n  @Input() showQuickActions: boolean = true;\n  @Input() showFeaturedCollections: boolean = true;\n  \n  quickLinks: QuickLink[] = [];\n  popularCategories: any[] = [];\n  quickActions: any[] = [];\n  featuredCollections: any[] = [];\n  \n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private shopDataService: ShopDataService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadQuickLinks();\n    this.initializePopularCategories();\n    this.initializeQuickActions();\n    this.initializeFeaturedCollections();\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadQuickLinks(): void {\n    this.shopDataService.quickLinks$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(links => {\n        this.quickLinks = links\n          .filter(link => link.featured)\n          .sort((a, b) => a.order - b.order)\n          .slice(0, this.maxLinks);\n      });\n  }\n\n  navigateToLink(link: QuickLink): void {\n    this.trackLinkClick(link);\n    this.router.navigate([link.route]);\n  }\n\n  navigateToCategory(categorySlug: string): void {\n    this.router.navigate(['/shop/category', categorySlug]);\n  }\n\n  navigateToCollection(collectionSlug: string): void {\n    this.router.navigate(['/shop/collection', collectionSlug]);\n  }\n\n  performAction(action: string): void {\n    switch (action) {\n      case 'search':\n        this.router.navigate(['/search']);\n        break;\n      case 'wishlist':\n        this.router.navigate(['/wishlist']);\n        break;\n      case 'cart':\n        this.router.navigate(['/cart']);\n        break;\n      case 'orders':\n        this.router.navigate(['/orders']);\n        break;\n      case 'offers':\n        this.router.navigate(['/offers']);\n        break;\n      case 'support':\n        this.router.navigate(['/support']);\n        break;\n      default:\n        console.log('Action not implemented:', action);\n    }\n  }\n\n  getCardBackground(color: string): string {\n    return `linear-gradient(135deg, ${color}10 0%, ${color}20 100%)`;\n  }\n\n  trackByLinkId(index: number, link: QuickLink): string {\n    return link.id;\n  }\n\n  private initializePopularCategories(): void {\n    this.popularCategories = [\n      {\n        name: 'Men\\'s Fashion',\n        slug: 'men',\n        icon: 'fas fa-male',\n        color: '#3498db',\n        productCount: 1250\n      },\n      {\n        name: 'Women\\'s Fashion',\n        slug: 'women',\n        icon: 'fas fa-female',\n        color: '#e91e63',\n        productCount: 1890\n      },\n      {\n        name: 'Footwear',\n        slug: 'footwear',\n        icon: 'fas fa-shoe-prints',\n        color: '#ff9800',\n        productCount: 567\n      },\n      {\n        name: 'Accessories',\n        slug: 'accessories',\n        icon: 'fas fa-gem',\n        color: '#9c27b0',\n        productCount: 423\n      },\n      {\n        name: 'Ethnic Wear',\n        slug: 'ethnic',\n        icon: 'fas fa-star-and-crescent',\n        color: '#ff5722',\n        productCount: 789\n      },\n      {\n        name: 'Sports & Fitness',\n        slug: 'sports',\n        icon: 'fas fa-dumbbell',\n        color: '#4caf50',\n        productCount: 345\n      }\n    ];\n  }\n\n  private initializeQuickActions(): void {\n    this.quickActions = [\n      {\n        label: 'Search',\n        icon: 'fas fa-search',\n        action: 'search',\n        color: '#667eea'\n      },\n      {\n        label: 'Wishlist',\n        icon: 'fas fa-heart',\n        action: 'wishlist',\n        color: '#e91e63'\n      },\n      {\n        label: 'Cart',\n        icon: 'fas fa-shopping-cart',\n        action: 'cart',\n        color: '#4caf50'\n      },\n      {\n        label: 'Orders',\n        icon: 'fas fa-box',\n        action: 'orders',\n        color: '#ff9800'\n      },\n      {\n        label: 'Offers',\n        icon: 'fas fa-tags',\n        action: 'offers',\n        color: '#f44336'\n      },\n      {\n        label: 'Support',\n        icon: 'fas fa-headset',\n        action: 'support',\n        color: '#00bcd4'\n      }\n    ];\n  }\n\n  private initializeFeaturedCollections(): void {\n    this.featuredCollections = [\n      {\n        name: 'Summer Collection',\n        slug: 'summer-2024',\n        image: 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?w=300&h=200&fit=crop',\n        itemCount: 156\n      },\n      {\n        name: 'Ethnic Elegance',\n        slug: 'ethnic-elegance',\n        image: 'https://images.unsplash.com/photo-1622470953794-aa9c70b0fb9d?w=300&h=200&fit=crop',\n        itemCount: 89\n      },\n      {\n        name: 'Casual Comfort',\n        slug: 'casual-comfort',\n        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300&h=200&fit=crop',\n        itemCount: 234\n      },\n      {\n        name: 'Formal Wear',\n        slug: 'formal-wear',\n        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop',\n        itemCount: 123\n      }\n    ];\n  }\n\n  private trackLinkClick(link: QuickLink): void {\n    if (typeof (window as any).gtag !== 'undefined') {\n      (window as any).gtag('event', 'quick_link_click', {\n        link_title: link.title,\n        link_id: link.id,\n        link_category: link.category || 'general',\n        event_category: 'navigation'\n      });\n    }\n\n    // Also track in console for development\n    console.log('Quick link clicked:', link.title, '→', link.route);\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;IAY/BC,EAFJ,CAAAC,cAAA,aAA+C,aACjB,YACA;IACxBD,EAAA,CAAAE,SAAA,YAA2B;IAC3BF,EAAA,CAAAG,MAAA,oBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,YAA4B;IAAAD,EAAA,CAAAG,MAAA,uCAAgC;IAEhEH,EAFgE,CAAAI,YAAA,EAAI,EAC5D,EACF;;;;;IAsBAJ,EAAA,CAAAC,cAAA,YAA+C;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAA1BJ,EAAA,CAAAK,SAAA,EAAsB;IAAtBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAC,WAAA,CAAsB;;;;;;IAlBzER,EAAA,CAAAC,cAAA,cAQyC;IAAvCD,EAJA,CAAAS,UAAA,mBAAAC,wDAAA;MAAA,MAAAH,OAAA,GAAAP,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAG,cAAA,CAAAV,OAAA,CAAoB;IAAA,EAAC,2BAAAW,gEAAA;MAAA,MAAAX,OAAA,GAAAP,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAGbF,MAAA,CAAAG,cAAA,CAAAV,OAAA,CAAoB;IAAA,EAAC,2BAAAY,gEAAA;MAAA,MAAAZ,OAAA,GAAAP,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CACrBF,MAAA,CAAAG,cAAA,CAAAV,OAAA,CAAoB;IAAA,EAAC;IAGtCP,EAAA,CAAAC,cAAA,cAAkD;IAChDD,EAAA,CAAAE,SAAA,QAA2B;IAC7BF,EAAA,CAAAI,YAAA,EAAM;IAIJJ,EADF,CAAAC,cAAA,cAA0B,aACD;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5CJ,EAAA,CAAAoB,UAAA,IAAAC,sCAAA,gBAA+C;IACjDrB,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,SAAA,YAAoC;IACtCF,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,SAAA,cAAgE;IAClEF,EAAA,CAAAI,YAAA,EAAM;;;;;IAzBJJ,EAAA,CAAAsB,WAAA,eAAAR,MAAA,CAAAS,iBAAA,CAAAhB,OAAA,CAAAiB,KAAA,EAAkD;;IAQ3BxB,EAAA,CAAAK,SAAA,EAA0B;IAA1BL,EAAA,CAAAsB,WAAA,UAAAf,OAAA,CAAAiB,KAAA,CAA0B;IAC5CxB,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAAyB,UAAA,CAAAlB,OAAA,CAAAmB,IAAA,CAAmB;IAKC1B,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAoB,KAAA,CAAgB;IACV3B,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAA4B,UAAA,UAAAd,MAAA,CAAAe,SAAA,CAAgB;IASrB7B,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAsB,WAAA,eAAAf,OAAA,CAAAiB,KAAA,CAA+B;;;;;;IAQzDxB,EAAA,CAAAC,cAAA,iBAKiC;IAF/BD,EAAA,CAAAS,UAAA,mBAAAqB,oEAAA;MAAA,MAAAC,WAAA,GAAA/B,EAAA,CAAAW,aAAA,CAAAqB,GAAA,EAAAnB,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAmB,kBAAA,CAAAF,WAAA,CAAAG,IAAA,CAAiC;IAAA,EAAC;IAG3ClC,EAAA,CAAAE,SAAA,QAA+B;IAC/BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChCJ,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAG,MAAA,GAA6B;IAC3DH,EAD2D,CAAAI,YAAA,EAAO,EACzD;;;;IAJPJ,EADA,CAAAsB,WAAA,iBAAAS,WAAA,CAAAP,KAAA,CAAqC,UAAAO,WAAA,CAAAP,KAAA,CACP;IAC3BxB,EAAA,CAAAK,SAAA,EAAuB;IAAvBL,EAAA,CAAAyB,UAAA,CAAAM,WAAA,CAAAL,IAAA,CAAuB;IACpB1B,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAyB,WAAA,CAAAI,IAAA,CAAmB;IACGnC,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAoC,kBAAA,MAAAL,WAAA,CAAAM,YAAA,MAA6B;;;;;IAV7DrC,EADF,CAAAC,cAAA,cAA8D,aAC/B;IAAAD,EAAA,CAAAG,MAAA,yBAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACpDJ,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAoB,UAAA,IAAAkB,2CAAA,qBAKiC;IAMrCtC,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAVqBJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA4B,UAAA,YAAAd,MAAA,CAAAyB,iBAAA,CAAoB;;;;;;IAgB3CvC,EAAA,CAAAC,cAAA,iBAIoC;IADlCD,EAAA,CAAAS,UAAA,mBAAA+B,oEAAA;MAAA,MAAAC,SAAA,GAAAzC,EAAA,CAAAW,aAAA,CAAA+B,GAAA,EAAA7B,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA6B,aAAA,CAAAF,SAAA,CAAAG,MAAA,CAA4B;IAAA,EAAC;IAEtC5C,EAAA,CAAAE,SAAA,QAA6B;IAC7BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAC1BH,EAD0B,CAAAI,YAAA,EAAO,EACxB;;;;IAHPJ,EAAA,CAAAsB,WAAA,eAAAmB,SAAA,CAAAjB,KAAA,CAAiC;IAC9BxB,EAAA,CAAAK,SAAA,EAAqB;IAArBL,EAAA,CAAAyB,UAAA,CAAAgB,SAAA,CAAAf,IAAA,CAAqB;IAClB1B,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAmC,SAAA,CAAAI,KAAA,CAAkB;;;;;IAR5B7C,EADF,CAAAC,cAAA,cAAoD,aACxB;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5CJ,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAoB,UAAA,IAAA0B,2CAAA,qBAIoC;IAKxC9C,EADE,CAAAI,YAAA,EAAM,EACF;;;;IARmBJ,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAA4B,UAAA,YAAAd,MAAA,CAAAiC,YAAA,CAAe;;;;;;IAcpC/C,EAAA,CAAAC,cAAA,cAGkD;IAAhDD,EAAA,CAAAS,UAAA,mBAAAuC,8DAAA;MAAA,MAAAC,aAAA,GAAAjD,EAAA,CAAAW,aAAA,CAAAuC,GAAA,EAAArC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAqC,oBAAA,CAAAF,aAAA,CAAAf,IAAA,CAAqC;IAAA,EAAC;IAC/ClC,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,cAAqE;IAGjEF,EAFJ,CAAAC,cAAA,cAAgC,cACD,SACvB;IAAAD,EAAA,CAAAG,MAAA,GAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAI3CH,EAJ2C,CAAAI,YAAA,EAAI,EACnC,EACF,EACF,EACF;;;;IARGJ,EAAA,CAAAK,SAAA,GAAwB;IAACL,EAAzB,CAAA4B,UAAA,QAAAqB,aAAA,CAAAG,KAAA,EAAApD,EAAA,CAAAqD,aAAA,CAAwB,QAAAJ,aAAA,CAAAd,IAAA,CAAwB;IAG7CnC,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAM,iBAAA,CAAA2C,aAAA,CAAAd,IAAA,CAAqB;IACtBnC,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAoC,kBAAA,KAAAa,aAAA,CAAAK,SAAA,WAAgC;;;;;IAX7CtD,EADF,CAAAC,cAAA,cAAkE,aAClC;IAAAD,EAAA,CAAAG,MAAA,2BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACvDJ,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAoB,UAAA,IAAAmC,wCAAA,kBAGkD;IAYtDvD,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAduBJ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA4B,UAAA,YAAAd,MAAA,CAAA0C,mBAAA,CAAsB;;;AAmBzD,OAAM,MAAOC,mBAAmB;EAe9BC,YACUC,eAAgC,EAChCC,MAAc;IADd,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAhBP,KAAAC,UAAU,GAAY,IAAI;IAC1B,KAAAhC,SAAS,GAAY,KAAK;IAC1B,KAAAiC,QAAQ,GAAW,CAAC;IACpB,KAAAC,qBAAqB,GAAY,IAAI;IACrC,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,uBAAuB,GAAY,IAAI;IAEhD,KAAAC,UAAU,GAAgB,EAAE;IAC5B,KAAA3B,iBAAiB,GAAU,EAAE;IAC7B,KAAAQ,YAAY,GAAU,EAAE;IACxB,KAAAS,mBAAmB,GAAU,EAAE;IAEvB,KAAAW,QAAQ,GAAG,IAAIrE,OAAO,EAAQ;EAKnC;EAEHsE,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,2BAA2B,EAAE;IAClC,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,6BAA6B,EAAE;EACtC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACN,QAAQ,CAACO,IAAI,EAAE;IACpB,IAAI,CAACP,QAAQ,CAACQ,QAAQ,EAAE;EAC1B;EAEAN,cAAcA,CAAA;IACZ,IAAI,CAACV,eAAe,CAACiB,WAAW,CAC7BC,IAAI,CAAC9E,SAAS,CAAC,IAAI,CAACoE,QAAQ,CAAC,CAAC,CAC9BW,SAAS,CAACC,KAAK,IAAG;MACjB,IAAI,CAACb,UAAU,GAAGa,KAAK,CACpBC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,CAC7BC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,KAAK,GAAGD,CAAC,CAACC,KAAK,CAAC,CACjCC,KAAK,CAAC,CAAC,EAAE,IAAI,CAACzB,QAAQ,CAAC;IAC5B,CAAC,CAAC;EACN;EAEA7C,cAAcA,CAACgE,IAAe;IAC5B,IAAI,CAACO,cAAc,CAACP,IAAI,CAAC;IACzB,IAAI,CAACrB,MAAM,CAAC6B,QAAQ,CAAC,CAACR,IAAI,CAACS,KAAK,CAAC,CAAC;EACpC;EAEAzD,kBAAkBA,CAAC0D,YAAoB;IACrC,IAAI,CAAC/B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,gBAAgB,EAAEE,YAAY,CAAC,CAAC;EACxD;EAEAxC,oBAAoBA,CAACyC,cAAsB;IACzC,IAAI,CAAChC,MAAM,CAAC6B,QAAQ,CAAC,CAAC,kBAAkB,EAAEG,cAAc,CAAC,CAAC;EAC5D;EAEAjD,aAAaA,CAACC,MAAc;IAC1B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,IAAI,CAACgB,MAAM,CAAC6B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;QACjC;MACF,KAAK,UAAU;QACb,IAAI,CAAC7B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACnC;MACF,KAAK,MAAM;QACT,IAAI,CAAC7B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;QAC/B;MACF,KAAK,QAAQ;QACX,IAAI,CAAC7B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;QACjC;MACF,KAAK,QAAQ;QACX,IAAI,CAAC7B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;QACjC;MACF,KAAK,SAAS;QACZ,IAAI,CAAC7B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QAClC;MACF;QACEI,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAElD,MAAM,CAAC;;EAEpD;EAEArB,iBAAiBA,CAACC,KAAa;IAC7B,OAAO,2BAA2BA,KAAK,UAAUA,KAAK,UAAU;EAClE;EAEAuE,aAAaA,CAACC,KAAa,EAAEf,IAAe;IAC1C,OAAOA,IAAI,CAACgB,EAAE;EAChB;EAEQ3B,2BAA2BA,CAAA;IACjC,IAAI,CAAC/B,iBAAiB,GAAG,CACvB;MACEJ,IAAI,EAAE,gBAAgB;MACtBD,IAAI,EAAE,KAAK;MACXR,IAAI,EAAE,aAAa;MACnBF,KAAK,EAAE,SAAS;MAChBa,YAAY,EAAE;KACf,EACD;MACEF,IAAI,EAAE,kBAAkB;MACxBD,IAAI,EAAE,OAAO;MACbR,IAAI,EAAE,eAAe;MACrBF,KAAK,EAAE,SAAS;MAChBa,YAAY,EAAE;KACf,EACD;MACEF,IAAI,EAAE,UAAU;MAChBD,IAAI,EAAE,UAAU;MAChBR,IAAI,EAAE,oBAAoB;MAC1BF,KAAK,EAAE,SAAS;MAChBa,YAAY,EAAE;KACf,EACD;MACEF,IAAI,EAAE,aAAa;MACnBD,IAAI,EAAE,aAAa;MACnBR,IAAI,EAAE,YAAY;MAClBF,KAAK,EAAE,SAAS;MAChBa,YAAY,EAAE;KACf,EACD;MACEF,IAAI,EAAE,aAAa;MACnBD,IAAI,EAAE,QAAQ;MACdR,IAAI,EAAE,0BAA0B;MAChCF,KAAK,EAAE,SAAS;MAChBa,YAAY,EAAE;KACf,EACD;MACEF,IAAI,EAAE,kBAAkB;MACxBD,IAAI,EAAE,QAAQ;MACdR,IAAI,EAAE,iBAAiB;MACvBF,KAAK,EAAE,SAAS;MAChBa,YAAY,EAAE;KACf,CACF;EACH;EAEQkC,sBAAsBA,CAAA;IAC5B,IAAI,CAACxB,YAAY,GAAG,CAClB;MACEF,KAAK,EAAE,QAAQ;MACfnB,IAAI,EAAE,eAAe;MACrBkB,MAAM,EAAE,QAAQ;MAChBpB,KAAK,EAAE;KACR,EACD;MACEqB,KAAK,EAAE,UAAU;MACjBnB,IAAI,EAAE,cAAc;MACpBkB,MAAM,EAAE,UAAU;MAClBpB,KAAK,EAAE;KACR,EACD;MACEqB,KAAK,EAAE,MAAM;MACbnB,IAAI,EAAE,sBAAsB;MAC5BkB,MAAM,EAAE,MAAM;MACdpB,KAAK,EAAE;KACR,EACD;MACEqB,KAAK,EAAE,QAAQ;MACfnB,IAAI,EAAE,YAAY;MAClBkB,MAAM,EAAE,QAAQ;MAChBpB,KAAK,EAAE;KACR,EACD;MACEqB,KAAK,EAAE,QAAQ;MACfnB,IAAI,EAAE,aAAa;MACnBkB,MAAM,EAAE,QAAQ;MAChBpB,KAAK,EAAE;KACR,EACD;MACEqB,KAAK,EAAE,SAAS;MAChBnB,IAAI,EAAE,gBAAgB;MACtBkB,MAAM,EAAE,SAAS;MACjBpB,KAAK,EAAE;KACR,CACF;EACH;EAEQgD,6BAA6BA,CAAA;IACnC,IAAI,CAAChB,mBAAmB,GAAG,CACzB;MACErB,IAAI,EAAE,mBAAmB;MACzBD,IAAI,EAAE,aAAa;MACnBkB,KAAK,EAAE,mFAAmF;MAC1FE,SAAS,EAAE;KACZ,EACD;MACEnB,IAAI,EAAE,iBAAiB;MACvBD,IAAI,EAAE,iBAAiB;MACvBkB,KAAK,EAAE,mFAAmF;MAC1FE,SAAS,EAAE;KACZ,EACD;MACEnB,IAAI,EAAE,gBAAgB;MACtBD,IAAI,EAAE,gBAAgB;MACtBkB,KAAK,EAAE,mFAAmF;MAC1FE,SAAS,EAAE;KACZ,EACD;MACEnB,IAAI,EAAE,aAAa;MACnBD,IAAI,EAAE,aAAa;MACnBkB,KAAK,EAAE,mFAAmF;MAC1FE,SAAS,EAAE;KACZ,CACF;EACH;EAEQkC,cAAcA,CAACP,IAAe;IACpC,IAAI,OAAQiB,MAAc,CAACC,IAAI,KAAK,WAAW,EAAE;MAC9CD,MAAc,CAACC,IAAI,CAAC,OAAO,EAAE,kBAAkB,EAAE;QAChDC,UAAU,EAAEnB,IAAI,CAACtD,KAAK;QACtB0E,OAAO,EAAEpB,IAAI,CAACgB,EAAE;QAChBK,aAAa,EAAErB,IAAI,CAACsB,QAAQ,IAAI,SAAS;QACzCC,cAAc,EAAE;OACjB,CAAC;;IAGJ;IACAX,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEb,IAAI,CAACtD,KAAK,EAAE,GAAG,EAAEsD,IAAI,CAACS,KAAK,CAAC;EACjE;;;uBA1NWjC,mBAAmB,EAAAzD,EAAA,CAAAyG,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAA3G,EAAA,CAAAyG,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAnBpD,mBAAmB;MAAAqD,SAAA;MAAAC,MAAA;QAAAlD,UAAA;QAAAhC,SAAA;QAAAiC,QAAA;QAAAC,qBAAA;QAAAC,gBAAA;QAAAC,uBAAA;MAAA;MAAA+C,UAAA;MAAAC,QAAA,GAAAjH,EAAA,CAAAkH,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArG5BxH,EAAA,CAAAC,cAAA,aAAiC;UAE/BD,EAAA,CAAAoB,UAAA,IAAAsG,kCAAA,iBAA+C;UAW/C1H,EAAA,CAAAC,cAAA,aAA0D;UACxDD,EAAA,CAAAoB,UAAA,IAAAuG,kCAAA,mBAQyC;UAqB3C3H,EAAA,CAAAI,YAAA,EAAM;UAmCNJ,EAhCA,CAAAoB,UAAA,IAAAwG,kCAAA,iBAA8D,IAAAC,kCAAA,iBAiBV,IAAAC,kCAAA,iBAec;UAmBpE9H,EAAA,CAAAI,YAAA,EAAM;;;UA/FyBJ,EAAA,CAAAK,SAAA,EAAgB;UAAhBL,EAAA,CAAA4B,UAAA,SAAA6F,GAAA,CAAA5D,UAAA,CAAgB;UAWf7D,EAAA,CAAAK,SAAA,EAA2B;UAA3BL,EAAA,CAAA+H,WAAA,YAAAN,GAAA,CAAA5F,SAAA,CAA2B;UAEpC7B,EAAA,CAAAK,SAAA,EAAe;UAAAL,EAAf,CAAA4B,UAAA,YAAA6F,GAAA,CAAAvD,UAAA,CAAe,iBAAAuD,GAAA,CAAA1B,aAAA,CAAsB;UA+BzB/F,EAAA,CAAAK,SAAA,EAA2B;UAA3BL,EAAA,CAAA4B,UAAA,SAAA6F,GAAA,CAAA1D,qBAAA,CAA2B;UAiBhC/D,EAAA,CAAAK,SAAA,EAAsB;UAAtBL,EAAA,CAAA4B,UAAA,SAAA6F,GAAA,CAAAzD,gBAAA,CAAsB;UAefhE,EAAA,CAAAK,SAAA,EAA6B;UAA7BL,EAAA,CAAA4B,UAAA,SAAA6F,GAAA,CAAAxD,uBAAA,CAA6B;;;qBAhF1DpE,YAAY,EAAAmI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}