{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport { NoDataComponent } from '../../../../shared/components/no-data/no-data.component';\nimport { CategorySelectionComponent } from '../../../../shared/components/category-selection/category-selection.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/product.service\";\nimport * as i3 from \"../../../../core/services/no-data-config.service\";\nimport * as i4 from \"../../../../core/services/advanced-search.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nconst _c0 = () => [1, 2, 3, 4, 5];\nfunction SearchComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function SearchComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 13);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchComponent_div_9_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_9_div_11_Template_div_click_0_listener() {\n      const category_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectCategoryForSearch(category_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 24);\n    i0.ɵɵelement(2, \"i\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 26)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(8, \"i\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r6 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(category_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r6.productCount, \" products\");\n  }\n}\nfunction SearchComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 16)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_9_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.dismissCategorySelection());\n    });\n    i0.ɵɵelement(6, \"i\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 18)(8, \"p\");\n    i0.ɵɵtext(9, \"We found multiple categories that match your search. Please select one:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 19);\n    i0.ɵɵtemplate(11, SearchComponent_div_9_div_11_Template, 9, 2, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 21)(13, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_9_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.searchInAllCategories());\n    });\n    i0.ɵɵtext(14, \" Search in All Categories \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Choose Category for \\\"\", ctx_r2.searchQuery, \"\\\"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.categoryOptions);\n  }\n}\nfunction SearchComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"app-category-selection\", 29);\n    i0.ɵɵlistener(\"categorySelected\", function SearchComponent_div_10_Template_app_category_selection_categorySelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCategorySelected($event));\n    })(\"searchAllRequested\", function SearchComponent_div_10_Template_app_category_selection_searchAllRequested_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchAllRequested($event));\n    })(\"refineSearchRequested\", function SearchComponent_div_10_Template_app_category_selection_refineSearchRequested_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRefineSearchRequested($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"categories\", ctx_r2.categoryOptions)(\"originalQuery\", ctx_r2.searchQuery);\n  }\n}\nfunction SearchComponent_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"div\", 35);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Searching...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SearchComponent_div_11_div_3_div_6_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_3_div_6_span_3_Template_span_click_0_listener() {\n      const suggestion_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.searchFor(suggestion_r10));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const suggestion_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", suggestion_r10, \" \");\n  }\n}\nfunction SearchComponent_div_11_div_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"p\");\n    i0.ɵɵtext(2, \"Related searches:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SearchComponent_div_11_div_3_div_6_span_3_Template, 2, 1, \"span\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.searchSuggestions);\n  }\n}\nfunction SearchComponent_div_11_div_3_option_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r11);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(category_r11);\n  }\n}\nfunction SearchComponent_div_11_div_3_app_no_data_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-no-data\", 61);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"title\", ctx_r2.noDataConfig.title)(\"message\", ctx_r2.noDataConfig.message)(\"iconClass\", ctx_r2.noDataConfig.iconClass)(\"containerClass\", ctx_r2.noDataConfig.containerClass)(\"showActions\", ctx_r2.noDataConfig.showActions)(\"primaryAction\", ctx_r2.noDataConfig.primaryAction)(\"secondaryAction\", ctx_r2.noDataConfig.secondaryAction)(\"suggestions\", ctx_r2.noDataConfig.suggestions)(\"suggestionsTitle\", ctx_r2.noDataConfig.suggestionsTitle);\n  }\n}\nfunction SearchComponent_div_11_div_3_div_46_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.getDiscountPercentage(product_r13), \"% OFF\");\n  }\n}\nfunction SearchComponent_div_11_div_3_div_46_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(2, 1, product_r13.price, \"1.0-0\"), \"\");\n  }\n}\nfunction SearchComponent_div_11_div_3_div_46_div_1_div_17_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 86);\n  }\n  if (rf & 2) {\n    const i_r14 = ctx.index;\n    const product_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassProp(\"filled\", i_r14 < product_r13.rating.average);\n  }\n}\nfunction SearchComponent_div_11_div_3_div_46_div_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"div\", 83);\n    i0.ɵɵtemplate(2, SearchComponent_div_11_div_3_div_46_div_1_div_17_i_2_Template, 1, 2, \"i\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 85);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(2, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r13.rating.count, \")\");\n  }\n}\nfunction SearchComponent_div_11_div_3_div_46_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_3_div_46_div_1_Template_div_click_0_listener() {\n      const product_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.viewProduct(product_r13._id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 65);\n    i0.ɵɵelement(2, \"img\", 66);\n    i0.ɵɵtemplate(3, SearchComponent_div_11_div_3_div_46_div_1_div_3_Template, 3, 1, \"div\", 67);\n    i0.ɵɵelementStart(4, \"div\", 68)(5, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_3_div_46_div_1_Template_button_click_5_listener($event) {\n      const product_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      ctx_r2.toggleWishlist(product_r13._id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(6, \"i\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 70)(8, \"h3\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 71);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 72)(13, \"span\", 73);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, SearchComponent_div_11_div_3_div_46_div_1_span_16_Template, 3, 4, \"span\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, SearchComponent_div_11_div_3_div_46_div_1_div_17_Template, 5, 3, \"div\", 75);\n    i0.ɵɵelementStart(18, \"div\", 76)(19, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_3_div_46_div_1_Template_button_click_19_listener($event) {\n      const product_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      ctx_r2.addToCart(product_r13._id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(20, \"i\", 78);\n    i0.ɵɵtext(21, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_3_div_46_div_1_Template_button_click_22_listener($event) {\n      const product_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      ctx_r2.buyNow(product_r13._id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtext(23, \" Buy Now \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r13 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (product_r13.images == null ? null : product_r13.images[0]) || \"/assets/images/placeholder-product.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", product_r13.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r13.discountPrice);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r2.isInWishlist(product_r13._id) ? \"fas fa-heart\" : \"far fa-heart\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r13.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r13.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(15, 10, product_r13.discountPrice || product_r13.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r13.discountPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r13.rating);\n  }\n}\nfunction SearchComponent_div_11_div_3_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, SearchComponent_div_11_div_3_div_46_div_1_Template, 24, 13, \"div\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.searchResults);\n  }\n}\nfunction SearchComponent_div_11_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"h2\");\n    i0.ɵɵtext(3, \"Search Results\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SearchComponent_div_11_div_3_div_6_Template, 4, 1, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 40)(8, \"div\", 41)(9, \"label\");\n    i0.ɵɵtext(10, \"Category:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"select\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchComponent_div_11_div_3_Template_select_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedCategory, $event) || (ctx_r2.selectedCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SearchComponent_div_11_div_3_Template_select_change_11_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyFilters());\n    });\n    i0.ɵɵelementStart(12, \"option\", 43);\n    i0.ɵɵtext(13, \"All Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, SearchComponent_div_11_div_3_option_14_Template, 2, 2, \"option\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 41)(16, \"label\");\n    i0.ɵɵtext(17, \"Price Range:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"select\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchComponent_div_11_div_3_Template_select_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedPriceRange, $event) || (ctx_r2.selectedPriceRange = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SearchComponent_div_11_div_3_Template_select_change_18_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyFilters());\n    });\n    i0.ɵɵelementStart(19, \"option\", 43);\n    i0.ɵɵtext(20, \"All Prices\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"option\", 45);\n    i0.ɵɵtext(22, \"Under \\u20B91,000\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"option\", 46);\n    i0.ɵɵtext(24, \"\\u20B91,000 - \\u20B92,500\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"option\", 47);\n    i0.ɵɵtext(26, \"\\u20B92,500 - \\u20B95,000\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"option\", 48);\n    i0.ɵɵtext(28, \"\\u20B95,000 - \\u20B910,000\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"option\", 49);\n    i0.ɵɵtext(30, \"Above \\u20B910,000\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 41)(32, \"label\");\n    i0.ɵɵtext(33, \"Sort by:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"select\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchComponent_div_11_div_3_Template_select_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.sortBy, $event) || (ctx_r2.sortBy = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function SearchComponent_div_11_div_3_Template_select_change_34_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyFilters());\n    });\n    i0.ɵɵelementStart(35, \"option\", 50);\n    i0.ɵɵtext(36, \"Relevance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"option\", 51);\n    i0.ɵɵtext(38, \"Price: Low to High\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"option\", 52);\n    i0.ɵɵtext(40, \"Price: High to Low\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"option\", 53);\n    i0.ɵɵtext(42, \"Newest First\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"option\", 54);\n    i0.ɵɵtext(44, \"Highest Rated\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(45, SearchComponent_div_11_div_3_app_no_data_45_Template, 1, 9, \"app-no-data\", 55)(46, SearchComponent_div_11_div_3_div_46_Template, 2, 1, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.totalCount || ctx_r2.searchResults.length, \" products found\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchSuggestions.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedCategory);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.categories);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedPriceRange);\n    i0.ɵɵadvance(16);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.sortBy);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchResults.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchResults.length > 0);\n  }\n}\nfunction SearchComponent_div_11_div_4_div_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_4_div_1_span_4_Template_span_click_0_listener() {\n      const search_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.searchFor(search_r16));\n    });\n    i0.ɵɵelement(1, \"i\", 96);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"i\", 97);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_4_div_1_span_4_Template_i_click_3_listener($event) {\n      const search_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      ctx_r2.removeRecentSearch(search_r16);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const search_r16 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", search_r16, \" \");\n  }\n}\nfunction SearchComponent_div_11_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"h3\");\n    i0.ɵɵtext(2, \"Recent Searches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 90);\n    i0.ɵɵtemplate(4, SearchComponent_div_11_div_4_div_1_span_4_Template, 4, 1, \"span\", 94);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.recentSearches);\n  }\n}\nfunction SearchComponent_div_11_div_4_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 98);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_4_span_6_Template_span_click_0_listener() {\n      const search_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.searchFor(search_r18));\n    });\n    i0.ɵɵelement(1, \"i\", 99);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const search_r18 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", search_r18, \" \");\n  }\n}\nfunction SearchComponent_div_11_div_4_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵlistener(\"click\", function SearchComponent_div_11_div_4_div_11_Template_div_click_0_listener() {\n      const category_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.browseCategory(category_r20.name));\n    });\n    i0.ɵɵelementStart(1, \"div\", 24);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r20 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(category_r20.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r20.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r20.count, \" items\");\n  }\n}\nfunction SearchComponent_div_11_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵtemplate(1, SearchComponent_div_11_div_4_div_1_Template, 5, 1, \"div\", 88);\n    i0.ɵɵelementStart(2, \"div\", 89)(3, \"h3\");\n    i0.ɵɵtext(4, \"Popular Searches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 90);\n    i0.ɵɵtemplate(6, SearchComponent_div_11_div_4_span_6_Template, 3, 1, \"span\", 91);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 89)(8, \"h3\");\n    i0.ɵɵtext(9, \"Browse Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 92);\n    i0.ɵɵtemplate(11, SearchComponent_div_11_div_4_div_11_Template, 7, 4, \"div\", 93);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.recentSearches.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.popularSearches);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.categoryList);\n  }\n}\nfunction SearchComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 3);\n    i0.ɵɵtemplate(2, SearchComponent_div_11_div_2_Template, 4, 0, \"div\", 31)(3, SearchComponent_div_11_div_3_Template, 47, 8, \"div\", 32)(4, SearchComponent_div_11_div_4_Template, 12, 3, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasSearched && !ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.hasSearched && !ctx_r2.isLoading);\n  }\n}\nexport let SearchComponent = /*#__PURE__*/(() => {\n  class SearchComponent {\n    constructor(route, router, productService, noDataConfigService, advancedSearchService) {\n      this.route = route;\n      this.router = router;\n      this.productService = productService;\n      this.noDataConfigService = noDataConfigService;\n      this.advancedSearchService = advancedSearchService;\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.recentSearches = [];\n      this.isLoading = false;\n      this.hasSearched = false;\n      // Enhanced search features\n      this.showCategorySelection = false;\n      this.categoryOptions = [];\n      this.searchResult = null;\n      this.searchSuggestions = [];\n      this.totalCount = 0;\n      // Filters\n      this.selectedCategory = '';\n      this.selectedPriceRange = '';\n      this.sortBy = 'relevance';\n      this.categories = ['Men', 'Women', 'Kids', 'Accessories', 'Footwear', 'Electronics'];\n      // Data\n      this.popularSearches = ['Dresses', 'Jeans', 'T-shirts', 'Sneakers', 'Jackets', 'Accessories', 'Formal Wear', 'Casual Wear'];\n      this.categoryList = [{\n        name: 'Men',\n        icon: 'fas fa-male',\n        count: 1250\n      }, {\n        name: 'Women',\n        icon: 'fas fa-female',\n        count: 1890\n      }, {\n        name: 'Kids',\n        icon: 'fas fa-child',\n        count: 650\n      }, {\n        name: 'Accessories',\n        icon: 'fas fa-gem',\n        count: 890\n      }, {\n        name: 'Footwear',\n        icon: 'fas fa-shoe-prints',\n        count: 750\n      }, {\n        name: 'Electronics',\n        icon: 'fas fa-mobile-alt',\n        count: 450\n      }];\n      // Category mapping for intelligent search\n      this.categoryKeywords = {\n        'Men': ['men', 'mens', 'man', 'male', 'boy', 'boys', 'gentleman', 'kurta', 'kurtas'],\n        'Women': ['women', 'womens', 'woman', 'female', 'girl', 'girls', 'lady', 'ladies', 'saree', 'sarees', 'kurti', 'kurtis'],\n        'Kids': ['kids', 'children', 'child', 'baby', 'toddler', 'infant'],\n        'Accessories': ['accessories', 'jewelry', 'jewellery', 'watch', 'watches', 'belt', 'belts'],\n        'Footwear': ['shoes', 'footwear', 'sandals', 'boots', 'sneakers', 'heels'],\n        'Electronics': ['mobile', 'phone', 'laptop', 'tablet', 'headphones', 'earphones']\n      };\n      this.wishlistItems = [];\n      this.noDataConfig = {};\n      this.searchSubject = new Subject();\n      this.noDataConfig = this.noDataConfigService.getSearchConfig();\n    }\n    ngOnInit() {\n      this.loadRecentSearches();\n      this.loadWishlistItems();\n      // Check for query parameter\n      this.route.queryParams.subscribe(params => {\n        if (params['q']) {\n          this.searchQuery = params['q'];\n          this.performSearch();\n        }\n      });\n      // Setup search with debounce using AdvancedSearchService\n      this.searchSubject.pipe(debounceTime(300), distinctUntilChanged(), switchMap(query => {\n        if (query.trim().length > 0) {\n          this.isLoading = true;\n          const searchQuery = {\n            query: query.trim(),\n            category: this.selectedCategory || undefined,\n            priceRange: this.getPriceRange().min || this.getPriceRange().max ? {\n              min: this.getPriceRange().min || 0,\n              max: this.getPriceRange().max || 999999\n            } : undefined,\n            sortBy: this.getSortBy(),\n            filters: this.getActiveFilters()\n          };\n          return this.advancedSearchService.search(searchQuery);\n        } else {\n          this.searchResults = [];\n          this.hasSearched = false;\n          this.isLoading = false;\n          this.showCategorySelection = false;\n          return [];\n        }\n      })).subscribe({\n        next: result => {\n          console.log('✅ Advanced search response received:', result);\n          // Handle category redirect for ambiguous queries\n          if (result.categoryRedirect?.shouldRedirect) {\n            this.showCategorySelection = true;\n            this.categoryOptions = result.categoryRedirect.categories;\n            this.searchResults = [];\n            this.hasSearched = false;\n          } else {\n            // Normal search results\n            this.showCategorySelection = false;\n            this.searchResults = result.products || [];\n            this.totalCount = result.totalCount || 0;\n            this.searchSuggestions = result.suggestions || [];\n            this.hasSearched = true;\n            console.log('✅ Search completed:', this.searchResults.length, 'results');\n          }\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('❌ Advanced search error:', error);\n          this.isLoading = false;\n          this.hasSearched = true;\n          this.searchResults = [];\n          this.showCategorySelection = false;\n          this.showErrorMessage('Search failed. Please try again.');\n        }\n      });\n    }\n    onSearchInput(event) {\n      this.searchQuery = event.target.value;\n      if (this.searchQuery.trim().length > 0) {\n        this.searchSubject.next(this.searchQuery);\n      } else {\n        this.searchResults = [];\n        this.hasSearched = false;\n        this.showCategorySelection = false;\n        this.categoryOptions = [];\n      }\n    }\n    // Method removed - now handled by AdvancedSearchService\n    onSearchSubmit() {\n      if (this.searchQuery.trim()) {\n        this.saveRecentSearch(this.searchQuery.trim());\n        this.performSearch();\n      }\n    }\n    performSearch() {\n      if (this.searchQuery.trim()) {\n        this.searchSubject.next(this.searchQuery);\n      }\n    }\n    clearSearch() {\n      this.searchQuery = '';\n      this.searchResults = [];\n      this.hasSearched = false;\n      this.showCategorySelection = false;\n      this.categoryOptions = [];\n    }\n    // Category selection event handlers\n    onCategorySelected(category) {\n      console.log('Category selected:', category);\n      this.advancedSearchService.saveSearchToHistory(this.searchQuery);\n      // Navigation will be handled by the category selection component\n    }\n    onSearchAllRequested(query) {\n      console.log('Search all categories requested for:', query);\n      this.showCategorySelection = false;\n      this.performDirectSearch();\n    }\n    onRefineSearchRequested(query) {\n      console.log('Refine search requested for:', query);\n      this.showCategorySelection = false;\n      // Focus on search input for refinement\n      setTimeout(() => {\n        const searchInput = document.querySelector('.search-input');\n        if (searchInput) {\n          searchInput.focus();\n        }\n      }, 100);\n    }\n    performDirectSearch() {\n      if (this.searchQuery.trim()) {\n        const searchQuery = {\n          query: this.searchQuery.trim(),\n          category: this.selectedCategory || undefined,\n          sortBy: this.getSortBy(),\n          filters: this.getActiveFilters()\n        };\n        this.isLoading = true;\n        this.advancedSearchService.search(searchQuery).subscribe({\n          next: result => {\n            this.searchResults = result.products || [];\n            this.totalCount = result.totalCount || 0;\n            this.searchSuggestions = result.suggestions || [];\n            this.hasSearched = true;\n            this.isLoading = false;\n          },\n          error: error => {\n            console.error('Direct search error:', error);\n            this.isLoading = false;\n            this.hasSearched = true;\n            this.searchResults = [];\n          }\n        });\n      }\n    }\n    searchFor(term) {\n      this.searchQuery = term;\n      this.saveRecentSearch(term);\n      this.performSearch();\n    }\n    applyFilters() {\n      if (this.searchQuery) {\n        this.performSearch();\n      }\n    }\n    browseCategory(category) {\n      this.selectedCategory = category;\n      this.searchQuery = category;\n      this.performSearch();\n    }\n    selectCategoryForSearch(category) {\n      // Intelligent redirection based on category selection\n      this.selectedCategory = category.name;\n      this.showCategorySelection = false;\n      // Update search query to include category filter\n      const originalQuery = this.searchQuery;\n      this.searchQuery = `${originalQuery} in ${category.name}`;\n      // Navigate to filtered category page\n      this.router.navigate(['/shop'], {\n        queryParams: {\n          q: originalQuery,\n          category: category.name.toLowerCase(),\n          intent: 'category'\n        }\n      });\n    }\n    dismissCategorySelection() {\n      this.showCategorySelection = false;\n      this.categoryOptions = [];\n    }\n    searchInAllCategories() {\n      this.showCategorySelection = false;\n      this.selectedCategory = '';\n      this.performSearch();\n    }\n    viewProduct(productId) {\n      this.router.navigate(['/shop/product', productId]);\n    }\n    toggleWishlist(productId) {\n      if (this.isInWishlist(productId)) {\n        this.wishlistItems = this.wishlistItems.filter(id => id !== productId);\n      } else {\n        this.wishlistItems.push(productId);\n      }\n      localStorage.setItem('wishlist', JSON.stringify(this.wishlistItems));\n    }\n    isInWishlist(productId) {\n      return this.wishlistItems.includes(productId);\n    }\n    addToCart(productId) {\n      // Add to cart logic\n      console.log('Added to cart:', productId);\n      this.showNotification('Added to cart!', 'success');\n    }\n    buyNow(productId) {\n      // Buy now logic\n      console.log('Buy now:', productId);\n      this.showNotification('Redirecting to checkout...', 'info');\n    }\n    getDiscountPercentage(product) {\n      if (product.discountPrice) {\n        return Math.round((product.price - product.discountPrice) / product.price * 100);\n      }\n      return 0;\n    }\n    removeRecentSearch(term) {\n      this.recentSearches = this.recentSearches.filter(search => search !== term);\n      localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));\n    }\n    loadRecentSearches() {\n      const saved = localStorage.getItem('recentSearches');\n      this.recentSearches = saved ? JSON.parse(saved) : [];\n    }\n    loadWishlistItems() {\n      const saved = localStorage.getItem('wishlist');\n      this.wishlistItems = saved ? JSON.parse(saved) : [];\n    }\n    saveRecentSearch(term) {\n      this.recentSearches = this.recentSearches.filter(search => search !== term);\n      this.recentSearches.unshift(term);\n      this.recentSearches = this.recentSearches.slice(0, 10);\n      localStorage.setItem('recentSearches', JSON.stringify(this.recentSearches));\n    }\n    showErrorMessage(message) {\n      // Create error notification\n      const notification = document.createElement('div');\n      notification.className = 'search-error-notification';\n      notification.textContent = message;\n      notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: #f44336;\n      color: white;\n      padding: 12px 20px;\n      border-radius: 6px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 14px;\n      animation: slideIn 0.3s ease;\n    `;\n      document.body.appendChild(notification);\n      setTimeout(() => {\n        notification.remove();\n      }, 3000);\n    }\n    getSortBy() {\n      return this.sortBy;\n    }\n    getActiveFilters() {\n      const filters = {};\n      // Add any active filters here\n      if (this.selectedCategory) {\n        filters.category = [this.selectedCategory];\n      }\n      return filters;\n    }\n    getPriceRange() {\n      if (!this.selectedPriceRange) return {\n        min: undefined,\n        max: undefined\n      };\n      const ranges = {\n        '0-1000': {\n          min: 0,\n          max: 1000\n        },\n        '1000-2500': {\n          min: 1000,\n          max: 2500\n        },\n        '2500-5000': {\n          min: 2500,\n          max: 5000\n        },\n        '5000-10000': {\n          min: 5000,\n          max: 10000\n        },\n        '10000+': {\n          min: 10000\n        }\n      };\n      return ranges[this.selectedPriceRange] || {\n        min: undefined,\n        max: undefined\n      };\n    }\n    getSortField() {\n      const sortMap = {\n        'relevance': 'createdAt',\n        'price-low': 'price',\n        'price-high': 'price',\n        'newest': 'createdAt',\n        'rating': 'rating'\n      };\n      return sortMap[this.sortBy] || 'createdAt';\n    }\n    getSortOrder() {\n      return this.sortBy === 'price-high' ? 'desc' : 'asc';\n    }\n    showNotification(message, type) {\n      const notification = document.createElement('div');\n      notification.className = `notification notification-${type}`;\n      notification.textContent = message;\n      notification.style.cssText = `\n      position: fixed;\n      top: 80px;\n      right: 20px;\n      background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};\n      color: white;\n      padding: 1rem 1.5rem;\n      border-radius: 8px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      z-index: 10000;\n      font-size: 0.9rem;\n      font-weight: 500;\n      animation: slideIn 0.3s ease;\n    `;\n      document.body.appendChild(notification);\n      setTimeout(() => notification.remove(), 3000);\n    }\n    static {\n      this.ɵfac = function SearchComponent_Factory(t) {\n        return new (t || SearchComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.NoDataConfigService), i0.ɵɵdirectiveInject(i4.AdvancedSearchService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SearchComponent,\n        selectors: [[\"app-search\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 12,\n        vars: 5,\n        consts: [[\"searchInput\", \"\"], [1, \"search-page\"], [1, \"search-header\"], [1, \"container\"], [1, \"search-bar-container\"], [1, \"search-input-wrapper\"], [1, \"fas\", \"fa-search\", \"search-icon\"], [\"type\", \"text\", \"placeholder\", \"Search for fashion, brands, and more...\", 1, \"search-input\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [\"class\", \"clear-btn\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"category-selection-overlay\", 4, \"ngIf\"], [\"class\", \"category-selection-section\", 4, \"ngIf\"], [\"class\", \"search-content\", 4, \"ngIf\"], [1, \"clear-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"category-selection-overlay\"], [1, \"category-selection-modal\"], [1, \"modal-header\"], [1, \"close-btn\", 3, \"click\"], [1, \"modal-content\"], [1, \"category-options\"], [\"class\", \"category-option\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"modal-actions\"], [1, \"btn-secondary\", 3, \"click\"], [1, \"category-option\", 3, \"click\"], [1, \"category-icon\"], [1, \"fas\", \"fa-tags\"], [1, \"category-details\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"category-selection-section\"], [3, \"categorySelected\", \"searchAllRequested\", \"refineSearchRequested\", \"categories\", \"originalQuery\"], [1, \"search-content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"search-results\", 4, \"ngIf\"], [\"class\", \"default-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"search-results\"], [1, \"results-header\"], [1, \"results-count\"], [\"class\", \"search-suggestions\", 4, \"ngIf\"], [1, \"filters-section\"], [1, \"filter-group\"], [3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"0-1000\"], [\"value\", \"1000-2500\"], [\"value\", \"2500-5000\"], [\"value\", \"5000-10000\"], [\"value\", \"10000+\"], [\"value\", \"relevance\"], [\"value\", \"price-low\"], [\"value\", \"price-high\"], [\"value\", \"newest\"], [\"value\", \"rating\"], [3, \"title\", \"message\", \"iconClass\", \"containerClass\", \"showActions\", \"primaryAction\", \"secondaryAction\", \"suggestions\", \"suggestionsTitle\", 4, \"ngIf\"], [\"class\", \"results-grid\", 4, \"ngIf\"], [1, \"search-suggestions\"], [\"class\", \"suggestion-chip\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"suggestion-chip\", 3, \"click\"], [3, \"value\"], [3, \"title\", \"message\", \"iconClass\", \"containerClass\", \"showActions\", \"primaryAction\", \"secondaryAction\", \"suggestions\", \"suggestionsTitle\"], [1, \"results-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image\"], [3, \"src\", \"alt\"], [\"class\", \"product-badge\", 4, \"ngIf\"], [1, \"product-actions\"], [1, \"action-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"product-info\"], [1, \"brand\"], [1, \"price-container\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [\"class\", \"rating\", 4, \"ngIf\"], [1, \"product-buttons\"], [1, \"btn-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"btn-buy\", 3, \"click\"], [1, \"product-badge\"], [1, \"original-price\"], [1, \"rating\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 3, \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-count\"], [1, \"fas\", \"fa-star\"], [1, \"default-content\"], [\"class\", \"section\", 4, \"ngIf\"], [1, \"section\"], [1, \"search-chips\"], [\"class\", \"search-chip popular\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"categories-grid\"], [\"class\", \"category-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"search-chip\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"search-chip\", 3, \"click\"], [1, \"fas\", \"fa-clock\"], [1, \"fas\", \"fa-times\", 3, \"click\"], [1, \"search-chip\", \"popular\", 3, \"click\"], [1, \"fas\", \"fa-fire\"], [1, \"category-card\", 3, \"click\"]],\n        template: function SearchComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"div\", 5);\n            i0.ɵɵelement(5, \"i\", 6);\n            i0.ɵɵelementStart(6, \"input\", 7, 0);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchComponent_Template_input_ngModelChange_6_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"input\", function SearchComponent_Template_input_input_6_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearchInput($event));\n            })(\"keyup.enter\", function SearchComponent_Template_input_keyup_enter_6_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearchSubmit());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(8, SearchComponent_button_8_Template, 2, 0, \"button\", 8);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(9, SearchComponent_div_9_Template, 15, 2, \"div\", 9)(10, SearchComponent_div_10_Template, 2, 2, \"div\", 10)(11, SearchComponent_div_11_Template, 5, 3, \"div\", 11);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.searchQuery);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showCategorySelection);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showCategorySelection && ctx.categoryOptions.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.showCategorySelection);\n          }\n        },\n        dependencies: [CommonModule, i5.NgForOf, i5.NgIf, i5.DecimalPipe, FormsModule, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i6.DefaultValueAccessor, i6.SelectControlValueAccessor, i6.NgControlStatus, i6.NgModel, NoDataComponent, CategorySelectionComponent],\n        styles: [\".search-page[_ngcontent-%COMP%]{min-height:calc(100vh - 60px);background:#f8f9fa}.search-header[_ngcontent-%COMP%]{background:#fff;border-bottom:1px solid #e9ecef;padding:2rem 0;position:sticky;top:60px;z-index:100}.search-bar-container[_ngcontent-%COMP%]{max-width:600px;margin:0 auto}.search-input-wrapper[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center}.search-icon[_ngcontent-%COMP%]{position:absolute;left:1rem;color:#6c757d;z-index:1}.search-input[_ngcontent-%COMP%]{width:100%;padding:1rem 1rem 1rem 3rem;border:2px solid #e9ecef;border-radius:50px;font-size:1rem;outline:none;transition:all .3s ease}.search-input[_ngcontent-%COMP%]:focus{border-color:#007bff;box-shadow:0 0 0 .2rem #007bff40}.clear-btn[_ngcontent-%COMP%]{position:absolute;right:1rem;background:none;border:none;color:#6c757d;cursor:pointer;padding:.5rem}.category-selection-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#00000080;z-index:1000;display:flex;align-items:center;justify-content:center;animation:_ngcontent-%COMP%_fadeIn .3s ease}.category-selection-modal[_ngcontent-%COMP%]{background:#fff;border-radius:12px;max-width:500px;width:90%;max-height:80vh;overflow-y:auto;box-shadow:0 20px 60px #0000004d;animation:_ngcontent-%COMP%_slideUp .3s ease}.modal-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1.5rem;border-bottom:1px solid #e9ecef}.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:1.25rem;font-weight:600;color:#212529}.close-btn[_ngcontent-%COMP%]{background:none;border:none;font-size:1.25rem;color:#6c757d;cursor:pointer;padding:.5rem;border-radius:50%;transition:all .2s ease}.close-btn[_ngcontent-%COMP%]:hover{background:#f8f9fa;color:#495057}.modal-content[_ngcontent-%COMP%]{padding:1.5rem}.modal-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;margin-bottom:1.5rem;font-size:.95rem}.category-options[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.75rem;margin-bottom:1.5rem}.category-option[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;padding:1rem;border:2px solid #e9ecef;border-radius:8px;cursor:pointer;transition:all .2s ease}.category-option[_ngcontent-%COMP%]:hover{border-color:#007bff;background:#f8f9ff}.category-option[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]{width:48px;height:48px;background:linear-gradient(135deg,#007bff,#0056b3);border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff;font-size:1.25rem}.category-details[_ngcontent-%COMP%]{flex:1}.category-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 .25rem;font-size:1rem;font-weight:600;color:#212529}.category-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.85rem;color:#6c757d}.category-option[_ngcontent-%COMP%]   .fa-arrow-right[_ngcontent-%COMP%]{color:#6c757d;font-size:.9rem}.modal-actions[_ngcontent-%COMP%]{text-align:center;padding-top:1rem;border-top:1px solid #e9ecef}.btn-secondary[_ngcontent-%COMP%]{background:#f8f9fa;color:#6c757d;border:1px solid #dee2e6;padding:.75rem 1.5rem;border-radius:6px;cursor:pointer;font-size:.9rem;font-weight:500;transition:all .2s ease}.btn-secondary[_ngcontent-%COMP%]:hover{background:#e9ecef;color:#495057}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}@keyframes _ngcontent-%COMP%_slideUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}.search-content[_ngcontent-%COMP%]{padding:2rem 0}.container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:0 1rem}.loading-container[_ngcontent-%COMP%]{text-align:center;padding:4rem 0}.loading-spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid #007bff;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin:0 auto 1rem}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.results-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:2rem}.results-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:1.5rem;font-weight:600}.results-count[_ngcontent-%COMP%]{color:#6c757d;font-size:.9rem}.filters-section[_ngcontent-%COMP%]{display:flex;gap:2rem;margin-bottom:2rem;padding:1rem;background:#fff;border-radius:8px;box-shadow:0 2px 4px #0000001a}.filter-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem}.filter-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:500;font-size:.9rem;color:#495057}.filter-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{padding:.5rem;border:1px solid #ced4da;border-radius:4px;font-size:.9rem}.no-results[_ngcontent-%COMP%]{text-align:center;padding:4rem 2rem;background:#fff;border-radius:8px}.no-results[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;color:#dee2e6;margin-bottom:1rem}.no-results[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:1.5rem;color:#495057}.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;margin-bottom:2rem}.suggested-searches[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 1rem;font-size:1rem;color:#495057}.search-tags[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem;justify-content:center}.search-tag[_ngcontent-%COMP%]{background:#e9ecef;color:#495057;padding:.5rem 1rem;border-radius:20px;cursor:pointer;font-size:.9rem;transition:all .2s ease}.search-tag[_ngcontent-%COMP%]:hover{background:#007bff;color:#fff}.results-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:1.5rem}.product-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;overflow:hidden;box-shadow:0 2px 8px #0000001a;transition:all .3s ease;cursor:pointer}.product-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 24px #00000026}.product-image[_ngcontent-%COMP%]{position:relative;width:100%;height:200px;overflow:hidden}.product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.product-card[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transform:scale(1.05)}.product-badge[_ngcontent-%COMP%]{position:absolute;top:.5rem;left:.5rem;background:#dc3545;color:#fff;padding:.25rem .5rem;border-radius:4px;font-size:.75rem;font-weight:600}.product-actions[_ngcontent-%COMP%]{position:absolute;top:.5rem;right:.5rem}.action-btn[_ngcontent-%COMP%]{background:#ffffffe6;border:none;width:36px;height:36px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease;color:#6c757d}.action-btn[_ngcontent-%COMP%]:hover{background:#fff;color:#dc3545}.product-info[_ngcontent-%COMP%]{padding:1rem}.product-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:1rem;font-weight:600;color:#212529;line-height:1.3}.brand[_ngcontent-%COMP%]{color:#6c757d;font-size:.85rem;margin:0 0 .5rem;text-transform:uppercase;letter-spacing:.5px}.price-container[_ngcontent-%COMP%]{margin-bottom:.5rem}.current-price[_ngcontent-%COMP%]{font-size:1.125rem;font-weight:700;color:#28a745;margin-right:.5rem}.original-price[_ngcontent-%COMP%]{font-size:.9rem;color:#6c757d;text-decoration:line-through}.rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:1rem}.stars[_ngcontent-%COMP%]{display:flex;gap:.125rem}.stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem;color:#dee2e6}.stars[_ngcontent-%COMP%]   i.filled[_ngcontent-%COMP%]{color:#ffc107}.rating-count[_ngcontent-%COMP%]{font-size:.8rem;color:#6c757d}.product-buttons[_ngcontent-%COMP%]{display:flex;gap:.5rem}.btn-cart[_ngcontent-%COMP%], .btn-buy[_ngcontent-%COMP%]{flex:1;padding:.5rem;border:none;border-radius:6px;font-size:.85rem;font-weight:600;cursor:pointer;transition:all .2s ease}.btn-cart[_ngcontent-%COMP%]{background:#f8f9fa;color:#495057;border:1px solid #dee2e6}.btn-cart[_ngcontent-%COMP%]:hover{background:#e9ecef}.btn-buy[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-buy[_ngcontent-%COMP%]:hover{background:#0056b3}.default-content[_ngcontent-%COMP%]{max-width:800px;margin:0 auto}.section[_ngcontent-%COMP%]{margin-bottom:3rem}.section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 1rem;font-size:1.25rem;font-weight:600;color:#212529}.search-chips[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem}.search-chip[_ngcontent-%COMP%]{background:#fff;border:1px solid #dee2e6;color:#495057;padding:.5rem 1rem;border-radius:20px;cursor:pointer;font-size:.9rem;transition:all .2s ease;display:flex;align-items:center;gap:.5rem}.search-chip[_ngcontent-%COMP%]:hover{border-color:#007bff;color:#007bff}.search-chip.popular[_ngcontent-%COMP%]{background:linear-gradient(45deg,#ff6b6b,#feca57);color:#fff;border:none}.categories-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1rem}.category-card[_ngcontent-%COMP%]{background:#fff;padding:2rem 1rem;border-radius:12px;text-align:center;cursor:pointer;transition:all .3s ease;box-shadow:0 2px 8px #0000001a}.category-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 24px #00000026}.category-icon[_ngcontent-%COMP%]{width:60px;height:60px;background:linear-gradient(45deg,#007bff,#0056b3);border-radius:50%;display:flex;align-items:center;justify-content:center;margin:0 auto 1rem}.category-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.5rem;color:#fff}.category-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:1.125rem;font-weight:600;color:#212529}.category-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#6c757d;font-size:.9rem}@media (max-width: 768px){.filters-section[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}.results-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(250px,1fr));gap:1rem}.categories-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(150px,1fr))}.search-header[_ngcontent-%COMP%]{padding:1rem 0}}\"]\n      });\n    }\n  }\n  return SearchComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}