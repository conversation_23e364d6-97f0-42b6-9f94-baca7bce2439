{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/story.service\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../../../core/services/media.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"storiesContainer\"];\nfunction StoriesComponent_div_2_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_div_2_button_1_Template_button_click_0_listener() {\n      const i_r3 = i0.ɵɵrestoreView(_r2).index;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.goToSlide(i_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r3 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r3 === ctx_r3.currentSlide);\n  }\n}\nfunction StoriesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, StoriesComponent_div_2_button_1_Template, 1, 2, \"button\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.dots);\n  }\n}\nfunction StoriesComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_div_12_Template_div_click_0_listener() {\n      const storyGroup_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.openStoryViewer(storyGroup_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 7)(2, \"div\", 18)(3, \"img\", 19);\n    i0.ɵɵlistener(\"error\", function StoriesComponent_div_12_Template_img_error_3_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onImageError($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(4, \"span\", 10);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const storyGroup_r6 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"has-story\", storyGroup_r6.stories.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r3.getSafeImageUrl(storyGroup_r6.user.avatar), i0.ɵɵsanitizeUrl)(\"alt\", storyGroup_r6.user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(storyGroup_r6.user.username);\n  }\n}\nfunction StoriesComponent_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.slideLeft());\n    });\n    i0.ɵɵelement(1, \"i\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.canSlideLeft);\n  }\n}\nfunction StoriesComponent_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.slideRight());\n    });\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.canSlideRight);\n  }\n}\nexport class StoriesComponent {\n  constructor(storyService, authService, router, mediaService) {\n    this.storyService = storyService;\n    this.authService = authService;\n    this.router = router;\n    this.mediaService = mediaService;\n    this.storyGroups = [];\n    this.currentUser = null;\n    // Slider navigation properties\n    this.canScrollLeft = false;\n    this.canScrollRight = false;\n    this.showNavButtons = false;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    this.loadStories();\n  }\n  ngAfterViewInit() {\n    // Check if navigation buttons are needed after view init\n    setTimeout(() => {\n      this.updateNavigationButtons();\n    }, 100);\n    // Add resize listener to update navigation buttons\n    window.addEventListener('resize', () => {\n      this.updateNavigationButtons();\n    });\n  }\n  loadStories() {\n    this.storyService.getStories().subscribe({\n      next: response => {\n        this.storyGroups = response.storyGroups;\n        // Update navigation buttons after stories load\n        setTimeout(() => {\n          this.updateNavigationButtons();\n        }, 100);\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.storyGroups = [];\n      }\n    });\n  }\n  // Slider navigation methods\n  scrollLeft() {\n    if (this.storiesContainer) {\n      const container = this.storiesContainer.nativeElement;\n      const scrollAmount = container.clientWidth * 0.8; // Scroll 80% of visible width\n      container.scrollBy({\n        left: -scrollAmount,\n        behavior: 'smooth'\n      });\n    }\n  }\n  scrollRight() {\n    if (this.storiesContainer) {\n      const container = this.storiesContainer.nativeElement;\n      const scrollAmount = container.clientWidth * 0.8; // Scroll 80% of visible width\n      container.scrollBy({\n        left: scrollAmount,\n        behavior: 'smooth'\n      });\n    }\n  }\n  onScroll() {\n    this.updateNavigationButtons();\n  }\n  updateNavigationButtons() {\n    if (this.storiesContainer) {\n      const container = this.storiesContainer.nativeElement;\n      const {\n        scrollLeft,\n        scrollWidth,\n        clientWidth\n      } = container;\n      // Check if content overflows and navigation is needed\n      this.showNavButtons = scrollWidth > clientWidth;\n      // Update button states\n      this.canScrollLeft = scrollLeft > 0;\n      this.canScrollRight = scrollLeft < scrollWidth - clientWidth - 1;\n    }\n  }\n  // Image handling methods\n  getSafeImageUrl(url) {\n    return this.mediaService.getSafeImageUrl(url, 'user');\n  }\n  onImageError(event) {\n    this.mediaService.handleImageError(event, 'user');\n  }\n  openAddStory() {\n    this.showStoryCreationModal();\n  }\n  showStoryCreationModal() {\n    // Create modal overlay\n    const modalOverlay = document.createElement('div');\n    modalOverlay.className = 'story-creation-modal';\n    modalOverlay.innerHTML = `\n      <div class=\"modal-content\">\n        <div class=\"modal-header\">\n          <h3>Create Story</h3>\n          <button class=\"close-btn\" onclick=\"this.closest('.story-creation-modal').remove()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        <div class=\"modal-body\">\n          <div class=\"creation-options\">\n            <div class=\"option-card camera-option\" onclick=\"document.getElementById('camera-input').click()\">\n              <div class=\"option-icon camera-icon\">\n                <i class=\"fas fa-camera\"></i>\n              </div>\n              <h4>Camera</h4>\n              <p>Take a photo or video</p>\n            </div>\n            <div class=\"option-card gallery-option\" onclick=\"document.getElementById('gallery-input').click()\">\n              <div class=\"option-icon gallery-icon\">\n                <i class=\"fas fa-images\"></i>\n              </div>\n              <h4>Gallery</h4>\n              <p>Choose from your photos</p>\n            </div>\n            <div class=\"option-card create-option\" onclick=\"this.closest('.story-creation-modal').querySelector('.text-story-creator').style.display='block'\">\n              <div class=\"option-icon create-icon\">\n                <i class=\"fas fa-font\"></i>\n              </div>\n              <h4>Create</h4>\n              <p>Design with text and colors</p>\n            </div>\n            <div class=\"option-card live-option\" onclick=\"alert('Live feature coming soon!')\">\n              <div class=\"option-icon live-icon\">\n                <i class=\"fas fa-video\"></i>\n              </div>\n              <h4>Live</h4>\n              <p>Go live with your audience</p>\n            </div>\n          </div>\n\n          <!-- Text Story Creator -->\n          <div class=\"text-story-creator\" style=\"display: none;\">\n            <div class=\"text-editor\">\n              <textarea placeholder=\"What's on your mind?\" maxlength=\"500\"></textarea>\n              <div class=\"color-picker\">\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff6b6b, #feca57)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff6b6b, #feca57)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #48dbfb, #0abde3)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #48dbfb, #0abde3)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff9ff3, #f368e0)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff9ff3, #f368e0)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #54a0ff, #2e86de)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #54a0ff, #2e86de)'\"></div>\n              </div>\n              <button class=\"create-text-story-btn\" onclick=\"alert('Text story created! (Demo)')\">Create Story</button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Hidden file inputs -->\n        <input type=\"file\" id=\"camera-input\" accept=\"image/*,video/*\" capture=\"environment\" style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n        <input type=\"file\" id=\"gallery-input\" accept=\"image/*,video/*\" multiple style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n      </div>\n    `;\n    // Add styles\n    const styles = document.createElement('style');\n    styles.textContent = `\n      .story-creation-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.8);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 10000;\n        animation: fadeIn 0.3s ease;\n      }\n\n      .modal-content {\n        background: white;\n        border-radius: 16px;\n        width: 90%;\n        max-width: 400px;\n        max-height: 80vh;\n        overflow-y: auto;\n      }\n\n      .modal-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem 1.5rem;\n        border-bottom: 1px solid #eee;\n      }\n\n      .modal-header h3 {\n        margin: 0;\n        font-size: 1.25rem;\n        font-weight: 600;\n      }\n\n      .close-btn {\n        background: none;\n        border: none;\n        font-size: 1.25rem;\n        cursor: pointer;\n        padding: 0.5rem;\n        border-radius: 50%;\n        transition: background 0.2s;\n      }\n\n      .close-btn:hover {\n        background: #f0f0f0;\n      }\n\n      .creation-options {\n        display: grid;\n        grid-template-columns: 1fr;\n        gap: 1rem;\n        padding: 1.5rem;\n      }\n\n      .option-card {\n        display: flex;\n        align-items: center;\n        padding: 1.5rem;\n        border: none;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        position: relative;\n        overflow: hidden;\n      }\n\n      .option-card:hover {\n        transform: translateY(-3px) scale(1.02);\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n      }\n\n      .camera-option {\n        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n      }\n\n      .gallery-option {\n        background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);\n      }\n\n      .create-option {\n        background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\n      }\n\n      .live-option {\n        background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);\n      }\n\n      .option-icon {\n        width: 60px;\n        height: 60px;\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 1rem;\n        backdrop-filter: blur(10px);\n        border: 2px solid rgba(255, 255, 255, 0.3);\n      }\n\n      .option-icon i {\n        color: white;\n        font-size: 1.5rem;\n      }\n\n      .option-card h4 {\n        margin: 0 0 0.25rem 0;\n        font-size: 1.1rem;\n        font-weight: 700;\n        color: white;\n      }\n\n      .option-card p {\n        margin: 0;\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 0.875rem;\n      }\n\n      .text-story-creator {\n        padding: 1.5rem;\n        background: linear-gradient(45deg, #ff6b6b, #feca57);\n        border-radius: 12px;\n        margin: 1rem;\n      }\n\n      .text-editor textarea {\n        width: 100%;\n        min-height: 120px;\n        border: none;\n        background: rgba(255, 255, 255, 0.9);\n        border-radius: 8px;\n        padding: 1rem;\n        font-size: 1.125rem;\n        resize: none;\n        margin-bottom: 1rem;\n      }\n\n      .color-picker {\n        display: flex;\n        gap: 0.5rem;\n        margin-bottom: 1rem;\n      }\n\n      .color-option {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        cursor: pointer;\n        border: 3px solid white;\n        transition: transform 0.2s;\n      }\n\n      .color-option:hover {\n        transform: scale(1.1);\n      }\n\n      .create-text-story-btn {\n        background: white;\n        border: none;\n        padding: 0.75rem 1.5rem;\n        border-radius: 25px;\n        font-weight: 600;\n        cursor: pointer;\n        transition: transform 0.2s;\n      }\n\n      .create-text-story-btn:hover {\n        transform: scale(1.05);\n      }\n\n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n    `;\n    document.head.appendChild(styles);\n    document.body.appendChild(modalOverlay);\n    // Add global function for file handling\n    window.handleFileSelect = input => {\n      const files = input.files;\n      if (files && files.length > 0) {\n        const file = files[0];\n        console.log('Selected file:', file.name, file.type);\n        // Create preview modal\n        this.showFilePreview(file);\n        modalOverlay.remove();\n      }\n    };\n  }\n  showFilePreview(file) {\n    const previewModal = document.createElement('div');\n    previewModal.className = 'story-preview-modal';\n    const fileURL = URL.createObjectURL(file);\n    const isVideo = file.type.startsWith('video/');\n    previewModal.innerHTML = `\n      <div class=\"preview-content\">\n        <div class=\"preview-header\">\n          <button class=\"back-btn\" onclick=\"this.closest('.story-preview-modal').remove()\">\n            <i class=\"fas fa-arrow-left\"></i>\n          </button>\n          <h3>Create Story</h3>\n          <button class=\"share-btn\" onclick=\"alert('Story shared! (Demo)')\">Share</button>\n        </div>\n        <div class=\"preview-media\">\n          ${isVideo ? `<video src=\"${fileURL}\" controls autoplay muted></video>` : `<img src=\"${fileURL}\" alt=\"Story preview\">`}\n          <div class=\"story-tools\">\n            <button class=\"tool-btn\" onclick=\"alert('Add text feature coming soon!')\">\n              <i class=\"fas fa-font\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Add stickers feature coming soon!')\">\n              <i class=\"fas fa-smile\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Tag products feature coming soon!')\">\n              <i class=\"fas fa-tag\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n    `;\n    // Add preview styles\n    const previewStyles = document.createElement('style');\n    previewStyles.textContent = `\n      .story-preview-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: black;\n        z-index: 10001;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .preview-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem;\n        background: rgba(0, 0, 0, 0.5);\n        color: white;\n      }\n\n      .back-btn, .share-btn {\n        background: none;\n        border: none;\n        color: white;\n        font-size: 1rem;\n        cursor: pointer;\n        padding: 0.5rem;\n      }\n\n      .share-btn {\n        background: #007bff;\n        border-radius: 20px;\n        padding: 0.5rem 1rem;\n      }\n\n      .preview-media {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        position: relative;\n      }\n\n      .preview-media img,\n      .preview-media video {\n        max-width: 100%;\n        max-height: 100%;\n        object-fit: contain;\n      }\n\n      .story-tools {\n        position: absolute;\n        right: 1rem;\n        top: 50%;\n        transform: translateY(-50%);\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n      }\n\n      .tool-btn {\n        width: 48px;\n        height: 48px;\n        background: rgba(255, 255, 255, 0.2);\n        border: none;\n        border-radius: 50%;\n        color: white;\n        font-size: 1.25rem;\n        cursor: pointer;\n        backdrop-filter: blur(10px);\n        transition: background 0.2s;\n      }\n\n      .tool-btn:hover {\n        background: rgba(255, 255, 255, 0.3);\n      }\n    `;\n    document.head.appendChild(previewStyles);\n    document.body.appendChild(previewModal);\n  }\n  openStoryViewer(storyGroup) {\n    if (storyGroup.stories.length > 0) {\n      // Navigate to story viewer with user ID and start from first story\n      this.router.navigate(['/story', storyGroup.user._id, 0]);\n    } else {\n      console.log('No stories available for:', storyGroup.user.username);\n    }\n  }\n  static {\n    this.ɵfac = function StoriesComponent_Factory(t) {\n      return new (t || StoriesComponent)(i0.ɵɵdirectiveInject(i1.StoryService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MediaService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoriesComponent,\n      selectors: [[\"app-stories\"]],\n      viewQuery: function StoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 15,\n      vars: 8,\n      consts: [[\"storiesSlider\", \"\"], [1, \"stories-section\"], [1, \"stories-slider-wrapper\"], [\"class\", \"nav-dots\", 4, \"ngIf\"], [1, \"stories-slider\", 3, \"touchstart\", \"touchmove\", \"touchend\", \"scroll\"], [1, \"stories-track\"], [1, \"story-slide\", \"add-story\", 3, \"click\"], [1, \"story-avatar\"], [1, \"avatar-ring\", \"add-ring\"], [1, \"fas\", \"fa-plus\"], [1, \"story-username\"], [\"class\", \"story-slide\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"nav-arrow nav-left\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"nav-arrow nav-right\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"nav-dots\"], [\"class\", \"nav-dot\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-dot\", 3, \"click\"], [1, \"story-slide\", 3, \"click\"], [1, \"avatar-ring\"], [1, \"avatar-image\", 3, \"error\", \"src\", \"alt\"], [1, \"nav-arrow\", \"nav-left\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"nav-arrow\", \"nav-right\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function StoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n          i0.ɵɵtemplate(2, StoriesComponent_div_2_Template, 2, 1, \"div\", 3);\n          i0.ɵɵelementStart(3, \"div\", 4, 0);\n          i0.ɵɵlistener(\"touchstart\", function StoriesComponent_Template_div_touchstart_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchStart($event));\n          })(\"touchmove\", function StoriesComponent_Template_div_touchmove_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchMove($event));\n          })(\"touchend\", function StoriesComponent_Template_div_touchend_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchEnd($event));\n          })(\"scroll\", function StoriesComponent_Template_div_scroll_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onScroll());\n          });\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function StoriesComponent_Template_div_click_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openAddStory());\n          });\n          i0.ɵɵelementStart(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵelement(9, \"i\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"span\", 10);\n          i0.ɵɵtext(11, \"Your Story\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, StoriesComponent_div_12_Template, 6, 5, \"div\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, StoriesComponent_button_13_Template, 2, 1, \"button\", 12)(14, StoriesComponent_button_14_Template, 2, 1, \"button\", 13);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDots);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx.translateX + \"px)\")(\"transition\", ctx.isTransitioning ? \"transform 0.3s ease-out\" : \"none\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.storyGroups);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showArrows);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showArrows);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf],\n      styles: [\".stories-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  width: 100%;\\n  max-width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: #fff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  width: 100%;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.stories-track[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0;\\n  will-change: transform;\\n  padding: 16px 0;\\n}\\n\\n.story-slide[_ngcontent-%COMP%] {\\n  flex: 0 0 auto;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 0 12px;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n  width: 80px;\\n}\\n\\n.story-slide[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.story-slide[_ngcontent-%COMP%]:first-child {\\n  padding-left: 16px;\\n}\\n\\n.story-slide[_ngcontent-%COMP%]:last-child {\\n  padding-right: 16px;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  position: relative;\\n}\\n\\n.avatar-ring[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  padding: 3px;\\n  background: #fafafa;\\n  border: 1px solid #dbdbdb;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n\\n.avatar-ring.has-story[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n  border: none;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.avatar-ring.add-ring[_ngcontent-%COMP%] {\\n  background: #fafafa;\\n  border: 2px dashed #dbdbdb;\\n}\\n\\n.avatar-ring.add-ring[_ngcontent-%COMP%]:hover {\\n  border-color: var(--primary-color);\\n  background: rgba(0, 149, 246, 0.05);\\n}\\n\\n.avatar-image[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid #fff;\\n}\\n\\n.avatar-ring[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 20px;\\n}\\n\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 70px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  font-weight: 400;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n\\n\\n.nav-arrow[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 36px;\\n  height: 36px;\\n  border: none;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.95);\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);\\n  color: #262626;\\n  font-size: 16px;\\n  cursor: pointer;\\n  z-index: 10;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #fff;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n  transform: translateY(-50%);\\n}\\n\\n.nav-arrow.nav-left[_ngcontent-%COMP%] {\\n  left: 12px;\\n}\\n\\n.nav-arrow.nav-right[_ngcontent-%COMP%] {\\n  right: 12px;\\n}\\n\\n\\n\\n.nav-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 8px;\\n  padding: 12px 0 8px;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.nav-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  border: none;\\n  background: #dbdbdb;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.nav-dot.active[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  transform: scale(1.2);\\n}\\n\\n.nav-dot[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  opacity: 0.7;\\n}\\n\\n@media (max-width: 768px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    padding: 12px;\\n    gap: 12px;\\n  }\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .story-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 52px;\\n    height: 52px;\\n  }\\n  .story-item[_ngcontent-%COMP%] {\\n    min-width: 70px;\\n  }\\n  .nav-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n    font-size: 12px;\\n  }\\n  .nav-left[_ngcontent-%COMP%] {\\n    left: 4px;\\n  }\\n  .nav-right[_ngcontent-%COMP%] {\\n    right: 4px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n    gap: 8px;\\n  }\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .story-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 44px;\\n    height: 44px;\\n  }\\n  .story-item[_ngcontent-%COMP%] {\\n    min-width: 60px;\\n  }\\n  .story-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .nav-btn[_ngcontent-%COMP%] {\\n    width: 24px;\\n    height: 24px;\\n    font-size: 10px;\\n  }\\n  .nav-left[_ngcontent-%COMP%] {\\n    left: 2px;\\n  }\\n  .nav-right[_ngcontent-%COMP%] {\\n    right: 2px;\\n  }\\n}\\n\\n\\n@media (max-width: 360px) {\\n  .nav-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementStart", "ɵɵlistener", "StoriesComponent_div_2_button_1_Template_button_click_0_listener", "i_r3", "ɵɵrestoreView", "_r2", "index", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "goToSlide", "ɵɵelementEnd", "ɵɵclassProp", "currentSlide", "ɵɵtemplate", "StoriesComponent_div_2_button_1_Template", "ɵɵadvance", "ɵɵproperty", "dots", "StoriesComponent_div_12_Template_div_click_0_listener", "storyGroup_r6", "_r5", "$implicit", "openStoryViewer", "StoriesComponent_div_12_Template_img_error_3_listener", "$event", "onImageError", "ɵɵtext", "stories", "length", "getSafeImageUrl", "user", "avatar", "ɵɵsanitizeUrl", "fullName", "ɵɵtextInterpolate", "username", "StoriesComponent_button_13_Template_button_click_0_listener", "_r7", "slideLeft", "ɵɵelement", "canSlideLeft", "StoriesComponent_button_14_Template_button_click_0_listener", "_r8", "slideRight", "canSlideRight", "StoriesComponent", "constructor", "storyService", "authService", "router", "mediaService", "storyGroups", "currentUser", "canScrollLeft", "canScrollRight", "showNavButtons", "ngOnInit", "currentUser$", "subscribe", "loadStories", "ngAfterViewInit", "setTimeout", "updateNavigationButtons", "window", "addEventListener", "getStories", "next", "response", "error", "console", "scrollLeft", "storiesContainer", "container", "nativeElement", "scrollAmount", "clientWidth", "scrollBy", "left", "behavior", "scrollRight", "onScroll", "scrollWidth", "url", "event", "handleImageError", "openAddStory", "showStoryCreationModal", "modalOverlay", "document", "createElement", "className", "innerHTML", "styles", "textContent", "head", "append<PERSON><PERSON><PERSON>", "body", "handleFileSelect", "input", "files", "file", "log", "name", "type", "showFilePreview", "remove", "previewModal", "fileURL", "URL", "createObjectURL", "isVideo", "startsWith", "previewStyles", "storyGroup", "navigate", "_id", "ɵɵdirectiveInject", "i1", "StoryService", "i2", "AuthService", "i3", "Router", "i4", "MediaService", "selectors", "viewQuery", "StoriesComponent_Query", "rf", "ctx", "StoriesComponent_div_2_Template", "StoriesComponent_Template_div_touchstart_3_listener", "_r1", "onTouchStart", "StoriesComponent_Template_div_touchmove_3_listener", "onTouchMove", "StoriesComponent_Template_div_touchend_3_listener", "onTouchEnd", "StoriesComponent_Template_div_scroll_3_listener", "StoriesComponent_Template_div_click_6_listener", "StoriesComponent_div_12_Template", "StoriesComponent_button_13_Template", "StoriesComponent_button_14_Template", "showDots", "ɵɵstyleProp", "translateX", "isTransitioning", "showArrows", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\stories\\stories.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { MediaService } from '../../../../core/services/media.service';\n\nimport { StoryService } from '../../../../core/services/story.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { StoryGroup, Story } from '../../../../core/models/story.model';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-stories',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"stories-section\">\n      <div class=\"stories-slider-wrapper\">\n        <!-- Navigation Dots (Mobile) -->\n        <div class=\"nav-dots\" *ngIf=\"showDots\">\n          <button\n            *ngFor=\"let dot of dots; let i = index\"\n            class=\"nav-dot\"\n            [class.active]=\"i === currentSlide\"\n            (click)=\"goToSlide(i)\">\n          </button>\n        </div>\n\n        <!-- Stories Slider Container -->\n        <div class=\"stories-slider\"\n             #storiesSlider\n             (touchstart)=\"onTouchStart($event)\"\n             (touchmove)=\"onTouchMove($event)\"\n             (touchend)=\"onTouchEnd($event)\"\n             (scroll)=\"onScroll()\">\n\n          <!-- Stories Track -->\n          <div class=\"stories-track\"\n               [style.transform]=\"'translateX(' + translateX + 'px)'\"\n               [style.transition]=\"isTransitioning ? 'transform 0.3s ease-out' : 'none'\">\n\n            <!-- Add Story -->\n            <div class=\"story-slide add-story\" (click)=\"openAddStory()\">\n              <div class=\"story-avatar\">\n                <div class=\"avatar-ring add-ring\">\n                  <i class=\"fas fa-plus\"></i>\n                </div>\n              </div>\n              <span class=\"story-username\">Your Story</span>\n            </div>\n\n            <!-- User Stories -->\n            <div\n              *ngFor=\"let storyGroup of storyGroups; let i = index\"\n              class=\"story-slide\"\n              (click)=\"openStoryViewer(storyGroup)\"\n            >\n              <div class=\"story-avatar\">\n                <div class=\"avatar-ring\" [class.has-story]=\"storyGroup.stories.length > 0\">\n                  <img [src]=\"getSafeImageUrl(storyGroup.user.avatar)\"\n                       [alt]=\"storyGroup.user.fullName\"\n                       (error)=\"onImageError($event)\"\n                       class=\"avatar-image\">\n                </div>\n              </div>\n              <span class=\"story-username\">{{ storyGroup.user.username }}</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Navigation Arrows (Desktop/Tablet) -->\n        <button class=\"nav-arrow nav-left\"\n                (click)=\"slideLeft()\"\n                [disabled]=\"!canSlideLeft\"\n                *ngIf=\"showArrows\">\n          <i class=\"fas fa-chevron-left\"></i>\n        </button>\n\n        <button class=\"nav-arrow nav-right\"\n                (click)=\"slideRight()\"\n                [disabled]=\"!canSlideRight\"\n                *ngIf=\"showArrows\">\n          <i class=\"fas fa-chevron-right\"></i>\n        </button>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .stories-section {\n      margin-bottom: 24px;\n      width: 100%;\n      max-width: 100vw;\n      overflow: hidden;\n    }\n\n    .stories-slider-wrapper {\n      position: relative;\n      background: #fff;\n      border: 1px solid #dbdbdb;\n      border-radius: 12px;\n      overflow: hidden;\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n    }\n\n    .stories-slider {\n      width: 100%;\n      overflow: hidden;\n      position: relative;\n    }\n\n    .stories-track {\n      display: flex;\n      gap: 0;\n      will-change: transform;\n      padding: 16px 0;\n    }\n\n    .story-slide {\n      flex: 0 0 auto;\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      padding: 0 12px;\n      cursor: pointer;\n      transition: transform 0.2s ease;\n      width: 80px;\n    }\n\n    .story-slide:hover {\n      transform: scale(1.05);\n    }\n\n    .story-slide:first-child {\n      padding-left: 16px;\n    }\n\n    .story-slide:last-child {\n      padding-right: 16px;\n    }\n\n    .story-avatar {\n      margin-bottom: 8px;\n      position: relative;\n    }\n\n    .avatar-ring {\n      width: 64px;\n      height: 64px;\n      border-radius: 50%;\n      padding: 3px;\n      background: #fafafa;\n      border: 1px solid #dbdbdb;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      position: relative;\n      transition: all 0.3s ease;\n    }\n\n    .avatar-ring.has-story {\n      background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\n      border: none;\n      animation: pulse 2s infinite;\n    }\n\n    .avatar-ring.add-ring {\n      background: #fafafa;\n      border: 2px dashed #dbdbdb;\n    }\n\n    .avatar-ring.add-ring:hover {\n      border-color: var(--primary-color);\n      background: rgba(0, 149, 246, 0.05);\n    }\n\n    .avatar-image {\n      width: 56px;\n      height: 56px;\n      border-radius: 50%;\n      object-fit: cover;\n      border: 2px solid #fff;\n    }\n\n    .avatar-ring i {\n      color: var(--primary-color);\n      font-size: 20px;\n    }\n\n    .story-username {\n      font-size: 12px;\n      color: #262626;\n      text-align: center;\n      max-width: 70px;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      font-weight: 400;\n    }\n\n    @keyframes pulse {\n      0% { transform: scale(1); }\n      50% { transform: scale(1.05); }\n      100% { transform: scale(1); }\n    }\n\n    /* Navigation Arrows */\n    .nav-arrow {\n      position: absolute;\n      top: 50%;\n      transform: translateY(-50%);\n      width: 36px;\n      height: 36px;\n      border: none;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.95);\n      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);\n      color: #262626;\n      font-size: 16px;\n      cursor: pointer;\n      z-index: 10;\n      transition: all 0.3s ease;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      backdrop-filter: blur(10px);\n    }\n\n    .nav-arrow:hover:not(:disabled) {\n      background: #fff;\n      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\n      transform: translateY(-50%) scale(1.1);\n    }\n\n    .nav-arrow:disabled {\n      opacity: 0.3;\n      cursor: not-allowed;\n      transform: translateY(-50%);\n    }\n\n    .nav-arrow.nav-left {\n      left: 12px;\n    }\n\n    .nav-arrow.nav-right {\n      right: 12px;\n    }\n\n    /* Navigation Dots */\n    .nav-dots {\n      display: flex;\n      justify-content: center;\n      gap: 8px;\n      padding: 12px 0 8px;\n      background: rgba(255, 255, 255, 0.9);\n      backdrop-filter: blur(10px);\n    }\n\n    .nav-dot {\n      width: 8px;\n      height: 8px;\n      border-radius: 50%;\n      border: none;\n      background: #dbdbdb;\n      cursor: pointer;\n      transition: all 0.3s ease;\n    }\n\n    .nav-dot.active {\n      background: var(--primary-color);\n      transform: scale(1.2);\n    }\n\n    .nav-dot:hover {\n      background: var(--primary-color);\n      opacity: 0.7;\n    }\n\n    @media (max-width: 768px) {\n      .stories-container {\n        padding: 12px;\n        gap: 12px;\n      }\n\n      .story-avatar {\n        width: 56px;\n        height: 56px;\n      }\n\n      .story-avatar img {\n        width: 52px;\n        height: 52px;\n      }\n\n      .story-item {\n        min-width: 70px;\n      }\n\n      .nav-btn {\n        width: 28px;\n        height: 28px;\n        font-size: 12px;\n      }\n\n      .nav-left {\n        left: 4px;\n      }\n\n      .nav-right {\n        right: 4px;\n      }\n    }\n\n    @media (max-width: 480px) {\n      .stories-container {\n        padding: 8px;\n        gap: 8px;\n      }\n\n      .story-avatar {\n        width: 48px;\n        height: 48px;\n      }\n\n      .story-avatar img {\n        width: 44px;\n        height: 44px;\n      }\n\n      .story-item {\n        min-width: 60px;\n      }\n\n      .story-item span {\n        font-size: 11px;\n      }\n\n      .nav-btn {\n        width: 24px;\n        height: 24px;\n        font-size: 10px;\n      }\n\n      .nav-left {\n        left: 2px;\n      }\n\n      .nav-right {\n        right: 2px;\n      }\n    }\n\n    /* Hide navigation on very small screens */\n    @media (max-width: 360px) {\n      .nav-btn {\n        display: none;\n      }\n    }\n  `]\n})\nexport class StoriesComponent implements OnInit, AfterViewInit {\n  @ViewChild('storiesContainer') storiesContainer!: ElementRef<HTMLDivElement>;\n\n  storyGroups: StoryGroup[] = [];\n  currentUser: User | null = null;\n\n  // Slider navigation properties\n  canScrollLeft = false;\n  canScrollRight = false;\n  showNavButtons = false;\n\n  constructor(\n    private storyService: StoryService,\n    private authService: AuthService,\n    private router: Router,\n    private mediaService: MediaService\n  ) {}\n\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n\n    this.loadStories();\n  }\n\n  ngAfterViewInit() {\n    // Check if navigation buttons are needed after view init\n    setTimeout(() => {\n      this.updateNavigationButtons();\n    }, 100);\n\n    // Add resize listener to update navigation buttons\n    window.addEventListener('resize', () => {\n      this.updateNavigationButtons();\n    });\n  }\n\n  loadStories() {\n    this.storyService.getStories().subscribe({\n      next: (response) => {\n        this.storyGroups = response.storyGroups;\n        // Update navigation buttons after stories load\n        setTimeout(() => {\n          this.updateNavigationButtons();\n        }, 100);\n      },\n      error: (error) => {\n        console.error('Error loading stories:', error);\n        this.storyGroups = [];\n      }\n    });\n  }\n\n  // Slider navigation methods\n  scrollLeft() {\n    if (this.storiesContainer) {\n      const container = this.storiesContainer.nativeElement;\n      const scrollAmount = container.clientWidth * 0.8; // Scroll 80% of visible width\n      container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });\n    }\n  }\n\n  scrollRight() {\n    if (this.storiesContainer) {\n      const container = this.storiesContainer.nativeElement;\n      const scrollAmount = container.clientWidth * 0.8; // Scroll 80% of visible width\n      container.scrollBy({ left: scrollAmount, behavior: 'smooth' });\n    }\n  }\n\n  onScroll() {\n    this.updateNavigationButtons();\n  }\n\n  updateNavigationButtons() {\n    if (this.storiesContainer) {\n      const container = this.storiesContainer.nativeElement;\n      const { scrollLeft, scrollWidth, clientWidth } = container;\n\n      // Check if content overflows and navigation is needed\n      this.showNavButtons = scrollWidth > clientWidth;\n\n      // Update button states\n      this.canScrollLeft = scrollLeft > 0;\n      this.canScrollRight = scrollLeft < (scrollWidth - clientWidth - 1);\n    }\n  }\n\n  // Image handling methods\n  getSafeImageUrl(url: string | undefined): string {\n    return this.mediaService.getSafeImageUrl(url, 'user');\n  }\n\n  onImageError(event: Event): void {\n    this.mediaService.handleImageError(event, 'user');\n  }\n\n\n\n  openAddStory() {\n    this.showStoryCreationModal();\n  }\n\n  private showStoryCreationModal() {\n    // Create modal overlay\n    const modalOverlay = document.createElement('div');\n    modalOverlay.className = 'story-creation-modal';\n    modalOverlay.innerHTML = `\n      <div class=\"modal-content\">\n        <div class=\"modal-header\">\n          <h3>Create Story</h3>\n          <button class=\"close-btn\" onclick=\"this.closest('.story-creation-modal').remove()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        <div class=\"modal-body\">\n          <div class=\"creation-options\">\n            <div class=\"option-card camera-option\" onclick=\"document.getElementById('camera-input').click()\">\n              <div class=\"option-icon camera-icon\">\n                <i class=\"fas fa-camera\"></i>\n              </div>\n              <h4>Camera</h4>\n              <p>Take a photo or video</p>\n            </div>\n            <div class=\"option-card gallery-option\" onclick=\"document.getElementById('gallery-input').click()\">\n              <div class=\"option-icon gallery-icon\">\n                <i class=\"fas fa-images\"></i>\n              </div>\n              <h4>Gallery</h4>\n              <p>Choose from your photos</p>\n            </div>\n            <div class=\"option-card create-option\" onclick=\"this.closest('.story-creation-modal').querySelector('.text-story-creator').style.display='block'\">\n              <div class=\"option-icon create-icon\">\n                <i class=\"fas fa-font\"></i>\n              </div>\n              <h4>Create</h4>\n              <p>Design with text and colors</p>\n            </div>\n            <div class=\"option-card live-option\" onclick=\"alert('Live feature coming soon!')\">\n              <div class=\"option-icon live-icon\">\n                <i class=\"fas fa-video\"></i>\n              </div>\n              <h4>Live</h4>\n              <p>Go live with your audience</p>\n            </div>\n          </div>\n\n          <!-- Text Story Creator -->\n          <div class=\"text-story-creator\" style=\"display: none;\">\n            <div class=\"text-editor\">\n              <textarea placeholder=\"What's on your mind?\" maxlength=\"500\"></textarea>\n              <div class=\"color-picker\">\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff6b6b, #feca57)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff6b6b, #feca57)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #48dbfb, #0abde3)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #48dbfb, #0abde3)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff9ff3, #f368e0)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff9ff3, #f368e0)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #54a0ff, #2e86de)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #54a0ff, #2e86de)'\"></div>\n              </div>\n              <button class=\"create-text-story-btn\" onclick=\"alert('Text story created! (Demo)')\">Create Story</button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Hidden file inputs -->\n        <input type=\"file\" id=\"camera-input\" accept=\"image/*,video/*\" capture=\"environment\" style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n        <input type=\"file\" id=\"gallery-input\" accept=\"image/*,video/*\" multiple style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n      </div>\n    `;\n\n    // Add styles\n    const styles = document.createElement('style');\n    styles.textContent = `\n      .story-creation-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.8);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 10000;\n        animation: fadeIn 0.3s ease;\n      }\n\n      .modal-content {\n        background: white;\n        border-radius: 16px;\n        width: 90%;\n        max-width: 400px;\n        max-height: 80vh;\n        overflow-y: auto;\n      }\n\n      .modal-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem 1.5rem;\n        border-bottom: 1px solid #eee;\n      }\n\n      .modal-header h3 {\n        margin: 0;\n        font-size: 1.25rem;\n        font-weight: 600;\n      }\n\n      .close-btn {\n        background: none;\n        border: none;\n        font-size: 1.25rem;\n        cursor: pointer;\n        padding: 0.5rem;\n        border-radius: 50%;\n        transition: background 0.2s;\n      }\n\n      .close-btn:hover {\n        background: #f0f0f0;\n      }\n\n      .creation-options {\n        display: grid;\n        grid-template-columns: 1fr;\n        gap: 1rem;\n        padding: 1.5rem;\n      }\n\n      .option-card {\n        display: flex;\n        align-items: center;\n        padding: 1.5rem;\n        border: none;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        position: relative;\n        overflow: hidden;\n      }\n\n      .option-card:hover {\n        transform: translateY(-3px) scale(1.02);\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n      }\n\n      .camera-option {\n        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n      }\n\n      .gallery-option {\n        background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);\n      }\n\n      .create-option {\n        background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\n      }\n\n      .live-option {\n        background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);\n      }\n\n      .option-icon {\n        width: 60px;\n        height: 60px;\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 1rem;\n        backdrop-filter: blur(10px);\n        border: 2px solid rgba(255, 255, 255, 0.3);\n      }\n\n      .option-icon i {\n        color: white;\n        font-size: 1.5rem;\n      }\n\n      .option-card h4 {\n        margin: 0 0 0.25rem 0;\n        font-size: 1.1rem;\n        font-weight: 700;\n        color: white;\n      }\n\n      .option-card p {\n        margin: 0;\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 0.875rem;\n      }\n\n      .text-story-creator {\n        padding: 1.5rem;\n        background: linear-gradient(45deg, #ff6b6b, #feca57);\n        border-radius: 12px;\n        margin: 1rem;\n      }\n\n      .text-editor textarea {\n        width: 100%;\n        min-height: 120px;\n        border: none;\n        background: rgba(255, 255, 255, 0.9);\n        border-radius: 8px;\n        padding: 1rem;\n        font-size: 1.125rem;\n        resize: none;\n        margin-bottom: 1rem;\n      }\n\n      .color-picker {\n        display: flex;\n        gap: 0.5rem;\n        margin-bottom: 1rem;\n      }\n\n      .color-option {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        cursor: pointer;\n        border: 3px solid white;\n        transition: transform 0.2s;\n      }\n\n      .color-option:hover {\n        transform: scale(1.1);\n      }\n\n      .create-text-story-btn {\n        background: white;\n        border: none;\n        padding: 0.75rem 1.5rem;\n        border-radius: 25px;\n        font-weight: 600;\n        cursor: pointer;\n        transition: transform 0.2s;\n      }\n\n      .create-text-story-btn:hover {\n        transform: scale(1.05);\n      }\n\n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n    `;\n\n    document.head.appendChild(styles);\n    document.body.appendChild(modalOverlay);\n\n    // Add global function for file handling\n    (window as any).handleFileSelect = (input: HTMLInputElement) => {\n      const files = input.files;\n      if (files && files.length > 0) {\n        const file = files[0];\n        console.log('Selected file:', file.name, file.type);\n\n        // Create preview modal\n        this.showFilePreview(file);\n        modalOverlay.remove();\n      }\n    };\n  }\n\n  private showFilePreview(file: File) {\n    const previewModal = document.createElement('div');\n    previewModal.className = 'story-preview-modal';\n\n    const fileURL = URL.createObjectURL(file);\n    const isVideo = file.type.startsWith('video/');\n\n    previewModal.innerHTML = `\n      <div class=\"preview-content\">\n        <div class=\"preview-header\">\n          <button class=\"back-btn\" onclick=\"this.closest('.story-preview-modal').remove()\">\n            <i class=\"fas fa-arrow-left\"></i>\n          </button>\n          <h3>Create Story</h3>\n          <button class=\"share-btn\" onclick=\"alert('Story shared! (Demo)')\">Share</button>\n        </div>\n        <div class=\"preview-media\">\n          ${isVideo ?\n            `<video src=\"${fileURL}\" controls autoplay muted></video>` :\n            `<img src=\"${fileURL}\" alt=\"Story preview\">`\n          }\n          <div class=\"story-tools\">\n            <button class=\"tool-btn\" onclick=\"alert('Add text feature coming soon!')\">\n              <i class=\"fas fa-font\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Add stickers feature coming soon!')\">\n              <i class=\"fas fa-smile\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Tag products feature coming soon!')\">\n              <i class=\"fas fa-tag\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n    `;\n\n    // Add preview styles\n    const previewStyles = document.createElement('style');\n    previewStyles.textContent = `\n      .story-preview-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: black;\n        z-index: 10001;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .preview-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem;\n        background: rgba(0, 0, 0, 0.5);\n        color: white;\n      }\n\n      .back-btn, .share-btn {\n        background: none;\n        border: none;\n        color: white;\n        font-size: 1rem;\n        cursor: pointer;\n        padding: 0.5rem;\n      }\n\n      .share-btn {\n        background: #007bff;\n        border-radius: 20px;\n        padding: 0.5rem 1rem;\n      }\n\n      .preview-media {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        position: relative;\n      }\n\n      .preview-media img,\n      .preview-media video {\n        max-width: 100%;\n        max-height: 100%;\n        object-fit: contain;\n      }\n\n      .story-tools {\n        position: absolute;\n        right: 1rem;\n        top: 50%;\n        transform: translateY(-50%);\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n      }\n\n      .tool-btn {\n        width: 48px;\n        height: 48px;\n        background: rgba(255, 255, 255, 0.2);\n        border: none;\n        border-radius: 50%;\n        color: white;\n        font-size: 1.25rem;\n        cursor: pointer;\n        backdrop-filter: blur(10px);\n        transition: background 0.2s;\n      }\n\n      .tool-btn:hover {\n        background: rgba(255, 255, 255, 0.3);\n      }\n    `;\n\n    document.head.appendChild(previewStyles);\n    document.body.appendChild(previewModal);\n  }\n\n  openStoryViewer(storyGroup: StoryGroup) {\n    if (storyGroup.stories.length > 0) {\n      // Navigate to story viewer with user ID and start from first story\n      this.router.navigate(['/story', storyGroup.user._id, 0]);\n    } else {\n      console.log('No stories available for:', storyGroup.user.username);\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;;;;;;;IAkBpCC,EAAA,CAAAC,cAAA,iBAIyB;IAAvBD,EAAA,CAAAE,UAAA,mBAAAC,iEAAA;MAAA,MAAAC,IAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,SAAA,CAAAP,IAAA,CAAY;IAAA,EAAC;IACxBJ,EAAA,CAAAY,YAAA,EAAS;;;;;IAFPZ,EAAA,CAAAa,WAAA,WAAAT,IAAA,KAAAI,MAAA,CAAAM,YAAA,CAAmC;;;;;IAJvCd,EAAA,CAAAC,cAAA,cAAuC;IACrCD,EAAA,CAAAe,UAAA,IAAAC,wCAAA,qBAIyB;IAE3BhB,EAAA,CAAAY,YAAA,EAAM;;;;IALcZ,EAAA,CAAAiB,SAAA,EAAS;IAATjB,EAAA,CAAAkB,UAAA,YAAAV,MAAA,CAAAW,IAAA,CAAS;;;;;;IA+BzBnB,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAkB,sDAAA;MAAA,MAAAC,aAAA,GAAArB,EAAA,CAAAK,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAf,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgB,eAAA,CAAAH,aAAA,CAA2B;IAAA,EAAC;IAIjCrB,EAFJ,CAAAC,cAAA,aAA0B,cACmD,cAI/C;IADrBD,EAAA,CAAAE,UAAA,mBAAAuB,sDAAAC,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAiB,GAAA;MAAA,MAAAd,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAmB,YAAA,CAAAD,MAAA,CAAoB;IAAA,EAAC;IAGvC1B,EALI,CAAAY,YAAA,EAG0B,EACtB,EACF;IACNZ,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAA4B,MAAA,GAA8B;IAC7D5B,EAD6D,CAAAY,YAAA,EAAO,EAC9D;;;;;IARuBZ,EAAA,CAAAiB,SAAA,GAAiD;IAAjDjB,EAAA,CAAAa,WAAA,cAAAQ,aAAA,CAAAQ,OAAA,CAAAC,MAAA,KAAiD;IACnE9B,EAAA,CAAAiB,SAAA,EAA+C;IAC/CjB,EADA,CAAAkB,UAAA,QAAAV,MAAA,CAAAuB,eAAA,CAAAV,aAAA,CAAAW,IAAA,CAAAC,MAAA,GAAAjC,EAAA,CAAAkC,aAAA,CAA+C,QAAAb,aAAA,CAAAW,IAAA,CAAAG,QAAA,CACf;IAKZnC,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAoC,iBAAA,CAAAf,aAAA,CAAAW,IAAA,CAAAK,QAAA,CAA8B;;;;;;IAMjErC,EAAA,CAAAC,cAAA,iBAG2B;IAFnBD,EAAA,CAAAE,UAAA,mBAAAoC,4DAAA;MAAAtC,EAAA,CAAAK,aAAA,CAAAkC,GAAA;MAAA,MAAA/B,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgC,SAAA,EAAW;IAAA,EAAC;IAG3BxC,EAAA,CAAAyC,SAAA,YAAmC;IACrCzC,EAAA,CAAAY,YAAA,EAAS;;;;IAHDZ,EAAA,CAAAkB,UAAA,cAAAV,MAAA,CAAAkC,YAAA,CAA0B;;;;;;IAKlC1C,EAAA,CAAAC,cAAA,iBAG2B;IAFnBD,EAAA,CAAAE,UAAA,mBAAAyC,4DAAA;MAAA3C,EAAA,CAAAK,aAAA,CAAAuC,GAAA;MAAA,MAAApC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAqC,UAAA,EAAY;IAAA,EAAC;IAG5B7C,EAAA,CAAAyC,SAAA,YAAoC;IACtCzC,EAAA,CAAAY,YAAA,EAAS;;;;IAHDZ,EAAA,CAAAkB,UAAA,cAAAV,MAAA,CAAAsC,aAAA,CAA2B;;;AAuR3C,OAAM,MAAOC,gBAAgB;EAW3BC,YACUC,YAA0B,EAC1BC,WAAwB,EACxBC,MAAc,EACdC,YAA0B;IAH1B,KAAAH,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IAZtB,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,WAAW,GAAgB,IAAI;IAE/B;IACA,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,cAAc,GAAG,KAAK;EAOnB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACR,WAAW,CAACS,YAAY,CAACC,SAAS,CAAC5B,IAAI,IAAG;MAC7C,IAAI,CAACsB,WAAW,GAAGtB,IAAI;IACzB,CAAC,CAAC;IAEF,IAAI,CAAC6B,WAAW,EAAE;EACpB;EAEAC,eAAeA,CAAA;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,uBAAuB,EAAE;IAChC,CAAC,EAAE,GAAG,CAAC;IAEP;IACAC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAACF,uBAAuB,EAAE;IAChC,CAAC,CAAC;EACJ;EAEAH,WAAWA,CAAA;IACT,IAAI,CAACZ,YAAY,CAACkB,UAAU,EAAE,CAACP,SAAS,CAAC;MACvCQ,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAChB,WAAW,GAAGgB,QAAQ,CAAChB,WAAW;QACvC;QACAU,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,uBAAuB,EAAE;QAChC,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDM,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACjB,WAAW,GAAG,EAAE;MACvB;KACD,CAAC;EACJ;EAEA;EACAmB,UAAUA,CAAA;IACR,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACzB,MAAMC,SAAS,GAAG,IAAI,CAACD,gBAAgB,CAACE,aAAa;MACrD,MAAMC,YAAY,GAAGF,SAAS,CAACG,WAAW,GAAG,GAAG,CAAC,CAAC;MAClDH,SAAS,CAACI,QAAQ,CAAC;QAAEC,IAAI,EAAE,CAACH,YAAY;QAAEI,QAAQ,EAAE;MAAQ,CAAE,CAAC;;EAEnE;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACR,gBAAgB,EAAE;MACzB,MAAMC,SAAS,GAAG,IAAI,CAACD,gBAAgB,CAACE,aAAa;MACrD,MAAMC,YAAY,GAAGF,SAAS,CAACG,WAAW,GAAG,GAAG,CAAC,CAAC;MAClDH,SAAS,CAACI,QAAQ,CAAC;QAAEC,IAAI,EAAEH,YAAY;QAAEI,QAAQ,EAAE;MAAQ,CAAE,CAAC;;EAElE;EAEAE,QAAQA,CAAA;IACN,IAAI,CAAClB,uBAAuB,EAAE;EAChC;EAEAA,uBAAuBA,CAAA;IACrB,IAAI,IAAI,CAACS,gBAAgB,EAAE;MACzB,MAAMC,SAAS,GAAG,IAAI,CAACD,gBAAgB,CAACE,aAAa;MACrD,MAAM;QAAEH,UAAU;QAAEW,WAAW;QAAEN;MAAW,CAAE,GAAGH,SAAS;MAE1D;MACA,IAAI,CAACjB,cAAc,GAAG0B,WAAW,GAAGN,WAAW;MAE/C;MACA,IAAI,CAACtB,aAAa,GAAGiB,UAAU,GAAG,CAAC;MACnC,IAAI,CAAChB,cAAc,GAAGgB,UAAU,GAAIW,WAAW,GAAGN,WAAW,GAAG,CAAE;;EAEtE;EAEA;EACA9C,eAAeA,CAACqD,GAAuB;IACrC,OAAO,IAAI,CAAChC,YAAY,CAACrB,eAAe,CAACqD,GAAG,EAAE,MAAM,CAAC;EACvD;EAEAzD,YAAYA,CAAC0D,KAAY;IACvB,IAAI,CAACjC,YAAY,CAACkC,gBAAgB,CAACD,KAAK,EAAE,MAAM,CAAC;EACnD;EAIAE,YAAYA,CAAA;IACV,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQA,sBAAsBA,CAAA;IAC5B;IACA,MAAMC,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDF,YAAY,CAACG,SAAS,GAAG,sBAAsB;IAC/CH,YAAY,CAACI,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2DxB;IAED;IACA,MAAMC,MAAM,GAAGJ,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC9CG,MAAM,CAACC,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqLpB;IAEDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACH,MAAM,CAAC;IACjCJ,QAAQ,CAACQ,IAAI,CAACD,WAAW,CAACR,YAAY,CAAC;IAEvC;IACCxB,MAAc,CAACkC,gBAAgB,GAAIC,KAAuB,IAAI;MAC7D,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK;MACzB,IAAIA,KAAK,IAAIA,KAAK,CAACvE,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAMwE,IAAI,GAAGD,KAAK,CAAC,CAAC,CAAC;QACrB9B,OAAO,CAACgC,GAAG,CAAC,gBAAgB,EAAED,IAAI,CAACE,IAAI,EAAEF,IAAI,CAACG,IAAI,CAAC;QAEnD;QACA,IAAI,CAACC,eAAe,CAACJ,IAAI,CAAC;QAC1Bb,YAAY,CAACkB,MAAM,EAAE;;IAEzB,CAAC;EACH;EAEQD,eAAeA,CAACJ,IAAU;IAChC,MAAMM,YAAY,GAAGlB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDiB,YAAY,CAAChB,SAAS,GAAG,qBAAqB;IAE9C,MAAMiB,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACT,IAAI,CAAC;IACzC,MAAMU,OAAO,GAAGV,IAAI,CAACG,IAAI,CAACQ,UAAU,CAAC,QAAQ,CAAC;IAE9CL,YAAY,CAACf,SAAS,GAAG;;;;;;;;;;YAUjBmB,OAAO,GACP,eAAeH,OAAO,oCAAoC,GAC1D,aAAaA,OAAO,wBACtB;;;;;;;;;;;;;;KAcL;IAED;IACA,MAAMK,aAAa,GAAGxB,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACrDuB,aAAa,CAACnB,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8E3B;IAEDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACiB,aAAa,CAAC;IACxCxB,QAAQ,CAACQ,IAAI,CAACD,WAAW,CAACW,YAAY,CAAC;EACzC;EAEApF,eAAeA,CAAC2F,UAAsB;IACpC,IAAIA,UAAU,CAACtF,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACjC;MACA,IAAI,CAACqB,MAAM,CAACiE,QAAQ,CAAC,CAAC,QAAQ,EAAED,UAAU,CAACnF,IAAI,CAACqF,GAAG,EAAE,CAAC,CAAC,CAAC;KACzD,MAAM;MACL9C,OAAO,CAACgC,GAAG,CAAC,2BAA2B,EAAEY,UAAU,CAACnF,IAAI,CAACK,QAAQ,CAAC;;EAEtE;;;uBApfWU,gBAAgB,EAAA/C,EAAA,CAAAsH,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAxH,EAAA,CAAAsH,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1H,EAAA,CAAAsH,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA5H,EAAA,CAAAsH,iBAAA,CAAAO,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAhB/E,gBAAgB;MAAAgF,SAAA;MAAAC,SAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UAtVvBlI,EADF,CAAAC,cAAA,aAA6B,aACS;UAElCD,EAAA,CAAAe,UAAA,IAAAqH,+BAAA,iBAAuC;UAUvCpI,EAAA,CAAAC,cAAA,gBAK2B;UAAtBD,EAHA,CAAAE,UAAA,wBAAAmI,oDAAA3G,MAAA;YAAA1B,EAAA,CAAAK,aAAA,CAAAiI,GAAA;YAAA,OAAAtI,EAAA,CAAAU,WAAA,CAAcyH,GAAA,CAAAI,YAAA,CAAA7G,MAAA,CAAoB;UAAA,EAAC,uBAAA8G,mDAAA9G,MAAA;YAAA1B,EAAA,CAAAK,aAAA,CAAAiI,GAAA;YAAA,OAAAtI,EAAA,CAAAU,WAAA,CACtByH,GAAA,CAAAM,WAAA,CAAA/G,MAAA,CAAmB;UAAA,EAAC,sBAAAgH,kDAAAhH,MAAA;YAAA1B,EAAA,CAAAK,aAAA,CAAAiI,GAAA;YAAA,OAAAtI,EAAA,CAAAU,WAAA,CACrByH,GAAA,CAAAQ,UAAA,CAAAjH,MAAA,CAAkB;UAAA,EAAC,oBAAAkH,gDAAA;YAAA5I,EAAA,CAAAK,aAAA,CAAAiI,GAAA;YAAA,OAAAtI,EAAA,CAAAU,WAAA,CACrByH,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAC;UAQtBlF,EALF,CAAAC,cAAA,aAE+E,aAGjB;UAAzBD,EAAA,CAAAE,UAAA,mBAAA2I,+CAAA;YAAA7I,EAAA,CAAAK,aAAA,CAAAiI,GAAA;YAAA,OAAAtI,EAAA,CAAAU,WAAA,CAASyH,GAAA,CAAA5C,YAAA,EAAc;UAAA,EAAC;UAEvDvF,EADF,CAAAC,cAAA,aAA0B,aACU;UAChCD,EAAA,CAAAyC,SAAA,WAA2B;UAE/BzC,EADE,CAAAY,YAAA,EAAM,EACF;UACNZ,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAA4B,MAAA,kBAAU;UACzC5B,EADyC,CAAAY,YAAA,EAAO,EAC1C;UAGNZ,EAAA,CAAAe,UAAA,KAAA+H,gCAAA,kBAIC;UAYL9I,EADE,CAAAY,YAAA,EAAM,EACF;UAUNZ,EAPA,CAAAe,UAAA,KAAAgI,mCAAA,qBAG2B,KAAAC,mCAAA,qBAOA;UAI/BhJ,EADE,CAAAY,YAAA,EAAM,EACF;;;UAlEqBZ,EAAA,CAAAiB,SAAA,GAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAiH,GAAA,CAAAc,QAAA,CAAc;UAmB9BjJ,EAAA,CAAAiB,SAAA,GAAsD;UACtDjB,EADA,CAAAkJ,WAAA,8BAAAf,GAAA,CAAAgB,UAAA,SAAsD,eAAAhB,GAAA,CAAAiB,eAAA,sCACmB;UAcnDpJ,EAAA,CAAAiB,SAAA,GAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,YAAAiH,GAAA,CAAA9E,WAAA,CAAgB;UAqBpCrD,EAAA,CAAAiB,SAAA,EAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,SAAAiH,GAAA,CAAAkB,UAAA,CAAgB;UAOhBrJ,EAAA,CAAAiB,SAAA,EAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,SAAAiH,GAAA,CAAAkB,UAAA,CAAgB;;;qBAnErBtJ,YAAY,EAAAuJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAA1D,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}