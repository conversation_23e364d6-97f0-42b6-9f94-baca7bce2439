{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-a5d50daf.js';\nconst getCapacitor = () => {\n  if (win !== undefined) {\n    return win.Capacitor;\n  }\n  return undefined;\n};\nexport { getCapacitor as g };", "map": {"version": 3, "names": ["w", "win", "getCapacitor", "undefined", "Capacitor", "g"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/capacitor-59395cbd.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-a5d50daf.js';\n\nconst getCapacitor = () => {\n    if (win !== undefined) {\n        return win.Capacitor;\n    }\n    return undefined;\n};\n\nexport { getCapacitor as g };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,GAAG,QAAQ,qBAAqB;AAE9C,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,IAAID,GAAG,KAAKE,SAAS,EAAE;IACnB,OAAOF,GAAG,CAACG,SAAS;EACxB;EACA,OAAOD,SAAS;AACpB,CAAC;AAED,SAASD,YAAY,IAAIG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}