{"ast": null, "code": "import { HttpErrorResponse } from '@angular/common/http';\nimport { Observable, throwError, BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class ErrorHandlerService {\n  constructor() {\n    this.errorStateSubject = new BehaviorSubject({\n      hasError: false,\n      errors: [],\n      lastError: undefined\n    });\n    this.errorState$ = this.errorStateSubject.asObservable();\n  }\n  // Handle HTTP errors\n  handleHttpError(error, context) {\n    const appError = this.createAppError(error, context);\n    this.addError(appError);\n    return throwError(() => appError);\n  }\n  // Handle general errors\n  handleError(error, context) {\n    const appError = this.createAppError(error, context);\n    this.addError(appError);\n    return appError;\n  }\n  // Create standardized error object\n  createAppError(error, context) {\n    const errorId = this.generateErrorId();\n    let appError;\n    if (error instanceof HttpErrorResponse) {\n      appError = {\n        id: errorId,\n        type: this.getHttpErrorType(error.status),\n        message: this.getHttpErrorMessage(error),\n        details: {\n          status: error.status,\n          statusText: error.statusText,\n          url: error.url,\n          body: error.error\n        },\n        timestamp: new Date(),\n        context,\n        retryable: this.isRetryableHttpError(error.status),\n        userFriendly: this.isUserFriendlyHttpError(error.status)\n      };\n    } else if (error instanceof Error) {\n      appError = {\n        id: errorId,\n        type: 'client',\n        message: error.message,\n        details: {\n          name: error.name,\n          stack: error.stack\n        },\n        timestamp: new Date(),\n        context,\n        retryable: false,\n        userFriendly: false\n      };\n    } else {\n      appError = {\n        id: errorId,\n        type: 'unknown',\n        message: 'An unexpected error occurred',\n        details: error,\n        timestamp: new Date(),\n        context,\n        retryable: false,\n        userFriendly: true\n      };\n    }\n    return appError;\n  }\n  // Get error type based on HTTP status\n  getHttpErrorType(status) {\n    if (status >= 400 && status < 500) {\n      if (status === 401) return 'authentication';\n      if (status === 403) return 'authorization';\n      if (status === 422) return 'validation';\n      return 'client';\n    } else if (status >= 500) {\n      return 'server';\n    } else if (status === 0) {\n      return 'network';\n    }\n    return 'unknown';\n  }\n  // Get user-friendly error message\n  getHttpErrorMessage(error) {\n    // Try to extract message from error response\n    if (error.error?.message) {\n      return error.error.message;\n    }\n    // Default messages based on status code\n    switch (error.status) {\n      case 0:\n        return 'Unable to connect to the server. Please check your internet connection.';\n      case 400:\n        return 'Invalid request. Please check your input and try again.';\n      case 401:\n        return 'You need to log in to access this resource.';\n      case 403:\n        return 'You do not have permission to access this resource.';\n      case 404:\n        return 'The requested resource was not found.';\n      case 422:\n        return 'Please check your input and try again.';\n      case 429:\n        return 'Too many requests. Please wait a moment and try again.';\n      case 500:\n        return 'A server error occurred. Please try again later.';\n      case 502:\n        return 'Service temporarily unavailable. Please try again later.';\n      case 503:\n        return 'Service temporarily unavailable. Please try again later.';\n      default:\n        return `An error occurred (${error.status}). Please try again.`;\n    }\n  }\n  // Check if HTTP error is retryable\n  isRetryableHttpError(status) {\n    return [0, 408, 429, 500, 502, 503, 504].includes(status);\n  }\n  // Check if HTTP error should show user-friendly message\n  isUserFriendlyHttpError(status) {\n    return status >= 400 && status < 500;\n  }\n  // Add error to state\n  addError(error) {\n    const currentState = this.errorStateSubject.value;\n    const newErrors = [...currentState.errors, error];\n    // Keep only last 10 errors\n    if (newErrors.length > 10) {\n      newErrors.splice(0, newErrors.length - 10);\n    }\n    this.errorStateSubject.next({\n      hasError: true,\n      errors: newErrors,\n      lastError: error\n    });\n    // Log error for debugging\n    console.error('App Error:', error);\n  }\n  // Clear specific error\n  clearError(errorId) {\n    const currentState = this.errorStateSubject.value;\n    const filteredErrors = currentState.errors.filter(e => e.id !== errorId);\n    this.errorStateSubject.next({\n      hasError: filteredErrors.length > 0,\n      errors: filteredErrors,\n      lastError: filteredErrors[filteredErrors.length - 1]\n    });\n  }\n  // Clear all errors\n  clearAllErrors() {\n    this.errorStateSubject.next({\n      hasError: false,\n      errors: [],\n      lastError: undefined\n    });\n  }\n  // Get current error state\n  getCurrentErrorState() {\n    return this.errorStateSubject.value;\n  }\n  // Check if there are any errors\n  hasErrors() {\n    return this.errorStateSubject.value.hasError;\n  }\n  // Get errors by type\n  getErrorsByType(type) {\n    return this.errorStateSubject.value.errors.filter(e => e.type === type);\n  }\n  // Get retryable errors\n  getRetryableErrors() {\n    return this.errorStateSubject.value.errors.filter(e => e.retryable);\n  }\n  // Generate unique error ID\n  generateErrorId() {\n    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n  // Show user notification for error\n  showErrorNotification(error) {\n    if (error.userFriendly) {\n      // Show user-friendly notification\n      this.showToast(error.message, 'error');\n    } else {\n      // Show generic error message\n      this.showToast('An unexpected error occurred. Please try again.', 'error');\n    }\n  }\n  // Simple toast notification (can be replaced with proper toast service)\n  showToast(message, type) {\n    // This is a simple implementation - replace with your preferred toast library\n    console.log(`${type.toUpperCase()}: ${message}`);\n    // You can integrate with libraries like:\n    // - Angular Material Snackbar\n    // - ngx-toastr\n    // - Custom toast component\n  }\n  // Retry mechanism for retryable errors\n  retryOperation(operation, maxRetries = 3, delay = 1000) {\n    return new Observable(observer => {\n      let retryCount = 0;\n      const attemptOperation = () => {\n        operation().subscribe({\n          next: value => observer.next(value),\n          complete: () => observer.complete(),\n          error: error => {\n            const appError = this.createAppError(error);\n            if (appError.retryable && retryCount < maxRetries) {\n              retryCount++;\n              console.log(`Retrying operation (attempt ${retryCount}/${maxRetries})`);\n              setTimeout(attemptOperation, delay * retryCount);\n            } else {\n              this.addError(appError);\n              observer.error(appError);\n            }\n          }\n        });\n      };\n      attemptOperation();\n    });\n  }\n  static {\n    this.ɵfac = function ErrorHandlerService_Factory(t) {\n      return new (t || ErrorHandlerService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ErrorHandlerService,\n      factory: ErrorHandlerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpErrorResponse", "Observable", "throwError", "BehaviorSubject", "ErrorHandlerService", "constructor", "errorStateSubject", "<PERSON><PERSON><PERSON><PERSON>", "errors", "lastError", "undefined", "errorState$", "asObservable", "handleHttpError", "error", "context", "appError", "createAppError", "addError", "handleError", "errorId", "generateErrorId", "id", "type", "getHttpErrorType", "status", "message", "getHttpErrorMessage", "details", "statusText", "url", "body", "timestamp", "Date", "retryable", "isRetryableHttpError", "userFriendly", "isUserFriendlyHttpError", "Error", "name", "stack", "includes", "currentState", "value", "newErrors", "length", "splice", "next", "console", "clearError", "filteredErrors", "filter", "e", "clearAllErrors", "getCurrentErrorState", "hasErrors", "getErrorsByType", "getRetryableErrors", "now", "Math", "random", "toString", "substr", "showErrorNotification", "showToast", "log", "toUpperCase", "retryOperation", "operation", "maxRetries", "delay", "observer", "retryCount", "attemptOperation", "subscribe", "complete", "setTimeout", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\core\\services\\error-handler.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpErrorResponse } from '@angular/common/http';\nimport { Observable, throwError, BehaviorSubject } from 'rxjs';\n\nexport interface AppError {\n  id: string;\n  type: 'network' | 'validation' | 'authentication' | 'authorization' | 'server' | 'client' | 'unknown';\n  message: string;\n  details?: any;\n  timestamp: Date;\n  context?: string;\n  retryable: boolean;\n  userFriendly: boolean;\n}\n\nexport interface ErrorState {\n  hasError: boolean;\n  errors: AppError[];\n  lastError?: AppError;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ErrorHandlerService {\n  private errorStateSubject = new BehaviorSubject<ErrorState>({\n    hasError: false,\n    errors: [],\n    lastError: undefined\n  });\n\n  public errorState$ = this.errorStateSubject.asObservable();\n\n  constructor() {}\n\n  // Handle HTTP errors\n  handleHttpError(error: HttpErrorResponse, context?: string): Observable<never> {\n    const appError = this.createAppError(error, context);\n    this.addError(appError);\n    return throwError(() => appError);\n  }\n\n  // Handle general errors\n  handleError(error: any, context?: string): AppError {\n    const appError = this.createAppError(error, context);\n    this.addError(appError);\n    return appError;\n  }\n\n  // Create standardized error object\n  private createAppError(error: any, context?: string): AppError {\n    const errorId = this.generateErrorId();\n    let appError: AppError;\n\n    if (error instanceof HttpErrorResponse) {\n      appError = {\n        id: errorId,\n        type: this.getHttpErrorType(error.status),\n        message: this.getHttpErrorMessage(error),\n        details: {\n          status: error.status,\n          statusText: error.statusText,\n          url: error.url,\n          body: error.error\n        },\n        timestamp: new Date(),\n        context,\n        retryable: this.isRetryableHttpError(error.status),\n        userFriendly: this.isUserFriendlyHttpError(error.status)\n      };\n    } else if (error instanceof Error) {\n      appError = {\n        id: errorId,\n        type: 'client',\n        message: error.message,\n        details: {\n          name: error.name,\n          stack: error.stack\n        },\n        timestamp: new Date(),\n        context,\n        retryable: false,\n        userFriendly: false\n      };\n    } else {\n      appError = {\n        id: errorId,\n        type: 'unknown',\n        message: 'An unexpected error occurred',\n        details: error,\n        timestamp: new Date(),\n        context,\n        retryable: false,\n        userFriendly: true\n      };\n    }\n\n    return appError;\n  }\n\n  // Get error type based on HTTP status\n  private getHttpErrorType(status: number): AppError['type'] {\n    if (status >= 400 && status < 500) {\n      if (status === 401) return 'authentication';\n      if (status === 403) return 'authorization';\n      if (status === 422) return 'validation';\n      return 'client';\n    } else if (status >= 500) {\n      return 'server';\n    } else if (status === 0) {\n      return 'network';\n    }\n    return 'unknown';\n  }\n\n  // Get user-friendly error message\n  private getHttpErrorMessage(error: HttpErrorResponse): string {\n    // Try to extract message from error response\n    if (error.error?.message) {\n      return error.error.message;\n    }\n\n    // Default messages based on status code\n    switch (error.status) {\n      case 0:\n        return 'Unable to connect to the server. Please check your internet connection.';\n      case 400:\n        return 'Invalid request. Please check your input and try again.';\n      case 401:\n        return 'You need to log in to access this resource.';\n      case 403:\n        return 'You do not have permission to access this resource.';\n      case 404:\n        return 'The requested resource was not found.';\n      case 422:\n        return 'Please check your input and try again.';\n      case 429:\n        return 'Too many requests. Please wait a moment and try again.';\n      case 500:\n        return 'A server error occurred. Please try again later.';\n      case 502:\n        return 'Service temporarily unavailable. Please try again later.';\n      case 503:\n        return 'Service temporarily unavailable. Please try again later.';\n      default:\n        return `An error occurred (${error.status}). Please try again.`;\n    }\n  }\n\n  // Check if HTTP error is retryable\n  private isRetryableHttpError(status: number): boolean {\n    return [0, 408, 429, 500, 502, 503, 504].includes(status);\n  }\n\n  // Check if HTTP error should show user-friendly message\n  private isUserFriendlyHttpError(status: number): boolean {\n    return status >= 400 && status < 500;\n  }\n\n  // Add error to state\n  private addError(error: AppError): void {\n    const currentState = this.errorStateSubject.value;\n    const newErrors = [...currentState.errors, error];\n    \n    // Keep only last 10 errors\n    if (newErrors.length > 10) {\n      newErrors.splice(0, newErrors.length - 10);\n    }\n\n    this.errorStateSubject.next({\n      hasError: true,\n      errors: newErrors,\n      lastError: error\n    });\n\n    // Log error for debugging\n    console.error('App Error:', error);\n  }\n\n  // Clear specific error\n  clearError(errorId: string): void {\n    const currentState = this.errorStateSubject.value;\n    const filteredErrors = currentState.errors.filter(e => e.id !== errorId);\n    \n    this.errorStateSubject.next({\n      hasError: filteredErrors.length > 0,\n      errors: filteredErrors,\n      lastError: filteredErrors[filteredErrors.length - 1]\n    });\n  }\n\n  // Clear all errors\n  clearAllErrors(): void {\n    this.errorStateSubject.next({\n      hasError: false,\n      errors: [],\n      lastError: undefined\n    });\n  }\n\n  // Get current error state\n  getCurrentErrorState(): ErrorState {\n    return this.errorStateSubject.value;\n  }\n\n  // Check if there are any errors\n  hasErrors(): boolean {\n    return this.errorStateSubject.value.hasError;\n  }\n\n  // Get errors by type\n  getErrorsByType(type: AppError['type']): AppError[] {\n    return this.errorStateSubject.value.errors.filter(e => e.type === type);\n  }\n\n  // Get retryable errors\n  getRetryableErrors(): AppError[] {\n    return this.errorStateSubject.value.errors.filter(e => e.retryable);\n  }\n\n  // Generate unique error ID\n  private generateErrorId(): string {\n    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  // Show user notification for error\n  showErrorNotification(error: AppError): void {\n    if (error.userFriendly) {\n      // Show user-friendly notification\n      this.showToast(error.message, 'error');\n    } else {\n      // Show generic error message\n      this.showToast('An unexpected error occurred. Please try again.', 'error');\n    }\n  }\n\n  // Simple toast notification (can be replaced with proper toast service)\n  private showToast(message: string, type: 'error' | 'warning' | 'info'): void {\n    // This is a simple implementation - replace with your preferred toast library\n    console.log(`${type.toUpperCase()}: ${message}`);\n    \n    // You can integrate with libraries like:\n    // - Angular Material Snackbar\n    // - ngx-toastr\n    // - Custom toast component\n  }\n\n  // Retry mechanism for retryable errors\n  retryOperation<T>(operation: () => Observable<T>, maxRetries: number = 3, delay: number = 1000): Observable<T> {\n    return new Observable(observer => {\n      let retryCount = 0;\n      \n      const attemptOperation = () => {\n        operation().subscribe({\n          next: (value) => observer.next(value),\n          complete: () => observer.complete(),\n          error: (error) => {\n            const appError = this.createAppError(error);\n            \n            if (appError.retryable && retryCount < maxRetries) {\n              retryCount++;\n              console.log(`Retrying operation (attempt ${retryCount}/${maxRetries})`);\n              setTimeout(attemptOperation, delay * retryCount);\n            } else {\n              this.addError(appError);\n              observer.error(appError);\n            }\n          }\n        });\n      };\n      \n      attemptOperation();\n    });\n  }\n}\n"], "mappings": "AACA,SAASA,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,UAAU,EAAEC,UAAU,EAAEC,eAAe,QAAQ,MAAM;;AAsB9D,OAAM,MAAOC,mBAAmB;EAS9BC,YAAA;IARQ,KAAAC,iBAAiB,GAAG,IAAIH,eAAe,CAAa;MAC1DI,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAEC;KACZ,CAAC;IAEK,KAAAC,WAAW,GAAG,IAAI,CAACL,iBAAiB,CAACM,YAAY,EAAE;EAE3C;EAEf;EACAC,eAAeA,CAACC,KAAwB,EAAEC,OAAgB;IACxD,MAAMC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACH,KAAK,EAAEC,OAAO,CAAC;IACpD,IAAI,CAACG,QAAQ,CAACF,QAAQ,CAAC;IACvB,OAAOd,UAAU,CAAC,MAAMc,QAAQ,CAAC;EACnC;EAEA;EACAG,WAAWA,CAACL,KAAU,EAAEC,OAAgB;IACtC,MAAMC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACH,KAAK,EAAEC,OAAO,CAAC;IACpD,IAAI,CAACG,QAAQ,CAACF,QAAQ,CAAC;IACvB,OAAOA,QAAQ;EACjB;EAEA;EACQC,cAAcA,CAACH,KAAU,EAAEC,OAAgB;IACjD,MAAMK,OAAO,GAAG,IAAI,CAACC,eAAe,EAAE;IACtC,IAAIL,QAAkB;IAEtB,IAAIF,KAAK,YAAYd,iBAAiB,EAAE;MACtCgB,QAAQ,GAAG;QACTM,EAAE,EAAEF,OAAO;QACXG,IAAI,EAAE,IAAI,CAACC,gBAAgB,CAACV,KAAK,CAACW,MAAM,CAAC;QACzCC,OAAO,EAAE,IAAI,CAACC,mBAAmB,CAACb,KAAK,CAAC;QACxCc,OAAO,EAAE;UACPH,MAAM,EAAEX,KAAK,CAACW,MAAM;UACpBI,UAAU,EAAEf,KAAK,CAACe,UAAU;UAC5BC,GAAG,EAAEhB,KAAK,CAACgB,GAAG;UACdC,IAAI,EAAEjB,KAAK,CAACA;SACb;QACDkB,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBlB,OAAO;QACPmB,SAAS,EAAE,IAAI,CAACC,oBAAoB,CAACrB,KAAK,CAACW,MAAM,CAAC;QAClDW,YAAY,EAAE,IAAI,CAACC,uBAAuB,CAACvB,KAAK,CAACW,MAAM;OACxD;KACF,MAAM,IAAIX,KAAK,YAAYwB,KAAK,EAAE;MACjCtB,QAAQ,GAAG;QACTM,EAAE,EAAEF,OAAO;QACXG,IAAI,EAAE,QAAQ;QACdG,OAAO,EAAEZ,KAAK,CAACY,OAAO;QACtBE,OAAO,EAAE;UACPW,IAAI,EAAEzB,KAAK,CAACyB,IAAI;UAChBC,KAAK,EAAE1B,KAAK,CAAC0B;SACd;QACDR,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBlB,OAAO;QACPmB,SAAS,EAAE,KAAK;QAChBE,YAAY,EAAE;OACf;KACF,MAAM;MACLpB,QAAQ,GAAG;QACTM,EAAE,EAAEF,OAAO;QACXG,IAAI,EAAE,SAAS;QACfG,OAAO,EAAE,8BAA8B;QACvCE,OAAO,EAAEd,KAAK;QACdkB,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBlB,OAAO;QACPmB,SAAS,EAAE,KAAK;QAChBE,YAAY,EAAE;OACf;;IAGH,OAAOpB,QAAQ;EACjB;EAEA;EACQQ,gBAAgBA,CAACC,MAAc;IACrC,IAAIA,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG,EAAE;MACjC,IAAIA,MAAM,KAAK,GAAG,EAAE,OAAO,gBAAgB;MAC3C,IAAIA,MAAM,KAAK,GAAG,EAAE,OAAO,eAAe;MAC1C,IAAIA,MAAM,KAAK,GAAG,EAAE,OAAO,YAAY;MACvC,OAAO,QAAQ;KAChB,MAAM,IAAIA,MAAM,IAAI,GAAG,EAAE;MACxB,OAAO,QAAQ;KAChB,MAAM,IAAIA,MAAM,KAAK,CAAC,EAAE;MACvB,OAAO,SAAS;;IAElB,OAAO,SAAS;EAClB;EAEA;EACQE,mBAAmBA,CAACb,KAAwB;IAClD;IACA,IAAIA,KAAK,CAACA,KAAK,EAAEY,OAAO,EAAE;MACxB,OAAOZ,KAAK,CAACA,KAAK,CAACY,OAAO;;IAG5B;IACA,QAAQZ,KAAK,CAACW,MAAM;MAClB,KAAK,CAAC;QACJ,OAAO,yEAAyE;MAClF,KAAK,GAAG;QACN,OAAO,yDAAyD;MAClE,KAAK,GAAG;QACN,OAAO,6CAA6C;MACtD,KAAK,GAAG;QACN,OAAO,qDAAqD;MAC9D,KAAK,GAAG;QACN,OAAO,uCAAuC;MAChD,KAAK,GAAG;QACN,OAAO,wCAAwC;MACjD,KAAK,GAAG;QACN,OAAO,wDAAwD;MACjE,KAAK,GAAG;QACN,OAAO,kDAAkD;MAC3D,KAAK,GAAG;QACN,OAAO,0DAA0D;MACnE,KAAK,GAAG;QACN,OAAO,0DAA0D;MACnE;QACE,OAAO,sBAAsBX,KAAK,CAACW,MAAM,sBAAsB;;EAErE;EAEA;EACQU,oBAAoBA,CAACV,MAAc;IACzC,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACgB,QAAQ,CAAChB,MAAM,CAAC;EAC3D;EAEA;EACQY,uBAAuBA,CAACZ,MAAc;IAC5C,OAAOA,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG;EACtC;EAEA;EACQP,QAAQA,CAACJ,KAAe;IAC9B,MAAM4B,YAAY,GAAG,IAAI,CAACpC,iBAAiB,CAACqC,KAAK;IACjD,MAAMC,SAAS,GAAG,CAAC,GAAGF,YAAY,CAAClC,MAAM,EAAEM,KAAK,CAAC;IAEjD;IACA,IAAI8B,SAAS,CAACC,MAAM,GAAG,EAAE,EAAE;MACzBD,SAAS,CAACE,MAAM,CAAC,CAAC,EAAEF,SAAS,CAACC,MAAM,GAAG,EAAE,CAAC;;IAG5C,IAAI,CAACvC,iBAAiB,CAACyC,IAAI,CAAC;MAC1BxC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAEoC,SAAS;MACjBnC,SAAS,EAAEK;KACZ,CAAC;IAEF;IACAkC,OAAO,CAAClC,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;EAEA;EACAmC,UAAUA,CAAC7B,OAAe;IACxB,MAAMsB,YAAY,GAAG,IAAI,CAACpC,iBAAiB,CAACqC,KAAK;IACjD,MAAMO,cAAc,GAAGR,YAAY,CAAClC,MAAM,CAAC2C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,EAAE,KAAKF,OAAO,CAAC;IAExE,IAAI,CAACd,iBAAiB,CAACyC,IAAI,CAAC;MAC1BxC,QAAQ,EAAE2C,cAAc,CAACL,MAAM,GAAG,CAAC;MACnCrC,MAAM,EAAE0C,cAAc;MACtBzC,SAAS,EAAEyC,cAAc,CAACA,cAAc,CAACL,MAAM,GAAG,CAAC;KACpD,CAAC;EACJ;EAEA;EACAQ,cAAcA,CAAA;IACZ,IAAI,CAAC/C,iBAAiB,CAACyC,IAAI,CAAC;MAC1BxC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAEC;KACZ,CAAC;EACJ;EAEA;EACA4C,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAChD,iBAAiB,CAACqC,KAAK;EACrC;EAEA;EACAY,SAASA,CAAA;IACP,OAAO,IAAI,CAACjD,iBAAiB,CAACqC,KAAK,CAACpC,QAAQ;EAC9C;EAEA;EACAiD,eAAeA,CAACjC,IAAsB;IACpC,OAAO,IAAI,CAACjB,iBAAiB,CAACqC,KAAK,CAACnC,MAAM,CAAC2C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7B,IAAI,KAAKA,IAAI,CAAC;EACzE;EAEA;EACAkC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACnD,iBAAiB,CAACqC,KAAK,CAACnC,MAAM,CAAC2C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,SAAS,CAAC;EACrE;EAEA;EACQb,eAAeA,CAAA;IACrB,OAAO,SAASY,IAAI,CAACyB,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACzE;EAEA;EACAC,qBAAqBA,CAACjD,KAAe;IACnC,IAAIA,KAAK,CAACsB,YAAY,EAAE;MACtB;MACA,IAAI,CAAC4B,SAAS,CAAClD,KAAK,CAACY,OAAO,EAAE,OAAO,CAAC;KACvC,MAAM;MACL;MACA,IAAI,CAACsC,SAAS,CAAC,iDAAiD,EAAE,OAAO,CAAC;;EAE9E;EAEA;EACQA,SAASA,CAACtC,OAAe,EAAEH,IAAkC;IACnE;IACAyB,OAAO,CAACiB,GAAG,CAAC,GAAG1C,IAAI,CAAC2C,WAAW,EAAE,KAAKxC,OAAO,EAAE,CAAC;IAEhD;IACA;IACA;IACA;EACF;EAEA;EACAyC,cAAcA,CAAIC,SAA8B,EAAEC,UAAA,GAAqB,CAAC,EAAEC,KAAA,GAAgB,IAAI;IAC5F,OAAO,IAAIrE,UAAU,CAACsE,QAAQ,IAAG;MAC/B,IAAIC,UAAU,GAAG,CAAC;MAElB,MAAMC,gBAAgB,GAAGA,CAAA,KAAK;QAC5BL,SAAS,EAAE,CAACM,SAAS,CAAC;UACpB3B,IAAI,EAAGJ,KAAK,IAAK4B,QAAQ,CAACxB,IAAI,CAACJ,KAAK,CAAC;UACrCgC,QAAQ,EAAEA,CAAA,KAAMJ,QAAQ,CAACI,QAAQ,EAAE;UACnC7D,KAAK,EAAGA,KAAK,IAAI;YACf,MAAME,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACH,KAAK,CAAC;YAE3C,IAAIE,QAAQ,CAACkB,SAAS,IAAIsC,UAAU,GAAGH,UAAU,EAAE;cACjDG,UAAU,EAAE;cACZxB,OAAO,CAACiB,GAAG,CAAC,+BAA+BO,UAAU,IAAIH,UAAU,GAAG,CAAC;cACvEO,UAAU,CAACH,gBAAgB,EAAEH,KAAK,GAAGE,UAAU,CAAC;aACjD,MAAM;cACL,IAAI,CAACtD,QAAQ,CAACF,QAAQ,CAAC;cACvBuD,QAAQ,CAACzD,KAAK,CAACE,QAAQ,CAAC;;UAE5B;SACD,CAAC;MACJ,CAAC;MAEDyD,gBAAgB,EAAE;IACpB,CAAC,CAAC;EACJ;;;uBAzPWrE,mBAAmB;IAAA;EAAA;;;aAAnBA,mBAAmB;MAAAyE,OAAA,EAAnBzE,mBAAmB,CAAA0E,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}