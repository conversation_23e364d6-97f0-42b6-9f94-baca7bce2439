{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"src/app/core/services/cart.service\";\nimport * as i5 from \"src/app/core/services/wishlist.service\";\nimport * as i6 from \"src/app/core/services/social-media.service\";\nimport * as i7 from \"src/app/core/services/realtime.service\";\nimport * as i8 from \"@angular/common\";\nconst _c0 = [\"storiesSlider\"];\nconst _c1 = [\"storiesTrack\"];\nconst _c2 = [\"storyVideo\"];\nfunction ViewAddStoriesComponent_div_25_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_25_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", story_r4.products.length, \" item\", story_r4.products.length > 1 ? \"s\" : \"\", \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_25_Template_div_click_0_listener() {\n      const ctx_r2 = i0.ɵɵrestoreView(_r2);\n      const story_r4 = ctx_r2.$implicit;\n      const i_r5 = ctx_r2.index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.openStory(story_r4, i_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"div\", 27)(3, \"div\", 18);\n    i0.ɵɵelement(4, \"img\", 28);\n    i0.ɵɵtemplate(5, ViewAddStoriesComponent_div_25_div_5_Template, 2, 0, \"div\", 29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 21);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ViewAddStoriesComponent_div_25_div_8_Template, 2, 2, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"has-products\", story_r4.products && story_r4.products.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", story_r4.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", story_r4.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r4.products && story_r4.products.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r4.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", story_r4.products && story_r4.products.length > 0);\n  }\n}\nfunction ViewAddStoriesComponent_div_28_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵelement(1, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r8 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r8 === ctx_r5.currentStoryIndex)(\"completed\", i_r8 < ctx_r5.currentStoryIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r5.getProgressWidth(i_r8), \"%\");\n  }\n}\nfunction ViewAddStoriesComponent_div_28_img_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 62);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_28_video_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 63, 4);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_28_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_div_17_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.handleMiddleAreaClick($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 65);\n    i0.ɵɵelement(2, \"i\", 66);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_6_0;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate1(\"title\", \"View linked \", (tmp_6_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_6_0.linkedContent == null ? null : tmp_6_0.linkedContent.type, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r5.getLinkedContentText());\n  }\n}\nfunction ViewAddStoriesComponent_div_28_div_18_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_div_18_div_1_Template_div_click_0_listener($event) {\n      const productTag_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      ctx_r5.openProductDetails(productTag_r11.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"div\", 70);\n    i0.ɵɵelementStart(2, \"div\", 71);\n    i0.ɵɵelement(3, \"img\", 72);\n    i0.ɵɵelementStart(4, \"div\", 73)(5, \"span\", 74);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 75);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 76)(10, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_div_18_div_1_Template_button_click_10_listener($event) {\n      const productTag_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      ctx_r5.addToCart(productTag_r11.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(11, \"i\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_div_18_div_1_Template_button_click_12_listener($event) {\n      const productTag_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      ctx_r5.addToWishlist(productTag_r11.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(13, \"i\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_div_18_div_1_Template_button_click_14_listener($event) {\n      const productTag_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      ctx_r5.openProductDetails(productTag_r11.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(15, \"i\", 82);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const productTag_r11 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", (productTag_r11.position == null ? null : productTag_r11.position.x) || 50, \"%\")(\"top\", (productTag_r11.position == null ? null : productTag_r11.position.y) || 50, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", productTag_r11.product.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", productTag_r11.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(productTag_r11.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"$\", productTag_r11.product.price, \"\");\n  }\n}\nfunction ViewAddStoriesComponent_div_28_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_28_div_18_div_1_Template, 16, 8, \"div\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_6_0;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", (tmp_6_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_6_0.products);\n  }\n}\nfunction ViewAddStoriesComponent_div_28_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_button_19_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      ctx_r5.toggleProductTags();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 84);\n    i0.ɵɵelementStart(2, \"span\", 85);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", ctx_r5.showProductTags);\n    i0.ɵɵproperty(\"title\", \"View \" + ctx_r5.getProductCount() + \" product\" + (ctx_r5.getProductCount() > 1 ? \"s\" : \"\"));\n    i0.ɵɵattribute(\"aria-label\", \"View \" + ctx_r5.getProductCount() + \" product\" + (ctx_r5.getProductCount() > 1 ? \"s\" : \"\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getProductCount());\n  }\n}\nfunction ViewAddStoriesComponent_div_28_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.previousStory());\n    });\n    i0.ɵɵelement(1, \"i\", 87);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_28_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.nextStory());\n    });\n    i0.ɵɵelement(1, \"i\", 89);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35, 3)(3, \"div\", 36);\n    i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_28_div_4_Template, 2, 6, \"div\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 38);\n    i0.ɵɵelement(6, \"img\", 39);\n    i0.ɵɵelementStart(7, \"div\", 40)(8, \"span\", 41);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 42);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.closeStories());\n    });\n    i0.ɵɵelement(13, \"i\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_Template_div_click_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_28_Template_div_touchstart_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_28_Template_div_touchmove_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_28_Template_div_touchend_14_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onTouchEnd($event));\n    });\n    i0.ɵɵtemplate(15, ViewAddStoriesComponent_div_28_img_15_Template, 1, 1, \"img\", 46)(16, ViewAddStoriesComponent_div_28_video_16_Template, 2, 1, \"video\", 47)(17, ViewAddStoriesComponent_div_28_div_17_Template, 5, 3, \"div\", 48)(18, ViewAddStoriesComponent_div_28_div_18_Template, 2, 1, \"div\", 49)(19, ViewAddStoriesComponent_div_28_button_19_Template, 4, 5, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 51)(21, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.toggleLike());\n    });\n    i0.ɵɵelement(22, \"i\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.shareStory());\n    });\n    i0.ɵɵelement(24, \"i\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_28_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.saveStory());\n    });\n    i0.ɵɵelement(26, \"i\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(27, ViewAddStoriesComponent_div_28_button_27_Template, 2, 0, \"button\", 58)(28, ViewAddStoriesComponent_div_28_button_28_Template, 2, 0, \"button\", 59);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_12_0;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.stories);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.getCurrentStory().user.avatar, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getCurrentStory().user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.getTimeAgo(ctx_r5.getCurrentStory().createdAt));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getCurrentStory().mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getCurrentStory().mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_11_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_11_0.linkedContent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.showProductTags && ((tmp_12_0 = ctx_r5.getCurrentStory()) == null ? null : tmp_12_0.products));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.hasProducts());\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r5.isLiked);\n    i0.ɵɵproperty(\"title\", ctx_r5.isLiked ? \"Unlike story\" : \"Like story\");\n    i0.ɵɵattribute(\"aria-label\", ctx_r5.isLiked ? \"Unlike story\" : \"Like story\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.currentStoryIndex > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.currentStoryIndex < ctx_r5.stories.length - 1);\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, authService, cartService, wishlistService, socialMediaService, realtimeService) {\n    this.router = router;\n    this.http = http;\n    this.authService = authService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.socialMediaService = socialMediaService;\n    this.realtimeService = realtimeService;\n    this.currentUser = null;\n    this.stories = [];\n    this.isLoadingStories = true;\n    // Story viewer state\n    this.isOpen = false;\n    this.currentStoryIndex = 0;\n    this.showProductTags = false;\n    this.isLiked = false;\n    // Navigation state\n    this.canScrollLeft = false;\n    this.canScrollRight = false;\n    this.showNavArrows = true;\n    // Custom slider properties\n    this.translateX = 0;\n    this.itemWidth = 80; // Width of each story item including margin\n    this.visibleItems = 6; // Number of visible items\n    this.currentSlideIndex = 0;\n    this.storyDuration = 15000; // 15 seconds default\n    this.progressStartTime = 0;\n    // Touch handling\n    this.touchStartTime = 0;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    this.loadStories();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    // Check if we should show navigation arrows based on screen size\n    this.updateNavArrowsVisibility();\n  }\n  ngAfterViewInit() {\n    console.log('ngAfterViewInit called');\n    setTimeout(() => {\n      console.log('Initializing slider after view init');\n      this.calculateSliderDimensions();\n      this.updateScrollButtons();\n    }, 500);\n    // Also try immediate initialization\n    setTimeout(() => {\n      if (this.stories.length > 0) {\n        console.log('Re-initializing slider with stories');\n        this.calculateSliderDimensions();\n        this.updateScrollButtons();\n      }\n    }, 1000);\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.clearStoryTimer();\n  }\n  onResize() {\n    this.updateNavArrowsVisibility();\n    this.calculateSliderDimensions();\n    this.updateScrollButtons();\n  }\n  updateNavArrowsVisibility() {\n    this.showNavArrows = window.innerWidth > 768;\n  }\n  calculateSliderDimensions() {\n    if (this.storiesSlider && this.storiesSlider.nativeElement) {\n      const containerWidth = this.storiesSlider.nativeElement.offsetWidth;\n      const screenWidth = window.innerWidth;\n      console.log('Calculating slider dimensions:', {\n        containerWidth,\n        screenWidth\n      });\n      // Responsive visible items\n      if (screenWidth <= 600) {\n        this.visibleItems = 3;\n        this.itemWidth = containerWidth / 3;\n      } else if (screenWidth <= 900) {\n        this.visibleItems = 4;\n        this.itemWidth = containerWidth / 4;\n      } else {\n        this.visibleItems = 6;\n        this.itemWidth = containerWidth / 6;\n      }\n      console.log('Slider dimensions calculated:', {\n        visibleItems: this.visibleItems,\n        itemWidth: this.itemWidth\n      });\n      // Reset slider position if needed\n      this.currentSlideIndex = 0;\n      this.translateX = 0;\n    } else {\n      console.warn('Stories slider element not found, using default dimensions');\n      // Fallback dimensions\n      this.itemWidth = 100;\n      this.visibleItems = 6;\n    }\n  }\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(this.http.get(`${environment.apiUrl}/stories`).subscribe({\n      next: response => {\n        console.log('Stories API response:', response);\n        if (response.success && response.stories && response.stories.length > 0) {\n          this.stories = response.stories.map(story => ({\n            ...story,\n            mediaUrl: story.media?.url || story.mediaUrl,\n            mediaType: story.media?.type || story.mediaType\n          }));\n          console.log('Loaded stories from API:', this.stories);\n        } else {\n          console.log('No stories from API, loading fallback stories');\n          this.loadFallbackStories();\n        }\n        this.isLoadingStories = false;\n        setTimeout(() => {\n          this.calculateSliderDimensions();\n          this.updateScrollButtons();\n        }, 100);\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        console.log('Loading fallback stories due to error');\n        this.loadFallbackStories();\n        this.isLoadingStories = false;\n        setTimeout(() => {\n          this.calculateSliderDimensions();\n          this.updateScrollButtons();\n        }, 100);\n      }\n    }));\n  }\n  loadFallbackStories() {\n    console.log('Loading fallback stories...');\n    this.stories = [{\n      _id: '1',\n      user: {\n        _id: '1',\n        username: 'fashionista_maya',\n        fullName: 'Maya Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150'\n      },\n      media: {\n        type: 'image',\n        url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600',\n      mediaType: 'image',\n      caption: 'Check out this amazing vintage denim jacket! 💙',\n      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(),\n      views: 1250,\n      isActive: true,\n      products: [{\n        _id: 'p1',\n        product: {\n          _id: 'prod1',\n          name: 'Vintage Denim Jacket',\n          price: 89.99,\n          images: [{\n            url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=300',\n            isPrimary: true\n          }]\n        },\n        position: {\n          x: 30,\n          y: 40\n        }\n      }]\n    }, {\n      _id: '2',\n      user: {\n        _id: '2',\n        username: 'style_guru_alex',\n        fullName: 'Alex Chen',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150'\n      },\n      media: {\n        type: 'image',\n        url: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=600'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=600',\n      mediaType: 'image',\n      caption: 'Elegant evening look with this silk dress 💫',\n      createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 20 * 60 * 60 * 1000).toISOString(),\n      views: 892,\n      isActive: true,\n      products: [{\n        _id: 'p2',\n        product: {\n          _id: 'prod2',\n          name: 'Silk Slip Dress',\n          price: 159.99,\n          images: [{\n            url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=300',\n            isPrimary: true\n          }]\n        },\n        position: {\n          x: 50,\n          y: 60\n        }\n      }]\n    }, {\n      _id: '3',\n      user: {\n        _id: '3',\n        username: 'trendy_sarah',\n        fullName: 'Sarah Johnson',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150'\n      },\n      media: {\n        type: 'image',\n        url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600',\n      mediaType: 'image',\n      caption: 'Cozy vibes in this amazing knit sweater! 🍂',\n      createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(),\n      views: 567,\n      isActive: true,\n      products: [{\n        _id: 'p3',\n        product: {\n          _id: 'prod3',\n          name: 'Cozy Knit Sweater',\n          price: 79.99,\n          images: [{\n            url: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=300',\n            isPrimary: true\n          }]\n        },\n        position: {\n          x: 40,\n          y: 50\n        }\n      }]\n    }, {\n      _id: '4',\n      user: {\n        _id: '4',\n        username: 'urban_style',\n        fullName: 'Mike Thompson',\n        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150'\n      },\n      media: {\n        type: 'image',\n        url: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=600'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=600',\n      mediaType: 'image',\n      caption: 'Perfect fit with these high-waisted jeans! 👖',\n      createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 16 * 60 * 60 * 1000).toISOString(),\n      views: 1456,\n      isActive: true,\n      products: [{\n        _id: 'p4',\n        product: {\n          _id: 'prod4',\n          name: 'High-Waisted Jeans',\n          price: 119.99,\n          images: [{\n            url: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=300',\n            isPrimary: true\n          }]\n        },\n        position: {\n          x: 35,\n          y: 45\n        }\n      }]\n    }, {\n      _id: '5',\n      user: {\n        _id: '5',\n        username: 'boho_chic',\n        fullName: 'Emma Wilson',\n        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150'\n      },\n      media: {\n        type: 'image',\n        url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600',\n      mediaType: 'image',\n      caption: 'Summer vibes with this floral dress! 🌸',\n      createdAt: new Date(Date.now() - 10 * 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 14 * 60 * 60 * 1000).toISOString(),\n      views: 743,\n      isActive: true,\n      products: [{\n        _id: 'p5',\n        product: {\n          _id: 'prod5',\n          name: 'Floral Summer Dress',\n          price: 139.99,\n          images: [{\n            url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=300',\n            isPrimary: true\n          }]\n        },\n        position: {\n          x: 45,\n          y: 55\n        }\n      }]\n    }];\n    console.log('Fallback stories loaded:', this.stories.length, 'stories');\n  }\n  // Navigation methods\n  scrollLeft() {\n    console.log('Scroll left clicked, current index:', this.currentSlideIndex);\n    if (this.currentSlideIndex > 0) {\n      this.currentSlideIndex--;\n      this.updateSliderPosition();\n      console.log('Scrolled left to index:', this.currentSlideIndex);\n    } else {\n      console.log('Cannot scroll left, already at start');\n    }\n  }\n  scrollRight() {\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\n    console.log('Scroll right clicked:', {\n      currentIndex: this.currentSlideIndex,\n      totalItems,\n      maxSlideIndex,\n      visibleItems: this.visibleItems\n    });\n    if (this.currentSlideIndex < maxSlideIndex) {\n      this.currentSlideIndex++;\n      this.updateSliderPosition();\n      console.log('Scrolled right to index:', this.currentSlideIndex);\n    } else {\n      console.log('Cannot scroll right, already at end');\n    }\n  }\n  updateSliderPosition() {\n    const newTranslateX = -this.currentSlideIndex * this.itemWidth;\n    console.log('Updating slider position:', {\n      currentIndex: this.currentSlideIndex,\n      itemWidth: this.itemWidth,\n      newTranslateX\n    });\n    this.translateX = newTranslateX;\n    this.updateScrollButtons();\n  }\n  updateScrollButtons() {\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\n    this.canScrollLeft = this.currentSlideIndex > 0;\n    this.canScrollRight = this.currentSlideIndex < maxSlideIndex;\n    console.log('Updated scroll buttons:', {\n      canScrollLeft: this.canScrollLeft,\n      canScrollRight: this.canScrollRight,\n      totalItems,\n      maxSlideIndex,\n      currentIndex: this.currentSlideIndex\n    });\n  }\n  // Story viewer methods\n  openStory(_story, index) {\n    this.currentStoryIndex = index;\n    this.isOpen = true;\n    this.showProductTags = false;\n    this.startStoryTimer();\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.clearStoryTimer();\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n  }\n  nextStory() {\n    if (this.currentStoryIndex < this.stories.length - 1) {\n      this.currentStoryIndex++;\n      this.startStoryTimer();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentStoryIndex > 0) {\n      this.currentStoryIndex--;\n      this.startStoryTimer();\n    }\n  }\n  getCurrentStory() {\n    return this.stories[this.currentStoryIndex] || this.stories[0];\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getProductCount() {\n    const story = this.getCurrentStory();\n    return story?.products?.length || 0;\n  }\n  // Progress tracking\n  getProgressWidth(index) {\n    if (index < this.currentStoryIndex) return 100;\n    if (index > this.currentStoryIndex) return 0;\n    if (this.progressStartTime) {\n      const elapsed = Date.now() - this.progressStartTime;\n      return Math.min(elapsed / this.storyDuration * 100, 100);\n    }\n    return 0;\n  }\n  startStoryTimer() {\n    this.clearStoryTimer();\n    this.progressStartTime = Date.now();\n    this.progressTimer = setTimeout(() => {\n      this.nextStory();\n    }, this.storyDuration);\n  }\n  clearStoryTimer() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n      this.progressTimer = null;\n    }\n    this.progressStartTime = 0;\n  }\n  // Story interaction methods\n  onStoryClick(event) {\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else if (clickX > windowWidth * 2 / 3) {\n      this.nextStory();\n    }\n  }\n  // Touch handling\n  onTouchStart(_event) {\n    this.touchStartTime = Date.now();\n    this.longPressTimer = setTimeout(() => {\n      this.clearStoryTimer(); // Pause story progress on long press\n    }, 500);\n  }\n  onTouchMove(_event) {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n  onTouchEnd(event) {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n    const touchDuration = Date.now() - this.touchStartTime;\n    if (touchDuration < 500) {\n      // Short tap - treat as click\n      const touch = event.changedTouches[0];\n      this.onStoryClick({\n        clientX: touch.clientX\n      });\n    } else {\n      // Long press ended - resume story progress\n      this.startStoryTimer();\n    }\n  }\n  // Product interaction methods\n  toggleProductTags() {\n    this.showProductTags = !this.showProductTags;\n  }\n  openProductDetails(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  // Add product to cart with real-time functionality\n  addToCart(product) {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n    console.log('Adding product to cart:', product);\n    this.cartService.addToCart(product._id, 1, product.size, product.color).subscribe({\n      next: response => {\n        console.log('Product added to cart successfully:', response);\n        // Real-time update will be handled by the cart service\n      },\n      error: error => {\n        console.error('Error adding product to cart:', error);\n      }\n    });\n  }\n  // Add product to wishlist with real-time functionality\n  addToWishlist(product) {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n    console.log('Adding product to wishlist:', product);\n    this.wishlistService.addToWishlist(product._id).subscribe({\n      next: response => {\n        console.log('Product added to wishlist successfully:', response);\n        // Real-time update will be handled by the wishlist service\n      },\n      error: error => {\n        console.error('Error adding product to wishlist:', error);\n      }\n    });\n  }\n  // Story actions with real-time functionality\n  toggleLike() {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n    const currentStory = this.getCurrentStory();\n    this.isLiked = !this.isLiked;\n    // Call API to like/unlike story\n    const endpoint = this.isLiked ? 'like' : 'unlike';\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/${endpoint}`, {}).subscribe({\n      next: response => {\n        console.log(`Story ${endpoint}d successfully:`, response);\n        // Emit real-time event (using socket directly)\n        if (this.realtimeService.isConnected()) {\n          // For now, we'll track this locally until we add story-specific emit methods\n          console.log('Story liked event:', {\n            storyId: currentStory._id,\n            userId: this.authService.currentUserValue?._id,\n            liked: this.isLiked\n          });\n        }\n      },\n      error: error => {\n        console.error(`Error ${endpoint}ing story:`, error);\n        // Revert the like state on error\n        this.isLiked = !this.isLiked;\n      }\n    });\n  }\n  shareStory() {\n    const currentStory = this.getCurrentStory();\n    if (navigator.share) {\n      // Use native sharing if available\n      navigator.share({\n        title: `Story by ${currentStory.user.username}`,\n        text: currentStory.caption,\n        url: `${environment.frontendUrl}/story/${currentStory._id}`\n      }).then(() => {\n        console.log('Story shared successfully');\n        // Track share event\n        this.trackStoryShare(currentStory._id);\n      }).catch(error => {\n        console.error('Error sharing story:', error);\n      });\n    } else {\n      // Fallback: copy link to clipboard\n      const shareUrl = `${environment.frontendUrl}/story/${currentStory._id}`;\n      navigator.clipboard.writeText(shareUrl).then(() => {\n        console.log('Story link copied to clipboard');\n        this.trackStoryShare(currentStory._id);\n        // Show toast notification\n        this.showToast('Story link copied to clipboard!');\n      }).catch(error => {\n        console.error('Error copying to clipboard:', error);\n      });\n    }\n  }\n  saveStory() {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login']);\n      return;\n    }\n    const currentStory = this.getCurrentStory();\n    // Call API to save story\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/save`, {}).subscribe({\n      next: response => {\n        console.log('Story saved successfully:', response);\n        this.showToast('Story saved to your collection!');\n        // Emit real-time event\n        this.realtimeService.emitEvent('story:saved', {\n          storyId: currentStory._id,\n          userId: this.authService.currentUserValue?._id\n        });\n      },\n      error: error => {\n        console.error('Error saving story:', error);\n        this.showToast('Error saving story. Please try again.');\n      }\n    });\n  }\n  trackStoryShare(storyId) {\n    if (this.authService.isAuthenticated) {\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/share`, {}).subscribe({\n        next: response => {\n          console.log('Story share tracked:', response);\n        },\n        error: error => {\n          console.error('Error tracking story share:', error);\n        }\n      });\n    }\n  }\n  // Handle middle area click for product/category navigation\n  handleMiddleAreaClick(event) {\n    event.stopPropagation();\n    const currentStory = this.getCurrentStory();\n    if (!currentStory?.linkedContent) {\n      return;\n    }\n    console.log('Middle area clicked, navigating to:', currentStory.linkedContent);\n    switch (currentStory.linkedContent.type) {\n      case 'product':\n        if (currentStory.linkedContent.productId) {\n          this.router.navigate(['/product', currentStory.linkedContent.productId]);\n        } else if (currentStory.products && currentStory.products.length > 0) {\n          // Fallback to first product if no specific productId\n          this.router.navigate(['/product', currentStory.products[0].product._id]);\n        }\n        break;\n      case 'category':\n        if (currentStory.linkedContent.categoryId) {\n          this.router.navigate(['/shop'], {\n            queryParams: {\n              category: currentStory.linkedContent.categoryId\n            }\n          });\n        }\n        break;\n      case 'brand':\n        if (currentStory.linkedContent.brandId) {\n          this.router.navigate(['/shop'], {\n            queryParams: {\n              brand: currentStory.linkedContent.brandId\n            }\n          });\n        }\n        break;\n      case 'collection':\n        if (currentStory.linkedContent.collectionId) {\n          this.router.navigate(['/collection', currentStory.linkedContent.collectionId]);\n        }\n        break;\n      default:\n        console.warn('Unknown linked content type:', currentStory.linkedContent.type);\n    }\n    // Track click event\n    this.trackLinkedContentClick(currentStory._id, currentStory.linkedContent);\n  }\n  // Get text for linked content indicator\n  getLinkedContentText() {\n    const currentStory = this.getCurrentStory();\n    if (!currentStory?.linkedContent) {\n      return '';\n    }\n    switch (currentStory.linkedContent.type) {\n      case 'product':\n        return 'View Product';\n      case 'category':\n        return 'Browse Category';\n      case 'brand':\n        return 'View Brand';\n      case 'collection':\n        return 'View Collection';\n      default:\n        return 'View Details';\n    }\n  }\n  // Track linked content click for analytics\n  trackLinkedContentClick(storyId, linkedContent) {\n    if (this.authService.isAuthenticated) {\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/click`, {\n        contentType: linkedContent.type,\n        contentId: linkedContent.productId || linkedContent.categoryId || linkedContent.brandId || linkedContent.collectionId\n      }).subscribe({\n        next: response => {\n          console.log('Linked content click tracked:', response);\n        },\n        error: error => {\n          console.error('Error tracking linked content click:', error);\n        }\n      });\n    }\n  }\n  showToast(message) {\n    // Simple toast implementation - you can replace with your preferred toast library\n    const toast = document.createElement('div');\n    toast.textContent = message;\n    toast.style.cssText = `\n      position: fixed;\n      bottom: 20px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: white;\n      padding: 12px 24px;\n      border-radius: 8px;\n      z-index: 10000;\n      font-size: 14px;\n    `;\n    document.body.appendChild(toast);\n    setTimeout(() => {\n      document.body.removeChild(toast);\n    }, 3000);\n  }\n  // Add story functionality\n  onAdd() {\n    this.router.navigate(['/stories/create']);\n  }\n  // Utility methods\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.CartService), i0.ɵɵdirectiveInject(i5.WishlistService), i0.ɵɵdirectiveInject(i6.SocialMediaService), i0.ɵɵdirectiveInject(i7.RealtimeService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesTrack = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storyVideo = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function ViewAddStoriesComponent_resize_HostBindingHandler($event) {\n            return ctx.onResize($event);\n          }, false, i0.ɵɵresolveWindow)(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 29,\n      vars: 11,\n      consts: [[\"storiesContainer\", \"\"], [\"storiesSlider\", \"\"], [\"storiesTrack\", \"\"], [\"feedCover\", \"\"], [\"storyVideo\", \"\"], [1, \"stories-container\"], [1, \"stories-header\"], [1, \"stories-title\"], [\"type\", \"button\", \"aria-label\", \"Create new story\", \"title\", \"Create a new story\", 1, \"create-story-btn\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-plus\"], [1, \"stories-slider-wrapper\"], [\"type\", \"button\", \"aria-label\", \"Scroll stories left\", \"title\", \"Previous stories\", 1, \"nav-arrow\", \"nav-arrow-left\", 3, \"click\", \"disabled\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-chevron-left\"], [1, \"stories-slider\"], [1, \"stories-track\"], [1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"story-avatar\", \"add-avatar\"], [1, \"story-avatar-inner\"], [\"alt\", \"Your Story\", 1, \"story-avatar-img\", 3, \"src\"], [1, \"add-story-plus\"], [1, \"story-username\"], [\"class\", \"story-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"aria-label\", \"Scroll stories right\", \"title\", \"Next stories\", 1, \"nav-arrow\", \"nav-arrow-right\", 3, \"click\", \"disabled\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-chevron-right\"], [\"class\", \"stories-overlay\", 4, \"ngIf\"], [1, \"story-item\", 3, \"click\"], [1, \"story-avatar\"], [1, \"story-avatar-img\", 3, \"src\", \"alt\"], [\"class\", \"shopping-bag-indicator\", \"title\", \"Shoppable content\", 4, \"ngIf\"], [\"class\", \"product-count-badge\", 4, \"ngIf\"], [\"title\", \"Shoppable content\", 1, \"shopping-bag-indicator\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"product-count-badge\"], [1, \"stories-overlay\"], [1, \"stories-content\"], [1, \"progress-container\"], [\"class\", \"progress-bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-header\"], [\"alt\", \"Avatar\", 1, \"story-header-avatar\", 3, \"src\"], [1, \"story-header-info\"], [1, \"username\"], [1, \"time\"], [1, \"close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story-media\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [\"class\", \"story-image\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story-video\", \"autoplay\", \"\", \"muted\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story-middle-click-area\", 3, \"title\", \"click\", 4, \"ngIf\"], [\"class\", \"product-tags\", 4, \"ngIf\"], [\"class\", \"shopping-bag-btn\", \"type\", \"button\", 3, \"active\", \"title\", \"click\", 4, \"ngIf\"], [1, \"story-actions\"], [\"type\", \"button\", 1, \"action-btn\", \"like-btn\", 3, \"click\", \"title\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-heart\"], [\"type\", \"button\", \"aria-label\", \"Share story\", \"title\", \"Share story\", 1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-share\"], [\"type\", \"button\", \"aria-label\", \"Save story\", \"title\", \"Save story\", 1, \"action-btn\", \"save-btn\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-bookmark\"], [\"class\", \"story-nav-btn story-nav-prev\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"story-nav-btn story-nav-next\", 3, \"click\", 4, \"ngIf\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"story-image\", 3, \"src\"], [\"autoplay\", \"\", \"muted\", \"\", 1, \"story-video\", 3, \"src\"], [1, \"story-middle-click-area\", 3, \"click\", \"title\"], [1, \"middle-click-indicator\"], [1, \"fas\", \"fa-external-link-alt\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"left\", \"top\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-dot\"], [1, \"product-tag-info\"], [1, \"product-tag-image\", 3, \"src\", \"alt\"], [1, \"product-tag-details\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"product-tag-actions\"], [\"type\", \"button\", \"title\", \"Add to Cart\", 1, \"product-action-btn\", \"cart-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [\"type\", \"button\", \"title\", \"Add to Wishlist\", 1, \"product-action-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [\"type\", \"button\", \"title\", \"Buy Now\", 1, \"product-action-btn\", \"buy-btn\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [\"type\", \"button\", 1, \"shopping-bag-btn\", 3, \"click\", \"title\"], [\"aria-hidden\", \"true\", 1, \"fas\", \"fa-shopping-bag\"], [\"aria-hidden\", \"true\", 1, \"product-count\"], [1, \"story-nav-btn\", \"story-nav-prev\", 3, \"click\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"story-nav-btn\", \"story-nav-next\", 3, \"click\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 5, 0)(2, \"div\", 6)(3, \"h3\", 7);\n          i0.ɵɵtext(4, \"Stories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onAdd());\n          });\n          i0.ɵɵelement(6, \"i\", 9);\n          i0.ɵɵelementStart(7, \"span\");\n          i0.ɵɵtext(8, \"Create\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 10)(10, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_10_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.scrollLeft());\n          });\n          i0.ɵɵelement(11, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 13, 1)(14, \"div\", 14, 2)(16, \"div\", 15);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_div_click_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onAdd());\n          });\n          i0.ɵɵelementStart(17, \"div\", 16)(18, \"div\", 17)(19, \"div\", 18);\n          i0.ɵɵelement(20, \"img\", 19);\n          i0.ɵɵelementStart(21, \"span\", 20);\n          i0.ɵɵtext(22, \"+\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(23, \"div\", 21);\n          i0.ɵɵtext(24, \"Your Story\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(25, ViewAddStoriesComponent_div_25_Template, 9, 7, \"div\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_26_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.scrollRight());\n          });\n          i0.ɵɵelement(27, \"i\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(28, ViewAddStoriesComponent_div_28_Template, 29, 15, \"div\", 25);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassProp(\"hidden\", !ctx.showNavArrows);\n          i0.ɵɵproperty(\"disabled\", !ctx.canScrollLeft);\n          i0.ɵɵadvance(4);\n          i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx.translateX + \"px)\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"src\", (ctx.currentUser == null ? null : ctx.currentUser.avatar) || \"assets/default-avatar.png\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.stories);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"hidden\", !ctx.showNavArrows);\n          i0.ɵɵproperty(\"disabled\", !ctx.canScrollRight);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, FormsModule],\n      styles: [\".stories-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin: 16px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  overflow: hidden;\\n}\\n@media (max-width: 768px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    margin: 8px;\\n    padding: 12px;\\n    border-radius: 12px;\\n  }\\n}\\n\\n.stories-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n.stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n  flex: 1;\\n  min-width: 0;\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  color: white;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  white-space: nowrap;\\n  min-height: 36px;\\n  flex-shrink: 0;\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid rgba(255, 255, 255, 0.5);\\n  outline-offset: 2px;\\n}\\n.stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n@media (max-width: 480px) {\\n  .stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%] {\\n    padding: 6px 12px;\\n    font-size: 12px;\\n    min-height: 32px;\\n  }\\n  .stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .stories-header[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n  .stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  z-index: 10;\\n}\\n.nav-arrow[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: white;\\n  transform: scale(1.1);\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\\n}\\n.nav-arrow[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.nav-arrow.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.nav-arrow[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 14px;\\n}\\n@media (max-width: 768px) {\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.stories-track[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.3s ease;\\n  will-change: transform;\\n}\\n\\n.story-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  cursor: pointer;\\n  transition: transform 0.3s ease;\\n  position: relative;\\n  flex-shrink: 0;\\n  width: calc(16.6666666667% - 16px);\\n  margin: 0 8px;\\n  box-sizing: border-box;\\n}\\n.story-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n}\\n@media (max-width: 900px) {\\n  .story-item[_ngcontent-%COMP%] {\\n    width: calc(25% - 12px);\\n    margin: 0 6px;\\n  }\\n}\\n@media (max-width: 600px) {\\n  .story-item[_ngcontent-%COMP%] {\\n    width: calc(33.3333333333% - 12px);\\n    margin: 0 6px;\\n  }\\n}\\n\\n.story-avatar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n  padding: 3px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.story-avatar.has-products[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n@media (max-width: 768px) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.8;\\n  }\\n}\\n.story-avatar-inner[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n  position: relative;\\n}\\n@media (max-width: 768px) {\\n  .story-avatar-inner[_ngcontent-%COMP%] {\\n    width: 54px;\\n    height: 54px;\\n  }\\n}\\n\\n.story-avatar-img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.shopping-bag-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -2px;\\n  right: -2px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 50%;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 2px solid white;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n}\\n.shopping-bag-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 10px;\\n}\\n@media (max-width: 768px) {\\n  .shopping-bag-indicator[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n  }\\n  .shopping-bag-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n}\\n\\n.story-username[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: white;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  max-width: 80px;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n}\\n@media (max-width: 768px) {\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 70px;\\n  }\\n}\\n\\n.product-count-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -8px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #333;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  white-space: nowrap;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n@media (max-width: 768px) {\\n  .product-count-badge[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    padding: 1px 4px;\\n  }\\n}\\n\\n\\n\\n.add-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border: 2px dashed rgba(255, 255, 255, 0.5);\\n}\\n\\n.add-story-plus[_ngcontent-%COMP%] {\\n  position: absolute;\\n  font-size: 18px;\\n  font-weight: bold;\\n  color: #fff;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  bottom: -2px;\\n  right: -2px;\\n  z-index: 1;\\n  border: 2px solid white;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n}\\n@media (max-width: 768px) {\\n  .add-story-plus[_ngcontent-%COMP%] {\\n    width: 20px;\\n    height: 20px;\\n    font-size: 14px;\\n  }\\n}\\n\\n\\n\\n.stories-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.95));\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .stories-overlay[_ngcontent-%COMP%] {\\n    background: #000;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n.stories-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 90%;\\n  max-width: 400px;\\n  height: 80vh;\\n  max-height: 700px;\\n  background: #000;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\\n  animation: _ngcontent-%COMP%_slideUp 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .stories-content[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: 100vh;\\n    max-height: none;\\n    border-radius: 0;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(50px) scale(0.9);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n.progress-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  right: 8px;\\n  display: flex;\\n  gap: 4px;\\n  z-index: 10;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 3px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 2px;\\n  overflow: hidden;\\n}\\n.progress-bar.active[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progressFill 5s linear;\\n}\\n.progress-bar.completed[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: white;\\n  border-radius: 2px;\\n  transition: width 0.1s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progressFill {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n.story-header[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  display: flex;\\n  align-items: center;\\n  padding: 16px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);\\n  color: #fff;\\n  z-index: 10;\\n}\\n@media (max-width: 768px) {\\n  .story-header[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n}\\n\\n.story-header-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  margin-right: 12px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n}\\n@media (max-width: 768px) {\\n  .story-header-avatar[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n}\\n\\n.story-header-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  font-size: 16px;\\n  margin-bottom: 2px;\\n}\\n@media (max-width: 768px) {\\n  .username[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n\\n.time[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-weight: 400;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: none;\\n  border-radius: 50%;\\n  width: 36px;\\n  height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #fff;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: scale(1.1);\\n}\\n.close-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n@media (max-width: 768px) {\\n  .close-btn[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .close-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n\\n.story-media[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  background: #000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n}\\n\\n.story-middle-click-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 5;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.story-middle-click-area[_ngcontent-%COMP%]:hover {\\n  transform: translate(-50%, -50%) scale(1.1);\\n}\\n\\n.middle-click-indicator[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 12px 20px;\\n  border-radius: 25px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  animation: _ngcontent-%COMP%_pulseIndicator 2s infinite;\\n}\\n.middle-click-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n.middle-click-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n}\\n@media (max-width: 768px) {\\n  .middle-click-indicator[_ngcontent-%COMP%] {\\n    padding: 10px 16px;\\n    font-size: 12px;\\n  }\\n  .middle-click-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulseIndicator {\\n  0% {\\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.2);\\n  }\\n  100% {\\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\\n  }\\n}\\n.story-image[_ngcontent-%COMP%], .story-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  display: block;\\n}\\n\\n.product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: all;\\n  cursor: pointer;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n.product-tag-dot[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background: white;\\n  border-radius: 50%;\\n  border: 3px solid #667eea;\\n  position: relative;\\n  animation: _ngcontent-%COMP%_ripple 2s infinite;\\n}\\n.product-tag-dot[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 8px;\\n  height: 8px;\\n  background: #667eea;\\n  border-radius: 50%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_ripple {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);\\n  }\\n}\\n.product-tag-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 30px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(0, 0, 0, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 12px;\\n  padding: 12px;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  min-width: 200px;\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.product-tag-image[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n}\\n\\n.product-tag-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n  color: white;\\n}\\n\\n.product-tag-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  font-size: 14px;\\n  margin-bottom: 4px;\\n  line-height: 1.2;\\n}\\n\\n.product-tag-price[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #667eea;\\n  font-weight: 700;\\n  font-size: 16px;\\n  margin-bottom: 8px;\\n}\\n\\n.product-tag-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 6px;\\n  justify-content: flex-start;\\n}\\n\\n.product-action-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  border-radius: 50%;\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  color: #333;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.product-action-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n.product-action-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.product-action-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  background: #667eea;\\n  color: white;\\n}\\n.product-action-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  background: #f5576c;\\n  color: white;\\n}\\n.product-action-btn.buy-btn[_ngcontent-%COMP%]:hover {\\n  background: #28a745;\\n  color: white;\\n}\\n.product-action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n@media (max-width: 768px) {\\n  .product-action-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n  .product-action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n\\n.shopping-bag-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 20px;\\n  right: 20px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border: none;\\n  border-radius: 50%;\\n  width: 56px;\\n  height: 56px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\\n  z-index: 5;\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid rgba(255, 255, 255, 0.8);\\n  outline-offset: 2px;\\n}\\n.shopping-bag-btn.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n  animation: _ngcontent-%COMP%_bounce 0.6s ease;\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 20px;\\n}\\n.shopping-bag-btn[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -8px;\\n  right: -8px;\\n  background: #f5576c;\\n  color: white;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: 600;\\n  border: 2px solid white;\\n  min-width: 24px;\\n}\\n@media (max-width: 768px) {\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n    bottom: 16px;\\n    right: 16px;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%] {\\n    width: 20px;\\n    height: 20px;\\n    font-size: 10px;\\n    min-width: 20px;\\n    top: -6px;\\n    right: -6px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    width: 44px;\\n    height: 44px;\\n    bottom: 12px;\\n    right: 12px;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n    font-size: 9px;\\n    min-width: 18px;\\n    top: -5px;\\n    right: -5px;\\n  }\\n}\\n@media (max-height: 600px) {\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    bottom: 10px;\\n    right: 10px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 20%, 60%, 100% {\\n    transform: translateY(0);\\n  }\\n  40% {\\n    transform: translateY(-10px);\\n  }\\n  80% {\\n    transform: translateY(-5px);\\n  }\\n}\\n.story-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 20px;\\n  left: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  z-index: 5;\\n}\\n@media (max-width: 768px) {\\n  .story-actions[_ngcontent-%COMP%] {\\n    bottom: 16px;\\n    left: 16px;\\n    gap: 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-actions[_ngcontent-%COMP%] {\\n    bottom: 12px;\\n    left: 12px;\\n    gap: 10px;\\n  }\\n}\\n@media (max-width: 768px) and (max-height: 600px) {\\n  .story-actions[_ngcontent-%COMP%] {\\n    bottom: 10px;\\n    left: 10px;\\n    gap: 8px;\\n  }\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  color: white;\\n}\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: scale(1.1);\\n}\\n.action-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.action-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid rgba(255, 255, 255, 0.6);\\n  outline-offset: 2px;\\n}\\n.action-btn.liked[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n  color: white;\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease;\\n}\\n.action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n@media (max-width: 768px) {\\n  .action-btn[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .action-btn[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .action-btn[_ngcontent-%COMP%] {\\n    min-width: 44px;\\n    min-height: 44px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.2);\\n  }\\n}\\n.story-nav-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 50%;\\n  width: 48px;\\n  height: 48px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  color: white;\\n  z-index: 5;\\n}\\n.story-nav-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.story-nav-btn.story-nav-prev[_ngcontent-%COMP%] {\\n  left: 20px;\\n}\\n.story-nav-btn.story-nav-next[_ngcontent-%COMP%] {\\n  right: 20px;\\n}\\n.story-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n@media (max-width: 768px) {\\n  .story-nav-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .stories-container[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .stories-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .stories-container[_ngcontent-%COMP%]   .stories-header[_ngcontent-%COMP%]   .create-story-btn[_ngcontent-%COMP%] {\\n    padding: 6px 12px;\\n    font-size: 12px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%] {\\n    min-width: 160px;\\n    padding: 8px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-image[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-name[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-price[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .stories-content[_ngcontent-%COMP%]   .shopping-bag-btn[_ngcontent-%COMP%]:not(:only-child) {\\n    right: 16px;\\n    bottom: 80px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .stories-container[_ngcontent-%COMP%] {\\n    margin: 4px;\\n    padding: 8px;\\n    border-radius: 8px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%] {\\n    min-width: 140px;\\n    padding: 6px;\\n    bottom: 20px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-image[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-name[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%]   .product-tag-price[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n}\\n@media (max-width: 768px) and (orientation: landscape) {\\n  .stories-content[_ngcontent-%COMP%] {\\n    height: 100vh;\\n    max-height: none;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    bottom: 10px;\\n    right: 10px;\\n  }\\n  .story-actions[_ngcontent-%COMP%] {\\n    bottom: 10px;\\n    left: 10px;\\n    gap: 8px;\\n  }\\n}\\n.story-item.loading[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.story-item[_ngcontent-%COMP%]:focus, .action-btn[_ngcontent-%COMP%]:focus, .shopping-bag-btn[_ngcontent-%COMP%]:focus, .nav-arrow[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #667eea;\\n  outline-offset: 2px;\\n}\\n\\n@media (prefers-contrast: high) {\\n  .story-avatar[_ngcontent-%COMP%] {\\n    border: 2px solid #000;\\n  }\\n  .shopping-bag-btn[_ngcontent-%COMP%] {\\n    border: 2px solid #fff;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "environment", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "story_r4", "products", "length", "ɵɵlistener", "ViewAddStoriesComponent_div_25_Template_div_click_0_listener", "ctx_r2", "ɵɵrestoreView", "_r2", "$implicit", "i_r5", "index", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "openStory", "ɵɵtemplate", "ViewAddStoriesComponent_div_25_div_5_Template", "ViewAddStoriesComponent_div_25_div_8_Template", "ɵɵclassProp", "ɵɵproperty", "user", "avatar", "ɵɵsanitizeUrl", "username", "ɵɵtextInterpolate", "i_r8", "currentStoryIndex", "ɵɵstyleProp", "getProgressWidth", "getCurrentStory", "mediaUrl", "ViewAddStoriesComponent_div_28_div_17_Template_div_click_0_listener", "$event", "_r9", "handleMiddleAreaClick", "ɵɵpropertyInterpolate1", "tmp_6_0", "linkedContent", "type", "getLinkedContentText", "ViewAddStoriesComponent_div_28_div_18_div_1_Template_div_click_0_listener", "productTag_r11", "_r10", "openProductDetails", "product", "stopPropagation", "ViewAddStoriesComponent_div_28_div_18_div_1_Template_button_click_10_listener", "addToCart", "ViewAddStoriesComponent_div_28_div_18_div_1_Template_button_click_12_listener", "addToWishlist", "ViewAddStoriesComponent_div_28_div_18_div_1_Template_button_click_14_listener", "position", "x", "y", "images", "url", "name", "ɵɵtextInterpolate1", "price", "ViewAddStoriesComponent_div_28_div_18_div_1_Template", "ViewAddStoriesComponent_div_28_button_19_Template_button_click_0_listener", "_r12", "toggleProductTags", "showProductTags", "getProductCount", "ViewAddStoriesComponent_div_28_button_27_Template_button_click_0_listener", "_r13", "previousStory", "ViewAddStoriesComponent_div_28_button_28_Template_button_click_0_listener", "_r14", "nextStory", "ViewAddStoriesComponent_div_28_div_4_Template", "ViewAddStoriesComponent_div_28_Template_button_click_12_listener", "_r7", "closeStories", "ViewAddStoriesComponent_div_28_Template_div_click_14_listener", "onStoryClick", "ViewAddStoriesComponent_div_28_Template_div_touchstart_14_listener", "onTouchStart", "ViewAddStoriesComponent_div_28_Template_div_touchmove_14_listener", "onTouchMove", "ViewAddStoriesComponent_div_28_Template_div_touchend_14_listener", "onTouchEnd", "ViewAddStoriesComponent_div_28_img_15_Template", "ViewAddStoriesComponent_div_28_video_16_Template", "ViewAddStoriesComponent_div_28_div_17_Template", "ViewAddStoriesComponent_div_28_div_18_Template", "ViewAddStoriesComponent_div_28_button_19_Template", "ViewAddStoriesComponent_div_28_Template_button_click_21_listener", "toggleLike", "ViewAddStoriesComponent_div_28_Template_button_click_23_listener", "shareStory", "ViewAddStoriesComponent_div_28_Template_button_click_25_listener", "saveStory", "ViewAddStoriesComponent_div_28_button_27_Template", "ViewAddStoriesComponent_div_28_button_28_Template", "stories", "getTimeAgo", "createdAt", "mediaType", "tmp_11_0", "tmp_12_0", "hasProducts", "isLiked", "ViewAddStoriesComponent", "constructor", "router", "http", "authService", "cartService", "wishlistService", "socialMediaService", "realtimeService", "currentUser", "isLoadingStories", "isOpen", "canScrollLeft", "canScrollRight", "showNavArrows", "translateX", "itemWidth", "visibleItems", "currentSlideIndex", "storyDuration", "progressStartTime", "touchStartTime", "subscriptions", "ngOnInit", "loadStories", "currentUser$", "subscribe", "updateNavArrowsVisibility", "ngAfterViewInit", "console", "log", "setTimeout", "calculateSliderDimensions", "updateScrollButtons", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "clearStoryTimer", "onResize", "window", "innerWidth", "storiesSlider", "nativeElement", "containerWidth", "offsetWidth", "screenWidth", "warn", "push", "get", "apiUrl", "next", "response", "success", "map", "story", "media", "loadFallbackStories", "error", "_id", "fullName", "caption", "Date", "now", "toISOString", "expiresAt", "views", "isActive", "isPrimary", "scrollLeft", "updateSliderPosition", "scrollRight", "totalItems", "maxSlideIndex", "Math", "max", "currentIndex", "newTranslateX", "_story", "startStoryTimer", "document", "body", "style", "overflow", "pauseAllVideos", "elapsed", "min", "progressTimer", "clearTimeout", "event", "clickX", "clientX", "windowWidth", "_event", "longPressTimer", "touchDuration", "touch", "changedTouches", "navigate", "isAuthenticated", "size", "color", "currentStory", "endpoint", "post", "isConnected", "storyId", "userId", "currentUserValue", "liked", "navigator", "share", "title", "text", "frontendUrl", "then", "trackStoryShare", "catch", "shareUrl", "clipboard", "writeText", "showToast", "emitEvent", "productId", "categoryId", "queryParams", "category", "brandId", "brand", "collectionId", "trackLinkedContentClick", "contentType", "contentId", "message", "toast", "createElement", "textContent", "cssText", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "onAdd", "dateString", "date", "diffInMinutes", "floor", "getTime", "diffInHours", "diffInDays", "videos", "querySelectorAll", "video", "pause", "handleKeydown", "key", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "AuthService", "i4", "CartService", "i5", "WishlistService", "i6", "SocialMediaService", "i7", "RealtimeService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_resize_HostBindingHandler", "ɵɵresolveWindow", "ViewAddStoriesComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ViewAddStoriesComponent_Template_button_click_5_listener", "_r1", "ViewAddStoriesComponent_Template_button_click_10_listener", "ViewAddStoriesComponent_Template_div_click_16_listener", "ViewAddStoriesComponent_div_25_Template", "ViewAddStoriesComponent_Template_button_click_26_listener", "ViewAddStoriesComponent_div_28_Template", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts", "E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.html"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ElementRef, ViewChild, HostListener, AfterViewInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Subscription } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { CartService } from 'src/app/core/services/cart.service';\r\nimport { WishlistService } from 'src/app/core/services/wishlist.service';\r\nimport { SocialMediaService } from 'src/app/core/services/social-media.service';\r\nimport { RealtimeService } from 'src/app/core/services/realtime.service';\r\n\r\ninterface Story {\r\n  _id: string;\r\n  user: {\r\n    _id: string;\r\n    username: string;\r\n    fullName: string;\r\n    avatar: string;\r\n  };\r\n  media: {\r\n    type: 'image' | 'video';\r\n    url: string;\r\n    thumbnail?: string;\r\n    duration?: number;\r\n  };\r\n  mediaUrl: string; // For backward compatibility\r\n  mediaType: 'image' | 'video'; // For backward compatibility\r\n  caption?: string;\r\n  createdAt: string;\r\n  expiresAt: string;\r\n  views: number;\r\n  isActive: boolean;\r\n  linkedContent?: {\r\n    type: 'product' | 'category' | 'brand' | 'collection';\r\n    productId?: string;\r\n    categoryId?: string;\r\n    brandId?: string;\r\n    collectionId?: string;\r\n  };\r\n  products?: Array<{\r\n    _id: string;\r\n    product: {\r\n      _id: string;\r\n      name: string;\r\n      price: number;\r\n      images: Array<{ url: string; alt?: string; isPrimary?: boolean }>;\r\n    };\r\n    position?: {\r\n      x: number;\r\n      y: number;\r\n    };\r\n    size?: string;\r\n    color?: string;\r\n  }>;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-view-add-stories',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule],\r\n  templateUrl: './view-add-stories.component.html',\r\n  styleUrls: ['./view-add-stories.component.scss']\r\n})\r\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef<HTMLDivElement>;\r\n  @ViewChild('storiesTrack', { static: false }) storiesTrack!: ElementRef<HTMLDivElement>;\r\n  @ViewChild('storyVideo', { static: false }) storyVideo!: ElementRef<HTMLVideoElement>;\r\n\r\n  currentUser: any = null;\r\n  stories: Story[] = [];\r\n  isLoadingStories = true;\r\n\r\n  // Story viewer state\r\n  isOpen = false;\r\n  currentStoryIndex = 0;\r\n  showProductTags = false;\r\n  isLiked = false;\r\n\r\n  // Navigation state\r\n  canScrollLeft = false;\r\n  canScrollRight = false;\r\n  showNavArrows = true;\r\n\r\n  // Custom slider properties\r\n  translateX = 0;\r\n  itemWidth = 80; // Width of each story item including margin\r\n  visibleItems = 6; // Number of visible items\r\n  currentSlideIndex = 0;\r\n\r\n  // Progress tracking\r\n  private progressTimer: any;\r\n  private storyDuration = 15000; // 15 seconds default\r\n  private progressStartTime = 0;\r\n\r\n  // Touch handling\r\n  private touchStartTime = 0;\r\n  private longPressTimer: any;\r\n\r\n  private subscriptions: Subscription[] = [];\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private http: HttpClient,\r\n    private authService: AuthService,\r\n    private cartService: CartService,\r\n    private wishlistService: WishlistService,\r\n    private socialMediaService: SocialMediaService,\r\n    private realtimeService: RealtimeService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadStories();\r\n    this.authService.currentUser$.subscribe(user => {\r\n      this.currentUser = user;\r\n    });\r\n\r\n    // Check if we should show navigation arrows based on screen size\r\n    this.updateNavArrowsVisibility();\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    console.log('ngAfterViewInit called');\r\n    setTimeout(() => {\r\n      console.log('Initializing slider after view init');\r\n      this.calculateSliderDimensions();\r\n      this.updateScrollButtons();\r\n    }, 500);\r\n\r\n    // Also try immediate initialization\r\n    setTimeout(() => {\r\n      if (this.stories.length > 0) {\r\n        console.log('Re-initializing slider with stories');\r\n        this.calculateSliderDimensions();\r\n        this.updateScrollButtons();\r\n      }\r\n    }, 1000);\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subscriptions.forEach(sub => sub.unsubscribe());\r\n    this.clearStoryTimer();\r\n  }\r\n\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize() {\r\n    this.updateNavArrowsVisibility();\r\n    this.calculateSliderDimensions();\r\n    this.updateScrollButtons();\r\n  }\r\n\r\n  private updateNavArrowsVisibility() {\r\n    this.showNavArrows = window.innerWidth > 768;\r\n  }\r\n\r\n  private calculateSliderDimensions() {\r\n    if (this.storiesSlider && this.storiesSlider.nativeElement) {\r\n      const containerWidth = this.storiesSlider.nativeElement.offsetWidth;\r\n      const screenWidth = window.innerWidth;\r\n\r\n      console.log('Calculating slider dimensions:', { containerWidth, screenWidth });\r\n\r\n      // Responsive visible items\r\n      if (screenWidth <= 600) {\r\n        this.visibleItems = 3;\r\n        this.itemWidth = containerWidth / 3;\r\n      } else if (screenWidth <= 900) {\r\n        this.visibleItems = 4;\r\n        this.itemWidth = containerWidth / 4;\r\n      } else {\r\n        this.visibleItems = 6;\r\n        this.itemWidth = containerWidth / 6;\r\n      }\r\n\r\n      console.log('Slider dimensions calculated:', {\r\n        visibleItems: this.visibleItems,\r\n        itemWidth: this.itemWidth\r\n      });\r\n\r\n      // Reset slider position if needed\r\n      this.currentSlideIndex = 0;\r\n      this.translateX = 0;\r\n    } else {\r\n      console.warn('Stories slider element not found, using default dimensions');\r\n      // Fallback dimensions\r\n      this.itemWidth = 100;\r\n      this.visibleItems = 6;\r\n    }\r\n  }\r\n\r\n  loadStories() {\r\n    this.isLoadingStories = true;\r\n    this.subscriptions.push(\r\n      this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({\r\n        next: (response) => {\r\n          console.log('Stories API response:', response);\r\n          if (response.success && response.stories && response.stories.length > 0) {\r\n            this.stories = response.stories.map((story: any) => ({\r\n              ...story,\r\n              mediaUrl: story.media?.url || story.mediaUrl,\r\n              mediaType: story.media?.type || story.mediaType\r\n            }));\r\n            console.log('Loaded stories from API:', this.stories);\r\n          } else {\r\n            console.log('No stories from API, loading fallback stories');\r\n            this.loadFallbackStories();\r\n          }\r\n          this.isLoadingStories = false;\r\n          setTimeout(() => {\r\n            this.calculateSliderDimensions();\r\n            this.updateScrollButtons();\r\n          }, 100);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading stories:', error);\r\n          console.log('Loading fallback stories due to error');\r\n          this.loadFallbackStories();\r\n          this.isLoadingStories = false;\r\n          setTimeout(() => {\r\n            this.calculateSliderDimensions();\r\n            this.updateScrollButtons();\r\n          }, 100);\r\n        }\r\n      })\r\n    );\r\n  }\r\n\r\n  loadFallbackStories() {\r\n    console.log('Loading fallback stories...');\r\n    this.stories = [\r\n      {\r\n        _id: '1',\r\n        user: {\r\n          _id: '1',\r\n          username: 'fashionista_maya',\r\n          fullName: 'Maya Rodriguez',\r\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150'\r\n        },\r\n        media: {\r\n          type: 'image',\r\n          url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600'\r\n        },\r\n        mediaUrl: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600',\r\n        mediaType: 'image',\r\n        caption: 'Check out this amazing vintage denim jacket! 💙',\r\n        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\r\n        expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(),\r\n        views: 1250,\r\n        isActive: true,\r\n        products: [\r\n          {\r\n            _id: 'p1',\r\n            product: {\r\n              _id: 'prod1',\r\n              name: 'Vintage Denim Jacket',\r\n              price: 89.99,\r\n              images: [{ url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=300', isPrimary: true }]\r\n            },\r\n            position: { x: 30, y: 40 }\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        _id: '2',\r\n        user: {\r\n          _id: '2',\r\n          username: 'style_guru_alex',\r\n          fullName: 'Alex Chen',\r\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150'\r\n        },\r\n        media: {\r\n          type: 'image',\r\n          url: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=600'\r\n        },\r\n        mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=600',\r\n        mediaType: 'image',\r\n        caption: 'Elegant evening look with this silk dress 💫',\r\n        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\r\n        expiresAt: new Date(Date.now() + 20 * 60 * 60 * 1000).toISOString(),\r\n        views: 892,\r\n        isActive: true,\r\n        products: [\r\n          {\r\n            _id: 'p2',\r\n            product: {\r\n              _id: 'prod2',\r\n              name: 'Silk Slip Dress',\r\n              price: 159.99,\r\n              images: [{ url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=300', isPrimary: true }]\r\n            },\r\n            position: { x: 50, y: 60 }\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        _id: '3',\r\n        user: {\r\n          _id: '3',\r\n          username: 'trendy_sarah',\r\n          fullName: 'Sarah Johnson',\r\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150'\r\n        },\r\n        media: {\r\n          type: 'image',\r\n          url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600'\r\n        },\r\n        mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600',\r\n        mediaType: 'image',\r\n        caption: 'Cozy vibes in this amazing knit sweater! 🍂',\r\n        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\r\n        expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(),\r\n        views: 567,\r\n        isActive: true,\r\n        products: [\r\n          {\r\n            _id: 'p3',\r\n            product: {\r\n              _id: 'prod3',\r\n              name: 'Cozy Knit Sweater',\r\n              price: 79.99,\r\n              images: [{ url: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=300', isPrimary: true }]\r\n            },\r\n            position: { x: 40, y: 50 }\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        _id: '4',\r\n        user: {\r\n          _id: '4',\r\n          username: 'urban_style',\r\n          fullName: 'Mike Thompson',\r\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150'\r\n        },\r\n        media: {\r\n          type: 'image',\r\n          url: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=600'\r\n        },\r\n        mediaUrl: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=600',\r\n        mediaType: 'image',\r\n        caption: 'Perfect fit with these high-waisted jeans! 👖',\r\n        createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\r\n        expiresAt: new Date(Date.now() + 16 * 60 * 60 * 1000).toISOString(),\r\n        views: 1456,\r\n        isActive: true,\r\n        products: [\r\n          {\r\n            _id: 'p4',\r\n            product: {\r\n              _id: 'prod4',\r\n              name: 'High-Waisted Jeans',\r\n              price: 119.99,\r\n              images: [{ url: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=300', isPrimary: true }]\r\n            },\r\n            position: { x: 35, y: 45 }\r\n          }\r\n        ]\r\n      },\r\n      {\r\n        _id: '5',\r\n        user: {\r\n          _id: '5',\r\n          username: 'boho_chic',\r\n          fullName: 'Emma Wilson',\r\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150'\r\n        },\r\n        media: {\r\n          type: 'image',\r\n          url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600'\r\n        },\r\n        mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600',\r\n        mediaType: 'image',\r\n        caption: 'Summer vibes with this floral dress! 🌸',\r\n        createdAt: new Date(Date.now() - 10 * 60 * 60 * 1000).toISOString(),\r\n        expiresAt: new Date(Date.now() + 14 * 60 * 60 * 1000).toISOString(),\r\n        views: 743,\r\n        isActive: true,\r\n        products: [\r\n          {\r\n            _id: 'p5',\r\n            product: {\r\n              _id: 'prod5',\r\n              name: 'Floral Summer Dress',\r\n              price: 139.99,\r\n              images: [{ url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=300', isPrimary: true }]\r\n            },\r\n            position: { x: 45, y: 55 }\r\n          }\r\n        ]\r\n      }\r\n    ];\r\n    console.log('Fallback stories loaded:', this.stories.length, 'stories');\r\n  }\r\n\r\n  // Navigation methods\r\n  scrollLeft() {\r\n    console.log('Scroll left clicked, current index:', this.currentSlideIndex);\r\n    if (this.currentSlideIndex > 0) {\r\n      this.currentSlideIndex--;\r\n      this.updateSliderPosition();\r\n      console.log('Scrolled left to index:', this.currentSlideIndex);\r\n    } else {\r\n      console.log('Cannot scroll left, already at start');\r\n    }\r\n  }\r\n\r\n  scrollRight() {\r\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\r\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\r\n\r\n    console.log('Scroll right clicked:', {\r\n      currentIndex: this.currentSlideIndex,\r\n      totalItems,\r\n      maxSlideIndex,\r\n      visibleItems: this.visibleItems\r\n    });\r\n\r\n    if (this.currentSlideIndex < maxSlideIndex) {\r\n      this.currentSlideIndex++;\r\n      this.updateSliderPosition();\r\n      console.log('Scrolled right to index:', this.currentSlideIndex);\r\n    } else {\r\n      console.log('Cannot scroll right, already at end');\r\n    }\r\n  }\r\n\r\n  private updateSliderPosition() {\r\n    const newTranslateX = -this.currentSlideIndex * this.itemWidth;\r\n    console.log('Updating slider position:', {\r\n      currentIndex: this.currentSlideIndex,\r\n      itemWidth: this.itemWidth,\r\n      newTranslateX\r\n    });\r\n\r\n    this.translateX = newTranslateX;\r\n    this.updateScrollButtons();\r\n  }\r\n\r\n  updateScrollButtons() {\r\n    const totalItems = this.stories.length + 1; // +1 for \"Add Story\" button\r\n    const maxSlideIndex = Math.max(0, totalItems - this.visibleItems);\r\n\r\n    this.canScrollLeft = this.currentSlideIndex > 0;\r\n    this.canScrollRight = this.currentSlideIndex < maxSlideIndex;\r\n\r\n    console.log('Updated scroll buttons:', {\r\n      canScrollLeft: this.canScrollLeft,\r\n      canScrollRight: this.canScrollRight,\r\n      totalItems,\r\n      maxSlideIndex,\r\n      currentIndex: this.currentSlideIndex\r\n    });\r\n  }\r\n\r\n  // Story viewer methods\r\n  openStory(_story: Story, index: number) {\r\n    this.currentStoryIndex = index;\r\n    this.isOpen = true;\r\n    this.showProductTags = false;\r\n    this.startStoryTimer();\r\n    document.body.style.overflow = 'hidden';\r\n  }\r\n\r\n  closeStories() {\r\n    this.isOpen = false;\r\n    this.clearStoryTimer();\r\n    this.pauseAllVideos();\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  nextStory() {\r\n    if (this.currentStoryIndex < this.stories.length - 1) {\r\n      this.currentStoryIndex++;\r\n      this.startStoryTimer();\r\n    } else {\r\n      this.closeStories();\r\n    }\r\n  }\r\n\r\n  previousStory() {\r\n    if (this.currentStoryIndex > 0) {\r\n      this.currentStoryIndex--;\r\n      this.startStoryTimer();\r\n    }\r\n  }\r\n\r\n  getCurrentStory(): Story {\r\n    return this.stories[this.currentStoryIndex] || this.stories[0];\r\n  }\r\n\r\n  hasProducts(): boolean {\r\n    const story = this.getCurrentStory();\r\n    return !!(story && story.products && story.products.length > 0);\r\n  }\r\n\r\n  getProductCount(): number {\r\n    const story = this.getCurrentStory();\r\n    return story?.products?.length || 0;\r\n  }\r\n\r\n  // Progress tracking\r\n  getProgressWidth(index: number): number {\r\n    if (index < this.currentStoryIndex) return 100;\r\n    if (index > this.currentStoryIndex) return 0;\r\n\r\n    if (this.progressStartTime) {\r\n      const elapsed = Date.now() - this.progressStartTime;\r\n      return Math.min((elapsed / this.storyDuration) * 100, 100);\r\n    }\r\n    return 0;\r\n  }\r\n\r\n  private startStoryTimer() {\r\n    this.clearStoryTimer();\r\n    this.progressStartTime = Date.now();\r\n\r\n    this.progressTimer = setTimeout(() => {\r\n      this.nextStory();\r\n    }, this.storyDuration);\r\n  }\r\n\r\n  private clearStoryTimer() {\r\n    if (this.progressTimer) {\r\n      clearTimeout(this.progressTimer);\r\n      this.progressTimer = null;\r\n    }\r\n    this.progressStartTime = 0;\r\n  }\r\n\r\n  // Story interaction methods\r\n  onStoryClick(event: MouseEvent) {\r\n    const clickX = event.clientX;\r\n    const windowWidth = window.innerWidth;\r\n\r\n    if (clickX < windowWidth / 3) {\r\n      this.previousStory();\r\n    } else if (clickX > (windowWidth * 2) / 3) {\r\n      this.nextStory();\r\n    }\r\n  }\r\n\r\n  // Touch handling\r\n  onTouchStart(_event: TouchEvent) {\r\n    this.touchStartTime = Date.now();\r\n    this.longPressTimer = setTimeout(() => {\r\n      this.clearStoryTimer(); // Pause story progress on long press\r\n    }, 500);\r\n  }\r\n\r\n  onTouchMove(_event: TouchEvent) {\r\n    if (this.longPressTimer) {\r\n      clearTimeout(this.longPressTimer);\r\n      this.longPressTimer = null;\r\n    }\r\n  }\r\n\r\n  onTouchEnd(event: TouchEvent) {\r\n    if (this.longPressTimer) {\r\n      clearTimeout(this.longPressTimer);\r\n      this.longPressTimer = null;\r\n    }\r\n\r\n    const touchDuration = Date.now() - this.touchStartTime;\r\n    if (touchDuration < 500) {\r\n      // Short tap - treat as click\r\n      const touch = event.changedTouches[0];\r\n      this.onStoryClick({ clientX: touch.clientX } as MouseEvent);\r\n    } else {\r\n      // Long press ended - resume story progress\r\n      this.startStoryTimer();\r\n    }\r\n  }\r\n\r\n  // Product interaction methods\r\n  toggleProductTags() {\r\n    this.showProductTags = !this.showProductTags;\r\n  }\r\n\r\n  openProductDetails(product: any) {\r\n    this.router.navigate(['/product', product._id]);\r\n  }\r\n\r\n  // Add product to cart with real-time functionality\r\n  addToCart(product: any) {\r\n    if (!this.authService.isAuthenticated) {\r\n      this.router.navigate(['/auth/login']);\r\n      return;\r\n    }\r\n\r\n    console.log('Adding product to cart:', product);\r\n    this.cartService.addToCart(product._id, 1, product.size, product.color).subscribe({\r\n      next: (response) => {\r\n        console.log('Product added to cart successfully:', response);\r\n        // Real-time update will be handled by the cart service\r\n      },\r\n      error: (error) => {\r\n        console.error('Error adding product to cart:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Add product to wishlist with real-time functionality\r\n  addToWishlist(product: any) {\r\n    if (!this.authService.isAuthenticated) {\r\n      this.router.navigate(['/auth/login']);\r\n      return;\r\n    }\r\n\r\n    console.log('Adding product to wishlist:', product);\r\n    this.wishlistService.addToWishlist(product._id).subscribe({\r\n      next: (response) => {\r\n        console.log('Product added to wishlist successfully:', response);\r\n        // Real-time update will be handled by the wishlist service\r\n      },\r\n      error: (error) => {\r\n        console.error('Error adding product to wishlist:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Story actions with real-time functionality\r\n  toggleLike() {\r\n    if (!this.authService.isAuthenticated) {\r\n      this.router.navigate(['/auth/login']);\r\n      return;\r\n    }\r\n\r\n    const currentStory = this.getCurrentStory();\r\n    this.isLiked = !this.isLiked;\r\n\r\n    // Call API to like/unlike story\r\n    const endpoint = this.isLiked ? 'like' : 'unlike';\r\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/${endpoint}`, {}).subscribe({\r\n      next: (response) => {\r\n        console.log(`Story ${endpoint}d successfully:`, response);\r\n        // Emit real-time event (using socket directly)\r\n        if (this.realtimeService.isConnected()) {\r\n          // For now, we'll track this locally until we add story-specific emit methods\r\n          console.log('Story liked event:', {\r\n            storyId: currentStory._id,\r\n            userId: this.authService.currentUserValue?._id,\r\n            liked: this.isLiked\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error(`Error ${endpoint}ing story:`, error);\r\n        // Revert the like state on error\r\n        this.isLiked = !this.isLiked;\r\n      }\r\n    });\r\n  }\r\n\r\n  shareStory() {\r\n    const currentStory = this.getCurrentStory();\r\n\r\n    if (navigator.share) {\r\n      // Use native sharing if available\r\n      navigator.share({\r\n        title: `Story by ${currentStory.user.username}`,\r\n        text: currentStory.caption,\r\n        url: `${environment.frontendUrl}/story/${currentStory._id}`\r\n      }).then(() => {\r\n        console.log('Story shared successfully');\r\n        // Track share event\r\n        this.trackStoryShare(currentStory._id);\r\n      }).catch((error) => {\r\n        console.error('Error sharing story:', error);\r\n      });\r\n    } else {\r\n      // Fallback: copy link to clipboard\r\n      const shareUrl = `${environment.frontendUrl}/story/${currentStory._id}`;\r\n      navigator.clipboard.writeText(shareUrl).then(() => {\r\n        console.log('Story link copied to clipboard');\r\n        this.trackStoryShare(currentStory._id);\r\n        // Show toast notification\r\n        this.showToast('Story link copied to clipboard!');\r\n      }).catch((error) => {\r\n        console.error('Error copying to clipboard:', error);\r\n      });\r\n    }\r\n  }\r\n\r\n  saveStory() {\r\n    if (!this.authService.isAuthenticated) {\r\n      this.router.navigate(['/auth/login']);\r\n      return;\r\n    }\r\n\r\n    const currentStory = this.getCurrentStory();\r\n\r\n    // Call API to save story\r\n    this.http.post(`${environment.apiUrl}/stories/${currentStory._id}/save`, {}).subscribe({\r\n      next: (response) => {\r\n        console.log('Story saved successfully:', response);\r\n        this.showToast('Story saved to your collection!');\r\n        // Emit real-time event\r\n        this.realtimeService.emitEvent('story:saved', {\r\n          storyId: currentStory._id,\r\n          userId: this.authService.currentUserValue?._id\r\n        });\r\n      },\r\n      error: (error) => {\r\n        console.error('Error saving story:', error);\r\n        this.showToast('Error saving story. Please try again.');\r\n      }\r\n    });\r\n  }\r\n\r\n  private trackStoryShare(storyId: string) {\r\n    if (this.authService.isAuthenticated) {\r\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/share`, {}).subscribe({\r\n        next: (response) => {\r\n          console.log('Story share tracked:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error tracking story share:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  // Handle middle area click for product/category navigation\r\n  handleMiddleAreaClick(event: Event) {\r\n    event.stopPropagation();\r\n    const currentStory = this.getCurrentStory();\r\n\r\n    if (!currentStory?.linkedContent) {\r\n      return;\r\n    }\r\n\r\n    console.log('Middle area clicked, navigating to:', currentStory.linkedContent);\r\n\r\n    switch (currentStory.linkedContent.type) {\r\n      case 'product':\r\n        if (currentStory.linkedContent.productId) {\r\n          this.router.navigate(['/product', currentStory.linkedContent.productId]);\r\n        } else if (currentStory.products && currentStory.products.length > 0) {\r\n          // Fallback to first product if no specific productId\r\n          this.router.navigate(['/product', currentStory.products[0].product._id]);\r\n        }\r\n        break;\r\n\r\n      case 'category':\r\n        if (currentStory.linkedContent.categoryId) {\r\n          this.router.navigate(['/shop'], {\r\n            queryParams: { category: currentStory.linkedContent.categoryId }\r\n          });\r\n        }\r\n        break;\r\n\r\n      case 'brand':\r\n        if (currentStory.linkedContent.brandId) {\r\n          this.router.navigate(['/shop'], {\r\n            queryParams: { brand: currentStory.linkedContent.brandId }\r\n          });\r\n        }\r\n        break;\r\n\r\n      case 'collection':\r\n        if (currentStory.linkedContent.collectionId) {\r\n          this.router.navigate(['/collection', currentStory.linkedContent.collectionId]);\r\n        }\r\n        break;\r\n\r\n      default:\r\n        console.warn('Unknown linked content type:', currentStory.linkedContent.type);\r\n    }\r\n\r\n    // Track click event\r\n    this.trackLinkedContentClick(currentStory._id, currentStory.linkedContent);\r\n  }\r\n\r\n  // Get text for linked content indicator\r\n  getLinkedContentText(): string {\r\n    const currentStory = this.getCurrentStory();\r\n\r\n    if (!currentStory?.linkedContent) {\r\n      return '';\r\n    }\r\n\r\n    switch (currentStory.linkedContent.type) {\r\n      case 'product':\r\n        return 'View Product';\r\n      case 'category':\r\n        return 'Browse Category';\r\n      case 'brand':\r\n        return 'View Brand';\r\n      case 'collection':\r\n        return 'View Collection';\r\n      default:\r\n        return 'View Details';\r\n    }\r\n  }\r\n\r\n  // Track linked content click for analytics\r\n  private trackLinkedContentClick(storyId: string, linkedContent: any) {\r\n    if (this.authService.isAuthenticated) {\r\n      this.http.post(`${environment.apiUrl}/stories/${storyId}/click`, {\r\n        contentType: linkedContent.type,\r\n        contentId: linkedContent.productId || linkedContent.categoryId || linkedContent.brandId || linkedContent.collectionId\r\n      }).subscribe({\r\n        next: (response) => {\r\n          console.log('Linked content click tracked:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error tracking linked content click:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  private showToast(message: string) {\r\n    // Simple toast implementation - you can replace with your preferred toast library\r\n    const toast = document.createElement('div');\r\n    toast.textContent = message;\r\n    toast.style.cssText = `\r\n      position: fixed;\r\n      bottom: 20px;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      background: rgba(0, 0, 0, 0.8);\r\n      color: white;\r\n      padding: 12px 24px;\r\n      border-radius: 8px;\r\n      z-index: 10000;\r\n      font-size: 14px;\r\n    `;\r\n    document.body.appendChild(toast);\r\n\r\n    setTimeout(() => {\r\n      document.body.removeChild(toast);\r\n    }, 3000);\r\n  }\r\n\r\n  // Add story functionality\r\n  onAdd() {\r\n    this.router.navigate(['/stories/create']);\r\n  }\r\n\r\n  // Utility methods\r\n  getTimeAgo(dateString: string): string {\r\n    const now = new Date();\r\n    const date = new Date(dateString);\r\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\r\n\r\n    if (diffInMinutes < 1) return 'now';\r\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\r\n\r\n    const diffInHours = Math.floor(diffInMinutes / 60);\r\n    if (diffInHours < 24) return `${diffInHours}h`;\r\n\r\n    const diffInDays = Math.floor(diffInHours / 24);\r\n    return `${diffInDays}d`;\r\n  }\r\n\r\n  private pauseAllVideos() {\r\n    const videos = document.querySelectorAll('video');\r\n    videos.forEach(video => {\r\n      if (video.pause) {\r\n        video.pause();\r\n      }\r\n    });\r\n  }\r\n\r\n  @HostListener('document:keydown', ['$event'])\r\n  handleKeydown(event: KeyboardEvent) {\r\n    if (!this.isOpen) return;\r\n\r\n    switch (event.key) {\r\n      case 'ArrowLeft':\r\n        this.previousStory();\r\n        break;\r\n      case 'ArrowRight':\r\n        this.nextStory();\r\n        break;\r\n      case 'Escape':\r\n        this.closeStories();\r\n        break;\r\n    }\r\n  }\r\n}", "<div class=\"stories-container\" #storiesContainer>\n  <!-- Stories Header -->\n  <div class=\"stories-header\">\n    <h3 class=\"stories-title\">Stories</h3>\n    <button class=\"create-story-btn\"\n            (click)=\"onAdd()\"\n            type=\"button\"\n            aria-label=\"Create new story\"\n            title=\"Create a new story\">\n      <i class=\"fas fa-plus\" aria-hidden=\"true\"></i>\n      <span>Create</span>\n    </button>\n  </div>\n\n  <!-- Stories Slider with Navigation -->\n  <div class=\"stories-slider-wrapper\">\n    <!-- Navigation Arrow Left -->\n    <button class=\"nav-arrow nav-arrow-left\"\n            (click)=\"scrollLeft()\"\n            [disabled]=\"!canScrollLeft\"\n            [class.hidden]=\"!showNavArrows\"\n            type=\"button\"\n            aria-label=\"Scroll stories left\"\n            title=\"Previous stories\">\n      <i class=\"fas fa-chevron-left\" aria-hidden=\"true\"></i>\n    </button>\n\n    <!-- Custom Stories Slider -->\n    <div class=\"stories-slider\" #storiesSlider>\n      <div class=\"stories-track\" #storiesTrack [style.transform]=\"'translateX(' + translateX + 'px)'\">\n        <!-- Add Story Button -->\n        <div class=\"story-item add-story-item\" (click)=\"onAdd()\">\n          <div class=\"story-avatar-container\">\n            <div class=\"story-avatar add-avatar\">\n              <div class=\"story-avatar-inner\">\n                <img\n                  class=\"story-avatar-img\"\n                  [src]=\"currentUser?.avatar || 'assets/default-avatar.png'\"\n                  alt=\"Your Story\"\n                />\n                <span class=\"add-story-plus\">+</span>\n              </div>\n            </div>\n          </div>\n          <div class=\"story-username\">Your Story</div>\n        </div>\n\n        <!-- Existing Stories -->\n        <div class=\"story-item\"\n             *ngFor=\"let story of stories; let i = index\"\n             (click)=\"openStory(story, i)\">\n          <div class=\"story-avatar-container\">\n            <div class=\"story-avatar\" [class.has-products]=\"story.products && story.products.length > 0\">\n              <div class=\"story-avatar-inner\">\n                <img\n                  class=\"story-avatar-img\"\n                  [src]=\"story.user.avatar\"\n                  [alt]=\"story.user.username\"\n                />\n                <!-- Shopping bag indicator -->\n                <div class=\"shopping-bag-indicator\"\n                     *ngIf=\"story.products && story.products.length > 0\"\n                     title=\"Shoppable content\">\n                  <i class=\"fas fa-shopping-bag\"></i>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"story-username\">{{ story.user.username }}</div>\n          <!-- Product count badge -->\n          <div class=\"product-count-badge\"\n               *ngIf=\"story.products && story.products.length > 0\">\n            {{ story.products.length }} item{{ story.products.length > 1 ? 's' : '' }}\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Navigation Arrow Right -->\n    <button class=\"nav-arrow nav-arrow-right\"\n            (click)=\"scrollRight()\"\n            [disabled]=\"!canScrollRight\"\n            [class.hidden]=\"!showNavArrows\"\n            type=\"button\"\n            aria-label=\"Scroll stories right\"\n            title=\"Next stories\">\n      <i class=\"fas fa-chevron-right\" aria-hidden=\"true\"></i>\n    </button>\n  </div>\n</div>\n\n<!-- Fullscreen Stories Viewer (shown when isOpen is true) -->\n<div class=\"stories-overlay\" *ngIf=\"isOpen\">\n  <div class=\"stories-content\" #feedCover>\n    <!-- Progress Bars -->\n    <div class=\"progress-container\">\n      <div class=\"progress-bar\"\n           *ngFor=\"let story of stories; let i = index\"\n           [class.active]=\"i === currentStoryIndex\"\n           [class.completed]=\"i < currentStoryIndex\">\n        <div class=\"progress-fill\"\n             [style.width.%]=\"getProgressWidth(i)\"></div>\n      </div>\n    </div>\n\n    <!-- Story Header -->\n    <div class=\"story-header\">\n      <img class=\"story-header-avatar\" [src]=\"getCurrentStory().user.avatar\" alt=\"Avatar\"/>\n      <div class=\"story-header-info\">\n        <span class=\"username\">{{ getCurrentStory().user.username }}</span>\n        <span class=\"time\">{{ getTimeAgo(getCurrentStory().createdAt) }}</span>\n      </div>\n      <button (click)=\"closeStories()\" class=\"close-btn\">\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n\n    <!-- Story Media -->\n    <div class=\"story-media\"\n         (click)=\"onStoryClick($event)\"\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\">\n\n      <!-- Image Story -->\n      <img *ngIf=\"getCurrentStory().mediaType === 'image'\"\n           [src]=\"getCurrentStory().mediaUrl\"\n           class=\"story-image\"/>\n\n      <!-- Video Story -->\n      <video *ngIf=\"getCurrentStory().mediaType === 'video'\"\n             class=\"story-video\"\n             [src]=\"getCurrentStory().mediaUrl\"\n             autoplay muted #storyVideo></video>\n\n      <!-- Middle Click Area for Product/Category Navigation -->\n      <div class=\"story-middle-click-area\"\n           (click)=\"handleMiddleAreaClick($event)\"\n           *ngIf=\"getCurrentStory()?.linkedContent\"\n           title=\"View linked {{ getCurrentStory()?.linkedContent?.type }}\">\n        <div class=\"middle-click-indicator\">\n          <i class=\"fas fa-external-link-alt\"></i>\n          <span>{{ getLinkedContentText() }}</span>\n        </div>\n      </div>\n\n      <!-- Product Tags -->\n      <div class=\"product-tags\" *ngIf=\"showProductTags && getCurrentStory()?.products\">\n        <div class=\"product-tag\"\n             *ngFor=\"let productTag of getCurrentStory()?.products\"\n             [style.left.%]=\"productTag.position?.x || 50\"\n             [style.top.%]=\"productTag.position?.y || 50\"\n             (click)=\"openProductDetails(productTag.product); $event.stopPropagation()\">\n          <div class=\"product-tag-dot\"></div>\n          <div class=\"product-tag-info\">\n            <img [src]=\"productTag.product.images[0].url\"\n                 [alt]=\"productTag.product.name\"\n                 class=\"product-tag-image\">\n            <div class=\"product-tag-details\">\n              <span class=\"product-tag-name\">{{ productTag.product.name }}</span>\n              <span class=\"product-tag-price\">${{ productTag.product.price }}</span>\n              <div class=\"product-tag-actions\">\n                <button class=\"product-action-btn cart-btn\"\n                        (click)=\"addToCart(productTag.product); $event.stopPropagation()\"\n                        type=\"button\"\n                        title=\"Add to Cart\">\n                  <i class=\"fas fa-shopping-cart\"></i>\n                </button>\n                <button class=\"product-action-btn wishlist-btn\"\n                        (click)=\"addToWishlist(productTag.product); $event.stopPropagation()\"\n                        type=\"button\"\n                        title=\"Add to Wishlist\">\n                  <i class=\"fas fa-heart\"></i>\n                </button>\n                <button class=\"product-action-btn buy-btn\"\n                        (click)=\"openProductDetails(productTag.product); $event.stopPropagation()\"\n                        type=\"button\"\n                        title=\"Buy Now\">\n                  <i class=\"fas fa-bolt\"></i>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Shopping Bag Button -->\n      <button class=\"shopping-bag-btn\"\n              *ngIf=\"hasProducts()\"\n              (click)=\"toggleProductTags(); $event.stopPropagation()\"\n              [class.active]=\"showProductTags\"\n              type=\"button\"\n              [attr.aria-label]=\"'View ' + getProductCount() + ' product' + (getProductCount() > 1 ? 's' : '')\"\n              [title]=\"'View ' + getProductCount() + ' product' + (getProductCount() > 1 ? 's' : '')\">\n        <i class=\"fas fa-shopping-bag\" aria-hidden=\"true\"></i>\n        <span class=\"product-count\" aria-hidden=\"true\">{{ getProductCount() }}</span>\n      </button>\n    </div>\n\n    <!-- Story Actions -->\n    <div class=\"story-actions\">\n      <button class=\"action-btn like-btn\"\n              (click)=\"toggleLike()\"\n              [class.liked]=\"isLiked\"\n              type=\"button\"\n              [attr.aria-label]=\"isLiked ? 'Unlike story' : 'Like story'\"\n              [title]=\"isLiked ? 'Unlike story' : 'Like story'\">\n        <i class=\"fas fa-heart\" aria-hidden=\"true\"></i>\n      </button>\n      <button class=\"action-btn share-btn\"\n              (click)=\"shareStory()\"\n              type=\"button\"\n              aria-label=\"Share story\"\n              title=\"Share story\">\n        <i class=\"fas fa-share\" aria-hidden=\"true\"></i>\n      </button>\n      <button class=\"action-btn save-btn\"\n              (click)=\"saveStory()\"\n              type=\"button\"\n              aria-label=\"Save story\"\n              title=\"Save story\">\n        <i class=\"fas fa-bookmark\" aria-hidden=\"true\"></i>\n      </button>\n    </div>\n\n    <!-- Navigation Arrows -->\n    <button class=\"story-nav-btn story-nav-prev\"\n            (click)=\"previousStory()\"\n            *ngIf=\"currentStoryIndex > 0\">\n      <i class=\"fas fa-chevron-left\"></i>\n    </button>\n    <button class=\"story-nav-btn story-nav-next\"\n            (click)=\"nextStory()\"\n            *ngIf=\"currentStoryIndex < stories.length - 1\">\n      <i class=\"fas fa-chevron-right\"></i>\n    </button>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;;;;;;;;;ICsD1CC,EAAA,CAAAC,cAAA,cAE+B;IAC7BD,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMZH,EAAA,CAAAC,cAAA,cACyD;IACvDD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,QAAA,CAAAC,QAAA,CAAAC,MAAA,WAAAF,QAAA,CAAAC,QAAA,CAAAC,MAAA,qBACF;;;;;;IAzBFT,EAAA,CAAAC,cAAA,cAEmC;IAA9BD,EAAA,CAAAU,UAAA,mBAAAC,6DAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAP,QAAA,GAAAK,MAAA,CAAAG,SAAA;MAAA,MAAAC,IAAA,GAAAJ,MAAA,CAAAK,KAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAG,SAAA,CAAAd,QAAA,EAAAS,IAAA,CAAmB;IAAA,EAAC;IAG5BhB,EAFJ,CAAAC,cAAA,cAAoC,cAC2D,cAC3D;IAC9BD,EAAA,CAAAE,SAAA,cAIE;IAEFF,EAAA,CAAAsB,UAAA,IAAAC,6CAAA,kBAE+B;IAKrCvB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAI,MAAA,GAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAE3DH,EAAA,CAAAsB,UAAA,IAAAE,6CAAA,kBACyD;IAG3DxB,EAAA,CAAAG,YAAA,EAAM;;;;IAtBwBH,EAAA,CAAAK,SAAA,GAAkE;IAAlEL,EAAA,CAAAyB,WAAA,iBAAAlB,QAAA,CAAAC,QAAA,IAAAD,QAAA,CAAAC,QAAA,CAAAC,MAAA,KAAkE;IAItFT,EAAA,CAAAK,SAAA,GAAyB;IACzBL,EADA,CAAA0B,UAAA,QAAAnB,QAAA,CAAAoB,IAAA,CAAAC,MAAA,EAAA5B,EAAA,CAAA6B,aAAA,CAAyB,QAAAtB,QAAA,CAAAoB,IAAA,CAAAG,QAAA,CACE;IAIvB9B,EAAA,CAAAK,SAAA,EAAiD;IAAjDL,EAAA,CAAA0B,UAAA,SAAAnB,QAAA,CAAAC,QAAA,IAAAD,QAAA,CAAAC,QAAA,CAAAC,MAAA,KAAiD;IAOjCT,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAA+B,iBAAA,CAAAxB,QAAA,CAAAoB,IAAA,CAAAG,QAAA,CAAyB;IAG/C9B,EAAA,CAAAK,SAAA,EAAiD;IAAjDL,EAAA,CAAA0B,UAAA,SAAAnB,QAAA,CAAAC,QAAA,IAAAD,QAAA,CAAAC,QAAA,CAAAC,MAAA,KAAiD;;;;;IAyB3DT,EAAA,CAAAC,cAAA,cAG+C;IAC7CD,EAAA,CAAAE,SAAA,cACiD;IACnDF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHDH,EADA,CAAAyB,WAAA,WAAAO,IAAA,KAAAd,MAAA,CAAAe,iBAAA,CAAwC,cAAAD,IAAA,GAAAd,MAAA,CAAAe,iBAAA,CACC;IAEvCjC,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAkC,WAAA,UAAAhB,MAAA,CAAAiB,gBAAA,CAAAH,IAAA,OAAqC;;;;;IAwB5ChC,EAAA,CAAAE,SAAA,cAE0B;;;;IADrBF,EAAA,CAAA0B,UAAA,QAAAR,MAAA,CAAAkB,eAAA,GAAAC,QAAA,EAAArC,EAAA,CAAA6B,aAAA,CAAkC;;;;;IAIvC7B,EAAA,CAAAE,SAAA,mBAG0C;;;;IADnCF,EAAA,CAAA0B,UAAA,QAAAR,MAAA,CAAAkB,eAAA,GAAAC,QAAA,EAAArC,EAAA,CAAA6B,aAAA,CAAkC;;;;;;IAIzC7B,EAAA,CAAAC,cAAA,cAGsE;IAFjED,EAAA,CAAAU,UAAA,mBAAA4B,oEAAAC,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAA2B,GAAA;MAAA,MAAAtB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAuB,qBAAA,CAAAF,MAAA,CAA6B;IAAA,EAAC;IAG1CvC,EAAA,CAAAC,cAAA,cAAoC;IAClCD,EAAA,CAAAE,SAAA,YAAwC;IACxCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAA4B;IAEtCJ,EAFsC,CAAAG,YAAA,EAAO,EACrC,EACF;;;;;IALDH,EAAA,CAAA0C,sBAAA,2BAAAC,OAAA,GAAAzB,MAAA,CAAAkB,eAAA,qBAAAO,OAAA,CAAAC,aAAA,kBAAAD,OAAA,CAAAC,aAAA,CAAAC,IAAA,KAAgE;IAG3D7C,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAA4B,oBAAA,GAA4B;;;;;;IAMpC9C,EAAA,CAAAC,cAAA,cAIgF;IAA3ED,EAAA,CAAAU,UAAA,mBAAAqC,0EAAAR,MAAA;MAAA,MAAAS,cAAA,GAAAhD,EAAA,CAAAa,aAAA,CAAAoC,IAAA,EAAAlC,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAASD,MAAA,CAAAgC,kBAAA,CAAAF,cAAA,CAAAG,OAAA,CAAsC;MAAA,OAAAnD,EAAA,CAAAoB,WAAA,CAAEmB,MAAA,CAAAa,eAAA,EAAwB;IAAA,EAAC;IAC7EpD,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,cAE+B;IAE7BF,EADF,CAAAC,cAAA,cAAiC,eACA;IAAAD,EAAA,CAAAI,MAAA,GAA6B;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAI,MAAA,GAA+B;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAEpEH,EADF,CAAAC,cAAA,cAAiC,kBAIH;IAFpBD,EAAA,CAAAU,UAAA,mBAAA2C,8EAAAd,MAAA;MAAA,MAAAS,cAAA,GAAAhD,EAAA,CAAAa,aAAA,CAAAoC,IAAA,EAAAlC,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAASD,MAAA,CAAAoC,SAAA,CAAAN,cAAA,CAAAG,OAAA,CAA6B;MAAA,OAAAnD,EAAA,CAAAoB,WAAA,CAAEmB,MAAA,CAAAa,eAAA,EAAwB;IAAA,EAAC;IAGvEpD,EAAA,CAAAE,SAAA,aAAoC;IACtCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGgC;IAFxBD,EAAA,CAAAU,UAAA,mBAAA6C,8EAAAhB,MAAA;MAAA,MAAAS,cAAA,GAAAhD,EAAA,CAAAa,aAAA,CAAAoC,IAAA,EAAAlC,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAASD,MAAA,CAAAsC,aAAA,CAAAR,cAAA,CAAAG,OAAA,CAAiC;MAAA,OAAAnD,EAAA,CAAAoB,WAAA,CAAEmB,MAAA,CAAAa,eAAA,EAAwB;IAAA,EAAC;IAG3EpD,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGwB;IAFhBD,EAAA,CAAAU,UAAA,mBAAA+C,8EAAAlB,MAAA;MAAA,MAAAS,cAAA,GAAAhD,EAAA,CAAAa,aAAA,CAAAoC,IAAA,EAAAlC,SAAA;MAAA,MAAAG,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAASD,MAAA,CAAAgC,kBAAA,CAAAF,cAAA,CAAAG,OAAA,CAAsC;MAAA,OAAAnD,EAAA,CAAAoB,WAAA,CAAEmB,MAAA,CAAAa,eAAA,EAAwB;IAAA,EAAC;IAGhFpD,EAAA,CAAAE,SAAA,aAA2B;IAKrCF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAhCDH,EADA,CAAAkC,WAAA,UAAAc,cAAA,CAAAU,QAAA,kBAAAV,cAAA,CAAAU,QAAA,CAAAC,CAAA,aAA6C,SAAAX,cAAA,CAAAU,QAAA,kBAAAV,cAAA,CAAAU,QAAA,CAAAE,CAAA,aACD;IAIxC5D,EAAA,CAAAK,SAAA,GAAwC;IACxCL,EADA,CAAA0B,UAAA,QAAAsB,cAAA,CAAAG,OAAA,CAAAU,MAAA,IAAAC,GAAA,EAAA9D,EAAA,CAAA6B,aAAA,CAAwC,QAAAmB,cAAA,CAAAG,OAAA,CAAAY,IAAA,CACT;IAGH/D,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAA+B,iBAAA,CAAAiB,cAAA,CAAAG,OAAA,CAAAY,IAAA,CAA6B;IAC5B/D,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAgE,kBAAA,MAAAhB,cAAA,CAAAG,OAAA,CAAAc,KAAA,KAA+B;;;;;IAbvEjE,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAsB,UAAA,IAAA4C,oDAAA,mBAIgF;IAgClFlE,EAAA,CAAAG,YAAA,EAAM;;;;;IAnCwBH,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAA0B,UAAA,aAAAiB,OAAA,GAAAzB,MAAA,CAAAkB,eAAA,qBAAAO,OAAA,CAAAnC,QAAA,CAA8B;;;;;;IAsC5DR,EAAA,CAAAC,cAAA,iBAMgG;IAJxFD,EAAA,CAAAU,UAAA,mBAAAyD,0EAAA5B,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAuD,IAAA;MAAA,MAAAlD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAASD,MAAA,CAAAmD,iBAAA,EAAmB;MAAA,OAAArE,EAAA,CAAAoB,WAAA,CAAEmB,MAAA,CAAAa,eAAA,EAAwB;IAAA,EAAC;IAK7DpD,EAAA,CAAAE,SAAA,YAAsD;IACtDF,EAAA,CAAAC,cAAA,eAA+C;IAAAD,EAAA,CAAAI,MAAA,GAAuB;IACxEJ,EADwE,CAAAG,YAAA,EAAO,EACtE;;;;IANDH,EAAA,CAAAyB,WAAA,WAAAP,MAAA,CAAAoD,eAAA,CAAgC;IAGhCtE,EAAA,CAAA0B,UAAA,oBAAAR,MAAA,CAAAqD,eAAA,mBAAArD,MAAA,CAAAqD,eAAA,mBAAuF;;IAE9CvE,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAAqD,eAAA,GAAuB;;;;;;IA+B1EvE,EAAA,CAAAC,cAAA,iBAEsC;IAD9BD,EAAA,CAAAU,UAAA,mBAAA8D,0EAAA;MAAAxE,EAAA,CAAAa,aAAA,CAAA4D,IAAA;MAAA,MAAAvD,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAwD,aAAA,EAAe;IAAA,EAAC;IAE/B1E,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAEuD;IAD/CD,EAAA,CAAAU,UAAA,mBAAAiE,0EAAA;MAAA3E,EAAA,CAAAa,aAAA,CAAA+D,IAAA;MAAA,MAAA1D,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAA2D,SAAA,EAAW;IAAA,EAAC;IAE3B7E,EAAA,CAAAE,SAAA,YAAoC;IACtCF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA5ITH,EAHJ,CAAAC,cAAA,cAA4C,iBACF,cAEN;IAC9BD,EAAA,CAAAsB,UAAA,IAAAwD,6CAAA,kBAG+C;IAIjD9E,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAE,SAAA,cAAqF;IAEnFF,EADF,CAAAC,cAAA,cAA+B,eACN;IAAAD,EAAA,CAAAI,MAAA,GAAqC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAI,MAAA,IAA6C;IAClEJ,EADkE,CAAAG,YAAA,EAAO,EACnE;IACNH,EAAA,CAAAC,cAAA,kBAAmD;IAA3CD,EAAA,CAAAU,UAAA,mBAAAqE,iEAAA;MAAA/E,EAAA,CAAAa,aAAA,CAAAmE,GAAA;MAAA,MAAA9D,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAA+D,YAAA,EAAc;IAAA,EAAC;IAC9BjF,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAC,cAAA,eAIqC;IAAhCD,EAHA,CAAAU,UAAA,mBAAAwE,8DAAA3C,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAmE,GAAA;MAAA,MAAA9D,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAiE,YAAA,CAAA5C,MAAA,CAAoB;IAAA,EAAC,wBAAA6C,mEAAA7C,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAmE,GAAA;MAAA,MAAA9D,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAChBF,MAAA,CAAAmE,YAAA,CAAA9C,MAAA,CAAoB;IAAA,EAAC,uBAAA+C,kEAAA/C,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAmE,GAAA;MAAA,MAAA9D,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACtBF,MAAA,CAAAqE,WAAA,CAAAhD,MAAA,CAAmB;IAAA,EAAC,sBAAAiD,iEAAAjD,MAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAAmE,GAAA;MAAA,MAAA9D,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACrBF,MAAA,CAAAuE,UAAA,CAAAlD,MAAA,CAAkB;IAAA,EAAC;IAiElCvC,EA9DA,CAAAsB,UAAA,KAAAoE,8CAAA,kBAE0B,KAAAC,gDAAA,oBAMQ,KAAAC,8CAAA,kBAMoC,KAAAC,8CAAA,kBAQW,KAAAC,iDAAA,qBA8Ce;IAIlG9F,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA2B,kBAMiC;IAJlDD,EAAA,CAAAU,UAAA,mBAAAqF,iEAAA;MAAA/F,EAAA,CAAAa,aAAA,CAAAmE,GAAA;MAAA,MAAA9D,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAA8E,UAAA,EAAY;IAAA,EAAC;IAK5BhG,EAAA,CAAAE,SAAA,aAA+C;IACjDF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAI4B;IAHpBD,EAAA,CAAAU,UAAA,mBAAAuF,iEAAA;MAAAjG,EAAA,CAAAa,aAAA,CAAAmE,GAAA;MAAA,MAAA9D,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAgF,UAAA,EAAY;IAAA,EAAC;IAI5BlG,EAAA,CAAAE,SAAA,aAA+C;IACjDF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAI2B;IAHnBD,EAAA,CAAAU,UAAA,mBAAAyF,iEAAA;MAAAnG,EAAA,CAAAa,aAAA,CAAAmE,GAAA;MAAA,MAAA9D,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAkF,SAAA,EAAW;IAAA,EAAC;IAI3BpG,EAAA,CAAAE,SAAA,aAAkD;IAEtDF,EADE,CAAAG,YAAA,EAAS,EACL;IAQNH,EALA,CAAAsB,UAAA,KAAA+E,iDAAA,qBAEsC,KAAAC,iDAAA,qBAKiB;IAI3DtG,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;IA5IuBH,EAAA,CAAAK,SAAA,GAAY;IAAZL,EAAA,CAAA0B,UAAA,YAAAR,MAAA,CAAAqF,OAAA,CAAY;IAUFvG,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAA0B,UAAA,QAAAR,MAAA,CAAAkB,eAAA,GAAAT,IAAA,CAAAC,MAAA,EAAA5B,EAAA,CAAA6B,aAAA,CAAqC;IAE7C7B,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAAkB,eAAA,GAAAT,IAAA,CAAAG,QAAA,CAAqC;IACzC9B,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAA+B,iBAAA,CAAAb,MAAA,CAAAsF,UAAA,CAAAtF,MAAA,CAAAkB,eAAA,GAAAqE,SAAA,EAA6C;IAe5DzG,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAkB,eAAA,GAAAsE,SAAA,aAA6C;IAK3C1G,EAAA,CAAAK,SAAA,EAA6C;IAA7CL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAkB,eAAA,GAAAsE,SAAA,aAA6C;IAQ/C1G,EAAA,CAAAK,SAAA,EAAsC;IAAtCL,EAAA,CAAA0B,UAAA,UAAAiF,QAAA,GAAAzF,MAAA,CAAAkB,eAAA,qBAAAuE,QAAA,CAAA/D,aAAA,CAAsC;IASjB5C,EAAA,CAAAK,SAAA,EAAoD;IAApDL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAoD,eAAA,MAAAsC,QAAA,GAAA1F,MAAA,CAAAkB,eAAA,qBAAAwE,QAAA,CAAApG,QAAA,EAAoD;IAyCtER,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAA2F,WAAA,GAAmB;IAepB7G,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAyB,WAAA,UAAAP,MAAA,CAAA4F,OAAA,CAAuB;IAGvB9G,EAAA,CAAA0B,UAAA,UAAAR,MAAA,CAAA4F,OAAA,iCAAiD;;IAsBlD9G,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAe,iBAAA,KAA2B;IAK3BjC,EAAA,CAAAK,SAAA,EAA4C;IAA5CL,EAAA,CAAA0B,UAAA,SAAAR,MAAA,CAAAe,iBAAA,GAAAf,MAAA,CAAAqF,OAAA,CAAA9F,MAAA,KAA4C;;;ADxKzD,OAAM,MAAOsG,uBAAuB;EAqClCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB,EACxBC,WAAwB,EACxBC,eAAgC,EAChCC,kBAAsC,EACtCC,eAAgC;IANhC,KAAAN,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,eAAe,GAAfA,eAAe;IAvCzB,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAjB,OAAO,GAAY,EAAE;IACrB,KAAAkB,gBAAgB,GAAG,IAAI;IAEvB;IACA,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAzF,iBAAiB,GAAG,CAAC;IACrB,KAAAqC,eAAe,GAAG,KAAK;IACvB,KAAAwC,OAAO,GAAG,KAAK;IAEf;IACA,KAAAa,aAAa,GAAG,KAAK;IACrB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAI;IAEpB;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,SAAS,GAAG,EAAE,CAAC,CAAC;IAChB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IAIb,KAAAC,aAAa,GAAG,KAAK,CAAC,CAAC;IACvB,KAAAC,iBAAiB,GAAG,CAAC;IAE7B;IACQ,KAAAC,cAAc,GAAG,CAAC;IAGlB,KAAAC,aAAa,GAAmB,EAAE;EAUvC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACpB,WAAW,CAACqB,YAAY,CAACC,SAAS,CAAC9G,IAAI,IAAG;MAC7C,IAAI,CAAC6F,WAAW,GAAG7F,IAAI;IACzB,CAAC,CAAC;IAEF;IACA,IAAI,CAAC+G,yBAAyB,EAAE;EAClC;EAEAC,eAAeA,CAAA;IACbC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCC,UAAU,CAAC,MAAK;MACdF,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,IAAI,CAACE,yBAAyB,EAAE;MAChC,IAAI,CAACC,mBAAmB,EAAE;IAC5B,CAAC,EAAE,GAAG,CAAC;IAEP;IACAF,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACvC,OAAO,CAAC9F,MAAM,GAAG,CAAC,EAAE;QAC3BmI,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClD,IAAI,CAACE,yBAAyB,EAAE;QAChC,IAAI,CAACC,mBAAmB,EAAE;;IAE9B,CAAC,EAAE,IAAI,CAAC;EACV;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACZ,aAAa,CAACa,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,eAAe,EAAE;EACxB;EAGAC,QAAQA,CAAA;IACN,IAAI,CAACZ,yBAAyB,EAAE;IAChC,IAAI,CAACK,yBAAyB,EAAE;IAChC,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQN,yBAAyBA,CAAA;IAC/B,IAAI,CAACb,aAAa,GAAG0B,MAAM,CAACC,UAAU,GAAG,GAAG;EAC9C;EAEQT,yBAAyBA,CAAA;IAC/B,IAAI,IAAI,CAACU,aAAa,IAAI,IAAI,CAACA,aAAa,CAACC,aAAa,EAAE;MAC1D,MAAMC,cAAc,GAAG,IAAI,CAACF,aAAa,CAACC,aAAa,CAACE,WAAW;MACnE,MAAMC,WAAW,GAAGN,MAAM,CAACC,UAAU;MAErCZ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAAEc,cAAc;QAAEE;MAAW,CAAE,CAAC;MAE9E;MACA,IAAIA,WAAW,IAAI,GAAG,EAAE;QACtB,IAAI,CAAC7B,YAAY,GAAG,CAAC;QACrB,IAAI,CAACD,SAAS,GAAG4B,cAAc,GAAG,CAAC;OACpC,MAAM,IAAIE,WAAW,IAAI,GAAG,EAAE;QAC7B,IAAI,CAAC7B,YAAY,GAAG,CAAC;QACrB,IAAI,CAACD,SAAS,GAAG4B,cAAc,GAAG,CAAC;OACpC,MAAM;QACL,IAAI,CAAC3B,YAAY,GAAG,CAAC;QACrB,IAAI,CAACD,SAAS,GAAG4B,cAAc,GAAG,CAAC;;MAGrCf,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3Cb,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BD,SAAS,EAAE,IAAI,CAACA;OACjB,CAAC;MAEF;MACA,IAAI,CAACE,iBAAiB,GAAG,CAAC;MAC1B,IAAI,CAACH,UAAU,GAAG,CAAC;KACpB,MAAM;MACLc,OAAO,CAACkB,IAAI,CAAC,4DAA4D,CAAC;MAC1E;MACA,IAAI,CAAC/B,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;EAEzB;EAEAO,WAAWA,CAAA;IACT,IAAI,CAACd,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACY,aAAa,CAAC0B,IAAI,CACrB,IAAI,CAAC7C,IAAI,CAAC8C,GAAG,CAAM,GAAGjK,WAAW,CAACkK,MAAM,UAAU,CAAC,CAACxB,SAAS,CAAC;MAC5DyB,IAAI,EAAGC,QAAQ,IAAI;QACjBvB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsB,QAAQ,CAAC;QAC9C,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAAC5D,OAAO,IAAI4D,QAAQ,CAAC5D,OAAO,CAAC9F,MAAM,GAAG,CAAC,EAAE;UACvE,IAAI,CAAC8F,OAAO,GAAG4D,QAAQ,CAAC5D,OAAO,CAAC8D,GAAG,CAAEC,KAAU,KAAM;YACnD,GAAGA,KAAK;YACRjI,QAAQ,EAAEiI,KAAK,CAACC,KAAK,EAAEzG,GAAG,IAAIwG,KAAK,CAACjI,QAAQ;YAC5CqE,SAAS,EAAE4D,KAAK,CAACC,KAAK,EAAE1H,IAAI,IAAIyH,KAAK,CAAC5D;WACvC,CAAC,CAAC;UACHkC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACtC,OAAO,CAAC;SACtD,MAAM;UACLqC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5D,IAAI,CAAC2B,mBAAmB,EAAE;;QAE5B,IAAI,CAAC/C,gBAAgB,GAAG,KAAK;QAC7BqB,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,yBAAyB,EAAE;UAChC,IAAI,CAACC,mBAAmB,EAAE;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDyB,KAAK,EAAGA,KAAK,IAAI;QACf7B,OAAO,CAAC6B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C7B,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD,IAAI,CAAC2B,mBAAmB,EAAE;QAC1B,IAAI,CAAC/C,gBAAgB,GAAG,KAAK;QAC7BqB,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,yBAAyB,EAAE;UAChC,IAAI,CAACC,mBAAmB,EAAE;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT;KACD,CAAC,CACH;EACH;EAEAwB,mBAAmBA,CAAA;IACjB5B,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1C,IAAI,CAACtC,OAAO,GAAG,CACb;MACEmE,GAAG,EAAE,GAAG;MACR/I,IAAI,EAAE;QACJ+I,GAAG,EAAE,GAAG;QACR5I,QAAQ,EAAE,kBAAkB;QAC5B6I,QAAQ,EAAE,gBAAgB;QAC1B/I,MAAM,EAAE;OACT;MACD2I,KAAK,EAAE;QACL1H,IAAI,EAAE,OAAO;QACbiB,GAAG,EAAE;OACN;MACDzB,QAAQ,EAAE,iEAAiE;MAC3EqE,SAAS,EAAE,OAAO;MAClBkE,OAAO,EAAE,iDAAiD;MAC1DnE,SAAS,EAAE,IAAIoE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEE,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,IAAI;MACd1K,QAAQ,EAAE,CACR;QACEkK,GAAG,EAAE,IAAI;QACTvH,OAAO,EAAE;UACPuH,GAAG,EAAE,OAAO;UACZ3G,IAAI,EAAE,sBAAsB;UAC5BE,KAAK,EAAE,KAAK;UACZJ,MAAM,EAAE,CAAC;YAAEC,GAAG,EAAE,iEAAiE;YAAEqH,SAAS,EAAE;UAAI,CAAE;SACrG;QACDzH,QAAQ,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE;OACzB;KAEJ,EACD;MACE8G,GAAG,EAAE,GAAG;MACR/I,IAAI,EAAE;QACJ+I,GAAG,EAAE,GAAG;QACR5I,QAAQ,EAAE,iBAAiB;QAC3B6I,QAAQ,EAAE,WAAW;QACrB/I,MAAM,EAAE;OACT;MACD2I,KAAK,EAAE;QACL1H,IAAI,EAAE,OAAO;QACbiB,GAAG,EAAE;OACN;MACDzB,QAAQ,EAAE,oEAAoE;MAC9EqE,SAAS,EAAE,OAAO;MAClBkE,OAAO,EAAE,8CAA8C;MACvDnE,SAAS,EAAE,IAAIoE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEE,KAAK,EAAE,GAAG;MACVC,QAAQ,EAAE,IAAI;MACd1K,QAAQ,EAAE,CACR;QACEkK,GAAG,EAAE,IAAI;QACTvH,OAAO,EAAE;UACPuH,GAAG,EAAE,OAAO;UACZ3G,IAAI,EAAE,iBAAiB;UACvBE,KAAK,EAAE,MAAM;UACbJ,MAAM,EAAE,CAAC;YAAEC,GAAG,EAAE,oEAAoE;YAAEqH,SAAS,EAAE;UAAI,CAAE;SACxG;QACDzH,QAAQ,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE;OACzB;KAEJ,EACD;MACE8G,GAAG,EAAE,GAAG;MACR/I,IAAI,EAAE;QACJ+I,GAAG,EAAE,GAAG;QACR5I,QAAQ,EAAE,cAAc;QACxB6I,QAAQ,EAAE,eAAe;QACzB/I,MAAM,EAAE;OACT;MACD2I,KAAK,EAAE;QACL1H,IAAI,EAAE,OAAO;QACbiB,GAAG,EAAE;OACN;MACDzB,QAAQ,EAAE,oEAAoE;MAC9EqE,SAAS,EAAE,OAAO;MAClBkE,OAAO,EAAE,6CAA6C;MACtDnE,SAAS,EAAE,IAAIoE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEE,KAAK,EAAE,GAAG;MACVC,QAAQ,EAAE,IAAI;MACd1K,QAAQ,EAAE,CACR;QACEkK,GAAG,EAAE,IAAI;QACTvH,OAAO,EAAE;UACPuH,GAAG,EAAE,OAAO;UACZ3G,IAAI,EAAE,mBAAmB;UACzBE,KAAK,EAAE,KAAK;UACZJ,MAAM,EAAE,CAAC;YAAEC,GAAG,EAAE,oEAAoE;YAAEqH,SAAS,EAAE;UAAI,CAAE;SACxG;QACDzH,QAAQ,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE;OACzB;KAEJ,EACD;MACE8G,GAAG,EAAE,GAAG;MACR/I,IAAI,EAAE;QACJ+I,GAAG,EAAE,GAAG;QACR5I,QAAQ,EAAE,aAAa;QACvB6I,QAAQ,EAAE,eAAe;QACzB/I,MAAM,EAAE;OACT;MACD2I,KAAK,EAAE;QACL1H,IAAI,EAAE,OAAO;QACbiB,GAAG,EAAE;OACN;MACDzB,QAAQ,EAAE,oEAAoE;MAC9EqE,SAAS,EAAE,OAAO;MAClBkE,OAAO,EAAE,+CAA+C;MACxDnE,SAAS,EAAE,IAAIoE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAClEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEE,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,IAAI;MACd1K,QAAQ,EAAE,CACR;QACEkK,GAAG,EAAE,IAAI;QACTvH,OAAO,EAAE;UACPuH,GAAG,EAAE,OAAO;UACZ3G,IAAI,EAAE,oBAAoB;UAC1BE,KAAK,EAAE,MAAM;UACbJ,MAAM,EAAE,CAAC;YAAEC,GAAG,EAAE,oEAAoE;YAAEqH,SAAS,EAAE;UAAI,CAAE;SACxG;QACDzH,QAAQ,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE;OACzB;KAEJ,EACD;MACE8G,GAAG,EAAE,GAAG;MACR/I,IAAI,EAAE;QACJ+I,GAAG,EAAE,GAAG;QACR5I,QAAQ,EAAE,WAAW;QACrB6I,QAAQ,EAAE,aAAa;QACvB/I,MAAM,EAAE;OACT;MACD2I,KAAK,EAAE;QACL1H,IAAI,EAAE,OAAO;QACbiB,GAAG,EAAE;OACN;MACDzB,QAAQ,EAAE,oEAAoE;MAC9EqE,SAAS,EAAE,OAAO;MAClBkE,OAAO,EAAE,yCAAyC;MAClDnE,SAAS,EAAE,IAAIoE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEE,KAAK,EAAE,GAAG;MACVC,QAAQ,EAAE,IAAI;MACd1K,QAAQ,EAAE,CACR;QACEkK,GAAG,EAAE,IAAI;QACTvH,OAAO,EAAE;UACPuH,GAAG,EAAE,OAAO;UACZ3G,IAAI,EAAE,qBAAqB;UAC3BE,KAAK,EAAE,MAAM;UACbJ,MAAM,EAAE,CAAC;YAAEC,GAAG,EAAE,oEAAoE;YAAEqH,SAAS,EAAE;UAAI,CAAE;SACxG;QACDzH,QAAQ,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE;OACzB;KAEJ,CACF;IACDgF,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACtC,OAAO,CAAC9F,MAAM,EAAE,SAAS,CAAC;EACzE;EAEA;EACA2K,UAAUA,CAAA;IACRxC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAACZ,iBAAiB,CAAC;IAC1E,IAAI,IAAI,CAACA,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACoD,oBAAoB,EAAE;MAC3BzC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACZ,iBAAiB,CAAC;KAC/D,MAAM;MACLW,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;EAEvD;EAEAyC,WAAWA,CAAA;IACT,MAAMC,UAAU,GAAG,IAAI,CAAChF,OAAO,CAAC9F,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5C,MAAM+K,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,UAAU,GAAG,IAAI,CAACvD,YAAY,CAAC;IAEjEY,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;MACnC8C,YAAY,EAAE,IAAI,CAAC1D,iBAAiB;MACpCsD,UAAU;MACVC,aAAa;MACbxD,YAAY,EAAE,IAAI,CAACA;KACpB,CAAC;IAEF,IAAI,IAAI,CAACC,iBAAiB,GAAGuD,aAAa,EAAE;MAC1C,IAAI,CAACvD,iBAAiB,EAAE;MACxB,IAAI,CAACoD,oBAAoB,EAAE;MAC3BzC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACZ,iBAAiB,CAAC;KAChE,MAAM;MACLW,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;EAEtD;EAEQwC,oBAAoBA,CAAA;IAC1B,MAAMO,aAAa,GAAG,CAAC,IAAI,CAAC3D,iBAAiB,GAAG,IAAI,CAACF,SAAS;IAC9Da,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvC8C,YAAY,EAAE,IAAI,CAAC1D,iBAAiB;MACpCF,SAAS,EAAE,IAAI,CAACA,SAAS;MACzB6D;KACD,CAAC;IAEF,IAAI,CAAC9D,UAAU,GAAG8D,aAAa;IAC/B,IAAI,CAAC5C,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB,MAAMuC,UAAU,GAAG,IAAI,CAAChF,OAAO,CAAC9F,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5C,MAAM+K,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,UAAU,GAAG,IAAI,CAACvD,YAAY,CAAC;IAEjE,IAAI,CAACL,aAAa,GAAG,IAAI,CAACM,iBAAiB,GAAG,CAAC;IAC/C,IAAI,CAACL,cAAc,GAAG,IAAI,CAACK,iBAAiB,GAAGuD,aAAa;IAE5D5C,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE;MACrClB,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnC2D,UAAU;MACVC,aAAa;MACbG,YAAY,EAAE,IAAI,CAAC1D;KACpB,CAAC;EACJ;EAEA;EACA5G,SAASA,CAACwK,MAAa,EAAE5K,KAAa;IACpC,IAAI,CAACgB,iBAAiB,GAAGhB,KAAK;IAC9B,IAAI,CAACyG,MAAM,GAAG,IAAI;IAClB,IAAI,CAACpD,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACwH,eAAe,EAAE;IACtBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EAEAjH,YAAYA,CAAA;IACV,IAAI,CAACyC,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC2B,eAAe,EAAE;IACtB,IAAI,CAAC8C,cAAc,EAAE;IACrBJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEArH,SAASA,CAAA;IACP,IAAI,IAAI,CAAC5C,iBAAiB,GAAG,IAAI,CAACsE,OAAO,CAAC9F,MAAM,GAAG,CAAC,EAAE;MACpD,IAAI,CAACwB,iBAAiB,EAAE;MACxB,IAAI,CAAC6J,eAAe,EAAE;KACvB,MAAM;MACL,IAAI,CAAC7G,YAAY,EAAE;;EAEvB;EAEAP,aAAaA,CAAA;IACX,IAAI,IAAI,CAACzC,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAAC6J,eAAe,EAAE;;EAE1B;EAEA1J,eAAeA,CAAA;IACb,OAAO,IAAI,CAACmE,OAAO,CAAC,IAAI,CAACtE,iBAAiB,CAAC,IAAI,IAAI,CAACsE,OAAO,CAAC,CAAC,CAAC;EAChE;EAEAM,WAAWA,CAAA;IACT,MAAMyD,KAAK,GAAG,IAAI,CAAClI,eAAe,EAAE;IACpC,OAAO,CAAC,EAAEkI,KAAK,IAAIA,KAAK,CAAC9J,QAAQ,IAAI8J,KAAK,CAAC9J,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;EACjE;EAEA8D,eAAeA,CAAA;IACb,MAAM+F,KAAK,GAAG,IAAI,CAAClI,eAAe,EAAE;IACpC,OAAOkI,KAAK,EAAE9J,QAAQ,EAAEC,MAAM,IAAI,CAAC;EACrC;EAEA;EACA0B,gBAAgBA,CAAClB,KAAa;IAC5B,IAAIA,KAAK,GAAG,IAAI,CAACgB,iBAAiB,EAAE,OAAO,GAAG;IAC9C,IAAIhB,KAAK,GAAG,IAAI,CAACgB,iBAAiB,EAAE,OAAO,CAAC;IAE5C,IAAI,IAAI,CAACkG,iBAAiB,EAAE;MAC1B,MAAMiE,OAAO,GAAGvB,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC3C,iBAAiB;MACnD,OAAOsD,IAAI,CAACY,GAAG,CAAED,OAAO,GAAG,IAAI,CAAClE,aAAa,GAAI,GAAG,EAAE,GAAG,CAAC;;IAE5D,OAAO,CAAC;EACV;EAEQ4D,eAAeA,CAAA;IACrB,IAAI,CAACzC,eAAe,EAAE;IACtB,IAAI,CAAClB,iBAAiB,GAAG0C,IAAI,CAACC,GAAG,EAAE;IAEnC,IAAI,CAACwB,aAAa,GAAGxD,UAAU,CAAC,MAAK;MACnC,IAAI,CAACjE,SAAS,EAAE;IAClB,CAAC,EAAE,IAAI,CAACqD,aAAa,CAAC;EACxB;EAEQmB,eAAeA,CAAA;IACrB,IAAI,IAAI,CAACiD,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;MAChC,IAAI,CAACA,aAAa,GAAG,IAAI;;IAE3B,IAAI,CAACnE,iBAAiB,GAAG,CAAC;EAC5B;EAEA;EACAhD,YAAYA,CAACqH,KAAiB;IAC5B,MAAMC,MAAM,GAAGD,KAAK,CAACE,OAAO;IAC5B,MAAMC,WAAW,GAAGpD,MAAM,CAACC,UAAU;IAErC,IAAIiD,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACjI,aAAa,EAAE;KACrB,MAAM,IAAI+H,MAAM,GAAIE,WAAW,GAAG,CAAC,GAAI,CAAC,EAAE;MACzC,IAAI,CAAC9H,SAAS,EAAE;;EAEpB;EAEA;EACAQ,YAAYA,CAACuH,MAAkB;IAC7B,IAAI,CAACxE,cAAc,GAAGyC,IAAI,CAACC,GAAG,EAAE;IAChC,IAAI,CAAC+B,cAAc,GAAG/D,UAAU,CAAC,MAAK;MACpC,IAAI,CAACO,eAAe,EAAE,CAAC,CAAC;IAC1B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA9D,WAAWA,CAACqH,MAAkB;IAC5B,IAAI,IAAI,CAACC,cAAc,EAAE;MACvBN,YAAY,CAAC,IAAI,CAACM,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;EAE9B;EAEApH,UAAUA,CAAC+G,KAAiB;IAC1B,IAAI,IAAI,CAACK,cAAc,EAAE;MACvBN,YAAY,CAAC,IAAI,CAACM,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;;IAG5B,MAAMC,aAAa,GAAGjC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC1C,cAAc;IACtD,IAAI0E,aAAa,GAAG,GAAG,EAAE;MACvB;MACA,MAAMC,KAAK,GAAGP,KAAK,CAACQ,cAAc,CAAC,CAAC,CAAC;MACrC,IAAI,CAAC7H,YAAY,CAAC;QAAEuH,OAAO,EAAEK,KAAK,CAACL;MAAO,CAAgB,CAAC;KAC5D,MAAM;MACL;MACA,IAAI,CAACZ,eAAe,EAAE;;EAE1B;EAEA;EACAzH,iBAAiBA,CAAA;IACf,IAAI,CAACC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEApB,kBAAkBA,CAACC,OAAY;IAC7B,IAAI,CAAC8D,MAAM,CAACgG,QAAQ,CAAC,CAAC,UAAU,EAAE9J,OAAO,CAACuH,GAAG,CAAC,CAAC;EACjD;EAEA;EACApH,SAASA,CAACH,OAAY;IACpB,IAAI,CAAC,IAAI,CAACgE,WAAW,CAAC+F,eAAe,EAAE;MACrC,IAAI,CAACjG,MAAM,CAACgG,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC;;IAGFrE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE1F,OAAO,CAAC;IAC/C,IAAI,CAACiE,WAAW,CAAC9D,SAAS,CAACH,OAAO,CAACuH,GAAG,EAAE,CAAC,EAAEvH,OAAO,CAACgK,IAAI,EAAEhK,OAAO,CAACiK,KAAK,CAAC,CAAC3E,SAAS,CAAC;MAChFyB,IAAI,EAAGC,QAAQ,IAAI;QACjBvB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEsB,QAAQ,CAAC;QAC5D;MACF,CAAC;MACDM,KAAK,EAAGA,KAAK,IAAI;QACf7B,OAAO,CAAC6B,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;KACD,CAAC;EACJ;EAEA;EACAjH,aAAaA,CAACL,OAAY;IACxB,IAAI,CAAC,IAAI,CAACgE,WAAW,CAAC+F,eAAe,EAAE;MACrC,IAAI,CAACjG,MAAM,CAACgG,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC;;IAGFrE,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE1F,OAAO,CAAC;IACnD,IAAI,CAACkE,eAAe,CAAC7D,aAAa,CAACL,OAAO,CAACuH,GAAG,CAAC,CAACjC,SAAS,CAAC;MACxDyB,IAAI,EAAGC,QAAQ,IAAI;QACjBvB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEsB,QAAQ,CAAC;QAChE;MACF,CAAC;MACDM,KAAK,EAAGA,KAAK,IAAI;QACf7B,OAAO,CAAC6B,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D;KACD,CAAC;EACJ;EAEA;EACAzE,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACmB,WAAW,CAAC+F,eAAe,EAAE;MACrC,IAAI,CAACjG,MAAM,CAACgG,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC;;IAGF,MAAMI,YAAY,GAAG,IAAI,CAACjL,eAAe,EAAE;IAC3C,IAAI,CAAC0E,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAE5B;IACA,MAAMwG,QAAQ,GAAG,IAAI,CAACxG,OAAO,GAAG,MAAM,GAAG,QAAQ;IACjD,IAAI,CAACI,IAAI,CAACqG,IAAI,CAAC,GAAGxN,WAAW,CAACkK,MAAM,YAAYoD,YAAY,CAAC3C,GAAG,IAAI4C,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC7E,SAAS,CAAC;MAC5FyB,IAAI,EAAGC,QAAQ,IAAI;QACjBvB,OAAO,CAACC,GAAG,CAAC,SAASyE,QAAQ,iBAAiB,EAAEnD,QAAQ,CAAC;QACzD;QACA,IAAI,IAAI,CAAC5C,eAAe,CAACiG,WAAW,EAAE,EAAE;UACtC;UACA5E,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;YAChC4E,OAAO,EAAEJ,YAAY,CAAC3C,GAAG;YACzBgD,MAAM,EAAE,IAAI,CAACvG,WAAW,CAACwG,gBAAgB,EAAEjD,GAAG;YAC9CkD,KAAK,EAAE,IAAI,CAAC9G;WACb,CAAC;;MAEN,CAAC;MACD2D,KAAK,EAAGA,KAAK,IAAI;QACf7B,OAAO,CAAC6B,KAAK,CAAC,SAAS6C,QAAQ,YAAY,EAAE7C,KAAK,CAAC;QACnD;QACA,IAAI,CAAC3D,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;MAC9B;KACD,CAAC;EACJ;EAEAZ,UAAUA,CAAA;IACR,MAAMmH,YAAY,GAAG,IAAI,CAACjL,eAAe,EAAE;IAE3C,IAAIyL,SAAS,CAACC,KAAK,EAAE;MACnB;MACAD,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAE,YAAYV,YAAY,CAAC1L,IAAI,CAACG,QAAQ,EAAE;QAC/CkM,IAAI,EAAEX,YAAY,CAACzC,OAAO;QAC1B9G,GAAG,EAAE,GAAG/D,WAAW,CAACkO,WAAW,UAAUZ,YAAY,CAAC3C,GAAG;OAC1D,CAAC,CAACwD,IAAI,CAAC,MAAK;QACXtF,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxC;QACA,IAAI,CAACsF,eAAe,CAACd,YAAY,CAAC3C,GAAG,CAAC;MACxC,CAAC,CAAC,CAAC0D,KAAK,CAAE3D,KAAK,IAAI;QACjB7B,OAAO,CAAC6B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C,CAAC,CAAC;KACH,MAAM;MACL;MACA,MAAM4D,QAAQ,GAAG,GAAGtO,WAAW,CAACkO,WAAW,UAAUZ,YAAY,CAAC3C,GAAG,EAAE;MACvEmD,SAAS,CAACS,SAAS,CAACC,SAAS,CAACF,QAAQ,CAAC,CAACH,IAAI,CAAC,MAAK;QAChDtF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7C,IAAI,CAACsF,eAAe,CAACd,YAAY,CAAC3C,GAAG,CAAC;QACtC;QACA,IAAI,CAAC8D,SAAS,CAAC,iCAAiC,CAAC;MACnD,CAAC,CAAC,CAACJ,KAAK,CAAE3D,KAAK,IAAI;QACjB7B,OAAO,CAAC6B,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD,CAAC,CAAC;;EAEN;EAEArE,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACe,WAAW,CAAC+F,eAAe,EAAE;MACrC,IAAI,CAACjG,MAAM,CAACgG,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;MACrC;;IAGF,MAAMI,YAAY,GAAG,IAAI,CAACjL,eAAe,EAAE;IAE3C;IACA,IAAI,CAAC8E,IAAI,CAACqG,IAAI,CAAC,GAAGxN,WAAW,CAACkK,MAAM,YAAYoD,YAAY,CAAC3C,GAAG,OAAO,EAAE,EAAE,CAAC,CAACjC,SAAS,CAAC;MACrFyB,IAAI,EAAGC,QAAQ,IAAI;QACjBvB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEsB,QAAQ,CAAC;QAClD,IAAI,CAACqE,SAAS,CAAC,iCAAiC,CAAC;QACjD;QACA,IAAI,CAACjH,eAAe,CAACkH,SAAS,CAAC,aAAa,EAAE;UAC5ChB,OAAO,EAAEJ,YAAY,CAAC3C,GAAG;UACzBgD,MAAM,EAAE,IAAI,CAACvG,WAAW,CAACwG,gBAAgB,EAAEjD;SAC5C,CAAC;MACJ,CAAC;MACDD,KAAK,EAAGA,KAAK,IAAI;QACf7B,OAAO,CAAC6B,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAAC+D,SAAS,CAAC,uCAAuC,CAAC;MACzD;KACD,CAAC;EACJ;EAEQL,eAAeA,CAACV,OAAe;IACrC,IAAI,IAAI,CAACtG,WAAW,CAAC+F,eAAe,EAAE;MACpC,IAAI,CAAChG,IAAI,CAACqG,IAAI,CAAC,GAAGxN,WAAW,CAACkK,MAAM,YAAYwD,OAAO,QAAQ,EAAE,EAAE,CAAC,CAAChF,SAAS,CAAC;QAC7EyB,IAAI,EAAGC,QAAQ,IAAI;UACjBvB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEsB,QAAQ,CAAC;QAC/C,CAAC;QACDM,KAAK,EAAGA,KAAK,IAAI;UACf7B,OAAO,CAAC6B,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;OACD,CAAC;;EAEN;EAEA;EACAhI,qBAAqBA,CAAC+J,KAAY;IAChCA,KAAK,CAACpJ,eAAe,EAAE;IACvB,MAAMiK,YAAY,GAAG,IAAI,CAACjL,eAAe,EAAE;IAE3C,IAAI,CAACiL,YAAY,EAAEzK,aAAa,EAAE;MAChC;;IAGFgG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEwE,YAAY,CAACzK,aAAa,CAAC;IAE9E,QAAQyK,YAAY,CAACzK,aAAa,CAACC,IAAI;MACrC,KAAK,SAAS;QACZ,IAAIwK,YAAY,CAACzK,aAAa,CAAC8L,SAAS,EAAE;UACxC,IAAI,CAACzH,MAAM,CAACgG,QAAQ,CAAC,CAAC,UAAU,EAAEI,YAAY,CAACzK,aAAa,CAAC8L,SAAS,CAAC,CAAC;SACzE,MAAM,IAAIrB,YAAY,CAAC7M,QAAQ,IAAI6M,YAAY,CAAC7M,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UACpE;UACA,IAAI,CAACwG,MAAM,CAACgG,QAAQ,CAAC,CAAC,UAAU,EAAEI,YAAY,CAAC7M,QAAQ,CAAC,CAAC,CAAC,CAAC2C,OAAO,CAACuH,GAAG,CAAC,CAAC;;QAE1E;MAEF,KAAK,UAAU;QACb,IAAI2C,YAAY,CAACzK,aAAa,CAAC+L,UAAU,EAAE;UACzC,IAAI,CAAC1H,MAAM,CAACgG,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;YAC9B2B,WAAW,EAAE;cAAEC,QAAQ,EAAExB,YAAY,CAACzK,aAAa,CAAC+L;YAAU;WAC/D,CAAC;;QAEJ;MAEF,KAAK,OAAO;QACV,IAAItB,YAAY,CAACzK,aAAa,CAACkM,OAAO,EAAE;UACtC,IAAI,CAAC7H,MAAM,CAACgG,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;YAC9B2B,WAAW,EAAE;cAAEG,KAAK,EAAE1B,YAAY,CAACzK,aAAa,CAACkM;YAAO;WACzD,CAAC;;QAEJ;MAEF,KAAK,YAAY;QACf,IAAIzB,YAAY,CAACzK,aAAa,CAACoM,YAAY,EAAE;UAC3C,IAAI,CAAC/H,MAAM,CAACgG,QAAQ,CAAC,CAAC,aAAa,EAAEI,YAAY,CAACzK,aAAa,CAACoM,YAAY,CAAC,CAAC;;QAEhF;MAEF;QACEpG,OAAO,CAACkB,IAAI,CAAC,8BAA8B,EAAEuD,YAAY,CAACzK,aAAa,CAACC,IAAI,CAAC;;IAGjF;IACA,IAAI,CAACoM,uBAAuB,CAAC5B,YAAY,CAAC3C,GAAG,EAAE2C,YAAY,CAACzK,aAAa,CAAC;EAC5E;EAEA;EACAE,oBAAoBA,CAAA;IAClB,MAAMuK,YAAY,GAAG,IAAI,CAACjL,eAAe,EAAE;IAE3C,IAAI,CAACiL,YAAY,EAAEzK,aAAa,EAAE;MAChC,OAAO,EAAE;;IAGX,QAAQyK,YAAY,CAACzK,aAAa,CAACC,IAAI;MACrC,KAAK,SAAS;QACZ,OAAO,cAAc;MACvB,KAAK,UAAU;QACb,OAAO,iBAAiB;MAC1B,KAAK,OAAO;QACV,OAAO,YAAY;MACrB,KAAK,YAAY;QACf,OAAO,iBAAiB;MAC1B;QACE,OAAO,cAAc;;EAE3B;EAEA;EACQoM,uBAAuBA,CAACxB,OAAe,EAAE7K,aAAkB;IACjE,IAAI,IAAI,CAACuE,WAAW,CAAC+F,eAAe,EAAE;MACpC,IAAI,CAAChG,IAAI,CAACqG,IAAI,CAAC,GAAGxN,WAAW,CAACkK,MAAM,YAAYwD,OAAO,QAAQ,EAAE;QAC/DyB,WAAW,EAAEtM,aAAa,CAACC,IAAI;QAC/BsM,SAAS,EAAEvM,aAAa,CAAC8L,SAAS,IAAI9L,aAAa,CAAC+L,UAAU,IAAI/L,aAAa,CAACkM,OAAO,IAAIlM,aAAa,CAACoM;OAC1G,CAAC,CAACvG,SAAS,CAAC;QACXyB,IAAI,EAAGC,QAAQ,IAAI;UACjBvB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEsB,QAAQ,CAAC;QACxD,CAAC;QACDM,KAAK,EAAGA,KAAK,IAAI;UACf7B,OAAO,CAAC6B,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC9D;OACD,CAAC;;EAEN;EAEQ+D,SAASA,CAACY,OAAe;IAC/B;IACA,MAAMC,KAAK,GAAGtD,QAAQ,CAACuD,aAAa,CAAC,KAAK,CAAC;IAC3CD,KAAK,CAACE,WAAW,GAAGH,OAAO;IAC3BC,KAAK,CAACpD,KAAK,CAACuD,OAAO,GAAG;;;;;;;;;;;KAWrB;IACDzD,QAAQ,CAACC,IAAI,CAACyD,WAAW,CAACJ,KAAK,CAAC;IAEhCvG,UAAU,CAAC,MAAK;MACdiD,QAAQ,CAACC,IAAI,CAAC0D,WAAW,CAACL,KAAK,CAAC;IAClC,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACAM,KAAKA,CAAA;IACH,IAAI,CAAC1I,MAAM,CAACgG,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEA;EACAzG,UAAUA,CAACoJ,UAAkB;IAC3B,MAAM9E,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMgF,IAAI,GAAG,IAAIhF,IAAI,CAAC+E,UAAU,CAAC;IACjC,MAAME,aAAa,GAAGrE,IAAI,CAACsE,KAAK,CAAC,CAACjF,GAAG,CAACkF,OAAO,EAAE,GAAGH,IAAI,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIF,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAMG,WAAW,GAAGxE,IAAI,CAACsE,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIG,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAGzE,IAAI,CAACsE,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEQ/D,cAAcA,CAAA;IACpB,MAAMgE,MAAM,GAAGpE,QAAQ,CAACqE,gBAAgB,CAAC,OAAO,CAAC;IACjDD,MAAM,CAACjH,OAAO,CAACmH,KAAK,IAAG;MACrB,IAAIA,KAAK,CAACC,KAAK,EAAE;QACfD,KAAK,CAACC,KAAK,EAAE;;IAEjB,CAAC,CAAC;EACJ;EAGAC,aAAaA,CAAC/D,KAAoB;IAChC,IAAI,CAAC,IAAI,CAAC9E,MAAM,EAAE;IAElB,QAAQ8E,KAAK,CAACgE,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAAC9L,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACG,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACI,YAAY,EAAE;QACnB;;EAEN;;;uBAhzBW8B,uBAAuB,EAAA/G,EAAA,CAAAyQ,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA3Q,EAAA,CAAAyQ,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA7Q,EAAA,CAAAyQ,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA/Q,EAAA,CAAAyQ,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAjR,EAAA,CAAAyQ,iBAAA,CAAAS,EAAA,CAAAC,eAAA,GAAAnR,EAAA,CAAAyQ,iBAAA,CAAAW,EAAA,CAAAC,kBAAA,GAAArR,EAAA,CAAAyQ,iBAAA,CAAAa,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAvBxK,uBAAuB;MAAAyK,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UAAvB3R,EAAA,CAAAU,UAAA,oBAAAmR,kDAAAtP,MAAA;YAAA,OAAAqP,GAAA,CAAAtI,QAAA,CAAA/G,MAAA,CAAgB;UAAA,UAAAvC,EAAA,CAAA8R,eAAA,CAAO,qBAAAC,mDAAAxP,MAAA;YAAA,OAAvBqP,GAAA,CAAArB,aAAA,CAAAhO,MAAA,CAAqB;UAAA,UAAAvC,EAAA,CAAAgS,iBAAA,CAAE;;;;;;;;;;;UC9DhChS,EAHJ,CAAAC,cAAA,gBAAiD,aAEnB,YACA;UAAAD,EAAA,CAAAI,MAAA,cAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAC,cAAA,gBAImC;UAH3BD,EAAA,CAAAU,UAAA,mBAAAuR,yDAAA;YAAAjS,EAAA,CAAAa,aAAA,CAAAqR,GAAA;YAAA,OAAAlS,EAAA,CAAAoB,WAAA,CAASwQ,GAAA,CAAAjC,KAAA,EAAO;UAAA,EAAC;UAIvB3P,EAAA,CAAAE,SAAA,WAA8C;UAC9CF,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAI,MAAA,aAAM;UAEhBJ,EAFgB,CAAAG,YAAA,EAAO,EACZ,EACL;UAKJH,EAFF,CAAAC,cAAA,cAAoC,kBAQD;UALzBD,EAAA,CAAAU,UAAA,mBAAAyR,0DAAA;YAAAnS,EAAA,CAAAa,aAAA,CAAAqR,GAAA;YAAA,OAAAlS,EAAA,CAAAoB,WAAA,CAASwQ,GAAA,CAAAxG,UAAA,EAAY;UAAA,EAAC;UAM5BpL,EAAA,CAAAE,SAAA,aAAsD;UACxDF,EAAA,CAAAG,YAAA,EAAS;UAMLH,EAHJ,CAAAC,cAAA,kBAA2C,kBACuD,eAErC;UAAlBD,EAAA,CAAAU,UAAA,mBAAA0R,uDAAA;YAAApS,EAAA,CAAAa,aAAA,CAAAqR,GAAA;YAAA,OAAAlS,EAAA,CAAAoB,WAAA,CAASwQ,GAAA,CAAAjC,KAAA,EAAO;UAAA,EAAC;UAGlD3P,EAFJ,CAAAC,cAAA,eAAoC,eACG,eACH;UAC9BD,EAAA,CAAAE,SAAA,eAIE;UACFF,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAAI,MAAA,SAAC;UAGpCJ,EAHoC,CAAAG,YAAA,EAAO,EACjC,EACF,EACF;UACNH,EAAA,CAAAC,cAAA,eAA4B;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UACxCJ,EADwC,CAAAG,YAAA,EAAM,EACxC;UAGNH,EAAA,CAAAsB,UAAA,KAAA+Q,uCAAA,kBAEmC;UA0BvCrS,EADE,CAAAG,YAAA,EAAM,EACF;UAGNH,EAAA,CAAAC,cAAA,kBAM6B;UALrBD,EAAA,CAAAU,UAAA,mBAAA4R,0DAAA;YAAAtS,EAAA,CAAAa,aAAA,CAAAqR,GAAA;YAAA,OAAAlS,EAAA,CAAAoB,WAAA,CAASwQ,GAAA,CAAAtG,WAAA,EAAa;UAAA,EAAC;UAM7BtL,EAAA,CAAAE,SAAA,aAAuD;UAG7DF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGNH,EAAA,CAAAsB,UAAA,KAAAiR,uCAAA,oBAA4C;;;UAxEhCvS,EAAA,CAAAK,SAAA,IAA+B;UAA/BL,EAAA,CAAAyB,WAAA,YAAAmQ,GAAA,CAAA/J,aAAA,CAA+B;UAD/B7H,EAAA,CAAA0B,UAAA,cAAAkQ,GAAA,CAAAjK,aAAA,CAA2B;UAUQ3H,EAAA,CAAAK,SAAA,GAAsD;UAAtDL,EAAA,CAAAkC,WAAA,8BAAA0P,GAAA,CAAA9J,UAAA,SAAsD;UAQnF9H,EAAA,CAAAK,SAAA,GAA0D;UAA1DL,EAAA,CAAA0B,UAAA,SAAAkQ,GAAA,CAAApK,WAAA,kBAAAoK,GAAA,CAAApK,WAAA,CAAA5F,MAAA,kCAAA5B,EAAA,CAAA6B,aAAA,CAA0D;UAY7C7B,EAAA,CAAAK,SAAA,GAAY;UAAZL,EAAA,CAAA0B,UAAA,YAAAkQ,GAAA,CAAArL,OAAA,CAAY;UAiC/BvG,EAAA,CAAAK,SAAA,EAA+B;UAA/BL,EAAA,CAAAyB,WAAA,YAAAmQ,GAAA,CAAA/J,aAAA,CAA+B;UAD/B7H,EAAA,CAAA0B,UAAA,cAAAkQ,GAAA,CAAAhK,cAAA,CAA4B;UAWV5H,EAAA,CAAAK,SAAA,GAAY;UAAZL,EAAA,CAAA0B,UAAA,SAAAkQ,GAAA,CAAAlK,MAAA,CAAY;;;qBD/B9B7H,YAAY,EAAA2S,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE5S,WAAW;MAAA6S,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}