{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\n// Angular Material Modules\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatStepperModule } from '@angular/material/stepper';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatDividerModule } from '@angular/material/divider';\n// Chart.js (commented out until ng2-charts is properly installed)\n// import { NgChartsModule } from 'ng2-charts';\n// Routing\nimport { AdminRoutingModule } from './admin-routing.module';\n// Components\nimport { AdminLayoutComponent } from './layout/admin-layout.component';\nimport { AdminDashboardComponent } from './dashboard/admin-dashboard.component';\nimport { AdminLoginComponent } from './auth/admin-login.component';\nimport { UserManagementComponent } from './users/user-management.component';\nimport { UserDialogComponent } from './users/user-dialog.component';\nimport { ProductManagementComponent } from './products/product-management.component';\nimport { ProductDialogComponent } from './products/product-dialog.component';\nimport { OrderManagementComponent } from './orders/order-management.component';\nimport { OrderDetailsComponent } from './orders/order-details.component';\nimport { AnalyticsComponent } from './analytics/analytics.component';\nimport { SettingsComponent } from './settings/settings.component';\nimport { SidebarComponent } from './layout/sidebar.component';\nimport { HeaderComponent } from './layout/header.component';\n// Services\nimport { AdminAuthService } from './services/admin-auth.service';\nimport { AdminApiService } from './services/admin-api.service';\n///import { UserService } from './services/user.service';\nimport { ProductService } from './services/product.service';\nimport { OrderService } from './services/order.service';\nimport { AnalyticsService } from './services/analytics.service';\n// Guards\nimport { AdminAuthGuard } from './guards/admin-auth.guard';\nimport { PermissionGuard } from './guards/permission.guard';\n// Pipes\nimport { RolePipe } from './pipes/role.pipe';\nimport { StatusPipe } from './pipes/status.pipe';\nimport { CurrencyFormatPipe as AdminCurrencyFormatPipe } from './pipes/currency-format.pipe';\nimport * as i0 from \"@angular/core\";\nexport class AdminModule {\n  static {\n    this.ɵfac = function AdminModule_Factory(t) {\n      return new (t || AdminModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AdminModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [AdminAuthService, AdminApiService,\n      //UserService,\n      ProductService, OrderService, AnalyticsService, AdminAuthGuard, PermissionGuard],\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, HttpClientModule, AdminRoutingModule,\n      // NgChartsModule, // Commented out until ng2-charts is properly installed\n      // Angular Material Modules\n      MatToolbarModule, MatSidenavModule, MatListModule, MatIconModule, MatButtonModule, MatCardModule, MatTableModule, MatPaginatorModule, MatSortModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatDialogModule, MatSnackBarModule, MatProgressSpinnerModule, MatChipsModule, MatTabsModule, MatMenuModule, MatBadgeModule, MatSlideToggleModule, MatCheckboxModule, MatRadioModule, MatExpansionModule, MatStepperModule, MatTooltipModule, MatDividerModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AdminModule, {\n    declarations: [\n    // Layout Components\n    AdminLayoutComponent, SidebarComponent, HeaderComponent,\n    // Auth Components\n    AdminLoginComponent,\n    // Dashboard Components\n    AdminDashboardComponent,\n    // User Management\n    UserManagementComponent, UserDialogComponent,\n    // Product Management\n    ProductManagementComponent, ProductDialogComponent,\n    // Order Management\n    OrderManagementComponent, OrderDetailsComponent,\n    // Analytics\n    AnalyticsComponent,\n    // Settings\n    SettingsComponent,\n    // Pipes\n    RolePipe, StatusPipe, AdminCurrencyFormatPipe],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, HttpClientModule, AdminRoutingModule,\n    // NgChartsModule, // Commented out until ng2-charts is properly installed\n    // Angular Material Modules\n    MatToolbarModule, MatSidenavModule, MatListModule, MatIconModule, MatButtonModule, MatCardModule, MatTableModule, MatPaginatorModule, MatSortModule, MatInputModule, MatFormFieldModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatDialogModule, MatSnackBarModule, MatProgressSpinnerModule, MatChipsModule, MatTabsModule, MatMenuModule, MatBadgeModule, MatSlideToggleModule, MatCheckboxModule, MatRadioModule, MatExpansionModule, MatStepperModule, MatTooltipModule, MatDividerModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "HttpClientModule", "MatToolbarModule", "MatSidenavModule", "MatListModule", "MatIconModule", "MatButtonModule", "MatCardModule", "MatTableModule", "MatPaginatorModule", "MatSortModule", "MatInputModule", "MatFormFieldModule", "MatSelectModule", "MatDatepickerModule", "MatNativeDateModule", "MatDialogModule", "MatSnackBarModule", "MatProgressSpinnerModule", "MatChipsModule", "MatTabsModule", "MatMenuModule", "MatBadgeModule", "MatSlideToggleModule", "MatCheckboxModule", "MatRadioModule", "MatExpansionModule", "MatStepperModule", "MatTooltipModule", "MatDividerModule", "AdminRoutingModule", "AdminLayoutComponent", "AdminDashboardComponent", "AdminLoginComponent", "UserManagementComponent", "UserDialogComponent", "ProductManagementComponent", "ProductDialogComponent", "OrderManagementComponent", "OrderDetailsComponent", "AnalyticsComponent", "SettingsComponent", "SidebarComponent", "HeaderComponent", "AdminAuthService", "AdminApiService", "ProductService", "OrderService", "AnalyticsService", "Ad<PERSON><PERSON><PERSON><PERSON><PERSON>", "PermissionGuard", "RolePipe", "StatusPipe", "CurrencyFormatPipe", "AdminCurrencyFormatPipe", "AdminModule", "imports", "declarations"], "sources": ["E:\\DFashion\\frontend\\src\\app\\admin\\admin.module.ts"], "sourcesContent": ["import { NgModule, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\n\n// Angular Material Modules\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatStepperModule } from '@angular/material/stepper';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatDividerModule } from '@angular/material/divider';\n\n// Chart.js (commented out until ng2-charts is properly installed)\n// import { NgChartsModule } from 'ng2-charts';\n\n// Routing\nimport { AdminRoutingModule } from './admin-routing.module';\n\n// Components\nimport { AdminLayoutComponent } from './layout/admin-layout.component';\nimport { AdminDashboardComponent } from './dashboard/admin-dashboard.component';\nimport { AdminLoginComponent } from './auth/admin-login.component';\nimport { UserManagementComponent } from './users/user-management.component';\nimport { UserDialogComponent } from './users/user-dialog.component';\nimport { ProductManagementComponent } from './products/product-management.component';\nimport { ProductDialogComponent } from './products/product-dialog.component';\nimport { OrderManagementComponent } from './orders/order-management.component';\nimport { OrderDetailsComponent } from './orders/order-details.component';\nimport { AnalyticsComponent } from './analytics/analytics.component';\nimport { SettingsComponent } from './settings/settings.component';\nimport { SidebarComponent } from './layout/sidebar.component';\nimport { HeaderComponent } from './layout/header.component';\n\n// Services\nimport { AdminAuthService } from './services/admin-auth.service';\nimport { AdminApiService } from './services/admin-api.service';\n///import { UserService } from './services/user.service';\nimport { ProductService } from './services/product.service';\nimport { OrderService } from './services/order.service';\nimport { AnalyticsService } from './services/analytics.service';\n\n// Guards\nimport { AdminAuthGuard } from './guards/admin-auth.guard';\nimport { PermissionGuard } from './guards/permission.guard';\n\n// Pipes\nimport { RolePipe } from './pipes/role.pipe';\nimport { StatusPipe } from './pipes/status.pipe';\nimport { CurrencyFormatPipe as AdminCurrencyFormatPipe } from './pipes/currency-format.pipe';\n\n@NgModule({\n  declarations: [\n    // Layout Components\n    AdminLayoutComponent,\n    SidebarComponent,\n    HeaderComponent,\n    \n    // Auth Components\n    AdminLoginComponent,\n    \n    // Dashboard Components\n    AdminDashboardComponent,\n    \n    // User Management\n    UserManagementComponent,\n    UserDialogComponent,\n    \n    // Product Management\n    ProductManagementComponent,\n    ProductDialogComponent,\n    \n    // Order Management\n    OrderManagementComponent,\n    OrderDetailsComponent,\n    \n    // Analytics\n    AnalyticsComponent,\n    \n    // Settings\n    SettingsComponent,\n    \n    // Pipes\n    RolePipe,\n    StatusPipe,\n    AdminCurrencyFormatPipe\n  ],\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    HttpClientModule,\n    AdminRoutingModule,\n    // NgChartsModule, // Commented out until ng2-charts is properly installed\n    \n    // Angular Material Modules\n    MatToolbarModule,\n    MatSidenavModule,\n    MatListModule,\n    MatIconModule,\n    MatButtonModule,\n    MatCardModule,\n    MatTableModule,\n    MatPaginatorModule,\n    MatSortModule,\n    MatInputModule,\n    MatFormFieldModule,\n    MatSelectModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule,\n    MatChipsModule,\n    MatTabsModule,\n    MatMenuModule,\n    MatBadgeModule,\n    MatSlideToggleModule,\n    MatCheckboxModule,\n    MatRadioModule,\n    MatExpansionModule,\n    MatStepperModule,\n    MatTooltipModule,\n    MatDividerModule\n  ],\n  providers: [\n    AdminAuthService,\n    AdminApiService,\n    //UserService,\n    ProductService,\n    OrderService,\n    AnalyticsService,\n    AdminAuthGuard,\n    PermissionGuard\n  ],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]\n})\nexport class AdminModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,sBAAsB;AAEvD;AACA,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D;AACA;AAEA;AACA,SAASC,kBAAkB,QAAQ,wBAAwB;AAE3D;AACA,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,uBAAuB,QAAQ,mCAAmC;AAC3E,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,0BAA0B,QAAQ,yCAAyC;AACpF,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,wBAAwB,QAAQ,qCAAqC;AAC9E,SAASC,qBAAqB,QAAQ,kCAAkC;AACxE,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,eAAe,QAAQ,2BAA2B;AAE3D;AACA,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,eAAe,QAAQ,8BAA8B;AAC9D;AACA,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,YAAY,QAAQ,0BAA0B;AACvD,SAASC,gBAAgB,QAAQ,8BAA8B;AAE/D;AACA,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,eAAe,QAAQ,2BAA2B;AAE3D;AACA,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,kBAAkB,IAAIC,uBAAuB,QAAQ,8BAA8B;;AAwF5F,OAAM,MAAOC,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;iBAZX,CACTX,gBAAgB,EAChBC,eAAe;MACf;MACAC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,eAAe,CAChB;MAAAM,OAAA,GA9CC1D,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,gBAAgB,EAChB6B,kBAAkB;MAClB;MAEA;MACA5B,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB,EACxBC,cAAc,EACdC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpBC,iBAAiB,EACjBC,cAAc,EACdC,kBAAkB,EAClBC,gBAAgB,EAChBC,gBAAgB,EAChBC,gBAAgB;IAAA;EAAA;;;2EAcP0B,WAAW;IAAAE,YAAA;IApFpB;IACA1B,oBAAoB,EACpBW,gBAAgB,EAChBC,eAAe;IAEf;IACAV,mBAAmB;IAEnB;IACAD,uBAAuB;IAEvB;IACAE,uBAAuB,EACvBC,mBAAmB;IAEnB;IACAC,0BAA0B,EAC1BC,sBAAsB;IAEtB;IACAC,wBAAwB,EACxBC,qBAAqB;IAErB;IACAC,kBAAkB;IAElB;IACAC,iBAAiB;IAEjB;IACAU,QAAQ,EACRC,UAAU,EACVE,uBAAuB;IAAAE,OAAA,GAGvB1D,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,gBAAgB,EAChB6B,kBAAkB;IAClB;IAEA;IACA5B,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB,EACxBC,cAAc,EACdC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpBC,iBAAiB,EACjBC,cAAc,EACdC,kBAAkB,EAClBC,gBAAgB,EAChBC,gBAAgB,EAChBC,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}