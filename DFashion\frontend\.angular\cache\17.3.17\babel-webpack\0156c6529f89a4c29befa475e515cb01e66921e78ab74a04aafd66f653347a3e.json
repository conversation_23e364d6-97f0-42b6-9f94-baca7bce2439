{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/story.service\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../../../core/services/media.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"storiesSlider\"];\nfunction StoriesComponent_div_2_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_div_2_button_1_Template_button_click_0_listener() {\n      const i_r3 = i0.ɵɵrestoreView(_r2).index;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.goToSlide(i_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r3 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r3 === ctx_r3.currentSlide);\n  }\n}\nfunction StoriesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, StoriesComponent_div_2_button_1_Template, 1, 2, \"button\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.dots);\n  }\n}\nfunction StoriesComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_div_12_Template_div_click_0_listener() {\n      const storyGroup_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.openStoryViewer(storyGroup_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 7)(2, \"div\", 19)(3, \"img\", 20);\n    i0.ɵɵlistener(\"error\", function StoriesComponent_div_12_Template_img_error_3_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onImageError($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(4, \"span\", 10);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const storyGroup_r6 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"has-story\", storyGroup_r6.stories.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r3.getSafeImageUrl(storyGroup_r6.user.avatar), i0.ɵɵsanitizeUrl)(\"alt\", storyGroup_r6.user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(storyGroup_r6.user.username);\n  }\n}\nfunction StoriesComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"div\", 22);\n    i0.ɵɵelementStart(2, \"span\", 23);\n    i0.ɵɵtext(3, \"Auto-sliding\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StoriesComponent_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.slideLeft());\n    });\n    i0.ɵɵelement(1, \"i\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.canSlideLeft);\n  }\n}\nfunction StoriesComponent_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function StoriesComponent_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.slideRight());\n    });\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.canSlideRight);\n  }\n}\nexport class StoriesComponent {\n  constructor(storyService, authService, router, mediaService) {\n    this.storyService = storyService;\n    this.authService = authService;\n    this.router = router;\n    this.mediaService = mediaService;\n    this.storyGroups = [];\n    this.currentUser = null;\n    // Slider properties\n    this.translateX = 0;\n    this.currentSlide = 0;\n    this.isTransitioning = false;\n    // Navigation properties\n    this.canSlideLeft = false;\n    this.canSlideRight = false;\n    this.showArrows = false;\n    this.showDots = false;\n    this.dots = [];\n    // Touch properties\n    this.touchStartX = 0;\n    this.touchCurrentX = 0;\n    this.isDragging = false;\n    // Responsive properties\n    this.slidesPerView = 1;\n    this.slideWidth = 0;\n    this.autoSlideDelay = 3000; // 3 seconds\n    this.isAutoSliding = true;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    this.loadStories();\n  }\n  ngAfterViewInit() {\n    // Initialize slider after view init\n    setTimeout(() => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n      this.startAutoSlide();\n    }, 100);\n    // Add resize listener for responsive updates\n    window.addEventListener('resize', () => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n    });\n  }\n  ngOnDestroy() {\n    this.stopAutoSlide();\n  }\n  calculateResponsiveSettings() {\n    if (!this.storiesSlider) return;\n    const containerWidth = this.storiesSlider.nativeElement.clientWidth;\n    const screenWidth = window.innerWidth;\n    // Calculate slides per view based on screen size\n    if (screenWidth >= 1024) {\n      this.slidesPerView = Math.floor(containerWidth / 90); // Desktop: 90px per slide\n      this.slideWidth = 90;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 768) {\n      this.slidesPerView = Math.floor(containerWidth / 80); // Tablet: 80px per slide\n      this.slideWidth = 80;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 481) {\n      this.slidesPerView = Math.floor(containerWidth / 70); // Mobile landscape: 70px per slide\n      this.slideWidth = 70;\n      this.showArrows = false;\n      this.showDots = true;\n    } else if (screenWidth >= 361) {\n      this.slidesPerView = Math.floor(containerWidth / 65); // Mobile portrait: 65px per slide\n      this.slideWidth = 65;\n      this.showArrows = false;\n      this.showDots = true;\n    } else {\n      this.slidesPerView = Math.floor(containerWidth / 60); // Very small: 60px per slide\n      this.slideWidth = 60;\n      this.showArrows = false;\n      this.showDots = true;\n    }\n    // Ensure minimum slides per view\n    this.slidesPerView = Math.max(this.slidesPerView, 3);\n    // Calculate dots for mobile\n    if (this.showDots) {\n      const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n      const totalPages = Math.ceil(totalSlides / this.slidesPerView);\n      this.dots = Array(totalPages).fill(0).map((_, i) => i);\n    }\n  }\n  loadStories() {\n    this.storyService.getStories().subscribe({\n      next: response => {\n        if (response.storyGroups && response.storyGroups.length > 0) {\n          this.storyGroups = response.storyGroups;\n        } else {\n          // Load mock data if no stories from API\n          this.loadMockStories();\n        }\n        // Update slider after stories load\n        setTimeout(() => {\n          this.calculateResponsiveSettings();\n          this.updateSliderState();\n          this.startAutoSlide();\n        }, 100);\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        // Load mock data for demonstration\n        this.loadMockStories();\n      }\n    });\n  }\n  // Auto-slide methods\n  startAutoSlide() {\n    if (this.storyGroups.length <= this.slidesPerView) return; // No need to auto-slide if all stories fit\n    this.stopAutoSlide(); // Clear any existing interval\n    this.autoSlideInterval = setInterval(() => {\n      if (this.isAutoSliding) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  pauseAutoSlide() {\n    this.isAutoSliding = false;\n  }\n  resumeAutoSlide() {\n    this.isAutoSliding = true;\n  }\n  autoSlideNext() {\n    const maxSlide = this.getMaxSlide();\n    if (this.currentSlide < maxSlide) {\n      this.slideRight();\n    } else {\n      // Reset to beginning\n      this.currentSlide = 0;\n      this.updateSliderPosition();\n    }\n  }\n  // Slider navigation methods\n  slideLeft() {\n    if (this.canSlideLeft) {\n      this.pauseAutoSlide();\n      this.currentSlide = Math.max(0, this.currentSlide - 1);\n      this.updateSliderPosition();\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n  slideRight() {\n    if (this.canSlideRight) {\n      this.pauseAutoSlide();\n      const maxSlide = this.getMaxSlide();\n      this.currentSlide = Math.min(maxSlide, this.currentSlide + 1);\n      this.updateSliderPosition();\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n  goToSlide(slideIndex) {\n    this.pauseAutoSlide();\n    this.currentSlide = slideIndex;\n    this.updateSliderPosition();\n    // Resume auto-slide after user interaction\n    setTimeout(() => this.resumeAutoSlide(), 5000);\n  }\n  updateSliderPosition() {\n    this.isTransitioning = true;\n    this.translateX = -this.currentSlide * this.slideWidth * this.slidesPerView;\n    this.updateSliderState();\n    // Reset transition flag after animation\n    setTimeout(() => {\n      this.isTransitioning = false;\n    }, 300);\n  }\n  updateSliderState() {\n    const maxSlide = this.getMaxSlide();\n    this.canSlideLeft = this.currentSlide > 0;\n    this.canSlideRight = this.currentSlide < maxSlide;\n  }\n  getMaxSlide() {\n    const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n    return Math.max(0, Math.ceil(totalSlides / this.slidesPerView) - 1);\n  }\n  onScroll() {\n    // Handle manual scroll if needed\n    this.updateSliderState();\n  }\n  // Touch gesture methods\n  onTouchStart(event) {\n    this.touchStartX = event.touches[0].clientX;\n    this.touchCurrentX = this.touchStartX;\n    this.isDragging = true;\n    this.pauseAutoSlide();\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.touchCurrentX = event.touches[0].clientX;\n    const deltaX = this.touchCurrentX - this.touchStartX;\n    // Prevent default scrolling\n    if (Math.abs(deltaX) > 10) {\n      event.preventDefault();\n    }\n  }\n  onTouchEnd(event) {\n    if (!this.isDragging) return;\n    const deltaX = this.touchCurrentX - this.touchStartX;\n    const threshold = 50; // Minimum swipe distance\n    if (Math.abs(deltaX) > threshold) {\n      if (deltaX > 0) {\n        // Swipe right - go to previous slide\n        this.slideLeft();\n      } else {\n        // Swipe left - go to next slide\n        this.slideRight();\n      }\n    } else {\n      // Resume auto-slide if no significant swipe\n      setTimeout(() => this.resumeAutoSlide(), 2000);\n    }\n    this.isDragging = false;\n  }\n  // Image handling methods\n  getSafeImageUrl(url) {\n    return this.mediaService.getSafeImageUrl(url, 'user');\n  }\n  onImageError(event) {\n    this.mediaService.handleImageError(event, 'user');\n  }\n  openAddStory() {\n    this.showStoryCreationModal();\n  }\n  showStoryCreationModal() {\n    // Create modal overlay\n    const modalOverlay = document.createElement('div');\n    modalOverlay.className = 'story-creation-modal';\n    modalOverlay.innerHTML = `\n      <div class=\"modal-content\">\n        <div class=\"modal-header\">\n          <h3>Create Story</h3>\n          <button class=\"close-btn\" onclick=\"this.closest('.story-creation-modal').remove()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        <div class=\"modal-body\">\n          <div class=\"creation-options\">\n            <div class=\"option-card camera-option\" onclick=\"document.getElementById('camera-input').click()\">\n              <div class=\"option-icon camera-icon\">\n                <i class=\"fas fa-camera\"></i>\n              </div>\n              <h4>Camera</h4>\n              <p>Take a photo or video</p>\n            </div>\n            <div class=\"option-card gallery-option\" onclick=\"document.getElementById('gallery-input').click()\">\n              <div class=\"option-icon gallery-icon\">\n                <i class=\"fas fa-images\"></i>\n              </div>\n              <h4>Gallery</h4>\n              <p>Choose from your photos</p>\n            </div>\n            <div class=\"option-card create-option\" onclick=\"this.closest('.story-creation-modal').querySelector('.text-story-creator').style.display='block'\">\n              <div class=\"option-icon create-icon\">\n                <i class=\"fas fa-font\"></i>\n              </div>\n              <h4>Create</h4>\n              <p>Design with text and colors</p>\n            </div>\n            <div class=\"option-card live-option\" onclick=\"alert('Live feature coming soon!')\">\n              <div class=\"option-icon live-icon\">\n                <i class=\"fas fa-video\"></i>\n              </div>\n              <h4>Live</h4>\n              <p>Go live with your audience</p>\n            </div>\n          </div>\n\n          <!-- Text Story Creator -->\n          <div class=\"text-story-creator\" style=\"display: none;\">\n            <div class=\"text-editor\">\n              <textarea placeholder=\"What's on your mind?\" maxlength=\"500\"></textarea>\n              <div class=\"color-picker\">\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff6b6b, #feca57)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff6b6b, #feca57)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #48dbfb, #0abde3)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #48dbfb, #0abde3)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff9ff3, #f368e0)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff9ff3, #f368e0)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #54a0ff, #2e86de)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #54a0ff, #2e86de)'\"></div>\n              </div>\n              <button class=\"create-text-story-btn\" onclick=\"alert('Text story created! (Demo)')\">Create Story</button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Hidden file inputs -->\n        <input type=\"file\" id=\"camera-input\" accept=\"image/*,video/*\" capture=\"environment\" style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n        <input type=\"file\" id=\"gallery-input\" accept=\"image/*,video/*\" multiple style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n      </div>\n    `;\n    // Add styles\n    const styles = document.createElement('style');\n    styles.textContent = `\n      .story-creation-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.8);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 10000;\n        animation: fadeIn 0.3s ease;\n      }\n\n      .modal-content {\n        background: white;\n        border-radius: 16px;\n        width: 90%;\n        max-width: 400px;\n        max-height: 80vh;\n        overflow-y: auto;\n      }\n\n      .modal-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem 1.5rem;\n        border-bottom: 1px solid #eee;\n      }\n\n      .modal-header h3 {\n        margin: 0;\n        font-size: 1.25rem;\n        font-weight: 600;\n      }\n\n      .close-btn {\n        background: none;\n        border: none;\n        font-size: 1.25rem;\n        cursor: pointer;\n        padding: 0.5rem;\n        border-radius: 50%;\n        transition: background 0.2s;\n      }\n\n      .close-btn:hover {\n        background: #f0f0f0;\n      }\n\n      .creation-options {\n        display: grid;\n        grid-template-columns: 1fr;\n        gap: 1rem;\n        padding: 1.5rem;\n      }\n\n      .option-card {\n        display: flex;\n        align-items: center;\n        padding: 1.5rem;\n        border: none;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        position: relative;\n        overflow: hidden;\n      }\n\n      .option-card:hover {\n        transform: translateY(-3px) scale(1.02);\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n      }\n\n      .camera-option {\n        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n      }\n\n      .gallery-option {\n        background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);\n      }\n\n      .create-option {\n        background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\n      }\n\n      .live-option {\n        background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);\n      }\n\n      .option-icon {\n        width: 60px;\n        height: 60px;\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 1rem;\n        backdrop-filter: blur(10px);\n        border: 2px solid rgba(255, 255, 255, 0.3);\n      }\n\n      .option-icon i {\n        color: white;\n        font-size: 1.5rem;\n      }\n\n      .option-card h4 {\n        margin: 0 0 0.25rem 0;\n        font-size: 1.1rem;\n        font-weight: 700;\n        color: white;\n      }\n\n      .option-card p {\n        margin: 0;\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 0.875rem;\n      }\n\n      .text-story-creator {\n        padding: 1.5rem;\n        background: linear-gradient(45deg, #ff6b6b, #feca57);\n        border-radius: 12px;\n        margin: 1rem;\n      }\n\n      .text-editor textarea {\n        width: 100%;\n        min-height: 120px;\n        border: none;\n        background: rgba(255, 255, 255, 0.9);\n        border-radius: 8px;\n        padding: 1rem;\n        font-size: 1.125rem;\n        resize: none;\n        margin-bottom: 1rem;\n      }\n\n      .color-picker {\n        display: flex;\n        gap: 0.5rem;\n        margin-bottom: 1rem;\n      }\n\n      .color-option {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        cursor: pointer;\n        border: 3px solid white;\n        transition: transform 0.2s;\n      }\n\n      .color-option:hover {\n        transform: scale(1.1);\n      }\n\n      .create-text-story-btn {\n        background: white;\n        border: none;\n        padding: 0.75rem 1.5rem;\n        border-radius: 25px;\n        font-weight: 600;\n        cursor: pointer;\n        transition: transform 0.2s;\n      }\n\n      .create-text-story-btn:hover {\n        transform: scale(1.05);\n      }\n\n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n    `;\n    document.head.appendChild(styles);\n    document.body.appendChild(modalOverlay);\n    // Add global function for file handling\n    window.handleFileSelect = input => {\n      const files = input.files;\n      if (files && files.length > 0) {\n        const file = files[0];\n        console.log('Selected file:', file.name, file.type);\n        // Create preview modal\n        this.showFilePreview(file);\n        modalOverlay.remove();\n      }\n    };\n  }\n  showFilePreview(file) {\n    const previewModal = document.createElement('div');\n    previewModal.className = 'story-preview-modal';\n    const fileURL = URL.createObjectURL(file);\n    const isVideo = file.type.startsWith('video/');\n    previewModal.innerHTML = `\n      <div class=\"preview-content\">\n        <div class=\"preview-header\">\n          <button class=\"back-btn\" onclick=\"this.closest('.story-preview-modal').remove()\">\n            <i class=\"fas fa-arrow-left\"></i>\n          </button>\n          <h3>Create Story</h3>\n          <button class=\"share-btn\" onclick=\"alert('Story shared! (Demo)')\">Share</button>\n        </div>\n        <div class=\"preview-media\">\n          ${isVideo ? `<video src=\"${fileURL}\" controls autoplay muted></video>` : `<img src=\"${fileURL}\" alt=\"Story preview\">`}\n          <div class=\"story-tools\">\n            <button class=\"tool-btn\" onclick=\"alert('Add text feature coming soon!')\">\n              <i class=\"fas fa-font\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Add stickers feature coming soon!')\">\n              <i class=\"fas fa-smile\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Tag products feature coming soon!')\">\n              <i class=\"fas fa-tag\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n    `;\n    // Add preview styles\n    const previewStyles = document.createElement('style');\n    previewStyles.textContent = `\n      .story-preview-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: black;\n        z-index: 10001;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .preview-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem;\n        background: rgba(0, 0, 0, 0.5);\n        color: white;\n      }\n\n      .back-btn, .share-btn {\n        background: none;\n        border: none;\n        color: white;\n        font-size: 1rem;\n        cursor: pointer;\n        padding: 0.5rem;\n      }\n\n      .share-btn {\n        background: #007bff;\n        border-radius: 20px;\n        padding: 0.5rem 1rem;\n      }\n\n      .preview-media {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        position: relative;\n      }\n\n      .preview-media img,\n      .preview-media video {\n        max-width: 100%;\n        max-height: 100%;\n        object-fit: contain;\n      }\n\n      .story-tools {\n        position: absolute;\n        right: 1rem;\n        top: 50%;\n        transform: translateY(-50%);\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n      }\n\n      .tool-btn {\n        width: 48px;\n        height: 48px;\n        background: rgba(255, 255, 255, 0.2);\n        border: none;\n        border-radius: 50%;\n        color: white;\n        font-size: 1.25rem;\n        cursor: pointer;\n        backdrop-filter: blur(10px);\n        transition: background 0.2s;\n      }\n\n      .tool-btn:hover {\n        background: rgba(255, 255, 255, 0.3);\n      }\n    `;\n    document.head.appendChild(previewStyles);\n    document.body.appendChild(previewModal);\n  }\n  openStoryViewer(storyGroup) {\n    if (storyGroup.stories.length > 0) {\n      // Create Instagram-like story viewer modal\n      this.createStoryViewerModal(storyGroup);\n    } else {\n      console.log('No stories available for:', storyGroup.user.username);\n    }\n  }\n  createStoryViewerModal(storyGroup) {\n    // Create modal overlay\n    const modalOverlay = document.createElement('div');\n    modalOverlay.className = 'story-viewer-modal';\n    modalOverlay.innerHTML = `\n      <div class=\"story-viewer-container\">\n        <div class=\"story-viewer-content\">\n          <!-- Progress bars -->\n          <div class=\"progress-container\">\n            ${storyGroup.stories.map((_, index) => `\n              <div class=\"progress-bar ${index === 0 ? 'active' : ''}\" data-index=\"${index}\">\n                <div class=\"progress-fill\"></div>\n              </div>\n            `).join('')}\n          </div>\n\n          <!-- Header -->\n          <div class=\"story-header\">\n            <div class=\"user-info\">\n              <img src=\"${storyGroup.user.avatar || '/assets/images/default-avatar.png'}\"\n                   alt=\"${storyGroup.user.fullName}\" class=\"user-avatar\">\n              <div class=\"user-details\">\n                <span class=\"username\">${storyGroup.user.username}</span>\n                <span class=\"timestamp\">${this.getTimeAgo(storyGroup.stories[0].createdAt)}</span>\n              </div>\n            </div>\n            <div class=\"story-controls\">\n              <button class=\"btn-close\" onclick=\"this.closest('.story-viewer-modal').remove()\">\n                <i class=\"fas fa-times\"></i>\n              </button>\n            </div>\n          </div>\n\n          <!-- Story content -->\n          <div class=\"story-content\" id=\"story-content\">\n            <!-- Will be populated dynamically -->\n          </div>\n\n          <!-- Navigation areas -->\n          <div class=\"nav-area nav-prev\" onclick=\"window.storyViewer.previousStory()\"></div>\n          <div class=\"nav-area nav-next\" onclick=\"window.storyViewer.nextStory()\"></div>\n\n          <!-- Bottom actions -->\n          <div class=\"story-actions\">\n            <div class=\"social-actions\">\n              <button class=\"social-btn like\" onclick=\"window.storyViewer.toggleLike()\">\n                <i class=\"fas fa-heart\"></i>\n              </button>\n              <button class=\"social-btn comment\" onclick=\"window.storyViewer.openComments()\">\n                <i class=\"fas fa-comment\"></i>\n              </button>\n              <button class=\"social-btn share\" onclick=\"window.storyViewer.shareStory()\">\n                <i class=\"fas fa-share\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    `;\n    // Add styles\n    const styles = document.createElement('style');\n    styles.textContent = `\n      .story-viewer-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100vw;\n        height: 100vh;\n        background: #000;\n        z-index: 10000;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        animation: fadeIn 0.3s ease;\n      }\n\n      .story-viewer-container {\n        width: 100%;\n        height: 100%;\n        max-width: 400px;\n        position: relative;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .story-viewer-content {\n        flex: 1;\n        position: relative;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .progress-container {\n        display: flex;\n        gap: 2px;\n        padding: 8px 20px;\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        z-index: 10;\n      }\n\n      .progress-bar {\n        flex: 1;\n        height: 2px;\n        background: rgba(255,255,255,0.3);\n        border-radius: 1px;\n        overflow: hidden;\n      }\n\n      .progress-fill {\n        height: 100%;\n        background: #fff;\n        width: 0%;\n        transition: width 0.1s ease;\n      }\n\n      .progress-bar.active .progress-fill {\n        animation: progress 15s linear;\n      }\n\n      .progress-bar.completed .progress-fill {\n        width: 100%;\n      }\n\n      @keyframes progress {\n        from { width: 0%; }\n        to { width: 100%; }\n      }\n\n      .story-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 16px 20px;\n        background: linear-gradient(180deg, rgba(0,0,0,0.6) 0%, transparent 100%);\n        position: relative;\n        z-index: 10;\n      }\n\n      .user-info {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n      }\n\n      .user-avatar {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        border: 2px solid #fff;\n      }\n\n      .user-details {\n        display: flex;\n        flex-direction: column;\n      }\n\n      .username {\n        color: #fff;\n        font-weight: 600;\n        font-size: 0.9rem;\n      }\n\n      .timestamp {\n        color: rgba(255,255,255,0.7);\n        font-size: 0.8rem;\n      }\n\n      .btn-close {\n        background: none;\n        border: none;\n        color: #fff;\n        font-size: 1.2rem;\n        cursor: pointer;\n        padding: 8px;\n      }\n\n      .story-content {\n        flex: 1;\n        position: relative;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #000;\n      }\n\n      .story-media {\n        max-width: 100%;\n        max-height: 100%;\n        object-fit: contain;\n      }\n\n      .nav-area {\n        position: absolute;\n        top: 0;\n        bottom: 0;\n        width: 30%;\n        cursor: pointer;\n        z-index: 5;\n      }\n\n      .nav-prev {\n        left: 0;\n      }\n\n      .nav-next {\n        right: 0;\n      }\n\n      .story-actions {\n        padding: 16px 20px;\n        background: linear-gradient(0deg, rgba(0,0,0,0.6) 0%, transparent 100%);\n      }\n\n      .social-actions {\n        display: flex;\n        justify-content: center;\n        gap: 24px;\n      }\n\n      .social-btn {\n        background: none;\n        border: none;\n        color: #fff;\n        font-size: 1.5rem;\n        cursor: pointer;\n        padding: 8px;\n        border-radius: 50%;\n        transition: all 0.2s ease;\n      }\n\n      .social-btn:hover {\n        background: rgba(255,255,255,0.1);\n        transform: scale(1.1);\n      }\n\n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n\n      @media (max-width: 768px) {\n        .story-viewer-container {\n          max-width: 100%;\n        }\n      }\n    `;\n    document.head.appendChild(styles);\n    document.body.appendChild(modalOverlay);\n    // Initialize story viewer functionality\n    this.initializeStoryViewer(storyGroup, modalOverlay);\n  }\n  initializeStoryViewer(storyGroup, modalElement) {\n    let currentIndex = 0;\n    let progressTimer;\n    const storyViewer = {\n      currentIndex,\n      stories: storyGroup.stories,\n      showStory: index => {\n        const story = storyGroup.stories[index];\n        const contentElement = modalElement.querySelector('#story-content');\n        if (contentElement) {\n          if (story.media.type === 'image') {\n            contentElement.innerHTML = `\n              <img src=\"${story.media.url}\" alt=\"${story.caption || ''}\" class=\"story-media\">\n            `;\n          } else {\n            contentElement.innerHTML = `\n              <video src=\"${story.media.url}\" class=\"story-media\" autoplay muted>\n              </video>\n            `;\n          }\n        }\n        // Update progress bars\n        const progressBars = modalElement.querySelectorAll('.progress-bar');\n        progressBars.forEach((bar, i) => {\n          bar.classList.remove('active', 'completed');\n          if (i < index) {\n            bar.classList.add('completed');\n          } else if (i === index) {\n            bar.classList.add('active');\n          }\n        });\n        this.startProgressTimer(15000); // 15 seconds\n      },\n      nextStory: () => {\n        if (currentIndex < storyGroup.stories.length - 1) {\n          currentIndex++;\n          storyViewer.showStory(currentIndex);\n        } else {\n          modalElement.remove();\n        }\n      },\n      previousStory: () => {\n        if (currentIndex > 0) {\n          currentIndex--;\n          storyViewer.showStory(currentIndex);\n        }\n      },\n      toggleLike: () => {\n        console.log('Toggle like for story');\n      },\n      openComments: () => {\n        console.log('Open comments for story');\n      },\n      shareStory: () => {\n        console.log('Share story');\n      }\n    };\n    // Make it globally accessible for onclick handlers\n    window.storyViewer = storyViewer;\n    // Show first story\n    storyViewer.showStory(0);\n    // Cleanup when modal is removed\n    const observer = new MutationObserver(mutations => {\n      mutations.forEach(mutation => {\n        if (mutation.type === 'childList') {\n          mutation.removedNodes.forEach(node => {\n            if (node === modalElement) {\n              delete window.storyViewer;\n              if (progressTimer) clearTimeout(progressTimer);\n              observer.disconnect();\n            }\n          });\n        }\n      });\n    });\n    observer.observe(document.body, {\n      childList: true\n    });\n  }\n  startProgressTimer(duration) {\n    // This would be implemented to handle progress timing\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diff = now.getTime() - new Date(date).getTime();\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    if (hours < 1) {\n      const minutes = Math.floor(diff / (1000 * 60));\n      return `${minutes}m`;\n    } else if (hours < 24) {\n      return `${hours}h`;\n    } else {\n      const days = Math.floor(hours / 24);\n      return `${days}d`;\n    }\n  }\n  // Mouse hover events for desktop\n  onMouseEnter() {\n    this.pauseAutoSlide();\n  }\n  onMouseLeave() {\n    this.resumeAutoSlide();\n  }\n  loadMockStories() {\n    // Mock story groups for demonstration\n    this.storyGroups = [{\n      user: {\n        _id: 'user1',\n        username: 'fashionista_maya',\n        fullName: 'Maya Sharma',\n        avatar: '/assets/images/users/maya.jpg'\n      },\n      stories: [{\n        _id: 'story1',\n        media: {\n          type: 'image',\n          url: '/assets/images/stories/story1.jpg',\n          duration: 15000\n        },\n        caption: 'New summer collection! 🌞',\n        isActive: true,\n        createdAt: new Date(),\n        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)\n      }]\n    }, {\n      user: {\n        _id: 'user2',\n        username: 'style_guru_raj',\n        fullName: 'Raj Patel',\n        avatar: '/assets/images/users/raj.jpg'\n      },\n      stories: [{\n        _id: 'story2',\n        media: {\n          type: 'video',\n          url: '/assets/videos/stories/story2.mp4',\n          duration: 20000\n        },\n        caption: 'Check out this amazing outfit! 👔',\n        isActive: true,\n        createdAt: new Date(),\n        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)\n      }]\n    }, {\n      user: {\n        _id: 'user3',\n        username: 'trendy_priya',\n        fullName: 'Priya Singh',\n        avatar: '/assets/images/users/priya.jpg'\n      },\n      stories: [{\n        _id: 'story3',\n        media: {\n          type: 'image',\n          url: '/assets/images/stories/story3.jpg',\n          duration: 15000\n        },\n        caption: 'Weekend vibes! ✨',\n        isActive: true,\n        createdAt: new Date(),\n        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)\n      }]\n    }, {\n      user: {\n        _id: 'user4',\n        username: 'fashion_forward',\n        fullName: 'Arjun Kumar',\n        avatar: '/assets/images/users/arjun.jpg'\n      },\n      stories: [{\n        _id: 'story4',\n        media: {\n          type: 'image',\n          url: '/assets/images/stories/story4.jpg',\n          duration: 15000\n        },\n        caption: 'Casual Friday look! 👕',\n        isActive: true,\n        createdAt: new Date(),\n        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)\n      }]\n    }, {\n      user: {\n        _id: 'user5',\n        username: 'chic_style',\n        fullName: 'Sneha Reddy',\n        avatar: '/assets/images/users/sneha.jpg'\n      },\n      stories: [{\n        _id: 'story5',\n        media: {\n          type: 'image',\n          url: '/assets/images/stories/story5.jpg',\n          duration: 15000\n        },\n        caption: 'Ethnic wear collection! 🌺',\n        isActive: true,\n        createdAt: new Date(),\n        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)\n      }]\n    }, {\n      user: {\n        _id: 'user6',\n        username: 'modern_wardrobe',\n        fullName: 'Vikram Joshi',\n        avatar: '/assets/images/users/vikram.jpg'\n      },\n      stories: [{\n        _id: 'story6',\n        media: {\n          type: 'image',\n          url: '/assets/images/stories/story6.jpg',\n          duration: 15000\n        },\n        caption: 'Office wear essentials! 💼',\n        isActive: true,\n        createdAt: new Date(),\n        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)\n      }]\n    }];\n    // Update slider after mock stories load\n    setTimeout(() => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n      this.startAutoSlide();\n    }, 100);\n  }\n  static {\n    this.ɵfac = function StoriesComponent_Factory(t) {\n      return new (t || StoriesComponent)(i0.ɵɵdirectiveInject(i1.StoryService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MediaService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoriesComponent,\n      selectors: [[\"app-stories\"]],\n      viewQuery: function StoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 16,\n      vars: 9,\n      consts: [[\"storiesSlider\", \"\"], [1, \"stories-section\"], [1, \"stories-slider-wrapper\"], [\"class\", \"nav-dots\", 4, \"ngIf\"], [1, \"stories-slider\", 3, \"touchstart\", \"touchmove\", \"touchend\", \"scroll\", \"mouseenter\", \"mouseleave\"], [1, \"stories-track\"], [1, \"story-slide\", \"add-story\", 3, \"click\"], [1, \"story-avatar\"], [1, \"avatar-ring\", \"add-ring\"], [1, \"fas\", \"fa-plus\"], [1, \"story-username\"], [\"class\", \"story-slide\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"auto-slide-indicator\", 4, \"ngIf\"], [\"class\", \"nav-arrow nav-left\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"nav-arrow nav-right\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"nav-dots\"], [\"class\", \"nav-dot\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-dot\", 3, \"click\"], [1, \"story-slide\", 3, \"click\"], [1, \"avatar-ring\"], [1, \"avatar-image\", 3, \"error\", \"src\", \"alt\"], [1, \"auto-slide-indicator\"], [1, \"slide-progress\"], [1, \"slide-text\"], [1, \"nav-arrow\", \"nav-left\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"nav-arrow\", \"nav-right\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function StoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n          i0.ɵɵtemplate(2, StoriesComponent_div_2_Template, 2, 1, \"div\", 3);\n          i0.ɵɵelementStart(3, \"div\", 4, 0);\n          i0.ɵɵlistener(\"touchstart\", function StoriesComponent_Template_div_touchstart_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchStart($event));\n          })(\"touchmove\", function StoriesComponent_Template_div_touchmove_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchMove($event));\n          })(\"touchend\", function StoriesComponent_Template_div_touchend_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTouchEnd($event));\n          })(\"scroll\", function StoriesComponent_Template_div_scroll_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onScroll());\n          })(\"mouseenter\", function StoriesComponent_Template_div_mouseenter_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseEnter());\n          })(\"mouseleave\", function StoriesComponent_Template_div_mouseleave_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseLeave());\n          });\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function StoriesComponent_Template_div_click_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openAddStory());\n          });\n          i0.ɵɵelementStart(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵelement(9, \"i\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"span\", 10);\n          i0.ɵɵtext(11, \"Your Story\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, StoriesComponent_div_12_Template, 6, 5, \"div\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, StoriesComponent_div_13_Template, 4, 0, \"div\", 12)(14, StoriesComponent_button_14_Template, 2, 1, \"button\", 13)(15, StoriesComponent_button_15_Template, 2, 1, \"button\", 14);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDots);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx.translateX + \"px)\")(\"transition\", ctx.isTransitioning ? \"transform 0.3s ease-out\" : \"none\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.storyGroups);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isAutoSliding && ctx.storyGroups.length > ctx.slidesPerView);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showArrows);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showArrows);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf],\n      styles: [\".stories-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  width: 100%;\\n  max-width: 100vw;\\n  overflow: hidden;\\n}\\n\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: #fff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  width: 100%;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.stories-track[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0;\\n  will-change: transform;\\n  padding: 16px 0;\\n}\\n\\n.story-slide[_ngcontent-%COMP%] {\\n  flex: 0 0 auto;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 0 12px;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n  width: 80px;\\n}\\n\\n.story-slide[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.story-slide[_ngcontent-%COMP%]:first-child {\\n  padding-left: 16px;\\n}\\n\\n.story-slide[_ngcontent-%COMP%]:last-child {\\n  padding-right: 16px;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  position: relative;\\n}\\n\\n.avatar-ring[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  padding: 3px;\\n  background: #fafafa;\\n  border: 1px solid #dbdbdb;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n\\n.avatar-ring.has-story[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n  border: none;\\n  animation: _ngcontent-%COMP%_storyPulse 2s infinite;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.avatar-ring.viewed-story[_ngcontent-%COMP%] {\\n  background: #c7c7c7;\\n  border: 1px solid #dbdbdb;\\n  animation: none;\\n}\\n\\n.avatar-ring.add-ring[_ngcontent-%COMP%] {\\n  background: #fafafa;\\n  border: 2px dashed #dbdbdb;\\n}\\n\\n.avatar-ring.add-ring[_ngcontent-%COMP%]:hover {\\n  border-color: var(--primary-color);\\n  background: rgba(0, 149, 246, 0.05);\\n}\\n\\n.avatar-image[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid #fff;\\n}\\n\\n.avatar-ring[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 20px;\\n}\\n\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 70px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  font-weight: 400;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_storyPulse {\\n  0% {\\n    transform: scale(1);\\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n  }\\n  50% {\\n    transform: scale(1.02);\\n    box-shadow: 0 4px 16px rgba(240, 148, 51, 0.3);\\n  }\\n  100% {\\n    transform: scale(1);\\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n  }\\n}\\n\\n\\n.auto-slide-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  z-index: 5;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  animation: _ngcontent-%COMP%_fadeInSlide 0.3s ease;\\n}\\n\\n.slide-progress[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  background: var(--primary-color, #007bff);\\n  animation: _ngcontent-%COMP%_slideProgress 3s linear infinite;\\n}\\n\\n.slide-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  letter-spacing: 0.5px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInSlide {\\n  from {\\n    opacity: 0;\\n    transform: translateX(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideProgress {\\n  0% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.2);\\n    opacity: 0.7;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.nav-arrow[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 36px;\\n  height: 36px;\\n  border: none;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.95);\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);\\n  color: #262626;\\n  font-size: 16px;\\n  cursor: pointer;\\n  z-index: 10;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #fff;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n  transform: translateY(-50%);\\n}\\n\\n.nav-arrow.nav-left[_ngcontent-%COMP%] {\\n  left: 112px; \\n\\n}\\n\\n.nav-arrow.nav-right[_ngcontent-%COMP%] {\\n  right: 12px;\\n}\\n\\n\\n\\n.nav-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 8px;\\n  padding: 12px 0 8px;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.nav-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  border: none;\\n  background: #dbdbdb;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.nav-dot.active[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  transform: scale(1.2);\\n}\\n\\n.nav-dot[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  opacity: 0.7;\\n}\\n\\n\\n\\n@media (min-width: 1024px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 20px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 90px;\\n    padding: 0 15px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 72px;\\n    height: 72px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 64px;\\n    height: 64px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n    max-width: 80px;\\n  }\\n  .nav-arrow.nav-left[_ngcontent-%COMP%] {\\n    left: 140px; \\n\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n\\n@media (max-width: 1023px) and (min-width: 768px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 16px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 80px;\\n    padding: 0 12px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 64px;\\n    height: 64px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n    font-size: 14px;\\n  }\\n  .nav-arrow.nav-left[_ngcontent-%COMP%] {\\n    left: 120px; \\n\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n\\n@media (max-width: 767px) and (min-width: 481px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 14px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 70px;\\n    padding: 0 10px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 60px;\\n  }\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n}\\n\\n\\n@media (max-width: 480px) {\\n  .stories-track[_ngcontent-%COMP%] {\\n    padding: 12px 0;\\n  }\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 65px;\\n    padding: 0 8px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:first-child {\\n    padding-left: 12px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:last-child {\\n    padding-right: 12px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 52px;\\n    height: 52px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 44px;\\n    height: 44px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 55px;\\n  }\\n  .nav-arrow[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .nav-dots[_ngcontent-%COMP%] {\\n    display: flex;\\n    padding: 8px 0 6px;\\n  }\\n  .nav-dot[_ngcontent-%COMP%] {\\n    width: 6px;\\n    height: 6px;\\n  }\\n}\\n\\n\\n@media (max-width: 360px) {\\n  .story-slide[_ngcontent-%COMP%] {\\n    width: 60px;\\n    padding: 0 6px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:first-child {\\n    padding-left: 8px;\\n  }\\n  .story-slide[_ngcontent-%COMP%]:last-child {\\n    padding-right: 8px;\\n  }\\n  .avatar-ring[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .avatar-image[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n    max-width: 50px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementStart", "ɵɵlistener", "StoriesComponent_div_2_button_1_Template_button_click_0_listener", "i_r3", "ɵɵrestoreView", "_r2", "index", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "goToSlide", "ɵɵelementEnd", "ɵɵclassProp", "currentSlide", "ɵɵtemplate", "StoriesComponent_div_2_button_1_Template", "ɵɵadvance", "ɵɵproperty", "dots", "StoriesComponent_div_12_Template_div_click_0_listener", "storyGroup_r6", "_r5", "$implicit", "openStoryViewer", "StoriesComponent_div_12_Template_img_error_3_listener", "$event", "onImageError", "ɵɵtext", "stories", "length", "getSafeImageUrl", "user", "avatar", "ɵɵsanitizeUrl", "fullName", "ɵɵtextInterpolate", "username", "ɵɵelement", "StoriesComponent_button_14_Template_button_click_0_listener", "_r7", "slideLeft", "canSlideLeft", "StoriesComponent_button_15_Template_button_click_0_listener", "_r8", "slideRight", "canSlideRight", "StoriesComponent", "constructor", "storyService", "authService", "router", "mediaService", "storyGroups", "currentUser", "translateX", "isTransitioning", "showArrows", "showDots", "touchStartX", "touchCurrentX", "isDragging", "<PERSON><PERSON><PERSON><PERSON>iew", "slideWidth", "autoSlideDelay", "isAutoSliding", "ngOnInit", "currentUser$", "subscribe", "loadStories", "ngAfterViewInit", "setTimeout", "calculateResponsiveSettings", "updateSliderState", "startAutoSlide", "window", "addEventListener", "ngOnDestroy", "stopAutoSlide", "storiesSlider", "containerWidth", "nativeElement", "clientWidth", "screenWidth", "innerWidth", "Math", "floor", "max", "totalSlides", "totalPages", "ceil", "Array", "fill", "map", "_", "i", "getStories", "next", "response", "loadMockStories", "error", "console", "autoSlideInterval", "setInterval", "autoSlideNext", "clearInterval", "pauseAutoSlide", "resumeAutoSlide", "maxSlide", "getMaxSlide", "updateSliderPosition", "min", "slideIndex", "onScroll", "onTouchStart", "event", "touches", "clientX", "onTouchMove", "deltaX", "abs", "preventDefault", "onTouchEnd", "threshold", "url", "handleImageError", "openAddStory", "showStoryCreationModal", "modalOverlay", "document", "createElement", "className", "innerHTML", "styles", "textContent", "head", "append<PERSON><PERSON><PERSON>", "body", "handleFileSelect", "input", "files", "file", "log", "name", "type", "showFilePreview", "remove", "previewModal", "fileURL", "URL", "createObjectURL", "isVideo", "startsWith", "previewStyles", "storyGroup", "createStoryViewerModal", "join", "getTimeAgo", "createdAt", "initializeStoryViewer", "modalElement", "currentIndex", "progressTimer", "story<PERSON>iewer", "showStory", "story", "contentElement", "querySelector", "media", "caption", "progressBars", "querySelectorAll", "for<PERSON>ach", "bar", "classList", "add", "startProgressTimer", "nextStory", "previousStory", "toggleLike", "openComments", "shareStory", "observer", "MutationObserver", "mutations", "mutation", "removedNodes", "node", "clearTimeout", "disconnect", "observe", "childList", "duration", "date", "now", "Date", "diff", "getTime", "hours", "minutes", "days", "onMouseEnter", "onMouseLeave", "_id", "isActive", "expiresAt", "ɵɵdirectiveInject", "i1", "StoryService", "i2", "AuthService", "i3", "Router", "i4", "MediaService", "selectors", "viewQuery", "StoriesComponent_Query", "rf", "ctx", "StoriesComponent_div_2_Template", "StoriesComponent_Template_div_touchstart_3_listener", "_r1", "StoriesComponent_Template_div_touchmove_3_listener", "StoriesComponent_Template_div_touchend_3_listener", "StoriesComponent_Template_div_scroll_3_listener", "StoriesComponent_Template_div_mouseenter_3_listener", "StoriesComponent_Template_div_mouseleave_3_listener", "StoriesComponent_Template_div_click_6_listener", "StoriesComponent_div_12_Template", "StoriesComponent_div_13_Template", "StoriesComponent_button_14_Template", "StoriesComponent_button_15_Template", "ɵɵstyleProp", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\stories\\stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\stories\\stories.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { MediaService } from '../../../../core/services/media.service';\n\nimport { StoryService } from '../../../../core/services/story.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { StoryGroup, Story } from '../../../../core/models/story.model';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-stories',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './stories.component.html',\n  styleUrls: ['./stories.component.scss']\n\n\n})\nexport class StoriesComponent implements OnInit, AfterViewInit {\n  @ViewChild('storiesSlider') storiesSlider!: ElementRef<HTMLDivElement>;\n\n  storyGroups: StoryGroup[] = [];\n  currentUser: User | null = null;\n\n  // Slider properties\n  translateX = 0;\n  currentSlide = 0;\n  isTransitioning = false;\n\n  // Navigation properties\n  canSlideLeft = false;\n  canSlideRight = false;\n  showArrows = false;\n  showDots = false;\n  dots: number[] = [];\n\n  // Touch properties\n  touchStartX = 0;\n  touchCurrentX = 0;\n  isDragging = false;\n\n  // Responsive properties\n  slidesPerView = 1;\n  slideWidth = 0;\n\n  // Auto-slide properties\n  autoSlideInterval: any;\n  autoSlideDelay = 3000; // 3 seconds\n  isAutoSliding = true;\n\n  constructor(\n    private storyService: StoryService,\n    private authService: AuthService,\n    private router: Router,\n    private mediaService: MediaService\n  ) {}\n\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n\n    this.loadStories();\n  }\n\n  ngAfterViewInit() {\n    // Initialize slider after view init\n    setTimeout(() => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n      this.startAutoSlide();\n    }, 100);\n\n    // Add resize listener for responsive updates\n    window.addEventListener('resize', () => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n    });\n  }\n\n  ngOnDestroy() {\n    this.stopAutoSlide();\n  }\n\n  calculateResponsiveSettings() {\n    if (!this.storiesSlider) return;\n\n    const containerWidth = this.storiesSlider.nativeElement.clientWidth;\n    const screenWidth = window.innerWidth;\n\n    // Calculate slides per view based on screen size\n    if (screenWidth >= 1024) {\n      this.slidesPerView = Math.floor(containerWidth / 90); // Desktop: 90px per slide\n      this.slideWidth = 90;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 768) {\n      this.slidesPerView = Math.floor(containerWidth / 80); // Tablet: 80px per slide\n      this.slideWidth = 80;\n      this.showArrows = true;\n      this.showDots = false;\n    } else if (screenWidth >= 481) {\n      this.slidesPerView = Math.floor(containerWidth / 70); // Mobile landscape: 70px per slide\n      this.slideWidth = 70;\n      this.showArrows = false;\n      this.showDots = true;\n    } else if (screenWidth >= 361) {\n      this.slidesPerView = Math.floor(containerWidth / 65); // Mobile portrait: 65px per slide\n      this.slideWidth = 65;\n      this.showArrows = false;\n      this.showDots = true;\n    } else {\n      this.slidesPerView = Math.floor(containerWidth / 60); // Very small: 60px per slide\n      this.slideWidth = 60;\n      this.showArrows = false;\n      this.showDots = true;\n    }\n\n    // Ensure minimum slides per view\n    this.slidesPerView = Math.max(this.slidesPerView, 3);\n\n    // Calculate dots for mobile\n    if (this.showDots) {\n      const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n      const totalPages = Math.ceil(totalSlides / this.slidesPerView);\n      this.dots = Array(totalPages).fill(0).map((_, i) => i);\n    }\n  }\n\n  loadStories() {\n    this.storyService.getStories().subscribe({\n      next: (response) => {\n        if (response.storyGroups && response.storyGroups.length > 0) {\n          this.storyGroups = response.storyGroups;\n        } else {\n          // Load mock data if no stories from API\n          this.loadMockStories();\n        }\n        // Update slider after stories load\n        setTimeout(() => {\n          this.calculateResponsiveSettings();\n          this.updateSliderState();\n          this.startAutoSlide();\n        }, 100);\n      },\n      error: (error) => {\n        console.error('Error loading stories:', error);\n        // Load mock data for demonstration\n        this.loadMockStories();\n      }\n    });\n  }\n\n  // Auto-slide methods\n  startAutoSlide() {\n    if (this.storyGroups.length <= this.slidesPerView) return; // No need to auto-slide if all stories fit\n\n    this.stopAutoSlide(); // Clear any existing interval\n    this.autoSlideInterval = setInterval(() => {\n      if (this.isAutoSliding) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  pauseAutoSlide() {\n    this.isAutoSliding = false;\n  }\n\n  resumeAutoSlide() {\n    this.isAutoSliding = true;\n  }\n\n  autoSlideNext() {\n    const maxSlide = this.getMaxSlide();\n    if (this.currentSlide < maxSlide) {\n      this.slideRight();\n    } else {\n      // Reset to beginning\n      this.currentSlide = 0;\n      this.updateSliderPosition();\n    }\n  }\n\n  // Slider navigation methods\n  slideLeft() {\n    if (this.canSlideLeft) {\n      this.pauseAutoSlide();\n      this.currentSlide = Math.max(0, this.currentSlide - 1);\n      this.updateSliderPosition();\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n\n  slideRight() {\n    if (this.canSlideRight) {\n      this.pauseAutoSlide();\n      const maxSlide = this.getMaxSlide();\n      this.currentSlide = Math.min(maxSlide, this.currentSlide + 1);\n      this.updateSliderPosition();\n      // Resume auto-slide after user interaction\n      setTimeout(() => this.resumeAutoSlide(), 5000);\n    }\n  }\n\n  goToSlide(slideIndex: number) {\n    this.pauseAutoSlide();\n    this.currentSlide = slideIndex;\n    this.updateSliderPosition();\n    // Resume auto-slide after user interaction\n    setTimeout(() => this.resumeAutoSlide(), 5000);\n  }\n\n  updateSliderPosition() {\n    this.isTransitioning = true;\n    this.translateX = -this.currentSlide * this.slideWidth * this.slidesPerView;\n    this.updateSliderState();\n\n    // Reset transition flag after animation\n    setTimeout(() => {\n      this.isTransitioning = false;\n    }, 300);\n  }\n\n  updateSliderState() {\n    const maxSlide = this.getMaxSlide();\n    this.canSlideLeft = this.currentSlide > 0;\n    this.canSlideRight = this.currentSlide < maxSlide;\n  }\n\n  getMaxSlide(): number {\n    const totalSlides = this.storyGroups.length + 1; // +1 for \"Add Story\"\n    return Math.max(0, Math.ceil(totalSlides / this.slidesPerView) - 1);\n  }\n\n  onScroll() {\n    // Handle manual scroll if needed\n    this.updateSliderState();\n  }\n\n  // Touch gesture methods\n  onTouchStart(event: TouchEvent) {\n    this.touchStartX = event.touches[0].clientX;\n    this.touchCurrentX = this.touchStartX;\n    this.isDragging = true;\n    this.pauseAutoSlide();\n  }\n\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n\n    this.touchCurrentX = event.touches[0].clientX;\n    const deltaX = this.touchCurrentX - this.touchStartX;\n\n    // Prevent default scrolling\n    if (Math.abs(deltaX) > 10) {\n      event.preventDefault();\n    }\n  }\n\n  onTouchEnd(event: TouchEvent) {\n    if (!this.isDragging) return;\n\n    const deltaX = this.touchCurrentX - this.touchStartX;\n    const threshold = 50; // Minimum swipe distance\n\n    if (Math.abs(deltaX) > threshold) {\n      if (deltaX > 0) {\n        // Swipe right - go to previous slide\n        this.slideLeft();\n      } else {\n        // Swipe left - go to next slide\n        this.slideRight();\n      }\n    } else {\n      // Resume auto-slide if no significant swipe\n      setTimeout(() => this.resumeAutoSlide(), 2000);\n    }\n\n    this.isDragging = false;\n  }\n\n  // Image handling methods\n  getSafeImageUrl(url: string | undefined): string {\n    return this.mediaService.getSafeImageUrl(url, 'user');\n  }\n\n  onImageError(event: Event): void {\n    this.mediaService.handleImageError(event, 'user');\n  }\n\n\n\n  openAddStory() {\n    this.showStoryCreationModal();\n  }\n\n  private showStoryCreationModal() {\n    // Create modal overlay\n    const modalOverlay = document.createElement('div');\n    modalOverlay.className = 'story-creation-modal';\n    modalOverlay.innerHTML = `\n      <div class=\"modal-content\">\n        <div class=\"modal-header\">\n          <h3>Create Story</h3>\n          <button class=\"close-btn\" onclick=\"this.closest('.story-creation-modal').remove()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        <div class=\"modal-body\">\n          <div class=\"creation-options\">\n            <div class=\"option-card camera-option\" onclick=\"document.getElementById('camera-input').click()\">\n              <div class=\"option-icon camera-icon\">\n                <i class=\"fas fa-camera\"></i>\n              </div>\n              <h4>Camera</h4>\n              <p>Take a photo or video</p>\n            </div>\n            <div class=\"option-card gallery-option\" onclick=\"document.getElementById('gallery-input').click()\">\n              <div class=\"option-icon gallery-icon\">\n                <i class=\"fas fa-images\"></i>\n              </div>\n              <h4>Gallery</h4>\n              <p>Choose from your photos</p>\n            </div>\n            <div class=\"option-card create-option\" onclick=\"this.closest('.story-creation-modal').querySelector('.text-story-creator').style.display='block'\">\n              <div class=\"option-icon create-icon\">\n                <i class=\"fas fa-font\"></i>\n              </div>\n              <h4>Create</h4>\n              <p>Design with text and colors</p>\n            </div>\n            <div class=\"option-card live-option\" onclick=\"alert('Live feature coming soon!')\">\n              <div class=\"option-icon live-icon\">\n                <i class=\"fas fa-video\"></i>\n              </div>\n              <h4>Live</h4>\n              <p>Go live with your audience</p>\n            </div>\n          </div>\n\n          <!-- Text Story Creator -->\n          <div class=\"text-story-creator\" style=\"display: none;\">\n            <div class=\"text-editor\">\n              <textarea placeholder=\"What's on your mind?\" maxlength=\"500\"></textarea>\n              <div class=\"color-picker\">\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff6b6b, #feca57)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff6b6b, #feca57)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #48dbfb, #0abde3)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #48dbfb, #0abde3)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #ff9ff3, #f368e0)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #ff9ff3, #f368e0)'\"></div>\n                <div class=\"color-option\" style=\"background: linear-gradient(45deg, #54a0ff, #2e86de)\" onclick=\"this.closest('.text-story-creator').style.background='linear-gradient(45deg, #54a0ff, #2e86de)'\"></div>\n              </div>\n              <button class=\"create-text-story-btn\" onclick=\"alert('Text story created! (Demo)')\">Create Story</button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Hidden file inputs -->\n        <input type=\"file\" id=\"camera-input\" accept=\"image/*,video/*\" capture=\"environment\" style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n        <input type=\"file\" id=\"gallery-input\" accept=\"image/*,video/*\" multiple style=\"display: none;\" onchange=\"handleFileSelect(this)\">\n      </div>\n    `;\n\n    // Add styles\n    const styles = document.createElement('style');\n    styles.textContent = `\n      .story-creation-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: rgba(0, 0, 0, 0.8);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 10000;\n        animation: fadeIn 0.3s ease;\n      }\n\n      .modal-content {\n        background: white;\n        border-radius: 16px;\n        width: 90%;\n        max-width: 400px;\n        max-height: 80vh;\n        overflow-y: auto;\n      }\n\n      .modal-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem 1.5rem;\n        border-bottom: 1px solid #eee;\n      }\n\n      .modal-header h3 {\n        margin: 0;\n        font-size: 1.25rem;\n        font-weight: 600;\n      }\n\n      .close-btn {\n        background: none;\n        border: none;\n        font-size: 1.25rem;\n        cursor: pointer;\n        padding: 0.5rem;\n        border-radius: 50%;\n        transition: background 0.2s;\n      }\n\n      .close-btn:hover {\n        background: #f0f0f0;\n      }\n\n      .creation-options {\n        display: grid;\n        grid-template-columns: 1fr;\n        gap: 1rem;\n        padding: 1.5rem;\n      }\n\n      .option-card {\n        display: flex;\n        align-items: center;\n        padding: 1.5rem;\n        border: none;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n        color: white;\n        position: relative;\n        overflow: hidden;\n      }\n\n      .option-card:hover {\n        transform: translateY(-3px) scale(1.02);\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n      }\n\n      .camera-option {\n        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n      }\n\n      .gallery-option {\n        background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);\n      }\n\n      .create-option {\n        background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\n      }\n\n      .live-option {\n        background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);\n      }\n\n      .option-icon {\n        width: 60px;\n        height: 60px;\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 1rem;\n        backdrop-filter: blur(10px);\n        border: 2px solid rgba(255, 255, 255, 0.3);\n      }\n\n      .option-icon i {\n        color: white;\n        font-size: 1.5rem;\n      }\n\n      .option-card h4 {\n        margin: 0 0 0.25rem 0;\n        font-size: 1.1rem;\n        font-weight: 700;\n        color: white;\n      }\n\n      .option-card p {\n        margin: 0;\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 0.875rem;\n      }\n\n      .text-story-creator {\n        padding: 1.5rem;\n        background: linear-gradient(45deg, #ff6b6b, #feca57);\n        border-radius: 12px;\n        margin: 1rem;\n      }\n\n      .text-editor textarea {\n        width: 100%;\n        min-height: 120px;\n        border: none;\n        background: rgba(255, 255, 255, 0.9);\n        border-radius: 8px;\n        padding: 1rem;\n        font-size: 1.125rem;\n        resize: none;\n        margin-bottom: 1rem;\n      }\n\n      .color-picker {\n        display: flex;\n        gap: 0.5rem;\n        margin-bottom: 1rem;\n      }\n\n      .color-option {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        cursor: pointer;\n        border: 3px solid white;\n        transition: transform 0.2s;\n      }\n\n      .color-option:hover {\n        transform: scale(1.1);\n      }\n\n      .create-text-story-btn {\n        background: white;\n        border: none;\n        padding: 0.75rem 1.5rem;\n        border-radius: 25px;\n        font-weight: 600;\n        cursor: pointer;\n        transition: transform 0.2s;\n      }\n\n      .create-text-story-btn:hover {\n        transform: scale(1.05);\n      }\n\n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n    `;\n\n    document.head.appendChild(styles);\n    document.body.appendChild(modalOverlay);\n\n    // Add global function for file handling\n    (window as any).handleFileSelect = (input: HTMLInputElement) => {\n      const files = input.files;\n      if (files && files.length > 0) {\n        const file = files[0];\n        console.log('Selected file:', file.name, file.type);\n\n        // Create preview modal\n        this.showFilePreview(file);\n        modalOverlay.remove();\n      }\n    };\n  }\n\n  private showFilePreview(file: File) {\n    const previewModal = document.createElement('div');\n    previewModal.className = 'story-preview-modal';\n\n    const fileURL = URL.createObjectURL(file);\n    const isVideo = file.type.startsWith('video/');\n\n    previewModal.innerHTML = `\n      <div class=\"preview-content\">\n        <div class=\"preview-header\">\n          <button class=\"back-btn\" onclick=\"this.closest('.story-preview-modal').remove()\">\n            <i class=\"fas fa-arrow-left\"></i>\n          </button>\n          <h3>Create Story</h3>\n          <button class=\"share-btn\" onclick=\"alert('Story shared! (Demo)')\">Share</button>\n        </div>\n        <div class=\"preview-media\">\n          ${isVideo ?\n            `<video src=\"${fileURL}\" controls autoplay muted></video>` :\n            `<img src=\"${fileURL}\" alt=\"Story preview\">`\n          }\n          <div class=\"story-tools\">\n            <button class=\"tool-btn\" onclick=\"alert('Add text feature coming soon!')\">\n              <i class=\"fas fa-font\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Add stickers feature coming soon!')\">\n              <i class=\"fas fa-smile\"></i>\n            </button>\n            <button class=\"tool-btn\" onclick=\"alert('Tag products feature coming soon!')\">\n              <i class=\"fas fa-tag\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n    `;\n\n    // Add preview styles\n    const previewStyles = document.createElement('style');\n    previewStyles.textContent = `\n      .story-preview-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: black;\n        z-index: 10001;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .preview-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 1rem;\n        background: rgba(0, 0, 0, 0.5);\n        color: white;\n      }\n\n      .back-btn, .share-btn {\n        background: none;\n        border: none;\n        color: white;\n        font-size: 1rem;\n        cursor: pointer;\n        padding: 0.5rem;\n      }\n\n      .share-btn {\n        background: #007bff;\n        border-radius: 20px;\n        padding: 0.5rem 1rem;\n      }\n\n      .preview-media {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        position: relative;\n      }\n\n      .preview-media img,\n      .preview-media video {\n        max-width: 100%;\n        max-height: 100%;\n        object-fit: contain;\n      }\n\n      .story-tools {\n        position: absolute;\n        right: 1rem;\n        top: 50%;\n        transform: translateY(-50%);\n        display: flex;\n        flex-direction: column;\n        gap: 1rem;\n      }\n\n      .tool-btn {\n        width: 48px;\n        height: 48px;\n        background: rgba(255, 255, 255, 0.2);\n        border: none;\n        border-radius: 50%;\n        color: white;\n        font-size: 1.25rem;\n        cursor: pointer;\n        backdrop-filter: blur(10px);\n        transition: background 0.2s;\n      }\n\n      .tool-btn:hover {\n        background: rgba(255, 255, 255, 0.3);\n      }\n    `;\n\n    document.head.appendChild(previewStyles);\n    document.body.appendChild(previewModal);\n  }\n\n  openStoryViewer(storyGroup: StoryGroup) {\n    if (storyGroup.stories.length > 0) {\n      // Create Instagram-like story viewer modal\n      this.createStoryViewerModal(storyGroup);\n    } else {\n      console.log('No stories available for:', storyGroup.user.username);\n    }\n  }\n\n  private createStoryViewerModal(storyGroup: StoryGroup) {\n    // Create modal overlay\n    const modalOverlay = document.createElement('div');\n    modalOverlay.className = 'story-viewer-modal';\n    modalOverlay.innerHTML = `\n      <div class=\"story-viewer-container\">\n        <div class=\"story-viewer-content\">\n          <!-- Progress bars -->\n          <div class=\"progress-container\">\n            ${storyGroup.stories.map((_, index) => `\n              <div class=\"progress-bar ${index === 0 ? 'active' : ''}\" data-index=\"${index}\">\n                <div class=\"progress-fill\"></div>\n              </div>\n            `).join('')}\n          </div>\n\n          <!-- Header -->\n          <div class=\"story-header\">\n            <div class=\"user-info\">\n              <img src=\"${storyGroup.user.avatar || '/assets/images/default-avatar.png'}\"\n                   alt=\"${storyGroup.user.fullName}\" class=\"user-avatar\">\n              <div class=\"user-details\">\n                <span class=\"username\">${storyGroup.user.username}</span>\n                <span class=\"timestamp\">${this.getTimeAgo(storyGroup.stories[0].createdAt)}</span>\n              </div>\n            </div>\n            <div class=\"story-controls\">\n              <button class=\"btn-close\" onclick=\"this.closest('.story-viewer-modal').remove()\">\n                <i class=\"fas fa-times\"></i>\n              </button>\n            </div>\n          </div>\n\n          <!-- Story content -->\n          <div class=\"story-content\" id=\"story-content\">\n            <!-- Will be populated dynamically -->\n          </div>\n\n          <!-- Navigation areas -->\n          <div class=\"nav-area nav-prev\" onclick=\"window.storyViewer.previousStory()\"></div>\n          <div class=\"nav-area nav-next\" onclick=\"window.storyViewer.nextStory()\"></div>\n\n          <!-- Bottom actions -->\n          <div class=\"story-actions\">\n            <div class=\"social-actions\">\n              <button class=\"social-btn like\" onclick=\"window.storyViewer.toggleLike()\">\n                <i class=\"fas fa-heart\"></i>\n              </button>\n              <button class=\"social-btn comment\" onclick=\"window.storyViewer.openComments()\">\n                <i class=\"fas fa-comment\"></i>\n              </button>\n              <button class=\"social-btn share\" onclick=\"window.storyViewer.shareStory()\">\n                <i class=\"fas fa-share\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    `;\n\n    // Add styles\n    const styles = document.createElement('style');\n    styles.textContent = `\n      .story-viewer-modal {\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100vw;\n        height: 100vh;\n        background: #000;\n        z-index: 10000;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        animation: fadeIn 0.3s ease;\n      }\n\n      .story-viewer-container {\n        width: 100%;\n        height: 100%;\n        max-width: 400px;\n        position: relative;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .story-viewer-content {\n        flex: 1;\n        position: relative;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .progress-container {\n        display: flex;\n        gap: 2px;\n        padding: 8px 20px;\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        z-index: 10;\n      }\n\n      .progress-bar {\n        flex: 1;\n        height: 2px;\n        background: rgba(255,255,255,0.3);\n        border-radius: 1px;\n        overflow: hidden;\n      }\n\n      .progress-fill {\n        height: 100%;\n        background: #fff;\n        width: 0%;\n        transition: width 0.1s ease;\n      }\n\n      .progress-bar.active .progress-fill {\n        animation: progress 15s linear;\n      }\n\n      .progress-bar.completed .progress-fill {\n        width: 100%;\n      }\n\n      @keyframes progress {\n        from { width: 0%; }\n        to { width: 100%; }\n      }\n\n      .story-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 16px 20px;\n        background: linear-gradient(180deg, rgba(0,0,0,0.6) 0%, transparent 100%);\n        position: relative;\n        z-index: 10;\n      }\n\n      .user-info {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n      }\n\n      .user-avatar {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        border: 2px solid #fff;\n      }\n\n      .user-details {\n        display: flex;\n        flex-direction: column;\n      }\n\n      .username {\n        color: #fff;\n        font-weight: 600;\n        font-size: 0.9rem;\n      }\n\n      .timestamp {\n        color: rgba(255,255,255,0.7);\n        font-size: 0.8rem;\n      }\n\n      .btn-close {\n        background: none;\n        border: none;\n        color: #fff;\n        font-size: 1.2rem;\n        cursor: pointer;\n        padding: 8px;\n      }\n\n      .story-content {\n        flex: 1;\n        position: relative;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #000;\n      }\n\n      .story-media {\n        max-width: 100%;\n        max-height: 100%;\n        object-fit: contain;\n      }\n\n      .nav-area {\n        position: absolute;\n        top: 0;\n        bottom: 0;\n        width: 30%;\n        cursor: pointer;\n        z-index: 5;\n      }\n\n      .nav-prev {\n        left: 0;\n      }\n\n      .nav-next {\n        right: 0;\n      }\n\n      .story-actions {\n        padding: 16px 20px;\n        background: linear-gradient(0deg, rgba(0,0,0,0.6) 0%, transparent 100%);\n      }\n\n      .social-actions {\n        display: flex;\n        justify-content: center;\n        gap: 24px;\n      }\n\n      .social-btn {\n        background: none;\n        border: none;\n        color: #fff;\n        font-size: 1.5rem;\n        cursor: pointer;\n        padding: 8px;\n        border-radius: 50%;\n        transition: all 0.2s ease;\n      }\n\n      .social-btn:hover {\n        background: rgba(255,255,255,0.1);\n        transform: scale(1.1);\n      }\n\n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n\n      @media (max-width: 768px) {\n        .story-viewer-container {\n          max-width: 100%;\n        }\n      }\n    `;\n\n    document.head.appendChild(styles);\n    document.body.appendChild(modalOverlay);\n\n    // Initialize story viewer functionality\n    this.initializeStoryViewer(storyGroup, modalOverlay);\n  }\n\n  private initializeStoryViewer(storyGroup: StoryGroup, modalElement: HTMLElement) {\n    let currentIndex = 0;\n    let progressTimer: any;\n\n    const storyViewer = {\n      currentIndex,\n      stories: storyGroup.stories,\n\n      showStory: (index: number) => {\n        const story = storyGroup.stories[index];\n        const contentElement = modalElement.querySelector('#story-content');\n\n        if (contentElement) {\n          if (story.media.type === 'image') {\n            contentElement.innerHTML = `\n              <img src=\"${story.media.url}\" alt=\"${story.caption || ''}\" class=\"story-media\">\n            `;\n          } else {\n            contentElement.innerHTML = `\n              <video src=\"${story.media.url}\" class=\"story-media\" autoplay muted>\n              </video>\n            `;\n          }\n        }\n\n        // Update progress bars\n        const progressBars = modalElement.querySelectorAll('.progress-bar');\n        progressBars.forEach((bar, i) => {\n          bar.classList.remove('active', 'completed');\n          if (i < index) {\n            bar.classList.add('completed');\n          } else if (i === index) {\n            bar.classList.add('active');\n          }\n        });\n\n        this.startProgressTimer(15000); // 15 seconds\n      },\n\n      nextStory: () => {\n        if (currentIndex < storyGroup.stories.length - 1) {\n          currentIndex++;\n          storyViewer.showStory(currentIndex);\n        } else {\n          modalElement.remove();\n        }\n      },\n\n      previousStory: () => {\n        if (currentIndex > 0) {\n          currentIndex--;\n          storyViewer.showStory(currentIndex);\n        }\n      },\n\n      toggleLike: () => {\n        console.log('Toggle like for story');\n      },\n\n      openComments: () => {\n        console.log('Open comments for story');\n      },\n\n      shareStory: () => {\n        console.log('Share story');\n      }\n    };\n\n    // Make it globally accessible for onclick handlers\n    (window as any).storyViewer = storyViewer;\n\n    // Show first story\n    storyViewer.showStory(0);\n\n    // Cleanup when modal is removed\n    const observer = new MutationObserver((mutations) => {\n      mutations.forEach((mutation) => {\n        if (mutation.type === 'childList') {\n          mutation.removedNodes.forEach((node) => {\n            if (node === modalElement) {\n              delete (window as any).storyViewer;\n              if (progressTimer) clearTimeout(progressTimer);\n              observer.disconnect();\n            }\n          });\n        }\n      });\n    });\n\n    observer.observe(document.body, { childList: true });\n  }\n\n  private startProgressTimer(duration: number) {\n    // This would be implemented to handle progress timing\n  }\n\n  private getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diff = now.getTime() - new Date(date).getTime();\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n\n    if (hours < 1) {\n      const minutes = Math.floor(diff / (1000 * 60));\n      return `${minutes}m`;\n    } else if (hours < 24) {\n      return `${hours}h`;\n    } else {\n      const days = Math.floor(hours / 24);\n      return `${days}d`;\n    }\n  }\n\n  // Mouse hover events for desktop\n  onMouseEnter() {\n    this.pauseAutoSlide();\n  }\n\n  onMouseLeave() {\n    this.resumeAutoSlide();\n  }\n\n  private loadMockStories() {\n    // Mock story groups for demonstration\n    this.storyGroups = [\n      {\n        user: {\n          _id: 'user1',\n          username: 'fashionista_maya',\n          fullName: 'Maya Sharma',\n          avatar: '/assets/images/users/maya.jpg'\n        },\n        stories: [\n          {\n            _id: 'story1',\n            media: { type: 'image', url: '/assets/images/stories/story1.jpg', duration: 15000 },\n            caption: 'New summer collection! 🌞',\n            isActive: true,\n            createdAt: new Date(),\n            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)\n          }\n        ]\n      },\n      {\n        user: {\n          _id: 'user2',\n          username: 'style_guru_raj',\n          fullName: 'Raj Patel',\n          avatar: '/assets/images/users/raj.jpg'\n        },\n        stories: [\n          {\n            _id: 'story2',\n            media: { type: 'video', url: '/assets/videos/stories/story2.mp4', duration: 20000 },\n            caption: 'Check out this amazing outfit! 👔',\n            isActive: true,\n            createdAt: new Date(),\n            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)\n          }\n        ]\n      },\n      {\n        user: {\n          _id: 'user3',\n          username: 'trendy_priya',\n          fullName: 'Priya Singh',\n          avatar: '/assets/images/users/priya.jpg'\n        },\n        stories: [\n          {\n            _id: 'story3',\n            media: { type: 'image', url: '/assets/images/stories/story3.jpg', duration: 15000 },\n            caption: 'Weekend vibes! ✨',\n            isActive: true,\n            createdAt: new Date(),\n            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)\n          }\n        ]\n      },\n      {\n        user: {\n          _id: 'user4',\n          username: 'fashion_forward',\n          fullName: 'Arjun Kumar',\n          avatar: '/assets/images/users/arjun.jpg'\n        },\n        stories: [\n          {\n            _id: 'story4',\n            media: { type: 'image', url: '/assets/images/stories/story4.jpg', duration: 15000 },\n            caption: 'Casual Friday look! 👕',\n            isActive: true,\n            createdAt: new Date(),\n            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)\n          }\n        ]\n      },\n      {\n        user: {\n          _id: 'user5',\n          username: 'chic_style',\n          fullName: 'Sneha Reddy',\n          avatar: '/assets/images/users/sneha.jpg'\n        },\n        stories: [\n          {\n            _id: 'story5',\n            media: { type: 'image', url: '/assets/images/stories/story5.jpg', duration: 15000 },\n            caption: 'Ethnic wear collection! 🌺',\n            isActive: true,\n            createdAt: new Date(),\n            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)\n          }\n        ]\n      },\n      {\n        user: {\n          _id: 'user6',\n          username: 'modern_wardrobe',\n          fullName: 'Vikram Joshi',\n          avatar: '/assets/images/users/vikram.jpg'\n        },\n        stories: [\n          {\n            _id: 'story6',\n            media: { type: 'image', url: '/assets/images/stories/story6.jpg', duration: 15000 },\n            caption: 'Office wear essentials! 💼',\n            isActive: true,\n            createdAt: new Date(),\n            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)\n          }\n        ]\n      }\n    ] as StoryGroup[];\n\n    // Update slider after mock stories load\n    setTimeout(() => {\n      this.calculateResponsiveSettings();\n      this.updateSliderState();\n      this.startAutoSlide();\n    }, 100);\n  }\n}\n", "<div class=\"stories-section\">\n  <div class=\"stories-slider-wrapper\">\n    <!-- Navigation Dots (Mobile) -->\n    <div class=\"nav-dots\" *ngIf=\"showDots\">\n      <button \n        *ngFor=\"let dot of dots; let i = index\"\n        class=\"nav-dot\"\n        [class.active]=\"i === currentSlide\"\n        (click)=\"goToSlide(i)\">\n      </button>\n    </div>\n\n    <!-- Stories Slider Container -->\n    <div class=\"stories-slider\"\n         #storiesSlider\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\"\n         (scroll)=\"onScroll()\"\n         (mouseenter)=\"onMouseEnter()\"\n         (mouseleave)=\"onMouseLeave()\">\n      \n      <!-- Stories Track -->\n      <div class=\"stories-track\" \n           [style.transform]=\"'translateX(' + translateX + 'px)'\"\n           [style.transition]=\"isTransitioning ? 'transform 0.3s ease-out' : 'none'\">\n        \n        <!-- Add Story -->\n        <div class=\"story-slide add-story\" (click)=\"openAddStory()\">\n          <div class=\"story-avatar\">\n            <div class=\"avatar-ring add-ring\">\n              <i class=\"fas fa-plus\"></i>\n            </div>\n          </div>\n          <span class=\"story-username\">Your Story</span>\n        </div>\n\n        <!-- User Stories -->\n        <div \n          *ngFor=\"let storyGroup of storyGroups; let i = index\"\n          class=\"story-slide\"\n          (click)=\"openStoryViewer(storyGroup)\"\n        >\n          <div class=\"story-avatar\">\n            <div class=\"avatar-ring\" [class.has-story]=\"storyGroup.stories.length > 0\">\n              <img [src]=\"getSafeImageUrl(storyGroup.user.avatar)\"\n                   [alt]=\"storyGroup.user.fullName\"\n                   (error)=\"onImageError($event)\"\n                   class=\"avatar-image\">\n            </div>\n          </div>\n          <span class=\"story-username\">{{ storyGroup.user.username }}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Auto-slide Indicator -->\n    <div class=\"auto-slide-indicator\" *ngIf=\"isAutoSliding && storyGroups.length > slidesPerView\">\n      <div class=\"slide-progress\"></div>\n      <span class=\"slide-text\">Auto-sliding</span>\n    </div>\n\n    <!-- Navigation Arrows (Desktop/Tablet) -->\n    <button class=\"nav-arrow nav-left\"\n            (click)=\"slideLeft()\"\n            [disabled]=\"!canSlideLeft\"\n            *ngIf=\"showArrows\">\n      <i class=\"fas fa-chevron-left\"></i>\n    </button>\n\n    <button class=\"nav-arrow nav-right\"\n            (click)=\"slideRight()\"\n            [disabled]=\"!canSlideRight\"\n            *ngIf=\"showArrows\">\n      <i class=\"fas fa-chevron-right\"></i>\n    </button>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;;;;;;;ICGxCC,EAAA,CAAAC,cAAA,iBAIyB;IAAvBD,EAAA,CAAAE,UAAA,mBAAAC,iEAAA;MAAA,MAAAC,IAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,SAAA,CAAAP,IAAA,CAAY;IAAA,EAAC;IACxBJ,EAAA,CAAAY,YAAA,EAAS;;;;;IAFPZ,EAAA,CAAAa,WAAA,WAAAT,IAAA,KAAAI,MAAA,CAAAM,YAAA,CAAmC;;;;;IAJvCd,EAAA,CAAAC,cAAA,cAAuC;IACrCD,EAAA,CAAAe,UAAA,IAAAC,wCAAA,qBAIyB;IAE3BhB,EAAA,CAAAY,YAAA,EAAM;;;;IALcZ,EAAA,CAAAiB,SAAA,EAAS;IAATjB,EAAA,CAAAkB,UAAA,YAAAV,MAAA,CAAAW,IAAA,CAAS;;;;;;IAiCzBnB,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAkB,sDAAA;MAAA,MAAAC,aAAA,GAAArB,EAAA,CAAAK,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAf,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgB,eAAA,CAAAH,aAAA,CAA2B;IAAA,EAAC;IAIjCrB,EAFJ,CAAAC,cAAA,aAA0B,cACmD,cAI/C;IADrBD,EAAA,CAAAE,UAAA,mBAAAuB,sDAAAC,MAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAiB,GAAA;MAAA,MAAAd,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAmB,YAAA,CAAAD,MAAA,CAAoB;IAAA,EAAC;IAGvC1B,EALI,CAAAY,YAAA,EAG0B,EACtB,EACF;IACNZ,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAA4B,MAAA,GAA8B;IAC7D5B,EAD6D,CAAAY,YAAA,EAAO,EAC9D;;;;;IARuBZ,EAAA,CAAAiB,SAAA,GAAiD;IAAjDjB,EAAA,CAAAa,WAAA,cAAAQ,aAAA,CAAAQ,OAAA,CAAAC,MAAA,KAAiD;IACnE9B,EAAA,CAAAiB,SAAA,EAA+C;IAC/CjB,EADA,CAAAkB,UAAA,QAAAV,MAAA,CAAAuB,eAAA,CAAAV,aAAA,CAAAW,IAAA,CAAAC,MAAA,GAAAjC,EAAA,CAAAkC,aAAA,CAA+C,QAAAb,aAAA,CAAAW,IAAA,CAAAG,QAAA,CACf;IAKZnC,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAoC,iBAAA,CAAAf,aAAA,CAAAW,IAAA,CAAAK,QAAA,CAA8B;;;;;IAMjErC,EAAA,CAAAC,cAAA,cAA8F;IAC5FD,EAAA,CAAAsC,SAAA,cAAkC;IAClCtC,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAA4B,MAAA,mBAAY;IACvC5B,EADuC,CAAAY,YAAA,EAAO,EACxC;;;;;;IAGNZ,EAAA,CAAAC,cAAA,iBAG2B;IAFnBD,EAAA,CAAAE,UAAA,mBAAAqC,4DAAA;MAAAvC,EAAA,CAAAK,aAAA,CAAAmC,GAAA;MAAA,MAAAhC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiC,SAAA,EAAW;IAAA,EAAC;IAG3BzC,EAAA,CAAAsC,SAAA,YAAmC;IACrCtC,EAAA,CAAAY,YAAA,EAAS;;;;IAHDZ,EAAA,CAAAkB,UAAA,cAAAV,MAAA,CAAAkC,YAAA,CAA0B;;;;;;IAKlC1C,EAAA,CAAAC,cAAA,iBAG2B;IAFnBD,EAAA,CAAAE,UAAA,mBAAAyC,4DAAA;MAAA3C,EAAA,CAAAK,aAAA,CAAAuC,GAAA;MAAA,MAAApC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAqC,UAAA,EAAY;IAAA,EAAC;IAG5B7C,EAAA,CAAAsC,SAAA,YAAoC;IACtCtC,EAAA,CAAAY,YAAA,EAAS;;;;IAHDZ,EAAA,CAAAkB,UAAA,cAAAV,MAAA,CAAAsC,aAAA,CAA2B;;;ADrDvC,OAAM,MAAOC,gBAAgB;EAgC3BC,YACUC,YAA0B,EAC1BC,WAAwB,EACxBC,MAAc,EACdC,YAA0B;IAH1B,KAAAH,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IAjCtB,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,WAAW,GAAgB,IAAI;IAE/B;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAzC,YAAY,GAAG,CAAC;IAChB,KAAA0C,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAd,YAAY,GAAG,KAAK;IACpB,KAAAI,aAAa,GAAG,KAAK;IACrB,KAAAW,UAAU,GAAG,KAAK;IAClB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAvC,IAAI,GAAa,EAAE;IAEnB;IACA,KAAAwC,WAAW,GAAG,CAAC;IACf,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,UAAU,GAAG,KAAK;IAElB;IACA,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,UAAU,GAAG,CAAC;IAId,KAAAC,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;EAOjB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAAChB,WAAW,CAACiB,YAAY,CAACC,SAAS,CAACpC,IAAI,IAAG;MAC7C,IAAI,CAACsB,WAAW,GAAGtB,IAAI;IACzB,CAAC,CAAC;IAEF,IAAI,CAACqC,WAAW,EAAE;EACpB;EAEAC,eAAeA,CAAA;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,2BAA2B,EAAE;MAClC,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAACC,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;IAEP;IACAC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAACJ,2BAA2B,EAAE;MAClC,IAAI,CAACC,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAI,WAAWA,CAAA;IACT,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAN,2BAA2BA,CAAA;IACzB,IAAI,CAAC,IAAI,CAACO,aAAa,EAAE;IAEzB,MAAMC,cAAc,GAAG,IAAI,CAACD,aAAa,CAACE,aAAa,CAACC,WAAW;IACnE,MAAMC,WAAW,GAAGR,MAAM,CAACS,UAAU;IAErC;IACA,IAAID,WAAW,IAAI,IAAI,EAAE;MACvB,IAAI,CAACrB,aAAa,GAAGuB,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACjB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACN,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;KACtB,MAAM,IAAIyB,WAAW,IAAI,GAAG,EAAE;MAC7B,IAAI,CAACrB,aAAa,GAAGuB,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACjB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACN,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;KACtB,MAAM,IAAIyB,WAAW,IAAI,GAAG,EAAE;MAC7B,IAAI,CAACrB,aAAa,GAAGuB,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACjB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACN,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;KACrB,MAAM,IAAIyB,WAAW,IAAI,GAAG,EAAE;MAC7B,IAAI,CAACrB,aAAa,GAAGuB,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACjB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACN,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;KACrB,MAAM;MACL,IAAI,CAACI,aAAa,GAAGuB,IAAI,CAACC,KAAK,CAACN,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAACjB,UAAU,GAAG,EAAE;MACpB,IAAI,CAACN,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,QAAQ,GAAG,IAAI;;IAGtB;IACA,IAAI,CAACI,aAAa,GAAGuB,IAAI,CAACE,GAAG,CAAC,IAAI,CAACzB,aAAa,EAAE,CAAC,CAAC;IAEpD;IACA,IAAI,IAAI,CAACJ,QAAQ,EAAE;MACjB,MAAM8B,WAAW,GAAG,IAAI,CAACnC,WAAW,CAACvB,MAAM,GAAG,CAAC,CAAC,CAAC;MACjD,MAAM2D,UAAU,GAAGJ,IAAI,CAACK,IAAI,CAACF,WAAW,GAAG,IAAI,CAAC1B,aAAa,CAAC;MAC9D,IAAI,CAAC3C,IAAI,GAAGwE,KAAK,CAACF,UAAU,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;;EAE1D;EAEA1B,WAAWA,CAAA;IACT,IAAI,CAACpB,YAAY,CAAC+C,UAAU,EAAE,CAAC5B,SAAS,CAAC;MACvC6B,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAAC7C,WAAW,IAAI6C,QAAQ,CAAC7C,WAAW,CAACvB,MAAM,GAAG,CAAC,EAAE;UAC3D,IAAI,CAACuB,WAAW,GAAG6C,QAAQ,CAAC7C,WAAW;SACxC,MAAM;UACL;UACA,IAAI,CAAC8C,eAAe,EAAE;;QAExB;QACA5B,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,2BAA2B,EAAE;UAClC,IAAI,CAACC,iBAAiB,EAAE;UACxB,IAAI,CAACC,cAAc,EAAE;QACvB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD0B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;QACA,IAAI,CAACD,eAAe,EAAE;MACxB;KACD,CAAC;EACJ;EAEA;EACAzB,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACrB,WAAW,CAACvB,MAAM,IAAI,IAAI,CAACgC,aAAa,EAAE,OAAO,CAAC;IAE3D,IAAI,CAACgB,aAAa,EAAE,CAAC,CAAC;IACtB,IAAI,CAACwB,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,IAAI,CAACtC,aAAa,EAAE;QACtB,IAAI,CAACuC,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAACxC,cAAc,CAAC;EACzB;EAEAc,aAAaA,CAAA;IACX,IAAI,IAAI,CAACwB,iBAAiB,EAAE;MAC1BG,aAAa,CAAC,IAAI,CAACH,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEAI,cAAcA,CAAA;IACZ,IAAI,CAACzC,aAAa,GAAG,KAAK;EAC5B;EAEA0C,eAAeA,CAAA;IACb,IAAI,CAAC1C,aAAa,GAAG,IAAI;EAC3B;EAEAuC,aAAaA,CAAA;IACX,MAAMI,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE;IACnC,IAAI,IAAI,CAAC/F,YAAY,GAAG8F,QAAQ,EAAE;MAChC,IAAI,CAAC/D,UAAU,EAAE;KAClB,MAAM;MACL;MACA,IAAI,CAAC/B,YAAY,GAAG,CAAC;MACrB,IAAI,CAACgG,oBAAoB,EAAE;;EAE/B;EAEA;EACArE,SAASA,CAAA;IACP,IAAI,IAAI,CAACC,YAAY,EAAE;MACrB,IAAI,CAACgE,cAAc,EAAE;MACrB,IAAI,CAAC5F,YAAY,GAAGuE,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,IAAI,CAACzE,YAAY,GAAG,CAAC,CAAC;MACtD,IAAI,CAACgG,oBAAoB,EAAE;MAC3B;MACAvC,UAAU,CAAC,MAAM,IAAI,CAACoC,eAAe,EAAE,EAAE,IAAI,CAAC;;EAElD;EAEA9D,UAAUA,CAAA;IACR,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAAC4D,cAAc,EAAE;MACrB,MAAME,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE;MACnC,IAAI,CAAC/F,YAAY,GAAGuE,IAAI,CAAC0B,GAAG,CAACH,QAAQ,EAAE,IAAI,CAAC9F,YAAY,GAAG,CAAC,CAAC;MAC7D,IAAI,CAACgG,oBAAoB,EAAE;MAC3B;MACAvC,UAAU,CAAC,MAAM,IAAI,CAACoC,eAAe,EAAE,EAAE,IAAI,CAAC;;EAElD;EAEAhG,SAASA,CAACqG,UAAkB;IAC1B,IAAI,CAACN,cAAc,EAAE;IACrB,IAAI,CAAC5F,YAAY,GAAGkG,UAAU;IAC9B,IAAI,CAACF,oBAAoB,EAAE;IAC3B;IACAvC,UAAU,CAAC,MAAM,IAAI,CAACoC,eAAe,EAAE,EAAE,IAAI,CAAC;EAChD;EAEAG,oBAAoBA,CAAA;IAClB,IAAI,CAACtD,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACD,UAAU,GAAG,CAAC,IAAI,CAACzC,YAAY,GAAG,IAAI,CAACiD,UAAU,GAAG,IAAI,CAACD,aAAa;IAC3E,IAAI,CAACW,iBAAiB,EAAE;IAExB;IACAF,UAAU,CAAC,MAAK;MACd,IAAI,CAACf,eAAe,GAAG,KAAK;IAC9B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAiB,iBAAiBA,CAAA;IACf,MAAMmC,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE;IACnC,IAAI,CAACnE,YAAY,GAAG,IAAI,CAAC5B,YAAY,GAAG,CAAC;IACzC,IAAI,CAACgC,aAAa,GAAG,IAAI,CAAChC,YAAY,GAAG8F,QAAQ;EACnD;EAEAC,WAAWA,CAAA;IACT,MAAMrB,WAAW,GAAG,IAAI,CAACnC,WAAW,CAACvB,MAAM,GAAG,CAAC,CAAC,CAAC;IACjD,OAAOuD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACK,IAAI,CAACF,WAAW,GAAG,IAAI,CAAC1B,aAAa,CAAC,GAAG,CAAC,CAAC;EACrE;EAEAmD,QAAQA,CAAA;IACN;IACA,IAAI,CAACxC,iBAAiB,EAAE;EAC1B;EAEA;EACAyC,YAAYA,CAACC,KAAiB;IAC5B,IAAI,CAACxD,WAAW,GAAGwD,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IAC3C,IAAI,CAACzD,aAAa,GAAG,IAAI,CAACD,WAAW;IACrC,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC6C,cAAc,EAAE;EACvB;EAEAY,WAAWA,CAACH,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAACtD,UAAU,EAAE;IAEtB,IAAI,CAACD,aAAa,GAAGuD,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IAC7C,MAAME,MAAM,GAAG,IAAI,CAAC3D,aAAa,GAAG,IAAI,CAACD,WAAW;IAEpD;IACA,IAAI0B,IAAI,CAACmC,GAAG,CAACD,MAAM,CAAC,GAAG,EAAE,EAAE;MACzBJ,KAAK,CAACM,cAAc,EAAE;;EAE1B;EAEAC,UAAUA,CAACP,KAAiB;IAC1B,IAAI,CAAC,IAAI,CAACtD,UAAU,EAAE;IAEtB,MAAM0D,MAAM,GAAG,IAAI,CAAC3D,aAAa,GAAG,IAAI,CAACD,WAAW;IACpD,MAAMgE,SAAS,GAAG,EAAE,CAAC,CAAC;IAEtB,IAAItC,IAAI,CAACmC,GAAG,CAACD,MAAM,CAAC,GAAGI,SAAS,EAAE;MAChC,IAAIJ,MAAM,GAAG,CAAC,EAAE;QACd;QACA,IAAI,CAAC9E,SAAS,EAAE;OACjB,MAAM;QACL;QACA,IAAI,CAACI,UAAU,EAAE;;KAEpB,MAAM;MACL;MACA0B,UAAU,CAAC,MAAM,IAAI,CAACoC,eAAe,EAAE,EAAE,IAAI,CAAC;;IAGhD,IAAI,CAAC9C,UAAU,GAAG,KAAK;EACzB;EAEA;EACA9B,eAAeA,CAAC6F,GAAuB;IACrC,OAAO,IAAI,CAACxE,YAAY,CAACrB,eAAe,CAAC6F,GAAG,EAAE,MAAM,CAAC;EACvD;EAEAjG,YAAYA,CAACwF,KAAY;IACvB,IAAI,CAAC/D,YAAY,CAACyE,gBAAgB,CAACV,KAAK,EAAE,MAAM,CAAC;EACnD;EAIAW,YAAYA,CAAA;IACV,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQA,sBAAsBA,CAAA;IAC5B;IACA,MAAMC,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDF,YAAY,CAACG,SAAS,GAAG,sBAAsB;IAC/CH,YAAY,CAACI,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2DxB;IAED;IACA,MAAMC,MAAM,GAAGJ,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC9CG,MAAM,CAACC,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqLpB;IAEDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACH,MAAM,CAAC;IACjCJ,QAAQ,CAACQ,IAAI,CAACD,WAAW,CAACR,YAAY,CAAC;IAEvC;IACCrD,MAAc,CAAC+D,gBAAgB,GAAIC,KAAuB,IAAI;MAC7D,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK;MACzB,IAAIA,KAAK,IAAIA,KAAK,CAAC9G,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAM+G,IAAI,GAAGD,KAAK,CAAC,CAAC,CAAC;QACrBvC,OAAO,CAACyC,GAAG,CAAC,gBAAgB,EAAED,IAAI,CAACE,IAAI,EAAEF,IAAI,CAACG,IAAI,CAAC;QAEnD;QACA,IAAI,CAACC,eAAe,CAACJ,IAAI,CAAC;QAC1Bb,YAAY,CAACkB,MAAM,EAAE;;IAEzB,CAAC;EACH;EAEQD,eAAeA,CAACJ,IAAU;IAChC,MAAMM,YAAY,GAAGlB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDiB,YAAY,CAAChB,SAAS,GAAG,qBAAqB;IAE9C,MAAMiB,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACT,IAAI,CAAC;IACzC,MAAMU,OAAO,GAAGV,IAAI,CAACG,IAAI,CAACQ,UAAU,CAAC,QAAQ,CAAC;IAE9CL,YAAY,CAACf,SAAS,GAAG;;;;;;;;;;YAUjBmB,OAAO,GACP,eAAeH,OAAO,oCAAoC,GAC1D,aAAaA,OAAO,wBACtB;;;;;;;;;;;;;;KAcL;IAED;IACA,MAAMK,aAAa,GAAGxB,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACrDuB,aAAa,CAACnB,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8E3B;IAEDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACiB,aAAa,CAAC;IACxCxB,QAAQ,CAACQ,IAAI,CAACD,WAAW,CAACW,YAAY,CAAC;EACzC;EAEA3H,eAAeA,CAACkI,UAAsB;IACpC,IAAIA,UAAU,CAAC7H,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;MACjC;MACA,IAAI,CAAC6H,sBAAsB,CAACD,UAAU,CAAC;KACxC,MAAM;MACLrD,OAAO,CAACyC,GAAG,CAAC,2BAA2B,EAAEY,UAAU,CAAC1H,IAAI,CAACK,QAAQ,CAAC;;EAEtE;EAEQsH,sBAAsBA,CAACD,UAAsB;IACnD;IACA,MAAM1B,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDF,YAAY,CAACG,SAAS,GAAG,oBAAoB;IAC7CH,YAAY,CAACI,SAAS,GAAG;;;;;cAKfsB,UAAU,CAAC7H,OAAO,CAACgE,GAAG,CAAC,CAACC,CAAC,EAAEvF,KAAK,KAAK;yCACVA,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,iBAAiBA,KAAK;;;aAG7E,CAAC,CAACqJ,IAAI,CAAC,EAAE,CAAC;;;;;;0BAMGF,UAAU,CAAC1H,IAAI,CAACC,MAAM,IAAI,mCAAmC;0BAC7DyH,UAAU,CAAC1H,IAAI,CAACG,QAAQ;;yCAETuH,UAAU,CAAC1H,IAAI,CAACK,QAAQ;0CACvB,IAAI,CAACwH,UAAU,CAACH,UAAU,CAAC7H,OAAO,CAAC,CAAC,CAAC,CAACiI,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAmCrF;IAED;IACA,MAAMzB,MAAM,GAAGJ,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC9CG,MAAM,CAACC,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2LpB;IAEDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACH,MAAM,CAAC;IACjCJ,QAAQ,CAACQ,IAAI,CAACD,WAAW,CAACR,YAAY,CAAC;IAEvC;IACA,IAAI,CAAC+B,qBAAqB,CAACL,UAAU,EAAE1B,YAAY,CAAC;EACtD;EAEQ+B,qBAAqBA,CAACL,UAAsB,EAAEM,YAAyB;IAC7E,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,aAAkB;IAEtB,MAAMC,WAAW,GAAG;MAClBF,YAAY;MACZpI,OAAO,EAAE6H,UAAU,CAAC7H,OAAO;MAE3BuI,SAAS,EAAG7J,KAAa,IAAI;QAC3B,MAAM8J,KAAK,GAAGX,UAAU,CAAC7H,OAAO,CAACtB,KAAK,CAAC;QACvC,MAAM+J,cAAc,GAAGN,YAAY,CAACO,aAAa,CAAC,gBAAgB,CAAC;QAEnE,IAAID,cAAc,EAAE;UAClB,IAAID,KAAK,CAACG,KAAK,CAACxB,IAAI,KAAK,OAAO,EAAE;YAChCsB,cAAc,CAAClC,SAAS,GAAG;0BACbiC,KAAK,CAACG,KAAK,CAAC5C,GAAG,UAAUyC,KAAK,CAACI,OAAO,IAAI,EAAE;aACzD;WACF,MAAM;YACLH,cAAc,CAAClC,SAAS,GAAG;4BACXiC,KAAK,CAACG,KAAK,CAAC5C,GAAG;;aAE9B;;;QAIL;QACA,MAAM8C,YAAY,GAAGV,YAAY,CAACW,gBAAgB,CAAC,eAAe,CAAC;QACnED,YAAY,CAACE,OAAO,CAAC,CAACC,GAAG,EAAE9E,CAAC,KAAI;UAC9B8E,GAAG,CAACC,SAAS,CAAC5B,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC;UAC3C,IAAInD,CAAC,GAAGxF,KAAK,EAAE;YACbsK,GAAG,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;WAC/B,MAAM,IAAIhF,CAAC,KAAKxF,KAAK,EAAE;YACtBsK,GAAG,CAACC,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;;QAE/B,CAAC,CAAC;QAEF,IAAI,CAACC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;MAClC,CAAC;MAEDC,SAAS,EAAEA,CAAA,KAAK;QACd,IAAIhB,YAAY,GAAGP,UAAU,CAAC7H,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;UAChDmI,YAAY,EAAE;UACdE,WAAW,CAACC,SAAS,CAACH,YAAY,CAAC;SACpC,MAAM;UACLD,YAAY,CAACd,MAAM,EAAE;;MAEzB,CAAC;MAEDgC,aAAa,EAAEA,CAAA,KAAK;QAClB,IAAIjB,YAAY,GAAG,CAAC,EAAE;UACpBA,YAAY,EAAE;UACdE,WAAW,CAACC,SAAS,CAACH,YAAY,CAAC;;MAEvC,CAAC;MAEDkB,UAAU,EAAEA,CAAA,KAAK;QACf9E,OAAO,CAACyC,GAAG,CAAC,uBAAuB,CAAC;MACtC,CAAC;MAEDsC,YAAY,EAAEA,CAAA,KAAK;QACjB/E,OAAO,CAACyC,GAAG,CAAC,yBAAyB,CAAC;MACxC,CAAC;MAEDuC,UAAU,EAAEA,CAAA,KAAK;QACfhF,OAAO,CAACyC,GAAG,CAAC,aAAa,CAAC;MAC5B;KACD;IAED;IACCnE,MAAc,CAACwF,WAAW,GAAGA,WAAW;IAEzC;IACAA,WAAW,CAACC,SAAS,CAAC,CAAC,CAAC;IAExB;IACA,MAAMkB,QAAQ,GAAG,IAAIC,gBAAgB,CAAEC,SAAS,IAAI;MAClDA,SAAS,CAACZ,OAAO,CAAEa,QAAQ,IAAI;QAC7B,IAAIA,QAAQ,CAACzC,IAAI,KAAK,WAAW,EAAE;UACjCyC,QAAQ,CAACC,YAAY,CAACd,OAAO,CAAEe,IAAI,IAAI;YACrC,IAAIA,IAAI,KAAK3B,YAAY,EAAE;cACzB,OAAQrF,MAAc,CAACwF,WAAW;cAClC,IAAID,aAAa,EAAE0B,YAAY,CAAC1B,aAAa,CAAC;cAC9CoB,QAAQ,CAACO,UAAU,EAAE;;UAEzB,CAAC,CAAC;;MAEN,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFP,QAAQ,CAACQ,OAAO,CAAC7D,QAAQ,CAACQ,IAAI,EAAE;MAAEsD,SAAS,EAAE;IAAI,CAAE,CAAC;EACtD;EAEQf,kBAAkBA,CAACgB,QAAgB;IACzC;EAAA;EAGMnC,UAAUA,CAACoC,IAAU;IAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAGF,GAAG,CAACG,OAAO,EAAE,GAAG,IAAIF,IAAI,CAACF,IAAI,CAAC,CAACI,OAAO,EAAE;IACrD,MAAMC,KAAK,GAAGjH,IAAI,CAACC,KAAK,CAAC8G,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIE,KAAK,GAAG,CAAC,EAAE;MACb,MAAMC,OAAO,GAAGlH,IAAI,CAACC,KAAK,CAAC8G,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;MAC9C,OAAO,GAAGG,OAAO,GAAG;KACrB,MAAM,IAAID,KAAK,GAAG,EAAE,EAAE;MACrB,OAAO,GAAGA,KAAK,GAAG;KACnB,MAAM;MACL,MAAME,IAAI,GAAGnH,IAAI,CAACC,KAAK,CAACgH,KAAK,GAAG,EAAE,CAAC;MACnC,OAAO,GAAGE,IAAI,GAAG;;EAErB;EAEA;EACAC,YAAYA,CAAA;IACV,IAAI,CAAC/F,cAAc,EAAE;EACvB;EAEAgG,YAAYA,CAAA;IACV,IAAI,CAAC/F,eAAe,EAAE;EACxB;EAEQR,eAAeA,CAAA;IACrB;IACA,IAAI,CAAC9C,WAAW,GAAG,CACjB;MACErB,IAAI,EAAE;QACJ2K,GAAG,EAAE,OAAO;QACZtK,QAAQ,EAAE,kBAAkB;QAC5BF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE;OACT;MACDJ,OAAO,EAAE,CACP;QACE8K,GAAG,EAAE,QAAQ;QACbnC,KAAK,EAAE;UAAExB,IAAI,EAAE,OAAO;UAAEpB,GAAG,EAAE,mCAAmC;UAAEoE,QAAQ,EAAE;QAAK,CAAE;QACnFvB,OAAO,EAAE,2BAA2B;QACpCmC,QAAQ,EAAE,IAAI;QACd9C,SAAS,EAAE,IAAIqC,IAAI,EAAE;QACrBU,SAAS,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;OACrD;KAEJ,EACD;MACElK,IAAI,EAAE;QACJ2K,GAAG,EAAE,OAAO;QACZtK,QAAQ,EAAE,gBAAgB;QAC1BF,QAAQ,EAAE,WAAW;QACrBF,MAAM,EAAE;OACT;MACDJ,OAAO,EAAE,CACP;QACE8K,GAAG,EAAE,QAAQ;QACbnC,KAAK,EAAE;UAAExB,IAAI,EAAE,OAAO;UAAEpB,GAAG,EAAE,mCAAmC;UAAEoE,QAAQ,EAAE;QAAK,CAAE;QACnFvB,OAAO,EAAE,mCAAmC;QAC5CmC,QAAQ,EAAE,IAAI;QACd9C,SAAS,EAAE,IAAIqC,IAAI,EAAE;QACrBU,SAAS,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;OACrD;KAEJ,EACD;MACElK,IAAI,EAAE;QACJ2K,GAAG,EAAE,OAAO;QACZtK,QAAQ,EAAE,cAAc;QACxBF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE;OACT;MACDJ,OAAO,EAAE,CACP;QACE8K,GAAG,EAAE,QAAQ;QACbnC,KAAK,EAAE;UAAExB,IAAI,EAAE,OAAO;UAAEpB,GAAG,EAAE,mCAAmC;UAAEoE,QAAQ,EAAE;QAAK,CAAE;QACnFvB,OAAO,EAAE,kBAAkB;QAC3BmC,QAAQ,EAAE,IAAI;QACd9C,SAAS,EAAE,IAAIqC,IAAI,EAAE;QACrBU,SAAS,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;OACrD;KAEJ,EACD;MACElK,IAAI,EAAE;QACJ2K,GAAG,EAAE,OAAO;QACZtK,QAAQ,EAAE,iBAAiB;QAC3BF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE;OACT;MACDJ,OAAO,EAAE,CACP;QACE8K,GAAG,EAAE,QAAQ;QACbnC,KAAK,EAAE;UAAExB,IAAI,EAAE,OAAO;UAAEpB,GAAG,EAAE,mCAAmC;UAAEoE,QAAQ,EAAE;QAAK,CAAE;QACnFvB,OAAO,EAAE,wBAAwB;QACjCmC,QAAQ,EAAE,IAAI;QACd9C,SAAS,EAAE,IAAIqC,IAAI,EAAE;QACrBU,SAAS,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;OACrD;KAEJ,EACD;MACElK,IAAI,EAAE;QACJ2K,GAAG,EAAE,OAAO;QACZtK,QAAQ,EAAE,YAAY;QACtBF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE;OACT;MACDJ,OAAO,EAAE,CACP;QACE8K,GAAG,EAAE,QAAQ;QACbnC,KAAK,EAAE;UAAExB,IAAI,EAAE,OAAO;UAAEpB,GAAG,EAAE,mCAAmC;UAAEoE,QAAQ,EAAE;QAAK,CAAE;QACnFvB,OAAO,EAAE,4BAA4B;QACrCmC,QAAQ,EAAE,IAAI;QACd9C,SAAS,EAAE,IAAIqC,IAAI,EAAE;QACrBU,SAAS,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;OACrD;KAEJ,EACD;MACElK,IAAI,EAAE;QACJ2K,GAAG,EAAE,OAAO;QACZtK,QAAQ,EAAE,iBAAiB;QAC3BF,QAAQ,EAAE,cAAc;QACxBF,MAAM,EAAE;OACT;MACDJ,OAAO,EAAE,CACP;QACE8K,GAAG,EAAE,QAAQ;QACbnC,KAAK,EAAE;UAAExB,IAAI,EAAE,OAAO;UAAEpB,GAAG,EAAE,mCAAmC;UAAEoE,QAAQ,EAAE;QAAK,CAAE;QACnFvB,OAAO,EAAE,4BAA4B;QACrCmC,QAAQ,EAAE,IAAI;QACd9C,SAAS,EAAE,IAAIqC,IAAI,EAAE;QACrBU,SAAS,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;OACrD;KAEJ,CACc;IAEjB;IACA3H,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,2BAA2B,EAAE;MAClC,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAACC,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;;;uBA/pCW3B,gBAAgB,EAAA/C,EAAA,CAAA8M,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAhN,EAAA,CAAA8M,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAlN,EAAA,CAAA8M,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAApN,EAAA,CAAA8M,iBAAA,CAAAO,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAhBvK,gBAAgB;MAAAwK,SAAA;MAAAC,SAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UClB3B1N,EADF,CAAAC,cAAA,aAA6B,aACS;UAElCD,EAAA,CAAAe,UAAA,IAAA6M,+BAAA,iBAAuC;UAUvC5N,EAAA,CAAAC,cAAA,gBAOmC;UAA9BD,EALA,CAAAE,UAAA,wBAAA2N,oDAAAnM,MAAA;YAAA1B,EAAA,CAAAK,aAAA,CAAAyN,GAAA;YAAA,OAAA9N,EAAA,CAAAU,WAAA,CAAciN,GAAA,CAAAzG,YAAA,CAAAxF,MAAA,CAAoB;UAAA,EAAC,uBAAAqM,mDAAArM,MAAA;YAAA1B,EAAA,CAAAK,aAAA,CAAAyN,GAAA;YAAA,OAAA9N,EAAA,CAAAU,WAAA,CACtBiN,GAAA,CAAArG,WAAA,CAAA5F,MAAA,CAAmB;UAAA,EAAC,sBAAAsM,kDAAAtM,MAAA;YAAA1B,EAAA,CAAAK,aAAA,CAAAyN,GAAA;YAAA,OAAA9N,EAAA,CAAAU,WAAA,CACrBiN,GAAA,CAAAjG,UAAA,CAAAhG,MAAA,CAAkB;UAAA,EAAC,oBAAAuM,gDAAA;YAAAjO,EAAA,CAAAK,aAAA,CAAAyN,GAAA;YAAA,OAAA9N,EAAA,CAAAU,WAAA,CACrBiN,GAAA,CAAA1G,QAAA,EAAU;UAAA,EAAC,wBAAAiH,oDAAA;YAAAlO,EAAA,CAAAK,aAAA,CAAAyN,GAAA;YAAA,OAAA9N,EAAA,CAAAU,WAAA,CACPiN,GAAA,CAAAlB,YAAA,EAAc;UAAA,EAAC,wBAAA0B,oDAAA;YAAAnO,EAAA,CAAAK,aAAA,CAAAyN,GAAA;YAAA,OAAA9N,EAAA,CAAAU,WAAA,CACfiN,GAAA,CAAAjB,YAAA,EAAc;UAAA,EAAC;UAQ9B1M,EALF,CAAAC,cAAA,aAE+E,aAGjB;UAAzBD,EAAA,CAAAE,UAAA,mBAAAkO,+CAAA;YAAApO,EAAA,CAAAK,aAAA,CAAAyN,GAAA;YAAA,OAAA9N,EAAA,CAAAU,WAAA,CAASiN,GAAA,CAAA7F,YAAA,EAAc;UAAA,EAAC;UAEvD9H,EADF,CAAAC,cAAA,aAA0B,aACU;UAChCD,EAAA,CAAAsC,SAAA,WAA2B;UAE/BtC,EADE,CAAAY,YAAA,EAAM,EACF;UACNZ,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAA4B,MAAA,kBAAU;UACzC5B,EADyC,CAAAY,YAAA,EAAO,EAC1C;UAGNZ,EAAA,CAAAe,UAAA,KAAAsN,gCAAA,kBAIC;UAYLrO,EADE,CAAAY,YAAA,EAAM,EACF;UAgBNZ,EAbA,CAAAe,UAAA,KAAAuN,gCAAA,kBAA8F,KAAAC,mCAAA,qBASnE,KAAAC,mCAAA,qBAOA;UAI/BxO,EADE,CAAAY,YAAA,EAAM,EACF;;;UA1EqBZ,EAAA,CAAAiB,SAAA,GAAc;UAAdjB,EAAA,CAAAkB,UAAA,SAAAyM,GAAA,CAAAjK,QAAA,CAAc;UAqB9B1D,EAAA,CAAAiB,SAAA,GAAsD;UACtDjB,EADA,CAAAyO,WAAA,8BAAAd,GAAA,CAAApK,UAAA,SAAsD,eAAAoK,GAAA,CAAAnK,eAAA,sCACmB;UAcnDxD,EAAA,CAAAiB,SAAA,GAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,YAAAyM,GAAA,CAAAtK,WAAA,CAAgB;UAkBVrD,EAAA,CAAAiB,SAAA,EAAyD;UAAzDjB,EAAA,CAAAkB,UAAA,SAAAyM,GAAA,CAAA1J,aAAA,IAAA0J,GAAA,CAAAtK,WAAA,CAAAvB,MAAA,GAAA6L,GAAA,CAAA7J,aAAA,CAAyD;UASnF9D,EAAA,CAAAiB,SAAA,EAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,SAAAyM,GAAA,CAAAlK,UAAA,CAAgB;UAOhBzD,EAAA,CAAAiB,SAAA,EAAgB;UAAhBjB,EAAA,CAAAkB,UAAA,SAAAyM,GAAA,CAAAlK,UAAA,CAAgB;;;qBD5DjB1D,YAAY,EAAA2O,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAAvG,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}