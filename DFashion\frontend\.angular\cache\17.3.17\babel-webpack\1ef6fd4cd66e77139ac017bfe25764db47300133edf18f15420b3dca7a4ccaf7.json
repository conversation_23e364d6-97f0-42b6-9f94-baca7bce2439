{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win, d as doc } from './index-a5d50daf.js';\nimport { K as Keyboard, a as KeyboardResize } from './keyboard-73175e24.js';\n\n/**\n * The element that resizes when the keyboard opens\n * is going to depend on the resize mode\n * which is why we check that here.\n */\nconst getResizeContainer = resizeMode => {\n  /**\n   * If doc is undefined then we are\n   * in an SSR environment, so the keyboard\n   * adjustment does not apply.\n   * If the webview does not resize then there\n   * is no container to resize.\n   */\n  if (doc === undefined || resizeMode === KeyboardResize.None || resizeMode === undefined) {\n    return null;\n  }\n  /**\n   * The three remaining resize modes: Native, Ionic, and Body\n   * all cause `ion-app` to resize, so we can listen for changes\n   * on that. In the event `ion-app` is not available then\n   * we can fall back to `body`.\n   */\n  const ionApp = doc.querySelector('ion-app');\n  return ionApp !== null && ionApp !== void 0 ? ionApp : doc.body;\n};\n/**\n * Get the height of ion-app or body.\n * This is used for determining if the webview\n * has resized before the keyboard closed.\n * */\nconst getResizeContainerHeight = resizeMode => {\n  const containerElement = getResizeContainer(resizeMode);\n  return containerElement === null ? 0 : containerElement.clientHeight;\n};\n/**\n * Creates a controller that tracks and reacts to opening or closing the keyboard.\n *\n * @internal\n * @param keyboardChangeCallback A function to call when the keyboard opens or closes.\n */\nconst createKeyboardController = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (keyboardChangeCallback) {\n    let keyboardWillShowHandler;\n    let keyboardWillHideHandler;\n    let keyboardVisible;\n    /**\n     * This lets us determine if the webview content\n     * has resized as a result of the keyboard.\n     */\n    let initialResizeContainerHeight;\n    const init = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(function* () {\n        const resizeOptions = yield Keyboard.getResizeMode();\n        const resizeMode = resizeOptions === undefined ? undefined : resizeOptions.mode;\n        keyboardWillShowHandler = () => {\n          /**\n           * We need to compute initialResizeContainerHeight right before\n           * the keyboard opens to guarantee the resize container is visible.\n           * The resize container may not be visible if we compute this\n           * as soon as the keyboard controller is created.\n           * We should only need to do this once to avoid additional clientHeight\n           * computations.\n           */\n          if (initialResizeContainerHeight === undefined) {\n            initialResizeContainerHeight = getResizeContainerHeight(resizeMode);\n          }\n          keyboardVisible = true;\n          fireChangeCallback(keyboardVisible, resizeMode);\n        };\n        keyboardWillHideHandler = () => {\n          keyboardVisible = false;\n          fireChangeCallback(keyboardVisible, resizeMode);\n        };\n        win === null || win === void 0 ? void 0 : win.addEventListener('keyboardWillShow', keyboardWillShowHandler);\n        win === null || win === void 0 ? void 0 : win.addEventListener('keyboardWillHide', keyboardWillHideHandler);\n      });\n      return function init() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    const fireChangeCallback = (state, resizeMode) => {\n      if (keyboardChangeCallback) {\n        keyboardChangeCallback(state, createResizePromiseIfNeeded(resizeMode));\n      }\n    };\n    /**\n     * Code responding to keyboard lifecycles may need\n     * to show/hide content once the webview has\n     * resized as a result of the keyboard showing/hiding.\n     * createResizePromiseIfNeeded provides a way for code to wait for the\n     * resize event that was triggered as a result of the keyboard.\n     */\n    const createResizePromiseIfNeeded = resizeMode => {\n      if (\n      /**\n       * If we are in an SSR environment then there is\n       * no window to resize. Additionally, if there\n       * is no resize mode or the resize mode is \"None\"\n       * then initialResizeContainerHeight will be 0\n       */\n      initialResizeContainerHeight === 0 ||\n      /**\n       * If the keyboard is closed before the webview resizes initially\n       * then the webview will never resize.\n       */\n      initialResizeContainerHeight === getResizeContainerHeight(resizeMode)) {\n        return;\n      }\n      /**\n       * Get the resize container so we can\n       * attach the ResizeObserver below to\n       * the correct element.\n       */\n      const containerElement = getResizeContainer(resizeMode);\n      if (containerElement === null) {\n        return;\n      }\n      /**\n       * Some part of the web content should resize,\n       * and we need to listen for a resize.\n       */\n      return new Promise(resolve => {\n        const callback = () => {\n          /**\n           * As per the spec, the ResizeObserver\n           * will fire when observation starts if\n           * the observed element is rendered and does not\n           * have a size of 0 x 0. However, the watched element\n           * may or may not have resized by the time this first\n           * callback is fired. As a result, we need to check\n           * the dimensions of the element.\n           *\n           * https://www.w3.org/TR/resize-observer/#intro\n           */\n          if (containerElement.clientHeight === initialResizeContainerHeight) {\n            /**\n             * The resize happened, so stop listening\n             * for resize on this element.\n             */\n            ro.disconnect();\n            resolve();\n          }\n        };\n        /**\n         * In Capacitor there can be delay between when the window\n         * resizes and when the container element resizes, so we cannot\n         * rely on a 'resize' event listener on the window.\n         * Instead, we need to determine when the container\n         * element resizes using a ResizeObserver.\n         */\n        const ro = new ResizeObserver(callback);\n        ro.observe(containerElement);\n      });\n    };\n    const destroy = () => {\n      win === null || win === void 0 ? void 0 : win.removeEventListener('keyboardWillShow', keyboardWillShowHandler);\n      win === null || win === void 0 ? void 0 : win.removeEventListener('keyboardWillHide', keyboardWillHideHandler);\n      keyboardWillShowHandler = keyboardWillHideHandler = undefined;\n    };\n    const isKeyboardVisible = () => keyboardVisible;\n    yield init();\n    return {\n      init,\n      destroy,\n      isKeyboardVisible\n    };\n  });\n  return function createKeyboardController(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { createKeyboardController as c };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}