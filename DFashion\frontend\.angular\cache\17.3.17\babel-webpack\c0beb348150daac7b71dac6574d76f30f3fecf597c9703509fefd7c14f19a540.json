{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { StoriesComponent } from '../../components/stories/stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction HomeComponent_div_8_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r2.productCount, \" items \");\n  }\n}\nfunction HomeComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_8_Template_div_click_0_listener() {\n      const category_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToCategory(category_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 8);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 9);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HomeComponent_div_8_span_5_Template, 2, 1, \"span\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r2.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", category_r2.productCount);\n  }\n}\nexport class HomeComponent {\n  constructor(productService, router) {\n    this.productService = productService;\n    this.router = router;\n    this.categories = [];\n  }\n  ngOnInit() {\n    this.loadCategories();\n  }\n  loadCategories() {\n    this.productService.getCategories().subscribe({\n      next: response => {\n        if (response.success) {\n          // Show only main categories (first 8)\n          this.categories = response.data.slice(0, 8);\n        }\n      },\n      error: error => {\n        console.error('Error loading categories:', error);\n        // Fallback categories\n        this.categories = [{\n          name: 'Women',\n          slug: 'women',\n          icon: '👩',\n          productCount: 150\n        }, {\n          name: 'Men',\n          slug: 'men',\n          icon: '👨',\n          productCount: 120\n        }, {\n          name: 'Kids',\n          slug: 'children',\n          icon: '👶',\n          productCount: 80\n        }, {\n          name: 'Ethnic',\n          slug: 'ethnic-wear',\n          icon: '🥻',\n          productCount: 90\n        }, {\n          name: 'Footwear',\n          slug: 'footwear',\n          icon: '👟',\n          productCount: 75\n        }, {\n          name: 'Accessories',\n          slug: 'accessories',\n          icon: '💎',\n          productCount: 60\n        }, {\n          name: 'Sports',\n          slug: 'sportswear',\n          icon: '🏃',\n          productCount: 45\n        }, {\n          name: 'Winter',\n          slug: 'winter-wear',\n          icon: '🧥',\n          productCount: 35\n        }];\n      }\n    });\n  }\n  navigateToCategory(category) {\n    this.router.navigate(['/category', category.slug]);\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 11,\n      vars: 1,\n      consts: [[1, \"home-container\"], [1, \"content-grid\"], [1, \"main-content\"], [1, \"shop-by-category-section\"], [1, \"section-title\"], [1, \"categories-grid\"], [\"class\", \"category-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"category-card\", 3, \"click\"], [1, \"category-icon\"], [1, \"category-name\"], [\"class\", \"category-count\", 4, \"ngIf\"], [1, \"category-count\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"app-stories\");\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"h2\", 4);\n          i0.ɵɵtext(6, \"Shop by Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 5);\n          i0.ɵɵtemplate(8, HomeComponent_div_8_Template, 6, 3, \"div\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(9, \"app-feed\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(10, \"app-sidebar\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, StoriesComponent, FeedComponent, SidebarComponent],\n      styles: [\".home-container[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n  min-height: calc(100vh - 60px);\\n}\\n\\n.content-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 400px;\\n  gap: 40px;\\n  max-width: 1000px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.shop-by-category-section[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 24px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #333;\\n  margin: 0 0 20px 0;\\n  text-align: center;\\n}\\n\\n.categories-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));\\n  gap: 16px;\\n}\\n\\n.category-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 20px 16px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 12px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border: 2px solid transparent;\\n  text-align: center;\\n}\\n.category-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n  border-color: #007bff;\\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\\n}\\n.category-card[_ngcontent-%COMP%]:active {\\n  transform: translateY(-2px);\\n}\\n\\n.category-icon[_ngcontent-%COMP%] {\\n  font-size: 36px;\\n  margin-bottom: 12px;\\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));\\n}\\n\\n.category-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0 0 6px 0;\\n  line-height: 1.2;\\n}\\n\\n.category-count[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n\\n@media (max-width: 1024px) {\\n  .content-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    max-width: 600px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .home-container[_ngcontent-%COMP%] {\\n    padding: 16px 0;\\n  }\\n  .content-grid[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n    gap: 20px;\\n  }\\n  .main-content[_ngcontent-%COMP%] {\\n    gap: 20px;\\n  }\\n  .shop-by-category-section[_ngcontent-%COMP%] {\\n    padding: 20px 16px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n    margin-bottom: 16px;\\n  }\\n  .categories-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\\n    gap: 12px;\\n  }\\n  .category-card[_ngcontent-%COMP%] {\\n    padding: 16px 12px;\\n  }\\n  .category-icon[_ngcontent-%COMP%] {\\n    font-size: 28px;\\n    margin-bottom: 8px;\\n  }\\n  .category-name[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .category-count[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .categories-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 10px;\\n  }\\n  .category-card[_ngcontent-%COMP%] {\\n    padding: 14px 10px;\\n  }\\n  .category-icon[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n    margin-bottom: 6px;\\n  }\\n  .category-name[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n    margin-bottom: 4px;\\n  }\\n  .category-count[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaG9tZS9wYWdlcy9ob21lL2hvbWUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxlQUFBO0VBQ0EsOEJBQUE7QUFDRjs7QUFFQTtFQUNFLGFBQUE7RUFDQSxnQ0FBQTtFQUNBLFNBQUE7RUFDQSxpQkFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0FBQ0Y7O0FBRUE7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0FBQ0Y7O0FBR0E7RUFDRSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLHlDQUFBO0VBQ0EseUJBQUE7QUFBRjs7QUFHQTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7RUFDQSxrQkFBQTtFQUNBLGtCQUFBO0FBQUY7O0FBR0E7RUFDRSxhQUFBO0VBQ0EsMkRBQUE7RUFDQSxTQUFBO0FBQUY7O0FBR0E7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsNkRBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLDZCQUFBO0VBQ0Esa0JBQUE7QUFBRjtBQUVFO0VBQ0UsMkJBQUE7RUFDQSwwQ0FBQTtFQUNBLHFCQUFBO0VBQ0EsNkRBQUE7QUFBSjtBQUdFO0VBQ0UsMkJBQUE7QUFESjs7QUFLQTtFQUNFLGVBQUE7RUFDQSxtQkFBQTtFQUNBLGlEQUFBO0FBRkY7O0FBS0E7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxXQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtBQUZGOztBQUtBO0VBQ0UsZUFBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtBQUZGOztBQUtBO0VBQ0U7SUFDRSwwQkFBQTtJQUNBLGdCQUFBO0VBRkY7QUFDRjtBQUtBO0VBQ0U7SUFDRSxlQUFBO0VBSEY7RUFNQTtJQUNFLGVBQUE7SUFDQSxTQUFBO0VBSkY7RUFPQTtJQUNFLFNBQUE7RUFMRjtFQVFBO0lBQ0Usa0JBQUE7RUFORjtFQVNBO0lBQ0UsZUFBQTtJQUNBLG1CQUFBO0VBUEY7RUFVQTtJQUNFLDJEQUFBO0lBQ0EsU0FBQTtFQVJGO0VBV0E7SUFDRSxrQkFBQTtFQVRGO0VBWUE7SUFDRSxlQUFBO0lBQ0Esa0JBQUE7RUFWRjtFQWFBO0lBQ0UsZUFBQTtFQVhGO0VBY0E7SUFDRSxlQUFBO0VBWkY7QUFDRjtBQWVBO0VBQ0U7SUFDRSxxQ0FBQTtJQUNBLFNBQUE7RUFiRjtFQWdCQTtJQUNFLGtCQUFBO0VBZEY7RUFpQkE7SUFDRSxlQUFBO0lBQ0Esa0JBQUE7RUFmRjtFQWtCQTtJQUNFLGVBQUE7SUFDQSxrQkFBQTtFQWhCRjtFQW1CQTtJQUNFLGVBQUE7RUFqQkY7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5ob21lLWNvbnRhaW5lciB7XG4gIHBhZGRpbmc6IDIwcHggMDtcbiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDYwcHgpO1xufVxuXG4uY29udGVudC1ncmlkIHtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnIgNDAwcHg7XG4gIGdhcDogNDBweDtcbiAgbWF4LXdpZHRoOiAxMDAwcHg7XG4gIG1hcmdpbjogMCBhdXRvO1xuICBwYWRkaW5nOiAwIDIwcHg7XG59XG5cbi5tYWluLWNvbnRlbnQge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBnYXA6IDI0cHg7XG59XG5cbi8vIFNob3AgYnkgQ2F0ZWdvcnkgU2VjdGlvblxuLnNob3AtYnktY2F0ZWdvcnktc2VjdGlvbiB7XG4gIGJhY2tncm91bmQ6IHdoaXRlO1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBwYWRkaW5nOiAyNHB4O1xuICBib3gtc2hhZG93OiAwIDJweCAxMHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgYm9yZGVyOiAxcHggc29saWQgI2UwZTBlMDtcbn1cblxuLnNlY3Rpb24tdGl0bGUge1xuICBmb250LXNpemU6IDI0cHg7XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIGNvbG9yOiAjMzMzO1xuICBtYXJnaW46IDAgMCAyMHB4IDA7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbn1cblxuLmNhdGVnb3JpZXMtZ3JpZCB7XG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMTQwcHgsIDFmcikpO1xuICBnYXA6IDE2cHg7XG59XG5cbi5jYXRlZ29yeS1jYXJkIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgcGFkZGluZzogMjBweCAxNnB4O1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjhmOWZhIDAlLCAjZTllY2VmIDEwMCUpO1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gIGJvcmRlcjogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG5cbiAgJjpob3ZlciB7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC00cHgpO1xuICAgIGJveC1zaGFkb3c6IDAgOHB4IDI1cHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcbiAgICBib3JkZXItY29sb3I6ICMwMDdiZmY7XG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmZmZmZiAwJSwgI2Y4ZjlmYSAxMDAlKTtcbiAgfVxuXG4gICY6YWN0aXZlIHtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XG4gIH1cbn1cblxuLmNhdGVnb3J5LWljb24ge1xuICBmb250LXNpemU6IDM2cHg7XG4gIG1hcmdpbi1ib3R0b206IDEycHg7XG4gIGZpbHRlcjogZHJvcC1zaGFkb3coMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKSk7XG59XG5cbi5jYXRlZ29yeS1uYW1lIHtcbiAgZm9udC1zaXplOiAxNnB4O1xuICBmb250LXdlaWdodDogNjAwO1xuICBjb2xvcjogIzMzMztcbiAgbWFyZ2luOiAwIDAgNnB4IDA7XG4gIGxpbmUtaGVpZ2h0OiAxLjI7XG59XG5cbi5jYXRlZ29yeS1jb3VudCB7XG4gIGZvbnQtc2l6ZTogMTJweDtcbiAgY29sb3I6ICM2NjY7XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG59XG5cbkBtZWRpYSAobWF4LXdpZHRoOiAxMDI0cHgpIHtcbiAgLmNvbnRlbnQtZ3JpZCB7XG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gICAgbWF4LXdpZHRoOiA2MDBweDtcbiAgfVxufVxuXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLmhvbWUtY29udGFpbmVyIHtcbiAgICBwYWRkaW5nOiAxNnB4IDA7XG4gIH1cblxuICAuY29udGVudC1ncmlkIHtcbiAgICBwYWRkaW5nOiAwIDE2cHg7XG4gICAgZ2FwOiAyMHB4O1xuICB9XG5cbiAgLm1haW4tY29udGVudCB7XG4gICAgZ2FwOiAyMHB4O1xuICB9XG5cbiAgLnNob3AtYnktY2F0ZWdvcnktc2VjdGlvbiB7XG4gICAgcGFkZGluZzogMjBweCAxNnB4O1xuICB9XG5cbiAgLnNlY3Rpb24tdGl0bGUge1xuICAgIGZvbnQtc2l6ZTogMjBweDtcbiAgICBtYXJnaW4tYm90dG9tOiAxNnB4O1xuICB9XG5cbiAgLmNhdGVnb3JpZXMtZ3JpZCB7XG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgxMjBweCwgMWZyKSk7XG4gICAgZ2FwOiAxMnB4O1xuICB9XG5cbiAgLmNhdGVnb3J5LWNhcmQge1xuICAgIHBhZGRpbmc6IDE2cHggMTJweDtcbiAgfVxuXG4gIC5jYXRlZ29yeS1pY29uIHtcbiAgICBmb250LXNpemU6IDI4cHg7XG4gICAgbWFyZ2luLWJvdHRvbTogOHB4O1xuICB9XG5cbiAgLmNhdGVnb3J5LW5hbWUge1xuICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgfVxuXG4gIC5jYXRlZ29yeS1jb3VudCB7XG4gICAgZm9udC1zaXplOiAxMXB4O1xuICB9XG59XG5cbkBtZWRpYSAobWF4LXdpZHRoOiA0ODBweCkge1xuICAuY2F0ZWdvcmllcy1ncmlkIHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCAxZnIpO1xuICAgIGdhcDogMTBweDtcbiAgfVxuXG4gIC5jYXRlZ29yeS1jYXJkIHtcbiAgICBwYWRkaW5nOiAxNHB4IDEwcHg7XG4gIH1cblxuICAuY2F0ZWdvcnktaWNvbiB7XG4gICAgZm9udC1zaXplOiAyNHB4O1xuICAgIG1hcmdpbi1ib3R0b206IDZweDtcbiAgfVxuXG4gIC5jYXRlZ29yeS1uYW1lIHtcbiAgICBmb250LXNpemU6IDEzcHg7XG4gICAgbWFyZ2luLWJvdHRvbTogNHB4O1xuICB9XG5cbiAgLmNhdGVnb3J5LWNvdW50IHtcbiAgICBmb250LXNpemU6IDEwcHg7XG4gIH1cbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "StoriesComponent", "FeedComponent", "SidebarComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "category_r2", "productCount", "ɵɵlistener", "HomeComponent_div_8_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "navigateToCategory", "ɵɵtemplate", "HomeComponent_div_8_span_5_Template", "ɵɵtextInterpolate", "icon", "name", "ɵɵproperty", "HomeComponent", "constructor", "productService", "router", "categories", "ngOnInit", "loadCategories", "getCategories", "subscribe", "next", "response", "success", "data", "slice", "error", "console", "slug", "category", "navigate", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelement", "HomeComponent_div_8_Template", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\n\nimport { StoriesComponent } from '../../components/stories/stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport { ProductService } from '../../../../core/services/product.service';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, StoriesComponent, FeedComponent, SidebarComponent],\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit {\n  categories: any[] = [];\n\n  constructor(\n    private productService: ProductService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadCategories();\n  }\n\n  loadCategories() {\n    this.productService.getCategories().subscribe({\n      next: (response) => {\n        if (response.success) {\n          // Show only main categories (first 8)\n          this.categories = response.data.slice(0, 8);\n        }\n      },\n      error: (error) => {\n        console.error('Error loading categories:', error);\n        // Fallback categories\n        this.categories = [\n          { name: 'Women', slug: 'women', icon: '👩', productCount: 150 },\n          { name: 'Men', slug: 'men', icon: '👨', productCount: 120 },\n          { name: 'Kids', slug: 'children', icon: '👶', productCount: 80 },\n          { name: 'Ethnic', slug: 'ethnic-wear', icon: '🥻', productCount: 90 },\n          { name: 'Footwear', slug: 'footwear', icon: '👟', productCount: 75 },\n          { name: 'Accessories', slug: 'accessories', icon: '💎', productCount: 60 },\n          { name: 'Sports', slug: 'sportswear', icon: '🏃', productCount: 45 },\n          { name: 'Winter', slug: 'winter-wear', icon: '🧥', productCount: 35 }\n        ];\n      }\n    });\n  }\n\n  navigateToCategory(category: any) {\n    this.router.navigate(['/category', category.slug]);\n  }\n}\n", "<div class=\"home-container\">\n  <div class=\"content-grid\">\n    <!-- Main Feed -->\n    <div class=\"main-content\">\n      <app-stories></app-stories>\n\n      <!-- Shop by Category Section -->\n      <div class=\"shop-by-category-section\">\n        <h2 class=\"section-title\">Shop by Category</h2>\n        <div class=\"categories-grid\">\n          <div\n            *ngFor=\"let category of categories\"\n            class=\"category-card\"\n            (click)=\"navigateToCategory(category)\">\n            <div class=\"category-icon\">{{ category.icon }}</div>\n            <h3 class=\"category-name\">{{ category.name }}</h3>\n            <span class=\"category-count\" *ngIf=\"category.productCount\">\n              {{ category.productCount }} items\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <app-feed></app-feed>\n    </div>\n\n    <!-- Sidebar -->\n    <app-sidebar></app-sidebar>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAG9C,SAASC,gBAAgB,QAAQ,4CAA4C;AAC7E,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,gBAAgB,QAAQ,4CAA4C;;;;;;;ICUjEC,EAAA,CAAAC,cAAA,eAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,WAAA,CAAAC,YAAA,YACF;;;;;;IARFP,EAAA,CAAAC,cAAA,aAGyC;IAAvCD,EAAA,CAAAQ,UAAA,mBAAAC,kDAAA;MAAA,MAAAH,WAAA,GAAAN,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAG,kBAAA,CAAAV,WAAA,CAA4B;IAAA,EAAC;IACtCN,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,YAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAiB,UAAA,IAAAC,mCAAA,mBAA2D;IAG7DlB,EAAA,CAAAG,YAAA,EAAM;;;;IALuBH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAmB,iBAAA,CAAAb,WAAA,CAAAc,IAAA,CAAmB;IACpBpB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAmB,iBAAA,CAAAb,WAAA,CAAAe,IAAA,CAAmB;IACfrB,EAAA,CAAAI,SAAA,EAA2B;IAA3BJ,EAAA,CAAAsB,UAAA,SAAAhB,WAAA,CAAAC,YAAA,CAA2B;;;ADArE,OAAM,MAAOgB,aAAa;EAGxBC,YACUC,cAA8B,EAC9BC,MAAc;IADd,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAJhB,KAAAC,UAAU,GAAU,EAAE;EAKnB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAA,cAAcA,CAAA;IACZ,IAAI,CAACJ,cAAc,CAACK,aAAa,EAAE,CAACC,SAAS,CAAC;MAC5CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB;UACA,IAAI,CAACP,UAAU,GAAGM,QAAQ,CAACE,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;MAE/C,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD;QACA,IAAI,CAACV,UAAU,GAAG,CAChB;UAAEN,IAAI,EAAE,OAAO;UAAEkB,IAAI,EAAE,OAAO;UAAEnB,IAAI,EAAE,IAAI;UAAEb,YAAY,EAAE;QAAG,CAAE,EAC/D;UAAEc,IAAI,EAAE,KAAK;UAAEkB,IAAI,EAAE,KAAK;UAAEnB,IAAI,EAAE,IAAI;UAAEb,YAAY,EAAE;QAAG,CAAE,EAC3D;UAAEc,IAAI,EAAE,MAAM;UAAEkB,IAAI,EAAE,UAAU;UAAEnB,IAAI,EAAE,IAAI;UAAEb,YAAY,EAAE;QAAE,CAAE,EAChE;UAAEc,IAAI,EAAE,QAAQ;UAAEkB,IAAI,EAAE,aAAa;UAAEnB,IAAI,EAAE,IAAI;UAAEb,YAAY,EAAE;QAAE,CAAE,EACrE;UAAEc,IAAI,EAAE,UAAU;UAAEkB,IAAI,EAAE,UAAU;UAAEnB,IAAI,EAAE,IAAI;UAAEb,YAAY,EAAE;QAAE,CAAE,EACpE;UAAEc,IAAI,EAAE,aAAa;UAAEkB,IAAI,EAAE,aAAa;UAAEnB,IAAI,EAAE,IAAI;UAAEb,YAAY,EAAE;QAAE,CAAE,EAC1E;UAAEc,IAAI,EAAE,QAAQ;UAAEkB,IAAI,EAAE,YAAY;UAAEnB,IAAI,EAAE,IAAI;UAAEb,YAAY,EAAE;QAAE,CAAE,EACpE;UAAEc,IAAI,EAAE,QAAQ;UAAEkB,IAAI,EAAE,aAAa;UAAEnB,IAAI,EAAE,IAAI;UAAEb,YAAY,EAAE;QAAE,CAAE,CACtE;MACH;KACD,CAAC;EACJ;EAEAS,kBAAkBA,CAACwB,QAAa;IAC9B,IAAI,CAACd,MAAM,CAACe,QAAQ,CAAC,CAAC,WAAW,EAAED,QAAQ,CAACD,IAAI,CAAC,CAAC;EACpD;;;uBAvCWhB,aAAa,EAAAvB,EAAA,CAAA0C,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5C,EAAA,CAAA0C,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAbvB,aAAa;MAAAwB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAjD,EAAA,CAAAkD,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbtBxD,EAHJ,CAAAC,cAAA,aAA4B,aACA,aAEE;UACxBD,EAAA,CAAA0D,SAAA,kBAA2B;UAIzB1D,EADF,CAAAC,cAAA,aAAsC,YACV;UAAAD,EAAA,CAAAE,MAAA,uBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/CH,EAAA,CAAAC,cAAA,aAA6B;UAC3BD,EAAA,CAAAiB,UAAA,IAAA0C,4BAAA,iBAGyC;UAQ7C3D,EADE,CAAAG,YAAA,EAAM,EACF;UAENH,EAAA,CAAA0D,SAAA,eAAqB;UACvB1D,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAA0D,SAAA,mBAA2B;UAE/B1D,EADE,CAAAG,YAAA,EAAM,EACF;;;UAlB2BH,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAsB,UAAA,YAAAmC,GAAA,CAAA9B,UAAA,CAAa;;;qBDClC/B,YAAY,EAAAgE,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEjE,gBAAgB,EAAEC,aAAa,EAAEC,gBAAgB;MAAAgE,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}