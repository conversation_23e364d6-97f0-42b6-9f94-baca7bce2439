import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, fromEvent } from 'rxjs';
import { map, startWith, distinctUntilChanged, debounceTime } from 'rxjs/operators';

export interface BreakpointState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLargeDesktop: boolean;
  screenWidth: number;
  screenHeight: number;
}

@Injectable({
  providedIn: 'root'
})
export class ResponsiveService {
  private breakpointSubject = new BehaviorSubject<BreakpointState>(this.getBreakpointState());
  
  // Breakpoint definitions (mobile-first)
  private readonly breakpoints = {
    mobile: 768,
    tablet: 1024,
    desktop: 1440,
    largeDesktop: 1920
  };

  constructor() {
    // Listen to window resize events
    fromEvent(window, 'resize')
      .pipe(
        debounceTime(100),
        map(() => this.getBreakpointState()),
        distinctUntilChanged((prev, curr) => 
          prev.isMobile === curr.isMobile &&
          prev.isTablet === curr.isTablet &&
          prev.isDesktop === curr.isDesktop &&
          prev.isLargeDesktop === curr.isLargeDesktop
        )
      )
      .subscribe(state => this.breakpointSubject.next(state));
  }

  get breakpoint$(): Observable<BreakpointState> {
    return this.breakpointSubject.asObservable();
  }

  get currentBreakpoint(): BreakpointState {
    return this.breakpointSubject.value;
  }

  get isMobile$(): Observable<boolean> {
    return this.breakpoint$.pipe(map(state => state.isMobile));
  }

  get isTablet$(): Observable<boolean> {
    return this.breakpoint$.pipe(map(state => state.isTablet));
  }

  get isDesktop$(): Observable<boolean> {
    return this.breakpoint$.pipe(map(state => state.isDesktop));
  }

  get isMobileOrTablet$(): Observable<boolean> {
    return this.breakpoint$.pipe(map(state => state.isMobile || state.isTablet));
  }

  get isDesktopOrLarger$(): Observable<boolean> {
    return this.breakpoint$.pipe(map(state => state.isDesktop || state.isLargeDesktop));
  }

  // Synchronous getters for immediate access
  get isMobile(): boolean {
    return this.currentBreakpoint.isMobile;
  }

  get isTablet(): boolean {
    return this.currentBreakpoint.isTablet;
  }

  get isDesktop(): boolean {
    return this.currentBreakpoint.isDesktop;
  }

  get isMobileOrTablet(): boolean {
    return this.isMobile || this.isTablet;
  }

  get isDesktopOrLarger(): boolean {
    return this.isDesktop || this.currentBreakpoint.isLargeDesktop;
  }

  private getBreakpointState(): BreakpointState {
    const width = window.innerWidth;
    const height = window.innerHeight;

    return {
      isMobile: width < this.breakpoints.mobile,
      isTablet: width >= this.breakpoints.mobile && width < this.breakpoints.tablet,
      isDesktop: width >= this.breakpoints.tablet && width < this.breakpoints.largeDesktop,
      isLargeDesktop: width >= this.breakpoints.largeDesktop,
      screenWidth: width,
      screenHeight: height
    };
  }

  // Utility methods for responsive behavior
  getColumnsForGrid(mobileColumns: number = 1, tabletColumns: number = 2, desktopColumns: number = 3): number {
    const state = this.currentBreakpoint;
    
    if (state.isMobile) return mobileColumns;
    if (state.isTablet) return tabletColumns;
    return desktopColumns;
  }

  getItemsPerPage(mobileItems: number = 6, tabletItems: number = 12, desktopItems: number = 24): number {
    const state = this.currentBreakpoint;
    
    if (state.isMobile) return mobileItems;
    if (state.isTablet) return tabletItems;
    return desktopItems;
  }

  shouldShowMobileUI(): boolean {
    return this.isMobile;
  }

  shouldShowDesktopSidebar(): boolean {
    return this.isDesktopOrLarger;
  }

  shouldUseMobileNavigation(): boolean {
    return this.isMobileOrTablet;
  }

  getOptimalImageSize(): 'small' | 'medium' | 'large' | 'xlarge' {
    const state = this.currentBreakpoint;
    
    if (state.isMobile) return 'small';
    if (state.isTablet) return 'medium';
    if (state.isDesktop) return 'large';
    return 'xlarge';
  }

  // CSS class helpers for responsive design
  getResponsiveClasses(): string[] {
    const state = this.currentBreakpoint;
    const classes: string[] = [];

    if (state.isMobile) classes.push('mobile');
    if (state.isTablet) classes.push('tablet');
    if (state.isDesktop) classes.push('desktop');
    if (state.isLargeDesktop) classes.push('large-desktop');

    return classes;
  }

  // Touch device detection
  isTouchDevice(): boolean {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }

  // Orientation detection
  isPortrait(): boolean {
    return window.innerHeight > window.innerWidth;
  }

  isLandscape(): boolean {
    return window.innerWidth > window.innerHeight;
  }

  // Safe area support for mobile devices
  getSafeAreaInsets(): { top: number; bottom: number; left: number; right: number } {
    const style = getComputedStyle(document.documentElement);
    
    return {
      top: parseInt(style.getPropertyValue('env(safe-area-inset-top)') || '0'),
      bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
      left: parseInt(style.getPropertyValue('env(safe-area-inset-left)') || '0'),
      right: parseInt(style.getPropertyValue('env(safe-area-inset-right)') || '0')
    };
  }

  // Viewport height calculation (accounting for mobile browser UI)
  getViewportHeight(): number {
    return window.visualViewport?.height || window.innerHeight;
  }

  // Check if device supports hover (not touch-only)
  supportsHover(): boolean {
    return window.matchMedia('(hover: hover)').matches;
  }

  // Check for reduced motion preference
  prefersReducedMotion(): boolean {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  // Check for dark mode preference
  prefersDarkMode(): boolean {
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  }

  // Check for high contrast preference
  prefersHighContrast(): boolean {
    return window.matchMedia('(prefers-contrast: high)').matches;
  }
}
