.shop-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  background: #f8f9fa;
  min-height: 100vh;
}

.shop-content {
  padding-bottom: 2rem;
}

// Hero Section
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  .hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;

    .hero-title {
      font-size: 3rem;
      font-weight: 800;
      margin: 0 0 1rem 0;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .hero-subtitle {
      font-size: 1.2rem;
      margin: 0 0 2rem 0;
      opacity: 0.9;

      @media (max-width: 768px) {
        font-size: 1rem;
      }
    }
  }
}

// Search Container
.search-container {
  margin-bottom: 2rem;

  .search-bar {
    display: flex;
    gap: 1rem;
    max-width: 700px;
    margin: 0 auto 1.5rem auto;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 0.75rem;
    }

    .search-input-wrapper {
      flex: 1;
      position: relative;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 25px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);

      .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #8e8e8e;
        font-size: 1rem;
      }

      .search-input {
        width: 100%;
        padding: 1rem 1rem 1rem 3rem;
        border: none;
        outline: none;
        background: transparent;
        font-size: 1rem;
        color: #262626;

        &::placeholder {
          color: #8e8e8e;
        }
      }

      .clear-btn {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #8e8e8e;
        cursor: pointer;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: rgba(0, 0, 0, 0.1);
        }
      }
    }

    .search-btn {
      padding: 1rem 2rem;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 25px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
      }

      @media (max-width: 768px) {
        justify-content: center;
      }
    }
  }

  .quick-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;

    .filter-chip {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 0.85rem;
      font-weight: 500;
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
      }

      i {
        font-size: 0.8rem;
      }
    }
  }
}

// Shop Stats
.shop-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 2rem;

  @media (max-width: 768px) {
    gap: 2rem;
  }

  @media (max-width: 480px) {
    gap: 1rem;
  }

  .stat-item {
    text-align: center;

    .stat-number {
      display: block;
      font-size: 2rem;
      font-weight: 800;
      line-height: 1;
      margin-bottom: 0.25rem;

      @media (max-width: 768px) {
        font-size: 1.5rem;
      }
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.8;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      @media (max-width: 768px) {
        font-size: 0.8rem;
      }
    }
  }
}

// Main Sections
.main-sections {
  background: #f8f9fa;
}

// Filtered Results
.filtered-results {
  padding: 2rem;
  background: white;
  margin: 2rem;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

  .results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;
    }

    .results-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: #262626;
      margin: 0;
    }

    .results-meta {
      display: flex;
      align-items: center;
      gap: 1rem;

      .results-count {
        color: #8e8e8e;
        font-size: 0.9rem;
      }

      .clear-filters-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: #f8f9fa;
        color: #666;
        border: 1px solid #dee2e6;
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.85rem;

        &:hover {
          background: #e9ecef;
          color: #495057;
        }
      }
    }
  }

  .products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;

    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1rem;
    }

    .product-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .product-image-container {
        position: relative;
        height: 250px;
        overflow: hidden;

        .product-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .discount-badge {
          position: absolute;
          top: 12px;
          right: 12px;
          background: #e91e63;
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 600;
        }
      }

      .product-info {
        padding: 1rem;

        .product-name {
          font-size: 1rem;
          font-weight: 600;
          color: #262626;
          margin: 0 0 0.25rem 0;
          line-height: 1.3;
        }

        .product-brand {
          color: #8e8e8e;
          font-size: 0.85rem;
          margin: 0 0 0.5rem 0;
        }

        .product-pricing {
          .current-price {
            font-size: 1.1rem;
            font-weight: 700;
            color: #262626;
          }

          .original-price {
            font-size: 0.9rem;
            color: #8e8e8e;
            text-decoration: line-through;
            margin-left: 0.5rem;
          }
        }
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .shop-container {
    background: #121212;
  }

  .hero-section {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  }

  .filtered-results {
    background: #1e1e1e;

    .results-title {
      color: #ffffff;
    }

    .product-card {
      background: #2a2a2a;

      .product-name {
        color: #ffffff;
      }
    }

    .clear-filters-btn {
      background: #2a2a2a;
      color: #ffffff;
      border-color: #333;

      &:hover {
        background: #333;
      }
    }
  }
}
