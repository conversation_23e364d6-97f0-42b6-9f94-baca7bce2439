{"ast": null, "code": "import { BehaviorSubject, of } from 'rxjs';\nimport { tap, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nimport * as i3 from \"./cart-new.service\";\nimport * as i4 from \"./wishlist-new.service\";\nimport * as i5 from \"./social-media.service\";\nimport * as i6 from \"./realtime.service\";\nimport * as i7 from \"@angular/router\";\nexport let ButtonActionsService = /*#__PURE__*/(() => {\n  class ButtonActionsService {\n    constructor(http, authService, cartService, wishlistService, socialMediaService, realtimeService, router) {\n      this.http = http;\n      this.authService = authService;\n      this.cartService = cartService;\n      this.wishlistService = wishlistService;\n      this.socialMediaService = socialMediaService;\n      this.realtimeService = realtimeService;\n      this.router = router;\n      this.API_URL = 'http://localhost:5000/api';\n      // Real-time state subjects\n      this.likedPostsSubject = new BehaviorSubject(new Set());\n      this.likedStoriesSubject = new BehaviorSubject(new Set());\n      this.savedPostsSubject = new BehaviorSubject(new Set());\n      this.cartItemsSubject = new BehaviorSubject(new Set());\n      this.wishlistItemsSubject = new BehaviorSubject(new Set());\n      // Observables for components to subscribe to\n      this.likedPosts$ = this.likedPostsSubject.asObservable();\n      this.likedStories$ = this.likedStoriesSubject.asObservable();\n      this.savedPosts$ = this.savedPostsSubject.asObservable();\n      this.cartItems$ = this.cartItemsSubject.asObservable();\n      this.wishlistItems$ = this.wishlistItemsSubject.asObservable();\n      this.initializeUserState();\n    }\n    initializeUserState() {\n      // Initialize user's liked posts, cart items, etc. when service starts\n      if (this.authService.isAuthenticated) {\n        this.loadUserState();\n      }\n      // Listen to auth changes\n      this.authService.currentUser$.subscribe(user => {\n        if (user) {\n          this.loadUserState();\n        } else {\n          this.clearUserState();\n        }\n      });\n    }\n    loadUserState() {\n      // Load user's current state (liked posts, cart items, etc.)\n      this.loadLikedPosts();\n      this.loadCartItems();\n      this.loadWishlistItems();\n    }\n    clearUserState() {\n      this.likedPostsSubject.next(new Set());\n      this.likedStoriesSubject.next(new Set());\n      this.savedPostsSubject.next(new Set());\n      this.cartItemsSubject.next(new Set());\n      this.wishlistItemsSubject.next(new Set());\n    }\n    loadLikedPosts() {\n      // This would load user's liked posts from API\n      // For now, we'll track them as they happen\n    }\n    loadCartItems() {\n      this.cartService.cart$.subscribe(cart => {\n        if (cart?.items) {\n          const productIds = new Set(cart.items.map(item => {\n            const product = item.product;\n            return typeof product === 'string' ? product : product._id;\n          }));\n          this.cartItemsSubject.next(productIds);\n        }\n      });\n    }\n    loadWishlistItems() {\n      this.wishlistService.wishlist$.subscribe(wishlist => {\n        if (wishlist?.items) {\n          const productIds = new Set(wishlist.items.map(item => {\n            const product = item.product;\n            return typeof product === 'string' ? product : product._id;\n          }));\n          this.wishlistItemsSubject.next(productIds);\n        }\n      });\n    }\n    // ==================== CART ACTIONS ====================\n    addToCart(data) {\n      if (!this.authService.isAuthenticated) {\n        // Navigate to login page\n        this.router.navigate(['/auth/login']);\n        return of({\n          success: false,\n          message: 'Please login to add items to cart'\n        });\n      }\n      return this.cartService.addToCart(data.productId, data.quantity || 1, data.size, data.color, data.addedFrom || 'manual').pipe(tap(response => {\n        if (response.success) {\n          // Update local state\n          const currentItems = this.cartItemsSubject.value;\n          currentItems.add(data.productId);\n          this.cartItemsSubject.next(currentItems);\n          // Emit real-time event\n          this.realtimeService.emitCartAdd(data);\n        }\n      }), catchError(error => {\n        console.error('Add to cart error:', error);\n        return of({\n          success: false,\n          message: 'Failed to add item to cart'\n        });\n      }));\n    }\n    // ==================== WISHLIST ACTIONS ====================\n    addToWishlist(data) {\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/auth/login']);\n        return of({\n          success: false,\n          message: 'Please login to add items to wishlist'\n        });\n      }\n      return this.wishlistService.addToWishlist(data.productId, data.size, data.color, data.addedFrom || 'manual').pipe(tap(response => {\n        if (response.success) {\n          // Update local state\n          const currentItems = this.wishlistItemsSubject.value;\n          currentItems.add(data.productId);\n          this.wishlistItemsSubject.next(currentItems);\n          // Emit real-time event\n          this.realtimeService.emitWishlistAdd(data);\n        }\n      }), catchError(error => {\n        console.error('Add to wishlist error:', error);\n        return of({\n          success: false,\n          message: 'Failed to add item to wishlist'\n        });\n      }));\n    }\n    removeFromWishlist(productId) {\n      if (!this.authService.isAuthenticated) {\n        return of({\n          success: false,\n          message: 'Please login to manage wishlist'\n        });\n      }\n      return this.wishlistService.removeFromWishlist(productId).pipe(tap(response => {\n        if (response.success) {\n          // Update local state\n          const currentItems = this.wishlistItemsSubject.value;\n          currentItems.delete(productId);\n          this.wishlistItemsSubject.next(currentItems);\n          // Emit real-time event\n          this.realtimeService.emitWishlistRemove(productId);\n        }\n      }), catchError(error => {\n        console.error('Remove from wishlist error:', error);\n        return of({\n          success: false,\n          message: 'Failed to remove item from wishlist'\n        });\n      }));\n    }\n    // ==================== BUY NOW ACTION ====================\n    buyNow(data) {\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/auth/login']);\n        return of({\n          success: false,\n          message: 'Please login to purchase items'\n        });\n      }\n      // Add to cart first, then redirect to checkout\n      return this.addToCart(data).pipe(tap(response => {\n        if (response.success) {\n          // Navigate to checkout page\n          this.router.navigate(['/checkout']);\n        }\n      }));\n    }\n    // ==================== POST ACTIONS ====================\n    likePost(postId) {\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/auth/login']);\n        return of({\n          success: false,\n          message: 'Please login to like posts'\n        });\n      }\n      return this.socialMediaService.likePost(postId).pipe(tap(response => {\n        if (response.success) {\n          // Update local state\n          const currentLiked = this.likedPostsSubject.value;\n          currentLiked.add(postId);\n          this.likedPostsSubject.next(currentLiked);\n        }\n      }), catchError(error => {\n        console.error('Like post error:', error);\n        return of({\n          success: false,\n          message: 'Failed to like post'\n        });\n      }));\n    }\n    unlikePost(postId) {\n      if (!this.authService.isAuthenticated) {\n        return of({\n          success: false,\n          message: 'Please login to unlike posts'\n        });\n      }\n      return this.socialMediaService.unlikePost(postId).pipe(tap(response => {\n        if (response.success) {\n          // Update local state\n          const currentLiked = this.likedPostsSubject.value;\n          currentLiked.delete(postId);\n          this.likedPostsSubject.next(currentLiked);\n        }\n      }), catchError(error => {\n        console.error('Unlike post error:', error);\n        return of({\n          success: false,\n          message: 'Failed to unlike post'\n        });\n      }));\n    }\n    commentOnPost(data) {\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/auth/login']);\n        return of({\n          success: false,\n          message: 'Please login to comment on posts'\n        });\n      }\n      if (!data.text?.trim()) {\n        return of({\n          success: false,\n          message: 'Comment text is required'\n        });\n      }\n      return this.socialMediaService.commentOnPost(data.postId, data.text).pipe(catchError(error => {\n        console.error('Comment on post error:', error);\n        return of({\n          success: false,\n          message: 'Failed to add comment'\n        });\n      }));\n    }\n    sharePost(postId) {\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/auth/login']);\n        return of({\n          success: false,\n          message: 'Please login to share posts'\n        });\n      }\n      return this.socialMediaService.sharePost(postId).pipe(catchError(error => {\n        console.error('Share post error:', error);\n        return of({\n          success: false,\n          message: 'Failed to share post'\n        });\n      }));\n    }\n    savePost(postId) {\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/auth/login']);\n        return of({\n          success: false,\n          message: 'Please login to save posts'\n        });\n      }\n      return this.socialMediaService.savePost(postId).pipe(tap(response => {\n        if (response.success) {\n          // Update local state\n          const currentSaved = this.savedPostsSubject.value;\n          currentSaved.add(postId);\n          this.savedPostsSubject.next(currentSaved);\n        }\n      }), catchError(error => {\n        console.error('Save post error:', error);\n        return of({\n          success: false,\n          message: 'Failed to save post'\n        });\n      }));\n    }\n    // ==================== UTILITY METHODS ====================\n    isInCart(productId) {\n      return this.cartItemsSubject.value.has(productId);\n    }\n    isInWishlist(productId) {\n      return this.wishlistItemsSubject.value.has(productId);\n    }\n    isPostLiked(postId) {\n      return this.likedPostsSubject.value.has(postId);\n    }\n    isPostSaved(postId) {\n      return this.savedPostsSubject.value.has(postId);\n    }\n    showSuccessMessage(message) {\n      // This would show a toast/snackbar message\n      console.log('✅', message);\n    }\n    showErrorMessage(message) {\n      // This would show a toast/snackbar message\n      console.error('❌', message);\n    }\n    static {\n      this.ɵfac = function ButtonActionsService_Factory(t) {\n        return new (t || ButtonActionsService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService), i0.ɵɵinject(i3.CartNewService), i0.ɵɵinject(i4.WishlistNewService), i0.ɵɵinject(i5.SocialMediaService), i0.ɵɵinject(i6.RealtimeService), i0.ɵɵinject(i7.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ButtonActionsService,\n        factory: ButtonActionsService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ButtonActionsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}