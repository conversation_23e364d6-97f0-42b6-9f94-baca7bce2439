{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@angular/common\";\nfunction CreatePostComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Click to upload images or videos\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Support: JPG, PNG, MP4, MOV\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CreatePostComponent_div_15_div_1_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 40);\n  }\n  if (rf & 2) {\n    const file_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", file_r4.preview, i0.ɵɵsanitizeUrl)(\"alt\", file_r4.name);\n  }\n}\nfunction CreatePostComponent_div_15_div_1_video_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 41);\n  }\n  if (rf & 2) {\n    const file_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", file_r4.preview, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CreatePostComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, CreatePostComponent_div_15_div_1_img_1_Template, 1, 2, \"img\", 36)(2, CreatePostComponent_div_15_div_1_video_2_Template, 1, 1, \"video\", 37);\n    i0.ɵɵelementStart(3, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function CreatePostComponent_div_15_div_1_Template_button_click_3_listener() {\n      const i_r5 = i0.ɵɵrestoreView(_r3).index;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.removeFile(i_r5));\n    });\n    i0.ɵɵelement(4, \"i\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r4.type.startsWith(\"image\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r4.type.startsWith(\"video\"));\n  }\n}\nfunction CreatePostComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, CreatePostComponent_div_15_div_1_Template, 5, 2, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.selectedFiles);\n  }\n}\nfunction CreatePostComponent_div_44_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵlistener(\"click\", function CreatePostComponent_div_44_div_5_div_1_Template_div_click_0_listener() {\n      const category_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r5.selectCategory(category_r9));\n    });\n    i0.ɵɵelement(1, \"i\", 49);\n    i0.ɵɵelementStart(2, \"div\", 50)(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const category_r9 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(category_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r9.description);\n  }\n}\nfunction CreatePostComponent_div_44_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtemplate(1, CreatePostComponent_div_44_div_5_div_1_Template, 7, 2, \"div\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.categorySearchResults);\n  }\n}\nfunction CreatePostComponent_div_44_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"h4\");\n    i0.ɵɵtext(2, \"Selected Category:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52);\n    i0.ɵɵelement(4, \"i\", 49);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function CreatePostComponent_div_44_div_6_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.selectedCategory = null);\n    });\n    i0.ɵɵelement(8, \"i\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r5.selectedCategory.name);\n  }\n}\nfunction CreatePostComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"h3\");\n    i0.ɵɵtext(2, \"Select Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42)(4, \"input\", 43);\n    i0.ɵɵlistener(\"input\", function CreatePostComponent_div_44_Template_input_input_4_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.searchCategories($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CreatePostComponent_div_44_div_5_Template, 2, 1, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, CreatePostComponent_div_44_div_6_Template, 9, 1, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.categorySearchResults.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.selectedCategory);\n  }\n}\nfunction CreatePostComponent_div_45_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵlistener(\"click\", function CreatePostComponent_div_45_div_5_div_1_Template_div_click_0_listener() {\n      const product_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r5.addProductTag(product_r13));\n    });\n    i0.ɵɵelement(1, \"img\", 40);\n    i0.ɵɵelementStart(2, \"div\", 61)(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r13 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r13.images[0] == null ? null : product_r13.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r13.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r13.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(7, 4, product_r13.price, \"1.0-0\"), \"\");\n  }\n}\nfunction CreatePostComponent_div_45_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, CreatePostComponent_div_45_div_5_div_1_Template, 8, 7, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.searchResults);\n  }\n}\nfunction CreatePostComponent_div_45_div_6_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelement(1, \"img\", 40);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function CreatePostComponent_div_45_div_6_div_4_Template_button_click_4_listener() {\n      const i_r15 = i0.ɵɵrestoreView(_r14).index;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r5.removeProductTag(i_r15));\n    });\n    i0.ɵɵelement(5, \"i\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r16 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r16.images[0] == null ? null : product_r16.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r16.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r16.name);\n  }\n}\nfunction CreatePostComponent_div_45_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"h4\");\n    i0.ɵɵtext(2, \"Tagged Products:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 63);\n    i0.ɵɵtemplate(4, CreatePostComponent_div_45_div_6_div_4_Template, 6, 3, \"div\", 64);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.taggedProducts);\n  }\n}\nfunction CreatePostComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"h3\");\n    i0.ɵɵtext(2, \"Tag Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 54)(4, \"input\", 55);\n    i0.ɵɵlistener(\"input\", function CreatePostComponent_div_45_Template_input_input_4_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.searchProducts($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CreatePostComponent_div_45_div_5_Template, 2, 1, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, CreatePostComponent_div_45_div_6_Template, 5, 1, \"div\", 57);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.searchResults.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.taggedProducts.length > 0);\n  }\n}\nfunction CreatePostComponent_div_50_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function CreatePostComponent_div_50_span_1_Template_button_click_2_listener() {\n      const i_r18 = i0.ɵɵrestoreView(_r17).index;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.removeHashtag(i_r18));\n    });\n    i0.ɵɵtext(3, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tag_r19 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" #\", tag_r19, \" \");\n  }\n}\nfunction CreatePostComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵtemplate(1, CreatePostComponent_div_50_span_1_Template, 4, 1, \"span\", 67);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.hashtags);\n  }\n}\nfunction CreatePostComponent_span_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Publishing...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreatePostComponent_span_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Publish Post\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let CreatePostComponent = /*#__PURE__*/(() => {\n  class CreatePostComponent {\n    constructor(fb, router, http) {\n      this.fb = fb;\n      this.router = router;\n      this.http = http;\n      this.selectedFiles = [];\n      this.taggedProducts = [];\n      this.hashtags = [];\n      this.searchResults = [];\n      this.uploading = false;\n      // New properties for mandatory linking\n      this.linkType = 'product';\n      this.selectedCategory = null;\n      this.categories = [];\n      this.categorySearchResults = [];\n      this.postForm = this.fb.group({\n        caption: ['', [Validators.required, Validators.maxLength(2000)]],\n        allowComments: [true],\n        allowSharing: [true],\n        linkType: ['product', Validators.required]\n      });\n    }\n    ngOnInit() {\n      // Initialize component\n      this.loadCategories();\n      // Watch for link type changes\n      this.postForm.get('linkType')?.valueChanges.subscribe(value => {\n        this.linkType = value;\n        // Clear selections when switching types\n        if (value === 'product') {\n          this.selectedCategory = null;\n        } else {\n          this.taggedProducts = [];\n        }\n      });\n    }\n    loadCategories() {\n      // Load categories from API\n      this.http.get('/api/categories').subscribe({\n        next: response => {\n          this.categories = response.categories || [];\n        },\n        error: error => {\n          console.error('Error loading categories:', error);\n        }\n      });\n    }\n    onFileSelect(event) {\n      const files = Array.from(event.target.files);\n      files.forEach(file => {\n        const reader = new FileReader();\n        reader.onload = e => {\n          this.selectedFiles.push({\n            file,\n            preview: e.target.result,\n            type: file.type,\n            name: file.name\n          });\n        };\n        reader.readAsDataURL(file);\n      });\n    }\n    removeFile(index) {\n      this.selectedFiles.splice(index, 1);\n    }\n    searchProducts(event) {\n      const query = event.target.value;\n      if (query.length > 2) {\n        // Search products from API\n        this.http.get(`/api/products/search?q=${query}`).subscribe({\n          next: response => {\n            this.searchResults = response.products || [];\n          },\n          error: error => {\n            console.error('Error searching products:', error);\n            this.searchResults = [];\n          }\n        });\n      } else {\n        this.searchResults = [];\n      }\n    }\n    searchCategories(event) {\n      const query = event.target.value;\n      if (query.length > 2) {\n        // Filter categories based on query\n        this.categorySearchResults = this.categories.filter(category => category.name.toLowerCase().includes(query.toLowerCase()));\n      } else {\n        this.categorySearchResults = [];\n      }\n    }\n    selectCategory(category) {\n      this.selectedCategory = category;\n      this.categorySearchResults = [];\n    }\n    addProductTag(product) {\n      if (!this.taggedProducts.find(p => p._id === product._id)) {\n        this.taggedProducts.push(product);\n      }\n      this.searchResults = [];\n    }\n    removeProductTag(index) {\n      this.taggedProducts.splice(index, 1);\n    }\n    addHashtag(event) {\n      const tag = event.target.value.trim().replace('#', '');\n      if (tag && !this.hashtags.includes(tag)) {\n        this.hashtags.push(tag);\n        event.target.value = '';\n      }\n    }\n    removeHashtag(index) {\n      this.hashtags.splice(index, 1);\n    }\n    saveDraft() {\n      // TODO: Implement save as draft functionality\n      console.log('Saving as draft...');\n    }\n    onSubmit() {\n      // Validate mandatory linking\n      if (this.linkType === 'product' && this.taggedProducts.length === 0) {\n        alert('Please tag at least one product before publishing your post. This helps customers discover and purchase your products!');\n        return;\n      }\n      if (this.linkType === 'category' && !this.selectedCategory) {\n        alert('Please select a category before publishing your post. This helps customers find relevant content!');\n        return;\n      }\n      if (this.postForm.valid && this.selectedFiles.length > 0) {\n        this.uploading = true;\n        // Prepare linked content based on type\n        const linkedContent = this.linkType === 'product' ? {\n          type: 'product',\n          productId: this.taggedProducts[0]._id // Use first tagged product as primary link\n        } : {\n          type: 'category',\n          categoryId: this.selectedCategory._id\n        };\n        const postData = {\n          caption: this.postForm.value.caption,\n          media: this.selectedFiles.map(f => ({\n            type: f.type.startsWith('image') ? 'image' : 'video',\n            url: f.preview // In real implementation, upload to server first\n          })),\n          linkedContent: linkedContent,\n          products: this.taggedProducts.map(p => ({\n            product: p._id,\n            position: {\n              x: 50,\n              y: 50\n            } // Default position\n          })),\n          hashtags: this.hashtags,\n          settings: {\n            allowComments: this.postForm.value.allowComments,\n            allowSharing: this.postForm.value.allowSharing\n          }\n        };\n        // TODO: Implement actual post creation API\n        this.http.post('/api/posts', postData).subscribe({\n          next: response => {\n            this.uploading = false;\n            alert('Post created successfully!');\n            this.router.navigate(['/vendor/posts']);\n          },\n          error: error => {\n            this.uploading = false;\n            alert('Error creating post: ' + (error.error?.message || 'Unknown error'));\n          }\n        });\n      }\n    }\n    static {\n      this.ɵfac = function CreatePostComponent_Factory(t) {\n        return new (t || CreatePostComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.HttpClient));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CreatePostComponent,\n        selectors: [[\"app-create-post\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 69,\n        vars: 12,\n        consts: [[\"fileInput\", \"\"], [1, \"create-post-container\"], [1, \"header\"], [1, \"post-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"form-section\"], [1, \"media-upload\"], [1, \"upload-area\", 3, \"click\"], [\"type\", \"file\", \"multiple\", \"\", \"accept\", \"image/*,video/*\", 2, \"display\", \"none\", 3, \"change\"], [\"class\", \"upload-content\", 4, \"ngIf\"], [\"class\", \"file-preview\", 4, \"ngIf\"], [\"formControlName\", \"caption\", \"placeholder\", \"Write a caption for your post...\", \"rows\", \"4\", \"maxlength\", \"2000\"], [1, \"char-count\"], [1, \"section-description\"], [1, \"link-type-selector\"], [1, \"link-option\"], [\"type\", \"radio\", \"formControlName\", \"linkType\", \"value\", \"product\"], [1, \"option-content\"], [1, \"fas\", \"fa-box\"], [\"type\", \"radio\", \"formControlName\", \"linkType\", \"value\", \"category\"], [1, \"fas\", \"fa-tags\"], [\"class\", \"form-section\", 4, \"ngIf\"], [\"type\", \"text\", \"placeholder\", \"Add hashtags (e.g., #fashion #style #trending)\", 1, \"hashtag-input\", 3, \"keyup.enter\"], [\"class\", \"hashtags\", 4, \"ngIf\"], [1, \"settings-grid\"], [1, \"setting-item\"], [\"type\", \"checkbox\", \"formControlName\", \"allowComments\"], [\"type\", \"checkbox\", \"formControlName\", \"allowSharing\"], [1, \"form-actions\"], [\"type\", \"button\", 1, \"btn-secondary\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn-primary\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"upload-content\"], [1, \"fas\", \"fa-cloud-upload-alt\"], [1, \"file-preview\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-item\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [\"controls\", \"\", 3, \"src\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"remove-file\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [3, \"src\", \"alt\"], [\"controls\", \"\", 3, \"src\"], [1, \"category-search\"], [\"type\", \"text\", \"placeholder\", \"Search categories...\", 1, \"search-input\", 3, \"input\"], [\"class\", \"category-results\", 4, \"ngIf\"], [\"class\", \"selected-category\", 4, \"ngIf\"], [1, \"category-results\"], [\"class\", \"category-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"category-item\", 3, \"click\"], [1, \"fas\", \"fa-tag\"], [1, \"category-info\"], [1, \"selected-category\"], [1, \"category-display\"], [\"type\", \"button\", 3, \"click\"], [1, \"product-search\"], [\"type\", \"text\", \"placeholder\", \"Search your products...\", 1, \"search-input\", 3, \"input\"], [\"class\", \"product-results\", 4, \"ngIf\"], [\"class\", \"tagged-products\", 4, \"ngIf\"], [1, \"product-results\"], [\"class\", \"product-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-item\", 3, \"click\"], [1, \"product-info\"], [1, \"tagged-products\"], [1, \"tagged-list\"], [\"class\", \"tagged-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"tagged-item\"], [1, \"hashtags\"], [\"class\", \"hashtag\", 4, \"ngFor\", \"ngForOf\"], [1, \"hashtag\"]],\n        template: function CreatePostComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h1\");\n            i0.ɵɵtext(3, \"Create New Post\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\");\n            i0.ɵɵtext(5, \"Share your products with the community\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"form\", 3);\n            i0.ɵɵlistener(\"ngSubmit\", function CreatePostComponent_Template_form_ngSubmit_6_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSubmit());\n            });\n            i0.ɵɵelementStart(7, \"div\", 4)(8, \"h3\");\n            i0.ɵɵtext(9, \"Media\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"div\", 5)(11, \"div\", 6);\n            i0.ɵɵlistener(\"click\", function CreatePostComponent_Template_div_click_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const fileInput_r2 = i0.ɵɵreference(13);\n              return i0.ɵɵresetView(fileInput_r2.click());\n            });\n            i0.ɵɵelementStart(12, \"input\", 7, 0);\n            i0.ɵɵlistener(\"change\", function CreatePostComponent_Template_input_change_12_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onFileSelect($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(14, CreatePostComponent_div_14_Template, 6, 0, \"div\", 8)(15, CreatePostComponent_div_15_Template, 2, 1, \"div\", 9);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(16, \"div\", 4)(17, \"h3\");\n            i0.ɵɵtext(18, \"Caption\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(19, \"textarea\", 10);\n            i0.ɵɵelementStart(20, \"div\", 11);\n            i0.ɵɵtext(21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 4)(23, \"h3\");\n            i0.ɵɵtext(24, \"Content Linking (Required)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"p\", 12);\n            i0.ɵɵtext(26, \"Choose how to link your post for better discoverability\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"div\", 13)(28, \"label\", 14);\n            i0.ɵɵelement(29, \"input\", 15);\n            i0.ɵɵelementStart(30, \"span\", 16);\n            i0.ɵɵelement(31, \"i\", 17);\n            i0.ɵɵelementStart(32, \"strong\");\n            i0.ɵɵtext(33, \"Link to Product\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"small\");\n            i0.ɵɵtext(35, \"Tag specific products in your post\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(36, \"label\", 14);\n            i0.ɵɵelement(37, \"input\", 18);\n            i0.ɵɵelementStart(38, \"span\", 16);\n            i0.ɵɵelement(39, \"i\", 19);\n            i0.ɵɵelementStart(40, \"strong\");\n            i0.ɵɵtext(41, \"Link to Category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"small\");\n            i0.ɵɵtext(43, \"Associate with a product category\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(44, CreatePostComponent_div_44_Template, 7, 2, \"div\", 20)(45, CreatePostComponent_div_45_Template, 7, 2, \"div\", 20);\n            i0.ɵɵelementStart(46, \"div\", 4)(47, \"h3\");\n            i0.ɵɵtext(48, \"Hashtags\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"input\", 21);\n            i0.ɵɵlistener(\"keyup.enter\", function CreatePostComponent_Template_input_keyup_enter_49_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.addHashtag($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(50, CreatePostComponent_div_50_Template, 2, 1, \"div\", 22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"div\", 4)(52, \"h3\");\n            i0.ɵɵtext(53, \"Post Settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"div\", 23)(55, \"label\", 24);\n            i0.ɵɵelement(56, \"input\", 25);\n            i0.ɵɵelementStart(57, \"span\");\n            i0.ɵɵtext(58, \"Allow comments\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(59, \"label\", 24);\n            i0.ɵɵelement(60, \"input\", 26);\n            i0.ɵɵelementStart(61, \"span\");\n            i0.ɵɵtext(62, \"Allow sharing\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(63, \"div\", 27)(64, \"button\", 28);\n            i0.ɵɵlistener(\"click\", function CreatePostComponent_Template_button_click_64_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.saveDraft());\n            });\n            i0.ɵɵtext(65, \"Save as Draft\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"button\", 29);\n            i0.ɵɵtemplate(67, CreatePostComponent_span_67_Template, 2, 0, \"span\", 30)(68, CreatePostComponent_span_68_Template, 2, 0, \"span\", 30);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            let tmp_5_0;\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"formGroup\", ctx.postForm);\n            i0.ɵɵadvance(5);\n            i0.ɵɵclassProp(\"has-files\", ctx.selectedFiles.length > 0);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedFiles.length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedFiles.length > 0);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate1(\"\", ((tmp_5_0 = ctx.postForm.get(\"caption\")) == null ? null : tmp_5_0.value == null ? null : tmp_5_0.value.length) || 0, \"/2000\");\n            i0.ɵɵadvance(23);\n            i0.ɵɵproperty(\"ngIf\", ctx.linkType === \"category\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.linkType === \"product\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.hashtags.length > 0);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"disabled\", !ctx.postForm.valid || ctx.uploading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.uploading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.uploading);\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DecimalPipe, FormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, ReactiveFormsModule, i1.FormGroupDirective, i1.FormControlName],\n        styles: [\".create-post-container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto;padding:20px}.header[_ngcontent-%COMP%]{margin-bottom:30px}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:600;margin-bottom:8px}.header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666}.post-form[_ngcontent-%COMP%]{background:#fff;border-radius:8px;padding:30px;border:1px solid #eee}.form-section[_ngcontent-%COMP%]{margin-bottom:30px}.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;margin-bottom:15px}.section-description[_ngcontent-%COMP%]{color:#666;font-size:.9rem;margin-bottom:15px}.link-type-selector[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:15px;margin-bottom:20px}.link-option[_ngcontent-%COMP%]{border:2px solid #e9ecef;border-radius:8px;padding:20px;cursor:pointer;transition:all .2s;display:block}.link-option[_ngcontent-%COMP%]:hover{border-color:#007bff;background:#f8f9ff}.link-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{display:none}.link-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked + .option-content[_ngcontent-%COMP%]{color:#007bff}.link-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked + .option-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#007bff}.link-option[_ngcontent-%COMP%]:has(input[type=radio]:checked){border-color:#007bff;background:#f8f9ff}.option-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;text-align:center;gap:8px}.option-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem;color:#6c757d;margin-bottom:5px}.option-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-size:1rem;margin-bottom:2px}.option-content[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{color:#6c757d;font-size:.85rem}.category-results[_ngcontent-%COMP%]{max-height:200px;overflow-y:auto;border:1px solid #eee;border-radius:6px}.category-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:12px;cursor:pointer;border-bottom:1px solid #f5f5f5}.category-item[_ngcontent-%COMP%]:hover{background:#f8f9fa}.category-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#007bff;font-size:1.2rem}.category-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:.9rem;margin-bottom:2px}.category-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:.85rem}.selected-category[_ngcontent-%COMP%]{margin-top:15px}.category-display[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;background:#f8f9fa;padding:8px 12px;border-radius:20px;font-size:.85rem;width:-moz-fit-content;width:fit-content}.category-display[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#007bff}.category-display[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:none;border:none;color:#666;cursor:pointer;margin-left:5px}.upload-area[_ngcontent-%COMP%]{border:2px dashed #ddd;border-radius:8px;padding:40px;text-align:center;cursor:pointer;transition:all .2s}.upload-area[_ngcontent-%COMP%]:hover{border-color:#007bff;background:#f8f9ff}.upload-area.has-files[_ngcontent-%COMP%]{padding:20px}.upload-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem;color:#ddd;margin-bottom:15px}.upload-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.1rem;margin-bottom:5px}.upload-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#666;font-size:.9rem}.file-preview[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(150px,1fr));gap:15px}.file-item[_ngcontent-%COMP%]{position:relative;border-radius:8px;overflow:hidden}.file-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .file-item[_ngcontent-%COMP%]   video[_ngcontent-%COMP%]{width:100%;height:150px;object-fit:cover}.remove-file[_ngcontent-%COMP%]{position:absolute;top:8px;right:8px;background:#000000b3;color:#fff;border:none;border-radius:50%;width:24px;height:24px;cursor:pointer}textarea[_ngcontent-%COMP%]{width:100%;padding:12px;border:1px solid #ddd;border-radius:6px;font-family:inherit;resize:vertical}.char-count[_ngcontent-%COMP%]{text-align:right;color:#666;font-size:.85rem;margin-top:5px}.search-input[_ngcontent-%COMP%]{width:100%;padding:12px;border:1px solid #ddd;border-radius:6px;margin-bottom:10px}.product-results[_ngcontent-%COMP%]{max-height:200px;overflow-y:auto;border:1px solid #eee;border-radius:6px}.product-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:12px;cursor:pointer;border-bottom:1px solid #f5f5f5}.product-item[_ngcontent-%COMP%]:hover{background:#f8f9fa}.product-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:50px;height:50px;object-fit:cover;border-radius:4px}.product-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:.9rem;margin-bottom:2px}.product-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:.85rem}.tagged-products[_ngcontent-%COMP%]{margin-top:15px}.tagged-list[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:10px;margin-top:10px}.tagged-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;background:#f8f9fa;padding:8px 12px;border-radius:20px;font-size:.85rem}.tagged-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:24px;height:24px;object-fit:cover;border-radius:50%}.tagged-item[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:none;border:none;color:#666;cursor:pointer;margin-left:5px}.hashtag-input[_ngcontent-%COMP%]{width:100%;padding:12px;border:1px solid #ddd;border-radius:6px}.hashtags[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px;margin-top:10px}.hashtag[_ngcontent-%COMP%]{background:#007bff;color:#fff;padding:4px 8px;border-radius:12px;font-size:.85rem;display:flex;align-items:center;gap:5px}.hashtag[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:none;border:none;color:#fff;cursor:pointer;font-size:1.1rem}.settings-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px}.setting-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;cursor:pointer}.form-actions[_ngcontent-%COMP%]{display:flex;gap:15px;justify-content:flex-end;margin-top:30px;padding-top:20px;border-top:1px solid #eee}.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%]{padding:12px 24px;border-radius:6px;font-weight:500;cursor:pointer;border:none;transition:all .2s}.btn-primary[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){background:#0056b3}.btn-primary[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.btn-secondary[_ngcontent-%COMP%]{background:#f8f9fa;color:#6c757d;border:1px solid #dee2e6}.btn-secondary[_ngcontent-%COMP%]:hover{background:#e9ecef}@media (max-width: 768px){.form-actions[_ngcontent-%COMP%]{flex-direction:column}.file-preview[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(120px,1fr))}.link-type-selector[_ngcontent-%COMP%]{grid-template-columns:1fr}.link-option[_ngcontent-%COMP%]{padding:15px}.option-content[_ngcontent-%COMP%]{flex-direction:row;text-align:left}.option-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:0}}\"]\n      });\n    }\n  }\n  return CreatePostComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}