{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { of, throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ProductService = /*#__PURE__*/(() => {\n  class ProductService {\n    constructor(http) {\n      this.http = http;\n      this.API_URL = 'http://localhost:5000/api';\n    }\n    getProducts(filters = {}) {\n      let params = new HttpParams();\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null && value !== '') {\n          params = params.set(key, value.toString());\n        }\n      });\n      return this.http.get(`${this.API_URL}/products`, {\n        params\n      });\n    }\n    getProduct(id) {\n      return this.http.get(`${this.API_URL}/products/${id}`);\n    }\n    createProduct(productData) {\n      return this.http.post(`${this.API_URL}/products`, productData);\n    }\n    updateProduct(id, productData) {\n      return this.http.put(`${this.API_URL}/products/${id}`, productData);\n    }\n    deleteProduct(id) {\n      return this.http.delete(`${this.API_URL}/products/${id}`);\n    }\n    addReview(productId, reviewData) {\n      return this.http.post(`${this.API_URL}/products/${productId}/review`, reviewData);\n    }\n    getFeaturedProducts() {\n      return this.http.get(`${this.API_URL}/products/featured`);\n    }\n    getVendorProducts(vendorId, filters = {}) {\n      let params = new HttpParams();\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null && value !== '') {\n          params = params.set(key, value.toString());\n        }\n      });\n      return this.http.get(`${this.API_URL}/products/vendor/${vendorId}`, {\n        params\n      });\n    }\n    searchProducts(query, filters = {}) {\n      const searchFilters = {\n        ...filters,\n        search: query\n      };\n      return this.getProducts(searchFilters);\n    }\n    getCategories() {\n      return this.http.get(`${this.API_URL}/categories`);\n    }\n    getBrands() {\n      return this.http.get(`${this.API_URL}/products/brands`);\n    }\n    // Featured Brands\n    getFeaturedBrands() {\n      return this.http.get(`${this.API_URL}/brands/featured`).pipe(catchError(error => {\n        console.error('Error fetching featured brands:', error);\n        return of({\n          success: false,\n          data: [],\n          error: error.message\n        });\n      }));\n    }\n    // Trending Products\n    getTrendingProducts(limit = 10) {\n      return this.http.get(`${this.API_URL}/products/trending?limit=${limit}`).pipe(catchError(error => {\n        console.error('Error fetching trending products:', error);\n        return of({\n          success: false,\n          data: [],\n          error: error.message\n        });\n      }));\n    }\n    // New Arrivals\n    getNewArrivals(limit = 10) {\n      return this.http.get(`${this.API_URL}/products/new-arrivals?limit=${limit}`).pipe(catchError(error => {\n        console.error('Error fetching new arrivals:', error);\n        return of({\n          success: false,\n          data: [],\n          error: error.message\n        });\n      }));\n    }\n    // Get products by brand\n    getProductsByBrand(brandId, page = 1, limit = 20) {\n      let params = new HttpParams().set('page', page.toString()).set('limit', limit.toString());\n      return this.http.get(`${this.API_URL}/products/brand/${brandId}`, {\n        params\n      }).pipe(catchError(error => {\n        console.error('Error fetching products by brand:', error);\n        return of({\n          products: [],\n          pagination: {\n            current: 1,\n            pages: 0,\n            total: 0\n          }\n        });\n      }));\n    }\n    // Get product recommendations\n    getRecommendations(productId, limit = 5) {\n      return this.http.get(`${this.API_URL}/products/${productId}/recommendations?limit=${limit}`).pipe(catchError(error => {\n        console.error('Error fetching recommendations:', error);\n        return of({\n          success: false,\n          data: [],\n          error: error.message\n        });\n      }));\n    }\n    // Get product reviews\n    getProductReviews(productId, page = 1, limit = 10) {\n      let params = new HttpParams().set('page', page.toString()).set('limit', limit.toString());\n      return this.http.get(`${this.API_URL}/products/${productId}/reviews`, {\n        params\n      }).pipe(catchError(error => {\n        console.error('Error fetching product reviews:', error);\n        return of({\n          success: false,\n          data: [],\n          pagination: {\n            page: 1,\n            limit: 10,\n            total: 0,\n            totalPages: 0\n          },\n          error: error.message\n        });\n      }));\n    }\n    // Add product review\n    addProductReview(productId, review) {\n      return this.http.post(`${this.API_URL}/products/${productId}/reviews`, review).pipe(catchError(error => {\n        console.error('Error adding product review:', error);\n        return throwError(() => error);\n      }));\n    }\n    // Product interactions\n    toggleProductLike(productId) {\n      return this.http.post(`${this.API_URL}/products/${productId}/like`, {});\n    }\n    shareProduct(productId) {\n      return this.http.post(`${this.API_URL}/products/${productId}/share`, {});\n    }\n    // Category products\n    getCategoryProducts(categorySlug, filters = {}) {\n      let params = new HttpParams();\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null && value !== '') {\n          params = params.set(key, value.toString());\n        }\n      });\n      return this.http.get(`${this.API_URL}/products/category/${categorySlug}`, {\n        params\n      });\n    }\n    static {\n      this.ɵfac = function ProductService_Factory(t) {\n        return new (t || ProductService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ProductService,\n        factory: ProductService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ProductService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}