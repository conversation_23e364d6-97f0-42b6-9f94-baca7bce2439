<div class="home-sidebar">
  <!-- User Profile Card -->
  <div class="profile-card" *ngIf="currentUser">
    <div class="profile-avatar-container">
      <img [src]="currentUser.avatar || '/assets/images/default-avatar.jpg'"
           [alt]="currentUser.username" class="profile-avatar">
    </div>
    <div class="profile-info">
      <h4 class="profile-username">{{ currentUser.username }}</h4>
      <span class="profile-name">{{ currentUser.fullName }}</span>
    </div>
    <button class="switch-btn" routerLink="/profile">
      <i class="fas fa-user-cog"></i>
      <span>Profile</span>
    </button>
  </div>

  <!-- Summer Collection 2024 -->
  <section class="sidebar-section summer-collection" *ngIf="summerCollection.length > 0">
    <div class="section-header">
      <h3><i class="fas fa-sun"></i> Summer Collection 2024</h3>
      <button class="view-all-btn" (click)="viewSummerCollection()">
        <span>View All</span>
        <i class="fas fa-arrow-right"></i>
      </button>
    </div>
    <div class="collection-items">
      <div *ngFor="let item of summerCollection.slice(0, 3); trackBy: trackByProductId"
           class="collection-item" (click)="onProductClick(item)">
        <div class="collection-image-container">
          <img [src]="item.images?.[0]?.url || '/assets/images/product-placeholder.jpg'"
               [alt]="item.name" class="collection-image">
          <div class="collection-overlay">
            <i class="fas fa-eye"></i>
          </div>
        </div>
        <div class="collection-info">
          <span class="collection-name">{{ item.name }}</span>
          <span class="collection-price">{{ formatPrice(item.price) }}</span>
          <div class="collection-discount" *ngIf="getDiscountPercentage(item) > 0">
            <i class="fas fa-tag"></i>
            {{ getDiscountPercentage(item) }}% OFF
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Suggested Products Component -->
  <section class="sidebar-section suggested-section">
    <div class="section-header">
      <h3><i class="fas fa-magic"></i> Suggested for You</h3>
      <button class="view-all-btn" routerLink="/shop?filter=suggested">
        <span>View All</span>
        <i class="fas fa-arrow-right"></i>
      </button>
    </div>
    <div class="component-wrapper">
      <app-suggested-products></app-suggested-products>
    </div>
  </section>

  <!-- Featured Products -->
  <section class="sidebar-section featured-section" *ngIf="featuredProducts.length > 0">
    <div class="section-header">
      <h3><i class="fas fa-star"></i> Featured Products</h3>
      <button class="view-all-btn" (click)="viewFeaturedProducts()">
        <span>View All</span>
        <i class="fas fa-arrow-right"></i>
      </button>
    </div>
    <div class="featured-grid">
      <div *ngFor="let product of featuredProducts.slice(0, 4); trackBy: trackByProductId"
           class="featured-item" (click)="onProductClick(product)">
        <div class="featured-image-container">
          <img [src]="product.images?.[0]?.url || '/assets/images/product-placeholder.jpg'"
               [alt]="product.name" class="featured-image">
          <div class="featured-overlay">
            <i class="fas fa-shopping-bag"></i>
          </div>
        </div>
        <div class="featured-info">
          <span class="featured-name">{{ product.name }}</span>
          <span class="featured-brand">{{ product.brand }}</span>
          <div class="featured-price">
            <span class="current-price">{{ formatPrice(product.price) }}</span>
            <span class="original-price" *ngIf="product.originalPrice">
              {{ formatPrice(product.originalPrice) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Trending Products Component -->
  <section class="sidebar-section">
    <div class="section-header">
      <h3><i class="fas fa-fire"></i> Trending Now</h3>
      <button class="see-all-btn" routerLink="/shop?filter=trending">View All</button>
    </div>
    <app-trending-products></app-trending-products>
  </section>

  <!-- New Arrivals -->
  <section class="sidebar-section" *ngIf="newArrivals.length > 0">
    <div class="section-header">
      <h3><i class="fas fa-sparkles"></i> New Arrivals</h3>
      <button class="see-all-btn" (click)="viewNewArrivals()">View All</button>
    </div>
    <div class="arrivals-list">
      <div *ngFor="let product of newArrivals.slice(0, 4); trackBy: trackByProductId" 
           class="arrival-item" (click)="onProductClick(product)">
        <img [src]="product.images?.[0]?.url || '/assets/images/product-placeholder.jpg'" 
             [alt]="product.name" class="arrival-image">
        <div class="new-badge">New</div>
        <div class="arrival-info">
          <span class="arrival-name">{{ product.name }}</span>
          <span class="arrival-brand">{{ product.brand }}</span>
          <span class="arrival-price">{{ formatPrice(product.price) }}</span>
        </div>
      </div>
    </div>
  </section>

  <!-- Top Influencers Component -->
  <section class="sidebar-section">
    <div class="section-header">
      <h3><i class="fas fa-crown"></i> Top Influencers</h3>
      <button class="see-all-btn" routerLink="/influencers">View All</button>
    </div>
    <app-top-influencers></app-top-influencers>
  </section>

  <!-- Categories -->
  <section class="sidebar-section">
    <div class="section-header">
      <h3><i class="fas fa-th-large"></i> Shop by Category</h3>
      <button class="see-all-btn" (click)="viewAllCategories()">View All</button>
    </div>
    <div class="categories-grid">
      <div *ngFor="let category of getDisplayCategories(); trackBy: trackByCategoryId"
           class="category-item" (click)="onCategoryClick(category)">
        <img [src]="category.image" [alt]="category.name" class="category-image">
        <span class="category-name">{{ category.name }}</span>
      </div>
    </div>
  </section>

  <!-- Footer Links -->
  <div class="sidebar-footer">
    <div class="footer-links">
      <a href="#" routerLink="/about">About</a>
      <a href="#" routerLink="/help">Help</a>
      <a href="#" routerLink="/press">Press</a>
      <a href="#" routerLink="/api">API</a>
      <a href="#" routerLink="/jobs">Jobs</a>
      <a href="#" routerLink="/privacy">Privacy</a>
      <a href="#" routerLink="/terms">Terms</a>
    </div>
    <div class="copyright">
      <span>© 2024 DFashion</span>
    </div>
  </div>
</div>
