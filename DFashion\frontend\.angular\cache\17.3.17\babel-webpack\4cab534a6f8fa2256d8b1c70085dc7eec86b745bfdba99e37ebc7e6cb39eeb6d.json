{"ast": null, "code": "import { Breakpoints } from '@angular/cdk/layout';\nimport { Subject } from 'rxjs';\nimport { map, shareReplay, takeUntil } from 'rxjs/operators';\nimport { NavigationEnd } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/cdk/layout\";\nimport * as i2 from \"../services/admin-auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/toolbar\";\nimport * as i6 from \"@angular/material/sidenav\";\nimport * as i7 from \"@angular/material/list\";\nimport * as i8 from \"@angular/material/divider\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/button\";\nimport * as i11 from \"@angular/material/menu\";\nimport * as i12 from \"@angular/material/badge\";\nconst _c0 = [\"drawer\"];\nfunction AdminLayoutComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"span\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 32)(5, \"div\", 33);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 34);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 35);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.getRoleColor());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getUserInitials());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getRoleDisplayName());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 6, ctx_r1.currentUser.department));\n  }\n}\nfunction AdminLayoutComponent_mat_list_item_16_mat_icon_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 40);\n    i0.ɵɵtext(1, \"chevron_right\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminLayoutComponent_mat_list_item_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-list-item\", 36);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_mat_list_item_16_Template_mat_list_item_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMenuItemClick());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, AdminLayoutComponent_mat_list_item_16_mat_icon_5_Template, 2, 0, \"mat-icon\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", item_r4.route);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isActiveRoute(item_r4.route));\n  }\n}\nfunction AdminLayoutComponent_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      i0.ɵɵnextContext();\n      const drawer_r6 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(drawer_r6.toggle());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\", 42);\n    i0.ɵɵtext(2, \"menu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AdminLayoutComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"span\", 45);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 46)(5, \"div\", 47);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 48);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 49);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.getRoleColor());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getUserInitials());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getRoleDisplayName());\n  }\n}\nexport class AdminLayoutComponent {\n  constructor(breakpointObserver, authService, router) {\n    this.breakpointObserver = breakpointObserver;\n    this.authService = authService;\n    this.router = router;\n    this.destroy$ = new Subject();\n    this.currentUser = null;\n    this.pageTitle = 'Dashboard';\n    this.isHandset$ = this.breakpointObserver.observe(Breakpoints.Handset).pipe(map(result => result.matches), shareReplay());\n    this.navigationItems = [{\n      title: 'Dashboard',\n      icon: 'dashboard',\n      route: '/admin/dashboard',\n      permission: 'dashboard:view'\n    }, {\n      title: 'User Management',\n      icon: 'people',\n      route: '/admin/users',\n      permission: 'users:view'\n    }, {\n      title: 'Product Management',\n      icon: 'inventory',\n      route: '/admin/products',\n      permission: 'products:view'\n    }, {\n      title: 'Order Management',\n      icon: 'shopping_cart',\n      route: '/admin/orders',\n      permission: 'orders:view'\n    }, {\n      title: 'Analytics',\n      icon: 'analytics',\n      route: '/admin/analytics',\n      permission: 'analytics:view'\n    }, {\n      title: 'Settings',\n      icon: 'settings',\n      route: '/admin/settings',\n      permission: 'settings:view'\n    }];\n    this.currentUser$ = this.authService.currentUser$;\n  }\n  ngOnInit() {\n    // Subscribe to current user\n    this.currentUser$.pipe(takeUntil(this.destroy$)).subscribe(user => {\n      this.currentUser = user;\n    });\n    // Listen to route changes to update page title\n    this.router.events.pipe(takeUntil(this.destroy$)).subscribe(event => {\n      if (event instanceof NavigationEnd) {\n        this.updatePageTitle();\n      }\n    });\n    // Initial page title update\n    this.updatePageTitle();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  hasPermission(permission) {\n    if (!permission || !this.currentUser) return true;\n    const [module, action] = permission.split(':');\n    return this.authService.hasPermission(module, action);\n  }\n  getVisibleNavigationItems() {\n    return this.navigationItems.filter(item => this.hasPermission(item.permission));\n  }\n  onLogout() {\n    this.authService.logout();\n  }\n  updatePageTitle() {\n    const url = this.router.url;\n    const routeTitleMap = {\n      '/admin/dashboard': 'Dashboard',\n      '/admin/users': 'User Management',\n      '/admin/products': 'Product Management',\n      '/admin/orders': 'Order Management',\n      '/admin/analytics': 'Analytics',\n      '/admin/settings': 'Settings'\n    };\n    this.pageTitle = routeTitleMap[url] || 'Admin Panel';\n  }\n  getUserInitials() {\n    if (!this.currentUser?.fullName) return 'AD';\n    const names = this.currentUser.fullName.split(' ');\n    if (names.length >= 2) {\n      return (names[0][0] + names[1][0]).toUpperCase();\n    }\n    return names[0][0].toUpperCase();\n  }\n  getRoleColor() {\n    if (!this.currentUser?.role) return '#666';\n    const roleColors = {\n      'super_admin': '#e91e63',\n      'admin': '#9c27b0',\n      'sales_manager': '#2196f3',\n      'marketing_manager': '#ff9800',\n      'account_manager': '#4caf50',\n      'support_manager': '#795548'\n    };\n    return roleColors[this.currentUser.role] || '#666';\n  }\n  getRoleDisplayName() {\n    if (!this.currentUser?.role) return '';\n    const roleDisplayNames = {\n      'super_admin': 'Super Admin',\n      'admin': 'Admin',\n      'sales_manager': 'Sales Manager',\n      'sales_executive': 'Sales Executive',\n      'marketing_manager': 'Marketing Manager',\n      'marketing_executive': 'Marketing Executive',\n      'account_manager': 'Account Manager',\n      'accountant': 'Accountant',\n      'support_manager': 'Support Manager',\n      'support_agent': 'Support Agent',\n      'content_manager': 'Content Manager',\n      'vendor_manager': 'Vendor Manager'\n    };\n    return roleDisplayNames[this.currentUser.role] || this.currentUser.role.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n  }\n  isActiveRoute(route) {\n    return this.router.url === route;\n  }\n  onMenuItemClick() {\n    // Close drawer on mobile after navigation\n    this.isHandset$.pipe(takeUntil(this.destroy$)).subscribe(isHandset => {\n      if (isHandset && this.drawer) {\n        this.drawer.close();\n      }\n    });\n  }\n  static {\n    this.ɵfac = function AdminLayoutComponent_Factory(t) {\n      return new (t || AdminLayoutComponent)(i0.ɵɵdirectiveInject(i1.BreakpointObserver), i0.ɵɵdirectiveInject(i2.AdminAuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminLayoutComponent,\n      selectors: [[\"app-admin-layout\"]],\n      viewQuery: function AdminLayoutComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.drawer = _t.first);\n        }\n      },\n      decls: 64,\n      vars: 20,\n      consts: [[\"drawer\", \"\"], [\"userMenu\", \"matMenu\"], [1, \"sidenav-container\"], [\"fixedInViewport\", \"\", 1, \"sidenav\", 3, \"mode\", \"opened\"], [1, \"sidenav-header\"], [1, \"logo\"], [1, \"logo-icon\"], [1, \"logo-text\"], [1, \"admin-badge\"], [\"class\", \"user-profile\", 4, \"ngIf\"], [1, \"nav-list\"], [\"routerLinkActive\", \"active-nav-item\", \"class\", \"nav-item\", 3, \"routerLink\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidenav-footer\"], [\"mat-button\", \"\", 1, \"logout-button\", 3, \"click\"], [1, \"main-content\"], [\"color\", \"primary\", 1, \"toolbar\"], [\"type\", \"button\", \"aria-label\", \"Toggle sidenav\", \"mat-icon-button\", \"\", 3, \"click\", 4, \"ngIf\"], [1, \"page-title\"], [1, \"toolbar-spacer\"], [\"mat-icon-button\", \"\", 1, \"notification-button\"], [\"matBadge\", \"3\", \"matBadgeColor\", \"warn\"], [\"mat-icon-button\", \"\", 1, \"user-menu-button\", 3, \"matMenuTriggerFor\"], [1, \"toolbar-user-avatar\"], [1, \"toolbar-user-initials\"], [1, \"user-menu\"], [\"class\", \"user-menu-header\", 4, \"ngIf\"], [\"mat-menu-item\", \"\"], [\"mat-menu-item\", \"\", 1, \"logout-menu-item\", 3, \"click\"], [1, \"page-content\"], [1, \"user-profile\"], [1, \"user-avatar\"], [1, \"user-initials\"], [1, \"user-info\"], [1, \"user-name\"], [1, \"user-role\"], [1, \"user-department\"], [\"routerLinkActive\", \"active-nav-item\", 1, \"nav-item\", 3, \"click\", \"routerLink\"], [\"matListIcon\", \"\"], [\"matLine\", \"\"], [\"class\", \"nav-arrow\", 4, \"ngIf\"], [1, \"nav-arrow\"], [\"type\", \"button\", \"aria-label\", \"Toggle sidenav\", \"mat-icon-button\", \"\", 3, \"click\"], [\"aria-label\", \"Side nav toggle icon\"], [1, \"user-menu-header\"], [1, \"menu-user-avatar\"], [1, \"menu-user-initials\"], [1, \"menu-user-info\"], [1, \"menu-user-name\"], [1, \"menu-user-email\"], [1, \"menu-user-role\"]],\n      template: function AdminLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"mat-sidenav-container\", 2)(1, \"mat-sidenav\", 3, 0);\n          i0.ɵɵpipe(3, \"async\");\n          i0.ɵɵpipe(4, \"async\");\n          i0.ɵɵpipe(5, \"async\");\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"div\", 5)(8, \"mat-icon\", 6);\n          i0.ɵɵtext(9, \"shopping_bag\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"span\", 7);\n          i0.ɵɵtext(11, \"DFashion\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 8);\n          i0.ɵɵtext(13, \"Admin Panel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(14, AdminLayoutComponent_div_14_Template, 12, 8, \"div\", 9);\n          i0.ɵɵelementStart(15, \"mat-nav-list\", 10);\n          i0.ɵɵtemplate(16, AdminLayoutComponent_mat_list_item_16_Template, 6, 4, \"mat-list-item\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 12)(18, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function AdminLayoutComponent_Template_button_click_18_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onLogout());\n          });\n          i0.ɵɵelementStart(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"logout\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"span\");\n          i0.ɵɵtext(22, \"Logout\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(23, \"mat-sidenav-content\", 14)(24, \"mat-toolbar\", 15);\n          i0.ɵɵtemplate(25, AdminLayoutComponent_button_25_Template, 3, 0, \"button\", 16);\n          i0.ɵɵpipe(26, \"async\");\n          i0.ɵɵelementStart(27, \"span\", 17);\n          i0.ɵɵtext(28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(29, \"span\", 18);\n          i0.ɵɵelementStart(30, \"button\", 19)(31, \"mat-icon\", 20);\n          i0.ɵɵtext(32, \"notifications\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"button\", 21)(34, \"div\", 22)(35, \"span\", 23);\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"mat-menu\", 24, 1);\n          i0.ɵɵtemplate(39, AdminLayoutComponent_div_39_Template, 11, 6, \"div\", 25);\n          i0.ɵɵelement(40, \"mat-divider\");\n          i0.ɵɵelementStart(41, \"button\", 26)(42, \"mat-icon\");\n          i0.ɵɵtext(43, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\");\n          i0.ɵɵtext(45, \"Profile\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"button\", 26)(47, \"mat-icon\");\n          i0.ɵɵtext(48, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"span\");\n          i0.ɵɵtext(50, \"Account Settings\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"button\", 26)(52, \"mat-icon\");\n          i0.ɵɵtext(53, \"help\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"span\");\n          i0.ɵɵtext(55, \"Help & Support\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(56, \"mat-divider\");\n          i0.ɵɵelementStart(57, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function AdminLayoutComponent_Template_button_click_57_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onLogout());\n          });\n          i0.ɵɵelementStart(58, \"mat-icon\");\n          i0.ɵɵtext(59, \"logout\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"span\");\n          i0.ɵɵtext(61, \"Logout\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(62, \"div\", 28);\n          i0.ɵɵelement(63, \"router-outlet\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const userMenu_r7 = i0.ɵɵreference(38);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"mode\", i0.ɵɵpipeBind1(3, 12, ctx.isHandset$) ? \"over\" : \"side\")(\"opened\", i0.ɵɵpipeBind1(4, 14, ctx.isHandset$) === false);\n          i0.ɵɵattribute(\"role\", i0.ɵɵpipeBind1(5, 16, ctx.isHandset$) ? \"dialog\" : \"navigation\");\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getVisibleNavigationItems());\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(26, 18, ctx.isHandset$));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.pageTitle);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r7);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"background-color\", ctx.getRoleColor());\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getUserInitials());\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i3.RouterOutlet, i3.RouterLink, i3.RouterLinkActive, i5.MatToolbar, i6.MatSidenav, i6.MatSidenavContainer, i6.MatSidenavContent, i7.MatNavList, i7.MatListItem, i8.MatDivider, i9.MatIcon, i10.MatButton, i10.MatIconButton, i11.MatMenu, i11.MatMenuItem, i11.MatMenuTrigger, i12.MatBadge, i4.AsyncPipe, i4.TitleCasePipe],\n      styles: [\".sidenav-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n}\\n\\n.sidenav[_ngcontent-%COMP%] {\\n  width: 280px;\\n  background: #1a1a2e;\\n  color: white;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.sidenav-header[_ngcontent-%COMP%] {\\n  padding: 1.5rem 1rem 1rem 1rem;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n.sidenav-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  margin-bottom: 0.5rem;\\n}\\n.sidenav-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   .logo-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  width: 2rem;\\n  height: 2rem;\\n  color: #667eea;\\n}\\n.sidenav-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.sidenav-header[_ngcontent-%COMP%]   .admin-badge[_ngcontent-%COMP%] {\\n  background: rgba(102, 126, 234, 0.2);\\n  color: #667eea;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  display: inline-block;\\n}\\n\\n.user-profile[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n  color: white;\\n}\\n.user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]   .user-initials[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n.user-profile[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.user-profile[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 0.95rem;\\n  margin-bottom: 0.25rem;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.user-profile[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-role[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n.user-profile[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-department[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 0.75rem;\\n}\\n\\n.nav-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding-top: 1rem;\\n}\\n.nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n  margin: 0.25rem 1rem;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  color: rgba(255, 255, 255, 0.8);\\n}\\n.nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: white;\\n}\\n.nav-list[_ngcontent-%COMP%]   .nav-item.active-nav-item[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n}\\n.nav-list[_ngcontent-%COMP%]   .nav-item.active-nav-item[_ngcontent-%COMP%]   .nav-arrow[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: inherit;\\n}\\n.nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-arrow[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n\\n.sidenav-footer[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n.sidenav-footer[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  color: rgba(255, 255, 255, 0.8);\\n  justify-content: flex-start;\\n  gap: 0.75rem;\\n}\\n.sidenav-footer[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  color: white;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100vh;\\n}\\n\\n.toolbar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  z-index: 1000;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.toolbar[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 500;\\n}\\n.toolbar[_ngcontent-%COMP%]   .toolbar-spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n.toolbar[_ngcontent-%COMP%]   .notification-button[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n.toolbar[_ngcontent-%COMP%]   .user-menu-button[_ngcontent-%COMP%]   .toolbar-user-avatar[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-weight: 600;\\n}\\n.toolbar[_ngcontent-%COMP%]   .user-menu-button[_ngcontent-%COMP%]   .toolbar-user-avatar[_ngcontent-%COMP%]   .toolbar-user-initials[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.page-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 1.5rem;\\n  background: #f5f5f5;\\n}\\n\\n  .user-menu .mat-menu-content {\\n  padding: 0;\\n}\\n  .user-menu .user-menu-header {\\n  padding: 1rem;\\n  background: #f8f9fa;\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n  .user-menu .user-menu-header .menu-user-avatar {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-weight: 600;\\n}\\n  .user-menu .user-menu-header .menu-user-avatar .menu-user-initials {\\n  font-size: 1.1rem;\\n}\\n  .user-menu .user-menu-header .menu-user-info .menu-user-name {\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 0.25rem;\\n}\\n  .user-menu .user-menu-header .menu-user-info .menu-user-email {\\n  color: #666;\\n  font-size: 0.85rem;\\n  margin-bottom: 0.25rem;\\n}\\n  .user-menu .user-menu-header .menu-user-info .menu-user-role {\\n  color: #667eea;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n  .user-menu .logout-menu-item {\\n  color: #f44336;\\n}\\n  .user-menu .logout-menu-item mat-icon {\\n  color: #f44336;\\n}\\n\\n@media (max-width: 768px) {\\n  .sidenav[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 280px;\\n  }\\n  .page-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .user-profile[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n}\\n  .mat-list-item {\\n  height: 48px !important;\\n}\\n  .mat-list-item-content {\\n  padding: 0 16px !important;\\n}\\n  .mat-toolbar {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;\\n}\\n  .mat-badge-content {\\n  background: #f44336 !important;\\n  color: white !important;\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.user-menu-button[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.user-menu-button[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWRtaW4vbGF5b3V0L2FkbWluLWxheW91dC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7QUFDRjs7QUFFQTtFQUNFLFlBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7QUFDRjs7QUFFQTtFQUNFLDhCQUFBO0VBQ0EsaURBQUE7QUFDRjtBQUNFO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLHFCQUFBO0FBQ0o7QUFDSTtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGNBQUE7QUFDTjtBQUVJO0VBQ0UsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLDZEQUFBO0VBQ0EsNkJBQUE7RUFDQSxvQ0FBQTtFQUNBLHFCQUFBO0FBQU47QUFJRTtFQUNFLG9DQUFBO0VBQ0EsY0FBQTtFQUNBLHdCQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EscUJBQUE7QUFGSjs7QUFNQTtFQUNFLGFBQUE7RUFDQSxpREFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7QUFIRjtBQUtFO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxZQUFBO0FBSEo7QUFLSTtFQUNFLGlCQUFBO0FBSE47QUFPRTtFQUNFLE9BQUE7RUFDQSxZQUFBO0FBTEo7QUFPSTtFQUNFLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSx1QkFBQTtBQUxOO0FBUUk7RUFDRSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtBQU5OO0FBU0k7RUFDRSwrQkFBQTtFQUNBLGtCQUFBO0FBUE47O0FBWUE7RUFDRSxPQUFBO0VBQ0EsaUJBQUE7QUFURjtBQVdFO0VBQ0Usb0JBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EsK0JBQUE7QUFUSjtBQVdJO0VBQ0Usb0NBQUE7RUFDQSxZQUFBO0FBVE47QUFZSTtFQUNFLDZEQUFBO0VBQ0EsWUFBQTtBQVZOO0FBWU07RUFDRSxZQUFBO0FBVlI7QUFjSTtFQUNFLGNBQUE7QUFaTjtBQWVJO0VBQ0UsaUJBQUE7QUFiTjs7QUFrQkE7RUFDRSxhQUFBO0VBQ0EsOENBQUE7QUFmRjtBQWlCRTtFQUNFLFdBQUE7RUFDQSwrQkFBQTtFQUNBLDJCQUFBO0VBQ0EsWUFBQTtBQWZKO0FBaUJJO0VBQ0Usb0NBQUE7RUFDQSxZQUFBO0FBZk47O0FBb0JBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsYUFBQTtBQWpCRjs7QUFvQkE7RUFDRSxnQkFBQTtFQUNBLE1BQUE7RUFDQSxhQUFBO0VBQ0Esd0NBQUE7QUFqQkY7QUFtQkU7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0FBakJKO0FBb0JFO0VBQ0UsY0FBQTtBQWxCSjtBQXFCRTtFQUNFLG9CQUFBO0FBbkJKO0FBdUJJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0FBckJOO0FBdUJNO0VBQ0UsaUJBQUE7QUFyQlI7O0FBMkJBO0VBQ0UsT0FBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLG1CQUFBO0FBeEJGOztBQTZCRTtFQUNFLFVBQUE7QUExQko7QUE2QkU7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0FBM0JKO0FBNkJJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0FBM0JOO0FBNkJNO0VBQ0UsaUJBQUE7QUEzQlI7QUFnQ007RUFDRSxnQkFBQTtFQUNBLFdBQUE7RUFDQSxzQkFBQTtBQTlCUjtBQWlDTTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLHNCQUFBO0FBL0JSO0FBa0NNO0VBQ0UsY0FBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7QUFoQ1I7QUFxQ0U7RUFDRSxjQUFBO0FBbkNKO0FBcUNJO0VBQ0UsY0FBQTtBQW5DTjs7QUF5Q0E7RUFDRTtJQUNFLFdBQUE7SUFDQSxnQkFBQTtFQXRDRjtFQXlDQTtJQUNFLGFBQUE7RUF2Q0Y7RUE0Q0k7SUFDRSxpQkFBQTtFQTFDTjtBQUNGO0FBaURFO0VBQ0UsdUJBQUE7QUEvQ0o7QUFrREU7RUFDRSwwQkFBQTtBQWhESjtBQW1ERTtFQUNFLHdFQUFBO0FBakRKO0FBb0RFO0VBQ0UsOEJBQUE7RUFDQSx1QkFBQTtBQWxESjs7QUF1REE7RUFDRSxpREFBQTtBQXBERjs7QUF1REE7RUFDRSwrQkFBQTtBQXBERjtBQXNERTtFQUNFLHNCQUFBO0FBcERKIiwic291cmNlc0NvbnRlbnQiOlsiLnNpZGVuYXYtY29udGFpbmVyIHtcbiAgaGVpZ2h0OiAxMDB2aDtcbn1cblxuLnNpZGVuYXYge1xuICB3aWR0aDogMjgwcHg7XG4gIGJhY2tncm91bmQ6ICMxYTFhMmU7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbn1cblxuLnNpZGVuYXYtaGVhZGVyIHtcbiAgcGFkZGluZzogMS41cmVtIDFyZW0gMXJlbSAxcmVtO1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuXG4gIC5sb2dvIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgZ2FwOiAwLjc1cmVtO1xuICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcblxuICAgIC5sb2dvLWljb24ge1xuICAgICAgZm9udC1zaXplOiAycmVtO1xuICAgICAgd2lkdGg6IDJyZW07XG4gICAgICBoZWlnaHQ6IDJyZW07XG4gICAgICBjb2xvcjogIzY2N2VlYTtcbiAgICB9XG5cbiAgICAubG9nby10ZXh0IHtcbiAgICAgIGZvbnQtc2l6ZTogMS41cmVtO1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7XG4gICAgICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcbiAgICAgIC13ZWJraXQtdGV4dC1maWxsLWNvbG9yOiB0cmFuc3BhcmVudDtcbiAgICAgIGJhY2tncm91bmQtY2xpcDogdGV4dDtcbiAgICB9XG4gIH1cblxuICAuYWRtaW4tYmFkZ2Uge1xuICAgIGJhY2tncm91bmQ6IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4yKTtcbiAgICBjb2xvcjogIzY2N2VlYTtcbiAgICBwYWRkaW5nOiAwLjI1cmVtIDAuNzVyZW07XG4gICAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgICBmb250LXNpemU6IDAuNzVyZW07XG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XG4gIH1cbn1cblxuLnVzZXItcHJvZmlsZSB7XG4gIHBhZGRpbmc6IDFyZW07XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMXJlbTtcblxuICAudXNlci1hdmF0YXIge1xuICAgIHdpZHRoOiA0OHB4O1xuICAgIGhlaWdodDogNDhweDtcbiAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgY29sb3I6IHdoaXRlO1xuXG4gICAgLnVzZXItaW5pdGlhbHMge1xuICAgICAgZm9udC1zaXplOiAxLjFyZW07XG4gICAgfVxuICB9XG5cbiAgLnVzZXItaW5mbyB7XG4gICAgZmxleDogMTtcbiAgICBtaW4td2lkdGg6IDA7XG5cbiAgICAudXNlci1uYW1lIHtcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICBmb250LXNpemU6IDAuOTVyZW07XG4gICAgICBtYXJnaW4tYm90dG9tOiAwLjI1cmVtO1xuICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpcztcbiAgICB9XG5cbiAgICAudXNlci1yb2xlIHtcbiAgICAgIGNvbG9yOiAjNjY3ZWVhO1xuICAgICAgZm9udC1zaXplOiAwLjhyZW07XG4gICAgICBmb250LXdlaWdodDogNTAwO1xuICAgIH1cblxuICAgIC51c2VyLWRlcGFydG1lbnQge1xuICAgICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC43KTtcbiAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcbiAgICB9XG4gIH1cbn1cblxuLm5hdi1saXN0IHtcbiAgZmxleDogMTtcbiAgcGFkZGluZy10b3A6IDFyZW07XG5cbiAgLm5hdi1pdGVtIHtcbiAgICBtYXJnaW46IDAuMjVyZW0gMXJlbTtcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcbiAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgfVxuXG4gICAgJi5hY3RpdmUtbmF2LWl0ZW0ge1xuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcblxuICAgICAgLm5hdi1hcnJvdyB7XG4gICAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBtYXQtaWNvbiB7XG4gICAgICBjb2xvcjogaW5oZXJpdDtcbiAgICB9XG5cbiAgICAubmF2LWFycm93IHtcbiAgICAgIG1hcmdpbi1sZWZ0OiBhdXRvO1xuICAgIH1cbiAgfVxufVxuXG4uc2lkZW5hdi1mb290ZXIge1xuICBwYWRkaW5nOiAxcmVtO1xuICBib3JkZXItdG9wOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuXG4gIC5sb2dvdXQtYnV0dG9uIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpO1xuICAgIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDtcbiAgICBnYXA6IDAuNzVyZW07XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICB9XG4gIH1cbn1cblxuLm1haW4tY29udGVudCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGhlaWdodDogMTAwdmg7XG59XG5cbi50b29sYmFyIHtcbiAgcG9zaXRpb246IHN0aWNreTtcbiAgdG9wOiAwO1xuICB6LWluZGV4OiAxMDAwO1xuICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuXG4gIC5wYWdlLXRpdGxlIHtcbiAgICBmb250LXNpemU6IDEuMjVyZW07XG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgfVxuXG4gIC50b29sYmFyLXNwYWNlciB7XG4gICAgZmxleDogMSAxIGF1dG87XG4gIH1cblxuICAubm90aWZpY2F0aW9uLWJ1dHRvbiB7XG4gICAgbWFyZ2luLXJpZ2h0OiAwLjVyZW07XG4gIH1cblxuICAudXNlci1tZW51LWJ1dHRvbiB7XG4gICAgLnRvb2xiYXItdXNlci1hdmF0YXIge1xuICAgICAgd2lkdGg6IDM2cHg7XG4gICAgICBoZWlnaHQ6IDM2cHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcblxuICAgICAgLnRvb2xiYXItdXNlci1pbml0aWFscyB7XG4gICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4ucGFnZS1jb250ZW50IHtcbiAgZmxleDogMTtcbiAgb3ZlcmZsb3cteTogYXV0bztcbiAgcGFkZGluZzogMS41cmVtO1xuICBiYWNrZ3JvdW5kOiAjZjVmNWY1O1xufVxuXG4vLyBVc2VyIE1lbnUgU3R5bGVzXG46Om5nLWRlZXAgLnVzZXItbWVudSB7XG4gIC5tYXQtbWVudS1jb250ZW50IHtcbiAgICBwYWRkaW5nOiAwO1xuICB9XG5cbiAgLnVzZXItbWVudS1oZWFkZXIge1xuICAgIHBhZGRpbmc6IDFyZW07XG4gICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgZ2FwOiAxcmVtO1xuXG4gICAgLm1lbnUtdXNlci1hdmF0YXIge1xuICAgICAgd2lkdGg6IDQ4cHg7XG4gICAgICBoZWlnaHQ6IDQ4cHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcblxuICAgICAgLm1lbnUtdXNlci1pbml0aWFscyB7XG4gICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xuICAgICAgfVxuICAgIH1cblxuICAgIC5tZW51LXVzZXItaW5mbyB7XG4gICAgICAubWVudS11c2VyLW5hbWUge1xuICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICBjb2xvcjogIzMzMztcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMC4yNXJlbTtcbiAgICAgIH1cblxuICAgICAgLm1lbnUtdXNlci1lbWFpbCB7XG4gICAgICAgIGNvbG9yOiAjNjY2O1xuICAgICAgICBmb250LXNpemU6IDAuODVyZW07XG4gICAgICAgIG1hcmdpbi1ib3R0b206IDAuMjVyZW07XG4gICAgICB9XG5cbiAgICAgIC5tZW51LXVzZXItcm9sZSB7XG4gICAgICAgIGNvbG9yOiAjNjY3ZWVhO1xuICAgICAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAubG9nb3V0LW1lbnUtaXRlbSB7XG4gICAgY29sb3I6ICNmNDQzMzY7XG5cbiAgICBtYXQtaWNvbiB7XG4gICAgICBjb2xvcjogI2Y0NDMzNjtcbiAgICB9XG4gIH1cbn1cblxuLy8gUmVzcG9uc2l2ZSBEZXNpZ25cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAuc2lkZW5hdiB7XG4gICAgd2lkdGg6IDEwMCU7XG4gICAgbWF4LXdpZHRoOiAyODBweDtcbiAgfVxuXG4gIC5wYWdlLWNvbnRlbnQge1xuICAgIHBhZGRpbmc6IDFyZW07XG4gIH1cblxuICAudXNlci1wcm9maWxlIHtcbiAgICAudXNlci1pbmZvIHtcbiAgICAgIC51c2VyLW5hbWUge1xuICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLy8gTWF0ZXJpYWwgT3ZlcnJpZGVzXG46Om5nLWRlZXAge1xuICAubWF0LWxpc3QtaXRlbSB7XG4gICAgaGVpZ2h0OiA0OHB4ICFpbXBvcnRhbnQ7XG4gIH1cblxuICAubWF0LWxpc3QtaXRlbS1jb250ZW50IHtcbiAgICBwYWRkaW5nOiAwIDE2cHggIWltcG9ydGFudDtcbiAgfVxuXG4gIC5tYXQtdG9vbGJhciB7XG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKSAhaW1wb3J0YW50O1xuICB9XG5cbiAgLm1hdC1iYWRnZS1jb250ZW50IHtcbiAgICBiYWNrZ3JvdW5kOiAjZjQ0MzM2ICFpbXBvcnRhbnQ7XG4gICAgY29sb3I6IHdoaXRlICFpbXBvcnRhbnQ7XG4gIH1cbn1cblxuLy8gQW5pbWF0aW9uc1xuLm5hdi1pdGVtIHtcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgY3ViaWMtYmV6aWVyKDAuNCwgMC4wLCAwLjIsIDEpO1xufVxuXG4udXNlci1tZW51LWJ1dHRvbiB7XG4gIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjJzIGVhc2U7XG5cbiAgJjpob3ZlciB7XG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTtcbiAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Breakpoints", "Subject", "map", "shareReplay", "takeUntil", "NavigationEnd", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "ctx_r1", "getRoleColor", "ɵɵtextInterpolate", "getUserInitials", "currentUser", "fullName", "getRoleDisplayName", "ɵɵpipeBind1", "department", "ɵɵlistener", "AdminLayoutComponent_mat_list_item_16_Template_mat_list_item_click_0_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "onMenuItemClick", "ɵɵtemplate", "AdminLayoutComponent_mat_list_item_16_mat_icon_5_Template", "ɵɵproperty", "item_r4", "route", "icon", "title", "isActiveRoute", "AdminLayoutComponent_button_25_Template_button_click_0_listener", "_r5", "drawer_r6", "ɵɵreference", "toggle", "email", "AdminLayoutComponent", "constructor", "breakpointObserver", "authService", "router", "destroy$", "pageTitle", "isHandset$", "observe", "Handset", "pipe", "result", "matches", "navigationItems", "permission", "currentUser$", "ngOnInit", "subscribe", "user", "events", "event", "updatePageTitle", "ngOnDestroy", "next", "complete", "hasPermission", "module", "action", "split", "getVisibleNavigationItems", "filter", "item", "onLogout", "logout", "url", "routeTitleMap", "names", "length", "toUpperCase", "role", "roleColors", "roleDisplayNames", "replace", "l", "isHandset", "drawer", "close", "ɵɵdirectiveInject", "i1", "BreakpointObserver", "i2", "AdminAuthService", "i3", "Router", "selectors", "viewQuery", "AdminLayoutComponent_Query", "rf", "ctx", "AdminLayoutComponent_div_14_Template", "AdminLayoutComponent_mat_list_item_16_Template", "AdminLayoutComponent_Template_button_click_18_listener", "_r1", "AdminLayoutComponent_button_25_Template", "ɵɵelement", "AdminLayoutComponent_div_39_Template", "AdminLayoutComponent_Template_button_click_57_listener", "userMenu_r7"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\admin\\layout\\admin-layout.component.ts", "E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\admin\\layout\\admin-layout.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';\nimport { MatSidenav } from '@angular/material/sidenav';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { Observable, Subject } from 'rxjs';\nimport { map, shareReplay, takeUntil } from 'rxjs/operators';\nimport { AdminAuthService, AdminUser } from '../services/admin-auth.service';\nimport { Router, NavigationEnd } from '@angular/router';\n\n@Component({\n  selector: 'app-admin-layout',\n  templateUrl: './admin-layout.component.html',\n  styleUrls: ['./admin-layout.component.scss']\n})\nexport class AdminLayoutComponent implements OnInit, OnDestroy {\n  @ViewChild('drawer') drawer!: MatSidenav;\n  \n  private destroy$ = new Subject<void>();\n  currentUser$: Observable<AdminUser | null>;\n  currentUser: AdminUser | null = null;\n  pageTitle = 'Dashboard';\n\n  isHandset$: Observable<boolean> = this.breakpointObserver.observe(Breakpoints.Handset)\n    .pipe(\n      map(result => result.matches),\n      shareReplay()\n    );\n\n  navigationItems = [\n    {\n      title: 'Dashboard',\n      icon: 'dashboard',\n      route: '/admin/dashboard',\n      permission: 'dashboard:view'\n    },\n    {\n      title: 'User Management',\n      icon: 'people',\n      route: '/admin/users',\n      permission: 'users:view'\n    },\n    {\n      title: 'Product Management',\n      icon: 'inventory',\n      route: '/admin/products',\n      permission: 'products:view'\n    },\n    {\n      title: 'Order Management',\n      icon: 'shopping_cart',\n      route: '/admin/orders',\n      permission: 'orders:view'\n    },\n    {\n      title: 'Analytics',\n      icon: 'analytics',\n      route: '/admin/analytics',\n      permission: 'analytics:view'\n    },\n    {\n      title: 'Settings',\n      icon: 'settings',\n      route: '/admin/settings',\n      permission: 'settings:view'\n    }\n  ];\n\n  constructor(\n    private breakpointObserver: BreakpointObserver,\n    public authService: AdminAuthService,\n    private router: Router\n  ) {\n    this.currentUser$ = this.authService.currentUser$;\n  }\n\n  ngOnInit(): void {\n    // Subscribe to current user\n    this.currentUser$.pipe(\n      takeUntil(this.destroy$)\n    ).subscribe(user => {\n      this.currentUser = user;\n    });\n\n    // Listen to route changes to update page title\n    this.router.events.pipe(\n      takeUntil(this.destroy$)\n    ).subscribe(event => {\n      if (event instanceof NavigationEnd) {\n        this.updatePageTitle();\n      }\n    });\n\n    // Initial page title update\n    this.updatePageTitle();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  hasPermission(permission: string): boolean {\n    if (!permission || !this.currentUser) return true;\n    \n    const [module, action] = permission.split(':');\n    return this.authService.hasPermission(module, action);\n  }\n\n  getVisibleNavigationItems() {\n    return this.navigationItems.filter(item => this.hasPermission(item.permission));\n  }\n\n  onLogout(): void {\n    this.authService.logout();\n  }\n\n  private updatePageTitle(): void {\n    const url = this.router.url;\n    const routeTitleMap: { [key: string]: string } = {\n      '/admin/dashboard': 'Dashboard',\n      '/admin/users': 'User Management',\n      '/admin/products': 'Product Management',\n      '/admin/orders': 'Order Management',\n      '/admin/analytics': 'Analytics',\n      '/admin/settings': 'Settings'\n    };\n\n    this.pageTitle = routeTitleMap[url] || 'Admin Panel';\n  }\n\n  getUserInitials(): string {\n    if (!this.currentUser?.fullName) return 'AD';\n    \n    const names = this.currentUser.fullName.split(' ');\n    if (names.length >= 2) {\n      return (names[0][0] + names[1][0]).toUpperCase();\n    }\n    return names[0][0].toUpperCase();\n  }\n\n  getRoleColor(): string {\n    if (!this.currentUser?.role) return '#666';\n\n    const roleColors: { [key: string]: string } = {\n      'super_admin': '#e91e63',\n      'admin': '#9c27b0',\n      'sales_manager': '#2196f3',\n      'marketing_manager': '#ff9800',\n      'account_manager': '#4caf50',\n      'support_manager': '#795548'\n    };\n\n    return roleColors[this.currentUser.role] || '#666';\n  }\n\n  getRoleDisplayName(): string {\n    if (!this.currentUser?.role) return '';\n\n    const roleDisplayNames: { [key: string]: string } = {\n      'super_admin': 'Super Admin',\n      'admin': 'Admin',\n      'sales_manager': 'Sales Manager',\n      'sales_executive': 'Sales Executive',\n      'marketing_manager': 'Marketing Manager',\n      'marketing_executive': 'Marketing Executive',\n      'account_manager': 'Account Manager',\n      'accountant': 'Accountant',\n      'support_manager': 'Support Manager',\n      'support_agent': 'Support Agent',\n      'content_manager': 'Content Manager',\n      'vendor_manager': 'Vendor Manager'\n    };\n\n    return roleDisplayNames[this.currentUser.role] || this.currentUser.role.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n  }\n\n  isActiveRoute(route: string): boolean {\n    return this.router.url === route;\n  }\n\n  onMenuItemClick(): void {\n    // Close drawer on mobile after navigation\n    this.isHandset$.pipe(takeUntil(this.destroy$)).subscribe(isHandset => {\n      if (isHandset && this.drawer) {\n        this.drawer.close();\n      }\n    });\n  }\n}\n", "<mat-sidenav-container class=\"sidenav-container\">\n  <!-- Sidebar -->\n  <mat-sidenav #drawer \n               class=\"sidenav\" \n               fixedInViewport\n               [attr.role]=\"(isHandset$ | async) ? 'dialog' : 'navigation'\"\n               [mode]=\"(isHandset$ | async) ? 'over' : 'side'\"\n               [opened]=\"(isHandset$ | async) === false\">\n    \n    <!-- Sidebar Header -->\n    <div class=\"sidenav-header\">\n      <div class=\"logo\">\n        <mat-icon class=\"logo-icon\">shopping_bag</mat-icon>\n        <span class=\"logo-text\">DFashion</span>\n      </div>\n      <div class=\"admin-badge\">Admin Panel</div>\n    </div>\n\n    <!-- User Profile Section -->\n    <div class=\"user-profile\" *ngIf=\"currentUser\">\n      <div class=\"user-avatar\" [style.background-color]=\"getRoleColor()\">\n        <span class=\"user-initials\">{{ getUserInitials() }}</span>\n      </div>\n      <div class=\"user-info\">\n        <div class=\"user-name\">{{ currentUser.fullName }}</div>\n        <div class=\"user-role\">{{ getRoleDisplayName() }}</div>\n        <div class=\"user-department\">{{ currentUser.department | titlecase }}</div>\n      </div>\n    </div>\n\n    <!-- Navigation Menu -->\n    <mat-nav-list class=\"nav-list\">\n      <mat-list-item \n        *ngFor=\"let item of getVisibleNavigationItems()\"\n        [routerLink]=\"item.route\"\n        routerLinkActive=\"active-nav-item\"\n        class=\"nav-item\"\n        (click)=\"onMenuItemClick()\">\n        <mat-icon matListIcon>{{ item.icon }}</mat-icon>\n        <span matLine>{{ item.title }}</span>\n        <mat-icon class=\"nav-arrow\" *ngIf=\"isActiveRoute(item.route)\">chevron_right</mat-icon>\n      </mat-list-item>\n    </mat-nav-list>\n\n    <!-- Sidebar Footer -->\n    <div class=\"sidenav-footer\">\n      <button mat-button class=\"logout-button\" (click)=\"onLogout()\">\n        <mat-icon>logout</mat-icon>\n        <span>Logout</span>\n      </button>\n    </div>\n  </mat-sidenav>\n\n  <!-- Main Content -->\n  <mat-sidenav-content class=\"main-content\">\n    <!-- Top Toolbar -->\n    <mat-toolbar class=\"toolbar\" color=\"primary\">\n      <!-- Menu Button (Mobile) -->\n      <button\n        type=\"button\"\n        aria-label=\"Toggle sidenav\"\n        mat-icon-button\n        (click)=\"drawer.toggle()\"\n        *ngIf=\"isHandset$ | async\">\n        <mat-icon aria-label=\"Side nav toggle icon\">menu</mat-icon>\n      </button>\n\n      <!-- Page Title -->\n      <span class=\"page-title\">{{ pageTitle }}</span>\n\n      <!-- Spacer -->\n      <span class=\"toolbar-spacer\"></span>\n\n      <!-- Notifications -->\n      <button mat-icon-button class=\"notification-button\">\n        <mat-icon matBadge=\"3\" matBadgeColor=\"warn\">notifications</mat-icon>\n      </button>\n\n      <!-- User Menu -->\n      <button mat-icon-button [matMenuTriggerFor]=\"userMenu\" class=\"user-menu-button\">\n        <div class=\"toolbar-user-avatar\" [style.background-color]=\"getRoleColor()\">\n          <span class=\"toolbar-user-initials\">{{ getUserInitials() }}</span>\n        </div>\n      </button>\n\n      <!-- User Menu Dropdown -->\n      <mat-menu #userMenu=\"matMenu\" class=\"user-menu\">\n        <div class=\"user-menu-header\" *ngIf=\"currentUser\">\n          <div class=\"menu-user-avatar\" [style.background-color]=\"getRoleColor()\">\n            <span class=\"menu-user-initials\">{{ getUserInitials() }}</span>\n          </div>\n          <div class=\"menu-user-info\">\n            <div class=\"menu-user-name\">{{ currentUser.fullName }}</div>\n            <div class=\"menu-user-email\">{{ currentUser.email }}</div>\n            <div class=\"menu-user-role\">{{ getRoleDisplayName() }}</div>\n          </div>\n        </div>\n        <mat-divider></mat-divider>\n        <button mat-menu-item>\n          <mat-icon>person</mat-icon>\n          <span>Profile</span>\n        </button>\n        <button mat-menu-item>\n          <mat-icon>settings</mat-icon>\n          <span>Account Settings</span>\n        </button>\n        <button mat-menu-item>\n          <mat-icon>help</mat-icon>\n          <span>Help & Support</span>\n        </button>\n        <mat-divider></mat-divider>\n        <button mat-menu-item (click)=\"onLogout()\" class=\"logout-menu-item\">\n          <mat-icon>logout</mat-icon>\n          <span>Logout</span>\n        </button>\n      </mat-menu>\n    </mat-toolbar>\n\n    <!-- Page Content -->\n    <div class=\"page-content\">\n      <router-outlet></router-outlet>\n    </div>\n  </mat-sidenav-content>\n</mat-sidenav-container>\n"], "mappings": "AAEA,SAA6BA,WAAW,QAAQ,qBAAqB;AACrE,SAAqBC,OAAO,QAAQ,MAAM;AAC1C,SAASC,GAAG,EAAEC,WAAW,EAAEC,SAAS,QAAQ,gBAAgB;AAE5D,SAAiBC,aAAa,QAAQ,iBAAiB;;;;;;;;;;;;;;;;;ICe/CC,EAFJ,CAAAC,cAAA,cAA8C,cACuB,eACrC;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IACrDF,EADqD,CAAAG,YAAA,EAAO,EACtD;IAEJH,EADF,CAAAC,cAAA,cAAuB,cACE;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,cAA6B;IAAAD,EAAA,CAAAE,MAAA,IAAwC;;IAEzEF,EAFyE,CAAAG,YAAA,EAAM,EACvE,EACF;;;;IARqBH,EAAA,CAAAI,SAAA,EAAyC;IAAzCJ,EAAA,CAAAK,WAAA,qBAAAC,MAAA,CAAAC,YAAA,GAAyC;IACpCP,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAQ,iBAAA,CAAAF,MAAA,CAAAG,eAAA,GAAuB;IAG5BT,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAQ,iBAAA,CAAAF,MAAA,CAAAI,WAAA,CAAAC,QAAA,CAA0B;IAC1BX,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAQ,iBAAA,CAAAF,MAAA,CAAAM,kBAAA,GAA0B;IACpBZ,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAa,WAAA,QAAAP,MAAA,CAAAI,WAAA,CAAAI,UAAA,EAAwC;;;;;IAcrEd,EAAA,CAAAC,cAAA,mBAA8D;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;IARxFH,EAAA,CAAAC,cAAA,wBAK8B;IAA5BD,EAAA,CAAAe,UAAA,mBAAAC,8EAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASd,MAAA,CAAAe,eAAA,EAAiB;IAAA,EAAC;IAC3BrB,EAAA,CAAAC,cAAA,mBAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChDH,EAAA,CAAAC,cAAA,eAAc;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAsB,UAAA,IAAAC,yDAAA,uBAA8D;IAChEvB,EAAA,CAAAG,YAAA,EAAgB;;;;;IAPdH,EAAA,CAAAwB,UAAA,eAAAC,OAAA,CAAAC,KAAA,CAAyB;IAIH1B,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAQ,iBAAA,CAAAiB,OAAA,CAAAE,IAAA,CAAe;IACvB3B,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAQ,iBAAA,CAAAiB,OAAA,CAAAG,KAAA,CAAgB;IACD5B,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAuB,aAAA,CAAAJ,OAAA,CAAAC,KAAA,EAA+B;;;;;;IAkB9D1B,EAAA,CAAAC,cAAA,iBAK6B;IAD3BD,EAAA,CAAAe,UAAA,mBAAAe,gEAAA;MAAA9B,EAAA,CAAAiB,aAAA,CAAAc,GAAA;MAAA/B,EAAA,CAAAmB,aAAA;MAAA,MAAAa,SAAA,GAAAhC,EAAA,CAAAiC,WAAA;MAAA,OAAAjC,EAAA,CAAAoB,WAAA,CAASY,SAAA,CAAAE,MAAA,EAAe;IAAA,EAAC;IAEzBlC,EAAA,CAAAC,cAAA,mBAA4C;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAClDF,EADkD,CAAAG,YAAA,EAAW,EACpD;;;;;IAwBHH,EAFJ,CAAAC,cAAA,cAAkD,cACwB,eACrC;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAEJH,EADF,CAAAC,cAAA,cAA4B,cACE;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5DH,EAAA,CAAAC,cAAA,cAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1DH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAE1DF,EAF0D,CAAAG,YAAA,EAAM,EACxD,EACF;;;;IAR0BH,EAAA,CAAAI,SAAA,EAAyC;IAAzCJ,EAAA,CAAAK,WAAA,qBAAAC,MAAA,CAAAC,YAAA,GAAyC;IACpCP,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAQ,iBAAA,CAAAF,MAAA,CAAAG,eAAA,GAAuB;IAG5BT,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAQ,iBAAA,CAAAF,MAAA,CAAAI,WAAA,CAAAC,QAAA,CAA0B;IACzBX,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAQ,iBAAA,CAAAF,MAAA,CAAAI,WAAA,CAAAyB,KAAA,CAAuB;IACxBnC,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAQ,iBAAA,CAAAF,MAAA,CAAAM,kBAAA,GAA0B;;;ADjFlE,OAAM,MAAOwB,oBAAoB;EAqD/BC,YACUC,kBAAsC,EACvCC,WAA6B,EAC5BC,MAAc;IAFd,KAAAF,kBAAkB,GAAlBA,kBAAkB;IACnB,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,MAAM,GAANA,MAAM;IArDR,KAAAC,QAAQ,GAAG,IAAI9C,OAAO,EAAQ;IAEtC,KAAAe,WAAW,GAAqB,IAAI;IACpC,KAAAgC,SAAS,GAAG,WAAW;IAEvB,KAAAC,UAAU,GAAwB,IAAI,CAACL,kBAAkB,CAACM,OAAO,CAAClD,WAAW,CAACmD,OAAO,CAAC,CACnFC,IAAI,CACHlD,GAAG,CAACmD,MAAM,IAAIA,MAAM,CAACC,OAAO,CAAC,EAC7BnD,WAAW,EAAE,CACd;IAEH,KAAAoD,eAAe,GAAG,CAChB;MACErB,KAAK,EAAE,WAAW;MAClBD,IAAI,EAAE,WAAW;MACjBD,KAAK,EAAE,kBAAkB;MACzBwB,UAAU,EAAE;KACb,EACD;MACEtB,KAAK,EAAE,iBAAiB;MACxBD,IAAI,EAAE,QAAQ;MACdD,KAAK,EAAE,cAAc;MACrBwB,UAAU,EAAE;KACb,EACD;MACEtB,KAAK,EAAE,oBAAoB;MAC3BD,IAAI,EAAE,WAAW;MACjBD,KAAK,EAAE,iBAAiB;MACxBwB,UAAU,EAAE;KACb,EACD;MACEtB,KAAK,EAAE,kBAAkB;MACzBD,IAAI,EAAE,eAAe;MACrBD,KAAK,EAAE,eAAe;MACtBwB,UAAU,EAAE;KACb,EACD;MACEtB,KAAK,EAAE,WAAW;MAClBD,IAAI,EAAE,WAAW;MACjBD,KAAK,EAAE,kBAAkB;MACzBwB,UAAU,EAAE;KACb,EACD;MACEtB,KAAK,EAAE,UAAU;MACjBD,IAAI,EAAE,UAAU;MAChBD,KAAK,EAAE,iBAAiB;MACxBwB,UAAU,EAAE;KACb,CACF;IAOC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACZ,WAAW,CAACY,YAAY;EACnD;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,YAAY,CAACL,IAAI,CACpBhD,SAAS,CAAC,IAAI,CAAC2C,QAAQ,CAAC,CACzB,CAACY,SAAS,CAACC,IAAI,IAAG;MACjB,IAAI,CAAC5C,WAAW,GAAG4C,IAAI;IACzB,CAAC,CAAC;IAEF;IACA,IAAI,CAACd,MAAM,CAACe,MAAM,CAACT,IAAI,CACrBhD,SAAS,CAAC,IAAI,CAAC2C,QAAQ,CAAC,CACzB,CAACY,SAAS,CAACG,KAAK,IAAG;MAClB,IAAIA,KAAK,YAAYzD,aAAa,EAAE;QAClC,IAAI,CAAC0D,eAAe,EAAE;;IAE1B,CAAC,CAAC;IAEF;IACA,IAAI,CAACA,eAAe,EAAE;EACxB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACjB,QAAQ,CAACkB,IAAI,EAAE;IACpB,IAAI,CAAClB,QAAQ,CAACmB,QAAQ,EAAE;EAC1B;EAEAC,aAAaA,CAACX,UAAkB;IAC9B,IAAI,CAACA,UAAU,IAAI,CAAC,IAAI,CAACxC,WAAW,EAAE,OAAO,IAAI;IAEjD,MAAM,CAACoD,MAAM,EAAEC,MAAM,CAAC,GAAGb,UAAU,CAACc,KAAK,CAAC,GAAG,CAAC;IAC9C,OAAO,IAAI,CAACzB,WAAW,CAACsB,aAAa,CAACC,MAAM,EAAEC,MAAM,CAAC;EACvD;EAEAE,yBAAyBA,CAAA;IACvB,OAAO,IAAI,CAAChB,eAAe,CAACiB,MAAM,CAACC,IAAI,IAAI,IAAI,CAACN,aAAa,CAACM,IAAI,CAACjB,UAAU,CAAC,CAAC;EACjF;EAEAkB,QAAQA,CAAA;IACN,IAAI,CAAC7B,WAAW,CAAC8B,MAAM,EAAE;EAC3B;EAEQZ,eAAeA,CAAA;IACrB,MAAMa,GAAG,GAAG,IAAI,CAAC9B,MAAM,CAAC8B,GAAG;IAC3B,MAAMC,aAAa,GAA8B;MAC/C,kBAAkB,EAAE,WAAW;MAC/B,cAAc,EAAE,iBAAiB;MACjC,iBAAiB,EAAE,oBAAoB;MACvC,eAAe,EAAE,kBAAkB;MACnC,kBAAkB,EAAE,WAAW;MAC/B,iBAAiB,EAAE;KACpB;IAED,IAAI,CAAC7B,SAAS,GAAG6B,aAAa,CAACD,GAAG,CAAC,IAAI,aAAa;EACtD;EAEA7D,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACC,WAAW,EAAEC,QAAQ,EAAE,OAAO,IAAI;IAE5C,MAAM6D,KAAK,GAAG,IAAI,CAAC9D,WAAW,CAACC,QAAQ,CAACqD,KAAK,CAAC,GAAG,CAAC;IAClD,IAAIQ,KAAK,CAACC,MAAM,IAAI,CAAC,EAAE;MACrB,OAAO,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEE,WAAW,EAAE;;IAElD,OAAOF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACE,WAAW,EAAE;EAClC;EAEAnE,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACG,WAAW,EAAEiE,IAAI,EAAE,OAAO,MAAM;IAE1C,MAAMC,UAAU,GAA8B;MAC5C,aAAa,EAAE,SAAS;MACxB,OAAO,EAAE,SAAS;MAClB,eAAe,EAAE,SAAS;MAC1B,mBAAmB,EAAE,SAAS;MAC9B,iBAAiB,EAAE,SAAS;MAC5B,iBAAiB,EAAE;KACpB;IAED,OAAOA,UAAU,CAAC,IAAI,CAAClE,WAAW,CAACiE,IAAI,CAAC,IAAI,MAAM;EACpD;EAEA/D,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACF,WAAW,EAAEiE,IAAI,EAAE,OAAO,EAAE;IAEtC,MAAME,gBAAgB,GAA8B;MAClD,aAAa,EAAE,aAAa;MAC5B,OAAO,EAAE,OAAO;MAChB,eAAe,EAAE,eAAe;MAChC,iBAAiB,EAAE,iBAAiB;MACpC,mBAAmB,EAAE,mBAAmB;MACxC,qBAAqB,EAAE,qBAAqB;MAC5C,iBAAiB,EAAE,iBAAiB;MACpC,YAAY,EAAE,YAAY;MAC1B,iBAAiB,EAAE,iBAAiB;MACpC,eAAe,EAAE,eAAe;MAChC,iBAAiB,EAAE,iBAAiB;MACpC,gBAAgB,EAAE;KACnB;IAED,OAAOA,gBAAgB,CAAC,IAAI,CAACnE,WAAW,CAACiE,IAAI,CAAC,IAAI,IAAI,CAACjE,WAAW,CAACiE,IAAI,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACL,WAAW,EAAE,CAAC;EACnI;EAEA7C,aAAaA,CAACH,KAAa;IACzB,OAAO,IAAI,CAACc,MAAM,CAAC8B,GAAG,KAAK5C,KAAK;EAClC;EAEAL,eAAeA,CAAA;IACb;IACA,IAAI,CAACsB,UAAU,CAACG,IAAI,CAAChD,SAAS,CAAC,IAAI,CAAC2C,QAAQ,CAAC,CAAC,CAACY,SAAS,CAAC2B,SAAS,IAAG;MACnE,IAAIA,SAAS,IAAI,IAAI,CAACC,MAAM,EAAE;QAC5B,IAAI,CAACA,MAAM,CAACC,KAAK,EAAE;;IAEvB,CAAC,CAAC;EACJ;;;uBA7KW9C,oBAAoB,EAAApC,EAAA,CAAAmF,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAArF,EAAA,CAAAmF,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAvF,EAAA,CAAAmF,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApBrD,oBAAoB;MAAAsD,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCX/B7F,EAFF,CAAAC,cAAA,+BAAiD,wBAOQ;;;;UAKjDD,EAFJ,CAAAC,cAAA,aAA4B,aACR,kBACY;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAClCF,EADkC,CAAAG,YAAA,EAAO,EACnC;UACNH,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACtCF,EADsC,CAAAG,YAAA,EAAM,EACtC;UAGNH,EAAA,CAAAsB,UAAA,KAAAyE,oCAAA,kBAA8C;UAY9C/F,EAAA,CAAAC,cAAA,wBAA+B;UAC7BD,EAAA,CAAAsB,UAAA,KAAA0E,8CAAA,4BAK8B;UAKhChG,EAAA,CAAAG,YAAA,EAAe;UAIbH,EADF,CAAAC,cAAA,eAA4B,kBACoC;UAArBD,EAAA,CAAAe,UAAA,mBAAAkF,uDAAA;YAAAjG,EAAA,CAAAiB,aAAA,CAAAiF,GAAA;YAAA,OAAAlG,EAAA,CAAAoB,WAAA,CAAS0E,GAAA,CAAA1B,QAAA,EAAU;UAAA,EAAC;UAC3DpE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAGlBF,EAHkB,CAAAG,YAAA,EAAO,EACZ,EACL,EACM;UAKZH,EAFF,CAAAC,cAAA,+BAA0C,uBAEK;UAE3CD,EAAA,CAAAsB,UAAA,KAAA6E,uCAAA,qBAK6B;;UAK7BnG,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,IAAe;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAG/CH,EAAA,CAAAoG,SAAA,gBAAoC;UAIlCpG,EADF,CAAAC,cAAA,kBAAoD,oBACN;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAC3DF,EAD2D,CAAAG,YAAA,EAAW,EAC7D;UAKLH,EAFJ,CAAAC,cAAA,kBAAgF,eACH,gBACrC;UAAAD,EAAA,CAAAE,MAAA,IAAuB;UAE/DF,EAF+D,CAAAG,YAAA,EAAO,EAC9D,EACC;UAGTH,EAAA,CAAAC,cAAA,uBAAgD;UAC9CD,EAAA,CAAAsB,UAAA,KAAA+E,oCAAA,mBAAkD;UAUlDrG,EAAA,CAAAoG,SAAA,mBAA2B;UAEzBpG,EADF,CAAAC,cAAA,kBAAsB,gBACV;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,eAAO;UACfF,EADe,CAAAG,YAAA,EAAO,EACb;UAEPH,EADF,CAAAC,cAAA,kBAAsB,gBACV;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UACxBF,EADwB,CAAAG,YAAA,EAAO,EACtB;UAEPH,EADF,CAAAC,cAAA,kBAAsB,gBACV;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UACtBF,EADsB,CAAAG,YAAA,EAAO,EACpB;UACTH,EAAA,CAAAoG,SAAA,mBAA2B;UAC3BpG,EAAA,CAAAC,cAAA,kBAAoE;UAA9CD,EAAA,CAAAe,UAAA,mBAAAuF,uDAAA;YAAAtG,EAAA,CAAAiB,aAAA,CAAAiF,GAAA;YAAA,OAAAlG,EAAA,CAAAoB,WAAA,CAAS0E,GAAA,CAAA1B,QAAA,EAAU;UAAA,EAAC;UACxCpE,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAGlBF,EAHkB,CAAAG,YAAA,EAAO,EACZ,EACA,EACC;UAGdH,EAAA,CAAAC,cAAA,eAA0B;UACxBD,EAAA,CAAAoG,SAAA,qBAA+B;UAGrCpG,EAFI,CAAAG,YAAA,EAAM,EACc,EACA;;;;UArHTH,EAAA,CAAAI,SAAA,EAA+C;UAC/CJ,EADA,CAAAwB,UAAA,SAAAxB,EAAA,CAAAa,WAAA,QAAAiF,GAAA,CAAAnD,UAAA,oBAA+C,WAAA3C,EAAA,CAAAa,WAAA,QAAAiF,GAAA,CAAAnD,UAAA,YACN;;UAYzB3C,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAwB,UAAA,SAAAsE,GAAA,CAAApF,WAAA,CAAiB;UAcvBV,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAwB,UAAA,YAAAsE,GAAA,CAAA7B,yBAAA,GAA8B;UA8B9CjE,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAwB,UAAA,SAAAxB,EAAA,CAAAa,WAAA,SAAAiF,GAAA,CAAAnD,UAAA,EAAwB;UAKF3C,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAQ,iBAAA,CAAAsF,GAAA,CAAApD,SAAA,CAAe;UAWhB1C,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAwB,UAAA,sBAAA+E,WAAA,CAA8B;UACnBvG,EAAA,CAAAI,SAAA,EAAyC;UAAzCJ,EAAA,CAAAK,WAAA,qBAAAyF,GAAA,CAAAvF,YAAA,GAAyC;UACpCP,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAQ,iBAAA,CAAAsF,GAAA,CAAArF,eAAA,GAAuB;UAM9BT,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAwB,UAAA,SAAAsE,GAAA,CAAApF,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}