{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { MatCommonModule } from '@angular/material/core';\nlet MatDivider = /*#__PURE__*/(() => {\n  class MatDivider {\n    constructor() {\n      this._vertical = false;\n      this._inset = false;\n    }\n    /** Whether the divider is vertically aligned. */\n    get vertical() {\n      return this._vertical;\n    }\n    set vertical(value) {\n      this._vertical = coerceBooleanProperty(value);\n    }\n    /** Whether the divider is an inset divider. */\n    get inset() {\n      return this._inset;\n    }\n    set inset(value) {\n      this._inset = coerceBooleanProperty(value);\n    }\n    static {\n      this.ɵfac = function MatDivider_Factory(t) {\n        return new (t || MatDivider)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatDivider,\n        selectors: [[\"mat-divider\"]],\n        hostAttrs: [\"role\", \"separator\", 1, \"mat-divider\"],\n        hostVars: 7,\n        hostBindings: function MatDivider_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"aria-orientation\", ctx.vertical ? \"vertical\" : \"horizontal\");\n            i0.ɵɵclassProp(\"mat-divider-vertical\", ctx.vertical)(\"mat-divider-horizontal\", !ctx.vertical)(\"mat-divider-inset\", ctx.inset);\n          }\n        },\n        inputs: {\n          vertical: \"vertical\",\n          inset: \"inset\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 0,\n        vars: 0,\n        template: function MatDivider_Template(rf, ctx) {},\n        styles: [\".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color);border-top-width:var(--mat-divider-width)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color);border-right-width:var(--mat-divider-width)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\"],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatDivider;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatDividerModule = /*#__PURE__*/(() => {\n  class MatDividerModule {\n    static {\n      this.ɵfac = function MatDividerModule_Factory(t) {\n        return new (t || MatDividerModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatDividerModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [MatCommonModule, MatCommonModule]\n      });\n    }\n  }\n  return MatDividerModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatDivider, MatDividerModule };\n//# sourceMappingURL=divider.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}