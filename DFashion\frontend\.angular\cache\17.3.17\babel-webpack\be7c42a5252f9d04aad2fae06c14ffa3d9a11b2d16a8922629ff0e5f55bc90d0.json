{"ast": null, "code": "import _asyncToGenerator from \"E:/Fahion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, of, throwError } from 'rxjs';\nimport { tap, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./storage.service\";\nimport * as i3 from \"@ionic/angular\";\nexport class CartService {\n  constructor(http, storageService, toastController) {\n    this.http = http;\n    this.storageService = storageService;\n    this.toastController = toastController;\n    this.API_URL = 'http://localhost:5000/api/v1';\n    this.cartItems = new BehaviorSubject([]);\n    this.cartSummary = new BehaviorSubject(null);\n    this.cartItemCount = new BehaviorSubject(0);\n    this.cartTotalAmount = new BehaviorSubject(0);\n    this.showCartTotalPrice = new BehaviorSubject(false);\n    this.totalItemCount = new BehaviorSubject(0); // Combined cart + wishlist count\n    this.isLoadingCart = false;\n    this.useLocalStorageOnly = false; // Temporary flag to disable API calls\n    this.cartItems$ = this.cartItems.asObservable();\n    this.cartSummary$ = this.cartSummary.asObservable();\n    this.cartItemCount$ = this.cartItemCount.asObservable();\n    this.cartTotalAmount$ = this.cartTotalAmount.asObservable();\n    this.showCartTotalPrice$ = this.showCartTotalPrice.asObservable();\n    this.totalItemCount$ = this.totalItemCount.asObservable(); // Observable for total count\n    // Initialize cart on service creation - but only load from storage for guest users\n    this.initializeCart();\n  }\n  initializeCart() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      // User is authenticated, load from API\n      this.loadCart();\n    } else {\n      // Guest user, load from local storage only\n      console.log('🔄 Guest user detected, loading cart from local storage only...');\n      this.loadCartFromStorage();\n    }\n  }\n  // Get cart from API\n  getCart() {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.get(`${this.API_URL}/cart`, options);\n  }\n  // Get cart count only (lightweight endpoint) - returns total quantities\n  getCartCount() {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.get(`${this.API_URL}/cart/count`, options);\n  }\n  // Get total count for logged-in user (cart + wishlist)\n  getTotalCount() {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.get(`${this.API_URL}/cart/total-count`, options);\n  }\n  // Debug cart data\n  debugCart() {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.get(`${this.API_URL}/cart/debug`, options).pipe(catchError(error => {\n      console.log('🔍 Debug endpoint not available, skipping debug');\n      return of({\n        success: false,\n        message: 'Debug endpoint not available'\n      });\n    }));\n  }\n  // Recalculate cart totals\n  recalculateCart() {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.post(`${this.API_URL}/cart-new/recalculate`, {}, options).pipe(catchError(error => {\n      console.log('🔧 Recalculate endpoint not available, skipping recalculation');\n      return of({\n        success: false,\n        message: 'Recalculate endpoint not available'\n      });\n    }));\n  }\n  // Load cart and update local state\n  loadCart() {\n    // Temporary: Use local storage only to avoid API errors\n    if (this.useLocalStorageOnly) {\n      console.log('🔄 Using local storage only (API disabled)...');\n      this.loadCartFromStorage();\n      return;\n    }\n    // Check if user is logged in\n    const token = localStorage.getItem('token');\n    if (token) {\n      // User is logged in - try API first, fallback to local storage\n      console.log('🔄 User authenticated, attempting to load cart from API...');\n      this.loadCartFromAPI();\n    } else {\n      // Guest user - load from local storage\n      console.log('🔄 Guest user, loading cart from local storage...');\n      this.loadCartFromStorage();\n    }\n  }\n  // Load cart from API for logged-in users\n  loadCartFromAPI() {\n    // Check if user is authenticated\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.log('❌ No authentication token, using local storage fallback');\n      this.loadCartFromStorage();\n      return;\n    }\n    // Prevent multiple simultaneous API calls\n    if (this.isLoadingCart) {\n      console.log('🔄 Cart already loading, skipping duplicate request');\n      return;\n    }\n    this.isLoadingCart = true;\n    this.getCart().subscribe({\n      next: response => {\n        this.isLoadingCart = false;\n        if (response.success && response.cart) {\n          const items = response.cart.items || [];\n          this.cartItems.next(items);\n          this.cartSummary.next(response.summary);\n          this.updateCartCount();\n          console.log('✅ Cart loaded from API:', items.length, 'items');\n          console.log('🛒 Cart items details:', items.map(item => ({\n            id: item._id,\n            name: item.product?.name,\n            quantity: item.quantity,\n            price: item.product?.price\n          })));\n        } else {\n          // No cart data from API, initialize empty cart\n          this.cartItems.next([]);\n          this.cartSummary.next(null);\n          this.updateCartCount();\n          console.log('❌ No cart data from API');\n        }\n      },\n      error: error => {\n        this.isLoadingCart = false;\n        console.error('❌ API cart error:', error);\n        if (error.status === 401) {\n          console.log('❌ Authentication failed, clearing token');\n          localStorage.removeItem('token');\n          this.cartItems.next([]);\n          this.cartSummary.next(null);\n          this.updateCartCount();\n        } else if (error.status === 500) {\n          console.log('❌ Server error, using local storage fallback');\n          this.loadCartFromStorage();\n        } else {\n          console.log('❌ API error, using local storage fallback');\n          this.loadCartFromStorage();\n        }\n      }\n    });\n  }\n  loadCartFromStorage() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Check if storage service is available\n        if (!_this.storageService) {\n          console.log('Storage service not available, using empty cart');\n          _this.cartItems.next([]);\n          _this.updateCartCount();\n          return;\n        }\n        // Wait a bit for storage to initialize\n        yield new Promise(resolve => setTimeout(resolve, 100));\n        const cart = yield _this.storageService.getCart();\n        _this.cartItems.next(cart || []);\n        _this.updateCartCount();\n      } catch (error) {\n        console.error('Error loading cart from storage:', error);\n        _this.cartItems.next([]);\n        _this.updateCartCount();\n      }\n    })();\n  }\n  saveCartToStorage() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this2.storageService) {\n          console.log('Storage service not available, skipping cart save');\n          return;\n        }\n        yield _this2.storageService.setCart(_this2.cartItems.value);\n      } catch (error) {\n        console.error('Error saving cart to storage:', error);\n      }\n    })();\n  }\n  updateCartCount() {\n    const items = this.cartItems.value || [];\n    const count = items.reduce((total, item) => total + item.quantity, 0);\n    this.cartItemCount.next(count);\n    console.log('🛒 Cart count updated:', count);\n  }\n  // Method to refresh cart on user login\n  refreshCartOnLogin() {\n    console.log('🔄 Refreshing cart on login...');\n    this.loadCartFromAPI();\n  }\n  // Method to refresh total count (cart + wishlist) for logged-in user\n  refreshTotalCount() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      // Skip debug for now and go directly to getting total count\n      console.log('🔄 Refreshing total count for logged-in user...');\n      this.getTotalCountAfterRecalculation();\n    } else {\n      // No token, set all counts to 0\n      this.resetAllCounts();\n    }\n  }\n  getTotalCountAfterRecalculation() {\n    // Only refresh if user is authenticated\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.log('🔒 No authentication token, skipping cart count refresh');\n      this.resetAllCounts();\n      return;\n    }\n    this.getTotalCount().subscribe({\n      next: response => {\n        if (response.success) {\n          const data = response.data;\n          // Update cart count with TOTAL QUANTITY (not just item count)\n          this.cartItemCount.next(data.cart.quantityTotal || 0);\n          this.cartTotalAmount.next(data.cart.totalAmount || 0);\n          this.showCartTotalPrice.next(data.showCartTotalPrice || false);\n          // Update TOTAL COUNT (cart + wishlist)\n          this.totalItemCount.next(data.totalCount || 0);\n          console.log('🔢 Total count refreshed for user:', response.username, {\n            cartQuantityTotal: data.cart.quantityTotal,\n            cartItemCount: data.cart.itemCount,\n            wishlistItems: data.wishlist.itemCount,\n            totalCount: data.totalCount,\n            cartTotal: data.cart.totalAmount,\n            showPrice: data.showCartTotalPrice\n          });\n        }\n      },\n      error: error => {\n        console.error('❌ Error refreshing total count:', error);\n        if (error.status === 401) {\n          console.log('❌ Authentication failed, clearing token');\n          localStorage.removeItem('token');\n          this.resetAllCounts();\n        } else if (error.status === 404) {\n          console.log('❌ Total count endpoint not found, using fallback');\n          // Fallback: Load cart directly to get counts\n          this.loadCartFromAPI();\n        } else {\n          // For other errors, reset counts to avoid showing stale data\n          this.resetAllCounts();\n        }\n      }\n    });\n  }\n  // Method to refresh only cart count (lightweight) - kept for backward compatibility\n  refreshCartCount() {\n    this.refreshTotalCount(); // Use the new total count method\n  }\n  // Reset all counts to 0\n  resetAllCounts() {\n    this.cartItemCount.next(0);\n    this.cartTotalAmount.next(0);\n    this.showCartTotalPrice.next(false);\n    this.totalItemCount.next(0);\n    console.log('🔄 All counts reset to 0');\n  }\n  // Method to clear cart on logout\n  clearCartOnLogout() {\n    console.log('🔄 Clearing cart on logout...');\n    this.cartItems.next([]);\n    this.cartSummary.next(null);\n    this.resetAllCounts();\n  }\n  // Temporary method to enable/disable API calls\n  setUseLocalStorageOnly(useLocalOnly) {\n    this.useLocalStorageOnly = useLocalOnly;\n    console.log('🔧 Cart API calls', useLocalOnly ? 'DISABLED' : 'ENABLED');\n    if (useLocalOnly) {\n      console.log('🔧 Cart will use local storage only');\n    }\n  }\n  // Add item to cart via API\n  addToCart(productId, quantity = 1, size, color) {\n    const payload = {\n      productId,\n      quantity,\n      size,\n      color\n    };\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.post(`${this.API_URL}/cart-new/add`, payload, options).pipe(tap(response => {\n      if (response.success) {\n        console.log('✅ Product added to cart successfully');\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      } else {\n        console.warn('⚠️ Add to cart API returned unsuccessful response');\n      }\n    }), catchError(error => {\n      console.error('❌ Error adding to cart:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Legacy method for backward compatibility - works for guest users\n  addToCartLegacy(_x) {\n    var _this3 = this;\n    return _asyncToGenerator(function* (product, quantity = 1, size, color) {\n      try {\n        const productId = product._id || product.id;\n        // Try API first, but fallback to local storage for guest users\n        try {\n          const response = yield _this3.addToCart(productId, quantity, size, color).toPromise();\n          if (response?.success) {\n            yield _this3.showToast('Item added to cart', 'success');\n            _this3.loadCart(); // Refresh cart\n            return true;\n          }\n        } catch (apiError) {\n          console.log('API not available, using local storage');\n        }\n        // Fallback to local storage (for guest users)\n        const cartItem = {\n          _id: `${productId}_${size || 'default'}_${color || 'default'}`,\n          product: {\n            _id: productId,\n            name: product.name,\n            price: product.price,\n            originalPrice: product.originalPrice,\n            images: product.images || [],\n            brand: product.brand || '',\n            discount: product.discount\n          },\n          quantity,\n          size,\n          color,\n          addedAt: new Date()\n        };\n        const currentCart = _this3.cartItems.value;\n        const existingItemIndex = currentCart.findIndex(item => item.product._id === productId && (item.size || 'default') === (size || 'default') && (item.color || 'default') === (color || 'default'));\n        if (existingItemIndex >= 0) {\n          currentCart[existingItemIndex].quantity += quantity;\n        } else {\n          currentCart.push(cartItem);\n        }\n        _this3.cartItems.next(currentCart);\n        _this3.updateCartCount();\n        yield _this3.saveCartToStorage();\n        yield _this3.showToast('Item added to cart', 'success');\n        return true;\n      } catch (error) {\n        console.error('Error adding to cart:', error);\n        yield _this3.showToast('Failed to add item to cart', 'danger');\n        return false;\n      }\n    }).apply(this, arguments);\n  }\n  // Remove item from cart via API\n  removeFromCart(itemId) {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.delete(`${this.API_URL}/cart-new/remove/${itemId}`, options).pipe(tap(response => {\n      if (response.success) {\n        console.log('✅ Product removed from cart successfully');\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      } else {\n        console.warn('⚠️ Remove from cart API returned unsuccessful response');\n      }\n    }), catchError(error => {\n      console.error('❌ Error removing from cart:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Bulk remove items from cart\n  bulkRemoveFromCart(itemIds) {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      body: {\n        itemIds\n      },\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {\n      body: {\n        itemIds\n      }\n    };\n    return this.http.delete(`${this.API_URL}/cart-new/bulk-remove`, options).pipe(tap(response => {\n      if (response.success) {\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      }\n    }));\n  }\n  // Legacy method\n  removeFromCartLegacy(itemId) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield _this4.removeFromCart(itemId).toPromise();\n        if (response?.success) {\n          yield _this4.showToast('Item removed from cart', 'success');\n          _this4.loadCart(); // Refresh cart\n        }\n      } catch (error) {\n        console.error('Error removing from cart:', error);\n        yield _this4.showToast('Failed to remove item from cart', 'danger');\n      }\n    })();\n  }\n  // Update cart item quantity via API\n  updateCartItem(itemId, quantity) {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.put(`${this.API_URL}/cart-new/update/${itemId}`, {\n      quantity\n    }, options).pipe(tap(response => {\n      if (response.success) {\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      }\n    }));\n  }\n  // Legacy method\n  updateQuantity(itemId, quantity) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (quantity <= 0) {\n          yield _this5.removeFromCartLegacy(itemId);\n          return;\n        }\n        const response = yield _this5.updateCartItem(itemId, quantity).toPromise();\n        if (response?.success) {\n          _this5.loadCart(); // Refresh cart\n        }\n      } catch (error) {\n        console.error('Error updating quantity:', error);\n        yield _this5.showToast('Failed to update quantity', 'danger');\n      }\n    })();\n  }\n  // Clear cart via API\n  clearCartAPI() {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.delete(`${this.API_URL}/cart-new/clear`, options);\n  }\n  clearCart() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield _this6.clearCartAPI().toPromise();\n        if (response?.success) {\n          _this6.cartItems.next([]);\n          _this6.cartSummary.next(null);\n          _this6.updateCartCount();\n          yield _this6.showToast('Cart cleared', 'success');\n        }\n      } catch (error) {\n        console.error('Error clearing cart:', error);\n        yield _this6.showToast('Failed to clear cart', 'danger');\n      }\n    })();\n  }\n  getCartTotal() {\n    const summary = this.cartSummary.value;\n    if (summary) {\n      return summary.total;\n    }\n    // Fallback calculation\n    return this.cartItems.value.reduce((total, item) => {\n      const price = item.product.price;\n      return total + price * item.quantity;\n    }, 0);\n  }\n  // Get total count (cart + wishlist items) for the logged-in user\n  getTotalItemCount() {\n    return this.totalItemCount.value;\n  }\n  // Get cart item count only\n  getCartItemCount() {\n    return this.cartItemCount.value;\n  }\n  // Get cart total amount\n  getCartTotalAmount() {\n    return this.cartTotalAmount.value;\n  }\n  // Check if cart total price should be displayed (when cart has 4+ products)\n  shouldShowCartTotalPrice() {\n    return this.showCartTotalPrice.value;\n  }\n  isInCart(productId, size, color) {\n    return this.cartItems.value.some(item => item.product._id === productId && (item.size || 'default') === (size || 'default') && (item.color || 'default') === (color || 'default'));\n  }\n  getCartItem(productId, size, color) {\n    return this.cartItems.value.find(item => item.product._id === productId && (item.size || 'default') === (size || 'default') && (item.color || 'default') === (color || 'default'));\n  }\n  showToast(message, color) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const toast = yield _this7.toastController.create({\n        message: message,\n        duration: 2000,\n        color: color,\n        position: 'bottom'\n      });\n      toast.present();\n    })();\n  }\n  static {\n    this.ɵfac = function CartService_Factory(t) {\n      return new (t || CartService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.StorageService), i0.ɵɵinject(i3.ToastController));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CartService,\n      factory: CartService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "of", "throwError", "tap", "catchError", "CartService", "constructor", "http", "storageService", "toastController", "API_URL", "cartItems", "cartSummary", "cartItemCount", "cartTotalAmount", "showCartTotalPrice", "totalItemCount", "isLoadingCart", "useLocalStorageOnly", "cartItems$", "asObservable", "cartSummary$", "cartItemCount$", "cartTotalAmount$", "showCartTotalPrice$", "totalItemCount$", "initializeCart", "token", "localStorage", "getItem", "loadCart", "console", "log", "loadCartFromStorage", "getCart", "options", "headers", "get", "getCartCount", "getTotalCount", "debugCart", "pipe", "error", "success", "message", "recalculateCart", "post", "loadCartFromAPI", "subscribe", "next", "response", "cart", "items", "summary", "updateCartCount", "length", "map", "item", "id", "_id", "name", "product", "quantity", "price", "status", "removeItem", "_this", "_asyncToGenerator", "Promise", "resolve", "setTimeout", "saveCartToStorage", "_this2", "setCart", "value", "count", "reduce", "total", "refreshCartOnLogin", "refreshTotalCount", "getTotalCountAfterRecalculation", "resetAllCounts", "data", "quantityTotal", "totalAmount", "totalCount", "username", "cartQuantityTotal", "itemCount", "wishlistItems", "wishlist", "cartTotal", "showPrice", "refreshCartCount", "clearCartOnLogout", "setUseLocalStorageOnly", "useLocalOnly", "addToCart", "productId", "size", "color", "payload", "warn", "addToCartLegacy", "_x", "_this3", "to<PERSON>romise", "showToast", "apiError", "cartItem", "originalPrice", "images", "brand", "discount", "addedAt", "Date", "currentCart", "existingItemIndex", "findIndex", "push", "apply", "arguments", "removeFromCart", "itemId", "delete", "bulkRemoveFromCart", "itemIds", "body", "removeFromCartLegacy", "_this4", "updateCartItem", "put", "updateQuantity", "_this5", "clearCartAPI", "clearCart", "_this6", "getCartTotal", "getTotalItemCount", "getCartItemCount", "getCartTotalAmount", "shouldShowCartTotalPrice", "isInCart", "some", "getCartItem", "find", "_this7", "toast", "create", "duration", "position", "present", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "StorageService", "i3", "ToastController", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\core\\services\\cart.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable, of, throwError } from 'rxjs';\nimport { tap, catchError } from 'rxjs/operators';\nimport { HttpClient } from '@angular/common/http';\nimport { StorageService } from './storage.service';\nimport { ToastController } from '@ionic/angular';\nimport { environment } from '../../../environments/environment';\nimport { RealtimeService } from './realtime.service';\n\nexport interface CartItem {\n  _id: string;\n  product: {\n    _id: string;\n    name: string;\n    price: number;\n    originalPrice?: number;\n    images: { url: string; isPrimary: boolean }[];\n    brand: string;\n    discount?: number;\n  };\n  quantity: number;\n  size?: string;\n  color?: string;\n  addedAt: Date;\n}\n\nexport interface CartSummary {\n  itemCount: number;\n  totalQuantity: number;\n  subtotal: number;\n  discount: number;\n  total: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class CartService {\n  private readonly API_URL = 'http://localhost:5000/api/v1';\n  private cartItems = new BehaviorSubject<CartItem[]>([]);\n  private cartSummary = new BehaviorSubject<CartSummary | null>(null);\n  private cartItemCount = new BehaviorSubject<number>(0);\n  private cartTotalAmount = new BehaviorSubject<number>(0);\n  private showCartTotalPrice = new BehaviorSubject<boolean>(false);\n  private totalItemCount = new BehaviorSubject<number>(0); // Combined cart + wishlist count\n  private isLoadingCart = false;\n  private useLocalStorageOnly = false; // Temporary flag to disable API calls\n\n  public cartItems$ = this.cartItems.asObservable();\n  public cartSummary$ = this.cartSummary.asObservable();\n  public cartItemCount$ = this.cartItemCount.asObservable();\n  public cartTotalAmount$ = this.cartTotalAmount.asObservable();\n  public showCartTotalPrice$ = this.showCartTotalPrice.asObservable();\n  public totalItemCount$ = this.totalItemCount.asObservable(); // Observable for total count\n\n  constructor(\n    private http: HttpClient,\n    private storageService: StorageService,\n    private toastController: ToastController\n  ) {\n    // Initialize cart on service creation - but only load from storage for guest users\n    this.initializeCart();\n  }\n\n  private initializeCart() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      // User is authenticated, load from API\n      this.loadCart();\n    } else {\n      // Guest user, load from local storage only\n      console.log('🔄 Guest user detected, loading cart from local storage only...');\n      this.loadCartFromStorage();\n    }\n  }\n\n\n\n  // Get cart from API\n  getCart(): Observable<{ success: boolean; cart: any; summary: any }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.get<{ success: boolean; cart: any; summary: any }>(`${this.API_URL}/cart`, options);\n  }\n\n  // Get cart count only (lightweight endpoint) - returns total quantities\n  getCartCount(): Observable<{ success: boolean; count: number; totalItems: number; itemCount: number; totalAmount: number; showTotalPrice: boolean }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.get<{ success: boolean; count: number; totalItems: number; itemCount: number; totalAmount: number; showTotalPrice: boolean }>(`${this.API_URL}/cart/count`, options);\n  }\n\n  // Get total count for logged-in user (cart + wishlist)\n  getTotalCount(): Observable<any> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.get<any>(`${this.API_URL}/cart/total-count`, options);\n  }\n\n  // Debug cart data\n  debugCart(): Observable<any> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.get<any>(`${this.API_URL}/cart/debug`, options).pipe(\n      catchError(error => {\n        console.log('🔍 Debug endpoint not available, skipping debug');\n        return of({ success: false, message: 'Debug endpoint not available' });\n      })\n    );\n  }\n\n  // Recalculate cart totals\n  recalculateCart(): Observable<any> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.post<any>(`${this.API_URL}/cart-new/recalculate`, {}, options).pipe(\n      catchError(error => {\n        console.log('🔧 Recalculate endpoint not available, skipping recalculation');\n        return of({ success: false, message: 'Recalculate endpoint not available' });\n      })\n    );\n  }\n\n  // Load cart and update local state\n  loadCart() {\n    // Temporary: Use local storage only to avoid API errors\n    if (this.useLocalStorageOnly) {\n      console.log('🔄 Using local storage only (API disabled)...');\n      this.loadCartFromStorage();\n      return;\n    }\n\n    // Check if user is logged in\n    const token = localStorage.getItem('token');\n\n    if (token) {\n      // User is logged in - try API first, fallback to local storage\n      console.log('🔄 User authenticated, attempting to load cart from API...');\n      this.loadCartFromAPI();\n    } else {\n      // Guest user - load from local storage\n      console.log('🔄 Guest user, loading cart from local storage...');\n      this.loadCartFromStorage();\n    }\n  }\n\n  // Load cart from API for logged-in users\n  private loadCartFromAPI() {\n    // Check if user is authenticated\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.log('❌ No authentication token, using local storage fallback');\n      this.loadCartFromStorage();\n      return;\n    }\n\n    // Prevent multiple simultaneous API calls\n    if (this.isLoadingCart) {\n      console.log('🔄 Cart already loading, skipping duplicate request');\n      return;\n    }\n\n    this.isLoadingCart = true;\n\n    this.getCart().subscribe({\n      next: (response) => {\n        this.isLoadingCart = false;\n        if (response.success && response.cart) {\n          const items = response.cart.items || [];\n          this.cartItems.next(items);\n          this.cartSummary.next(response.summary);\n          this.updateCartCount();\n          console.log('✅ Cart loaded from API:', items.length, 'items');\n          console.log('🛒 Cart items details:', items.map((item: any) => ({\n            id: item._id,\n            name: item.product?.name,\n            quantity: item.quantity,\n            price: item.product?.price\n          })));\n        } else {\n          // No cart data from API, initialize empty cart\n          this.cartItems.next([]);\n          this.cartSummary.next(null);\n          this.updateCartCount();\n          console.log('❌ No cart data from API');\n        }\n      },\n      error: (error) => {\n        this.isLoadingCart = false;\n        console.error('❌ API cart error:', error);\n\n        if (error.status === 401) {\n          console.log('❌ Authentication failed, clearing token');\n          localStorage.removeItem('token');\n          this.cartItems.next([]);\n          this.cartSummary.next(null);\n          this.updateCartCount();\n        } else if (error.status === 500) {\n          console.log('❌ Server error, using local storage fallback');\n          this.loadCartFromStorage();\n        } else {\n          console.log('❌ API error, using local storage fallback');\n          this.loadCartFromStorage();\n        }\n      }\n    });\n  }\n\n  private async loadCartFromStorage() {\n    try {\n      // Check if storage service is available\n      if (!this.storageService) {\n        console.log('Storage service not available, using empty cart');\n        this.cartItems.next([]);\n        this.updateCartCount();\n        return;\n      }\n\n      // Wait a bit for storage to initialize\n      await new Promise(resolve => setTimeout(resolve, 100));\n      const cart = await this.storageService.getCart();\n      this.cartItems.next(cart || []);\n      this.updateCartCount();\n    } catch (error) {\n      console.error('Error loading cart from storage:', error);\n      this.cartItems.next([]);\n      this.updateCartCount();\n    }\n  }\n\n  private async saveCartToStorage() {\n    try {\n      if (!this.storageService) {\n        console.log('Storage service not available, skipping cart save');\n        return;\n      }\n      await this.storageService.setCart(this.cartItems.value);\n    } catch (error) {\n      console.error('Error saving cart to storage:', error);\n    }\n  }\n\n  private updateCartCount() {\n    const items = this.cartItems.value || [];\n    const count = items.reduce((total, item) => total + item.quantity, 0);\n    this.cartItemCount.next(count);\n    console.log('🛒 Cart count updated:', count);\n  }\n\n  // Method to refresh cart on user login\n  refreshCartOnLogin() {\n    console.log('🔄 Refreshing cart on login...');\n    this.loadCartFromAPI();\n  }\n\n  // Method to refresh total count (cart + wishlist) for logged-in user\n  refreshTotalCount() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      // Skip debug for now and go directly to getting total count\n      console.log('🔄 Refreshing total count for logged-in user...');\n      this.getTotalCountAfterRecalculation();\n    } else {\n      // No token, set all counts to 0\n      this.resetAllCounts();\n    }\n  }\n\n  private getTotalCountAfterRecalculation() {\n    // Only refresh if user is authenticated\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.log('🔒 No authentication token, skipping cart count refresh');\n      this.resetAllCounts();\n      return;\n    }\n\n    this.getTotalCount().subscribe({\n      next: (response) => {\n        if (response.success) {\n          const data = response.data;\n\n          // Update cart count with TOTAL QUANTITY (not just item count)\n          this.cartItemCount.next(data.cart.quantityTotal || 0);\n          this.cartTotalAmount.next(data.cart.totalAmount || 0);\n          this.showCartTotalPrice.next(data.showCartTotalPrice || false);\n\n          // Update TOTAL COUNT (cart + wishlist)\n          this.totalItemCount.next(data.totalCount || 0);\n\n          console.log('🔢 Total count refreshed for user:', response.username, {\n            cartQuantityTotal: data.cart.quantityTotal,\n            cartItemCount: data.cart.itemCount,\n            wishlistItems: data.wishlist.itemCount,\n            totalCount: data.totalCount,\n            cartTotal: data.cart.totalAmount,\n            showPrice: data.showCartTotalPrice\n          });\n        }\n      },\n      error: (error) => {\n        console.error('❌ Error refreshing total count:', error);\n        if (error.status === 401) {\n          console.log('❌ Authentication failed, clearing token');\n          localStorage.removeItem('token');\n          this.resetAllCounts();\n        } else if (error.status === 404) {\n          console.log('❌ Total count endpoint not found, using fallback');\n          // Fallback: Load cart directly to get counts\n          this.loadCartFromAPI();\n        } else {\n          // For other errors, reset counts to avoid showing stale data\n          this.resetAllCounts();\n        }\n      }\n    });\n  }\n\n  // Method to refresh only cart count (lightweight) - kept for backward compatibility\n  refreshCartCount() {\n    this.refreshTotalCount(); // Use the new total count method\n  }\n\n  // Reset all counts to 0\n  private resetAllCounts() {\n    this.cartItemCount.next(0);\n    this.cartTotalAmount.next(0);\n    this.showCartTotalPrice.next(false);\n    this.totalItemCount.next(0);\n    console.log('🔄 All counts reset to 0');\n  }\n\n  // Method to clear cart on logout\n  clearCartOnLogout() {\n    console.log('🔄 Clearing cart on logout...');\n    this.cartItems.next([]);\n    this.cartSummary.next(null);\n    this.resetAllCounts();\n  }\n\n  // Temporary method to enable/disable API calls\n  setUseLocalStorageOnly(useLocalOnly: boolean) {\n    this.useLocalStorageOnly = useLocalOnly;\n    console.log('🔧 Cart API calls', useLocalOnly ? 'DISABLED' : 'ENABLED');\n    if (useLocalOnly) {\n      console.log('🔧 Cart will use local storage only');\n    }\n  }\n\n  // Add item to cart via API\n  addToCart(productId: string, quantity: number = 1, size?: string, color?: string): Observable<{ success: boolean; message: string }> {\n    const payload = { productId, quantity, size, color };\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.post<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/add`, payload, options).pipe(\n      tap(response => {\n        if (response.success) {\n          console.log('✅ Product added to cart successfully');\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        } else {\n          console.warn('⚠️ Add to cart API returned unsuccessful response');\n        }\n      }),\n      catchError(error => {\n        console.error('❌ Error adding to cart:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  // Legacy method for backward compatibility - works for guest users\n  async addToCartLegacy(product: any, quantity: number = 1, size?: string, color?: string): Promise<boolean> {\n    try {\n      const productId = product._id || product.id;\n\n      // Try API first, but fallback to local storage for guest users\n      try {\n        const response = await this.addToCart(productId, quantity, size, color).toPromise();\n        if (response?.success) {\n          await this.showToast('Item added to cart', 'success');\n          this.loadCart(); // Refresh cart\n          return true;\n        }\n      } catch (apiError) {\n        console.log('API not available, using local storage');\n      }\n\n      // Fallback to local storage (for guest users)\n      const cartItem: CartItem = {\n        _id: `${productId}_${size || 'default'}_${color || 'default'}`,\n        product: {\n          _id: productId,\n          name: product.name,\n          price: product.price,\n          originalPrice: product.originalPrice,\n          images: product.images || [],\n          brand: product.brand || '',\n          discount: product.discount\n        },\n        quantity,\n        size,\n        color,\n        addedAt: new Date()\n      };\n\n      const currentCart = this.cartItems.value;\n      const existingItemIndex = currentCart.findIndex(item =>\n        item.product._id === productId &&\n        (item.size || 'default') === (size || 'default') &&\n        (item.color || 'default') === (color || 'default')\n      );\n\n      if (existingItemIndex >= 0) {\n        currentCart[existingItemIndex].quantity += quantity;\n      } else {\n        currentCart.push(cartItem);\n      }\n\n      this.cartItems.next(currentCart);\n      this.updateCartCount();\n      await this.saveCartToStorage();\n      await this.showToast('Item added to cart', 'success');\n      return true;\n\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n      await this.showToast('Failed to add item to cart', 'danger');\n      return false;\n    }\n  }\n\n  // Remove item from cart via API\n  removeFromCart(itemId: string): Observable<{ success: boolean; message: string }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.delete<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/remove/${itemId}`, options).pipe(\n      tap(response => {\n        if (response.success) {\n          console.log('✅ Product removed from cart successfully');\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        } else {\n          console.warn('⚠️ Remove from cart API returned unsuccessful response');\n        }\n      }),\n      catchError(error => {\n        console.error('❌ Error removing from cart:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  // Bulk remove items from cart\n  bulkRemoveFromCart(itemIds: string[]): Observable<{ success: boolean; message: string; removedCount: number }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      body: { itemIds },\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {\n      body: { itemIds }\n    };\n    return this.http.delete<{ success: boolean; message: string; removedCount: number }>(`${this.API_URL}/cart-new/bulk-remove`, options).pipe(\n      tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      })\n    );\n  }\n\n  // Legacy method\n  async removeFromCartLegacy(itemId: string): Promise<void> {\n    try {\n      const response = await this.removeFromCart(itemId).toPromise();\n      if (response?.success) {\n        await this.showToast('Item removed from cart', 'success');\n        this.loadCart(); // Refresh cart\n      }\n    } catch (error) {\n      console.error('Error removing from cart:', error);\n      await this.showToast('Failed to remove item from cart', 'danger');\n    }\n  }\n\n  // Update cart item quantity via API\n  updateCartItem(itemId: string, quantity: number): Observable<{ success: boolean; message: string }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.put<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/update/${itemId}`, { quantity }, options).pipe(\n      tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      })\n    );\n  }\n\n  // Legacy method\n  async updateQuantity(itemId: string, quantity: number): Promise<void> {\n    try {\n      if (quantity <= 0) {\n        await this.removeFromCartLegacy(itemId);\n        return;\n      }\n\n      const response = await this.updateCartItem(itemId, quantity).toPromise();\n      if (response?.success) {\n        this.loadCart(); // Refresh cart\n      }\n    } catch (error) {\n      console.error('Error updating quantity:', error);\n      await this.showToast('Failed to update quantity', 'danger');\n    }\n  }\n\n  // Clear cart via API\n  clearCartAPI(): Observable<{ success: boolean; message: string }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.delete<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/clear`, options);\n  }\n\n  async clearCart(): Promise<void> {\n    try {\n      const response = await this.clearCartAPI().toPromise();\n      if (response?.success) {\n        this.cartItems.next([]);\n        this.cartSummary.next(null);\n        this.updateCartCount();\n        await this.showToast('Cart cleared', 'success');\n      }\n    } catch (error) {\n      console.error('Error clearing cart:', error);\n      await this.showToast('Failed to clear cart', 'danger');\n    }\n  }\n\n  getCartTotal(): number {\n    const summary = this.cartSummary.value;\n    if (summary) {\n      return summary.total;\n    }\n\n    // Fallback calculation\n    return this.cartItems.value.reduce((total, item) => {\n      const price = item.product.price;\n      return total + (price * item.quantity);\n    }, 0);\n  }\n\n  // Get total count (cart + wishlist items) for the logged-in user\n  getTotalItemCount(): number {\n    return this.totalItemCount.value;\n  }\n\n  // Get cart item count only\n  getCartItemCount(): number {\n    return this.cartItemCount.value;\n  }\n\n  // Get cart total amount\n  getCartTotalAmount(): number {\n    return this.cartTotalAmount.value;\n  }\n\n  // Check if cart total price should be displayed (when cart has 4+ products)\n  shouldShowCartTotalPrice(): boolean {\n    return this.showCartTotalPrice.value;\n  }\n\n  isInCart(productId: string, size?: string, color?: string): boolean {\n    return this.cartItems.value.some(item =>\n      item.product._id === productId &&\n      (item.size || 'default') === (size || 'default') &&\n      (item.color || 'default') === (color || 'default')\n    );\n  }\n\n  getCartItem(productId: string, size?: string, color?: string): CartItem | undefined {\n    return this.cartItems.value.find(item =>\n      item.product._id === productId &&\n      (item.size || 'default') === (size || 'default') &&\n      (item.color || 'default') === (color || 'default')\n    );\n  }\n\n  private async showToast(message: string, color: string) {\n    const toast = await this.toastController.create({\n      message: message,\n      duration: 2000,\n      color: color,\n      position: 'bottom'\n    });\n    toast.present();\n  }\n}\n"], "mappings": ";AACA,SAASA,eAAe,EAAcC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AAClE,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;;;AAmChD,OAAM,MAAOC,WAAW;EAkBtBC,YACUC,IAAgB,EAChBC,cAA8B,EAC9BC,eAAgC;IAFhC,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IApBR,KAAAC,OAAO,GAAG,8BAA8B;IACjD,KAAAC,SAAS,GAAG,IAAIX,eAAe,CAAa,EAAE,CAAC;IAC/C,KAAAY,WAAW,GAAG,IAAIZ,eAAe,CAAqB,IAAI,CAAC;IAC3D,KAAAa,aAAa,GAAG,IAAIb,eAAe,CAAS,CAAC,CAAC;IAC9C,KAAAc,eAAe,GAAG,IAAId,eAAe,CAAS,CAAC,CAAC;IAChD,KAAAe,kBAAkB,GAAG,IAAIf,eAAe,CAAU,KAAK,CAAC;IACxD,KAAAgB,cAAc,GAAG,IAAIhB,eAAe,CAAS,CAAC,CAAC,CAAC,CAAC;IACjD,KAAAiB,aAAa,GAAG,KAAK;IACrB,KAAAC,mBAAmB,GAAG,KAAK,CAAC,CAAC;IAE9B,KAAAC,UAAU,GAAG,IAAI,CAACR,SAAS,CAACS,YAAY,EAAE;IAC1C,KAAAC,YAAY,GAAG,IAAI,CAACT,WAAW,CAACQ,YAAY,EAAE;IAC9C,KAAAE,cAAc,GAAG,IAAI,CAACT,aAAa,CAACO,YAAY,EAAE;IAClD,KAAAG,gBAAgB,GAAG,IAAI,CAACT,eAAe,CAACM,YAAY,EAAE;IACtD,KAAAI,mBAAmB,GAAG,IAAI,CAACT,kBAAkB,CAACK,YAAY,EAAE;IAC5D,KAAAK,eAAe,GAAG,IAAI,CAACT,cAAc,CAACI,YAAY,EAAE,CAAC,CAAC;IAO3D;IACA,IAAI,CAACM,cAAc,EAAE;EACvB;EAEQA,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT;MACA,IAAI,CAACG,QAAQ,EAAE;KAChB,MAAM;MACL;MACAC,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;MAC9E,IAAI,CAACC,mBAAmB,EAAE;;EAE9B;EAIA;EACAC,OAAOA,CAAA;IACL,MAAMP,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMM,OAAO,GAAGR,KAAK,GAAG;MACtBS,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUT,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAAC8B,GAAG,CAAgD,GAAG,IAAI,CAAC3B,OAAO,OAAO,EAAEyB,OAAO,CAAC;EACtG;EAEA;EACAG,YAAYA,CAAA;IACV,MAAMX,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMM,OAAO,GAAGR,KAAK,GAAG;MACtBS,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUT,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAAC8B,GAAG,CAA2H,GAAG,IAAI,CAAC3B,OAAO,aAAa,EAAEyB,OAAO,CAAC;EACvL;EAEA;EACAI,aAAaA,CAAA;IACX,MAAMZ,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMM,OAAO,GAAGR,KAAK,GAAG;MACtBS,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUT,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAAC8B,GAAG,CAAM,GAAG,IAAI,CAAC3B,OAAO,mBAAmB,EAAEyB,OAAO,CAAC;EACxE;EAEA;EACAK,SAASA,CAAA;IACP,MAAMb,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMM,OAAO,GAAGR,KAAK,GAAG;MACtBS,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUT,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAAC8B,GAAG,CAAM,GAAG,IAAI,CAAC3B,OAAO,aAAa,EAAEyB,OAAO,CAAC,CAACM,IAAI,CACnErC,UAAU,CAACsC,KAAK,IAAG;MACjBX,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9D,OAAO/B,EAAE,CAAC;QAAE0C,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAA8B,CAAE,CAAC;IACxE,CAAC,CAAC,CACH;EACH;EAEA;EACAC,eAAeA,CAAA;IACb,MAAMlB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMM,OAAO,GAAGR,KAAK,GAAG;MACtBS,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUT,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAACuC,IAAI,CAAM,GAAG,IAAI,CAACpC,OAAO,uBAAuB,EAAE,EAAE,EAAEyB,OAAO,CAAC,CAACM,IAAI,CAClFrC,UAAU,CAACsC,KAAK,IAAG;MACjBX,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;MAC5E,OAAO/B,EAAE,CAAC;QAAE0C,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAoC,CAAE,CAAC;IAC9E,CAAC,CAAC,CACH;EACH;EAEA;EACAd,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACZ,mBAAmB,EAAE;MAC5Ba,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,IAAI,CAACC,mBAAmB,EAAE;MAC1B;;IAGF;IACA,MAAMN,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAIF,KAAK,EAAE;MACT;MACAI,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzE,IAAI,CAACe,eAAe,EAAE;KACvB,MAAM;MACL;MACAhB,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAChE,IAAI,CAACC,mBAAmB,EAAE;;EAE9B;EAEA;EACQc,eAAeA,CAAA;IACrB;IACA,MAAMpB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVI,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;MACtE,IAAI,CAACC,mBAAmB,EAAE;MAC1B;;IAGF;IACA,IAAI,IAAI,CAAChB,aAAa,EAAE;MACtBc,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE;;IAGF,IAAI,CAACf,aAAa,GAAG,IAAI;IAEzB,IAAI,CAACiB,OAAO,EAAE,CAACc,SAAS,CAAC;MACvBC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACjC,aAAa,GAAG,KAAK;QAC1B,IAAIiC,QAAQ,CAACP,OAAO,IAAIO,QAAQ,CAACC,IAAI,EAAE;UACrC,MAAMC,KAAK,GAAGF,QAAQ,CAACC,IAAI,CAACC,KAAK,IAAI,EAAE;UACvC,IAAI,CAACzC,SAAS,CAACsC,IAAI,CAACG,KAAK,CAAC;UAC1B,IAAI,CAACxC,WAAW,CAACqC,IAAI,CAACC,QAAQ,CAACG,OAAO,CAAC;UACvC,IAAI,CAACC,eAAe,EAAE;UACtBvB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEoB,KAAK,CAACG,MAAM,EAAE,OAAO,CAAC;UAC7DxB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEoB,KAAK,CAACI,GAAG,CAAEC,IAAS,KAAM;YAC9DC,EAAE,EAAED,IAAI,CAACE,GAAG;YACZC,IAAI,EAAEH,IAAI,CAACI,OAAO,EAAED,IAAI;YACxBE,QAAQ,EAAEL,IAAI,CAACK,QAAQ;YACvBC,KAAK,EAAEN,IAAI,CAACI,OAAO,EAAEE;WACtB,CAAC,CAAC,CAAC;SACL,MAAM;UACL;UACA,IAAI,CAACpD,SAAS,CAACsC,IAAI,CAAC,EAAE,CAAC;UACvB,IAAI,CAACrC,WAAW,CAACqC,IAAI,CAAC,IAAI,CAAC;UAC3B,IAAI,CAACK,eAAe,EAAE;UACtBvB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;;MAE1C,CAAC;MACDU,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACzB,aAAa,GAAG,KAAK;QAC1Bc,OAAO,CAACW,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QAEzC,IAAIA,KAAK,CAACsB,MAAM,KAAK,GAAG,EAAE;UACxBjC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;UACtDJ,YAAY,CAACqC,UAAU,CAAC,OAAO,CAAC;UAChC,IAAI,CAACtD,SAAS,CAACsC,IAAI,CAAC,EAAE,CAAC;UACvB,IAAI,CAACrC,WAAW,CAACqC,IAAI,CAAC,IAAI,CAAC;UAC3B,IAAI,CAACK,eAAe,EAAE;SACvB,MAAM,IAAIZ,KAAK,CAACsB,MAAM,KAAK,GAAG,EAAE;UAC/BjC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3D,IAAI,CAACC,mBAAmB,EAAE;SAC3B,MAAM;UACLF,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;UACxD,IAAI,CAACC,mBAAmB,EAAE;;MAE9B;KACD,CAAC;EACJ;EAEcA,mBAAmBA,CAAA;IAAA,IAAAiC,KAAA;IAAA,OAAAC,iBAAA;MAC/B,IAAI;QACF;QACA,IAAI,CAACD,KAAI,CAAC1D,cAAc,EAAE;UACxBuB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;UAC9DkC,KAAI,CAACvD,SAAS,CAACsC,IAAI,CAAC,EAAE,CAAC;UACvBiB,KAAI,CAACZ,eAAe,EAAE;UACtB;;QAGF;QACA,MAAM,IAAIc,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;QACtD,MAAMlB,IAAI,SAASe,KAAI,CAAC1D,cAAc,CAAC0B,OAAO,EAAE;QAChDgC,KAAI,CAACvD,SAAS,CAACsC,IAAI,CAACE,IAAI,IAAI,EAAE,CAAC;QAC/Be,KAAI,CAACZ,eAAe,EAAE;OACvB,CAAC,OAAOZ,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDwB,KAAI,CAACvD,SAAS,CAACsC,IAAI,CAAC,EAAE,CAAC;QACvBiB,KAAI,CAACZ,eAAe,EAAE;;IACvB;EACH;EAEciB,iBAAiBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MAC7B,IAAI;QACF,IAAI,CAACK,MAAI,CAAChE,cAAc,EAAE;UACxBuB,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;UAChE;;QAEF,MAAMwC,MAAI,CAAChE,cAAc,CAACiE,OAAO,CAACD,MAAI,CAAC7D,SAAS,CAAC+D,KAAK,CAAC;OACxD,CAAC,OAAOhC,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;IACtD;EACH;EAEQY,eAAeA,CAAA;IACrB,MAAMF,KAAK,GAAG,IAAI,CAACzC,SAAS,CAAC+D,KAAK,IAAI,EAAE;IACxC,MAAMC,KAAK,GAAGvB,KAAK,CAACwB,MAAM,CAAC,CAACC,KAAK,EAAEpB,IAAI,KAAKoB,KAAK,GAAGpB,IAAI,CAACK,QAAQ,EAAE,CAAC,CAAC;IACrE,IAAI,CAACjD,aAAa,CAACoC,IAAI,CAAC0B,KAAK,CAAC;IAC9B5C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE2C,KAAK,CAAC;EAC9C;EAEA;EACAG,kBAAkBA,CAAA;IAChB/C,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7C,IAAI,CAACe,eAAe,EAAE;EACxB;EAEA;EACAgC,iBAAiBA,CAAA;IACf,MAAMpD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT;MACAI,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9D,IAAI,CAACgD,+BAA+B,EAAE;KACvC,MAAM;MACL;MACA,IAAI,CAACC,cAAc,EAAE;;EAEzB;EAEQD,+BAA+BA,CAAA;IACrC;IACA,MAAMrD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVI,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;MACtE,IAAI,CAACiD,cAAc,EAAE;MACrB;;IAGF,IAAI,CAAC1C,aAAa,EAAE,CAACS,SAAS,CAAC;MAC7BC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACP,OAAO,EAAE;UACpB,MAAMuC,IAAI,GAAGhC,QAAQ,CAACgC,IAAI;UAE1B;UACA,IAAI,CAACrE,aAAa,CAACoC,IAAI,CAACiC,IAAI,CAAC/B,IAAI,CAACgC,aAAa,IAAI,CAAC,CAAC;UACrD,IAAI,CAACrE,eAAe,CAACmC,IAAI,CAACiC,IAAI,CAAC/B,IAAI,CAACiC,WAAW,IAAI,CAAC,CAAC;UACrD,IAAI,CAACrE,kBAAkB,CAACkC,IAAI,CAACiC,IAAI,CAACnE,kBAAkB,IAAI,KAAK,CAAC;UAE9D;UACA,IAAI,CAACC,cAAc,CAACiC,IAAI,CAACiC,IAAI,CAACG,UAAU,IAAI,CAAC,CAAC;UAE9CtD,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEkB,QAAQ,CAACoC,QAAQ,EAAE;YACnEC,iBAAiB,EAAEL,IAAI,CAAC/B,IAAI,CAACgC,aAAa;YAC1CtE,aAAa,EAAEqE,IAAI,CAAC/B,IAAI,CAACqC,SAAS;YAClCC,aAAa,EAAEP,IAAI,CAACQ,QAAQ,CAACF,SAAS;YACtCH,UAAU,EAAEH,IAAI,CAACG,UAAU;YAC3BM,SAAS,EAAET,IAAI,CAAC/B,IAAI,CAACiC,WAAW;YAChCQ,SAAS,EAAEV,IAAI,CAACnE;WACjB,CAAC;;MAEN,CAAC;MACD2B,KAAK,EAAGA,KAAK,IAAI;QACfX,OAAO,CAACW,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,IAAIA,KAAK,CAACsB,MAAM,KAAK,GAAG,EAAE;UACxBjC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;UACtDJ,YAAY,CAACqC,UAAU,CAAC,OAAO,CAAC;UAChC,IAAI,CAACgB,cAAc,EAAE;SACtB,MAAM,IAAIvC,KAAK,CAACsB,MAAM,KAAK,GAAG,EAAE;UAC/BjC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;UAC/D;UACA,IAAI,CAACe,eAAe,EAAE;SACvB,MAAM;UACL;UACA,IAAI,CAACkC,cAAc,EAAE;;MAEzB;KACD,CAAC;EACJ;EAEA;EACAY,gBAAgBA,CAAA;IACd,IAAI,CAACd,iBAAiB,EAAE,CAAC,CAAC;EAC5B;EAEA;EACQE,cAAcA,CAAA;IACpB,IAAI,CAACpE,aAAa,CAACoC,IAAI,CAAC,CAAC,CAAC;IAC1B,IAAI,CAACnC,eAAe,CAACmC,IAAI,CAAC,CAAC,CAAC;IAC5B,IAAI,CAAClC,kBAAkB,CAACkC,IAAI,CAAC,KAAK,CAAC;IACnC,IAAI,CAACjC,cAAc,CAACiC,IAAI,CAAC,CAAC,CAAC;IAC3BlB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACzC;EAEA;EACA8D,iBAAiBA,CAAA;IACf/D,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,IAAI,CAACrB,SAAS,CAACsC,IAAI,CAAC,EAAE,CAAC;IACvB,IAAI,CAACrC,WAAW,CAACqC,IAAI,CAAC,IAAI,CAAC;IAC3B,IAAI,CAACgC,cAAc,EAAE;EACvB;EAEA;EACAc,sBAAsBA,CAACC,YAAqB;IAC1C,IAAI,CAAC9E,mBAAmB,GAAG8E,YAAY;IACvCjE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEgE,YAAY,GAAG,UAAU,GAAG,SAAS,CAAC;IACvE,IAAIA,YAAY,EAAE;MAChBjE,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;EAEtD;EAEA;EACAiE,SAASA,CAACC,SAAiB,EAAEpC,QAAA,GAAmB,CAAC,EAAEqC,IAAa,EAAEC,KAAc;IAC9E,MAAMC,OAAO,GAAG;MAAEH,SAAS;MAAEpC,QAAQ;MAAEqC,IAAI;MAAEC;IAAK,CAAE;IACpD,MAAMzE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMM,OAAO,GAAGR,KAAK,GAAG;MACtBS,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUT,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAACuC,IAAI,CAAwC,GAAG,IAAI,CAACpC,OAAO,eAAe,EAAE2F,OAAO,EAAElE,OAAO,CAAC,CAACM,IAAI,CACjHtC,GAAG,CAAC+C,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACP,OAAO,EAAE;QACpBZ,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnD;QACA,IAAI,CAACe,eAAe,EAAE;OACvB,MAAM;QACLhB,OAAO,CAACuE,IAAI,CAAC,mDAAmD,CAAC;;IAErE,CAAC,CAAC,EACFlG,UAAU,CAACsC,KAAK,IAAG;MACjBX,OAAO,CAACW,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAOxC,UAAU,CAAC,MAAMwC,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACM6D,eAAeA,CAAAC,EAAA,EAAkE;IAAA,IAAAC,MAAA;IAAA,OAAAtC,iBAAA,YAAjEN,OAAY,EAAEC,QAAA,GAAmB,CAAC,EAAEqC,IAAa,EAAEC,KAAc;MACrF,IAAI;QACF,MAAMF,SAAS,GAAGrC,OAAO,CAACF,GAAG,IAAIE,OAAO,CAACH,EAAE;QAE3C;QACA,IAAI;UACF,MAAMR,QAAQ,SAASuD,MAAI,CAACR,SAAS,CAACC,SAAS,EAAEpC,QAAQ,EAAEqC,IAAI,EAAEC,KAAK,CAAC,CAACM,SAAS,EAAE;UACnF,IAAIxD,QAAQ,EAAEP,OAAO,EAAE;YACrB,MAAM8D,MAAI,CAACE,SAAS,CAAC,oBAAoB,EAAE,SAAS,CAAC;YACrDF,MAAI,CAAC3E,QAAQ,EAAE,CAAC,CAAC;YACjB,OAAO,IAAI;;SAEd,CAAC,OAAO8E,QAAQ,EAAE;UACjB7E,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAGvD;QACA,MAAM6E,QAAQ,GAAa;UACzBlD,GAAG,EAAE,GAAGuC,SAAS,IAAIC,IAAI,IAAI,SAAS,IAAIC,KAAK,IAAI,SAAS,EAAE;UAC9DvC,OAAO,EAAE;YACPF,GAAG,EAAEuC,SAAS;YACdtC,IAAI,EAAEC,OAAO,CAACD,IAAI;YAClBG,KAAK,EAAEF,OAAO,CAACE,KAAK;YACpB+C,aAAa,EAAEjD,OAAO,CAACiD,aAAa;YACpCC,MAAM,EAAElD,OAAO,CAACkD,MAAM,IAAI,EAAE;YAC5BC,KAAK,EAAEnD,OAAO,CAACmD,KAAK,IAAI,EAAE;YAC1BC,QAAQ,EAAEpD,OAAO,CAACoD;WACnB;UACDnD,QAAQ;UACRqC,IAAI;UACJC,KAAK;UACLc,OAAO,EAAE,IAAIC,IAAI;SAClB;QAED,MAAMC,WAAW,GAAGX,MAAI,CAAC9F,SAAS,CAAC+D,KAAK;QACxC,MAAM2C,iBAAiB,GAAGD,WAAW,CAACE,SAAS,CAAC7D,IAAI,IAClDA,IAAI,CAACI,OAAO,CAACF,GAAG,KAAKuC,SAAS,IAC9B,CAACzC,IAAI,CAAC0C,IAAI,IAAI,SAAS,OAAOA,IAAI,IAAI,SAAS,CAAC,IAChD,CAAC1C,IAAI,CAAC2C,KAAK,IAAI,SAAS,OAAOA,KAAK,IAAI,SAAS,CAAC,CACnD;QAED,IAAIiB,iBAAiB,IAAI,CAAC,EAAE;UAC1BD,WAAW,CAACC,iBAAiB,CAAC,CAACvD,QAAQ,IAAIA,QAAQ;SACpD,MAAM;UACLsD,WAAW,CAACG,IAAI,CAACV,QAAQ,CAAC;;QAG5BJ,MAAI,CAAC9F,SAAS,CAACsC,IAAI,CAACmE,WAAW,CAAC;QAChCX,MAAI,CAACnD,eAAe,EAAE;QACtB,MAAMmD,MAAI,CAAClC,iBAAiB,EAAE;QAC9B,MAAMkC,MAAI,CAACE,SAAS,CAAC,oBAAoB,EAAE,SAAS,CAAC;QACrD,OAAO,IAAI;OAEZ,CAAC,OAAOjE,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAM+D,MAAI,CAACE,SAAS,CAAC,4BAA4B,EAAE,QAAQ,CAAC;QAC5D,OAAO,KAAK;;IACb,GAAAa,KAAA,OAAAC,SAAA;EACH;EAEA;EACAC,cAAcA,CAACC,MAAc;IAC3B,MAAMhG,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMM,OAAO,GAAGR,KAAK,GAAG;MACtBS,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUT,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAACqH,MAAM,CAAwC,GAAG,IAAI,CAAClH,OAAO,oBAAoBiH,MAAM,EAAE,EAAExF,OAAO,CAAC,CAACM,IAAI,CACvHtC,GAAG,CAAC+C,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACP,OAAO,EAAE;QACpBZ,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD;QACA,IAAI,CAACe,eAAe,EAAE;OACvB,MAAM;QACLhB,OAAO,CAACuE,IAAI,CAAC,wDAAwD,CAAC;;IAE1E,CAAC,CAAC,EACFlG,UAAU,CAACsC,KAAK,IAAG;MACjBX,OAAO,CAACW,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAOxC,UAAU,CAAC,MAAMwC,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAmF,kBAAkBA,CAACC,OAAiB;IAClC,MAAMnG,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMM,OAAO,GAAGR,KAAK,GAAG;MACtBoG,IAAI,EAAE;QAAED;MAAO,CAAE;MACjB1F,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUT,KAAK;MAAE;KAC9C,GAAG;MACFoG,IAAI,EAAE;QAAED;MAAO;KAChB;IACD,OAAO,IAAI,CAACvH,IAAI,CAACqH,MAAM,CAA8D,GAAG,IAAI,CAAClH,OAAO,uBAAuB,EAAEyB,OAAO,CAAC,CAACM,IAAI,CACxItC,GAAG,CAAC+C,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACP,OAAO,EAAE;QACpB;QACA,IAAI,CAACI,eAAe,EAAE;;IAE1B,CAAC,CAAC,CACH;EACH;EAEA;EACMiF,oBAAoBA,CAACL,MAAc;IAAA,IAAAM,MAAA;IAAA,OAAA9D,iBAAA;MACvC,IAAI;QACF,MAAMjB,QAAQ,SAAS+E,MAAI,CAACP,cAAc,CAACC,MAAM,CAAC,CAACjB,SAAS,EAAE;QAC9D,IAAIxD,QAAQ,EAAEP,OAAO,EAAE;UACrB,MAAMsF,MAAI,CAACtB,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC;UACzDsB,MAAI,CAACnG,QAAQ,EAAE,CAAC,CAAC;;OAEpB,CAAC,OAAOY,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAMuF,MAAI,CAACtB,SAAS,CAAC,iCAAiC,EAAE,QAAQ,CAAC;;IAClE;EACH;EAEA;EACAuB,cAAcA,CAACP,MAAc,EAAE7D,QAAgB;IAC7C,MAAMnC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMM,OAAO,GAAGR,KAAK,GAAG;MACtBS,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUT,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAAC4H,GAAG,CAAwC,GAAG,IAAI,CAACzH,OAAO,oBAAoBiH,MAAM,EAAE,EAAE;MAAE7D;IAAQ,CAAE,EAAE3B,OAAO,CAAC,CAACM,IAAI,CAClItC,GAAG,CAAC+C,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACP,OAAO,EAAE;QACpB;QACA,IAAI,CAACI,eAAe,EAAE;;IAE1B,CAAC,CAAC,CACH;EACH;EAEA;EACMqF,cAAcA,CAACT,MAAc,EAAE7D,QAAgB;IAAA,IAAAuE,MAAA;IAAA,OAAAlE,iBAAA;MACnD,IAAI;QACF,IAAIL,QAAQ,IAAI,CAAC,EAAE;UACjB,MAAMuE,MAAI,CAACL,oBAAoB,CAACL,MAAM,CAAC;UACvC;;QAGF,MAAMzE,QAAQ,SAASmF,MAAI,CAACH,cAAc,CAACP,MAAM,EAAE7D,QAAQ,CAAC,CAAC4C,SAAS,EAAE;QACxE,IAAIxD,QAAQ,EAAEP,OAAO,EAAE;UACrB0F,MAAI,CAACvG,QAAQ,EAAE,CAAC,CAAC;;OAEpB,CAAC,OAAOY,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAM2F,MAAI,CAAC1B,SAAS,CAAC,2BAA2B,EAAE,QAAQ,CAAC;;IAC5D;EACH;EAEA;EACA2B,YAAYA,CAAA;IACV,MAAM3G,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMM,OAAO,GAAGR,KAAK,GAAG;MACtBS,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUT,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAACqH,MAAM,CAAwC,GAAG,IAAI,CAAClH,OAAO,iBAAiB,EAAEyB,OAAO,CAAC;EAC3G;EAEMoG,SAASA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAArE,iBAAA;MACb,IAAI;QACF,MAAMjB,QAAQ,SAASsF,MAAI,CAACF,YAAY,EAAE,CAAC5B,SAAS,EAAE;QACtD,IAAIxD,QAAQ,EAAEP,OAAO,EAAE;UACrB6F,MAAI,CAAC7H,SAAS,CAACsC,IAAI,CAAC,EAAE,CAAC;UACvBuF,MAAI,CAAC5H,WAAW,CAACqC,IAAI,CAAC,IAAI,CAAC;UAC3BuF,MAAI,CAAClF,eAAe,EAAE;UACtB,MAAMkF,MAAI,CAAC7B,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC;;OAElD,CAAC,OAAOjE,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,MAAM8F,MAAI,CAAC7B,SAAS,CAAC,sBAAsB,EAAE,QAAQ,CAAC;;IACvD;EACH;EAEA8B,YAAYA,CAAA;IACV,MAAMpF,OAAO,GAAG,IAAI,CAACzC,WAAW,CAAC8D,KAAK;IACtC,IAAIrB,OAAO,EAAE;MACX,OAAOA,OAAO,CAACwB,KAAK;;IAGtB;IACA,OAAO,IAAI,CAAClE,SAAS,CAAC+D,KAAK,CAACE,MAAM,CAAC,CAACC,KAAK,EAAEpB,IAAI,KAAI;MACjD,MAAMM,KAAK,GAAGN,IAAI,CAACI,OAAO,CAACE,KAAK;MAChC,OAAOc,KAAK,GAAId,KAAK,GAAGN,IAAI,CAACK,QAAS;IACxC,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACA4E,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC1H,cAAc,CAAC0D,KAAK;EAClC;EAEA;EACAiE,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC9H,aAAa,CAAC6D,KAAK;EACjC;EAEA;EACAkE,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC9H,eAAe,CAAC4D,KAAK;EACnC;EAEA;EACAmE,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAAC9H,kBAAkB,CAAC2D,KAAK;EACtC;EAEAoE,QAAQA,CAAC5C,SAAiB,EAAEC,IAAa,EAAEC,KAAc;IACvD,OAAO,IAAI,CAACzF,SAAS,CAAC+D,KAAK,CAACqE,IAAI,CAACtF,IAAI,IACnCA,IAAI,CAACI,OAAO,CAACF,GAAG,KAAKuC,SAAS,IAC9B,CAACzC,IAAI,CAAC0C,IAAI,IAAI,SAAS,OAAOA,IAAI,IAAI,SAAS,CAAC,IAChD,CAAC1C,IAAI,CAAC2C,KAAK,IAAI,SAAS,OAAOA,KAAK,IAAI,SAAS,CAAC,CACnD;EACH;EAEA4C,WAAWA,CAAC9C,SAAiB,EAAEC,IAAa,EAAEC,KAAc;IAC1D,OAAO,IAAI,CAACzF,SAAS,CAAC+D,KAAK,CAACuE,IAAI,CAACxF,IAAI,IACnCA,IAAI,CAACI,OAAO,CAACF,GAAG,KAAKuC,SAAS,IAC9B,CAACzC,IAAI,CAAC0C,IAAI,IAAI,SAAS,OAAOA,IAAI,IAAI,SAAS,CAAC,IAChD,CAAC1C,IAAI,CAAC2C,KAAK,IAAI,SAAS,OAAOA,KAAK,IAAI,SAAS,CAAC,CACnD;EACH;EAEcO,SAASA,CAAC/D,OAAe,EAAEwD,KAAa;IAAA,IAAA8C,MAAA;IAAA,OAAA/E,iBAAA;MACpD,MAAMgF,KAAK,SAASD,MAAI,CAACzI,eAAe,CAAC2I,MAAM,CAAC;QAC9CxG,OAAO,EAAEA,OAAO;QAChByG,QAAQ,EAAE,IAAI;QACdjD,KAAK,EAAEA,KAAK;QACZkD,QAAQ,EAAE;OACX,CAAC;MACFH,KAAK,CAACI,OAAO,EAAE;IAAC;EAClB;;;uBAlkBWlJ,WAAW,EAAAmJ,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,eAAA;IAAA;EAAA;;;aAAX1J,WAAW;MAAA2J,OAAA,EAAX3J,WAAW,CAAA4J,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}