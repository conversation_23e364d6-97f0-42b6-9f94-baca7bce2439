<div class="suggested-products-container">
  <!-- Header -->
  <div class="section-header">
    <h2 class="section-title">
      <i class="fas fa-star"></i>
      Suggested For You
    </h2>
    <button class="view-all-btn" (click)="router.navigate(['/shop'], { queryParams: { filter: 'suggested' } })">
      View All
      <i class="fas fa-arrow-right"></i>
    </button>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading && products.length === 0">
    <div class="loading-grid">
      <div class="product-skeleton" *ngFor="let item of [1,2,3,4,5,6]">
        <div class="skeleton-image"></div>
        <div class="skeleton-content">
          <div class="skeleton-line"></div>
          <div class="skeleton-line short"></div>
          <div class="skeleton-line"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div class="error-container" *ngIf="error && products.length === 0">
    <div class="error-content">
      <i class="fas fa-exclamation-triangle"></i>
      <h3>Unable to load suggested products</h3>
      <p>{{ error }}</p>
      <button class="retry-btn" (click)="retry()">
        <i class="fas fa-redo"></i>
        Try Again
      </button>
    </div>
  </div>

  <!-- Products Slider -->
  <div class="products-slider-wrapper" *ngIf="products.length > 0">
    <swiper-container
      class="products-swiper"
      [slides-per-view]="swiperConfig.slidesPerView"
      [space-between]="swiperConfig.spaceBetween"
      [navigation]="true"
      [autoplay]="swiperConfig.autoplay"
      [loop]="swiperConfig.loop"
      [breakpoints]="swiperConfig.breakpoints">

      <swiper-slide *ngFor="let product of products; trackBy: trackByProductId">
        <div class="product-card" (click)="viewProduct(product)">
      
      <!-- Product Image -->
      <div class="product-image-container">
        <img [src]="product.images[0].url"
             [alt]="product.name"
             class="product-image"
             loading="lazy">
        
        <!-- Discount Badge -->
        <div class="discount-badge" *ngIf="getDiscountPercentage(product) > 0">
          -{{ getDiscountPercentage(product) }}%
        </div>

        <!-- Wishlist Button -->
        <button class="wishlist-btn" 
                (click)="toggleWishlist(product, $event)"
                title="Add to wishlist">
          <i class="far fa-heart"></i>
        </button>

        <!-- Quick Actions -->
        <div class="quick-actions">
          <button class="quick-action-btn" 
                  (click)="addToCart(product, $event)"
                  title="Add to cart">
            <i class="fas fa-shopping-cart"></i>
          </button>
          <button class="quick-action-btn" 
                  (click)="shareProduct(product, $event)"
                  title="Share">
            <i class="fas fa-share"></i>
          </button>
        </div>
      </div>

      <!-- Product Info -->
      <div class="product-info">
        <!-- Vendor Info -->
        <div class="vendor-info" (click)="viewVendor(product.vendor); $event.stopPropagation()">
          <img [src]="product.vendor.avatar" 
               [alt]="product.vendor.fullName"
               class="vendor-avatar">
          <span class="vendor-name">{{ product.vendor.username }}</span>
          <i class="fas fa-check-circle verified-icon" 
             *ngIf="product.vendor.isInfluencer"
             title="Verified Influencer"></i>
        </div>

        <!-- Product Name -->
        <h3 class="product-name">{{ product.name }}</h3>

        <!-- Price -->
        <div class="price-container">
          <span class="current-price">{{ formatPrice(product.price) }}</span>
          <span class="original-price" *ngIf="product.originalPrice">
            {{ formatPrice(product.originalPrice) }}
          </span>
        </div>

        <!-- Rating -->
        <div class="rating-container" *ngIf="product.rating">
          <div class="stars">
            <i class="fas fa-star" 
               *ngFor="let star of [1,2,3,4,5]"
               [class.filled]="star <= product.rating.average"></i>
          </div>
          <span class="rating-text">
            {{ product.rating.average }} ({{ formatNumber(product.rating.count) }})
          </span>
        </div>

        <!-- Analytics -->
        <div class="analytics-container">
          <div class="analytics-item">
            <i class="fas fa-eye"></i>
            <span>{{ formatNumber(product.analytics.views) }}</span>
          </div>
          <div class="analytics-item">
            <i class="fas fa-heart"></i>
            <span>{{ formatNumber(product.analytics.likes) }}</span>
          </div>
          <div class="analytics-item">
            <i class="fas fa-shopping-bag"></i>
            <span>{{ formatNumber(product.analytics.purchases) }}</span>
          </div>
        </div>
        </div>
      </swiper-slide>
    </swiper-container>
  </div>

  <!-- Load More Button -->
  <div class="load-more-container" *ngIf="hasMore">
    <button class="load-more-btn" 
            (click)="loadMore()"
            [disabled]="isLoading">
      <span *ngIf="!isLoading">Load More Products</span>
      <span *ngIf="isLoading">
        <i class="fas fa-spinner fa-spin"></i>
        Loading...
      </span>
    </button>
  </div>

  <!-- Empty State -->
  <div class="empty-container" *ngIf="products.length === 0 && !isLoading && !error">
    <div class="empty-content">
      <i class="fas fa-shopping-bag"></i>
      <h3>No suggested products available</h3>
      <p>Check back later for personalized product recommendations!</p>
      <button class="browse-btn" (click)="router.navigate(['/shop'])">
        Browse All Products
      </button>
    </div>
  </div>
</div>
