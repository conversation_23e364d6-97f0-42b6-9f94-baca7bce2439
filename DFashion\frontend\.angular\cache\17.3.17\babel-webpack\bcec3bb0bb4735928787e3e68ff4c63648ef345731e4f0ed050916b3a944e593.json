{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport { SlickCarouselModule } from 'ngx-slick-carousel'; // ✅ Add this\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"ngx-slick-carousel\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nconst _c2 = [\"slickModal\"];\nconst _c3 = [\"storiesSlider\"];\nconst _c4 = () => [1, 2, 3, 4, 5];\nfunction ViewAddStoriesComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"div\", 10)(2, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_div_1_Template, 3, 0, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c4));\n  }\n}\nfunction ViewAddStoriesComponent_div_6_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_6_div_6_Template_div_click_0_listener() {\n      const i_r6 = i0.ɵɵrestoreView(_r5).index;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.openStories(i_r6));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const slickModal_r3 = i0.ɵɵreference(4);\n      return i0.ɵɵresetView(slickModal_r3 == null ? null : slickModal_r3.slickPrev());\n    });\n    i0.ɵɵtext(2, \" \\u2190 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ngx-slick-carousel\", 14, 1)(5, \"div\", 15);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_6_Template_div_click_5_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onAdd());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ViewAddStoriesComponent_div_6_div_6_Template, 1, 0, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_6_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const slickModal_r3 = i0.ɵɵreference(4);\n      return i0.ɵɵresetView(slickModal_r3 == null ? null : slickModal_r3.slickNext());\n    });\n    i0.ɵɵtext(8, \" \\u2192 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"config\", ctx_r3.slideConfig);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.visibleStories);\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, authService) {\n    this.router = router;\n    this.http = http;\n    this.authService = authService;\n    this.currentUser = null;\n    this.slideConfig = {\n      slidesToShow: 1,\n      slidesToScroll: 1,\n      infinite: false,\n      arrows: true,\n      nextArrow: '<button class=\"slick-arrow right\">&#8594;</button>',\n      prevArrow: '<button class=\"slick-arrow left\">&#8592;</button>',\n      responsive: [{\n        breakpoint: 768,\n        settings: {\n          slidesToShow: 4\n        }\n      }, {\n        breakpoint: 480,\n        settings: {\n          slidesToShow: 3\n        }\n      }]\n    };\n    this.stories = [];\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    this.subscriptions = [];\n    this.canScrollStoriesLeft = false;\n    this.canScrollStoriesRight = false;\n    // Handler for Add Story button\n    // Modal state\n    this.showAddModal = false;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    // Reel recording state\n    this.isRecording = false;\n    this.recordedChunks = [];\n    this.mediaRecorder = null;\n    this.videoStream = null;\n    this.reelPreviewUrl = null;\n    this.isUploadingReel = false;\n    this.newReelCaption = '';\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.isUploadingStory = false;\n  }\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  // --- Slider Arrow Logic ---\n  scrollStoriesLeft() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({\n        left: -200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  scrollStoriesRight() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({\n        left: 200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  updateStoriesArrows() {\n    if (this.storiesSlider) {\n      const el = this.storiesSlider.nativeElement;\n      this.canScrollStoriesLeft = el.scrollLeft > 0;\n      this.canScrollStoriesRight = el.scrollLeft < el.scrollWidth - el.clientWidth - 1;\n    }\n  }\n  ngAfterViewInit() {\n    // setTimeout(() => this.updateStoriesArrows(), 500);\n    setTimeout(() => {\n      this.updateStoriesArrows();\n      if (!this.slickModal) {\n        console.warn('⚠️ slickModal is not initialized yet.');\n      }\n    }, 500);\n  }\n  // --- Story Logic (unchanged) ---\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(this.http.get(`${environment.apiUrl}/stories`).subscribe({\n      next: response => {\n        if (response.success && response.storyGroups) {\n          this.stories = response.storyGroups;\n          console.log('stories', this.stories);\n        } else {\n          this.loadFallbackStories();\n        }\n        this.isLoadingStories = false;\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.loadFallbackStories();\n        this.isLoadingStories = false;\n      }\n    }));\n  }\n  loadFallbackStories() {\n    this.stories = [\n      // ... (same as before, omitted for brevity)\n    ];\n  }\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n  onTouchEnd(event) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n  updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = dragDelta / window.innerWidth * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  update() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' ? this.currentIndex + 1 : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.update());\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  setupEventListeners() {}\n  removeEventListeners() {}\n  getCurrentStory() {\n    return this.stories[this.currentIndex];\n  }\n  getStoryProgress() {\n    return (this.currentIndex + 1) / this.stories.length * 100;\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  startCameraForReel() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.videoStream = yield navigator.mediaDevices.getUserMedia({\n          video: true,\n          audio: true\n        });\n        const video = document.getElementById('reel-video');\n        if (video) {\n          video.srcObject = _this.videoStream;\n          video.play();\n        }\n      } catch (err) {\n        alert('Could not access camera.');\n        _this.showAddReelModal = false;\n      }\n    })();\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new window.MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = e => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, {\n        type: 'video/webm'\n      });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  submitNewReel() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.reelPreviewUrl) return;\n      _this2.isUploadingReel = true;\n      try {\n        const blob = new Blob(_this2.recordedChunks, {\n          type: 'video/webm'\n        });\n        const formData = new FormData();\n        formData.append('media', blob, 'reel.webm');\n        const uploadRes = yield _this2.http.post('/api/stories/upload', formData).toPromise();\n        const reelPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this2.newReelCaption,\n          isReel: true\n        };\n        yield _this2.http.post('/api/stories', reelPayload).toPromise();\n        _this2.showAddReelModal = false;\n        _this2.loadStories();\n      } catch (err) {\n        alert('Failed to upload reel.');\n      } finally {\n        _this2.isUploadingReel = false;\n        _this2.cleanupReelStream();\n      }\n    })();\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  submitNewStory() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.newStoryFile) return;\n      _this3.isUploadingStory = true;\n      try {\n        const uploadForm = new FormData();\n        uploadForm.append('media', _this3.newStoryFile);\n        const uploadRes = yield _this3.http.post('/api/stories/upload', uploadForm).toPromise();\n        const storyPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this3.newStoryCaption\n        };\n        yield _this3.http.post('/api/stories', storyPayload).toPromise();\n        _this3.showAddStoryModal = false;\n        _this3.loadStories();\n      } catch (err) {\n        alert('Failed to upload story.');\n      } finally {\n        _this3.isUploadingStory = false;\n      }\n    })();\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n  get visibleStories() {\n    if (Array.isArray(this.stories) && this.stories.length > 0 && typeof this.stories[0].stories !== 'undefined') {\n      // Grouped structure: { user, stories: Story[] }[]\n      return this.stories.flatMap(group => Array.isArray(group.stories) ? group.stories : []).filter(s => s.isActive);\n    } else if (Array.isArray(this.stories)) {\n      // Flat array: Story[]\n      return this.stories.filter(s => s.isActive);\n    }\n    return [];\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.slickModal = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 3,\n      consts: [[\"storiesSlider\", \"\"], [\"slickModal\", \"slick-carousel\"], [1, \"stories-container\"], [\"class\", \"stories-loading\", 4, \"ngIf\"], [1, \"arrow\", \"left\", 3, \"click\", \"disabled\"], [1, \"stories-slider\"], [\"class\", \"stories-slider-wrapper\", 4, \"ngIf\"], [1, \"stories-loading\"], [\"class\", \"story-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-skeleton\"], [1, \"skeleton-avatar\"], [1, \"skeleton-name\"], [1, \"stories-slider-wrapper\"], [1, \"arrow\", \"left\", 3, \"click\"], [1, \"stories-slider\", 3, \"config\"], [1, \"story-item\", \"add-story-item\", 3, \"click\"], [\"class\", \"story-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"arrow\", \"right\", 3, \"click\"], [1, \"story-item\", 3, \"click\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2);\n          i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_Template, 2, 2, \"div\", 3);\n          i0.ɵɵelementStart(2, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_Template_button_click_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.scrollStoriesLeft());\n          });\n          i0.ɵɵtext(3, \"\\u2190\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 5, 0);\n          i0.ɵɵtemplate(6, ViewAddStoriesComponent_div_6_Template, 9, 2, \"div\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", !ctx.canScrollStoriesLeft);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingStories && ctx.visibleStories.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, FormsModule, SlickCarouselModule, i5.SlickCarouselComponent],\n      styles: [\".stories-slider-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 24px;\\n  position: relative;\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  overflow-x: auto;\\n  scroll-behavior: smooth;\\n  gap: 15px;\\n  padding: 12px 0;\\n  width: 100%;\\n}\\n\\n.story-item[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 90px;\\n  text-align: center;\\n  margin: 0 auto;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  padding: 2px;\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.add-story-avatar[_ngcontent-%COMP%] {\\n  background: #dbdbdb !important;\\n  position: relative;\\n}\\n\\n.story-avatar-inner[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  border: 2px solid white;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.story-avatar-img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  display: block;\\n}\\n\\n.add-story-plus[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  right: 0;\\n  background: #0095f6;\\n  color: #fff;\\n  border-radius: 50%;\\n  width: 22px;\\n  height: 22px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 1.2rem;\\n  border: 2px solid #fff;\\n  font-weight: bold;\\n}\\n\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin-top: 5px;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  max-width: 100%;\\n  text-align: center;\\n}\\n\\n.arrow[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 2rem;\\n  cursor: pointer;\\n  padding: 0 8px;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.arrow[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n\\n@media (max-width: 614px) {\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    margin-bottom: 8px;\\n  }\\n  .stories-slider[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    padding: 8px 0;\\n  }\\n  .story-item[_ngcontent-%COMP%] {\\n    width: 60px;\\n  }\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .story-avatar-inner[_ngcontent-%COMP%] {\\n    border-width: 1.5px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "environment", "SlickCarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "ViewAddStoriesComponent_div_1_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c4", "ɵɵlistener", "ViewAddStoriesComponent_div_6_div_6_Template_div_click_0_listener", "i_r6", "ɵɵrestoreView", "_r5", "index", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "openStories", "ViewAddStoriesComponent_div_6_Template_button_click_1_listener", "_r2", "slickModal_r3", "ɵɵreference", "slick<PERSON>rev", "ɵɵtext", "ViewAddStoriesComponent_div_6_Template_div_click_5_listener", "onAdd", "ViewAddStoriesComponent_div_6_div_6_Template", "ViewAddStoriesComponent_div_6_Template_button_click_7_listener", "slickNext", "slideConfig", "visibleStories", "ViewAddStoriesComponent", "constructor", "router", "http", "authService", "currentUser", "slidesToShow", "slidesToScroll", "infinite", "arrows", "nextArrow", "prevArrow", "responsive", "breakpoint", "settings", "stories", "isLoadingStories", "currentIndex", "isOpen", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "subscriptions", "canScrollStoriesLeft", "canScrollStoriesRight", "showAddModal", "showAddStoryModal", "showAddReelModal", "showPermissionModal", "showCameraOrGallery", "permissionDenied", "isRecording", "recordedChunks", "mediaRecorder", "videoStream", "reelPreviewUrl", "isUploadingReel", "newReelCaption", "newStoryFile", "newStoryCaption", "isUploadingStory", "ngOnInit", "loadStories", "setupEventListeners", "currentUser$", "subscribe", "user", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "scrollStoriesLeft", "storiesSlider", "nativeElement", "scrollBy", "left", "behavior", "setTimeout", "updateStoriesArrows", "scrollStoriesRight", "el", "scrollLeft", "scrollWidth", "clientWidth", "ngAfterViewInit", "slickModal", "console", "warn", "push", "get", "apiUrl", "next", "response", "success", "storyGroups", "log", "loadFallbackStories", "error", "showStory", "document", "body", "style", "overflow", "closeStories", "pauseAllVideos", "storiesContainer", "classList", "add", "remove", "transform", "nextStory", "length", "update", "previousStory", "handleKeydown", "event", "key", "onStoryClick", "clickX", "clientX", "windowWidth", "window", "innerWidth", "onTouchStart", "touches", "onTouchMove", "updateDragPosition", "onTouchEnd", "<PERSON><PERSON><PERSON><PERSON>", "threshold", "Math", "abs", "newIndex", "requestAnimationFrame", "videos", "querySelectorAll", "video", "pause", "getCurrentStory", "getStoryProgress", "getTimeAgo", "dateString", "now", "Date", "date", "diffInMinutes", "floor", "getTime", "diffInHours", "diffInDays", "formatNumber", "num", "toFixed", "toString", "formatPrice", "price", "Intl", "NumberFormat", "currency", "minimumFractionDigits", "format", "viewProduct", "product", "navigate", "_id", "hasProducts", "story", "products", "getStoryProducts", "onAddStory", "onAddReel", "startCameraForReel", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "audio", "getElementById", "srcObject", "play", "err", "alert", "startRecording", "MediaRecorder", "ondataavailable", "e", "data", "size", "onstop", "blob", "Blob", "type", "URL", "createObjectURL", "start", "stopRecording", "stop", "submitNewReel", "_this2", "formData", "FormData", "append", "uploadRes", "post", "to<PERSON>romise", "reelPayload", "media", "url", "caption", "isReel", "cleanupReelStream", "getTracks", "track", "closeAddReelModal", "handlePermissionResponse", "allow", "openCamera", "input", "setAttribute", "click", "openGallery", "removeAttribute", "onStoryFileSelected", "file", "target", "files", "submitNewStory", "_this3", "uploadForm", "storyPayload", "closeAddStoryModal", "Array", "isArray", "flatMap", "group", "filter", "s", "isActive", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "AuthService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_keydown_HostBindingHandler", "$event", "ɵɵresolveDocument", "ViewAddStoriesComponent_div_1_Template", "ViewAddStoriesComponent_Template_button_click_2_listener", "_r1", "ViewAddStoriesComponent_div_6_Template", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "SlickCarouselComponent", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.html"], "sourcesContent": ["import { Component, On<PERSON>nit, On<PERSON><PERSON>roy, ElementRef, ViewChild, HostListener } \nfrom '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { AuthService } from 'src/app/core/services/auth.service';\nimport { SlickCarouselComponent, SlickCarouselModule } from 'ngx-slick-carousel'; // ✅ Add this\nimport { User } from 'src/app/core/models/user.model';\n\n\ninterface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, FormsModule, SlickCarouselModule ],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n@ViewChild('slickModal') slickModal!: SlickCarouselComponent;\n\n  currentUser: any = null;\nslideConfig = {\n  slidesToShow: 1,\n  slidesToScroll: 1,\n  infinite: false,\n  arrows: true,\n  nextArrow: '<button class=\"slick-arrow right\">&#8594;</button>',\n  prevArrow: '<button class=\"slick-arrow left\">&#8592;</button>',\n  responsive: [\n    {\n      breakpoint: 768,\n      settings: {\n        slidesToShow: 4\n      }\n    },\n    {\n      breakpoint: 480,\n      settings: {\n        slidesToShow: 3\n      }\n    }\n  ]\n};\n\n\n  stories: Story[] = [];\n  isLoadingStories = true;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n  private subscriptions: Subscription[] = [];\n\n  // For slider arrows\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef<HTMLDivElement>;\n  canScrollStoriesLeft = false;\n  canScrollStoriesRight = false;\n\n  constructor(\n    private router: Router,\n    private http: HttpClient,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  // --- Slider Arrow Logic ---\n  scrollStoriesLeft() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({ left: -200, behavior: 'smooth' });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  scrollStoriesRight() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({ left: 200, behavior: 'smooth' });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  updateStoriesArrows() {\n    if (this.storiesSlider) {\n      const el = this.storiesSlider.nativeElement;\n      this.canScrollStoriesLeft = el.scrollLeft > 0;\n      this.canScrollStoriesRight = el.scrollLeft < el.scrollWidth - el.clientWidth - 1;\n    }\n  }\n  ngAfterViewInit() {\n   // setTimeout(() => this.updateStoriesArrows(), 500);\n    setTimeout(() => {\n    this.updateStoriesArrows();\n    if (!this.slickModal) {\n      console.warn('⚠️ slickModal is not initialized yet.');\n    }\n  }, 500);\n  }\n\n  // --- Story Logic (unchanged) ---\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(\n      this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({\n        next: (response) => {\n          if (response.success && response.storyGroups) {\n            this.stories = response.storyGroups;\n            console.log('stories',this.stories)\n          } else {\n            this.loadFallbackStories();\n          }\n          this.isLoadingStories = false;\n        },\n        error: (error) => {\n          console.error('Error loading stories:', error);\n          this.loadFallbackStories();\n          this.isLoadingStories = false;\n        }\n      })\n    );\n  }\n\n  loadFallbackStories() {\n    this.stories = [\n      // ... (same as before, omitted for brevity)\n    ];\n  }\n\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index: number) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n  onTouchEnd(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n  private updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = (dragDelta / window.innerWidth) * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  private update() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' \n          ? this.currentIndex + 1 \n          : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.update());\n  }\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach((video: any) => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  private setupEventListeners() {}\n  private removeEventListeners() {}\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex];\n  }\n  getStoryProgress(): number {\n    return ((this.currentIndex + 1) / this.stories.length) * 100;\n  }\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product: any) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts(): any[] {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  // Handler for Add Story button\n  // Modal state\n  showAddModal = false;\n  showAddStoryModal = false;\n  showAddReelModal = false;\n  showPermissionModal = false;\n  showCameraOrGallery = false;\n  permissionDenied = false;\n  // Reel recording state\n  isRecording = false;\n  recordedChunks: Blob[] = [];\n  mediaRecorder: any = null;\n  videoStream: MediaStream | null = null;\n  reelPreviewUrl: string | null = null;\n  isUploadingReel = false;\n  newReelCaption = '';\n  newStoryFile: File | null = null;\n  newStoryCaption = '';\n  isUploadingStory = false;\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  async startCameraForReel() {\n    try {\n      this.videoStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });\n      const video: any = document.getElementById('reel-video');\n      if (video) {\n        video.srcObject = this.videoStream;\n        video.play();\n      }\n    } catch (err) {\n      alert('Could not access camera.');\n      this.showAddReelModal = false;\n    }\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new (window as any).MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = (e: any) => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  async submitNewReel() {\n    if (!this.reelPreviewUrl) return;\n    this.isUploadingReel = true;\n    try {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      const formData = new FormData();\n      formData.append('media', blob, 'reel.webm');\n      const uploadRes: any = await this.http.post('/api/stories/upload', formData).toPromise();\n      const reelPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newReelCaption,\n        isReel: true\n      };\n      await this.http.post('/api/stories', reelPayload).toPromise();\n      this.showAddReelModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload reel.');\n    } finally {\n      this.isUploadingReel = false;\n      this.cleanupReelStream();\n    }\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow: boolean) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event: any) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  async submitNewStory() {\n    if (!this.newStoryFile) return;\n    this.isUploadingStory = true;\n    try {\n      const uploadForm = new FormData();\n      uploadForm.append('media', this.newStoryFile);\n      const uploadRes: any = await this.http.post('/api/stories/upload', uploadForm).toPromise();\n      const storyPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newStoryCaption\n      };\n      await this.http.post('/api/stories', storyPayload).toPromise();\n      this.showAddStoryModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload story.');\n    } finally {\n      this.isUploadingStory = false;\n    }\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n\n \nget visibleStories(): Story[] {\n  if (\n    Array.isArray(this.stories) &&\n    this.stories.length > 0 &&\n    typeof (this.stories[0] as any).stories !== 'undefined'\n  ) {\n    // Grouped structure: { user, stories: Story[] }[]\n    return (this.stories as any[])\n      .flatMap(group => Array.isArray(group.stories) ? group.stories : [])\n      .filter((s: Story) => s.isActive);\n  } else if (Array.isArray(this.stories)) {\n    // Flat array: Story[]\n    return (this.stories as Story[]).filter(s => s.isActive);\n  }\n  return [];\n}\n\n\n}", "<!-- Instagram-style Stories Bar -->\n<div class=\"stories-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoadingStories\" class=\"stories-loading\">\n    <div *ngFor=\"let item of [1,2,3,4,5]\" class=\"story-skeleton\">\n      <div class=\"skeleton-avatar\"></div>\n      <div class=\"skeleton-name\"></div>\n    </div>\n  </div>\n\n    <button class=\"arrow left\" (click)=\"scrollStoriesLeft()\" [disabled]=\"!canScrollStoriesLeft\">&#8592;</button>\n    <div class=\"stories-slider\" #storiesSlider>\n \n  <!-- Stories Slider with Arrows -->\n \n<div class=\"stories-slider-wrapper\" *ngIf=\"!isLoadingStories && visibleStories.length > 0\">\n  <button class=\"arrow left\" (click)=\"slickModal?.slickPrev()\">\n    &#8592;\n  </button>\n  <ngx-slick-carousel #slickModal=\"slick-carousel\" class=\"stories-slider\" [config]=\"slideConfig\">\n    <div class=\"story-item add-story-item\" (click)=\"onAdd()\">\n      <!-- Add Story Content -->\n    </div>\n    <div *ngFor=\"let story of visibleStories; let i = index\"\n         class=\"story-item\"\n         (click)=\"openStories(i)\">\n      <!-- Story Content -->\n    </div>\n  </ngx-slick-carousel>\n  <button class=\"arrow right\" (click)=\"slickModal?.slickNext()\">\n    &#8594;\n  </button>\n</div>\n\n</div>\n"], "mappings": ";AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,WAAW,QAAQ,8BAA8B;AAE1D,SAAiCC,mBAAmB,QAAQ,oBAAoB,CAAC,CAAC;;;;;;;;;;;;;;ICL9EC,EAAA,CAAAC,cAAA,aAA6D;IAE3DD,EADA,CAAAE,SAAA,cAAmC,cACF;IACnCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAI,UAAA,IAAAC,4CAAA,iBAA6D;IAI/DL,EAAA,CAAAG,YAAA,EAAM;;;IAJkBH,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAc;;;;;;IAmBpCT,EAAA,CAAAC,cAAA,cAE8B;IAAzBD,EAAA,CAAAU,UAAA,mBAAAC,kEAAA;MAAA,MAAAC,IAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,IAAA,CAAc;IAAA,EAAC;IAE7BZ,EAAA,CAAAG,YAAA,EAAM;;;;;;IAXRH,EADF,CAAAC,cAAA,cAA2F,iBAC5B;IAAlCD,EAAA,CAAAU,UAAA,mBAAAU,+DAAA;MAAApB,EAAA,CAAAa,aAAA,CAAAQ,GAAA;MAAA,MAAAC,aAAA,GAAAtB,EAAA,CAAAuB,WAAA;MAAA,OAAAvB,EAAA,CAAAkB,WAAA,CAAAI,aAAA,kBAAAA,aAAA,CAAAE,SAAA;IAAA,EAAiC;IAC1DxB,EAAA,CAAAyB,MAAA,eACF;IAAAzB,EAAA,CAAAG,YAAA,EAAS;IAEPH,EADF,CAAAC,cAAA,gCAA+F,cACpC;IAAlBD,EAAA,CAAAU,UAAA,mBAAAgB,4DAAA;MAAA1B,EAAA,CAAAa,aAAA,CAAAQ,GAAA;MAAA,MAAAL,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAW,KAAA,EAAO;IAAA,EAAC;IAExD3B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,UAAA,IAAAwB,4CAAA,kBAE8B;IAGhC5B,EAAA,CAAAG,YAAA,EAAqB;IACrBH,EAAA,CAAAC,cAAA,iBAA8D;IAAlCD,EAAA,CAAAU,UAAA,mBAAAmB,+DAAA;MAAA7B,EAAA,CAAAa,aAAA,CAAAQ,GAAA;MAAA,MAAAC,aAAA,GAAAtB,EAAA,CAAAuB,WAAA;MAAA,OAAAvB,EAAA,CAAAkB,WAAA,CAAAI,aAAA,kBAAAA,aAAA,CAAAQ,SAAA;IAAA,EAAiC;IAC3D9B,EAAA,CAAAyB,MAAA,eACF;IACFzB,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAboEH,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAO,UAAA,WAAAS,MAAA,CAAAe,WAAA,CAAsB;IAIrE/B,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAO,UAAA,YAAAS,MAAA,CAAAgB,cAAA,CAAmB;;;ADoB9C,OAAM,MAAOC,uBAAuB;EAoDlCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB;IAFxB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAlDrB,KAAAC,WAAW,GAAQ,IAAI;IACzB,KAAAP,WAAW,GAAG;MACZQ,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE,oDAAoD;MAC/DC,SAAS,EAAE,mDAAmD;MAC9DC,UAAU,EAAE,CACV;QACEC,UAAU,EAAE,GAAG;QACfC,QAAQ,EAAE;UACRR,YAAY,EAAE;;OAEjB,EACD;QACEO,UAAU,EAAE,GAAG;QACfC,QAAQ,EAAE;UACRR,YAAY,EAAE;;OAEjB;KAEJ;IAGC,KAAAS,OAAO,GAAY,EAAE;IACrB,KAAAC,gBAAgB,GAAG,IAAI;IAEvB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IACjD,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IACX,KAAAC,aAAa,GAAmB,EAAE;IAI1C,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,qBAAqB,GAAG,KAAK;IAoQ7B;IACA;IACA,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,gBAAgB,GAAG,KAAK;IACxB;IACA,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,cAAc,GAAkB,IAAI;IACpC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,YAAY,GAAgB,IAAI;IAChC,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,gBAAgB,GAAG,KAAK;EAhRrB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAAC9C,WAAW,CAAC+C,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAAChD,WAAW,GAAGgD,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACzB,aAAa,CAAC0B,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEA;EACAC,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,QAAQ,CAAC;QAAEC,IAAI,EAAE,CAAC,GAAG;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAC7EC,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EACAC,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACP,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,QAAQ,CAAC;QAAEC,IAAI,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAC5EC,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EACAA,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACN,aAAa,EAAE;MACtB,MAAMQ,EAAE,GAAG,IAAI,CAACR,aAAa,CAACC,aAAa;MAC3C,IAAI,CAAC/B,oBAAoB,GAAGsC,EAAE,CAACC,UAAU,GAAG,CAAC;MAC7C,IAAI,CAACtC,qBAAqB,GAAGqC,EAAE,CAACC,UAAU,GAAGD,EAAE,CAACE,WAAW,GAAGF,EAAE,CAACG,WAAW,GAAG,CAAC;;EAEpF;EACAC,eAAeA,CAAA;IACd;IACCP,UAAU,CAAC,MAAK;MAChB,IAAI,CAACC,mBAAmB,EAAE;MAC1B,IAAI,CAAC,IAAI,CAACO,UAAU,EAAE;QACpBC,OAAO,CAACC,IAAI,CAAC,uCAAuC,CAAC;;IAEzD,CAAC,EAAE,GAAG,CAAC;EACP;EAEA;EACA1B,WAAWA,CAAA;IACT,IAAI,CAACjC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACa,aAAa,CAAC+C,IAAI,CACrB,IAAI,CAACzE,IAAI,CAAC0E,GAAG,CAAM,GAAGhH,WAAW,CAACiH,MAAM,UAAU,CAAC,CAAC1B,SAAS,CAAC;MAC5D2B,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,EAAE;UAC5C,IAAI,CAACnE,OAAO,GAAGiE,QAAQ,CAACE,WAAW;UACnCR,OAAO,CAACS,GAAG,CAAC,SAAS,EAAC,IAAI,CAACpE,OAAO,CAAC;SACpC,MAAM;UACL,IAAI,CAACqE,mBAAmB,EAAE;;QAE5B,IAAI,CAACpE,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACDqE,KAAK,EAAGA,KAAK,IAAI;QACfX,OAAO,CAACW,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACD,mBAAmB,EAAE;QAC1B,IAAI,CAACpE,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC,CACH;EACH;EAEAoE,mBAAmBA,CAAA;IACjB,IAAI,CAACrE,OAAO,GAAG;MACb;IAAA,CACD;EACH;EAEA7B,WAAWA,CAACJ,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAACmC,YAAY,GAAGnC,KAAK;IACzB,IAAI,CAACoC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACoE,SAAS,CAACxG,KAAK,CAAC;IACrByG,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EACAC,YAAYA,CAAA;IACV,IAAI,CAACzE,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC0E,cAAc,EAAE;IACrBL,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IACrC,IAAI,IAAI,CAACG,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAChC,aAAa,CAACiC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAEhE9B,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAAC4B,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAAChC,aAAa,CAACiC,SAAS,CAACE,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EACAV,SAASA,CAACxG,KAAa;IACrB,IAAI,CAACmC,YAAY,GAAGnC,KAAK;IACzB,IAAI,CAACuC,OAAO,GAAG,CAAC;IAChB,IAAI,IAAI,CAACwE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAChC,aAAa,CAAC4B,KAAK,CAACQ,SAAS,GAAG,mBAAmB;;EAE7E;EACAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACjF,YAAY,GAAG,IAAI,CAACF,OAAO,CAACoF,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAAC7E,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACiF,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACT,YAAY,EAAE;;EAEvB;EACAU,aAAaA,CAAA;IACX,IAAI,IAAI,CAACpF,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACK,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACiF,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACT,YAAY,EAAE;;EAEvB;EAEAW,aAAaA,CAACC,KAAoB;IAChC,IAAI,CAAC,IAAI,CAACrF,MAAM,EAAE;IAClB,QAAQqF,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACH,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACH,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACP,YAAY,EAAE;QACnB;;EAEN;EACAc,YAAYA,CAACF,KAAiB;IAC5B,IAAI,IAAI,CAACpF,UAAU,EAAE;IACrB,MAAMuF,MAAM,GAAGH,KAAK,CAACI,OAAO;IAC5B,MAAMC,WAAW,GAAGC,MAAM,CAACC,UAAU;IACrC,IAAIJ,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACP,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACH,SAAS,EAAE;;EAEpB;EACAa,YAAYA,CAACR,KAAiB;IAC5B,IAAI,CAACnF,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAG+E,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACL,OAAO;IAC1C,IAAI,CAAClF,YAAY,GAAG,IAAI,CAACD,UAAU;EACrC;EACAyF,WAAWA,CAACV,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAACnF,UAAU,EAAE;IACtB,IAAI,CAACK,YAAY,GAAG8E,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACL,OAAO;IAC5C,IAAI,CAACO,kBAAkB,EAAE;EAC3B;EACAC,UAAUA,CAACZ,KAAiB;IAC1B,IAAI,CAAC,IAAI,CAACnF,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,GAAG,KAAK;IACvB,MAAMgG,SAAS,GAAG,IAAI,CAAC3F,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,MAAM6F,SAAS,GAAGR,MAAM,CAACC,UAAU,GAAG,IAAI,CAACpF,0BAA0B;IACrE,IAAI4F,IAAI,CAACC,GAAG,CAACH,SAAS,CAAC,GAAGC,SAAS,EAAE;MACnC,IAAID,SAAS,GAAG,CAAC,EAAE;QACjB,IAAI,CAACf,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACH,SAAS,EAAE;;KAEnB,MAAM;MACL,IAAI,CAAC5E,aAAa,GAAG,CAAC;MACtB,IAAI,CAACH,UAAU,GAAG,IAAI;MACtB,IAAI,CAACiF,MAAM,EAAE;;EAEjB;EACQc,kBAAkBA,CAAA;IACxB,MAAME,SAAS,GAAG,IAAI,CAAC3F,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,IAAI,CAACH,OAAO,GAAI+F,SAAS,GAAGP,MAAM,CAACC,UAAU,GAAI,EAAE;IACnD,IAAI,IAAI,CAACjB,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAChC,aAAa,CAAC4B,KAAK,CAACQ,SAAS,GACjD,6BAA6B,IAAI,CAAC5E,OAAO,MAAM;;EAErD;EACQ+E,MAAMA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACjF,UAAU,EAAE;IACtB,IAAI,CAACE,OAAO,IAAI,CAAC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,OAAO,IAAI,IAAI,CAACO,eAAe;IAC1E,IAAI0F,IAAI,CAACC,GAAG,CAAC,IAAI,CAAClG,OAAO,GAAG,IAAI,CAACC,aAAa,CAAC,GAAG,GAAG,EAAE;MACrD,IAAI,CAACD,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MACvB,IAAI,IAAI,CAACI,eAAe,EAAE;QACxB,MAAMiG,QAAQ,GAAG,IAAI,CAACjG,eAAe,KAAK,SAAS,GAC/C,IAAI,CAACN,YAAY,GAAG,CAAC,GACrB,IAAI,CAACA,YAAY,GAAG,CAAC;QACzB,IAAI,CAACqE,SAAS,CAACkC,QAAQ,CAAC;QACxB,IAAI,CAACjG,eAAe,GAAG,IAAI;;MAE7B;;IAEF,IAAI,IAAI,CAACsE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAChC,aAAa,CAAC4B,KAAK,CAACQ,SAAS,GACjD,6BAA6B,IAAI,CAAC5E,OAAO,MAAM;;IAEnDoG,qBAAqB,CAAC,MAAM,IAAI,CAACrB,MAAM,EAAE,CAAC;EAC5C;EACQR,cAAcA,CAAA;IACpB,MAAM8B,MAAM,GAAGnC,QAAQ,CAACoC,gBAAgB,CAAC,eAAe,CAAC;IACzDD,MAAM,CAACnE,OAAO,CAAEqE,KAAU,IAAI;MAC5B,IAAIA,KAAK,CAACC,KAAK,EAAE;QACfD,KAAK,CAACC,KAAK,EAAE;;IAEjB,CAAC,CAAC;EACJ;EACQ3E,mBAAmBA,CAAA,GAAI;EACvBQ,oBAAoBA,CAAA,GAAI;EAChCoE,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC/G,OAAO,CAAC,IAAI,CAACE,YAAY,CAAC;EACxC;EACA8G,gBAAgBA,CAAA;IACd,OAAQ,CAAC,IAAI,CAAC9G,YAAY,GAAG,CAAC,IAAI,IAAI,CAACF,OAAO,CAACoF,MAAM,GAAI,GAAG;EAC9D;EACA6B,UAAUA,CAACC,UAAkB;IAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMI,aAAa,GAAGf,IAAI,CAACgB,KAAK,CAAC,CAACJ,GAAG,CAACK,OAAO,EAAE,GAAGH,IAAI,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAChF,IAAIF,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAClD,MAAMG,WAAW,GAAGlB,IAAI,CAACgB,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIG,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAC9C,MAAMC,UAAU,GAAGnB,IAAI,CAACgB,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EACAC,YAAYA,CAACC,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EACAC,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCxD,KAAK,EAAE,UAAU;MACjByD,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EAClB;EACAM,WAAWA,CAACC,OAAY;IACtB,IAAI,CAACpJ,MAAM,CAACqJ,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACE,GAAG,CAAC,CAAC;EACjD;EACAC,WAAWA,CAAA;IACT,MAAMC,KAAK,GAAG,IAAI,CAAC5B,eAAe,EAAE;IACpC,OAAO,CAAC,EAAE4B,KAAK,IAAIA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACxD,MAAM,GAAG,CAAC,CAAC;EACjE;EACAyD,gBAAgBA,CAAA;IACd,MAAMF,KAAK,GAAG,IAAI,CAAC5B,eAAe,EAAE;IACpC,OAAO4B,KAAK,EAAEC,QAAQ,IAAI,EAAE;EAC9B;EAoBAjK,KAAKA,CAAA;IACH,IAAI,CAACsC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACQ,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;EAC1B;EACAiH,UAAUA,CAAA;IACR,IAAI,CAAC7H,YAAY,GAAG,KAAK;IACzB,IAAI,CAACG,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACJ,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACG,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACS,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;EAC3B;EACAgH,SAASA,CAAA;IACP,IAAI,CAAC9H,YAAY,GAAG,KAAK;IACzB,IAAI,CAACE,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACQ,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;IACxBqB,UAAU,CAAC,MAAM,IAAI,CAAC8F,kBAAkB,EAAE,EAAE,GAAG,CAAC;EAClD;EACMA,kBAAkBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,IAAI;QACFD,KAAI,CAACvH,WAAW,SAASyH,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UAAExC,KAAK,EAAE,IAAI;UAAEyC,KAAK,EAAE;QAAI,CAAE,CAAC;QAC1F,MAAMzC,KAAK,GAAQrC,QAAQ,CAAC+E,cAAc,CAAC,YAAY,CAAC;QACxD,IAAI1C,KAAK,EAAE;UACTA,KAAK,CAAC2C,SAAS,GAAGP,KAAI,CAACvH,WAAW;UAClCmF,KAAK,CAAC4C,IAAI,EAAE;;OAEf,CAAC,OAAOC,GAAG,EAAE;QACZC,KAAK,CAAC,0BAA0B,CAAC;QACjCV,KAAI,CAAC9H,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACAyI,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAClI,WAAW,EAAE;IACvB,IAAI,CAACF,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,aAAa,GAAG,IAAKqE,MAAc,CAAC+D,aAAa,CAAC,IAAI,CAACnI,WAAW,CAAC;IACxE,IAAI,CAACD,aAAa,CAACqI,eAAe,GAAIC,CAAM,IAAI;MAC9C,IAAIA,CAAC,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE,IAAI,CAACzI,cAAc,CAACqC,IAAI,CAACkG,CAAC,CAACC,IAAI,CAAC;IACvD,CAAC;IACD,IAAI,CAACvI,aAAa,CAACyI,MAAM,GAAG,MAAK;MAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,IAAI,CAAC5I,cAAc,EAAE;QAAE6I,IAAI,EAAE;MAAY,CAAE,CAAC;MAClE,IAAI,CAAC1I,cAAc,GAAG2I,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACjD,CAAC;IACD,IAAI,CAAC1I,aAAa,CAAC+I,KAAK,EAAE;IAC1B,IAAI,CAACjJ,WAAW,GAAG,IAAI;EACzB;EACAkJ,aAAaA,CAAA;IACX,IAAI,IAAI,CAAChJ,aAAa,IAAI,IAAI,CAACF,WAAW,EAAE;MAC1C,IAAI,CAACE,aAAa,CAACiJ,IAAI,EAAE;MACzB,IAAI,CAACnJ,WAAW,GAAG,KAAK;;EAE5B;EACMoJ,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA1B,iBAAA;MACjB,IAAI,CAAC0B,MAAI,CAACjJ,cAAc,EAAE;MAC1BiJ,MAAI,CAAChJ,eAAe,GAAG,IAAI;MAC3B,IAAI;QACF,MAAMuI,IAAI,GAAG,IAAIC,IAAI,CAACQ,MAAI,CAACpJ,cAAc,EAAE;UAAE6I,IAAI,EAAE;QAAY,CAAE,CAAC;QAClE,MAAMQ,QAAQ,GAAG,IAAIC,QAAQ,EAAE;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEZ,IAAI,EAAE,WAAW,CAAC;QAC3C,MAAMa,SAAS,SAAcJ,MAAI,CAACxL,IAAI,CAAC6L,IAAI,CAAC,qBAAqB,EAAEJ,QAAQ,CAAC,CAACK,SAAS,EAAE;QACxF,MAAMC,WAAW,GAAG;UAClBC,KAAK,EAAE;YACLf,IAAI,EAAEW,SAAS,CAACX,IAAI;YACpBgB,GAAG,EAAEL,SAAS,CAACK;WAChB;UACDC,OAAO,EAAEV,MAAI,CAAC/I,cAAc;UAC5B0J,MAAM,EAAE;SACT;QACD,MAAMX,MAAI,CAACxL,IAAI,CAAC6L,IAAI,CAAC,cAAc,EAAEE,WAAW,CAAC,CAACD,SAAS,EAAE;QAC7DN,MAAI,CAACzJ,gBAAgB,GAAG,KAAK;QAC7ByJ,MAAI,CAAC1I,WAAW,EAAE;OACnB,CAAC,OAAOwH,GAAG,EAAE;QACZC,KAAK,CAAC,wBAAwB,CAAC;OAChC,SAAS;QACRiB,MAAI,CAAChJ,eAAe,GAAG,KAAK;QAC5BgJ,MAAI,CAACY,iBAAiB,EAAE;;IACzB;EACH;EACAA,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC9J,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC+J,SAAS,EAAE,CAACjJ,OAAO,CAACkJ,KAAK,IAAIA,KAAK,CAAChB,IAAI,EAAE,CAAC;MAC3D,IAAI,CAAChJ,WAAW,GAAG,IAAI;;IAEzB,IAAI,CAACD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACF,WAAW,GAAG,KAAK;IACxB,IAAI,CAACI,cAAc,GAAG,IAAI;EAC5B;EACAgK,iBAAiBA,CAAA;IACf,IAAI,CAACxK,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACqK,iBAAiB,EAAE;EAC1B;EACAI,wBAAwBA,CAACC,KAAc;IACrC,IAAI,CAACzK,mBAAmB,GAAG,KAAK;IAChC,IAAIyK,KAAK,EAAE;MACT,IAAI,CAACxK,mBAAmB,GAAG,IAAI;KAChC,MAAM;MACL,IAAI,CAACC,gBAAgB,GAAG,IAAI;;EAEhC;EACAwK,UAAUA,CAAA;IACR,IAAI,CAACzK,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,iBAAiB,GAAG,IAAI;IAC7BgC,UAAU,CAAC,MAAK;MACd,MAAM6I,KAAK,GAAQvH,QAAQ,CAAC+E,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIwC,KAAK,EAAE;QACTA,KAAK,CAACC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC;QAC5CD,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACAC,WAAWA,CAAA;IACT,IAAI,CAAC7K,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,iBAAiB,GAAG,IAAI;IAC7BgC,UAAU,CAAC,MAAK;MACd,MAAM6I,KAAK,GAAQvH,QAAQ,CAAC+E,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIwC,KAAK,EAAE;QACTA,KAAK,CAACI,eAAe,CAAC,SAAS,CAAC;QAChCJ,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACAG,mBAAmBA,CAAC5G,KAAU;IAC5B,MAAM6G,IAAI,GAAG7G,KAAK,CAAC8G,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAACvK,YAAY,GAAGuK,IAAI;;EAE5B;EACMG,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAvD,iBAAA;MAClB,IAAI,CAACuD,MAAI,CAAC3K,YAAY,EAAE;MACxB2K,MAAI,CAACzK,gBAAgB,GAAG,IAAI;MAC5B,IAAI;QACF,MAAM0K,UAAU,GAAG,IAAI5B,QAAQ,EAAE;QACjC4B,UAAU,CAAC3B,MAAM,CAAC,OAAO,EAAE0B,MAAI,CAAC3K,YAAY,CAAC;QAC7C,MAAMkJ,SAAS,SAAcyB,MAAI,CAACrN,IAAI,CAAC6L,IAAI,CAAC,qBAAqB,EAAEyB,UAAU,CAAC,CAACxB,SAAS,EAAE;QAC1F,MAAMyB,YAAY,GAAG;UACnBvB,KAAK,EAAE;YACLf,IAAI,EAAEW,SAAS,CAACX,IAAI;YACpBgB,GAAG,EAAEL,SAAS,CAACK;WAChB;UACDC,OAAO,EAAEmB,MAAI,CAAC1K;SACf;QACD,MAAM0K,MAAI,CAACrN,IAAI,CAAC6L,IAAI,CAAC,cAAc,EAAE0B,YAAY,CAAC,CAACzB,SAAS,EAAE;QAC9DuB,MAAI,CAACvL,iBAAiB,GAAG,KAAK;QAC9BuL,MAAI,CAACvK,WAAW,EAAE;OACnB,CAAC,OAAOwH,GAAG,EAAE;QACZC,KAAK,CAAC,yBAAyB,CAAC;OACjC,SAAS;QACR8C,MAAI,CAACzK,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACA4K,kBAAkBA,CAAA;IAChB,IAAI,CAAC1L,iBAAiB,GAAG,KAAK;EAChC;EAGF,IAAIlC,cAAcA,CAAA;IAChB,IACE6N,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC9M,OAAO,CAAC,IAC3B,IAAI,CAACA,OAAO,CAACoF,MAAM,GAAG,CAAC,IACvB,OAAQ,IAAI,CAACpF,OAAO,CAAC,CAAC,CAAS,CAACA,OAAO,KAAK,WAAW,EACvD;MACA;MACA,OAAQ,IAAI,CAACA,OAAiB,CAC3B+M,OAAO,CAACC,KAAK,IAAIH,KAAK,CAACC,OAAO,CAACE,KAAK,CAAChN,OAAO,CAAC,GAAGgN,KAAK,CAAChN,OAAO,GAAG,EAAE,CAAC,CACnEiN,MAAM,CAAEC,CAAQ,IAAKA,CAAC,CAACC,QAAQ,CAAC;KACpC,MAAM,IAAIN,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC9M,OAAO,CAAC,EAAE;MACtC;MACA,OAAQ,IAAI,CAACA,OAAmB,CAACiN,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC;;IAE1D,OAAO,EAAE;EACX;;;uBA5falO,uBAAuB,EAAAjC,EAAA,CAAAoQ,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAtQ,EAAA,CAAAoQ,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAxQ,EAAA,CAAAoQ,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAvBzO,uBAAuB;MAAA0O,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UAAvB9Q,EAAA,CAAAU,UAAA,qBAAAsQ,mDAAAC,MAAA;YAAA,OAAAF,GAAA,CAAAxI,aAAA,CAAA0I,MAAA,CAAqB;UAAA,UAAAjR,EAAA,CAAAkR,iBAAA,CAAE;;;;;;;;;;;UC1CpClR,EAAA,CAAAC,cAAA,aAA+B;UAE7BD,EAAA,CAAAI,UAAA,IAAA+Q,sCAAA,iBAAsD;UAOpDnR,EAAA,CAAAC,cAAA,gBAA4F;UAAjED,EAAA,CAAAU,UAAA,mBAAA0Q,yDAAA;YAAApR,EAAA,CAAAa,aAAA,CAAAwQ,GAAA;YAAA,OAAArR,EAAA,CAAAkB,WAAA,CAAS6P,GAAA,CAAAnL,iBAAA,EAAmB;UAAA,EAAC;UAAoC5F,EAAA,CAAAyB,MAAA,aAAO;UAAAzB,EAAA,CAAAG,YAAA,EAAS;UAC5GH,EAAA,CAAAC,cAAA,gBAA2C;UAI/CD,EAAA,CAAAI,UAAA,IAAAkR,sCAAA,iBAA2F;UAmB3FtR,EAAA,CAAAG,YAAA,EAAM,EAjCyB;;;UAEvBH,EAAA,CAAAM,SAAA,EAAsB;UAAtBN,EAAA,CAAAO,UAAA,SAAAwQ,GAAA,CAAA9N,gBAAA,CAAsB;UAO+BjD,EAAA,CAAAM,SAAA,EAAkC;UAAlCN,EAAA,CAAAO,UAAA,cAAAwQ,GAAA,CAAAhN,oBAAA,CAAkC;UAK1D/D,EAAA,CAAAM,SAAA,GAAoD;UAApDN,EAAA,CAAAO,UAAA,UAAAwQ,GAAA,CAAA9N,gBAAA,IAAA8N,GAAA,CAAA/O,cAAA,CAAAoG,MAAA,KAAoD;;;qBDwB7ExI,YAAY,EAAA2R,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE5R,WAAW,EAAEE,mBAAmB,EAAA2R,EAAA,CAAAC,sBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}