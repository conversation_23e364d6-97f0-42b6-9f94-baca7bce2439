import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';

@Component({
  selector: 'app-payment-failed',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="payment-failed-container">
      <div class="failed-card">
        <!-- Failed Icon -->
        <div class="failed-icon">
          <div class="error-circle">
            <div class="error-cross"></div>
          </div>
        </div>

        <!-- Failed Message -->
        <div class="failed-content">
          <h1>Payment Failed</h1>
          <p class="failed-message">
            We're sorry, but your payment could not be processed. Please try again or use a different payment method.
          </p>

          <!-- Error Details -->
          <div class="error-details" *ngIf="errorReason">
            <h3>Error Details</h3>
            <div class="detail-row">
              <span class="label">Reason:</span>
              <span class="value">{{ errorReason }}</span>
            </div>
            <div class="detail-row" *ngIf="orderId">
              <span class="label">Order ID:</span>
              <span class="value">{{ orderId }}</span>
            </div>
            <div class="detail-row">
              <span class="label">Time:</span>
              <span class="value">{{ failureTime | date:'medium' }}</span>
            </div>
          </div>

          <!-- Support Information -->
          <div class="support-info">
            <i class="fas fa-info-circle"></i>
            <p>If you continue to experience issues, please contact our support team.</p>
          </div>

          <!-- Action Buttons -->
          <div class="action-buttons">
            <button class="btn btn-primary" (click)="retryPayment()">
              <i class="fas fa-redo"></i>
              Try Again
            </button>
            
            <button class="btn btn-secondary" (click)="goToCart()">
              <i class="fas fa-shopping-cart"></i>
              Back to Cart
            </button>
            
            <button class="btn btn-outline" (click)="continueShopping()">
              <i class="fas fa-shopping-bag"></i>
              Continue Shopping
            </button>
          </div>

          <!-- Common Issues -->
          <div class="common-issues">
            <h3>Common Issues & Solutions</h3>
            <div class="issue-item">
              <i class="fas fa-credit-card"></i>
              <div>
                <h4>Insufficient Funds</h4>
                <p>Please check your account balance or try a different card.</p>
              </div>
            </div>
            
            <div class="issue-item">
              <i class="fas fa-shield-alt"></i>
              <div>
                <h4>Security Check</h4>
                <p>Your bank may have blocked the transaction. Please contact your bank.</p>
              </div>
            </div>
            
            <div class="issue-item">
              <i class="fas fa-wifi"></i>
              <div>
                <h4>Network Issues</h4>
                <p>Check your internet connection and try again.</p>
              </div>
            </div>
            
            <div class="issue-item">
              <i class="fas fa-clock"></i>
              <div>
                <h4>Session Timeout</h4>
                <p>Your session may have expired. Please start the checkout process again.</p>
              </div>
            </div>
          </div>

          <!-- Contact Support -->
          <div class="contact-support">
            <h3>Need Help?</h3>
            <p>Our customer support team is here to help you complete your purchase.</p>
            <div class="support-options">
              <a href="mailto:<EMAIL>" class="support-option">
                <i class="fas fa-envelope"></i>
                <span>Email Support</span>
              </a>
              <a href="tel:+919876543210" class="support-option">
                <i class="fas fa-phone"></i>
                <span>Call Support</span>
              </a>
              <button class="support-option" (click)="openChat()">
                <i class="fas fa-comments"></i>
                <span>Live Chat</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./payment-failed.component.scss']
})
export class PaymentFailedComponent implements OnInit {
  orderId: string = '';
  errorReason: string = '';
  failureTime: Date = new Date();

  constructor(
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit() {
    // Get error details from query parameters
    this.route.queryParams.subscribe(params => {
      this.orderId = params['orderId'] || '';
      this.errorReason = params['reason'] || 'Payment processing failed';
    });
  }

  retryPayment() {
    if (this.orderId) {
      this.router.navigate(['/checkout'], { queryParams: { retry: this.orderId } });
    } else {
      this.router.navigate(['/checkout']);
    }
  }

  goToCart() {
    this.router.navigate(['/cart']);
  }

  continueShopping() {
    this.router.navigate(['/']);
  }

  openChat() {
    // Implement live chat functionality
    alert('Live chat feature coming soon! Please use email or phone support for now.');
  }
}
