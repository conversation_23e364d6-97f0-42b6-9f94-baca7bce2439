// Payment Failed Page Styles
.payment-failed-container {
  min-height: calc(100vh - 80px);
  background: linear-gradient(135deg, #f5576c 0%, #f093fb 100%);
  padding: 40px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.failed-card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  text-align: center;
}

// Failed Icon Animation
.failed-icon {
  margin-bottom: 30px;
}

.error-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f5576c 0%, #f093fb 100%);
  margin: 0 auto;
  position: relative;
  animation: scaleIn 0.6s ease-out;
}

.error-cross {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;

  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 4px;
    height: 30px;
    background: white;
    border-radius: 2px;
  }

  &::before {
    transform: translate(-50%, -50%) rotate(45deg);
    animation: crossDraw1 0.4s ease-out 0.3s both;
  }

  &::after {
    transform: translate(-50%, -50%) rotate(-45deg);
    animation: crossDraw2 0.4s ease-out 0.5s both;
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes crossDraw1 {
  0% {
    height: 0;
  }
  100% {
    height: 30px;
  }
}

@keyframes crossDraw2 {
  0% {
    height: 0;
  }
  100% {
    height: 30px;
  }
}

// Failed Content
.failed-content {
  h1 {
    font-size: 32px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 16px;
    animation: fadeInUp 0.6s ease-out 0.2s both;
  }

  .failed-message {
    font-size: 16px;
    color: #718096;
    line-height: 1.6;
    margin-bottom: 30px;
    animation: fadeInUp 0.6s ease-out 0.4s both;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// Error Details
.error-details {
  background: #fef5e7;
  border: 1px solid #f6ad55;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
  text-align: left;
  animation: fadeInUp 0.6s ease-out 0.6s both;

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #c05621;
    margin-bottom: 16px;
    text-align: center;
  }

  .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #fed7aa;

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-weight: 500;
      color: #9c4221;
    }

    .value {
      font-weight: 600;
      color: #c05621;
    }
  }
}

// Support Info
.support-info {
  background: #e6fffa;
  border: 1px solid #81e6d9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  gap: 12px;
  animation: fadeInUp 0.6s ease-out 0.8s both;

  i {
    color: #319795;
    font-size: 20px;
  }

  p {
    margin: 0;
    color: #2c7a7b;
    font-size: 14px;
  }
}

// Action Buttons
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 40px;
  animation: fadeInUp 0.6s ease-out 1s both;

  .btn {
    padding: 14px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    border: none;

    i {
      font-size: 14px;
    }
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
  }

  .btn-secondary {
    background: #4a5568;
    color: white;

    &:hover {
      background: #2d3748;
      transform: translateY(-2px);
    }
  }

  .btn-outline {
    background: transparent;
    border: 2px solid #f5576c;
    color: #f5576c;

    &:hover {
      background: #f5576c;
      color: white;
      transform: translateY(-2px);
    }
  }
}

// Common Issues
.common-issues {
  text-align: left;
  margin-bottom: 40px;
  animation: fadeInUp 0.6s ease-out 1.2s both;

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 20px;
    text-align: center;
  }

  .issue-item {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;

    i {
      color: #f5576c;
      font-size: 20px;
      margin-top: 4px;
      flex-shrink: 0;
    }

    h4 {
      font-size: 16px;
      font-weight: 600;
      color: #2d3748;
      margin: 0 0 8px 0;
    }

    p {
      font-size: 14px;
      color: #718096;
      margin: 0;
      line-height: 1.5;
    }
  }
}

// Contact Support
.contact-support {
  text-align: center;
  animation: fadeInUp 0.6s ease-out 1.4s both;

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 12px;
  }

  p {
    font-size: 14px;
    color: #718096;
    margin-bottom: 20px;
  }

  .support-options {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
  }

  .support-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    text-decoration: none;
    color: #4a5568;
    transition: all 0.3s ease;
    cursor: pointer;
    min-width: 120px;

    &:hover {
      background: #667eea;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    i {
      font-size: 20px;
    }

    span {
      font-size: 12px;
      font-weight: 500;
    }
  }
}

// Mobile Responsive
@media (max-width: 768px) {
  .payment-failed-container {
    padding: 20px 16px;
  }

  .failed-card {
    padding: 30px 20px;
  }

  .failed-content h1 {
    font-size: 28px;
  }

  .action-buttons .btn {
    padding: 12px 20px;
    font-size: 14px;
  }

  .support-options {
    flex-direction: column;
    align-items: center;

    .support-option {
      width: 100%;
      max-width: 200px;
    }
  }
}

@media (max-width: 480px) {
  .error-details .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .common-issues .issue-item {
    flex-direction: column;
    gap: 8px;
  }
}
