{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/material/snack-bar\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/input\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/select\";\nimport * as i8 from \"@angular/material/core\";\nimport * as i9 from \"@angular/material/tabs\";\nimport * as i10 from \"@angular/material/checkbox\";\nexport let SettingsComponent = /*#__PURE__*/(() => {\n  class SettingsComponent {\n    constructor(fb, snackBar) {\n      this.fb = fb;\n      this.snackBar = snackBar;\n    }\n    ngOnInit() {\n      this.createForms();\n      this.loadSettings();\n    }\n    createForms() {\n      this.generalForm = this.fb.group({\n        appName: ['DFashion', Validators.required],\n        companyName: ['DFashion Inc.', Validators.required],\n        supportEmail: ['<EMAIL>', [Validators.required, Validators.email]],\n        defaultCurrency: ['INR', Validators.required],\n        timezone: ['Asia/Kolkata', Validators.required]\n      });\n      this.ecommerceForm = this.fb.group({\n        storeName: ['DFashion Store', Validators.required],\n        storeDescription: ['Your one-stop fashion destination'],\n        taxRate: [18, [Validators.required, Validators.min(0), Validators.max(100)]],\n        shippingFee: [99, [Validators.required, Validators.min(0)]],\n        freeShippingThreshold: [999, [Validators.required, Validators.min(0)]],\n        enableInventoryTracking: [true],\n        enableReviews: [true],\n        enableWishlist: [true]\n      });\n      this.notificationForm = this.fb.group({\n        emailNewOrders: [true],\n        emailLowStock: [true],\n        emailNewUsers: [false],\n        emailReviews: [false],\n        pushNewOrders: [true],\n        pushUrgentAlerts: [true],\n        pushDailyReports: [false]\n      });\n      this.securityForm = this.fb.group({\n        sessionTimeout: [30, [Validators.required, Validators.min(5), Validators.max(480)]],\n        passwordMinLength: [8, [Validators.required, Validators.min(6), Validators.max(20)]],\n        requireTwoFactor: [false],\n        enableLoginAttempts: [true],\n        enableAuditLog: [true]\n      });\n    }\n    loadSettings() {\n      // In a real app, load settings from the backend\n      console.log('Loading settings...');\n    }\n    saveGeneralSettings() {\n      if (this.generalForm.valid) {\n        console.log('Saving general settings:', this.generalForm.value);\n        this.showSuccessMessage('General settings saved successfully');\n      }\n    }\n    saveEcommerceSettings() {\n      if (this.ecommerceForm.valid) {\n        console.log('Saving e-commerce settings:', this.ecommerceForm.value);\n        this.showSuccessMessage('E-commerce settings saved successfully');\n      }\n    }\n    saveNotificationSettings() {\n      if (this.notificationForm.valid) {\n        console.log('Saving notification settings:', this.notificationForm.value);\n        this.showSuccessMessage('Notification settings saved successfully');\n      }\n    }\n    saveSecuritySettings() {\n      if (this.securityForm.valid) {\n        console.log('Saving security settings:', this.securityForm.value);\n        this.showSuccessMessage('Security settings saved successfully');\n      }\n    }\n    showSuccessMessage(message) {\n      this.snackBar.open(message, 'Close', {\n        duration: 3000,\n        panelClass: ['success-snackbar']\n      });\n    }\n    static {\n      this.ɵfac = function SettingsComponent_Factory(t) {\n        return new (t || SettingsComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SettingsComponent,\n        selectors: [[\"app-settings\"]],\n        decls: 153,\n        vars: 4,\n        consts: [[1, \"settings-container\"], [1, \"settings-header\"], [\"label\", \"General\"], [1, \"tab-content\"], [1, \"settings-form\", 3, \"formGroup\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"appName\"], [\"matInput\", \"\", \"formControlName\", \"companyName\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"supportEmail\"], [\"formControlName\", \"defaultCurrency\"], [\"value\", \"INR\"], [\"value\", \"USD\"], [\"value\", \"EUR\"], [\"formControlName\", \"timezone\"], [\"value\", \"Asia/Kolkata\"], [\"value\", \"America/New_York\"], [\"value\", \"Europe/London\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"label\", \"E-commerce\"], [\"matInput\", \"\", \"formControlName\", \"storeName\"], [\"matInput\", \"\", \"rows\", \"3\", \"formControlName\", \"storeDescription\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"taxRate\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"shippingFee\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"freeShippingThreshold\"], [1, \"checkbox-group\"], [\"formControlName\", \"enableInventoryTracking\"], [\"formControlName\", \"enableReviews\"], [\"formControlName\", \"enableWishlist\"], [\"label\", \"Notifications\"], [\"formControlName\", \"emailNewOrders\"], [\"formControlName\", \"emailLowStock\"], [\"formControlName\", \"emailNewUsers\"], [\"formControlName\", \"emailReviews\"], [\"formControlName\", \"pushNewOrders\"], [\"formControlName\", \"pushUrgentAlerts\"], [\"formControlName\", \"pushDailyReports\"], [\"label\", \"Security\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"sessionTimeout\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"passwordMinLength\"], [\"formControlName\", \"requireTwoFactor\"], [\"formControlName\", \"enableLoginAttempts\"], [\"formControlName\", \"enableAuditLog\"]],\n        template: function SettingsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3, \"Settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\");\n            i0.ɵɵtext(5, \"Manage your application settings and preferences\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"mat-tab-group\")(7, \"mat-tab\", 2)(8, \"div\", 3)(9, \"mat-card\")(10, \"mat-card-header\")(11, \"mat-card-title\");\n            i0.ɵɵtext(12, \"General Settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"mat-card-subtitle\");\n            i0.ɵɵtext(14, \"Basic application configuration\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"mat-card-content\")(16, \"form\", 4)(17, \"mat-form-field\", 5)(18, \"mat-label\");\n            i0.ɵɵtext(19, \"Application Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(20, \"input\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"mat-form-field\", 5)(22, \"mat-label\");\n            i0.ɵɵtext(23, \"Company Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(24, \"input\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"mat-form-field\", 5)(26, \"mat-label\");\n            i0.ɵɵtext(27, \"Support Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(28, \"input\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"mat-form-field\", 5)(30, \"mat-label\");\n            i0.ɵɵtext(31, \"Default Currency\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"mat-select\", 9)(33, \"mat-option\", 10);\n            i0.ɵɵtext(34, \"Indian Rupee (\\u20B9)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"mat-option\", 11);\n            i0.ɵɵtext(36, \"US Dollar ($)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"mat-option\", 12);\n            i0.ɵɵtext(38, \"Euro (\\u20AC)\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(39, \"mat-form-field\", 5)(40, \"mat-label\");\n            i0.ɵɵtext(41, \"Timezone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"mat-select\", 13)(43, \"mat-option\", 14);\n            i0.ɵɵtext(44, \"Asia/Kolkata\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"mat-option\", 15);\n            i0.ɵɵtext(46, \"America/New_York\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"mat-option\", 16);\n            i0.ɵɵtext(48, \"Europe/London\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(49, \"div\", 17)(50, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function SettingsComponent_Template_button_click_50_listener() {\n              return ctx.saveGeneralSettings();\n            });\n            i0.ɵɵtext(51, \" Save Changes \");\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵelementStart(52, \"mat-tab\", 19)(53, \"div\", 3)(54, \"mat-card\")(55, \"mat-card-header\")(56, \"mat-card-title\");\n            i0.ɵɵtext(57, \"E-commerce Settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"mat-card-subtitle\");\n            i0.ɵɵtext(59, \"Configure your online store\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(60, \"mat-card-content\")(61, \"form\", 4)(62, \"mat-form-field\", 5)(63, \"mat-label\");\n            i0.ɵɵtext(64, \"Store Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(65, \"input\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"mat-form-field\", 5)(67, \"mat-label\");\n            i0.ɵɵtext(68, \"Store Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(69, \"textarea\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(70, \"mat-form-field\", 5)(71, \"mat-label\");\n            i0.ɵɵtext(72, \"Default Tax Rate (%)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(73, \"input\", 22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"mat-form-field\", 5)(75, \"mat-label\");\n            i0.ɵɵtext(76, \"Shipping Fee\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(77, \"input\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(78, \"mat-form-field\", 5)(79, \"mat-label\");\n            i0.ɵɵtext(80, \"Free Shipping Threshold\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(81, \"input\", 24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(82, \"div\", 25)(83, \"mat-checkbox\", 26);\n            i0.ɵɵtext(84, \" Enable Inventory Tracking \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(85, \"mat-checkbox\", 27);\n            i0.ɵɵtext(86, \" Enable Product Reviews \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(87, \"mat-checkbox\", 28);\n            i0.ɵɵtext(88, \" Enable Wishlist \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(89, \"div\", 17)(90, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function SettingsComponent_Template_button_click_90_listener() {\n              return ctx.saveEcommerceSettings();\n            });\n            i0.ɵɵtext(91, \" Save Changes \");\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵelementStart(92, \"mat-tab\", 29)(93, \"div\", 3)(94, \"mat-card\")(95, \"mat-card-header\")(96, \"mat-card-title\");\n            i0.ɵɵtext(97, \"Notification Settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(98, \"mat-card-subtitle\");\n            i0.ɵɵtext(99, \"Configure email and push notifications\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(100, \"mat-card-content\")(101, \"form\", 4)(102, \"h3\");\n            i0.ɵɵtext(103, \"Email Notifications\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(104, \"div\", 25)(105, \"mat-checkbox\", 30);\n            i0.ɵɵtext(106, \" New Orders \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(107, \"mat-checkbox\", 31);\n            i0.ɵɵtext(108, \" Low Stock Alerts \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(109, \"mat-checkbox\", 32);\n            i0.ɵɵtext(110, \" New User Registrations \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(111, \"mat-checkbox\", 33);\n            i0.ɵɵtext(112, \" New Product Reviews \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(113, \"h3\");\n            i0.ɵɵtext(114, \"Push Notifications\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(115, \"div\", 25)(116, \"mat-checkbox\", 34);\n            i0.ɵɵtext(117, \" New Orders \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(118, \"mat-checkbox\", 35);\n            i0.ɵɵtext(119, \" Urgent System Alerts \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(120, \"mat-checkbox\", 36);\n            i0.ɵɵtext(121, \" Daily Reports \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(122, \"div\", 17)(123, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function SettingsComponent_Template_button_click_123_listener() {\n              return ctx.saveNotificationSettings();\n            });\n            i0.ɵɵtext(124, \" Save Changes \");\n            i0.ɵɵelementEnd()()()()()()();\n            i0.ɵɵelementStart(125, \"mat-tab\", 37)(126, \"div\", 3)(127, \"mat-card\")(128, \"mat-card-header\")(129, \"mat-card-title\");\n            i0.ɵɵtext(130, \"Security Settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(131, \"mat-card-subtitle\");\n            i0.ɵɵtext(132, \"Manage security and access controls\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(133, \"mat-card-content\")(134, \"form\", 4)(135, \"mat-form-field\", 5)(136, \"mat-label\");\n            i0.ɵɵtext(137, \"Session Timeout (minutes)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(138, \"input\", 38);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(139, \"mat-form-field\", 5)(140, \"mat-label\");\n            i0.ɵɵtext(141, \"Password Minimum Length\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(142, \"input\", 39);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(143, \"div\", 25)(144, \"mat-checkbox\", 40);\n            i0.ɵɵtext(145, \" Require Two-Factor Authentication \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(146, \"mat-checkbox\", 41);\n            i0.ɵɵtext(147, \" Enable Login Attempt Limits \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(148, \"mat-checkbox\", 42);\n            i0.ɵɵtext(149, \" Enable Audit Logging \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(150, \"div\", 17)(151, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function SettingsComponent_Template_button_click_151_listener() {\n              return ctx.saveSecuritySettings();\n            });\n            i0.ɵɵtext(152, \" Save Changes \");\n            i0.ɵɵelementEnd()()()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"formGroup\", ctx.generalForm);\n            i0.ɵɵadvance(45);\n            i0.ɵɵproperty(\"formGroup\", ctx.ecommerceForm);\n            i0.ɵɵadvance(40);\n            i0.ɵɵproperty(\"formGroup\", ctx.notificationForm);\n            i0.ɵɵadvance(33);\n            i0.ɵɵproperty(\"formGroup\", ctx.securityForm);\n          }\n        },\n        dependencies: [i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.MatButton, i4.MatCard, i4.MatCardContent, i4.MatCardHeader, i4.MatCardSubtitle, i4.MatCardTitle, i5.MatInput, i6.MatFormField, i6.MatLabel, i7.MatSelect, i8.MatOption, i9.MatTab, i9.MatTabGroup, i10.MatCheckbox],\n        styles: [\".settings-container[_ngcontent-%COMP%]{padding:1rem;max-width:1000px;margin:0 auto}.settings-header[_ngcontent-%COMP%]{margin-bottom:2rem}.settings-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#333;font-weight:500}.settings-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:1rem}.tab-content[_ngcontent-%COMP%]{padding:1.5rem 0}.settings-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1rem;max-width:600px}.settings-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%}.settings-form[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:1.5rem 0 1rem;color:#333;font-size:1.125rem;font-weight:500}.settings-form[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:first-child{margin-top:0}.checkbox-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.75rem;margin:1rem 0}.checkbox-group[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{font-size:.875rem}.form-actions[_ngcontent-%COMP%]{margin-top:1.5rem;padding-top:1rem;border-top:1px solid #e0e0e0}.form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px}mat-card[_ngcontent-%COMP%]{margin-bottom:1.5rem}mat-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]{margin-bottom:1rem}mat-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:500}mat-card[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]{font-size:.875rem;color:#666;margin-top:.25rem}mat-tab-group[_ngcontent-%COMP%]     .mat-tab-label{min-width:120px}mat-tab-group[_ngcontent-%COMP%]     .mat-tab-body-content{overflow:visible}@media (max-width: 768px){.settings-container[_ngcontent-%COMP%]{padding:.5rem}.settings-form[_ngcontent-%COMP%]{max-width:none}mat-tab-group[_ngcontent-%COMP%]     .mat-tab-label{min-width:auto;padding:0 12px}mat-tab-group[_ngcontent-%COMP%]     .mat-tab-label-content{font-size:.875rem}}\"]\n      });\n    }\n  }\n  return SettingsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}