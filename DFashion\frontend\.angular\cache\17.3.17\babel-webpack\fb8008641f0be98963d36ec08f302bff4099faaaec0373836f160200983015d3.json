{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ViewAddStoriesComponent } from '../../components/instagram-stories/view-add-stories.component';\nimport { PaymentModalComponent } from '../../../../shared/components/payment-modal/payment-modal.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/product.service\";\nimport * as i3 from \"../../../../core/services/auth.service\";\nimport * as i4 from \"../../../../core/services/button-actions.service\";\nimport * as i5 from \"../../../../core/services/posts.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction HomeComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"div\", 5);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading amazing fashion...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HomeComponent_div_2_article_5_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(post_r2.location);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_12_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(post_r2.products.length);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_12_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const post_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.handleProductTagsClick(post_r2));\n    });\n    i0.ɵɵelement(2, \"i\", 44);\n    i0.ɵɵtemplate(3, HomeComponent_div_2_article_5_div_12_span_3_Template, 2, 1, \"span\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", post_r2.showProductTags);\n    i0.ɵɵproperty(\"title\", ctx_r3.getProductTagsButtonTitle(post_r2));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", post_r2.products.length > 1);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_13_button_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"img\", 53);\n    i0.ɵɵelementStart(2, \"div\", 54)(3, \"span\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 56);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const productTag_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r3.getTagImageUrl(productTag_r6), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r3.getTagName(productTag_r6));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.getTagName(productTag_r6));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getTagSubtitle(productTag_r6));\n  }\n}\nfunction HomeComponent_div_2_article_5_div_13_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_13_button_1_Template_button_click_0_listener() {\n      const productTag_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.onProductTagClick(productTag_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 49);\n    i0.ɵɵelement(2, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, HomeComponent_div_2_article_5_div_13_button_1_div_3_Template, 7, 4, \"div\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const productTag_r6 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassMap(\"tag-type-\" + (productTag_r6.navigationType || \"product\"));\n    i0.ɵɵstyleProp(\"left\", (productTag_r6.position == null ? null : productTag_r6.position.x) || 50, \"%\")(\"top\", (productTag_r6.position == null ? null : productTag_r6.position.y) || 50, \"%\");\n    i0.ɵɵproperty(\"title\", ctx_r3.getTagTooltip(productTag_r6));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"dot-\" + (productTag_r6.navigationType || \"product\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r3.getTagIcon(productTag_r6.navigationType || \"product\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", productTag_r6.showPreview);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtemplate(1, HomeComponent_div_2_article_5_div_13_button_1_Template, 4, 12, \"button\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", post_r2.products);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"span\", 58);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.formatLikesCount(post_r2.analytics.likes), \" likes\");\n  }\n}\nfunction HomeComponent_div_2_article_5_div_30_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelement(1, \"img\", 70);\n    i0.ɵɵelementStart(2, \"div\", 71)(3, \"span\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 56);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r8 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r8.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r8.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.formatPrice(product_r8.price));\n  }\n}\nfunction HomeComponent_div_2_article_5_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60);\n    i0.ɵɵtemplate(2, HomeComponent_div_2_article_5_div_30_div_2_Template, 7, 4, \"div\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 62)(4, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_30_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const post_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.buyNow(post_r2.products[0]));\n    });\n    i0.ɵɵelement(5, \"i\", 64);\n    i0.ɵɵtext(6, \" Buy Now \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_30_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const post_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.addToWishlist(post_r2.products[0]));\n    });\n    i0.ɵɵelement(8, \"i\", 66);\n    i0.ɵɵtext(9, \" Wishlist \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_30_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const post_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.addToCart(post_r2.products[0]));\n    });\n    i0.ɵɵelement(11, \"i\", 68);\n    i0.ɵɵtext(12, \" Add to Cart \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", post_r2.products.slice(0, 2));\n  }\n}\nfunction HomeComponent_div_2_article_5_div_31_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"span\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 78);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const comment_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r10.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r10.text);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_31_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const post_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleComments(post_r2));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 74);\n    i0.ɵɵtemplate(4, HomeComponent_div_2_article_5_div_31_div_4_Template, 5, 2, \"div\", 75);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" View all \", post_r2.comments.length, \" comments \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", post_r2.comments.slice(0, 2));\n  }\n}\nfunction HomeComponent_div_2_article_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"article\", 12)(1, \"header\", 13)(2, \"div\", 14);\n    i0.ɵɵelement(3, \"img\", 15);\n    i0.ɵɵelementStart(4, \"div\", 16)(5, \"h3\", 17);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, HomeComponent_div_2_article_5_span_7_Template, 2, 1, \"span\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 19);\n    i0.ɵɵelement(9, \"i\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 21);\n    i0.ɵɵelement(11, \"img\", 22);\n    i0.ɵɵtemplate(12, HomeComponent_div_2_article_5_div_12_Template, 4, 4, \"div\", 23)(13, HomeComponent_div_2_article_5_div_13_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 24)(15, \"div\", 25)(16, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_16_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleLike(post_r2));\n    });\n    i0.ɵɵelement(17, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_18_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.focusCommentInput(post_r2));\n    });\n    i0.ɵɵelement(19, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_20_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.sharePost(post_r2));\n    });\n    i0.ɵɵelement(21, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_22_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleSave(post_r2));\n    });\n    i0.ɵɵelement(23, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, HomeComponent_div_2_article_5_div_24_Template, 3, 1, \"div\", 32);\n    i0.ɵɵelementStart(25, \"div\", 33)(26, \"span\", 17);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 34);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(30, HomeComponent_div_2_article_5_div_30_Template, 13, 1, \"div\", 35)(31, HomeComponent_div_2_article_5_div_31_Template, 5, 2, \"div\", 36);\n    i0.ɵɵelementStart(32, \"div\", 37);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 38)(35, \"input\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HomeComponent_div_2_article_5_Template_input_ngModelChange_35_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.newComment, $event) || (ctx_r3.newComment = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function HomeComponent_div_2_article_5_Template_input_keyup_enter_35_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.addComment(post_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_36_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.addComment(post_r2));\n    });\n    i0.ɵɵtext(37, \"Post\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const post_r2 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", post_r2.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", post_r2.user.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(post_r2.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.location);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", post_r2.mediaUrl, i0.ɵɵsanitizeUrl)(\"alt\", post_r2.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.products && post_r2.products.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.products && post_r2.products.length > 0 && post_r2.showProductTags);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"liked\", post_r2.isLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(post_r2.isLiked ? \"fas fa-heart\" : \"far fa-heart\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"saved\", post_r2.isSaved);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(post_r2.isSaved ? \"fas fa-bookmark\" : \"far fa-bookmark\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.analytics.likes > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(post_r2.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(post_r2.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.products && post_r2.products.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.comments && post_r2.comments.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getTimeAgo(post_r2.createdAt), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.newComment);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.newComment || !ctx_r3.newComment.trim());\n  }\n}\nfunction HomeComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"section\", 8);\n    i0.ɵɵelement(3, \"app-view-add-stories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"section\", 9);\n    i0.ɵɵtemplate(5, HomeComponent_div_2_article_5_Template, 38, 24, \"article\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"app-sidebar\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.instagramPosts)(\"ngForTrackBy\", ctx_r3.trackByPostId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"currentUser\", ctx_r3.currentUser)(\"featuredProducts\", ctx_r3.featuredProducts)(\"trendingProducts\", ctx_r3.trendingProducts)(\"newArrivals\", ctx_r3.newArrivals)(\"summerCollection\", ctx_r3.summerCollection)(\"categories\", ctx_r3.categories);\n  }\n}\nexport let HomeComponent = /*#__PURE__*/(() => {\n  class HomeComponent {\n    constructor(router, productService, authService, buttonActionsService, postsService) {\n      this.router = router;\n      this.productService = productService;\n      this.authService = authService;\n      this.buttonActionsService = buttonActionsService;\n      this.postsService = postsService;\n      this.featuredProducts = [];\n      this.trendingProducts = [];\n      this.newArrivals = [];\n      this.trendingPosts = [];\n      this.categories = [];\n      this.isLoading = true;\n      this.isAuthenticated = false;\n      // Instagram-style data\n      this.instagramPosts = [];\n      this.summerCollection = [];\n      this.suggestedUsers = [];\n      this.currentUser = null;\n      this.newComment = '';\n      // Payment Modal\n      this.showPaymentModal = false;\n      this.paymentModalData = null;\n    }\n    ngOnInit() {\n      this.loadHomeData();\n      this.checkAuthStatus();\n      this.loadInstagramData();\n    }\n    checkAuthStatus() {\n      this.authService.currentUser$.subscribe(user => {\n        this.isAuthenticated = !!user;\n        this.currentUser = user;\n      });\n    }\n    loadInstagramData() {\n      this.loadInstagramPosts();\n      this.loadSummerCollection();\n      this.loadSuggestedUsers();\n    }\n    loadInstagramPosts() {\n      // Load real Instagram-style posts from API\n      this.postsService.getPosts(1, 10).subscribe({\n        next: response => {\n          if (response.success) {\n            this.instagramPosts = response.posts;\n            console.log('✅ Instagram posts loaded:', this.instagramPosts.length);\n          } else {\n            this.instagramPosts = [];\n            console.log('⚠️ No Instagram posts available');\n          }\n        },\n        error: error => {\n          console.error('❌ Error loading Instagram posts:', error);\n          this.instagramPosts = [];\n        }\n      });\n    }\n    loadSummerCollection() {\n      // Load summer collection from API using category products\n      this.productService.getCategoryProducts('summer').subscribe({\n        next: response => {\n          this.summerCollection = response.products || [];\n          console.log('✅ Summer collection loaded:', this.summerCollection.length);\n        },\n        error: error => {\n          console.error('❌ Error loading summer collection:', error);\n          this.summerCollection = [];\n        }\n      });\n    }\n    loadSuggestedUsers() {\n      // Load suggested users from API\n      // For now, set empty array until users API is implemented\n      this.suggestedUsers = [];\n      console.log('✅ Suggested users loaded:', this.suggestedUsers.length);\n    }\n    loadHomeData() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this.isLoading = true;\n          // Load all data in parallel\n          const [featured, trending, arrivals] = yield Promise.all([_this.productService.getFeaturedProducts().toPromise(), _this.productService.getTrendingProducts().toPromise(), _this.productService.getNewArrivals().toPromise()]);\n          _this.featuredProducts = (featured?.products || featured?.data || featured || []).slice(0, 8);\n          _this.trendingProducts = (trending?.products || trending?.data || trending || []).slice(0, 8);\n          _this.newArrivals = (arrivals?.products || arrivals?.data || arrivals || []).slice(0, 8);\n          // Log loaded data counts\n          console.log('✅ Featured products loaded:', _this.featuredProducts.length);\n          console.log('✅ Trending products loaded:', _this.trendingProducts.length);\n          console.log('✅ New arrivals loaded:', _this.newArrivals.length);\n          // Load categories\n          _this.loadCategories();\n          // Load trending posts (mock data for now)\n          _this.loadTrendingPosts();\n        } catch (error) {\n          console.error('❌ Error loading home data:', error);\n          // Set empty arrays instead of fallback data\n          _this.featuredProducts = [];\n          _this.trendingProducts = [];\n          _this.newArrivals = [];\n        } finally {\n          _this.isLoading = false;\n        }\n      })();\n    }\n    loadCategories() {\n      // Load categories from API\n      this.productService.getCategories().subscribe({\n        next: response => {\n          this.categories = response.data || [];\n          console.log('✅ Categories loaded:', this.categories.length);\n        },\n        error: error => {\n          console.error('❌ Error loading categories:', error);\n          this.categories = [];\n        }\n      });\n    }\n    loadTrendingPosts() {\n      // Load trending posts from API\n      this.postsService.getTrendingPosts(6).subscribe({\n        next: response => {\n          if (response.success) {\n            this.trendingPosts = response.posts;\n            console.log('✅ Trending posts loaded:', this.trendingPosts.length);\n          } else {\n            this.trendingPosts = [];\n            console.log('⚠️ No trending posts available');\n          }\n        },\n        error: error => {\n          console.error('❌ Error loading trending posts:', error);\n          this.trendingPosts = [];\n        }\n      });\n    }\n    // Navigation methods\n    onProductClick(product) {\n      this.router.navigate(['/product', product._id || product.id]);\n    }\n    onCategoryClick(category) {\n      this.router.navigate(['/shop'], {\n        queryParams: {\n          category: category.name.toLowerCase()\n        }\n      });\n    }\n    viewAllCategories() {\n      this.router.navigate(['/shop']);\n    }\n    // Instagram-style interaction methods\n    toggleLike(post) {\n      if (!post._id) return;\n      // Optimistic update\n      const wasLiked = post.isLiked;\n      post.isLiked = !post.isLiked;\n      post.analytics.likes += post.isLiked ? 1 : -1;\n      this.postsService.toggleLike(post._id).subscribe({\n        next: result => {\n          if (result.success && result.likesCount !== undefined) {\n            post.analytics.likes = result.likesCount;\n            console.log('✅ Post like toggled successfully');\n          } else {\n            // Revert on failure\n            post.isLiked = wasLiked;\n            post.analytics.likes += wasLiked ? 1 : -1;\n            console.error('❌ Failed to toggle like:', result.message);\n          }\n        },\n        error: error => {\n          // Revert on error\n          post.isLiked = wasLiked;\n          post.analytics.likes += wasLiked ? 1 : -1;\n          console.error('❌ Error toggling like:', error);\n        }\n      });\n    }\n    toggleSave(post) {\n      if (!post.id && !post._id) return;\n      const postId = post.id || post._id;\n      // Optimistic update\n      post.isSaved = !post.isSaved;\n      this.buttonActionsService.savePost(postId).subscribe({\n        next: result => {\n          if (!result.success) {\n            // Revert on failure\n            post.isSaved = !post.isSaved;\n            console.error('Failed to save post:', result.message);\n          }\n        },\n        error: error => {\n          // Revert on error\n          post.isSaved = !post.isSaved;\n          console.error('Error saving post:', error);\n        }\n      });\n    }\n    sharePost(post) {\n      if (!post.id && !post._id) return;\n      const postId = post.id || post._id;\n      this.buttonActionsService.sharePost(postId).subscribe({\n        next: result => {\n          if (result.success) {\n            console.log('Post shared successfully');\n          } else {\n            console.error('Failed to share post:', result.message);\n          }\n        },\n        error: error => {\n          console.error('Error sharing post:', error);\n        }\n      });\n    }\n    focusCommentInput(post) {\n      // Focus on comment input for this post\n      console.log('Focus comment for post:', post.id);\n    }\n    addComment(post) {\n      if (!this.newComment || !this.newComment.trim()) return;\n      if (!post._id) return;\n      const commentText = this.newComment.trim();\n      this.postsService.addComment(post._id, commentText).subscribe({\n        next: result => {\n          if (result.success && result.comment) {\n            // Add comment to local state\n            post.comments.push(result.comment);\n            post.analytics.comments += 1;\n            this.newComment = '';\n            console.log('✅ Comment added successfully');\n          } else {\n            console.error('❌ Failed to add comment:', result.message);\n          }\n        },\n        error: error => {\n          console.error('❌ Error adding comment:', error);\n        }\n      });\n    }\n    showProductDetails(product) {\n      console.log('🛍️ Show product details:', product);\n      if (product._id) {\n        this.router.navigate(['/product', product._id]);\n      } else {\n        // Toggle product preview for posts\n        product.showPreview = !product.showPreview;\n      }\n    }\n    // View full post\n    viewPost(post) {\n      console.log('📱 View post:', post._id);\n      this.router.navigate(['/post', post._id]);\n    }\n    // View user profile\n    viewUserProfile(user) {\n      console.log('👤 View user profile:', user.username);\n      this.router.navigate(['/profile', user.username]);\n    }\n    // Enhanced product tags click handler\n    handleProductTagsClick(post) {\n      if (!post.products || post.products.length === 0) return;\n      // If only one product/item, navigate directly\n      if (post.products.length === 1) {\n        const productTag = post.products[0];\n        this.onProductTagClick(productTag);\n        return;\n      }\n      // If multiple products, toggle visibility to show selection\n      this.toggleProductTags(post);\n    }\n    // Get dynamic title for product tags button\n    getProductTagsButtonTitle(post) {\n      if (!post.products || post.products.length === 0) return 'No products';\n      if (post.products.length === 1) {\n        const productTag = post.products[0];\n        // Since PostProduct only has basic product info, we'll use the product name\n        return `View ${productTag.product?.name || 'Product'}`;\n      }\n      return `View ${post.products.length} Products`;\n    }\n    // Toggle product tags visibility (kept for multiple products)\n    toggleProductTags(post) {\n      post.showProductTags = !post.showProductTags;\n      console.log('🏷️ Product tags toggled:', post.showProductTags);\n    }\n    // E-commerce methods\n    buyNow(product) {\n      console.log('Buy now:', product);\n      if (!product.id && !product._id) return;\n      const productId = product.id || product._id;\n      this.buttonActionsService.buyNow({\n        productId: productId,\n        quantity: 1,\n        addedFrom: 'home_feed'\n      }).subscribe({\n        next: result => {\n          if (result.success) {\n            console.log('Buy now successful, redirecting to checkout');\n            // Navigation will be handled by the service\n          } else {\n            console.error('Failed to buy now:', result.message);\n            // Fallback to payment modal if needed\n            this.showPaymentModalFallback(product);\n          }\n        },\n        error: error => {\n          console.error('Error in buy now:', error);\n          // Fallback to payment modal\n          this.showPaymentModalFallback(product);\n        }\n      });\n    }\n    showPaymentModalFallback(product) {\n      if (!this.currentUser) {\n        this.router.navigate(['/auth/login'], {\n          queryParams: {\n            returnUrl: '/home'\n          }\n        });\n        return;\n      }\n      // Prepare payment modal data for single product purchase\n      this.paymentModalData = {\n        amount: product.price,\n        orderData: {\n          items: [{\n            id: product.id,\n            name: product.name,\n            price: product.price,\n            quantity: 1,\n            image: product.image\n          }],\n          subtotal: product.price,\n          tax: product.price * 0.18,\n          shipping: 0,\n          discount: 0,\n          total: product.price + product.price * 0.18\n        },\n        userDetails: {\n          name: this.currentUser.fullName || this.currentUser.username,\n          email: this.currentUser.email,\n          phone: this.currentUser.phone || ''\n        }\n      };\n      this.showPaymentModal = true;\n    }\n    addToWishlist(product) {\n      console.log('Add to wishlist:', product);\n      if (!product.id && !product._id) return;\n      const productId = product.id || product._id;\n      this.buttonActionsService.addToWishlist({\n        productId: productId,\n        addedFrom: 'home_feed'\n      }).subscribe({\n        next: result => {\n          if (result.success) {\n            console.log('Product added to wishlist successfully');\n          } else {\n            console.error('Failed to add to wishlist:', result.message);\n          }\n        },\n        error: error => {\n          console.error('Error adding to wishlist:', error);\n        }\n      });\n    }\n    addToCart(product) {\n      console.log('Add to cart:', product);\n      if (!product.id && !product._id) return;\n      const productId = product.id || product._id;\n      this.buttonActionsService.addToCart({\n        productId: productId,\n        quantity: 1,\n        addedFrom: 'home_feed'\n      }).subscribe({\n        next: result => {\n          if (result.success) {\n            console.log('Product added to cart successfully');\n          } else {\n            console.error('Failed to add to cart:', result.message);\n          }\n        },\n        error: error => {\n          console.error('Error adding to cart:', error);\n        }\n      });\n    }\n    followUser(user) {\n      console.log('Follow user:', user.username);\n      // Implement follow functionality\n    }\n    viewSummerCollection() {\n      this.router.navigate(['/shop'], {\n        queryParams: {\n          collection: 'summer2024'\n        }\n      });\n    }\n    toggleComments(post) {\n      console.log('Toggle comments for post:', post.id);\n      // Implement comments toggle\n    }\n    // Utility methods\n    formatPrice(price) {\n      return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD'\n      }).format(price);\n    }\n    formatNumber(num) {\n      if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + 'M';\n      } else if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'K';\n      }\n      return num.toString();\n    }\n    formatLikesCount(likes) {\n      return this.formatNumber(likes);\n    }\n    getTimeAgo(date) {\n      if (!date) return 'Unknown';\n      let dateObj;\n      // Handle different date formats\n      if (typeof date === 'string') {\n        dateObj = new Date(date);\n      } else if (date instanceof Date) {\n        dateObj = date;\n      } else {\n        return 'Unknown';\n      }\n      // Check if date is valid\n      if (isNaN(dateObj.getTime())) {\n        return 'Unknown';\n      }\n      const now = new Date();\n      const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n      if (diffInSeconds < 60) return 'Just now';\n      if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;\n      if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;\n      if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;\n      return `${Math.floor(diffInSeconds / 604800)}w`;\n    }\n    trackByPostId(index, post) {\n      return post.id;\n    }\n    // Payment Modal handlers\n    onPaymentCompleted(paymentResult) {\n      this.showPaymentModal = false;\n      if (paymentResult.status === 'success') {\n        // Navigate to success page\n        this.router.navigate(['/payment-success'], {\n          queryParams: {\n            orderId: paymentResult.orderId || this.generateOrderId(),\n            method: paymentResult.method\n          }\n        });\n      } else {\n        // Show error message\n        alert('Payment failed. Please try again.');\n      }\n    }\n    onPaymentModalClose() {\n      this.showPaymentModal = false;\n    }\n    generateOrderId() {\n      return 'ORD' + Date.now().toString();\n    }\n    // Enhanced product tag methods\n    onProductTagClick(productTag) {\n      if (!productTag || !productTag.product) return;\n      // Since PostProduct only has basic product info, we'll navigate to the product\n      this.navigateToProduct(productTag.product);\n    }\n    navigateToProduct(product) {\n      if (product?._id || product?.id) {\n        const productId = product._id || product.id;\n        console.log('🛍️ Navigating to product:', productId);\n        this.router.navigate(['/product', productId]);\n      } else {\n        console.warn('⚠️ Product navigation failed: No valid product ID', product);\n        this.showToast('Product not found');\n      }\n    }\n    navigateToCategory(category) {\n      if (category?.slug || category?.name || category?._id) {\n        const categoryIdentifier = category.slug || category._id || category.name.toLowerCase().replace(/\\s+/g, '-');\n        console.log('📂 Navigating to category:', categoryIdentifier);\n        this.router.navigate(['/shop'], {\n          queryParams: {\n            category: categoryIdentifier\n          }\n        });\n      } else {\n        console.warn('⚠️ Category navigation failed: No valid category identifier', category);\n        this.showToast('Category not found');\n      }\n    }\n    navigateToVendor(vendor) {\n      if (vendor?._id || vendor?.id || vendor?.username) {\n        const vendorId = vendor._id || vendor.id || vendor.username;\n        console.log('🏪 Navigating to vendor:', vendorId);\n        this.router.navigate(['/vendor', vendorId]);\n      } else {\n        console.warn('⚠️ Vendor navigation failed: No valid vendor identifier', vendor);\n        this.showToast('Vendor not found');\n      }\n    }\n    navigateToBrand(brand) {\n      if (brand?.slug || brand?.name || brand?._id) {\n        const brandIdentifier = brand.slug || brand._id || brand.name.toLowerCase().replace(/\\s+/g, '-');\n        console.log('🏷️ Navigating to brand:', brandIdentifier);\n        this.router.navigate(['/shop'], {\n          queryParams: {\n            brand: brandIdentifier\n          }\n        });\n      } else {\n        console.warn('⚠️ Brand navigation failed: No valid brand identifier', brand);\n        this.showToast('Brand not found');\n      }\n    }\n    getTagTooltip(productTag) {\n      const name = this.getTagName(productTag);\n      return `View product: ${name}`;\n    }\n    getTagIcon(navigationType) {\n      return 'fas fa-tag'; // Always use product tag icon since we only support products\n    }\n    getTagName(productTag) {\n      return productTag.product?.name || 'Product';\n    }\n    getTagSubtitle(productTag) {\n      return productTag.product?.price ? `$${productTag.product.price}` : '';\n    }\n    getTagImageUrl(productTag) {\n      return productTag.product?.images?.[0]?.url || '/assets/images/product-placeholder.jpg';\n    }\n    // Simple toast notification method\n    showToast(message) {\n      // Create toast element\n      const toast = document.createElement('div');\n      toast.textContent = message;\n      toast.style.cssText = `\n      position: fixed;\n      bottom: 20px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: white;\n      padding: 12px 24px;\n      border-radius: 8px;\n      z-index: 10000;\n      font-size: 14px;\n      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n      backdrop-filter: blur(10px);\n      animation: slideUp 0.3s ease-out;\n    `;\n      // Add animation styles\n      const style = document.createElement('style');\n      style.textContent = `\n      @keyframes slideUp {\n        from {\n          opacity: 0;\n          transform: translateX(-50%) translateY(20px);\n        }\n        to {\n          opacity: 1;\n          transform: translateX(-50%) translateY(0);\n        }\n      }\n    `;\n      document.head.appendChild(style);\n      // Add to DOM\n      document.body.appendChild(toast);\n      // Remove after 3 seconds\n      setTimeout(() => {\n        if (toast.parentNode) {\n          document.body.removeChild(toast);\n        }\n        if (style.parentNode) {\n          document.head.removeChild(style);\n        }\n      }, 3000);\n    }\n    static {\n      this.ɵfac = function HomeComponent_Factory(t) {\n        return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.ButtonActionsService), i0.ɵɵdirectiveInject(i5.PostsService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HomeComponent,\n        selectors: [[\"app-home\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 4,\n        vars: 4,\n        consts: [[1, \"instagram-home-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"instagram-layout\", 4, \"ngIf\"], [3, \"close\", \"paymentCompleted\", \"isVisible\", \"paymentData\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"instagram-layout\"], [1, \"main-feed\"], [1, \"stories-section\"], [1, \"posts-feed\"], [\"class\", \"instagram-post\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"currentUser\", \"featuredProducts\", \"trendingProducts\", \"newArrivals\", \"summerCollection\", \"categories\"], [1, \"instagram-post\"], [1, \"post-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"username\"], [\"class\", \"location\", 4, \"ngIf\"], [1, \"more-options\"], [1, \"fas\", \"fa-ellipsis-h\"], [1, \"post-media\"], [1, \"post-image\", 3, \"src\", \"alt\"], [\"class\", \"product-tags-overlay\", 4, \"ngIf\"], [1, \"post-actions\"], [1, \"primary-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [1, \"far\", \"fa-comment\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [1, \"far\", \"fa-paper-plane\"], [1, \"action-btn\", \"save-btn\", 3, \"click\"], [\"class\", \"likes-section\", 4, \"ngIf\"], [1, \"post-caption\"], [1, \"caption-text\"], [\"class\", \"ecommerce-actions\", 4, \"ngIf\"], [\"class\", \"comments-preview\", 4, \"ngIf\"], [1, \"post-time\"], [1, \"add-comment-section\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 1, \"comment-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"post-comment-btn\", 3, \"click\", \"disabled\"], [1, \"location\"], [1, \"product-tags-overlay\"], [1, \"product-tag-btn\", 3, \"click\", \"title\"], [1, \"fas\", \"fa-shopping-bag\"], [\"class\", \"product-count\", 4, \"ngIf\"], [1, \"product-count\"], [\"class\", \"product-tag\", 3, \"class\", \"left\", \"top\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\", \"title\"], [1, \"tag-dot\"], [1, \"tag-icon\"], [\"class\", \"product-preview\", 4, \"ngIf\"], [1, \"product-preview\"], [3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"likes-section\"], [1, \"likes-count\"], [1, \"ecommerce-actions\"], [1, \"product-showcase\"], [\"class\", \"featured-product\", 4, \"ngFor\", \"ngForOf\"], [1, \"shopping-buttons\"], [1, \"shop-btn\", \"buy-btn\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [1, \"shop-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"far\", \"fa-heart\"], [1, \"shop-btn\", \"cart-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"featured-product\"], [1, \"product-thumbnail\", 3, \"src\", \"alt\"], [1, \"product-details\"], [1, \"comments-preview\"], [1, \"view-comments-btn\", 3, \"click\"], [1, \"recent-comments\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [1, \"comment\"], [1, \"comment-username\"], [1, \"comment-text\"]],\n        template: function HomeComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, HomeComponent_div_1_Template, 4, 0, \"div\", 1)(2, HomeComponent_div_2_Template, 7, 8, \"div\", 2);\n            i0.ɵɵelementStart(3, \"app-payment-modal\", 3);\n            i0.ɵɵlistener(\"close\", function HomeComponent_Template_app_payment_modal_close_3_listener() {\n              return ctx.onPaymentModalClose();\n            })(\"paymentCompleted\", function HomeComponent_Template_app_payment_modal_paymentCompleted_3_listener($event) {\n              return ctx.onPaymentCompleted($event);\n            });\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"isVisible\", ctx.showPaymentModal)(\"paymentData\", ctx.paymentModalData);\n          }\n        },\n        dependencies: [CommonModule, i6.NgForOf, i6.NgIf, FormsModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, RouterModule, ViewAddStoriesComponent, SidebarComponent, PaymentModalComponent],\n        styles: [\".instagram-home-container[_ngcontent-%COMP%]{min-height:calc(100vh - 80px);background:#fafafa;padding-top:20px}.instagram-layout[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 320px;gap:30px;max-width:975px;margin:0 auto;padding:0 20px}@media (max-width: 1024px){.instagram-layout[_ngcontent-%COMP%]{grid-template-columns:1fr;max-width:614px;gap:0;padding:0}}@media (max-width: 768px){.instagram-layout[_ngcontent-%COMP%]{padding:0;gap:0}}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:200px;gap:16px}.loading-container[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:3px solid #e3e3e3;border-top:3px solid #667eea;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;font-size:14px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.main-feed[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:24px;width:100%;max-width:614px}.stories-section[_ngcontent-%COMP%]{background:#fff;border:1px solid #dbdbdb;border-radius:8px;overflow:hidden;margin-bottom:24px}.stories-section[_ngcontent-%COMP%]     .stories-container{margin:0;padding:16px;background:#fff;border-radius:0;box-shadow:none;border:none;-webkit-backdrop-filter:none;backdrop-filter:none}.stories-section[_ngcontent-%COMP%]     .stories-header{display:none}.stories-section[_ngcontent-%COMP%]     .stories-slider-wrapper{gap:8px}.stories-section[_ngcontent-%COMP%]     .nav-arrow{display:none}.stories-section[_ngcontent-%COMP%]     .story-item{margin:0 4px}.stories-section[_ngcontent-%COMP%]     .story-avatar{width:56px;height:56px}@media (max-width: 768px){.stories-section[_ngcontent-%COMP%]     .story-avatar{width:56px;height:56px}}.stories-section[_ngcontent-%COMP%]     .story-avatar-inner{width:50px;height:50px}@media (max-width: 768px){.stories-section[_ngcontent-%COMP%]     .story-avatar-inner{width:50px;height:50px}}.stories-section[_ngcontent-%COMP%]     .story-username{font-size:12px;color:#262626;text-shadow:none;max-width:64px}@media (max-width: 768px){.stories-section[_ngcontent-%COMP%]     .story-username{max-width:64px}}@media (max-width: 768px){.stories-section[_ngcontent-%COMP%]{border-radius:0;border-left:none;border-right:none;margin-bottom:0}}.posts-feed[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:24px}.instagram-post[_ngcontent-%COMP%]{background:#fff;border:1px solid #dbdbdb;border-radius:8px;overflow:hidden;margin-bottom:24px;max-width:614px;width:100%}@media (max-width: 768px){.instagram-post[_ngcontent-%COMP%]{border-radius:0;border-left:none;border-right:none;margin-bottom:0;border-bottom:none}}.post-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:16px}.post-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.post-header[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;object-fit:cover}.post-header[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%]{font-size:14px;font-weight:600;margin:0;color:#262626}.post-header[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   .location[_ngcontent-%COMP%]{font-size:12px;color:#8e8e8e}.post-header[_ngcontent-%COMP%]   .more-options[_ngcontent-%COMP%]{background:none;border:none;cursor:pointer;padding:8px;color:#262626}.post-header[_ngcontent-%COMP%]   .more-options[_ngcontent-%COMP%]:hover{color:#8e8e8e}.post-media[_ngcontent-%COMP%]{position:relative;width:100%;aspect-ratio:1;overflow:hidden}.post-media[_ngcontent-%COMP%]   .post-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;display:block}.product-tags-overlay[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none}.product-tags-overlay[_ngcontent-%COMP%]   .product-tag-btn[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:#000000b3;color:#fff;border:none;border-radius:50%;width:40px;height:40px;display:flex;align-items:center;justify-content:center;cursor:pointer;pointer-events:all;transition:all .3s ease;font-size:16px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);position:relative}.product-tags-overlay[_ngcontent-%COMP%]   .product-tag-btn[_ngcontent-%COMP%]:hover{background:#000000e6;transform:translate(-50%,-50%) scale(1.1)}.product-tags-overlay[_ngcontent-%COMP%]   .product-tag-btn.active[_ngcontent-%COMP%]{background:#0095f6e6;transform:translate(-50%,-50%) scale(1.1)}.product-tags-overlay[_ngcontent-%COMP%]   .product-tag-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px}.product-tags-overlay[_ngcontent-%COMP%]   .product-tag-btn[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;background:#ff3040;color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:10px;font-weight:600;border:2px solid white;animation:_ngcontent-%COMP%_pulse-count 2s infinite}.product-tag[_ngcontent-%COMP%]{position:absolute;pointer-events:all;cursor:pointer;z-index:5}.product-tag[_ngcontent-%COMP%]   .tag-dot[_ngcontent-%COMP%]{width:20px;height:20px;background:#fff;border-radius:50%;border:2px solid #262626;position:relative;animation:_ngcontent-%COMP%_pulse 2s infinite}.product-tag[_ngcontent-%COMP%]   .tag-dot[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:6px;height:6px;background:#262626;border-radius:50%}.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]{position:absolute;bottom:30px;left:50%;transform:translate(-50%);background:#000c;border-radius:8px;padding:8px;display:flex;align-items:center;gap:8px;min-width:200px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:4px;object-fit:cover}.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]{color:#fff}.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{display:block;font-size:12px;font-weight:500}.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]{display:block;font-size:14px;font-weight:600;color:#f5f5f5}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1)}50%{transform:scale(1.1)}}@keyframes _ngcontent-%COMP%_pulse-count{0%,to{transform:scale(1);opacity:1}50%{transform:scale(1.2);opacity:.8}}.post-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:12px 16px 8px}.post-actions[_ngcontent-%COMP%]   .primary-actions[_ngcontent-%COMP%]{display:flex;gap:16px}.post-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{background:none;border:none;cursor:pointer;padding:8px;font-size:24px;color:#262626;transition:all .3s ease}.post-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{color:#8e8e8e}.post-actions[_ngcontent-%COMP%]   .action-btn.liked[_ngcontent-%COMP%]{color:#ed4956;animation:_ngcontent-%COMP%_heartBeat .6s ease}.post-actions[_ngcontent-%COMP%]   .action-btn.saved[_ngcontent-%COMP%]{color:#262626}@keyframes _ngcontent-%COMP%_heartBeat{0%,to{transform:scale(1)}50%{transform:scale(1.2)}}.likes-section[_ngcontent-%COMP%]{padding:0 16px 8px}.likes-section[_ngcontent-%COMP%]   .likes-count[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#262626}.post-caption[_ngcontent-%COMP%]{padding:0 16px 8px;font-size:14px;line-height:1.4}.post-caption[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%]{font-weight:600;color:#262626;margin-right:8px}.post-caption[_ngcontent-%COMP%]   .caption-text[_ngcontent-%COMP%]{color:#262626}.ecommerce-actions[_ngcontent-%COMP%]{padding:12px 16px;background:#f8f9fa;border-top:1px solid #efefef}.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]{display:flex;gap:12px;margin-bottom:12px}.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;flex:1}.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:6px;object-fit:cover}.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]{flex:1}.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{display:block;font-size:12px;font-weight:500;color:#262626;line-height:1.2}.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]{display:block;font-size:14px;font-weight:600;color:#667eea}.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]{display:flex;gap:8px}.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%]{flex:1;padding:8px 16px;border:none;border-radius:6px;font-size:12px;font-weight:600;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;justify-content:center;gap:4px}.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.buy-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff}.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.buy-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #667eea66}.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.wishlist-btn[_ngcontent-%COMP%]{background:#fff;border:1px solid #dbdbdb;color:#262626}.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.wishlist-btn[_ngcontent-%COMP%]:hover{background:#f8f9fa;border-color:#c6c6c6}.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.cart-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f093fb,#f5576c);color:#fff}.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.cart-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #f093fb66}.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}.comments-preview[_ngcontent-%COMP%]{padding:0 16px 8px}.comments-preview[_ngcontent-%COMP%]   .view-comments-btn[_ngcontent-%COMP%]{background:none;border:none;color:#8e8e8e;font-size:14px;cursor:pointer;margin-bottom:4px}.comments-preview[_ngcontent-%COMP%]   .view-comments-btn[_ngcontent-%COMP%]:hover{color:#262626}.comments-preview[_ngcontent-%COMP%]   .recent-comments[_ngcontent-%COMP%]   .comment[_ngcontent-%COMP%]{font-size:14px;line-height:1.4;margin-bottom:2px}.comments-preview[_ngcontent-%COMP%]   .recent-comments[_ngcontent-%COMP%]   .comment[_ngcontent-%COMP%]   .comment-username[_ngcontent-%COMP%]{font-weight:600;color:#262626;margin-right:8px}.comments-preview[_ngcontent-%COMP%]   .recent-comments[_ngcontent-%COMP%]   .comment[_ngcontent-%COMP%]   .comment-text[_ngcontent-%COMP%]{color:#262626}.post-time[_ngcontent-%COMP%]{padding:0 16px 8px;font-size:10px;color:#8e8e8e;text-transform:uppercase;letter-spacing:.2px}.add-comment-section[_ngcontent-%COMP%]{padding:12px 16px;border-top:1px solid #efefef;display:flex;align-items:center;gap:8px}.add-comment-section[_ngcontent-%COMP%]   .comment-input[_ngcontent-%COMP%]{flex:1;border:none;outline:none;font-size:14px;color:#262626}.add-comment-section[_ngcontent-%COMP%]   .comment-input[_ngcontent-%COMP%]::placeholder{color:#8e8e8e}.add-comment-section[_ngcontent-%COMP%]   .post-comment-btn[_ngcontent-%COMP%]{background:none;border:none;color:#0095f6;font-size:14px;font-weight:600;cursor:pointer}.add-comment-section[_ngcontent-%COMP%]   .post-comment-btn[_ngcontent-%COMP%]:disabled{color:#c7c7c7;cursor:not-allowed}.add-comment-section[_ngcontent-%COMP%]   .post-comment-btn[_ngcontent-%COMP%]:not(:disabled):hover{color:#00376b}.instagram-sidebar[_ngcontent-%COMP%]{position:sticky;top:84px;height:-moz-fit-content;height:fit-content;padding-left:0}@media (max-width: 1024px){.instagram-sidebar[_ngcontent-%COMP%]{display:none}}.profile-card[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:16px 0 24px;margin-bottom:8px}.profile-card[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]{width:56px;height:56px;border-radius:50%;object-fit:cover}.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]{flex:1}.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-username[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#262626;margin:0 0 2px}.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%]{font-size:14px;color:#8e8e8e}.profile-card[_ngcontent-%COMP%]   .switch-btn[_ngcontent-%COMP%]{background:none;border:none;color:#0095f6;font-size:12px;font-weight:600;cursor:pointer}.profile-card[_ngcontent-%COMP%]   .switch-btn[_ngcontent-%COMP%]:hover{color:#00376b}.sidebar-section[_ngcontent-%COMP%]{margin-bottom:32px}.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px}.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#8e8e8e;margin:0;text-transform:none}.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%]{background:none;border:none;color:#262626;font-size:12px;font-weight:400;cursor:pointer}.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%]:hover{color:#8e8e8e}.collection-items[_ngcontent-%COMP%], .featured-items[_ngcontent-%COMP%], .trending-items[_ngcontent-%COMP%], .arrivals-items[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.collection-item[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:4px 0;border-radius:3px;cursor:pointer;transition:background-color .2s ease}.collection-item[_ngcontent-%COMP%]:hover, .featured-item[_ngcontent-%COMP%]:hover, .trending-item[_ngcontent-%COMP%]:hover, .arrival-item[_ngcontent-%COMP%]:hover{background:#0000000d}.collection-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:44px;height:44px;border-radius:3px;object-fit:cover}.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]{flex:1}.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%]{display:block;font-size:14px;font-weight:500;color:#262626;line-height:1.2;margin-bottom:2px}.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%]{display:block;font-size:12px;color:#8e8e8e;margin-bottom:2px}.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:#667eea}.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px}.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%]{color:#667eea}.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%]{color:#8e8e8e;text-decoration:line-through;font-size:10px}.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]{display:flex;gap:8px;margin-bottom:2px}.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:flex;align-items:center;gap:2px;font-size:10px;color:#8e8e8e}.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:8px}.trending-badge[_ngcontent-%COMP%], .new-badge[_ngcontent-%COMP%]{position:absolute;top:4px;right:4px;padding:2px 6px;border-radius:10px;font-size:8px;font-weight:600;color:#fff}.trending-badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff6b6b,orange);display:flex;align-items:center;gap:2px}.trending-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:6px}.new-badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,#43e97b,#38f9d7)}.trending-item[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]{position:relative}.suggested-users[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.suggested-user[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:8px}.suggested-user[_ngcontent-%COMP%]   .suggested-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;object-fit:cover}.suggested-user[_ngcontent-%COMP%]   .suggested-info[_ngcontent-%COMP%]{flex:1}.suggested-user[_ngcontent-%COMP%]   .suggested-info[_ngcontent-%COMP%]   .suggested-username[_ngcontent-%COMP%]{display:block;font-size:14px;font-weight:500;color:#262626;line-height:1.2}.suggested-user[_ngcontent-%COMP%]   .suggested-info[_ngcontent-%COMP%]   .suggested-followers[_ngcontent-%COMP%]{font-size:12px;color:#8e8e8e}.suggested-user[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]{background:#0095f6;border:none;color:#fff;padding:4px 12px;border-radius:4px;font-size:12px;font-weight:600;cursor:pointer;transition:background-color .3s ease}.suggested-user[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover{background:#00376b}.sidebar-footer[_ngcontent-%COMP%]{margin-top:32px;padding-top:16px;border-top:1px solid #efefef}.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px;margin-bottom:16px}.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{font-size:11px;color:#c7c7c7;text-decoration:none}.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.sidebar-footer[_ngcontent-%COMP%]   .copyright[_ngcontent-%COMP%]{font-size:11px;color:#c7c7c7}@media (max-width: 768px){.instagram-home-container[_ngcontent-%COMP%]{padding-top:0}.instagram-layout[_ngcontent-%COMP%]{padding:0;max-width:100%}.main-feed[_ngcontent-%COMP%]{gap:0}.instagram-post[_ngcontent-%COMP%]{margin-bottom:0;border-radius:0;border-left:none;border-right:none;max-width:100%}.instagram-post[_ngcontent-%COMP%]:last-child{border-bottom:1px solid #dbdbdb}.stories-section[_ngcontent-%COMP%]{border-radius:0;border-left:none;border-right:none;margin-bottom:0;border-bottom:1px solid #dbdbdb}.post-header[_ngcontent-%COMP%]{padding:14px 16px}.post-actions[_ngcontent-%COMP%]{padding:6px 16px 4px}.ecommerce-actions[_ngcontent-%COMP%]{padding:12px 16px}.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%]{padding:8px 12px;font-size:12px}.add-comment-section[_ngcontent-%COMP%]{padding:12px 16px}.likes-section[_ngcontent-%COMP%], .post-caption[_ngcontent-%COMP%], .comments-preview[_ngcontent-%COMP%], .post-time[_ngcontent-%COMP%]{padding-left:16px;padding-right:16px}}@media (max-width: 480px){.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]{flex-direction:column;gap:8px}.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%]{width:32px;height:32px}.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]{flex-direction:column;gap:6px}.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%]{padding:8px 16px;font-size:12px}}\"]\n      });\n    }\n  }\n  return HomeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}