{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, e as readTask, w as writeTask, h, f as getElement, H as Host } from './index-a1a47f01.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nconst rippleEffectCss = \":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:strict;pointer-events:none}:host(.unbounded){contain:layout size style}.ripple-effect{border-radius:50%;position:absolute;background-color:currentColor;color:inherit;contain:strict;opacity:0;-webkit-animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;will-change:transform, opacity;pointer-events:none}.fade-out{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1));-webkit-animation:150ms fadeOutAnimation forwards;animation:150ms fadeOutAnimation forwards}@-webkit-keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@-webkit-keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@-webkit-keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}@keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}\";\nconst IonRippleEffectStyle0 = rippleEffectCss;\nconst RippleEffect = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.type = 'bounded';\n  }\n  /**\n   * Adds the ripple effect to the parent element.\n   *\n   * @param x The horizontal coordinate of where the ripple should start.\n   * @param y The vertical coordinate of where the ripple should start.\n   */\n  addRipple(x, y) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      return new Promise(resolve => {\n        readTask(() => {\n          const rect = _this.el.getBoundingClientRect();\n          const width = rect.width;\n          const height = rect.height;\n          const hypotenuse = Math.sqrt(width * width + height * height);\n          const maxDim = Math.max(height, width);\n          const maxRadius = _this.unbounded ? maxDim : hypotenuse + PADDING;\n          const initialSize = Math.floor(maxDim * INITIAL_ORIGIN_SCALE);\n          const finalScale = maxRadius / initialSize;\n          let posX = x - rect.left;\n          let posY = y - rect.top;\n          if (_this.unbounded) {\n            posX = width * 0.5;\n            posY = height * 0.5;\n          }\n          const styleX = posX - initialSize * 0.5;\n          const styleY = posY - initialSize * 0.5;\n          const moveX = width * 0.5 - posX;\n          const moveY = height * 0.5 - posY;\n          writeTask(() => {\n            const div = document.createElement('div');\n            div.classList.add('ripple-effect');\n            const style = div.style;\n            style.top = styleY + 'px';\n            style.left = styleX + 'px';\n            style.width = style.height = initialSize + 'px';\n            style.setProperty('--final-scale', `${finalScale}`);\n            style.setProperty('--translate-end', `${moveX}px, ${moveY}px`);\n            const container = _this.el.shadowRoot || _this.el;\n            container.appendChild(div);\n            setTimeout(() => {\n              resolve(() => {\n                removeRipple(div);\n              });\n            }, 225 + 100);\n          });\n        });\n      });\n    })();\n  }\n  get unbounded() {\n    return this.type === 'unbounded';\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'f1129019a6d556b008c754aeb79618c69baea9f8',\n      role: \"presentation\",\n      class: {\n        [mode]: true,\n        unbounded: this.unbounded\n      }\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nconst removeRipple = ripple => {\n  ripple.classList.add('fade-out');\n  setTimeout(() => {\n    ripple.remove();\n  }, 200);\n};\nconst PADDING = 10;\nconst INITIAL_ORIGIN_SCALE = 0.5;\nRippleEffect.style = IonRippleEffectStyle0;\nexport { RippleEffect as ion_ripple_effect };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}