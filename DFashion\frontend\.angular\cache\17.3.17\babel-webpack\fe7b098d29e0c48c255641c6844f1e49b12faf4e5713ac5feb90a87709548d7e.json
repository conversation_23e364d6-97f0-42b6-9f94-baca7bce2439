{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction PostDetailComponent_div_0_i_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 43);\n  }\n}\nfunction PostDetailComponent_div_0_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵtext(1, \"Follow\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PostDetailComponent_div_0_div_21_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 48);\n  }\n  if (rf & 2) {\n    const media_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", media_r3.url, i0.ɵɵsanitizeUrl)(\"alt\", media_r3.alt);\n  }\n}\nfunction PostDetailComponent_div_0_div_21_video_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 49);\n  }\n  if (rf & 2) {\n    const media_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", media_r3.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction PostDetailComponent_div_0_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtemplate(1, PostDetailComponent_div_0_div_21_img_1_Template, 1, 2, \"img\", 46)(2, PostDetailComponent_div_0_div_21_video_2_Template, 1, 1, \"video\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const media_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", media_r3.type === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", media_r3.type === \"video\");\n  }\n}\nfunction PostDetailComponent_div_0_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_div_22_div_1_Template_div_click_0_listener() {\n      const productTag_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.showProductDetails(productTag_r5.product));\n    });\n    i0.ɵɵelementStart(1, \"div\", 53);\n    i0.ɵɵelement(2, \"i\", 54);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const productTag_r5 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", productTag_r5.position.x, \"%\")(\"top\", productTag_r5.position.y, \"%\");\n  }\n}\nfunction PostDetailComponent_div_0_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtemplate(1, PostDetailComponent_div_0_div_22_div_1_Template, 3, 4, \"div\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.post.products);\n  }\n}\nfunction PostDetailComponent_div_0_div_42_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_div_42_span_1_Template_span_click_0_listener() {\n      const hashtag_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.searchHashtag(hashtag_r7));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hashtag_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" #\", hashtag_r7, \" \");\n  }\n}\nfunction PostDetailComponent_div_0_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtemplate(1, PostDetailComponent_div_0_div_42_span_1_Template, 2, 1, \"span\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.post.hashtags);\n  }\n}\nfunction PostDetailComponent_div_0_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_div_43_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.buyNow());\n    });\n    i0.ɵɵelement(2, \"i\", 60);\n    i0.ɵɵtext(3, \" Buy Now \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_div_43_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart());\n    });\n    i0.ɵɵelement(5, \"i\", 62);\n    i0.ɵɵtext(6, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_div_43_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist());\n    });\n    i0.ɵɵelement(8, \"i\", 25);\n    i0.ɵɵtext(9, \" Wishlist \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PostDetailComponent_div_0_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"img\", 65);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_div_48_Template_img_click_1_listener() {\n      const comment_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProfile(comment_r10.user._id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 66)(3, \"div\", 67)(4, \"span\", 68);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_div_48_Template_span_click_4_listener() {\n      const comment_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProfile(comment_r10.user._id));\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 69);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"p\", 70);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const comment_r10 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", comment_r10.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", comment_r10.user.fullName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", comment_r10.user.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(comment_r10.commentedAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r10.text);\n  }\n}\nfunction PostDetailComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"header\", 4)(2, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(3, \"i\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h1\");\n    i0.ɵɵtext(5, \"Post\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showMenu());\n    });\n    i0.ɵɵelement(7, \"i\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 9)(9, \"div\", 10)(10, \"div\", 11);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.viewProfile(ctx_r1.post.user._id));\n    });\n    i0.ɵɵelement(11, \"img\", 12);\n    i0.ɵɵelementStart(12, \"div\", 13)(13, \"div\", 14)(14, \"span\", 15);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, PostDetailComponent_div_0_i_16_Template, 1, 0, \"i\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 17);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(19, PostDetailComponent_div_0_button_19_Template, 2, 0, \"button\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 19);\n    i0.ɵɵtemplate(21, PostDetailComponent_div_0_div_21_Template, 3, 2, \"div\", 20)(22, PostDetailComponent_div_0_div_22_Template, 2, 1, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 22)(24, \"div\", 23)(25, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleLike());\n    });\n    i0.ɵɵelement(26, \"i\", 25);\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.focusCommentInput());\n    });\n    i0.ɵɵelement(30, \"i\", 27);\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sharePost());\n    });\n    i0.ɵɵelement(34, \"i\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSave());\n    });\n    i0.ɵɵelement(36, \"i\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 32)(38, \"span\", 15);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"span\", 33);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, PostDetailComponent_div_0_div_42_Template, 2, 1, \"div\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(43, PostDetailComponent_div_0_div_43_Template, 10, 0, \"div\", 35);\n    i0.ɵɵelementStart(44, \"div\", 36)(45, \"h3\");\n    i0.ɵɵtext(46, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 37);\n    i0.ɵɵtemplate(48, PostDetailComponent_div_0_div_48_Template, 10, 5, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 39);\n    i0.ɵɵelement(50, \"img\", 40);\n    i0.ɵɵelementStart(51, \"input\", 41, 0);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PostDetailComponent_div_0_Template_input_ngModelChange_51_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newComment, $event) || (ctx_r1.newComment = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function PostDetailComponent_div_0_Template_input_keyup_enter_51_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addComment());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_0_Template_button_click_53_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addComment());\n    });\n    i0.ɵɵtext(54, \" Post \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"src\", ctx_r1.post.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.post.user.fullName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.post.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.post.user.isVerified);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.post.createdAt));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isOwnPost);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.post.media);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.post.products.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.post.isLiked);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.post.likes.length);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.post.comments.length);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"saved\", ctx_r1.post.isSaved);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.post.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.post.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.post.hashtags.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.post.products.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.post.comments);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.avatar) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentUser == null ? null : ctx_r1.currentUser.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newComment);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.newComment || !ctx_r1.newComment.trim());\n  }\n}\nfunction PostDetailComponent_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 87);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, ctx_r1.selectedProduct.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction PostDetailComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 72);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_1_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 73)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelement(6, \"i\", 75);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 76);\n    i0.ɵɵelement(8, \"img\", 77);\n    i0.ɵɵelementStart(9, \"div\", 78)(10, \"p\", 79);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 80)(13, \"span\", 81);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, PostDetailComponent_div_1_span_16_Template, 3, 4, \"span\", 82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 83)(18, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_1_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.buyProductNow());\n    });\n    i0.ɵɵtext(19, \"Buy Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_1_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addProductToCart());\n    });\n    i0.ɵɵtext(21, \"Add to Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function PostDetailComponent_div_1_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addProductToWishlist());\n    });\n    i0.ɵɵtext(23, \"Add to Wishlist\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.selectedProduct.images[0] == null ? null : ctx_r1.selectedProduct.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.selectedProduct.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(15, 6, ctx_r1.selectedProduct.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedProduct.originalPrice);\n  }\n}\nexport class PostDetailComponent {\n  constructor(route, router) {\n    this.route = route;\n    this.router = router;\n    this.post = null;\n    this.selectedProduct = null;\n    this.newComment = '';\n    this.currentUser = null;\n    this.isOwnPost = false;\n    this.loading = true;\n  }\n  ngOnInit() {\n    this.loadCurrentUser();\n    this.route.params.subscribe(params => {\n      if (params['id']) {\n        this.loadPost(params['id']);\n      }\n    });\n  }\n  loadCurrentUser() {\n    // TODO: Get from auth service\n    this.currentUser = {\n      _id: 'current-user',\n      username: 'you',\n      fullName: 'Your Name',\n      avatar: ''\n    };\n  }\n  loadPost(postId) {\n    this.loading = true;\n    // Load post from real API\n    fetch(`http://localhost:5000/api/posts/${postId}`).then(response => response.json()).then(data => {\n      if (data.success) {\n        this.post = {\n          ...data.post,\n          isLiked: false,\n          isSaved: false // TODO: Check if current user saved this post\n        };\n        this.isOwnPost = this.post?.user?._id === this.currentUser?._id;\n      }\n      this.loading = false;\n    }).catch(error => {\n      console.error('Error loading post:', error);\n      this.loading = false;\n    });\n  }\n  goBack() {\n    this.router.navigate(['/social']);\n  }\n  showMenu() {\n    // TODO: Show post menu\n    console.log('Show post menu');\n  }\n  viewProfile(userId) {\n    this.router.navigate(['/profile', userId]);\n  }\n  toggleLike() {\n    if (!this.post) return;\n    this.post.isLiked = !this.post.isLiked;\n    if (this.post.isLiked) {\n      this.post.likes.push({\n        user: this.currentUser._id,\n        likedAt: new Date()\n      });\n    } else {\n      this.post.likes = this.post.likes.filter(like => like.user !== this.currentUser._id);\n    }\n    // TODO: Update like status via API\n    console.log('Toggle like for post:', this.post._id, this.post.isLiked);\n  }\n  toggleSave() {\n    if (!this.post) return;\n    this.post.isSaved = !this.post.isSaved;\n    if (this.post.isSaved) {\n      this.post.saves.push({\n        user: this.currentUser._id,\n        savedAt: new Date()\n      });\n    } else {\n      this.post.saves = this.post.saves.filter(save => save.user !== this.currentUser._id);\n    }\n    // TODO: Update save status via API\n    console.log('Toggle save for post:', this.post._id, this.post.isSaved);\n  }\n  sharePost() {\n    if (!this.post) return;\n    // TODO: Implement share functionality\n    console.log('Share post:', this.post);\n    if (navigator.share) {\n      navigator.share({\n        title: `${this.post.user.username}'s post`,\n        text: this.post.caption,\n        url: window.location.href\n      });\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href);\n      alert('Link copied to clipboard!');\n    }\n  }\n  focusCommentInput() {\n    const commentInput = document.querySelector('.comment-input');\n    if (commentInput) {\n      commentInput.focus();\n    }\n  }\n  addComment() {\n    if (!this.post || !this.newComment?.trim()) return;\n    const newComment = {\n      _id: Date.now().toString(),\n      user: {\n        _id: this.currentUser._id,\n        username: this.currentUser.username,\n        fullName: this.currentUser.fullName,\n        avatar: this.currentUser.avatar\n      },\n      text: this.newComment.trim(),\n      commentedAt: new Date()\n    };\n    this.post.comments.push(newComment);\n    this.newComment = '';\n    // TODO: Add comment via API\n    console.log('Add comment to post:', this.post._id, newComment);\n  }\n  searchHashtag(hashtag) {\n    this.router.navigate(['/search'], {\n      queryParams: {\n        hashtag\n      }\n    });\n  }\n  // E-commerce actions\n  buyNow() {\n    if (this.post && this.post.products.length > 0) {\n      const product = this.post.products[0].product;\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'post'\n        }\n      });\n    }\n  }\n  addToCart() {\n    if (this.post && this.post.products.length > 0) {\n      const product = this.post.products[0].product;\n      // TODO: Add to cart via service\n      console.log('Add to cart from post:', product);\n      alert(`${product.name} added to cart!`);\n    }\n  }\n  addToWishlist() {\n    if (this.post && this.post.products.length > 0) {\n      const product = this.post.products[0].product;\n      // TODO: Add to wishlist via service\n      console.log('Add to wishlist from post:', product);\n      alert(`${product.name} added to wishlist!`);\n    }\n  }\n  // Product modal\n  showProductDetails(product) {\n    this.selectedProduct = product;\n  }\n  closeProductModal() {\n    this.selectedProduct = null;\n  }\n  buyProductNow() {\n    if (this.selectedProduct) {\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: this.selectedProduct._id,\n          source: 'post'\n        }\n      });\n    }\n  }\n  addProductToCart() {\n    if (this.selectedProduct) {\n      // TODO: Add to cart via service\n      console.log('Add product to cart:', this.selectedProduct);\n      alert(`${this.selectedProduct.name} added to cart!`);\n      this.closeProductModal();\n    }\n  }\n  addProductToWishlist() {\n    if (this.selectedProduct) {\n      // TODO: Add to wishlist via service\n      console.log('Add product to wishlist:', this.selectedProduct);\n      alert(`${this.selectedProduct.name} added to wishlist!`);\n      this.closeProductModal();\n    }\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diffMs = now.getTime() - new Date(date).getTime();\n    const diffMinutes = Math.floor(diffMs / (1000 * 60));\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n    if (diffMinutes < 1) return 'now';\n    if (diffMinutes < 60) return `${diffMinutes}m`;\n    if (diffHours < 24) return `${diffHours}h`;\n    if (diffDays < 7) return `${diffDays}d`;\n    return new Date(date).toLocaleDateString();\n  }\n  static {\n    this.ɵfac = function PostDetailComponent_Factory(t) {\n      return new (t || PostDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PostDetailComponent,\n      selectors: [[\"app-post-detail\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"commentInput\", \"\"], [\"class\", \"post-detail-container\", 4, \"ngIf\"], [\"class\", \"product-modal\", 3, \"click\", 4, \"ngIf\"], [1, \"post-detail-container\"], [1, \"detail-header\"], [1, \"btn-back\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [1, \"btn-menu\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-h\"], [1, \"post-detail-content\"], [1, \"post-header\"], [1, \"user-info\", 3, \"click\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"username-row\"], [1, \"username\"], [\"class\", \"fas fa-check-circle verified\", 4, \"ngIf\"], [1, \"post-time\"], [\"class\", \"btn-follow\", 4, \"ngIf\"], [1, \"post-media\"], [\"class\", \"media-container\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"product-tags\", 4, \"ngIf\"], [1, \"post-actions\"], [1, \"primary-actions\"], [1, \"action-btn\", \"like\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"action-btn\", \"comment\", 3, \"click\"], [1, \"fas\", \"fa-comment\"], [1, \"action-btn\", \"share\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"action-btn\", \"save\", 3, \"click\"], [1, \"fas\", \"fa-bookmark\"], [1, \"post-caption\"], [1, \"caption-text\"], [\"class\", \"hashtags\", 4, \"ngIf\"], [\"class\", \"ecommerce-actions\", 4, \"ngIf\"], [1, \"comments-section\"], [1, \"comments-list\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [1, \"add-comment\"], [1, \"comment-avatar\", 3, \"src\", \"alt\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 1, \"comment-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"btn-post-comment\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-check-circle\", \"verified\"], [1, \"btn-follow\"], [1, \"media-container\"], [\"class\", \"post-image\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"post-video\", \"controls\", \"\", 3, \"src\", 4, \"ngIf\"], [1, \"post-image\", 3, \"src\", \"alt\"], [\"controls\", \"\", 1, \"post-video\", 3, \"src\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"left\", \"top\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"hashtags\"], [\"class\", \"hashtag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"hashtag\", 3, \"click\"], [1, \"ecommerce-actions\"], [1, \"ecom-btn\", \"buy-now\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [1, \"ecom-btn\", \"add-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"ecom-btn\", \"wishlist\", 3, \"click\"], [1, \"comment\"], [1, \"comment-avatar\", 3, \"click\", \"src\", \"alt\"], [1, \"comment-content\"], [1, \"comment-header\"], [1, \"comment-username\", 3, \"click\"], [1, \"comment-time\"], [1, \"comment-text\"], [1, \"product-modal\", 3, \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"modal-header\"], [1, \"btn-close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"modal-body\"], [1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"brand\"], [1, \"price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"modal-actions\"], [1, \"btn-primary\", 3, \"click\"], [1, \"btn-secondary\", 3, \"click\"], [1, \"btn-outline\", 3, \"click\"], [1, \"original-price\"]],\n      template: function PostDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, PostDetailComponent_div_0_Template, 55, 23, \"div\", 1)(1, PostDetailComponent_div_1_Template, 24, 9, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.post);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedProduct);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\".post-detail-container[_ngcontent-%COMP%] {\\n  max-width: 600px;\\n  margin: 0 auto;\\n  background: #fff;\\n  min-height: 100vh;\\n}\\n\\n.detail-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 16px 20px;\\n  border-bottom: 1px solid #eee;\\n  position: sticky;\\n  top: 0;\\n  background: #fff;\\n  z-index: 100;\\n}\\n\\n.detail-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.btn-back[_ngcontent-%COMP%], .btn-menu[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 1.2rem;\\n  color: #333;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n}\\n\\n.btn-back[_ngcontent-%COMP%]:hover, .btn-menu[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n}\\n\\n.post-detail-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n.post-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n}\\n\\n.user-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.username-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  font-size: 0.9rem;\\n}\\n\\n.verified[_ngcontent-%COMP%] {\\n  color: #1da1f2;\\n  font-size: 0.8rem;\\n}\\n\\n.post-time[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.btn-follow[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: #fff;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  font-weight: 500;\\n  cursor: pointer;\\n}\\n\\n.btn-follow[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n.post-media[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: #000;\\n}\\n\\n.media-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  aspect-ratio: 1;\\n  overflow: hidden;\\n}\\n\\n.post-image[_ngcontent-%COMP%], .post-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: all;\\n  cursor: pointer;\\n  transform: translate(-50%, -50%);\\n}\\n\\n.product-tag-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #333;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: translate(-50%, -50%) scale(1);\\n  }\\n  50% {\\n    transform: translate(-50%, -50%) scale(1.1);\\n  }\\n}\\n.post-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px 20px;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.primary-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 1.2rem;\\n  color: #333;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n}\\n\\n.action-btn.liked[_ngcontent-%COMP%] {\\n  color: #ff6b6b;\\n}\\n\\n.action-btn.saved[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n\\n.post-caption[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n  line-height: 1.4;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.caption-text[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n  color: #333;\\n}\\n\\n.hashtags[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n\\n.hashtag[_ngcontent-%COMP%] {\\n  color: #1da1f2;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n}\\n\\n.hashtag[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.ecommerce-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  padding: 16px 20px;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.ecom-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px;\\n  border: none;\\n  border-radius: 6px;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  transition: all 0.2s ease;\\n}\\n\\n.buy-now[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n  color: #fff;\\n}\\n\\n.add-cart[_ngcontent-%COMP%] {\\n  background: #4ecdc4;\\n  color: #fff;\\n}\\n\\n.wishlist[_ngcontent-%COMP%] {\\n  background: #ff9ff3;\\n  color: #fff;\\n}\\n\\n.ecom-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n}\\n\\n.comments-section[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.comments-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.comments-list[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.comment[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n\\n.comment-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  cursor: pointer;\\n}\\n\\n.comment-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.comment-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 4px;\\n}\\n\\n.comment-username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n}\\n\\n.comment-username[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.comment-time[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.comment-text[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  line-height: 1.4;\\n  font-size: 0.9rem;\\n}\\n\\n.add-comment[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding-top: 16px;\\n  border-top: 1px solid #f0f0f0;\\n}\\n\\n.comment-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: 1px solid #ddd;\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  font-size: 0.9rem;\\n  outline: none;\\n}\\n\\n.comment-input[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff;\\n}\\n\\n.btn-post-comment[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: #fff;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n}\\n\\n.btn-post-comment[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n.btn-post-comment[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #0056b3;\\n}\\n\\n\\n\\n.product-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.8);\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 12px;\\n  max-width: 400px;\\n  width: 100%;\\n  max-height: 80vh;\\n  overflow-y: auto;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n  border-bottom: 1px solid #eee;\\n}\\n\\n.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.btn-close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 1.2rem;\\n  cursor: pointer;\\n  color: #666;\\n  padding: 4px;\\n}\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  object-fit: cover;\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.brand[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-bottom: 8px;\\n}\\n\\n.price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.current-price[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 700;\\n  color: #333;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #999;\\n  text-decoration: line-through;\\n}\\n\\n.modal-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%], .btn-outline[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  border: none;\\n  border-radius: 6px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: #fff;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: #6c757d;\\n  color: #fff;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: #007bff;\\n  border: 1px solid #007bff;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .post-detail-container[_ngcontent-%COMP%] {\\n    margin: 0;\\n  }\\n  .detail-header[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .post-header[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .post-actions[_ngcontent-%COMP%] {\\n    padding: 8px 16px;\\n  }\\n  .post-caption[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n  .comments-section[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "media_r3", "url", "ɵɵsanitizeUrl", "alt", "ɵɵtemplate", "PostDetailComponent_div_0_div_21_img_1_Template", "PostDetailComponent_div_0_div_21_video_2_Template", "ɵɵadvance", "type", "ɵɵlistener", "PostDetailComponent_div_0_div_22_div_1_Template_div_click_0_listener", "productTag_r5", "ɵɵrestoreView", "_r4", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "showProductDetails", "product", "ɵɵstyleProp", "position", "x", "y", "PostDetailComponent_div_0_div_22_div_1_Template", "post", "products", "PostDetailComponent_div_0_div_42_span_1_Template_span_click_0_listener", "hashtag_r7", "_r6", "searchHashtag", "ɵɵtextInterpolate1", "PostDetailComponent_div_0_div_42_span_1_Template", "hashtags", "PostDetailComponent_div_0_div_43_Template_button_click_1_listener", "_r8", "buyNow", "PostDetailComponent_div_0_div_43_Template_button_click_4_listener", "addToCart", "PostDetailComponent_div_0_div_43_Template_button_click_7_listener", "addToWishlist", "PostDetailComponent_div_0_div_48_Template_img_click_1_listener", "comment_r10", "_r9", "viewProfile", "user", "_id", "PostDetailComponent_div_0_div_48_Template_span_click_4_listener", "avatar", "fullName", "username", "ɵɵtextInterpolate", "getTimeAgo", "commentedAt", "text", "PostDetailComponent_div_0_Template_button_click_2_listener", "_r1", "goBack", "PostDetailComponent_div_0_Template_button_click_6_listener", "showMenu", "PostDetailComponent_div_0_Template_div_click_10_listener", "PostDetailComponent_div_0_i_16_Template", "PostDetailComponent_div_0_button_19_Template", "PostDetailComponent_div_0_div_21_Template", "PostDetailComponent_div_0_div_22_Template", "PostDetailComponent_div_0_Template_button_click_25_listener", "toggleLike", "PostDetailComponent_div_0_Template_button_click_29_listener", "focusCommentInput", "PostDetailComponent_div_0_Template_button_click_33_listener", "sharePost", "PostDetailComponent_div_0_Template_button_click_35_listener", "toggleSave", "PostDetailComponent_div_0_div_42_Template", "PostDetailComponent_div_0_div_43_Template", "PostDetailComponent_div_0_div_48_Template", "ɵɵtwoWayListener", "PostDetailComponent_div_0_Template_input_ngModelChange_51_listener", "$event", "ɵɵtwoWayBindingSet", "newComment", "PostDetailComponent_div_0_Template_input_keyup_enter_51_listener", "addComment", "PostDetailComponent_div_0_Template_button_click_53_listener", "isVerified", "createdAt", "isOwnPost", "media", "length", "ɵɵclassProp", "isLiked", "likes", "comments", "isSaved", "caption", "currentUser", "ɵɵtwoWayProperty", "trim", "ɵɵpipeBind2", "selectedProduct", "originalPrice", "PostDetailComponent_div_1_Template_div_click_0_listener", "_r11", "closeProductModal", "PostDetailComponent_div_1_Template_div_click_1_listener", "stopPropagation", "PostDetailComponent_div_1_Template_button_click_5_listener", "PostDetailComponent_div_1_span_16_Template", "PostDetailComponent_div_1_Template_button_click_18_listener", "buyProductNow", "PostDetailComponent_div_1_Template_button_click_20_listener", "addProductToCart", "PostDetailComponent_div_1_Template_button_click_22_listener", "addProductToWishlist", "name", "images", "brand", "price", "PostDetailComponent", "constructor", "route", "router", "loading", "ngOnInit", "loadCurrentUser", "params", "subscribe", "loadPost", "postId", "fetch", "then", "response", "json", "data", "success", "catch", "error", "console", "navigate", "log", "userId", "push", "likedAt", "Date", "filter", "like", "saves", "savedAt", "save", "navigator", "share", "title", "window", "location", "href", "clipboard", "writeText", "alert", "commentInput", "document", "querySelector", "focus", "now", "toString", "hashtag", "queryParams", "productId", "source", "date", "diffMs", "getTime", "diffMinutes", "Math", "floor", "diffHours", "diffDays", "toLocaleDateString", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PostDetailComponent_Template", "rf", "ctx", "PostDetailComponent_div_0_Template", "PostDetailComponent_div_1_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\features\\posts\\post-detail.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\n\ninterface Post {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar?: string;\n    isVerified?: boolean;\n  };\n  caption: string;\n  media: {\n    type: 'image' | 'video';\n    url: string;\n    alt: string;\n  }[];\n  products: {\n    _id: string;\n    product: {\n      _id: string;\n      name: string;\n      price: number;\n      originalPrice?: number;\n      images: { url: string; alt: string }[];\n      brand: string;\n    };\n    position: { x: number; y: number };\n  }[];\n  hashtags: string[];\n  likes: { user: string; likedAt: Date }[];\n  comments: {\n    _id: string;\n    user: {\n      _id: string;\n      username: string;\n      fullName: string;\n      avatar?: string;\n    };\n    text: string;\n    commentedAt: Date;\n  }[];\n  shares: { user: string; sharedAt: Date }[];\n  saves: { user: string; savedAt: Date }[];\n  isLiked: boolean;\n  isSaved: boolean;\n  createdAt: Date;\n}\n\n@Component({\n  selector: 'app-post-detail',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"post-detail-container\" *ngIf=\"post\">\n      <!-- Header -->\n      <header class=\"detail-header\">\n        <button class=\"btn-back\" (click)=\"goBack()\">\n          <i class=\"fas fa-arrow-left\"></i>\n        </button>\n        <h1>Post</h1>\n        <button class=\"btn-menu\" (click)=\"showMenu()\">\n          <i class=\"fas fa-ellipsis-h\"></i>\n        </button>\n      </header>\n\n      <!-- Post Content -->\n      <div class=\"post-detail-content\">\n        <!-- User Info -->\n        <div class=\"post-header\">\n          <div class=\"user-info\" (click)=\"viewProfile(post.user._id)\">\n            <img [src]=\"post.user.avatar || '/assets/images/default-avatar.png'\" \n                 [alt]=\"post.user.fullName\" \n                 class=\"user-avatar\">\n            <div class=\"user-details\">\n              <div class=\"username-row\">\n                <span class=\"username\">{{ post.user.username }}</span>\n                <i class=\"fas fa-check-circle verified\" *ngIf=\"post.user.isVerified\"></i>\n              </div>\n              <span class=\"post-time\">{{ getTimeAgo(post.createdAt) }}</span>\n            </div>\n          </div>\n          \n          <button class=\"btn-follow\" *ngIf=\"!isOwnPost\">Follow</button>\n        </div>\n\n        <!-- Media -->\n        <div class=\"post-media\">\n          <div class=\"media-container\" *ngFor=\"let media of post.media; let i = index\">\n            <img *ngIf=\"media.type === 'image'\" \n                 [src]=\"media.url\" \n                 [alt]=\"media.alt\"\n                 class=\"post-image\">\n            \n            <video *ngIf=\"media.type === 'video'\"\n                   [src]=\"media.url\"\n                   class=\"post-video\"\n                   controls>\n            </video>\n          </div>\n\n          <!-- Product Tags -->\n          <div class=\"product-tags\" *ngIf=\"post.products.length > 0\">\n            <div class=\"product-tag\" \n                 *ngFor=\"let productTag of post.products\"\n                 [style.left.%]=\"productTag.position.x\"\n                 [style.top.%]=\"productTag.position.y\"\n                 (click)=\"showProductDetails(productTag.product)\">\n              <div class=\"product-tag-icon\">\n                <i class=\"fas fa-shopping-bag\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Actions -->\n        <div class=\"post-actions\">\n          <div class=\"primary-actions\">\n            <button class=\"action-btn like\" \n                    [class.liked]=\"post.isLiked\" \n                    (click)=\"toggleLike()\">\n              <i class=\"fas fa-heart\"></i>\n              <span>{{ post.likes.length }}</span>\n            </button>\n            \n            <button class=\"action-btn comment\" (click)=\"focusCommentInput()\">\n              <i class=\"fas fa-comment\"></i>\n              <span>{{ post.comments.length }}</span>\n            </button>\n            \n            <button class=\"action-btn share\" (click)=\"sharePost()\">\n              <i class=\"fas fa-paper-plane\"></i>\n            </button>\n          </div>\n          \n          <button class=\"action-btn save\" \n                  [class.saved]=\"post.isSaved\" \n                  (click)=\"toggleSave()\">\n            <i class=\"fas fa-bookmark\"></i>\n          </button>\n        </div>\n\n        <!-- Caption -->\n        <div class=\"post-caption\">\n          <span class=\"username\">{{ post.user.username }}</span>\n          <span class=\"caption-text\">{{ post.caption }}</span>\n          \n          <div class=\"hashtags\" *ngIf=\"post.hashtags.length > 0\">\n            <span class=\"hashtag\" \n                  *ngFor=\"let hashtag of post.hashtags\"\n                  (click)=\"searchHashtag(hashtag)\">\n              #{{ hashtag }}\n            </span>\n          </div>\n        </div>\n\n        <!-- E-commerce Actions -->\n        <div class=\"ecommerce-actions\" *ngIf=\"post.products.length > 0\">\n          <button class=\"ecom-btn buy-now\" (click)=\"buyNow()\">\n            <i class=\"fas fa-bolt\"></i>\n            Buy Now\n          </button>\n          <button class=\"ecom-btn add-cart\" (click)=\"addToCart()\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            Add to Cart\n          </button>\n          <button class=\"ecom-btn wishlist\" (click)=\"addToWishlist()\">\n            <i class=\"fas fa-heart\"></i>\n            Wishlist\n          </button>\n        </div>\n\n        <!-- Comments Section -->\n        <div class=\"comments-section\">\n          <h3>Comments</h3>\n          \n          <div class=\"comments-list\">\n            <div class=\"comment\" *ngFor=\"let comment of post.comments\">\n              <img [src]=\"comment.user.avatar || '/assets/images/default-avatar.png'\" \n                   [alt]=\"comment.user.fullName\" \n                   class=\"comment-avatar\"\n                   (click)=\"viewProfile(comment.user._id)\">\n              <div class=\"comment-content\">\n                <div class=\"comment-header\">\n                  <span class=\"comment-username\" (click)=\"viewProfile(comment.user._id)\">\n                    {{ comment.user.username }}\n                  </span>\n                  <span class=\"comment-time\">{{ getTimeAgo(comment.commentedAt) }}</span>\n                </div>\n                <p class=\"comment-text\">{{ comment.text }}</p>\n              </div>\n            </div>\n          </div>\n          \n          <!-- Add Comment -->\n          <div class=\"add-comment\">\n            <img [src]=\"currentUser?.avatar || '/assets/images/default-avatar.png'\" \n                 [alt]=\"currentUser?.fullName\" \n                 class=\"comment-avatar\">\n            <input type=\"text\" \n                   #commentInput\n                   [(ngModel)]=\"newComment\" \n                   placeholder=\"Add a comment...\"\n                   (keyup.enter)=\"addComment()\"\n                   class=\"comment-input\">\n            <button class=\"btn-post-comment\" \n                    (click)=\"addComment()\"\n                    [disabled]=\"!newComment || !newComment.trim()\">\n              Post\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Product Modal -->\n    <div class=\"product-modal\" *ngIf=\"selectedProduct\" (click)=\"closeProductModal()\">\n      <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n        <div class=\"modal-header\">\n          <h3>{{ selectedProduct.name }}</h3>\n          <button class=\"btn-close\" (click)=\"closeProductModal()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        \n        <div class=\"modal-body\">\n          <img [src]=\"selectedProduct.images[0]?.url\" \n               [alt]=\"selectedProduct.name\" \n               class=\"product-image\">\n          \n          <div class=\"product-info\">\n            <p class=\"brand\">{{ selectedProduct.brand }}</p>\n            <div class=\"price\">\n              <span class=\"current-price\">₹{{ selectedProduct.price | number:'1.0-0' }}</span>\n              <span class=\"original-price\" *ngIf=\"selectedProduct.originalPrice\">\n                ₹{{ selectedProduct.originalPrice | number:'1.0-0' }}\n              </span>\n            </div>\n          </div>\n          \n          <div class=\"modal-actions\">\n            <button class=\"btn-primary\" (click)=\"buyProductNow()\">Buy Now</button>\n            <button class=\"btn-secondary\" (click)=\"addProductToCart()\">Add to Cart</button>\n            <button class=\"btn-outline\" (click)=\"addProductToWishlist()\">Add to Wishlist</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .post-detail-container {\n      max-width: 600px;\n      margin: 0 auto;\n      background: #fff;\n      min-height: 100vh;\n    }\n\n    .detail-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 16px 20px;\n      border-bottom: 1px solid #eee;\n      position: sticky;\n      top: 0;\n      background: #fff;\n      z-index: 100;\n    }\n\n    .detail-header h1 {\n      margin: 0;\n      font-size: 1.1rem;\n      font-weight: 600;\n      color: #333;\n    }\n\n    .btn-back, .btn-menu {\n      background: none;\n      border: none;\n      font-size: 1.2rem;\n      color: #333;\n      cursor: pointer;\n      padding: 8px;\n      border-radius: 50%;\n    }\n\n    .btn-back:hover, .btn-menu:hover {\n      background: #f8f9fa;\n    }\n\n    .post-detail-content {\n      padding: 0;\n    }\n\n    .post-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 20px;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      cursor: pointer;\n    }\n\n    .user-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n    }\n\n    .user-details {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .username-row {\n      display: flex;\n      align-items: center;\n      gap: 4px;\n    }\n\n    .username {\n      font-weight: 600;\n      color: #333;\n      font-size: 0.9rem;\n    }\n\n    .verified {\n      color: #1da1f2;\n      font-size: 0.8rem;\n    }\n\n    .post-time {\n      font-size: 0.8rem;\n      color: #666;\n    }\n\n    .btn-follow {\n      background: #007bff;\n      color: #fff;\n      border: none;\n      padding: 8px 16px;\n      border-radius: 6px;\n      font-weight: 500;\n      cursor: pointer;\n    }\n\n    .btn-follow:hover {\n      background: #0056b3;\n    }\n\n    .post-media {\n      position: relative;\n      background: #000;\n    }\n\n    .media-container {\n      width: 100%;\n      aspect-ratio: 1;\n      overflow: hidden;\n    }\n\n    .post-image, .post-video {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n\n    .product-tags {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      pointer-events: none;\n    }\n\n    .product-tag {\n      position: absolute;\n      pointer-events: all;\n      cursor: pointer;\n      transform: translate(-50%, -50%);\n    }\n\n    .product-tag-icon {\n      width: 32px;\n      height: 32px;\n      background: rgba(255,255,255,0.9);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #333;\n      animation: pulse 2s infinite;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.2);\n    }\n\n    @keyframes pulse {\n      0%, 100% { transform: translate(-50%, -50%) scale(1); }\n      50% { transform: translate(-50%, -50%) scale(1.1); }\n    }\n\n    .post-actions {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 12px 20px;\n      border-bottom: 1px solid #f0f0f0;\n    }\n\n    .primary-actions {\n      display: flex;\n      gap: 20px;\n    }\n\n    .action-btn {\n      background: none;\n      border: none;\n      font-size: 1.2rem;\n      color: #333;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      gap: 6px;\n      padding: 8px;\n      border-radius: 50%;\n      transition: all 0.2s ease;\n    }\n\n    .action-btn:hover {\n      background: #f8f9fa;\n    }\n\n    .action-btn.liked {\n      color: #ff6b6b;\n    }\n\n    .action-btn.saved {\n      color: #333;\n    }\n\n    .action-btn span {\n      font-size: 0.9rem;\n      font-weight: 500;\n    }\n\n    .post-caption {\n      padding: 16px 20px;\n      line-height: 1.4;\n      border-bottom: 1px solid #f0f0f0;\n    }\n\n    .caption-text {\n      margin-left: 8px;\n      color: #333;\n    }\n\n    .hashtags {\n      margin-top: 8px;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n    }\n\n    .hashtag {\n      color: #1da1f2;\n      cursor: pointer;\n      font-size: 0.9rem;\n    }\n\n    .hashtag:hover {\n      text-decoration: underline;\n    }\n\n    .ecommerce-actions {\n      display: flex;\n      gap: 8px;\n      padding: 16px 20px;\n      border-bottom: 1px solid #f0f0f0;\n    }\n\n    .ecom-btn {\n      flex: 1;\n      padding: 12px;\n      border: none;\n      border-radius: 6px;\n      font-weight: 600;\n      font-size: 0.9rem;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      transition: all 0.2s ease;\n    }\n\n    .buy-now {\n      background: #ff6b6b;\n      color: #fff;\n    }\n\n    .add-cart {\n      background: #4ecdc4;\n      color: #fff;\n    }\n\n    .wishlist {\n      background: #ff9ff3;\n      color: #fff;\n    }\n\n    .ecom-btn:hover {\n      transform: translateY(-1px);\n      box-shadow: 0 2px 8px rgba(0,0,0,0.2);\n    }\n\n    .comments-section {\n      padding: 20px;\n    }\n\n    .comments-section h3 {\n      margin: 0 0 16px 0;\n      font-size: 1.1rem;\n      font-weight: 600;\n      color: #333;\n    }\n\n    .comments-list {\n      margin-bottom: 20px;\n    }\n\n    .comment {\n      display: flex;\n      gap: 12px;\n      margin-bottom: 16px;\n    }\n\n    .comment-avatar {\n      width: 32px;\n      height: 32px;\n      border-radius: 50%;\n      cursor: pointer;\n    }\n\n    .comment-content {\n      flex: 1;\n    }\n\n    .comment-header {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-bottom: 4px;\n    }\n\n    .comment-username {\n      font-weight: 600;\n      color: #333;\n      cursor: pointer;\n      font-size: 0.9rem;\n    }\n\n    .comment-username:hover {\n      text-decoration: underline;\n    }\n\n    .comment-time {\n      font-size: 0.8rem;\n      color: #666;\n    }\n\n    .comment-text {\n      margin: 0;\n      color: #333;\n      line-height: 1.4;\n      font-size: 0.9rem;\n    }\n\n    .add-comment {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      padding-top: 16px;\n      border-top: 1px solid #f0f0f0;\n    }\n\n    .comment-input {\n      flex: 1;\n      border: 1px solid #ddd;\n      border-radius: 20px;\n      padding: 8px 16px;\n      font-size: 0.9rem;\n      outline: none;\n    }\n\n    .comment-input:focus {\n      border-color: #007bff;\n    }\n\n    .btn-post-comment {\n      background: #007bff;\n      color: #fff;\n      border: none;\n      padding: 8px 16px;\n      border-radius: 20px;\n      font-weight: 500;\n      cursor: pointer;\n      font-size: 0.9rem;\n    }\n\n    .btn-post-comment:disabled {\n      background: #ccc;\n      cursor: not-allowed;\n    }\n\n    .btn-post-comment:hover:not(:disabled) {\n      background: #0056b3;\n    }\n\n    /* Product Modal */\n    .product-modal {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: rgba(0,0,0,0.8);\n      z-index: 1000;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n    }\n\n    .modal-content {\n      background: #fff;\n      border-radius: 12px;\n      max-width: 400px;\n      width: 100%;\n      max-height: 80vh;\n      overflow-y: auto;\n    }\n\n    .modal-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 20px;\n      border-bottom: 1px solid #eee;\n    }\n\n    .modal-header h3 {\n      margin: 0;\n      font-size: 1.1rem;\n      font-weight: 600;\n    }\n\n    .btn-close {\n      background: none;\n      border: none;\n      font-size: 1.2rem;\n      cursor: pointer;\n      color: #666;\n      padding: 4px;\n    }\n\n    .modal-body {\n      padding: 20px;\n    }\n\n    .product-image {\n      width: 100%;\n      height: 200px;\n      object-fit: cover;\n      border-radius: 8px;\n      margin-bottom: 16px;\n    }\n\n    .product-info {\n      margin-bottom: 20px;\n    }\n\n    .brand {\n      color: #666;\n      font-size: 0.9rem;\n      margin-bottom: 8px;\n    }\n\n    .price {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .current-price {\n      font-size: 1.2rem;\n      font-weight: 700;\n      color: #333;\n    }\n\n    .original-price {\n      font-size: 0.9rem;\n      color: #999;\n      text-decoration: line-through;\n    }\n\n    .modal-actions {\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n    }\n\n    .btn-primary, .btn-secondary, .btn-outline {\n      padding: 12px;\n      border: none;\n      border-radius: 6px;\n      font-weight: 600;\n      cursor: pointer;\n      transition: all 0.2s ease;\n    }\n\n    .btn-primary {\n      background: #007bff;\n      color: #fff;\n    }\n\n    .btn-secondary {\n      background: #6c757d;\n      color: #fff;\n    }\n\n    .btn-outline {\n      background: transparent;\n      color: #007bff;\n      border: 1px solid #007bff;\n    }\n\n    /* Responsive */\n    @media (max-width: 768px) {\n      .post-detail-container {\n        margin: 0;\n      }\n\n      .detail-header {\n        padding: 12px 16px;\n      }\n\n      .post-header {\n        padding: 12px 16px;\n      }\n\n      .post-actions {\n        padding: 8px 16px;\n      }\n\n      .post-caption {\n        padding: 12px 16px;\n      }\n\n      .ecommerce-actions {\n        padding: 12px 16px;\n        flex-direction: column;\n        gap: 8px;\n      }\n\n      .comments-section {\n        padding: 16px;\n      }\n    }\n  `]\n})\nexport class PostDetailComponent implements OnInit {\n  post: Post | null = null;\n  selectedProduct: any = null;\n  newComment = '';\n  currentUser: any = null;\n  isOwnPost = false;\n  loading = true;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadCurrentUser();\n    this.route.params.subscribe(params => {\n      if (params['id']) {\n        this.loadPost(params['id']);\n      }\n    });\n  }\n\n  loadCurrentUser() {\n    // TODO: Get from auth service\n    this.currentUser = {\n      _id: 'current-user',\n      username: 'you',\n      fullName: 'Your Name',\n      avatar: ''\n    };\n  }\n\n  loadPost(postId: string) {\n    this.loading = true;\n    \n    // Load post from real API\n    fetch(`http://localhost:5000/api/posts/${postId}`)\n      .then(response => response.json())\n      .then(data => {\n        if (data.success) {\n          this.post = {\n            ...data.post,\n            isLiked: false, // TODO: Check if current user liked this post\n            isSaved: false  // TODO: Check if current user saved this post\n          };\n          this.isOwnPost = this.post?.user?._id === this.currentUser?._id;\n        }\n        this.loading = false;\n      })\n      .catch(error => {\n        console.error('Error loading post:', error);\n        this.loading = false;\n      });\n  }\n\n  goBack() {\n    this.router.navigate(['/social']);\n  }\n\n  showMenu() {\n    // TODO: Show post menu\n    console.log('Show post menu');\n  }\n\n  viewProfile(userId: string) {\n    this.router.navigate(['/profile', userId]);\n  }\n\n  toggleLike() {\n    if (!this.post) return;\n    \n    this.post.isLiked = !this.post.isLiked;\n    \n    if (this.post.isLiked) {\n      this.post.likes.push({\n        user: this.currentUser._id,\n        likedAt: new Date()\n      });\n    } else {\n      this.post.likes = this.post.likes.filter(like => like.user !== this.currentUser._id);\n    }\n    \n    // TODO: Update like status via API\n    console.log('Toggle like for post:', this.post._id, this.post.isLiked);\n  }\n\n  toggleSave() {\n    if (!this.post) return;\n    \n    this.post.isSaved = !this.post.isSaved;\n    \n    if (this.post.isSaved) {\n      this.post.saves.push({\n        user: this.currentUser._id,\n        savedAt: new Date()\n      });\n    } else {\n      this.post.saves = this.post.saves.filter(save => save.user !== this.currentUser._id);\n    }\n    \n    // TODO: Update save status via API\n    console.log('Toggle save for post:', this.post._id, this.post.isSaved);\n  }\n\n  sharePost() {\n    if (!this.post) return;\n    \n    // TODO: Implement share functionality\n    console.log('Share post:', this.post);\n    \n    if (navigator.share) {\n      navigator.share({\n        title: `${this.post.user.username}'s post`,\n        text: this.post.caption,\n        url: window.location.href\n      });\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href);\n      alert('Link copied to clipboard!');\n    }\n  }\n\n  focusCommentInput() {\n    const commentInput = document.querySelector('.comment-input') as HTMLInputElement;\n    if (commentInput) {\n      commentInput.focus();\n    }\n  }\n\n  addComment() {\n    if (!this.post || !this.newComment?.trim()) return;\n\n    const newComment = {\n      _id: Date.now().toString(),\n      user: {\n        _id: this.currentUser._id,\n        username: this.currentUser.username,\n        fullName: this.currentUser.fullName,\n        avatar: this.currentUser.avatar\n      },\n      text: this.newComment.trim(),\n      commentedAt: new Date()\n    };\n\n    this.post.comments.push(newComment);\n    this.newComment = '';\n\n    // TODO: Add comment via API\n    console.log('Add comment to post:', this.post._id, newComment);\n  }\n\n  searchHashtag(hashtag: string) {\n    this.router.navigate(['/search'], { queryParams: { hashtag } });\n  }\n\n  // E-commerce actions\n  buyNow() {\n    if (this.post && this.post.products.length > 0) {\n      const product = this.post.products[0].product;\n      this.router.navigate(['/checkout'], { \n        queryParams: { productId: product._id, source: 'post' } \n      });\n    }\n  }\n\n  addToCart() {\n    if (this.post && this.post.products.length > 0) {\n      const product = this.post.products[0].product;\n      // TODO: Add to cart via service\n      console.log('Add to cart from post:', product);\n      alert(`${product.name} added to cart!`);\n    }\n  }\n\n  addToWishlist() {\n    if (this.post && this.post.products.length > 0) {\n      const product = this.post.products[0].product;\n      // TODO: Add to wishlist via service\n      console.log('Add to wishlist from post:', product);\n      alert(`${product.name} added to wishlist!`);\n    }\n  }\n\n  // Product modal\n  showProductDetails(product: any) {\n    this.selectedProduct = product;\n  }\n\n  closeProductModal() {\n    this.selectedProduct = null;\n  }\n\n  buyProductNow() {\n    if (this.selectedProduct) {\n      this.router.navigate(['/checkout'], { \n        queryParams: { productId: this.selectedProduct._id, source: 'post' } \n      });\n    }\n  }\n\n  addProductToCart() {\n    if (this.selectedProduct) {\n      // TODO: Add to cart via service\n      console.log('Add product to cart:', this.selectedProduct);\n      alert(`${this.selectedProduct.name} added to cart!`);\n      this.closeProductModal();\n    }\n  }\n\n  addProductToWishlist() {\n    if (this.selectedProduct) {\n      // TODO: Add to wishlist via service\n      console.log('Add product to wishlist:', this.selectedProduct);\n      alert(`${this.selectedProduct.name} added to wishlist!`);\n      this.closeProductModal();\n    }\n  }\n\n  getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diffMs = now.getTime() - new Date(date).getTime();\n    const diffMinutes = Math.floor(diffMs / (1000 * 60));\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n    \n    if (diffMinutes < 1) return 'now';\n    if (diffMinutes < 60) return `${diffMinutes}m`;\n    if (diffHours < 24) return `${diffHours}h`;\n    if (diffDays < 7) return `${diffDays}d`;\n    return new Date(date).toLocaleDateString();\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;IA6E5BC,EAAA,CAAAC,SAAA,YAAyE;;;;;IAM/ED,EAAA,CAAAE,cAAA,iBAA8C;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IAM3DJ,EAAA,CAAAC,SAAA,cAGwB;;;;IADnBD,EADA,CAAAK,UAAA,QAAAC,QAAA,CAAAC,GAAA,EAAAP,EAAA,CAAAQ,aAAA,CAAiB,QAAAF,QAAA,CAAAG,GAAA,CACA;;;;;IAGtBT,EAAA,CAAAC,SAAA,gBAIQ;;;;IAHDD,EAAA,CAAAK,UAAA,QAAAC,QAAA,CAAAC,GAAA,EAAAP,EAAA,CAAAQ,aAAA,CAAiB;;;;;IAP1BR,EAAA,CAAAE,cAAA,cAA6E;IAM3EF,EALA,CAAAU,UAAA,IAAAC,+CAAA,kBAGwB,IAAAC,iDAAA,oBAKR;IAElBZ,EAAA,CAAAI,YAAA,EAAM;;;;IAVEJ,EAAA,CAAAa,SAAA,EAA4B;IAA5Bb,EAAA,CAAAK,UAAA,SAAAC,QAAA,CAAAQ,IAAA,aAA4B;IAK1Bd,EAAA,CAAAa,SAAA,EAA4B;IAA5Bb,EAAA,CAAAK,UAAA,SAAAC,QAAA,CAAAQ,IAAA,aAA4B;;;;;;IASpCd,EAAA,CAAAE,cAAA,cAIsD;IAAjDF,EAAA,CAAAe,UAAA,mBAAAC,qEAAA;MAAA,MAAAC,aAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAG,kBAAA,CAAAP,aAAA,CAAAQ,OAAA,CAAsC;IAAA,EAAC;IACnDzB,EAAA,CAAAE,cAAA,cAA8B;IAC5BF,EAAA,CAAAC,SAAA,YAAmC;IAEvCD,EADE,CAAAI,YAAA,EAAM,EACF;;;;IALDJ,EADA,CAAA0B,WAAA,SAAAT,aAAA,CAAAU,QAAA,CAAAC,CAAA,MAAsC,QAAAX,aAAA,CAAAU,QAAA,CAAAE,CAAA,MACD;;;;;IAJ5C7B,EAAA,CAAAE,cAAA,cAA2D;IACzDF,EAAA,CAAAU,UAAA,IAAAoB,+CAAA,kBAIsD;IAKxD9B,EAAA,CAAAI,YAAA,EAAM;;;;IARwBJ,EAAA,CAAAa,SAAA,EAAgB;IAAhBb,EAAA,CAAAK,UAAA,YAAAgB,MAAA,CAAAU,IAAA,CAAAC,QAAA,CAAgB;;;;;;IA4C5ChC,EAAA,CAAAE,cAAA,eAEuC;IAAjCF,EAAA,CAAAe,UAAA,mBAAAkB,uEAAA;MAAA,MAAAC,UAAA,GAAAlC,EAAA,CAAAkB,aAAA,CAAAiB,GAAA,EAAAf,SAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAe,aAAA,CAAAF,UAAA,CAAsB;IAAA,EAAC;IACpClC,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAqC,kBAAA,OAAAH,UAAA,MACF;;;;;IALFlC,EAAA,CAAAE,cAAA,cAAuD;IACrDF,EAAA,CAAAU,UAAA,IAAA4B,gDAAA,mBAEuC;IAGzCtC,EAAA,CAAAI,YAAA,EAAM;;;;IAJsBJ,EAAA,CAAAa,SAAA,EAAgB;IAAhBb,EAAA,CAAAK,UAAA,YAAAgB,MAAA,CAAAU,IAAA,CAAAQ,QAAA,CAAgB;;;;;;IAS5CvC,EADF,CAAAE,cAAA,cAAgE,iBACV;IAAnBF,EAAA,CAAAe,UAAA,mBAAAyB,kEAAA;MAAAxC,EAAA,CAAAkB,aAAA,CAAAuB,GAAA;MAAA,MAAApB,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAqB,MAAA,EAAQ;IAAA,EAAC;IACjD1C,EAAA,CAAAC,SAAA,YAA2B;IAC3BD,EAAA,CAAAG,MAAA,gBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,iBAAwD;IAAtBF,EAAA,CAAAe,UAAA,mBAAA4B,kEAAA;MAAA3C,EAAA,CAAAkB,aAAA,CAAAuB,GAAA;MAAA,MAAApB,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAuB,SAAA,EAAW;IAAA,EAAC;IACrD5C,EAAA,CAAAC,SAAA,YAAoC;IACpCD,EAAA,CAAAG,MAAA,oBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,iBAA4D;IAA1BF,EAAA,CAAAe,UAAA,mBAAA8B,kEAAA;MAAA7C,EAAA,CAAAkB,aAAA,CAAAuB,GAAA;MAAA,MAAApB,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAyB,aAAA,EAAe;IAAA,EAAC;IACzD9C,EAAA,CAAAC,SAAA,YAA4B;IAC5BD,EAAA,CAAAG,MAAA,iBACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;;;;IAQAJ,EADF,CAAAE,cAAA,cAA2D,cAIZ;IAAxCF,EAAA,CAAAe,UAAA,mBAAAgC,+DAAA;MAAA,MAAAC,WAAA,GAAAhD,EAAA,CAAAkB,aAAA,CAAA+B,GAAA,EAAA7B,SAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAA6B,WAAA,CAAAF,WAAA,CAAAG,IAAA,CAAAC,GAAA,CAA6B;IAAA,EAAC;IAH5CpD,EAAA,CAAAI,YAAA,EAG6C;IAGzCJ,EAFJ,CAAAE,cAAA,cAA6B,cACC,eAC6C;IAAxCF,EAAA,CAAAe,UAAA,mBAAAsC,gEAAA;MAAA,MAAAL,WAAA,GAAAhD,EAAA,CAAAkB,aAAA,CAAA+B,GAAA,EAAA7B,SAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAA6B,WAAA,CAAAF,WAAA,CAAAG,IAAA,CAAAC,GAAA,CAA6B;IAAA,EAAC;IACpEpD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAE,cAAA,eAA2B;IAAAF,EAAA,CAAAG,MAAA,GAAqC;IAClEH,EADkE,CAAAI,YAAA,EAAO,EACnE;IACNJ,EAAA,CAAAE,cAAA,YAAwB;IAAAF,EAAA,CAAAG,MAAA,GAAkB;IAE9CH,EAF8C,CAAAI,YAAA,EAAI,EAC1C,EACF;;;;;IAbCJ,EAAA,CAAAa,SAAA,EAAkE;IAClEb,EADA,CAAAK,UAAA,QAAA2C,WAAA,CAAAG,IAAA,CAAAG,MAAA,yCAAAtD,EAAA,CAAAQ,aAAA,CAAkE,QAAAwC,WAAA,CAAAG,IAAA,CAAAI,QAAA,CACrC;IAM5BvD,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAqC,kBAAA,MAAAW,WAAA,CAAAG,IAAA,CAAAK,QAAA,MACF;IAC2BxD,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAyD,iBAAA,CAAApC,MAAA,CAAAqC,UAAA,CAAAV,WAAA,CAAAW,WAAA,EAAqC;IAE1C3D,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAyD,iBAAA,CAAAT,WAAA,CAAAY,IAAA,CAAkB;;;;;;IApIlD5D,EAHJ,CAAAE,cAAA,aAAgD,gBAEhB,gBACgB;IAAnBF,EAAA,CAAAe,UAAA,mBAAA8C,2DAAA;MAAA7D,EAAA,CAAAkB,aAAA,CAAA4C,GAAA;MAAA,MAAAzC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAA0C,MAAA,EAAQ;IAAA,EAAC;IACzC/D,EAAA,CAAAC,SAAA,WAAiC;IACnCD,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,SAAI;IAAAF,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACbJ,EAAA,CAAAE,cAAA,gBAA8C;IAArBF,EAAA,CAAAe,UAAA,mBAAAiD,2DAAA;MAAAhE,EAAA,CAAAkB,aAAA,CAAA4C,GAAA;MAAA,MAAAzC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAA4C,QAAA,EAAU;IAAA,EAAC;IAC3CjE,EAAA,CAAAC,SAAA,WAAiC;IAErCD,EADE,CAAAI,YAAA,EAAS,EACF;IAMLJ,EAHJ,CAAAE,cAAA,aAAiC,cAEN,eACqC;IAArCF,EAAA,CAAAe,UAAA,mBAAAmD,yDAAA;MAAAlE,EAAA,CAAAkB,aAAA,CAAA4C,GAAA;MAAA,MAAAzC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAA6B,WAAA,CAAA7B,MAAA,CAAAU,IAAA,CAAAoB,IAAA,CAAAC,GAAA,CAA0B;IAAA,EAAC;IACzDpD,EAAA,CAAAC,SAAA,eAEyB;IAGrBD,EAFJ,CAAAE,cAAA,eAA0B,eACE,gBACD;IAAAF,EAAA,CAAAG,MAAA,IAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACtDJ,EAAA,CAAAU,UAAA,KAAAyD,uCAAA,gBAAqE;IACvEnE,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,gBAAwB;IAAAF,EAAA,CAAAG,MAAA,IAAgC;IAE5DH,EAF4D,CAAAI,YAAA,EAAO,EAC3D,EACF;IAENJ,EAAA,CAAAU,UAAA,KAAA0D,4CAAA,qBAA8C;IAChDpE,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAAwB;IAetBF,EAdA,CAAAU,UAAA,KAAA2D,yCAAA,kBAA6E,KAAAC,yCAAA,kBAclB;IAW7DtE,EAAA,CAAAI,YAAA,EAAM;IAKFJ,EAFJ,CAAAE,cAAA,eAA0B,eACK,kBAGI;IAAvBF,EAAA,CAAAe,UAAA,mBAAAwD,4DAAA;MAAAvE,EAAA,CAAAkB,aAAA,CAAA4C,GAAA;MAAA,MAAAzC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAmD,UAAA,EAAY;IAAA,EAAC;IAC5BxE,EAAA,CAAAC,SAAA,aAA4B;IAC5BD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,IAAuB;IAC/BH,EAD+B,CAAAI,YAAA,EAAO,EAC7B;IAETJ,EAAA,CAAAE,cAAA,kBAAiE;IAA9BF,EAAA,CAAAe,UAAA,mBAAA0D,4DAAA;MAAAzE,EAAA,CAAAkB,aAAA,CAAA4C,GAAA;MAAA,MAAAzC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAqD,iBAAA,EAAmB;IAAA,EAAC;IAC9D1E,EAAA,CAAAC,SAAA,aAA8B;IAC9BD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,IAA0B;IAClCH,EADkC,CAAAI,YAAA,EAAO,EAChC;IAETJ,EAAA,CAAAE,cAAA,kBAAuD;IAAtBF,EAAA,CAAAe,UAAA,mBAAA4D,4DAAA;MAAA3E,EAAA,CAAAkB,aAAA,CAAA4C,GAAA;MAAA,MAAAzC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAuD,SAAA,EAAW;IAAA,EAAC;IACpD5E,EAAA,CAAAC,SAAA,aAAkC;IAEtCD,EADE,CAAAI,YAAA,EAAS,EACL;IAENJ,EAAA,CAAAE,cAAA,kBAE+B;IAAvBF,EAAA,CAAAe,UAAA,mBAAA8D,4DAAA;MAAA7E,EAAA,CAAAkB,aAAA,CAAA4C,GAAA;MAAA,MAAAzC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAyD,UAAA,EAAY;IAAA,EAAC;IAC5B9E,EAAA,CAAAC,SAAA,aAA+B;IAEnCD,EADE,CAAAI,YAAA,EAAS,EACL;IAIJJ,EADF,CAAAE,cAAA,eAA0B,gBACD;IAAAF,EAAA,CAAAG,MAAA,IAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACtDJ,EAAA,CAAAE,cAAA,gBAA2B;IAAAF,EAAA,CAAAG,MAAA,IAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEpDJ,EAAA,CAAAU,UAAA,KAAAqE,yCAAA,kBAAuD;IAOzD/E,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAU,UAAA,KAAAsE,yCAAA,mBAAgE;IAiB9DhF,EADF,CAAAE,cAAA,eAA8B,UACxB;IAAAF,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEjBJ,EAAA,CAAAE,cAAA,eAA2B;IACzBF,EAAA,CAAAU,UAAA,KAAAuE,yCAAA,mBAA2D;IAe7DjF,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAAyB;IACvBF,EAAA,CAAAC,SAAA,eAE4B;IAC5BD,EAAA,CAAAE,cAAA,oBAK6B;IAHtBF,EAAA,CAAAkF,gBAAA,2BAAAC,mEAAAC,MAAA;MAAApF,EAAA,CAAAkB,aAAA,CAAA4C,GAAA;MAAA,MAAAzC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAqF,kBAAA,CAAAhE,MAAA,CAAAiE,UAAA,EAAAF,MAAA,MAAA/D,MAAA,CAAAiE,UAAA,GAAAF,MAAA;MAAA,OAAApF,EAAA,CAAAuB,WAAA,CAAA6D,MAAA;IAAA,EAAwB;IAExBpF,EAAA,CAAAe,UAAA,yBAAAwE,iEAAA;MAAAvF,EAAA,CAAAkB,aAAA,CAAA4C,GAAA;MAAA,MAAAzC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAeF,MAAA,CAAAmE,UAAA,EAAY;IAAA,EAAC;IAJnCxF,EAAA,CAAAI,YAAA,EAK6B;IAC7BJ,EAAA,CAAAE,cAAA,kBAEuD;IAD/CF,EAAA,CAAAe,UAAA,mBAAA0E,4DAAA;MAAAzF,EAAA,CAAAkB,aAAA,CAAA4C,GAAA;MAAA,MAAAzC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAmE,UAAA,EAAY;IAAA,EAAC;IAE5BxF,EAAA,CAAAG,MAAA,cACF;IAIRH,EAJQ,CAAAI,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IA9IOJ,EAAA,CAAAa,SAAA,IAA+D;IAC/Db,EADA,CAAAK,UAAA,QAAAgB,MAAA,CAAAU,IAAA,CAAAoB,IAAA,CAAAG,MAAA,yCAAAtD,EAAA,CAAAQ,aAAA,CAA+D,QAAAa,MAAA,CAAAU,IAAA,CAAAoB,IAAA,CAAAI,QAAA,CACrC;IAIJvD,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAyD,iBAAA,CAAApC,MAAA,CAAAU,IAAA,CAAAoB,IAAA,CAAAK,QAAA,CAAwB;IACNxD,EAAA,CAAAa,SAAA,EAA0B;IAA1Bb,EAAA,CAAAK,UAAA,SAAAgB,MAAA,CAAAU,IAAA,CAAAoB,IAAA,CAAAuC,UAAA,CAA0B;IAE7C1F,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAyD,iBAAA,CAAApC,MAAA,CAAAqC,UAAA,CAAArC,MAAA,CAAAU,IAAA,CAAA4D,SAAA,EAAgC;IAIhC3F,EAAA,CAAAa,SAAA,EAAgB;IAAhBb,EAAA,CAAAK,UAAA,UAAAgB,MAAA,CAAAuE,SAAA,CAAgB;IAKG5F,EAAA,CAAAa,SAAA,GAAe;IAAfb,EAAA,CAAAK,UAAA,YAAAgB,MAAA,CAAAU,IAAA,CAAA8D,KAAA,CAAe;IAcnC7F,EAAA,CAAAa,SAAA,EAA8B;IAA9Bb,EAAA,CAAAK,UAAA,SAAAgB,MAAA,CAAAU,IAAA,CAAAC,QAAA,CAAA8D,MAAA,KAA8B;IAiB/C9F,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAA+F,WAAA,UAAA1E,MAAA,CAAAU,IAAA,CAAAiE,OAAA,CAA4B;IAG5BhG,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAyD,iBAAA,CAAApC,MAAA,CAAAU,IAAA,CAAAkE,KAAA,CAAAH,MAAA,CAAuB;IAKvB9F,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAyD,iBAAA,CAAApC,MAAA,CAAAU,IAAA,CAAAmE,QAAA,CAAAJ,MAAA,CAA0B;IAS5B9F,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAA+F,WAAA,UAAA1E,MAAA,CAAAU,IAAA,CAAAoE,OAAA,CAA4B;IAQbnG,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAyD,iBAAA,CAAApC,MAAA,CAAAU,IAAA,CAAAoB,IAAA,CAAAK,QAAA,CAAwB;IACpBxD,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAyD,iBAAA,CAAApC,MAAA,CAAAU,IAAA,CAAAqE,OAAA,CAAkB;IAEtBpG,EAAA,CAAAa,SAAA,EAA8B;IAA9Bb,EAAA,CAAAK,UAAA,SAAAgB,MAAA,CAAAU,IAAA,CAAAQ,QAAA,CAAAuD,MAAA,KAA8B;IAUvB9F,EAAA,CAAAa,SAAA,EAA8B;IAA9Bb,EAAA,CAAAK,UAAA,SAAAgB,MAAA,CAAAU,IAAA,CAAAC,QAAA,CAAA8D,MAAA,KAA8B;IAoBjB9F,EAAA,CAAAa,SAAA,GAAgB;IAAhBb,EAAA,CAAAK,UAAA,YAAAgB,MAAA,CAAAU,IAAA,CAAAmE,QAAA,CAAgB;IAmBpDlG,EAAA,CAAAa,SAAA,GAAkE;IAClEb,EADA,CAAAK,UAAA,SAAAgB,MAAA,CAAAgF,WAAA,kBAAAhF,MAAA,CAAAgF,WAAA,CAAA/C,MAAA,0CAAAtD,EAAA,CAAAQ,aAAA,CAAkE,QAAAa,MAAA,CAAAgF,WAAA,kBAAAhF,MAAA,CAAAgF,WAAA,CAAA9C,QAAA,CACrC;IAI3BvD,EAAA,CAAAa,SAAA,EAAwB;IAAxBb,EAAA,CAAAsG,gBAAA,YAAAjF,MAAA,CAAAiE,UAAA,CAAwB;IAMvBtF,EAAA,CAAAa,SAAA,GAA8C;IAA9Cb,EAAA,CAAAK,UAAA,cAAAgB,MAAA,CAAAiE,UAAA,KAAAjE,MAAA,CAAAiE,UAAA,CAAAiB,IAAA,GAA8C;;;;;IA2BpDvG,EAAA,CAAAE,cAAA,eAAmE;IACjEF,EAAA,CAAAG,MAAA,GACF;;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAqC,kBAAA,YAAArC,EAAA,CAAAwG,WAAA,OAAAnF,MAAA,CAAAoF,eAAA,CAAAC,aAAA,gBACF;;;;;;IApBV1G,EAAA,CAAAE,cAAA,cAAiF;IAA9BF,EAAA,CAAAe,UAAA,mBAAA4F,wDAAA;MAAA3G,EAAA,CAAAkB,aAAA,CAAA0F,IAAA;MAAA,MAAAvF,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAwF,iBAAA,EAAmB;IAAA,EAAC;IAC9E7G,EAAA,CAAAE,cAAA,cAA8D;IAAnCF,EAAA,CAAAe,UAAA,mBAAA+F,wDAAA1B,MAAA;MAAApF,EAAA,CAAAkB,aAAA,CAAA0F,IAAA;MAAA,OAAA5G,EAAA,CAAAuB,WAAA,CAAS6D,MAAA,CAAA2B,eAAA,EAAwB;IAAA,EAAC;IAEzD/G,EADF,CAAAE,cAAA,cAA0B,SACpB;IAAAF,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnCJ,EAAA,CAAAE,cAAA,iBAAwD;IAA9BF,EAAA,CAAAe,UAAA,mBAAAiG,2DAAA;MAAAhH,EAAA,CAAAkB,aAAA,CAAA0F,IAAA;MAAA,MAAAvF,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAwF,iBAAA,EAAmB;IAAA,EAAC;IACrD7G,EAAA,CAAAC,SAAA,YAA4B;IAEhCD,EADE,CAAAI,YAAA,EAAS,EACL;IAENJ,EAAA,CAAAE,cAAA,cAAwB;IACtBF,EAAA,CAAAC,SAAA,cAE2B;IAGzBD,EADF,CAAAE,cAAA,cAA0B,aACP;IAAAF,EAAA,CAAAG,MAAA,IAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE9CJ,EADF,CAAAE,cAAA,eAAmB,gBACW;IAAAF,EAAA,CAAAG,MAAA,IAA6C;;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChFJ,EAAA,CAAAU,UAAA,KAAAuG,0CAAA,mBAAmE;IAIvEjH,EADE,CAAAI,YAAA,EAAM,EACF;IAGJJ,EADF,CAAAE,cAAA,eAA2B,kBAC6B;IAA1BF,EAAA,CAAAe,UAAA,mBAAAmG,4DAAA;MAAAlH,EAAA,CAAAkB,aAAA,CAAA0F,IAAA;MAAA,MAAAvF,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAA8F,aAAA,EAAe;IAAA,EAAC;IAACnH,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACtEJ,EAAA,CAAAE,cAAA,kBAA2D;IAA7BF,EAAA,CAAAe,UAAA,mBAAAqG,4DAAA;MAAApH,EAAA,CAAAkB,aAAA,CAAA0F,IAAA;MAAA,MAAAvF,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAgG,gBAAA,EAAkB;IAAA,EAAC;IAACrH,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC/EJ,EAAA,CAAAE,cAAA,kBAA6D;IAAjCF,EAAA,CAAAe,UAAA,mBAAAuG,4DAAA;MAAAtH,EAAA,CAAAkB,aAAA,CAAA0F,IAAA;MAAA,MAAAvF,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAkG,oBAAA,EAAsB;IAAA,EAAC;IAACvH,EAAA,CAAAG,MAAA,uBAAe;IAIpFH,EAJoF,CAAAI,YAAA,EAAS,EACjF,EACF,EACF,EACF;;;;IA5BIJ,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAyD,iBAAA,CAAApC,MAAA,CAAAoF,eAAA,CAAAe,IAAA,CAA0B;IAOzBxH,EAAA,CAAAa,SAAA,GAAsC;IACtCb,EADA,CAAAK,UAAA,QAAAgB,MAAA,CAAAoF,eAAA,CAAAgB,MAAA,qBAAApG,MAAA,CAAAoF,eAAA,CAAAgB,MAAA,IAAAlH,GAAA,EAAAP,EAAA,CAAAQ,aAAA,CAAsC,QAAAa,MAAA,CAAAoF,eAAA,CAAAe,IAAA,CACV;IAIdxH,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAyD,iBAAA,CAAApC,MAAA,CAAAoF,eAAA,CAAAiB,KAAA,CAA2B;IAEd1H,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAAqC,kBAAA,WAAArC,EAAA,CAAAwG,WAAA,QAAAnF,MAAA,CAAAoF,eAAA,CAAAkB,KAAA,eAA6C;IAC3C3H,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAK,UAAA,SAAAgB,MAAA,CAAAoF,eAAA,CAAAC,aAAA,CAAmC;;;AA6hB/E,OAAM,MAAOkB,mBAAmB;EAQ9BC,YACUC,KAAqB,EACrBC,MAAc;IADd,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IAThB,KAAAhG,IAAI,GAAgB,IAAI;IACxB,KAAA0E,eAAe,GAAQ,IAAI;IAC3B,KAAAnB,UAAU,GAAG,EAAE;IACf,KAAAe,WAAW,GAAQ,IAAI;IACvB,KAAAT,SAAS,GAAG,KAAK;IACjB,KAAAoC,OAAO,GAAG,IAAI;EAKX;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACJ,KAAK,CAACK,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;MACnC,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;QAChB,IAAI,CAACE,QAAQ,CAACF,MAAM,CAAC,IAAI,CAAC,CAAC;;IAE/B,CAAC,CAAC;EACJ;EAEAD,eAAeA,CAAA;IACb;IACA,IAAI,CAAC7B,WAAW,GAAG;MACjBjD,GAAG,EAAE,cAAc;MACnBI,QAAQ,EAAE,KAAK;MACfD,QAAQ,EAAE,WAAW;MACrBD,MAAM,EAAE;KACT;EACH;EAEA+E,QAAQA,CAACC,MAAc;IACrB,IAAI,CAACN,OAAO,GAAG,IAAI;IAEnB;IACAO,KAAK,CAAC,mCAAmCD,MAAM,EAAE,CAAC,CAC/CE,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAG;MACX,IAAIA,IAAI,CAACC,OAAO,EAAE;QAChB,IAAI,CAAC7G,IAAI,GAAG;UACV,GAAG4G,IAAI,CAAC5G,IAAI;UACZiE,OAAO,EAAE,KAAK;UACdG,OAAO,EAAE,KAAK,CAAE;SACjB;QACD,IAAI,CAACP,SAAS,GAAG,IAAI,CAAC7D,IAAI,EAAEoB,IAAI,EAAEC,GAAG,KAAK,IAAI,CAACiD,WAAW,EAAEjD,GAAG;;MAEjE,IAAI,CAAC4E,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC,CACDa,KAAK,CAACC,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,IAAI,CAACd,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC;EACN;EAEAjE,MAAMA,CAAA;IACJ,IAAI,CAACgE,MAAM,CAACiB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAEA/E,QAAQA,CAAA;IACN;IACA8E,OAAO,CAACE,GAAG,CAAC,gBAAgB,CAAC;EAC/B;EAEA/F,WAAWA,CAACgG,MAAc;IACxB,IAAI,CAACnB,MAAM,CAACiB,QAAQ,CAAC,CAAC,UAAU,EAAEE,MAAM,CAAC,CAAC;EAC5C;EAEA1E,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACzC,IAAI,EAAE;IAEhB,IAAI,CAACA,IAAI,CAACiE,OAAO,GAAG,CAAC,IAAI,CAACjE,IAAI,CAACiE,OAAO;IAEtC,IAAI,IAAI,CAACjE,IAAI,CAACiE,OAAO,EAAE;MACrB,IAAI,CAACjE,IAAI,CAACkE,KAAK,CAACkD,IAAI,CAAC;QACnBhG,IAAI,EAAE,IAAI,CAACkD,WAAW,CAACjD,GAAG;QAC1BgG,OAAO,EAAE,IAAIC,IAAI;OAClB,CAAC;KACH,MAAM;MACL,IAAI,CAACtH,IAAI,CAACkE,KAAK,GAAG,IAAI,CAAClE,IAAI,CAACkE,KAAK,CAACqD,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACpG,IAAI,KAAK,IAAI,CAACkD,WAAW,CAACjD,GAAG,CAAC;;IAGtF;IACA2F,OAAO,CAACE,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAClH,IAAI,CAACqB,GAAG,EAAE,IAAI,CAACrB,IAAI,CAACiE,OAAO,CAAC;EACxE;EAEAlB,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAC/C,IAAI,EAAE;IAEhB,IAAI,CAACA,IAAI,CAACoE,OAAO,GAAG,CAAC,IAAI,CAACpE,IAAI,CAACoE,OAAO;IAEtC,IAAI,IAAI,CAACpE,IAAI,CAACoE,OAAO,EAAE;MACrB,IAAI,CAACpE,IAAI,CAACyH,KAAK,CAACL,IAAI,CAAC;QACnBhG,IAAI,EAAE,IAAI,CAACkD,WAAW,CAACjD,GAAG;QAC1BqG,OAAO,EAAE,IAAIJ,IAAI;OAClB,CAAC;KACH,MAAM;MACL,IAAI,CAACtH,IAAI,CAACyH,KAAK,GAAG,IAAI,CAACzH,IAAI,CAACyH,KAAK,CAACF,MAAM,CAACI,IAAI,IAAIA,IAAI,CAACvG,IAAI,KAAK,IAAI,CAACkD,WAAW,CAACjD,GAAG,CAAC;;IAGtF;IACA2F,OAAO,CAACE,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAClH,IAAI,CAACqB,GAAG,EAAE,IAAI,CAACrB,IAAI,CAACoE,OAAO,CAAC;EACxE;EAEAvB,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAAC7C,IAAI,EAAE;IAEhB;IACAgH,OAAO,CAACE,GAAG,CAAC,aAAa,EAAE,IAAI,CAAClH,IAAI,CAAC;IAErC,IAAI4H,SAAS,CAACC,KAAK,EAAE;MACnBD,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAE,GAAG,IAAI,CAAC9H,IAAI,CAACoB,IAAI,CAACK,QAAQ,SAAS;QAC1CI,IAAI,EAAE,IAAI,CAAC7B,IAAI,CAACqE,OAAO;QACvB7F,GAAG,EAAEuJ,MAAM,CAACC,QAAQ,CAACC;OACtB,CAAC;KACH,MAAM;MACL;MACAL,SAAS,CAACM,SAAS,CAACC,SAAS,CAACJ,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;MACnDG,KAAK,CAAC,2BAA2B,CAAC;;EAEtC;EAEAzF,iBAAiBA,CAAA;IACf,MAAM0F,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAqB;IACjF,IAAIF,YAAY,EAAE;MAChBA,YAAY,CAACG,KAAK,EAAE;;EAExB;EAEA/E,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACzD,IAAI,IAAI,CAAC,IAAI,CAACuD,UAAU,EAAEiB,IAAI,EAAE,EAAE;IAE5C,MAAMjB,UAAU,GAAG;MACjBlC,GAAG,EAAEiG,IAAI,CAACmB,GAAG,EAAE,CAACC,QAAQ,EAAE;MAC1BtH,IAAI,EAAE;QACJC,GAAG,EAAE,IAAI,CAACiD,WAAW,CAACjD,GAAG;QACzBI,QAAQ,EAAE,IAAI,CAAC6C,WAAW,CAAC7C,QAAQ;QACnCD,QAAQ,EAAE,IAAI,CAAC8C,WAAW,CAAC9C,QAAQ;QACnCD,MAAM,EAAE,IAAI,CAAC+C,WAAW,CAAC/C;OAC1B;MACDM,IAAI,EAAE,IAAI,CAAC0B,UAAU,CAACiB,IAAI,EAAE;MAC5B5C,WAAW,EAAE,IAAI0F,IAAI;KACtB;IAED,IAAI,CAACtH,IAAI,CAACmE,QAAQ,CAACiD,IAAI,CAAC7D,UAAU,CAAC;IACnC,IAAI,CAACA,UAAU,GAAG,EAAE;IAEpB;IACAyD,OAAO,CAACE,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAClH,IAAI,CAACqB,GAAG,EAAEkC,UAAU,CAAC;EAChE;EAEAlD,aAAaA,CAACsI,OAAe;IAC3B,IAAI,CAAC3C,MAAM,CAACiB,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;MAAE2B,WAAW,EAAE;QAAED;MAAO;IAAE,CAAE,CAAC;EACjE;EAEA;EACAhI,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACX,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,QAAQ,CAAC8D,MAAM,GAAG,CAAC,EAAE;MAC9C,MAAMrE,OAAO,GAAG,IAAI,CAACM,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACP,OAAO;MAC7C,IAAI,CAACsG,MAAM,CAACiB,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClC2B,WAAW,EAAE;UAAEC,SAAS,EAAEnJ,OAAO,CAAC2B,GAAG;UAAEyH,MAAM,EAAE;QAAM;OACtD,CAAC;;EAEN;EAEAjI,SAASA,CAAA;IACP,IAAI,IAAI,CAACb,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,QAAQ,CAAC8D,MAAM,GAAG,CAAC,EAAE;MAC9C,MAAMrE,OAAO,GAAG,IAAI,CAACM,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACP,OAAO;MAC7C;MACAsH,OAAO,CAACE,GAAG,CAAC,wBAAwB,EAAExH,OAAO,CAAC;MAC9C0I,KAAK,CAAC,GAAG1I,OAAO,CAAC+F,IAAI,iBAAiB,CAAC;;EAE3C;EAEA1E,aAAaA,CAAA;IACX,IAAI,IAAI,CAACf,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,QAAQ,CAAC8D,MAAM,GAAG,CAAC,EAAE;MAC9C,MAAMrE,OAAO,GAAG,IAAI,CAACM,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACP,OAAO;MAC7C;MACAsH,OAAO,CAACE,GAAG,CAAC,4BAA4B,EAAExH,OAAO,CAAC;MAClD0I,KAAK,CAAC,GAAG1I,OAAO,CAAC+F,IAAI,qBAAqB,CAAC;;EAE/C;EAEA;EACAhG,kBAAkBA,CAACC,OAAY;IAC7B,IAAI,CAACgF,eAAe,GAAGhF,OAAO;EAChC;EAEAoF,iBAAiBA,CAAA;IACf,IAAI,CAACJ,eAAe,GAAG,IAAI;EAC7B;EAEAU,aAAaA,CAAA;IACX,IAAI,IAAI,CAACV,eAAe,EAAE;MACxB,IAAI,CAACsB,MAAM,CAACiB,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClC2B,WAAW,EAAE;UAAEC,SAAS,EAAE,IAAI,CAACnE,eAAe,CAACrD,GAAG;UAAEyH,MAAM,EAAE;QAAM;OACnE,CAAC;;EAEN;EAEAxD,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACZ,eAAe,EAAE;MACxB;MACAsC,OAAO,CAACE,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACxC,eAAe,CAAC;MACzD0D,KAAK,CAAC,GAAG,IAAI,CAAC1D,eAAe,CAACe,IAAI,iBAAiB,CAAC;MACpD,IAAI,CAACX,iBAAiB,EAAE;;EAE5B;EAEAU,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACd,eAAe,EAAE;MACxB;MACAsC,OAAO,CAACE,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACxC,eAAe,CAAC;MAC7D0D,KAAK,CAAC,GAAG,IAAI,CAAC1D,eAAe,CAACe,IAAI,qBAAqB,CAAC;MACxD,IAAI,CAACX,iBAAiB,EAAE;;EAE5B;EAEAnD,UAAUA,CAACoH,IAAU;IACnB,MAAMN,GAAG,GAAG,IAAInB,IAAI,EAAE;IACtB,MAAM0B,MAAM,GAAGP,GAAG,CAACQ,OAAO,EAAE,GAAG,IAAI3B,IAAI,CAACyB,IAAI,CAAC,CAACE,OAAO,EAAE;IACvD,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IACpD,MAAMK,SAAS,GAAGF,IAAI,CAACC,KAAK,CAACJ,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACvD,MAAMM,QAAQ,GAAGH,IAAI,CAACC,KAAK,CAACJ,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE3D,IAAIE,WAAW,GAAG,CAAC,EAAE,OAAO,KAAK;IACjC,IAAIA,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAC9C,IAAIG,SAAS,GAAG,EAAE,EAAE,OAAO,GAAGA,SAAS,GAAG;IAC1C,IAAIC,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,GAAG;IACvC,OAAO,IAAIhC,IAAI,CAACyB,IAAI,CAAC,CAACQ,kBAAkB,EAAE;EAC5C;;;uBAvOW1D,mBAAmB,EAAA5H,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzL,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAE,MAAA;IAAA;EAAA;;;YAAnB9D,mBAAmB;MAAA+D,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA7L,EAAA,CAAA8L,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/iB5BpM,EAlKA,CAAAU,UAAA,IAAA4L,kCAAA,mBAAgD,IAAAC,kCAAA,kBAkKiC;;;UAlK7CvM,EAAA,CAAAK,UAAA,SAAAgM,GAAA,CAAAtK,IAAA,CAAU;UAkKlB/B,EAAA,CAAAa,SAAA,EAAqB;UAArBb,EAAA,CAAAK,UAAA,SAAAgM,GAAA,CAAA5F,eAAA,CAAqB;;;qBApKzC3G,YAAY,EAAA0M,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAE5M,WAAW,EAAA6M,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}