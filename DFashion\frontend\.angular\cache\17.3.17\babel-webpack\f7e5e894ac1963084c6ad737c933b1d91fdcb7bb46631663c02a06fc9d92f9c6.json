{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport jsPDF from 'jspdf';\nimport 'jspdf-autotable';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class BillService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n  }\n  generateBillPDF(billData) {\n    const doc = new jsPDF();\n    // Company Header\n    doc.setFontSize(24);\n    doc.setTextColor(102, 126, 234); // DFashion brand color\n    doc.text('DFashion', 20, 30);\n    doc.setFontSize(12);\n    doc.setTextColor(0, 0, 0);\n    doc.text('Fashion & Lifestyle Store', 20, 40);\n    doc.text('Email: <EMAIL>', 20, 50);\n    doc.text('Phone: +91 9876543210', 20, 60);\n    // Bill Title\n    doc.setFontSize(18);\n    doc.setTextColor(102, 126, 234);\n    doc.text('INVOICE', 150, 30);\n    // Bill Details\n    doc.setFontSize(10);\n    doc.setTextColor(0, 0, 0);\n    doc.text(`Bill No: ${billData.billNumber}`, 150, 45);\n    doc.text(`Order ID: ${billData.orderId}`, 150, 55);\n    doc.text(`Payment ID: ${billData.paymentId}`, 150, 65);\n    doc.text(`Date: ${new Date(billData.orderDate).toLocaleDateString()}`, 150, 75);\n    // Customer Details\n    doc.setFontSize(14);\n    doc.setTextColor(102, 126, 234);\n    doc.text('Bill To:', 20, 85);\n    doc.setFontSize(10);\n    doc.setTextColor(0, 0, 0);\n    doc.text(billData.customerName, 20, 95);\n    doc.text(billData.customerEmail, 20, 105);\n    doc.text(billData.customerPhone, 20, 115);\n    // Address (split into multiple lines if needed)\n    const addressLines = this.splitText(billData.customerAddress, 50);\n    let addressY = 125;\n    addressLines.forEach(line => {\n      doc.text(line, 20, addressY);\n      addressY += 10;\n    });\n    // Items Table\n    const tableStartY = Math.max(addressY + 10, 140);\n    const tableColumns = ['Item', 'Qty', 'Price', 'Total'];\n    const tableRows = billData.items.map(item => [item.name, item.quantity.toString(), `₹${item.price.toFixed(2)}`, `₹${item.total.toFixed(2)}`]);\n    doc.autoTable({\n      head: [tableColumns],\n      body: tableRows,\n      startY: tableStartY,\n      theme: 'grid',\n      headStyles: {\n        fillColor: [102, 126, 234],\n        textColor: [255, 255, 255],\n        fontSize: 10\n      },\n      bodyStyles: {\n        fontSize: 9\n      },\n      columnStyles: {\n        0: {\n          cellWidth: 80\n        },\n        1: {\n          cellWidth: 20,\n          halign: 'center'\n        },\n        2: {\n          cellWidth: 30,\n          halign: 'right'\n        },\n        3: {\n          cellWidth: 30,\n          halign: 'right'\n        }\n      }\n    });\n    // Calculate totals section Y position\n    const finalY = doc.lastAutoTable.finalY + 20;\n    // Totals Section\n    const totalsX = 130;\n    doc.setFontSize(10);\n    doc.text('Subtotal:', totalsX, finalY);\n    doc.text(`₹${billData.subtotal.toFixed(2)}`, totalsX + 40, finalY);\n    if (billData.discount > 0) {\n      doc.text('Discount:', totalsX, finalY + 10);\n      doc.text(`-₹${billData.discount.toFixed(2)}`, totalsX + 40, finalY + 10);\n    }\n    doc.text('Shipping:', totalsX, finalY + 20);\n    doc.text(`₹${billData.shipping.toFixed(2)}`, totalsX + 40, finalY + 20);\n    doc.text('Tax (GST):', totalsX, finalY + 30);\n    doc.text(`₹${billData.tax.toFixed(2)}`, totalsX + 40, finalY + 30);\n    // Total\n    doc.setFontSize(12);\n    doc.setTextColor(102, 126, 234);\n    doc.text('Total:', totalsX, finalY + 45);\n    doc.text(`₹${billData.total.toFixed(2)}`, totalsX + 40, finalY + 45);\n    // Payment Method\n    doc.setFontSize(10);\n    doc.setTextColor(0, 0, 0);\n    doc.text(`Payment Method: ${billData.paymentMethod}`, 20, finalY + 60);\n    // Footer\n    doc.setFontSize(8);\n    doc.setTextColor(128, 128, 128);\n    doc.text('Thank you for shopping with DFashion!', 20, finalY + 80);\n    doc.text('For any queries, contact <NAME_EMAIL>', 20, finalY + 90);\n    return doc;\n  }\n  splitText(text, maxLength) {\n    const words = text.split(' ');\n    const lines = [];\n    let currentLine = '';\n    words.forEach(word => {\n      if ((currentLine + word).length <= maxLength) {\n        currentLine += (currentLine ? ' ' : '') + word;\n      } else {\n        if (currentLine) lines.push(currentLine);\n        currentLine = word;\n      }\n    });\n    if (currentLine) lines.push(currentLine);\n    return lines;\n  }\n  downloadBill(billData) {\n    const doc = this.generateBillPDF(billData);\n    doc.save(`DFashion_Invoice_${billData.billNumber}.pdf`);\n  }\n  generateBillBlob(billData) {\n    const doc = this.generateBillPDF(billData);\n    return doc.output('blob');\n  }\n  sendBillEmail(billData, message) {\n    const formData = new FormData();\n    // Generate PDF blob\n    const pdfBlob = this.generateBillBlob(billData);\n    formData.append('billpdf', pdfBlob, `DFashion_Invoice_${billData.billNumber}.pdf`);\n    // Add other data\n    formData.append('userId', billData.orderId); // Using orderId as userId for now\n    formData.append('name', billData.customerName);\n    formData.append('email', billData.customerEmail);\n    formData.append('message', message || 'Thank you for your purchase! Please find your invoice attached.');\n    formData.append('paymentType', JSON.stringify({\n      method: billData.paymentMethod,\n      paymentId: billData.paymentId,\n      orderId: billData.orderId\n    }));\n    return this.http.post(`${this.apiUrl}/email/send-bill`, formData);\n  }\n  generateBillNumber() {\n    const date = new Date();\n    const year = date.getFullYear().toString().slice(-2);\n    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n    const day = date.getDate().toString().padStart(2, '0');\n    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n    return `DF${year}${month}${day}${random}`;\n  }\n  calculateTotals(items, shippingCost = 0, discountAmount = 0, taxRate = 0.18) {\n    const subtotal = items.reduce((sum, item) => sum + item.total, 0);\n    const discountedSubtotal = subtotal - discountAmount;\n    const tax = discountedSubtotal * taxRate;\n    const total = discountedSubtotal + tax + shippingCost;\n    return {\n      subtotal,\n      tax,\n      shipping: shippingCost,\n      discount: discountAmount,\n      total\n    };\n  }\n  static {\n    this.ɵfac = function BillService_Factory(t) {\n      return new (t || BillService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: BillService,\n      factory: BillService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "jsPDF", "BillService", "constructor", "http", "apiUrl", "generateBillPDF", "billData", "doc", "setFontSize", "setTextColor", "text", "bill<PERSON><PERSON><PERSON>", "orderId", "paymentId", "Date", "orderDate", "toLocaleDateString", "customerName", "customerEmail", "customerPhone", "addressLines", "splitText", "customerAddress", "addressY", "for<PERSON>ach", "line", "tableStartY", "Math", "max", "tableColumns", "tableRows", "items", "map", "item", "name", "quantity", "toString", "price", "toFixed", "total", "autoTable", "head", "body", "startY", "theme", "headStyles", "fillColor", "textColor", "fontSize", "bodyStyles", "columnStyles", "cellWidth", "halign", "finalY", "lastAutoTable", "totalsX", "subtotal", "discount", "shipping", "tax", "paymentMethod", "max<PERSON><PERSON><PERSON>", "words", "split", "lines", "currentLine", "word", "length", "push", "downloadBill", "save", "generateBillBlob", "output", "sendBillEmail", "message", "formData", "FormData", "pdfBlob", "append", "JSON", "stringify", "method", "post", "generateBillNumber", "date", "year", "getFullYear", "slice", "month", "getMonth", "padStart", "day", "getDate", "random", "floor", "calculateTotals", "shippingCost", "discountAmount", "taxRate", "reduce", "sum", "discountedSubtotal", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\core\\services\\bill.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport jsPDF from 'jspdf';\nimport 'jspdf-autotable';\n\ndeclare module 'jspdf' {\n  interface jsPDF {\n    autoTable: (options: any) => jsPDF;\n  }\n}\n\nexport interface BillData {\n  orderId: string;\n  paymentId: string;\n  customerName: string;\n  customerEmail: string;\n  customerPhone: string;\n  customerAddress: string;\n  items: BillItem[];\n  subtotal: number;\n  tax: number;\n  shipping: number;\n  discount: number;\n  total: number;\n  paymentMethod: string;\n  orderDate: Date;\n  billNumber: string;\n}\n\nexport interface BillItem {\n  name: string;\n  description: string;\n  quantity: number;\n  price: number;\n  total: number;\n  image?: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class BillService {\n  private apiUrl = environment.apiUrl;\n\n  constructor(private http: HttpClient) {}\n\n  generateBillPDF(billData: BillData): jsPDF {\n    const doc = new jsPDF();\n    \n    // Company Header\n    doc.setFontSize(24);\n    doc.setTextColor(102, 126, 234); // DFashion brand color\n    doc.text('DFashion', 20, 30);\n    \n    doc.setFontSize(12);\n    doc.setTextColor(0, 0, 0);\n    doc.text('Fashion & Lifestyle Store', 20, 40);\n    doc.text('Email: <EMAIL>', 20, 50);\n    doc.text('Phone: +91 9876543210', 20, 60);\n    \n    // Bill Title\n    doc.setFontSize(18);\n    doc.setTextColor(102, 126, 234);\n    doc.text('INVOICE', 150, 30);\n    \n    // Bill Details\n    doc.setFontSize(10);\n    doc.setTextColor(0, 0, 0);\n    doc.text(`Bill No: ${billData.billNumber}`, 150, 45);\n    doc.text(`Order ID: ${billData.orderId}`, 150, 55);\n    doc.text(`Payment ID: ${billData.paymentId}`, 150, 65);\n    doc.text(`Date: ${new Date(billData.orderDate).toLocaleDateString()}`, 150, 75);\n    \n    // Customer Details\n    doc.setFontSize(14);\n    doc.setTextColor(102, 126, 234);\n    doc.text('Bill To:', 20, 85);\n    \n    doc.setFontSize(10);\n    doc.setTextColor(0, 0, 0);\n    doc.text(billData.customerName, 20, 95);\n    doc.text(billData.customerEmail, 20, 105);\n    doc.text(billData.customerPhone, 20, 115);\n    \n    // Address (split into multiple lines if needed)\n    const addressLines = this.splitText(billData.customerAddress, 50);\n    let addressY = 125;\n    addressLines.forEach(line => {\n      doc.text(line, 20, addressY);\n      addressY += 10;\n    });\n    \n    // Items Table\n    const tableStartY = Math.max(addressY + 10, 140);\n    \n    const tableColumns = ['Item', 'Qty', 'Price', 'Total'];\n    const tableRows = billData.items.map(item => [\n      item.name,\n      item.quantity.toString(),\n      `₹${item.price.toFixed(2)}`,\n      `₹${item.total.toFixed(2)}`\n    ]);\n    \n    doc.autoTable({\n      head: [tableColumns],\n      body: tableRows,\n      startY: tableStartY,\n      theme: 'grid',\n      headStyles: {\n        fillColor: [102, 126, 234],\n        textColor: [255, 255, 255],\n        fontSize: 10\n      },\n      bodyStyles: {\n        fontSize: 9\n      },\n      columnStyles: {\n        0: { cellWidth: 80 },\n        1: { cellWidth: 20, halign: 'center' },\n        2: { cellWidth: 30, halign: 'right' },\n        3: { cellWidth: 30, halign: 'right' }\n      }\n    });\n    \n    // Calculate totals section Y position\n    const finalY = (doc as any).lastAutoTable.finalY + 20;\n    \n    // Totals Section\n    const totalsX = 130;\n    doc.setFontSize(10);\n    \n    doc.text('Subtotal:', totalsX, finalY);\n    doc.text(`₹${billData.subtotal.toFixed(2)}`, totalsX + 40, finalY);\n    \n    if (billData.discount > 0) {\n      doc.text('Discount:', totalsX, finalY + 10);\n      doc.text(`-₹${billData.discount.toFixed(2)}`, totalsX + 40, finalY + 10);\n    }\n    \n    doc.text('Shipping:', totalsX, finalY + 20);\n    doc.text(`₹${billData.shipping.toFixed(2)}`, totalsX + 40, finalY + 20);\n    \n    doc.text('Tax (GST):', totalsX, finalY + 30);\n    doc.text(`₹${billData.tax.toFixed(2)}`, totalsX + 40, finalY + 30);\n    \n    // Total\n    doc.setFontSize(12);\n    doc.setTextColor(102, 126, 234);\n    doc.text('Total:', totalsX, finalY + 45);\n    doc.text(`₹${billData.total.toFixed(2)}`, totalsX + 40, finalY + 45);\n    \n    // Payment Method\n    doc.setFontSize(10);\n    doc.setTextColor(0, 0, 0);\n    doc.text(`Payment Method: ${billData.paymentMethod}`, 20, finalY + 60);\n    \n    // Footer\n    doc.setFontSize(8);\n    doc.setTextColor(128, 128, 128);\n    doc.text('Thank you for shopping with DFashion!', 20, finalY + 80);\n    doc.text('For any queries, contact <NAME_EMAIL>', 20, finalY + 90);\n    \n    return doc;\n  }\n  \n  private splitText(text: string, maxLength: number): string[] {\n    const words = text.split(' ');\n    const lines: string[] = [];\n    let currentLine = '';\n    \n    words.forEach(word => {\n      if ((currentLine + word).length <= maxLength) {\n        currentLine += (currentLine ? ' ' : '') + word;\n      } else {\n        if (currentLine) lines.push(currentLine);\n        currentLine = word;\n      }\n    });\n    \n    if (currentLine) lines.push(currentLine);\n    return lines;\n  }\n\n  downloadBill(billData: BillData): void {\n    const doc = this.generateBillPDF(billData);\n    doc.save(`DFashion_Invoice_${billData.billNumber}.pdf`);\n  }\n\n  generateBillBlob(billData: BillData): Blob {\n    const doc = this.generateBillPDF(billData);\n    return doc.output('blob');\n  }\n\n  sendBillEmail(billData: BillData, message?: string): Observable<any> {\n    const formData = new FormData();\n    \n    // Generate PDF blob\n    const pdfBlob = this.generateBillBlob(billData);\n    formData.append('billpdf', pdfBlob, `DFashion_Invoice_${billData.billNumber}.pdf`);\n    \n    // Add other data\n    formData.append('userId', billData.orderId); // Using orderId as userId for now\n    formData.append('name', billData.customerName);\n    formData.append('email', billData.customerEmail);\n    formData.append('message', message || 'Thank you for your purchase! Please find your invoice attached.');\n    formData.append('paymentType', JSON.stringify({\n      method: billData.paymentMethod,\n      paymentId: billData.paymentId,\n      orderId: billData.orderId\n    }));\n    \n    return this.http.post(`${this.apiUrl}/email/send-bill`, formData);\n  }\n\n  generateBillNumber(): string {\n    const date = new Date();\n    const year = date.getFullYear().toString().slice(-2);\n    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n    const day = date.getDate().toString().padStart(2, '0');\n    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n    \n    return `DF${year}${month}${day}${random}`;\n  }\n\n  calculateTotals(items: BillItem[], shippingCost: number = 0, discountAmount: number = 0, taxRate: number = 0.18): any {\n    const subtotal = items.reduce((sum, item) => sum + item.total, 0);\n    const discountedSubtotal = subtotal - discountAmount;\n    const tax = discountedSubtotal * taxRate;\n    const total = discountedSubtotal + tax + shippingCost;\n    \n    return {\n      subtotal,\n      tax,\n      shipping: shippingCost,\n      discount: discountAmount,\n      total\n    };\n  }\n}\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,mCAAmC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,iBAAiB;;;AAsCxB,OAAM,MAAOC,WAAW;EAGtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAGL,WAAW,CAACK,MAAM;EAEI;EAEvCC,eAAeA,CAACC,QAAkB;IAChC,MAAMC,GAAG,GAAG,IAAIP,KAAK,EAAE;IAEvB;IACAO,GAAG,CAACC,WAAW,CAAC,EAAE,CAAC;IACnBD,GAAG,CAACE,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACjCF,GAAG,CAACG,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,CAAC;IAE5BH,GAAG,CAACC,WAAW,CAAC,EAAE,CAAC;IACnBD,GAAG,CAACE,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzBF,GAAG,CAACG,IAAI,CAAC,2BAA2B,EAAE,EAAE,EAAE,EAAE,CAAC;IAC7CH,GAAG,CAACG,IAAI,CAAC,6BAA6B,EAAE,EAAE,EAAE,EAAE,CAAC;IAC/CH,GAAG,CAACG,IAAI,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,CAAC;IAEzC;IACAH,GAAG,CAACC,WAAW,CAAC,EAAE,CAAC;IACnBD,GAAG,CAACE,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC/BF,GAAG,CAACG,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,EAAE,CAAC;IAE5B;IACAH,GAAG,CAACC,WAAW,CAAC,EAAE,CAAC;IACnBD,GAAG,CAACE,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzBF,GAAG,CAACG,IAAI,CAAC,YAAYJ,QAAQ,CAACK,UAAU,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;IACpDJ,GAAG,CAACG,IAAI,CAAC,aAAaJ,QAAQ,CAACM,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;IAClDL,GAAG,CAACG,IAAI,CAAC,eAAeJ,QAAQ,CAACO,SAAS,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;IACtDN,GAAG,CAACG,IAAI,CAAC,SAAS,IAAII,IAAI,CAACR,QAAQ,CAACS,SAAS,CAAC,CAACC,kBAAkB,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;IAE/E;IACAT,GAAG,CAACC,WAAW,CAAC,EAAE,CAAC;IACnBD,GAAG,CAACE,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC/BF,GAAG,CAACG,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,CAAC;IAE5BH,GAAG,CAACC,WAAW,CAAC,EAAE,CAAC;IACnBD,GAAG,CAACE,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzBF,GAAG,CAACG,IAAI,CAACJ,QAAQ,CAACW,YAAY,EAAE,EAAE,EAAE,EAAE,CAAC;IACvCV,GAAG,CAACG,IAAI,CAACJ,QAAQ,CAACY,aAAa,EAAE,EAAE,EAAE,GAAG,CAAC;IACzCX,GAAG,CAACG,IAAI,CAACJ,QAAQ,CAACa,aAAa,EAAE,EAAE,EAAE,GAAG,CAAC;IAEzC;IACA,MAAMC,YAAY,GAAG,IAAI,CAACC,SAAS,CAACf,QAAQ,CAACgB,eAAe,EAAE,EAAE,CAAC;IACjE,IAAIC,QAAQ,GAAG,GAAG;IAClBH,YAAY,CAACI,OAAO,CAACC,IAAI,IAAG;MAC1BlB,GAAG,CAACG,IAAI,CAACe,IAAI,EAAE,EAAE,EAAEF,QAAQ,CAAC;MAC5BA,QAAQ,IAAI,EAAE;IAChB,CAAC,CAAC;IAEF;IACA,MAAMG,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACL,QAAQ,GAAG,EAAE,EAAE,GAAG,CAAC;IAEhD,MAAMM,YAAY,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC;IACtD,MAAMC,SAAS,GAAGxB,QAAQ,CAACyB,KAAK,CAACC,GAAG,CAACC,IAAI,IAAI,CAC3CA,IAAI,CAACC,IAAI,EACTD,IAAI,CAACE,QAAQ,CAACC,QAAQ,EAAE,EACxB,IAAIH,IAAI,CAACI,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC3B,IAAIL,IAAI,CAACM,KAAK,CAACD,OAAO,CAAC,CAAC,CAAC,EAAE,CAC5B,CAAC;IAEF/B,GAAG,CAACiC,SAAS,CAAC;MACZC,IAAI,EAAE,CAACZ,YAAY,CAAC;MACpBa,IAAI,EAAEZ,SAAS;MACfa,MAAM,EAAEjB,WAAW;MACnBkB,KAAK,EAAE,MAAM;MACbC,UAAU,EAAE;QACVC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC1BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC1BC,QAAQ,EAAE;OACX;MACDC,UAAU,EAAE;QACVD,QAAQ,EAAE;OACX;MACDE,YAAY,EAAE;QACZ,CAAC,EAAE;UAAEC,SAAS,EAAE;QAAE,CAAE;QACpB,CAAC,EAAE;UAAEA,SAAS,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAQ,CAAE;QACtC,CAAC,EAAE;UAAED,SAAS,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAO,CAAE;QACrC,CAAC,EAAE;UAAED,SAAS,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAO;;KAEtC,CAAC;IAEF;IACA,MAAMC,MAAM,GAAI9C,GAAW,CAAC+C,aAAa,CAACD,MAAM,GAAG,EAAE;IAErD;IACA,MAAME,OAAO,GAAG,GAAG;IACnBhD,GAAG,CAACC,WAAW,CAAC,EAAE,CAAC;IAEnBD,GAAG,CAACG,IAAI,CAAC,WAAW,EAAE6C,OAAO,EAAEF,MAAM,CAAC;IACtC9C,GAAG,CAACG,IAAI,CAAC,IAAIJ,QAAQ,CAACkD,QAAQ,CAAClB,OAAO,CAAC,CAAC,CAAC,EAAE,EAAEiB,OAAO,GAAG,EAAE,EAAEF,MAAM,CAAC;IAElE,IAAI/C,QAAQ,CAACmD,QAAQ,GAAG,CAAC,EAAE;MACzBlD,GAAG,CAACG,IAAI,CAAC,WAAW,EAAE6C,OAAO,EAAEF,MAAM,GAAG,EAAE,CAAC;MAC3C9C,GAAG,CAACG,IAAI,CAAC,KAAKJ,QAAQ,CAACmD,QAAQ,CAACnB,OAAO,CAAC,CAAC,CAAC,EAAE,EAAEiB,OAAO,GAAG,EAAE,EAAEF,MAAM,GAAG,EAAE,CAAC;;IAG1E9C,GAAG,CAACG,IAAI,CAAC,WAAW,EAAE6C,OAAO,EAAEF,MAAM,GAAG,EAAE,CAAC;IAC3C9C,GAAG,CAACG,IAAI,CAAC,IAAIJ,QAAQ,CAACoD,QAAQ,CAACpB,OAAO,CAAC,CAAC,CAAC,EAAE,EAAEiB,OAAO,GAAG,EAAE,EAAEF,MAAM,GAAG,EAAE,CAAC;IAEvE9C,GAAG,CAACG,IAAI,CAAC,YAAY,EAAE6C,OAAO,EAAEF,MAAM,GAAG,EAAE,CAAC;IAC5C9C,GAAG,CAACG,IAAI,CAAC,IAAIJ,QAAQ,CAACqD,GAAG,CAACrB,OAAO,CAAC,CAAC,CAAC,EAAE,EAAEiB,OAAO,GAAG,EAAE,EAAEF,MAAM,GAAG,EAAE,CAAC;IAElE;IACA9C,GAAG,CAACC,WAAW,CAAC,EAAE,CAAC;IACnBD,GAAG,CAACE,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC/BF,GAAG,CAACG,IAAI,CAAC,QAAQ,EAAE6C,OAAO,EAAEF,MAAM,GAAG,EAAE,CAAC;IACxC9C,GAAG,CAACG,IAAI,CAAC,IAAIJ,QAAQ,CAACiC,KAAK,CAACD,OAAO,CAAC,CAAC,CAAC,EAAE,EAAEiB,OAAO,GAAG,EAAE,EAAEF,MAAM,GAAG,EAAE,CAAC;IAEpE;IACA9C,GAAG,CAACC,WAAW,CAAC,EAAE,CAAC;IACnBD,GAAG,CAACE,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzBF,GAAG,CAACG,IAAI,CAAC,mBAAmBJ,QAAQ,CAACsD,aAAa,EAAE,EAAE,EAAE,EAAEP,MAAM,GAAG,EAAE,CAAC;IAEtE;IACA9C,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC;IAClBD,GAAG,CAACE,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC/BF,GAAG,CAACG,IAAI,CAAC,uCAAuC,EAAE,EAAE,EAAE2C,MAAM,GAAG,EAAE,CAAC;IAClE9C,GAAG,CAACG,IAAI,CAAC,qDAAqD,EAAE,EAAE,EAAE2C,MAAM,GAAG,EAAE,CAAC;IAEhF,OAAO9C,GAAG;EACZ;EAEQc,SAASA,CAACX,IAAY,EAAEmD,SAAiB;IAC/C,MAAMC,KAAK,GAAGpD,IAAI,CAACqD,KAAK,CAAC,GAAG,CAAC;IAC7B,MAAMC,KAAK,GAAa,EAAE;IAC1B,IAAIC,WAAW,GAAG,EAAE;IAEpBH,KAAK,CAACtC,OAAO,CAAC0C,IAAI,IAAG;MACnB,IAAI,CAACD,WAAW,GAAGC,IAAI,EAAEC,MAAM,IAAIN,SAAS,EAAE;QAC5CI,WAAW,IAAI,CAACA,WAAW,GAAG,GAAG,GAAG,EAAE,IAAIC,IAAI;OAC/C,MAAM;QACL,IAAID,WAAW,EAAED,KAAK,CAACI,IAAI,CAACH,WAAW,CAAC;QACxCA,WAAW,GAAGC,IAAI;;IAEtB,CAAC,CAAC;IAEF,IAAID,WAAW,EAAED,KAAK,CAACI,IAAI,CAACH,WAAW,CAAC;IACxC,OAAOD,KAAK;EACd;EAEAK,YAAYA,CAAC/D,QAAkB;IAC7B,MAAMC,GAAG,GAAG,IAAI,CAACF,eAAe,CAACC,QAAQ,CAAC;IAC1CC,GAAG,CAAC+D,IAAI,CAAC,oBAAoBhE,QAAQ,CAACK,UAAU,MAAM,CAAC;EACzD;EAEA4D,gBAAgBA,CAACjE,QAAkB;IACjC,MAAMC,GAAG,GAAG,IAAI,CAACF,eAAe,CAACC,QAAQ,CAAC;IAC1C,OAAOC,GAAG,CAACiE,MAAM,CAAC,MAAM,CAAC;EAC3B;EAEAC,aAAaA,CAACnE,QAAkB,EAAEoE,OAAgB;IAChD,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAE/B;IACA,MAAMC,OAAO,GAAG,IAAI,CAACN,gBAAgB,CAACjE,QAAQ,CAAC;IAC/CqE,QAAQ,CAACG,MAAM,CAAC,SAAS,EAAED,OAAO,EAAE,oBAAoBvE,QAAQ,CAACK,UAAU,MAAM,CAAC;IAElF;IACAgE,QAAQ,CAACG,MAAM,CAAC,QAAQ,EAAExE,QAAQ,CAACM,OAAO,CAAC,CAAC,CAAC;IAC7C+D,QAAQ,CAACG,MAAM,CAAC,MAAM,EAAExE,QAAQ,CAACW,YAAY,CAAC;IAC9C0D,QAAQ,CAACG,MAAM,CAAC,OAAO,EAAExE,QAAQ,CAACY,aAAa,CAAC;IAChDyD,QAAQ,CAACG,MAAM,CAAC,SAAS,EAAEJ,OAAO,IAAI,iEAAiE,CAAC;IACxGC,QAAQ,CAACG,MAAM,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAAC;MAC5CC,MAAM,EAAE3E,QAAQ,CAACsD,aAAa;MAC9B/C,SAAS,EAAEP,QAAQ,CAACO,SAAS;MAC7BD,OAAO,EAAEN,QAAQ,CAACM;KACnB,CAAC,CAAC;IAEH,OAAO,IAAI,CAACT,IAAI,CAAC+E,IAAI,CAAC,GAAG,IAAI,CAAC9E,MAAM,kBAAkB,EAAEuE,QAAQ,CAAC;EACnE;EAEAQ,kBAAkBA,CAAA;IAChB,MAAMC,IAAI,GAAG,IAAItE,IAAI,EAAE;IACvB,MAAMuE,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE,CAAClD,QAAQ,EAAE,CAACmD,KAAK,CAAC,CAAC,CAAC,CAAC;IACpD,MAAMC,KAAK,GAAG,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,EAAErD,QAAQ,EAAE,CAACsD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC/D,MAAMC,GAAG,GAAGP,IAAI,CAACQ,OAAO,EAAE,CAACxD,QAAQ,EAAE,CAACsD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACtD,MAAMG,MAAM,GAAGlE,IAAI,CAACmE,KAAK,CAACnE,IAAI,CAACkE,MAAM,EAAE,GAAG,IAAI,CAAC,CAACzD,QAAQ,EAAE,CAACsD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAE3E,OAAO,KAAKL,IAAI,GAAGG,KAAK,GAAGG,GAAG,GAAGE,MAAM,EAAE;EAC3C;EAEAE,eAAeA,CAAChE,KAAiB,EAAEiE,YAAA,GAAuB,CAAC,EAAEC,cAAA,GAAyB,CAAC,EAAEC,OAAA,GAAkB,IAAI;IAC7G,MAAM1C,QAAQ,GAAGzB,KAAK,CAACoE,MAAM,CAAC,CAACC,GAAG,EAAEnE,IAAI,KAAKmE,GAAG,GAAGnE,IAAI,CAACM,KAAK,EAAE,CAAC,CAAC;IACjE,MAAM8D,kBAAkB,GAAG7C,QAAQ,GAAGyC,cAAc;IACpD,MAAMtC,GAAG,GAAG0C,kBAAkB,GAAGH,OAAO;IACxC,MAAM3D,KAAK,GAAG8D,kBAAkB,GAAG1C,GAAG,GAAGqC,YAAY;IAErD,OAAO;MACLxC,QAAQ;MACRG,GAAG;MACHD,QAAQ,EAAEsC,YAAY;MACtBvC,QAAQ,EAAEwC,cAAc;MACxB1D;KACD;EACH;;;uBApMWtC,WAAW,EAAAqG,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXxG,WAAW;MAAAyG,OAAA,EAAXzG,WAAW,CAAA0G,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}