{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6];\nfunction TopInfluencersComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function TopInfluencersComponent_button_9_Template_button_click_0_listener() {\n      const category_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCategoryChange(category_r2.value));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r2.selectedCategory === category_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.getCategoryIcon(category_r2.value));\n    i0.ɵɵstyleProp(\"color\", ctx_r2.getCategoryColor(category_r2.value));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r2.label, \" \");\n  }\n}\nfunction TopInfluencersComponent_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"div\", 18);\n    i0.ɵɵelementStart(2, \"div\", 19);\n    i0.ɵɵelement(3, \"div\", 20)(4, \"div\", 21)(5, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TopInfluencersComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15);\n    i0.ɵɵtemplate(2, TopInfluencersComponent_div_10_div_2_Template, 6, 0, \"div\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction TopInfluencersComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵelement(2, \"i\", 24);\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Unable to load top influencers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function TopInfluencersComponent_div_11_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.retry());\n    });\n    i0.ɵɵelement(8, \"i\", 26);\n    i0.ɵɵtext(9, \" Try Again \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.error);\n  }\n}\nfunction TopInfluencersComponent_div_12_div_1_div_1_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 3);\n  }\n}\nfunction TopInfluencersComponent_div_12_div_1_div_1_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 57);\n  }\n}\nfunction TopInfluencersComponent_div_12_div_1_div_1_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 58);\n  }\n}\nfunction TopInfluencersComponent_div_12_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"span\", 53);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TopInfluencersComponent_div_12_div_1_div_1_i_3_Template, 1, 0, \"i\", 54)(4, TopInfluencersComponent_div_12_div_1_div_1_i_4_Template, 1, 0, \"i\", 55)(5, TopInfluencersComponent_div_12_div_1_div_1_i_5_Template, 1, 0, \"i\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext().index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"#\", i_r7 + 1, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r7 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r7 === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r7 === 2);\n  }\n}\nfunction TopInfluencersComponent_div_12_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"i\", 60);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TopInfluencersComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function TopInfluencersComponent_div_12_div_1_Template_div_click_0_listener() {\n      const influencer_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.viewInfluencer(influencer_r6));\n    });\n    i0.ɵɵtemplate(1, TopInfluencersComponent_div_12_div_1_div_1_Template, 6, 4, \"div\", 30);\n    i0.ɵɵelementStart(2, \"div\", 31);\n    i0.ɵɵelement(3, \"img\", 32);\n    i0.ɵɵtemplate(4, TopInfluencersComponent_div_12_div_1_div_4_Template, 2, 0, \"div\", 33);\n    i0.ɵɵelementStart(5, \"div\", 34);\n    i0.ɵɵelement(6, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 35)(8, \"h3\", 36);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 37);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 38);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 39)(15, \"div\", 40)(16, \"span\", 41);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 42);\n    i0.ɵɵtext(19, \"Followers\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 40)(21, \"span\", 41);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 42);\n    i0.ɵɵtext(24, \"Posts\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 40)(26, \"span\", 41);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 42);\n    i0.ɵɵtext(29, \"Engagement\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"div\", 43)(31, \"div\", 44);\n    i0.ɵɵelement(32, \"i\", 45);\n    i0.ɵɵelementStart(33, \"span\");\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 44);\n    i0.ɵɵelement(36, \"i\", 46);\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 47)(40, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function TopInfluencersComponent_div_12_div_1_Template_button_click_40_listener($event) {\n      const influencer_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.followInfluencer(influencer_r6, $event));\n    });\n    i0.ɵɵelement(41, \"i\", 49);\n    i0.ɵɵtext(42, \" Follow \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function TopInfluencersComponent_div_12_div_1_Template_button_click_43_listener($event) {\n      const influencer_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.shareInfluencer(influencer_r6, $event));\n    });\n    i0.ɵɵelement(44, \"i\", 51);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const influencer_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"top-rank\", i_r7 < 3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r7 < 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", influencer_r6.avatar, i0.ɵɵsanitizeUrl)(\"alt\", influencer_r6.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", influencer_r6.isInfluencer);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.getCategoryColor(influencer_r6.influencerStats.category));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.getCategoryIcon(influencer_r6.influencerStats.category));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(influencer_r6.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(\"@\" + influencer_r6.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(influencer_r6.bio);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.formatNumber(influencer_r6.socialStats.followersCount));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.formatNumber(influencer_r6.socialStats.postsCount));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r6.influencerStats.engagementRate, \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.formatNumber(influencer_r6.influencerStats.averageLikes), \" avg likes\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.formatNumber(influencer_r6.influencerStats.averageViews), \" avg views\");\n  }\n}\nfunction TopInfluencersComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, TopInfluencersComponent_div_12_div_1_Template, 45, 18, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.influencers)(\"ngForTrackBy\", ctx_r2.trackByInfluencerId);\n  }\n}\nfunction TopInfluencersComponent_div_13_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Load More Influencers\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TopInfluencersComponent_div_13_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" Loading... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TopInfluencersComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function TopInfluencersComponent_div_13_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.loadMore());\n    });\n    i0.ɵɵtemplate(2, TopInfluencersComponent_div_13_span_2_Template, 2, 0, \"span\", 63)(3, TopInfluencersComponent_div_13_span_3_Template, 3, 0, \"span\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n  }\n}\nfunction TopInfluencersComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵelement(2, \"i\", 67);\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No influencers found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try selecting a different category or check back later!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function TopInfluencersComponent_div_14_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCategoryChange(\"\"));\n    });\n    i0.ɵɵtext(8, \" View All Categories \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport let TopInfluencersComponent = /*#__PURE__*/(() => {\n  class TopInfluencersComponent {\n    constructor(router, http) {\n      this.router = router;\n      this.http = http;\n      this.influencers = [];\n      this.isLoading = true;\n      this.error = null;\n      this.currentPage = 1;\n      this.totalPages = 1;\n      this.hasMore = false;\n      this.selectedCategory = '';\n      this.categories = [{\n        value: '',\n        label: 'All Categories'\n      }, {\n        value: 'fashion',\n        label: 'Fashion'\n      }, {\n        value: 'beauty',\n        label: 'Beauty'\n      }, {\n        value: 'lifestyle',\n        label: 'Lifestyle'\n      }, {\n        value: 'fitness',\n        label: 'Fitness'\n      }, {\n        value: 'travel',\n        label: 'Travel'\n      }];\n      this.subscriptions = [];\n    }\n    ngOnInit() {\n      this.loadTopInfluencers();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    loadTopInfluencers(page = 1, category = '') {\n      this.isLoading = true;\n      this.error = null;\n      const params = new URLSearchParams({\n        page: page.toString(),\n        limit: '12'\n      });\n      if (category) {\n        params.append('category', category);\n      }\n      this.subscriptions.push(this.http.get(`${environment.apiUrl}/users/influencers?${params.toString()}`).subscribe({\n        next: response => {\n          if (response.success) {\n            if (page === 1) {\n              this.influencers = response.influencers;\n            } else {\n              this.influencers = [...this.influencers, ...response.influencers];\n            }\n            this.currentPage = response.pagination.page;\n            this.totalPages = response.pagination.pages;\n            this.hasMore = this.currentPage < this.totalPages;\n          } else {\n            this.loadFallbackInfluencers();\n          }\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading top influencers:', error);\n          if (page === 1) {\n            this.loadFallbackInfluencers();\n          }\n          this.error = 'Failed to load top influencers';\n          this.isLoading = false;\n        }\n      }));\n    }\n    loadFallbackInfluencers() {\n      this.influencers = [{\n        _id: '1',\n        username: 'fashionista_maya',\n        fullName: 'Maya Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150',\n        bio: '✨ Fashion & Lifestyle Influencer | 📍 NYC | Shop my looks ⬇️',\n        isInfluencer: true,\n        socialStats: {\n          followersCount: 125000,\n          followingCount: 890,\n          postsCount: 342\n        },\n        influencerStats: {\n          category: 'fashion',\n          engagementRate: 8.5,\n          averageLikes: 10500,\n          averageViews: 45000,\n          verifiedAt: new Date()\n        }\n      }, {\n        _id: '2',\n        username: 'style_guru_alex',\n        fullName: 'Alex Chen',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n        bio: '👗 Style Curator | 🌟 Sustainable Fashion Advocate | DM for collabs',\n        isInfluencer: true,\n        socialStats: {\n          followersCount: 89000,\n          followingCount: 1200,\n          postsCount: 278\n        },\n        influencerStats: {\n          category: 'fashion',\n          engagementRate: 12.3,\n          averageLikes: 8900,\n          averageViews: 32000,\n          verifiedAt: new Date()\n        }\n      }, {\n        _id: '3',\n        username: 'beauty_by_sarah',\n        fullName: 'Sarah Johnson',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',\n        bio: '💄 Beauty & Fashion | 📱 Daily OOTD | 🛍️ Affordable luxury finds',\n        isInfluencer: true,\n        socialStats: {\n          followersCount: 156000,\n          followingCount: 567,\n          postsCount: 445\n        },\n        influencerStats: {\n          category: 'beauty',\n          engagementRate: 9.8,\n          averageLikes: 15300,\n          averageViews: 58000,\n          verifiedAt: new Date()\n        }\n      }];\n    }\n    onCategoryChange(category) {\n      this.selectedCategory = category;\n      this.loadTopInfluencers(1, category);\n    }\n    loadMore() {\n      if (this.hasMore && !this.isLoading) {\n        this.loadTopInfluencers(this.currentPage + 1, this.selectedCategory);\n      }\n    }\n    viewInfluencer(influencer) {\n      this.router.navigate(['/influencer', influencer.username]);\n    }\n    followInfluencer(influencer, event) {\n      event.stopPropagation();\n      // TODO: Implement follow functionality\n      console.log('Follow influencer:', influencer);\n    }\n    shareInfluencer(influencer, event) {\n      event.stopPropagation();\n      // TODO: Implement share functionality\n      console.log('Share influencer:', influencer);\n    }\n    formatNumber(num) {\n      if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + 'M';\n      } else if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'K';\n      }\n      return num.toString();\n    }\n    getCategoryIcon(category) {\n      const icons = {\n        'fashion': 'fas fa-tshirt',\n        'beauty': 'fas fa-palette',\n        'lifestyle': 'fas fa-heart',\n        'fitness': 'fas fa-dumbbell',\n        'travel': 'fas fa-plane',\n        'food': 'fas fa-utensils',\n        'tech': 'fas fa-laptop'\n      };\n      return icons[category] || 'fas fa-star';\n    }\n    getCategoryColor(category) {\n      const colors = {\n        'fashion': '#667eea',\n        'beauty': '#f093fb',\n        'lifestyle': '#f6ad55',\n        'fitness': '#48bb78',\n        'travel': '#38b2ac',\n        'food': '#ed8936',\n        'tech': '#4299e1'\n      };\n      return colors[category] || '#a0aec0';\n    }\n    retry() {\n      this.loadTopInfluencers(1, this.selectedCategory);\n    }\n    trackByInfluencerId(index, influencer) {\n      return influencer._id;\n    }\n    static {\n      this.ɵfac = function TopInfluencersComponent_Factory(t) {\n        return new (t || TopInfluencersComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TopInfluencersComponent,\n        selectors: [[\"app-top-influencers\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 15,\n        vars: 6,\n        consts: [[1, \"top-influencers-container\"], [1, \"section-header\"], [1, \"section-title\"], [1, \"fas\", \"fa-crown\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"category-filter\"], [\"class\", \"category-btn\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"influencers-grid\", 4, \"ngIf\"], [\"class\", \"load-more-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"category-btn\", 3, \"click\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"influencer-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"influencer-skeleton\"], [1, \"skeleton-avatar\"], [1, \"skeleton-content\"], [1, \"skeleton-line\"], [1, \"skeleton-line\", \"short\"], [1, \"error-container\"], [1, \"error-content\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"retry-btn\", 3, \"click\"], [1, \"fas\", \"fa-redo\"], [1, \"influencers-grid\"], [\"class\", \"influencer-card\", 3, \"top-rank\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"influencer-card\", 3, \"click\"], [\"class\", \"rank-badge\", 4, \"ngIf\"], [1, \"influencer-avatar-container\"], [\"loading\", \"lazy\", 1, \"influencer-avatar\", 3, \"src\", \"alt\"], [\"class\", \"verified-badge\", 4, \"ngIf\"], [1, \"category-badge\"], [1, \"influencer-info\"], [1, \"influencer-name\"], [1, \"influencer-username\"], [1, \"influencer-bio\"], [1, \"influencer-stats\"], [1, \"stat-item\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"performance-metrics\"], [1, \"metric-item\"], [1, \"fas\", \"fa-heart\"], [1, \"fas\", \"fa-eye\"], [1, \"action-buttons\"], [1, \"follow-btn\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"share-btn\", 3, \"click\"], [1, \"fas\", \"fa-share\"], [1, \"rank-badge\"], [1, \"rank-number\"], [\"class\", \"fas fa-crown\", 4, \"ngIf\"], [\"class\", \"fas fa-medal\", 4, \"ngIf\"], [\"class\", \"fas fa-award\", 4, \"ngIf\"], [1, \"fas\", \"fa-medal\"], [1, \"fas\", \"fa-award\"], [1, \"verified-badge\"], [1, \"fas\", \"fa-check-circle\"], [1, \"load-more-container\"], [1, \"load-more-btn\", 3, \"click\", \"disabled\"], [4, \"ngIf\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"empty-container\"], [1, \"empty-content\"], [1, \"fas\", \"fa-users\"], [1, \"browse-btn\", 3, \"click\"]],\n        template: function TopInfluencersComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n            i0.ɵɵelement(3, \"i\", 3);\n            i0.ɵɵtext(4, \" Top Fashion Influencers \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function TopInfluencersComponent_Template_button_click_5_listener() {\n              return ctx.router.navigate([\"/influencers\"]);\n            });\n            i0.ɵɵtext(6, \" View All \");\n            i0.ɵɵelement(7, \"i\", 5);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 6);\n            i0.ɵɵtemplate(9, TopInfluencersComponent_button_9_Template, 3, 7, \"button\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(10, TopInfluencersComponent_div_10_Template, 3, 2, \"div\", 8)(11, TopInfluencersComponent_div_11_Template, 10, 1, \"div\", 9)(12, TopInfluencersComponent_div_12_Template, 2, 2, \"div\", 10)(13, TopInfluencersComponent_div_13_Template, 4, 3, \"div\", 11)(14, TopInfluencersComponent_div_14_Template, 9, 0, \"div\", 12);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading && ctx.influencers.length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.error && ctx.influencers.length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.influencers.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.hasMore);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.influencers.length === 0 && !ctx.isLoading && !ctx.error);\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf],\n        styles: [\".top-influencers-container[_ngcontent-%COMP%]{padding:24px;background:linear-gradient(135deg,#667eea,#764ba2);border-radius:16px;margin:16px;box-shadow:0 8px 32px #667eea4d}@media (max-width: 768px){.top-influencers-container[_ngcontent-%COMP%]{margin:8px;padding:16px;border-radius:12px}}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;font-size:24px;font-weight:700;color:#fff;margin:0}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:gold;font-size:20px;animation:_ngcontent-%COMP%_sparkle 2s infinite}@media (max-width: 768px){.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:20px}}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{background:#fff3;border:1px solid rgba(255,255,255,.3);border-radius:20px;padding:8px 16px;color:#fff;font-weight:500;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:8px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;transform:translateY(-2px);box-shadow:0 4px 12px #fff3}@media (max-width: 768px){.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{padding:6px 12px;font-size:14px}}@keyframes _ngcontent-%COMP%_sparkle{0%,to{transform:scale(1) rotate(0)}50%{transform:scale(1.1) rotate(180deg)}}.category-filter[_ngcontent-%COMP%]{display:flex;gap:8px;margin-bottom:24px;flex-wrap:wrap}.category-filter[_ngcontent-%COMP%]   .category-btn[_ngcontent-%COMP%]{background:#ffffff1a;border:1px solid rgba(255,255,255,.2);border-radius:20px;padding:8px 16px;color:#fff;font-size:14px;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;gap:6px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.category-filter[_ngcontent-%COMP%]   .category-btn[_ngcontent-%COMP%]:hover{background:#fff3;transform:translateY(-2px)}.category-filter[_ngcontent-%COMP%]   .category-btn.active[_ngcontent-%COMP%]{background:#ffffffe6;color:#667eea;font-weight:600}.category-filter[_ngcontent-%COMP%]   .category-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}@media (max-width: 768px){.category-filter[_ngcontent-%COMP%]   .category-btn[_ngcontent-%COMP%]{padding:6px 12px;font-size:12px}}.influencers-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(320px,1fr));gap:20px}@media (max-width: 768px){.influencers-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:16px}}.influencer-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:20px;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 20px #00000014;position:relative;overflow:hidden}.influencer-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 12px 40px #00000026}.influencer-card.top-rank[_ngcontent-%COMP%]{border:2px solid #ffd700;box-shadow:0 8px 32px #ffd7004d}.influencer-card.top-rank[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:4px;background:linear-gradient(90deg,gold,#ffed4e,gold);animation:_ngcontent-%COMP%_shimmer 2s infinite}@media (max-width: 768px){.influencer-card[_ngcontent-%COMP%]{padding:16px;border-radius:12px}.influencer-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200% 0}to{background-position:200% 0}}.rank-badge[_ngcontent-%COMP%]{position:absolute;top:12px;right:12px;background:linear-gradient(135deg,gold,#ffed4e);color:#744210;padding:4px 8px;border-radius:12px;font-size:12px;font-weight:700;display:flex;align-items:center;gap:4px;box-shadow:0 2px 8px #ffd70066}.rank-badge[_ngcontent-%COMP%]   .rank-number[_ngcontent-%COMP%]{font-size:14px}.rank-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px;animation:_ngcontent-%COMP%_bounce 1.5s infinite}@keyframes _ngcontent-%COMP%_bounce{0%,to{transform:translateY(0)}50%{transform:translateY(-2px)}}.influencer-avatar-container[_ngcontent-%COMP%]{position:relative;display:flex;justify-content:center;margin-bottom:16px}.influencer-avatar-container[_ngcontent-%COMP%]   .influencer-avatar[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;object-fit:cover;border:4px solid #667eea;transition:transform .3s ease}.influencer-avatar-container[_ngcontent-%COMP%]:hover   .influencer-avatar[_ngcontent-%COMP%]{transform:scale(1.05)}.influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%]{position:absolute;bottom:0;right:calc(50% - 50px);background:#667eea;color:#fff;border-radius:50%;width:24px;height:24px;display:flex;align-items:center;justify-content:center;border:2px solid white;box-shadow:0 2px 8px #667eea66}.influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}.influencer-avatar-container[_ngcontent-%COMP%]   .category-badge[_ngcontent-%COMP%]{position:absolute;bottom:0;left:calc(50% - 50px);color:#fff;border-radius:50%;width:24px;height:24px;display:flex;align-items:center;justify-content:center;border:2px solid white;box-shadow:0 2px 8px #0003}.influencer-avatar-container[_ngcontent-%COMP%]   .category-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}.influencer-info[_ngcontent-%COMP%]{text-align:center}.influencer-info[_ngcontent-%COMP%]   .influencer-name[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#2d3748;margin:0 0 4px;line-height:1.2}.influencer-info[_ngcontent-%COMP%]   .influencer-username[_ngcontent-%COMP%]{font-size:14px;color:#667eea;font-weight:500;margin:0 0 12px}.influencer-info[_ngcontent-%COMP%]   .influencer-bio[_ngcontent-%COMP%]{font-size:13px;color:#718096;line-height:1.4;margin:0 0 16px;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}@media (max-width: 768px){.influencer-info[_ngcontent-%COMP%]   .influencer-name[_ngcontent-%COMP%]{font-size:16px}.influencer-info[_ngcontent-%COMP%]   .influencer-username[_ngcontent-%COMP%]{font-size:13px}.influencer-info[_ngcontent-%COMP%]   .influencer-bio[_ngcontent-%COMP%]{font-size:12px}}.influencer-stats[_ngcontent-%COMP%]{display:flex;justify-content:space-around;margin-bottom:16px;padding:12px 0;border-top:1px solid #e2e8f0;border-bottom:1px solid #e2e8f0}.influencer-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{text-align:center}.influencer-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%]{display:block;font-size:16px;font-weight:700;color:#2d3748;line-height:1}.influencer-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:11px;color:#718096;text-transform:uppercase;letter-spacing:.5px}@media (max-width: 768px){.influencer-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%]{font-size:14px}.influencer-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:10px}}.performance-metrics[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;margin-bottom:16px}.performance-metrics[_ngcontent-%COMP%]   .metric-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:12px;color:#718096}.performance-metrics[_ngcontent-%COMP%]   .metric-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#667eea;font-size:11px;width:12px}.action-buttons[_ngcontent-%COMP%]{display:flex;gap:8px}.action-buttons[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]{flex:1;background:linear-gradient(135deg,#667eea,#764ba2);border:none;border-radius:20px;padding:8px 16px;color:#fff;font-weight:500;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;justify-content:center;gap:6px}.action-buttons[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #667eea66}.action-buttons[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}.action-buttons[_ngcontent-%COMP%]   .share-btn[_ngcontent-%COMP%]{background:#667eea1a;border:1px solid rgba(102,126,234,.2);border-radius:50%;width:36px;height:36px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;color:#667eea}.action-buttons[_ngcontent-%COMP%]   .share-btn[_ngcontent-%COMP%]:hover{background:#667eea33;transform:scale(1.1)}.action-buttons[_ngcontent-%COMP%]   .share-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(320px,1fr));gap:20px}@media (max-width: 768px){.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:16px}}.loading-container[_ngcontent-%COMP%]   .influencer-skeleton[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:20px;animation:_ngcontent-%COMP%_pulse 1.5s ease-in-out infinite}.loading-container[_ngcontent-%COMP%]   .influencer-skeleton[_ngcontent-%COMP%]   .skeleton-avatar[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;margin:0 auto 16px}.loading-container[_ngcontent-%COMP%]   .influencer-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-line[_ngcontent-%COMP%]{height:12px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;border-radius:6px;margin-bottom:8px}.loading-container[_ngcontent-%COMP%]   .influencer-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-line.short[_ngcontent-%COMP%]{width:60%;margin:0 auto 8px}@keyframes _ngcontent-%COMP%_loading{0%{background-position:200% 0}to{background-position:-200% 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.8}}.error-container[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:300px}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]{text-align:center;max-width:400px}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:48px;color:#ffffffb3;margin-bottom:16px}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#fff;margin-bottom:8px}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#fffc;margin-bottom:24px}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%], .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%]{background:#fff3;border:1px solid rgba(255,255,255,.3);border-radius:20px;padding:12px 24px;color:#fff;font-weight:500;cursor:pointer;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%]:hover, .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .error-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%]:hover, .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .empty-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%]:hover, .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover, .empty-container[_ngcontent-%COMP%]   .empty-content[_ngcontent-%COMP%]   .browse-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;transform:translateY(-2px);box-shadow:0 4px 12px #fff3}.load-more-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:32px}.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]{background:#fff3;border:1px solid rgba(255,255,255,.3);border-radius:25px;padding:12px 32px;color:#fff;font-weight:500;cursor:pointer;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#ffffff4d;transform:translateY(-2px);box-shadow:0 4px 12px #fff3}.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]:disabled{opacity:.7;cursor:not-allowed}.load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px}\"]\n      });\n    }\n  }\n  return TopInfluencersComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}