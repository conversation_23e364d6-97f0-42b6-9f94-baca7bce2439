{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/analytics.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/select\";\nimport * as i8 from \"@angular/material/core\";\nfunction AnalyticsComponent_mat_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const period_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", period_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", period_r1.label, \" \");\n  }\n}\nfunction AnalyticsComponent_div_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 37)(4, \"div\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 39);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 40)(10, \"mat-icon\", 41);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r3 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", product_r2.sales, \" sales \\u2022 \\u20B9\", i0.ɵɵpipeBind1(8, 6, product_r2.revenue), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"color\", product_r2.trend > 0 ? \"primary\" : \"warn\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", product_r2.trend > 0 ? \"trending_up\" : \"trending_down\", \" \");\n  }\n}\nfunction AnalyticsComponent_div_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43)(2, \"div\", 44);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 45);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 46);\n    i0.ɵɵelement(7, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 48);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const source_r4 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(source_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", source_r4.percentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", source_r4.percentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(10, 5, source_r4.visitors), \" visitors\");\n  }\n}\nexport let AnalyticsComponent = /*#__PURE__*/(() => {\n  class AnalyticsComponent {\n    constructor(analyticsService) {\n      this.analyticsService = analyticsService;\n      this.destroy$ = new Subject();\n      this.selectedPeriod = '30d';\n      this.periods = [{\n        value: '7d',\n        label: 'Last 7 days'\n      }, {\n        value: '30d',\n        label: 'Last 30 days'\n      }, {\n        value: '90d',\n        label: 'Last 3 months'\n      }, {\n        value: '12m',\n        label: 'Last 12 months'\n      }];\n      // Mock data\n      this.totalRevenue = 125000;\n      this.revenueGrowth = 12.5;\n      this.totalOrders = 2340;\n      this.orderGrowth = 8.3;\n      this.totalCustomers = 1250;\n      this.customerGrowth = 15.2;\n      this.conversionRate = 3.2;\n      this.conversionChange = 0.5;\n      this.newCustomers = 185;\n      this.newCustomerGrowth = 22;\n      this.returningCustomers = 1065;\n      this.returningCustomerGrowth = 12;\n      this.averageOrderValue = 2850;\n      this.aovChange = 5.2;\n      this.topProducts = [{\n        name: 'Classic White Shirt',\n        sales: 45,\n        revenue: 112500,\n        trend: 1\n      }, {\n        name: 'Denim Jeans',\n        sales: 38,\n        revenue: 95000,\n        trend: 1\n      }, {\n        name: 'Summer Dress',\n        sales: 32,\n        revenue: 80000,\n        trend: -1\n      }, {\n        name: 'Casual Sneakers',\n        sales: 28,\n        revenue: 70000,\n        trend: 1\n      }, {\n        name: 'Leather Jacket',\n        sales: 22,\n        revenue: 55000,\n        trend: -1\n      }];\n      this.trafficSources = [{\n        name: 'Direct',\n        percentage: 35,\n        visitors: 4200\n      }, {\n        name: 'Google Search',\n        percentage: 28,\n        visitors: 3360\n      }, {\n        name: 'Social Media',\n        percentage: 18,\n        visitors: 2160\n      }, {\n        name: 'Email Marketing',\n        percentage: 12,\n        visitors: 1440\n      }, {\n        name: 'Referrals',\n        percentage: 7,\n        visitors: 840\n      }];\n    }\n    ngOnInit() {\n      this.loadAnalyticsData();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    onPeriodChange() {\n      this.loadAnalyticsData();\n    }\n    loadAnalyticsData() {\n      // In a real app, this would load data from the analytics service\n      console.log('Loading analytics data for period:', this.selectedPeriod);\n    }\n    exportReport(type) {\n      console.log('Exporting report:', type);\n      // Implement export functionality\n    }\n    static {\n      this.ɵfac = function AnalyticsComponent_Factory(t) {\n        return new (t || AnalyticsComponent)(i0.ɵɵdirectiveInject(i1.AnalyticsService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AnalyticsComponent,\n        selectors: [[\"app-analytics\"]],\n        decls: 150,\n        vars: 27,\n        consts: [[1, \"analytics-container\"], [1, \"analytics-header\"], [1, \"period-selector\"], [\"appearance\", \"outline\"], [3, \"valueChange\", \"selectionChange\", \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"metrics-grid\"], [1, \"metric-card\", \"sales\"], [1, \"metric-content\"], [1, \"metric-icon\"], [1, \"metric-details\"], [1, \"metric-change\", \"positive\"], [1, \"metric-card\", \"orders\"], [1, \"metric-card\", \"customers\"], [1, \"metric-card\", \"conversion\"], [1, \"metric-change\", \"neutral\"], [1, \"charts-grid\"], [1, \"chart-card\"], [1, \"chart-placeholder\"], [1, \"top-products-list\"], [\"class\", \"product-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"customer-insights\"], [1, \"insight-item\"], [1, \"insight-label\"], [1, \"insight-value\"], [1, \"insight-change\", \"positive\"], [1, \"insight-change\", \"neutral\"], [1, \"traffic-sources\"], [\"class\", \"source-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"export-section\"], [1, \"export-buttons\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"click\"], [\"mat-raised-button\", \"\", 3, \"click\"], [3, \"value\"], [1, \"product-item\"], [1, \"product-rank\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-stats\"], [1, \"product-trend\"], [3, \"color\"], [1, \"source-item\"], [1, \"source-info\"], [1, \"source-name\"], [1, \"source-percentage\"], [1, \"source-bar\"], [1, \"source-fill\"], [1, \"source-visitors\"]],\n        template: function AnalyticsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3, \"Analytics Dashboard\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 2)(5, \"mat-form-field\", 3)(6, \"mat-label\");\n            i0.ɵɵtext(7, \"Time Period\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"mat-select\", 4);\n            i0.ɵɵtwoWayListener(\"valueChange\", function AnalyticsComponent_Template_mat_select_valueChange_8_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.selectedPeriod, $event) || (ctx.selectedPeriod = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"selectionChange\", function AnalyticsComponent_Template_mat_select_selectionChange_8_listener() {\n              return ctx.onPeriodChange();\n            });\n            i0.ɵɵtemplate(9, AnalyticsComponent_mat_option_9_Template, 2, 2, \"mat-option\", 5);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(10, \"div\", 6)(11, \"mat-card\", 7)(12, \"mat-card-content\")(13, \"div\", 8)(14, \"div\", 9)(15, \"mat-icon\");\n            i0.ɵɵtext(16, \"trending_up\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 10)(18, \"h3\");\n            i0.ɵɵtext(19);\n            i0.ɵɵpipe(20, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"p\");\n            i0.ɵɵtext(22, \"Total Revenue\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"span\", 11);\n            i0.ɵɵtext(24);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(25, \"mat-card\", 12)(26, \"mat-card-content\")(27, \"div\", 8)(28, \"div\", 9)(29, \"mat-icon\");\n            i0.ɵɵtext(30, \"shopping_cart\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 10)(32, \"h3\");\n            i0.ɵɵtext(33);\n            i0.ɵɵpipe(34, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"p\");\n            i0.ɵɵtext(36, \"Total Orders\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"span\", 11);\n            i0.ɵɵtext(38);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(39, \"mat-card\", 13)(40, \"mat-card-content\")(41, \"div\", 8)(42, \"div\", 9)(43, \"mat-icon\");\n            i0.ɵɵtext(44, \"people\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(45, \"div\", 10)(46, \"h3\");\n            i0.ɵɵtext(47);\n            i0.ɵɵpipe(48, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"p\");\n            i0.ɵɵtext(50, \"Total Customers\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"span\", 11);\n            i0.ɵɵtext(52);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(53, \"mat-card\", 14)(54, \"mat-card-content\")(55, \"div\", 8)(56, \"div\", 9)(57, \"mat-icon\");\n            i0.ɵɵtext(58, \"analytics\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(59, \"div\", 10)(60, \"h3\");\n            i0.ɵɵtext(61);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(62, \"p\");\n            i0.ɵɵtext(63, \"Conversion Rate\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"span\", 15);\n            i0.ɵɵtext(65);\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(66, \"div\", 16)(67, \"mat-card\", 17)(68, \"mat-card-header\")(69, \"mat-card-title\");\n            i0.ɵɵtext(70, \"Sales Overview\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"mat-card-subtitle\");\n            i0.ɵɵtext(72, \"Revenue and orders over time\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(73, \"mat-card-content\")(74, \"div\", 18)(75, \"mat-icon\");\n            i0.ɵɵtext(76, \"show_chart\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"p\");\n            i0.ɵɵtext(78, \"Sales chart will be displayed here\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"small\");\n            i0.ɵɵtext(80, \"Chart.js integration pending\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(81, \"mat-card\", 17)(82, \"mat-card-header\")(83, \"mat-card-title\");\n            i0.ɵɵtext(84, \"Top Products\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(85, \"mat-card-subtitle\");\n            i0.ɵɵtext(86, \"Best performing products\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(87, \"mat-card-content\")(88, \"div\", 19);\n            i0.ɵɵtemplate(89, AnalyticsComponent_div_89_Template, 12, 8, \"div\", 20);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(90, \"mat-card\", 17)(91, \"mat-card-header\")(92, \"mat-card-title\");\n            i0.ɵɵtext(93, \"Customer Insights\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(94, \"mat-card-subtitle\");\n            i0.ɵɵtext(95, \"Customer behavior and demographics\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(96, \"mat-card-content\")(97, \"div\", 21)(98, \"div\", 22)(99, \"div\", 23);\n            i0.ɵɵtext(100, \"New Customers\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(101, \"div\", 24);\n            i0.ɵɵtext(102);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(103, \"div\", 25);\n            i0.ɵɵtext(104);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(105, \"div\", 22)(106, \"div\", 23);\n            i0.ɵɵtext(107, \"Returning Customers\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(108, \"div\", 24);\n            i0.ɵɵtext(109);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(110, \"div\", 25);\n            i0.ɵɵtext(111);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(112, \"div\", 22)(113, \"div\", 23);\n            i0.ɵɵtext(114, \"Average Order Value\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(115, \"div\", 24);\n            i0.ɵɵtext(116);\n            i0.ɵɵpipe(117, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(118, \"div\", 26);\n            i0.ɵɵtext(119);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(120, \"mat-card\", 17)(121, \"mat-card-header\")(122, \"mat-card-title\");\n            i0.ɵɵtext(123, \"Traffic Sources\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(124, \"mat-card-subtitle\");\n            i0.ɵɵtext(125, \"Where your customers come from\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(126, \"mat-card-content\")(127, \"div\", 27);\n            i0.ɵɵtemplate(128, AnalyticsComponent_div_128_Template, 11, 7, \"div\", 28);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(129, \"div\", 29)(130, \"mat-card\")(131, \"mat-card-header\")(132, \"mat-card-title\");\n            i0.ɵɵtext(133, \"Export Reports\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(134, \"mat-card-subtitle\");\n            i0.ɵɵtext(135, \"Download analytics data\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(136, \"mat-card-content\")(137, \"div\", 30)(138, \"button\", 31);\n            i0.ɵɵlistener(\"click\", function AnalyticsComponent_Template_button_click_138_listener() {\n              return ctx.exportReport(\"sales\");\n            });\n            i0.ɵɵelementStart(139, \"mat-icon\");\n            i0.ɵɵtext(140, \"file_download\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(141, \" Sales Report \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(142, \"button\", 32);\n            i0.ɵɵlistener(\"click\", function AnalyticsComponent_Template_button_click_142_listener() {\n              return ctx.exportReport(\"customers\");\n            });\n            i0.ɵɵelementStart(143, \"mat-icon\");\n            i0.ɵɵtext(144, \"file_download\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(145, \" Customer Report \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(146, \"button\", 33);\n            i0.ɵɵlistener(\"click\", function AnalyticsComponent_Template_button_click_146_listener() {\n              return ctx.exportReport(\"products\");\n            });\n            i0.ɵɵelementStart(147, \"mat-icon\");\n            i0.ɵɵtext(148, \"file_download\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(149, \" Product Report \");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵtwoWayProperty(\"value\", ctx.selectedPeriod);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.periods);\n            i0.ɵɵadvance(10);\n            i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(20, 18, ctx.totalRevenue, \"1.0-0\"), \"\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\"+\", ctx.revenueGrowth, \"%\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(34, 21, ctx.totalOrders));\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\"+\", ctx.orderGrowth, \"%\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(48, 23, ctx.totalCustomers));\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\"+\", ctx.customerGrowth, \"%\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\"\", ctx.conversionRate, \"%\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\"\", ctx.conversionChange, \"%\");\n            i0.ɵɵadvance(24);\n            i0.ɵɵproperty(\"ngForOf\", ctx.topProducts);\n            i0.ɵɵadvance(13);\n            i0.ɵɵtextInterpolate(ctx.newCustomers);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\"+\", ctx.newCustomerGrowth, \"%\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.returningCustomers);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\"+\", ctx.returningCustomerGrowth, \"%\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(117, 25, ctx.averageOrderValue), \"\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\"\", ctx.aovChange, \"%\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngForOf\", ctx.trafficSources);\n          }\n        },\n        dependencies: [i2.NgForOf, i3.MatIcon, i4.MatButton, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardSubtitle, i5.MatCardTitle, i6.MatFormField, i6.MatLabel, i7.MatSelect, i8.MatOption, i2.DecimalPipe],\n        styles: [\".analytics-container[_ngcontent-%COMP%]{padding:1rem;max-width:1400px;margin:0 auto}.analytics-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:2rem}.analytics-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;color:#333;font-weight:500}.analytics-header[_ngcontent-%COMP%]   .period-selector[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:200px}.metrics-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:1rem;margin-bottom:2rem}.metric-card[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem}.metric-card[_ngcontent-%COMP%]   .metric-icon[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:12px;display:flex;align-items:center;justify-content:center}.metric-card[_ngcontent-%COMP%]   .metric-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:2rem;width:2rem;height:2rem;color:#fff}.metric-card[_ngcontent-%COMP%]   .metric-details[_ngcontent-%COMP%]{flex:1}.metric-card[_ngcontent-%COMP%]   .metric-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 .25rem;font-size:1.75rem;font-weight:600;color:#333}.metric-card[_ngcontent-%COMP%]   .metric-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#666;font-size:.875rem;text-transform:uppercase;letter-spacing:.5px}.metric-card[_ngcontent-%COMP%]   .metric-details[_ngcontent-%COMP%]   .metric-change[_ngcontent-%COMP%]{font-size:.75rem;font-weight:500}.metric-card[_ngcontent-%COMP%]   .metric-details[_ngcontent-%COMP%]   .metric-change.positive[_ngcontent-%COMP%]{color:#4caf50}.metric-card[_ngcontent-%COMP%]   .metric-details[_ngcontent-%COMP%]   .metric-change.negative[_ngcontent-%COMP%]{color:#f44336}.metric-card[_ngcontent-%COMP%]   .metric-details[_ngcontent-%COMP%]   .metric-change.neutral[_ngcontent-%COMP%]{color:#666}.metric-card.sales[_ngcontent-%COMP%]   .metric-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2)}.metric-card.orders[_ngcontent-%COMP%]   .metric-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4facfe,#00f2fe)}.metric-card.customers[_ngcontent-%COMP%]   .metric-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#43e97b,#38f9d7)}.metric-card.conversion[_ngcontent-%COMP%]   .metric-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fa709a,#fee140)}.charts-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(400px,1fr));gap:1rem;margin-bottom:2rem}.chart-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]{padding-bottom:1rem}.chart-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{font-size:1.125rem;font-weight:500}.chart-card[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]{font-size:.875rem;color:#666}.chart-placeholder[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:300px;background:#f5f5f5;border-radius:8px;color:#999}.chart-placeholder[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:4rem;width:4rem;height:4rem;margin-bottom:1rem}.chart-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:1rem}.chart-placeholder[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:.75rem}.top-products-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:1rem 0;border-bottom:1px solid #f0f0f0}.top-products-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.top-products-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-rank[_ngcontent-%COMP%]{width:30px;height:30px;border-radius:50%;background:#e3f2fd;color:#1976d2;display:flex;align-items:center;justify-content:center;font-weight:600;font-size:.875rem;margin-right:1rem}.top-products-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]{flex:1}.top-products-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{font-weight:500;margin-bottom:.25rem}.top-products-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-stats[_ngcontent-%COMP%]{font-size:.875rem;color:#666}.top-products-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-trend[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.25rem}.customer-insights[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(150px,1fr));gap:1rem}.customer-insights[_ngcontent-%COMP%]   .insight-item[_ngcontent-%COMP%]{text-align:center;padding:1rem;background:#f8f9fa;border-radius:8px}.customer-insights[_ngcontent-%COMP%]   .insight-item[_ngcontent-%COMP%]   .insight-label[_ngcontent-%COMP%]{font-size:.75rem;color:#666;text-transform:uppercase;letter-spacing:.5px;margin-bottom:.5rem}.customer-insights[_ngcontent-%COMP%]   .insight-item[_ngcontent-%COMP%]   .insight-value[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;color:#333;margin-bottom:.25rem}.customer-insights[_ngcontent-%COMP%]   .insight-item[_ngcontent-%COMP%]   .insight-change[_ngcontent-%COMP%]{font-size:.75rem;font-weight:500}.customer-insights[_ngcontent-%COMP%]   .insight-item[_ngcontent-%COMP%]   .insight-change.positive[_ngcontent-%COMP%]{color:#4caf50}.customer-insights[_ngcontent-%COMP%]   .insight-item[_ngcontent-%COMP%]   .insight-change.negative[_ngcontent-%COMP%]{color:#f44336}.customer-insights[_ngcontent-%COMP%]   .insight-item[_ngcontent-%COMP%]   .insight-change.neutral[_ngcontent-%COMP%]{color:#666}.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;padding:.75rem 0;border-bottom:1px solid #f0f0f0}.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%]   .source-info[_ngcontent-%COMP%]{min-width:120px}.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%]   .source-info[_ngcontent-%COMP%]   .source-name[_ngcontent-%COMP%]{font-weight:500;margin-bottom:.25rem}.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%]   .source-info[_ngcontent-%COMP%]   .source-percentage[_ngcontent-%COMP%]{font-size:.875rem;color:#666}.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%]   .source-bar[_ngcontent-%COMP%]{flex:1;height:8px;background:#e0e0e0;border-radius:4px;overflow:hidden}.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%]   .source-bar[_ngcontent-%COMP%]   .source-fill[_ngcontent-%COMP%]{height:100%;background:#1976d2;transition:width .3s ease}.traffic-sources[_ngcontent-%COMP%]   .source-item[_ngcontent-%COMP%]   .source-visitors[_ngcontent-%COMP%]{min-width:100px;text-align:right;font-size:.875rem;color:#666}.export-section[_ngcontent-%COMP%]   .export-buttons[_ngcontent-%COMP%]{display:flex;gap:1rem;flex-wrap:wrap}.export-section[_ngcontent-%COMP%]   .export-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}@media (max-width: 768px){.analytics-container[_ngcontent-%COMP%]{padding:.5rem}.analytics-header[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch;gap:1rem}.analytics-header[_ngcontent-%COMP%]   .period-selector[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%}.metrics-grid[_ngcontent-%COMP%], .charts-grid[_ngcontent-%COMP%], .customer-insights[_ngcontent-%COMP%]{grid-template-columns:1fr}.export-buttons[_ngcontent-%COMP%]{flex-direction:column}.export-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;justify-content:center}}\"]\n      });\n    }\n  }\n  return AnalyticsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}