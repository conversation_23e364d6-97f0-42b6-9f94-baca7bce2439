{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule, Validators } from '@angular/forms';\n// import { ToastrService } from 'ngx-toastr';\nlet CheckoutComponent = class CheckoutComponent {\n  constructor(fb, cartService,\n  // private checkoutService: CheckoutService,\n  // private paymentService: PaymentService,\n  authService, router) {\n    this.fb = fb;\n    this.cartService = cartService;\n    this.authService = authService;\n    this.router = router;\n    this.currentStep = 1;\n    this.cartItems = [];\n    this.cartSummary = null;\n    this.processing = false;\n    this.orderPlaced = false;\n    this.orderNumber = '';\n    this.selectedPaymentMethod = '';\n    this.paymentMethods = [{\n      id: 'card',\n      name: 'Credit/Debit Card',\n      description: 'Pay securely with your credit or debit card',\n      icon: 'fas fa-credit-card'\n    }, {\n      id: 'upi',\n      name: 'UPI',\n      description: 'Pay using UPI apps like GPay, PhonePe, Paytm',\n      icon: 'fas fa-mobile-alt'\n    }, {\n      id: 'netbanking',\n      name: 'Net Banking',\n      description: 'Pay directly from your bank account',\n      icon: 'fas fa-university'\n    }, {\n      id: 'wallet',\n      name: 'Digital Wallet',\n      description: 'Pay using digital wallets',\n      icon: 'fas fa-wallet'\n    }, {\n      id: 'cod',\n      name: 'Cash on Delivery',\n      description: 'Pay when your order is delivered',\n      icon: 'fas fa-money-bill-wave'\n    }];\n    this.shippingForm = this.fb.group({\n      fullName: ['', Validators.required],\n      phone: ['', [Validators.required, Validators.pattern(/^[6-9]\\d{9}$/)]],\n      addressLine1: ['', Validators.required],\n      addressLine2: [''],\n      city: ['', Validators.required],\n      state: ['', Validators.required],\n      pincode: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n  }\n  ngOnInit() {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login'], {\n        queryParams: {\n          returnUrl: '/checkout'\n        }\n      });\n      return;\n    }\n    this.loadCartData();\n  }\n  loadCartData() {\n    // Simulate cart data loading\n    this.cartItems = [{\n      _id: '1',\n      product: {\n        _id: 'prod1',\n        name: 'Sample Product',\n        price: 999,\n        originalPrice: 1299,\n        images: [{\n          url: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',\n          isPrimary: true\n        }],\n        brand: 'Sample Brand',\n        discount: 23\n      },\n      quantity: 1,\n      size: 'M',\n      color: 'Blue',\n      addedAt: new Date()\n    }];\n    this.cartSummary = {\n      itemCount: 1,\n      totalQuantity: 1,\n      subtotal: 999,\n      discount: 300,\n      total: 878.82\n    };\n  }\n  canProceed() {\n    switch (this.currentStep) {\n      case 1:\n        return this.cartItems.length > 0;\n      case 2:\n        return this.shippingForm.valid;\n      case 3:\n        return !!this.selectedPaymentMethod;\n      default:\n        return false;\n    }\n  }\n  nextStep() {\n    if (!this.canProceed()) return;\n    if (this.currentStep === 3) {\n      this.placeOrder();\n    } else {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  selectPaymentMethod(methodId) {\n    this.selectedPaymentMethod = methodId;\n  }\n  getPaymentMethodName(methodId) {\n    const method = this.paymentMethods.find(m => m.id === methodId);\n    return method ? method.name : '';\n  }\n  placeOrder() {\n    this.processing = true;\n    const orderData = {\n      shippingAddress: this.shippingForm.value,\n      billingAddress: this.shippingForm.value,\n      paymentMethod: this.selectedPaymentMethod\n    };\n    // Simulate order placement for now\n    setTimeout(() => {\n      this.orderNumber = `ORD${Date.now()}`;\n      this.currentStep = 4;\n      this.orderPlaced = true;\n      this.processing = false;\n      alert('Order placed successfully!');\n    }, 2000);\n    // TODO: Implement actual checkout service\n    // this.checkoutService.placeOrder(orderData).subscribe({\n    //   next: (response) => {\n    //     if (response.success) {\n    //       this.orderNumber = response.data.orderNumber;\n    //       this.currentStep = 4;\n    //       this.orderPlaced = true;\n    //       this.toastr.success('Order placed successfully!');\n    //     }\n    //     this.processing = false;\n    //   },\n    //   error: (error) => {\n    //     console.error('Error placing order:', error);\n    //     this.toastr.error('Failed to place order. Please try again.');\n    //     this.processing = false;\n    //   }\n    // });\n  }\n  goToShopping() {\n    this.router.navigate(['/products']);\n  }\n  viewOrder() {\n    this.router.navigate(['/account/orders']);\n  }\n  continueShopping() {\n    this.router.navigate(['/']);\n  }\n};\nCheckoutComponent = __decorate([Component({\n  selector: 'app-checkout',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, FormsModule],\n  template: `\n    <div class=\"checkout-container\">\n      <div class=\"checkout-header\">\n        <h1>Checkout</h1>\n        <div class=\"step-indicator\">\n          <div class=\"step\" [class.active]=\"currentStep >= 1\" [class.completed]=\"currentStep > 1\">\n            <span class=\"step-number\">1</span>\n            <span class=\"step-label\">Cart Review</span>\n          </div>\n          <div class=\"step\" [class.active]=\"currentStep >= 2\" [class.completed]=\"currentStep > 2\">\n            <span class=\"step-number\">2</span>\n            <span class=\"step-label\">Shipping</span>\n          </div>\n          <div class=\"step\" [class.active]=\"currentStep >= 3\" [class.completed]=\"currentStep > 3\">\n            <span class=\"step-number\">3</span>\n            <span class=\"step-label\">Payment</span>\n          </div>\n          <div class=\"step\" [class.active]=\"currentStep >= 4\">\n            <span class=\"step-number\">4</span>\n            <span class=\"step-label\">Confirmation</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"checkout-content\">\n        <!-- Step 1: Cart Review -->\n        <div class=\"checkout-step\" *ngIf=\"currentStep === 1\">\n          <h2>Review Your Order</h2>\n          <div class=\"cart-items\" *ngIf=\"cartItems.length > 0\">\n            <div class=\"cart-item\" *ngFor=\"let item of cartItems\">\n              <img [src]=\"item.product.images[0]?.url || item.product.images[0]\" [alt]=\"item.product.name\" class=\"item-image\">\n              <div class=\"item-details\">\n                <h3>{{ item.product.name }}</h3>\n                <p class=\"item-brand\">{{ item.product.brand }}</p>\n                <div class=\"item-variants\" *ngIf=\"item.size || item.color\">\n                  <span *ngIf=\"item.size\">Size: {{ item.size }}</span>\n                  <span *ngIf=\"item.color\">Color: {{ item.color }}</span>\n                </div>\n                <div class=\"item-price\">\n                  <span class=\"current-price\">₹{{ item.product.price | number:'1.0-0' }}</span>\n                  <span class=\"original-price\" *ngIf=\"item.product.originalPrice\">\n                    ₹{{ item.product.originalPrice | number:'1.0-0' }}\n                  </span>\n                </div>\n              </div>\n              <div class=\"item-quantity\">\n                <span>Qty: {{ item.quantity }}</span>\n                <span class=\"item-total\">₹{{ (item.product.price * item.quantity) | number:'1.0-0' }}</span>\n              </div>\n            </div>\n          </div>\n          <div class=\"empty-cart\" *ngIf=\"cartItems.length === 0\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            <h3>Your cart is empty</h3>\n            <p>Add some items to your cart to continue</p>\n            <button class=\"btn btn-primary\" (click)=\"goToShopping()\">Continue Shopping</button>\n          </div>\n        </div>\n\n        <!-- Step 2: Shipping Information -->\n        <div class=\"checkout-step\" *ngIf=\"currentStep === 2\">\n          <h2>Shipping Information</h2>\n          <form [formGroup]=\"shippingForm\" class=\"shipping-form\">\n            <div class=\"form-row\">\n              <div class=\"form-group\">\n                <label for=\"fullName\">Full Name *</label>\n                <input type=\"text\" id=\"fullName\" formControlName=\"fullName\" class=\"form-control\">\n                <div class=\"error-message\" *ngIf=\"shippingForm.get('fullName')?.invalid && shippingForm.get('fullName')?.touched\">\n                  Full name is required\n                </div>\n              </div>\n              <div class=\"form-group\">\n                <label for=\"phone\">Phone Number *</label>\n                <input type=\"tel\" id=\"phone\" formControlName=\"phone\" class=\"form-control\">\n                <div class=\"error-message\" *ngIf=\"shippingForm.get('phone')?.invalid && shippingForm.get('phone')?.touched\">\n                  Valid phone number is required\n                </div>\n              </div>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"addressLine1\">Address Line 1 *</label>\n              <input type=\"text\" id=\"addressLine1\" formControlName=\"addressLine1\" class=\"form-control\">\n              <div class=\"error-message\" *ngIf=\"shippingForm.get('addressLine1')?.invalid && shippingForm.get('addressLine1')?.touched\">\n                Address is required\n              </div>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"addressLine2\">Address Line 2</label>\n              <input type=\"text\" id=\"addressLine2\" formControlName=\"addressLine2\" class=\"form-control\">\n            </div>\n\n            <div class=\"form-row\">\n              <div class=\"form-group\">\n                <label for=\"city\">City *</label>\n                <input type=\"text\" id=\"city\" formControlName=\"city\" class=\"form-control\">\n                <div class=\"error-message\" *ngIf=\"shippingForm.get('city')?.invalid && shippingForm.get('city')?.touched\">\n                  City is required\n                </div>\n              </div>\n              <div class=\"form-group\">\n                <label for=\"state\">State *</label>\n                <select id=\"state\" formControlName=\"state\" class=\"form-control\">\n                  <option value=\"\">Select State</option>\n                  <option value=\"Maharashtra\">Maharashtra</option>\n                  <option value=\"Delhi\">Delhi</option>\n                  <option value=\"Karnataka\">Karnataka</option>\n                  <option value=\"Tamil Nadu\">Tamil Nadu</option>\n                  <option value=\"Gujarat\">Gujarat</option>\n                  <option value=\"Rajasthan\">Rajasthan</option>\n                  <option value=\"West Bengal\">West Bengal</option>\n                  <option value=\"Uttar Pradesh\">Uttar Pradesh</option>\n                </select>\n                <div class=\"error-message\" *ngIf=\"shippingForm.get('state')?.invalid && shippingForm.get('state')?.touched\">\n                  State is required\n                </div>\n              </div>\n              <div class=\"form-group\">\n                <label for=\"pincode\">Pincode *</label>\n                <input type=\"text\" id=\"pincode\" formControlName=\"pincode\" class=\"form-control\" maxlength=\"6\">\n                <div class=\"error-message\" *ngIf=\"shippingForm.get('pincode')?.invalid && shippingForm.get('pincode')?.touched\">\n                  Valid 6-digit pincode is required\n                </div>\n              </div>\n            </div>\n          </form>\n        </div>\n\n        <!-- Step 3: Payment Method -->\n        <div class=\"checkout-step\" *ngIf=\"currentStep === 3\">\n          <h2>Payment Method</h2>\n          <div class=\"payment-methods\">\n            <div class=\"payment-option\" \n                 *ngFor=\"let method of paymentMethods\" \n                 [class.selected]=\"selectedPaymentMethod === method.id\"\n                 (click)=\"selectPaymentMethod(method.id)\">\n              <i [class]=\"method.icon\"></i>\n              <div class=\"method-details\">\n                <h3>{{ method.name }}</h3>\n                <p>{{ method.description }}</p>\n              </div>\n              <div class=\"method-radio\">\n                <input type=\"radio\" [value]=\"method.id\" [(ngModel)]=\"selectedPaymentMethod\">\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Step 4: Order Confirmation -->\n        <div class=\"checkout-step\" *ngIf=\"currentStep === 4\">\n          <div class=\"order-confirmation\" *ngIf=\"!orderPlaced\">\n            <h2>Order Confirmation</h2>\n            <div class=\"confirmation-details\">\n              <div class=\"shipping-summary\">\n                <h3>Shipping Address</h3>\n                <div class=\"address-display\">\n                  <p><strong>{{ shippingForm.get('fullName')?.value }}</strong></p>\n                  <p>{{ shippingForm.get('phone')?.value }}</p>\n                  <p>{{ shippingForm.get('addressLine1')?.value }}</p>\n                  <p *ngIf=\"shippingForm.get('addressLine2')?.value\">{{ shippingForm.get('addressLine2')?.value }}</p>\n                  <p>{{ shippingForm.get('city')?.value }}, {{ shippingForm.get('state')?.value }} {{ shippingForm.get('pincode')?.value }}</p>\n                </div>\n              </div>\n              <div class=\"payment-summary\">\n                <h3>Payment Method</h3>\n                <p>{{ getPaymentMethodName(selectedPaymentMethod) }}</p>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"order-success\" *ngIf=\"orderPlaced\">\n            <div class=\"success-icon\">\n              <i class=\"fas fa-check-circle\"></i>\n            </div>\n            <h2>Order Placed Successfully!</h2>\n            <p>Your order #{{ orderNumber }} has been placed successfully.</p>\n            <div class=\"order-actions\">\n              <button class=\"btn btn-primary\" (click)=\"viewOrder()\">View Order</button>\n              <button class=\"btn btn-secondary\" (click)=\"continueShopping()\">Continue Shopping</button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Order Summary Sidebar -->\n        <div class=\"order-summary\">\n          <h3>Order Summary</h3>\n          <div class=\"summary-line\">\n            <span>Subtotal ({{ cartSummary?.itemCount }} items)</span>\n            <span>₹{{ cartSummary?.subtotal | number:'1.0-0' }}</span>\n          </div>\n          <div class=\"summary-line\" *ngIf=\"cartSummary && cartSummary.discount > 0\">\n            <span>Discount</span>\n            <span class=\"discount\">-₹{{ cartSummary.discount | number:'1.0-0' }}</span>\n          </div>\n          <div class=\"summary-line\">\n            <span>Tax (18% GST)</span>\n            <span>₹{{ cartSummary ? (cartSummary.subtotal * 0.18) | number:'1.0-0' : '0' }}</span>\n          </div>\n          <div class=\"summary-line\">\n            <span>Shipping</span>\n            <span>FREE</span>\n          </div>\n          <div class=\"summary-line total\">\n            <span><strong>Total</strong></span>\n            <span><strong>₹{{ cartSummary?.total | number:'1.0-0' }}</strong></span>\n          </div>\n\n          <div class=\"checkout-actions\">\n            <button class=\"btn btn-secondary\" \n                    *ngIf=\"currentStep > 1 && currentStep < 4\" \n                    (click)=\"previousStep()\"\n                    [disabled]=\"processing\">\n              Previous\n            </button>\n            <button class=\"btn btn-primary\" \n                    *ngIf=\"currentStep < 4\" \n                    (click)=\"nextStep()\"\n                    [disabled]=\"!canProceed() || processing\">\n              <span *ngIf=\"!processing\">{{ currentStep === 3 ? 'Place Order' : 'Continue' }}</span>\n              <span *ngIf=\"processing\">\n                <i class=\"fas fa-spinner fa-spin\"></i> Processing...\n              </span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .checkout-container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 20px;\n    }\n\n    .checkout-header {\n      margin-bottom: 30px;\n    }\n\n    .checkout-header h1 {\n      font-size: 2rem;\n      font-weight: 700;\n      color: #2d3436;\n      margin-bottom: 20px;\n    }\n\n    .step-indicator {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 30px;\n    }\n\n    .step {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      flex: 1;\n      position: relative;\n    }\n\n    .step:not(:last-child)::after {\n      content: '';\n      position: absolute;\n      top: 20px;\n      right: -50%;\n      width: 100%;\n      height: 2px;\n      background: #ddd;\n      z-index: 1;\n    }\n\n    .step.completed:not(:last-child)::after {\n      background: #00b894;\n    }\n\n    .step-number {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background: #ddd;\n      color: #636e72;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: 600;\n      margin-bottom: 8px;\n      position: relative;\n      z-index: 2;\n    }\n\n    .step.active .step-number {\n      background: #0984e3;\n      color: white;\n    }\n\n    .step.completed .step-number {\n      background: #00b894;\n      color: white;\n    }\n\n    .step-label {\n      font-size: 14px;\n      color: #636e72;\n      text-align: center;\n    }\n\n    .checkout-content {\n      display: grid;\n      grid-template-columns: 1fr 350px;\n      gap: 30px;\n    }\n\n    .checkout-step {\n      background: white;\n      padding: 30px;\n      border-radius: 12px;\n      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n    }\n\n    .checkout-step h2 {\n      font-size: 1.5rem;\n      font-weight: 600;\n      color: #2d3436;\n      margin-bottom: 20px;\n    }\n\n    .cart-items {\n      space-y: 16px;\n    }\n\n    .cart-item {\n      display: flex;\n      gap: 16px;\n      padding: 16px;\n      border: 1px solid #e9ecef;\n      border-radius: 8px;\n      margin-bottom: 16px;\n    }\n\n    .item-image {\n      width: 80px;\n      height: 80px;\n      object-fit: cover;\n      border-radius: 8px;\n    }\n\n    .item-details {\n      flex: 1;\n    }\n\n    .item-details h3 {\n      font-size: 16px;\n      font-weight: 600;\n      color: #2d3436;\n      margin-bottom: 4px;\n    }\n\n    .item-brand {\n      color: #636e72;\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .item-variants {\n      display: flex;\n      gap: 12px;\n      font-size: 12px;\n      color: #636e72;\n      margin-bottom: 8px;\n    }\n\n    .item-price {\n      display: flex;\n      gap: 8px;\n      align-items: center;\n    }\n\n    .current-price {\n      font-weight: 600;\n      color: #2d3436;\n    }\n\n    .original-price {\n      text-decoration: line-through;\n      color: #636e72;\n      font-size: 14px;\n    }\n\n    .item-quantity {\n      display: flex;\n      flex-direction: column;\n      align-items: flex-end;\n      gap: 8px;\n    }\n\n    .item-total {\n      font-weight: 600;\n      color: #2d3436;\n    }\n\n    .shipping-form {\n      space-y: 20px;\n    }\n\n    .form-row {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 20px;\n    }\n\n    .form-group {\n      margin-bottom: 20px;\n    }\n\n    .form-group label {\n      display: block;\n      font-weight: 600;\n      color: #2d3436;\n      margin-bottom: 8px;\n    }\n\n    .form-control {\n      width: 100%;\n      padding: 12px;\n      border: 1px solid #ddd;\n      border-radius: 8px;\n      font-size: 14px;\n      transition: border-color 0.3s ease;\n    }\n\n    .form-control:focus {\n      outline: none;\n      border-color: #0984e3;\n      box-shadow: 0 0 0 3px rgba(9, 132, 227, 0.1);\n    }\n\n    .error-message {\n      color: #e74c3c;\n      font-size: 12px;\n      margin-top: 4px;\n    }\n\n    .payment-methods {\n      space-y: 12px;\n    }\n\n    .payment-option {\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      padding: 16px;\n      border: 2px solid #e9ecef;\n      border-radius: 8px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      margin-bottom: 12px;\n    }\n\n    .payment-option:hover {\n      border-color: #0984e3;\n    }\n\n    .payment-option.selected {\n      border-color: #0984e3;\n      background: rgba(9, 132, 227, 0.05);\n    }\n\n    .payment-option i {\n      font-size: 24px;\n      color: #0984e3;\n    }\n\n    .method-details {\n      flex: 1;\n    }\n\n    .method-details h3 {\n      font-size: 16px;\n      font-weight: 600;\n      color: #2d3436;\n      margin-bottom: 4px;\n    }\n\n    .method-details p {\n      color: #636e72;\n      font-size: 14px;\n    }\n\n    .order-summary {\n      background: white;\n      padding: 24px;\n      border-radius: 12px;\n      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n      height: fit-content;\n      position: sticky;\n      top: 20px;\n    }\n\n    .order-summary h3 {\n      font-size: 1.25rem;\n      font-weight: 600;\n      color: #2d3436;\n      margin-bottom: 20px;\n    }\n\n    .summary-line {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 12px;\n      font-size: 14px;\n    }\n\n    .summary-line.total {\n      border-top: 1px solid #e9ecef;\n      padding-top: 12px;\n      margin-top: 16px;\n      font-size: 16px;\n    }\n\n    .discount {\n      color: #00b894;\n    }\n\n    .checkout-actions {\n      margin-top: 24px;\n      display: flex;\n      gap: 12px;\n    }\n\n    .btn {\n      padding: 12px 24px;\n      border: none;\n      border-radius: 8px;\n      font-weight: 600;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      flex: 1;\n    }\n\n    .btn-primary {\n      background: #0984e3;\n      color: white;\n    }\n\n    .btn-primary:hover:not(:disabled) {\n      background: #0770c2;\n    }\n\n    .btn-secondary {\n      background: #636e72;\n      color: white;\n    }\n\n    .btn-secondary:hover:not(:disabled) {\n      background: #4a5459;\n    }\n\n    .btn:disabled {\n      opacity: 0.6;\n      cursor: not-allowed;\n    }\n\n    .order-success {\n      text-align: center;\n      padding: 40px 20px;\n    }\n\n    .success-icon {\n      font-size: 4rem;\n      color: #00b894;\n      margin-bottom: 20px;\n    }\n\n    .order-success h2 {\n      color: #00b894;\n      margin-bottom: 16px;\n    }\n\n    .order-actions {\n      display: flex;\n      gap: 16px;\n      justify-content: center;\n      margin-top: 24px;\n    }\n\n    .empty-cart {\n      text-align: center;\n      padding: 60px 20px;\n    }\n\n    .empty-cart i {\n      font-size: 4rem;\n      color: #ddd;\n      margin-bottom: 20px;\n    }\n\n    .empty-cart h3 {\n      color: #636e72;\n      margin-bottom: 12px;\n    }\n\n    @media (max-width: 768px) {\n      .checkout-content {\n        grid-template-columns: 1fr;\n      }\n\n      .form-row {\n        grid-template-columns: 1fr;\n      }\n\n      .step-indicator {\n        flex-direction: column;\n        gap: 16px;\n      }\n\n      .step:not(:last-child)::after {\n        display: none;\n      }\n    }\n  `]\n})], CheckoutComponent);\nexport { CheckoutComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "ReactiveFormsModule", "FormsModule", "Validators", "CheckoutComponent", "constructor", "fb", "cartService", "authService", "router", "currentStep", "cartItems", "cartSummary", "processing", "orderPlaced", "orderNumber", "selectedPaymentMethod", "paymentMethods", "id", "name", "description", "icon", "shippingForm", "group", "fullName", "required", "phone", "pattern", "addressLine1", "addressLine2", "city", "state", "pincode", "ngOnInit", "isAuthenticated", "navigate", "queryParams", "returnUrl", "loadCartData", "_id", "product", "price", "originalPrice", "images", "url", "isPrimary", "brand", "discount", "quantity", "size", "color", "addedAt", "Date", "itemCount", "totalQuantity", "subtotal", "total", "canProceed", "length", "valid", "nextStep", "placeOrder", "previousStep", "selectPaymentMethod", "methodId", "getPaymentMethodName", "method", "find", "m", "orderData", "shippingAddress", "value", "billing<PERSON><PERSON>ress", "paymentMethod", "setTimeout", "now", "alert", "goToShopping", "viewOrder", "continueShopping", "__decorate", "selector", "standalone", "imports", "template", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\checkout\\checkout.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { CartService, CartItem, CartSummary } from '../../core/services/cart.service';\n// import { CheckoutService } from '../../core/services/checkout.service';\n// import { PaymentService } from '../../core/services/payment.service';\nimport { AuthService } from '../../core/services/auth.service';\n// import { ToastrService } from 'ngx-toastr';\n\n@Component({\n  selector: 'app-checkout',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, FormsModule],\n  template: `\n    <div class=\"checkout-container\">\n      <div class=\"checkout-header\">\n        <h1>Checkout</h1>\n        <div class=\"step-indicator\">\n          <div class=\"step\" [class.active]=\"currentStep >= 1\" [class.completed]=\"currentStep > 1\">\n            <span class=\"step-number\">1</span>\n            <span class=\"step-label\">Cart Review</span>\n          </div>\n          <div class=\"step\" [class.active]=\"currentStep >= 2\" [class.completed]=\"currentStep > 2\">\n            <span class=\"step-number\">2</span>\n            <span class=\"step-label\">Shipping</span>\n          </div>\n          <div class=\"step\" [class.active]=\"currentStep >= 3\" [class.completed]=\"currentStep > 3\">\n            <span class=\"step-number\">3</span>\n            <span class=\"step-label\">Payment</span>\n          </div>\n          <div class=\"step\" [class.active]=\"currentStep >= 4\">\n            <span class=\"step-number\">4</span>\n            <span class=\"step-label\">Confirmation</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"checkout-content\">\n        <!-- Step 1: Cart Review -->\n        <div class=\"checkout-step\" *ngIf=\"currentStep === 1\">\n          <h2>Review Your Order</h2>\n          <div class=\"cart-items\" *ngIf=\"cartItems.length > 0\">\n            <div class=\"cart-item\" *ngFor=\"let item of cartItems\">\n              <img [src]=\"item.product.images[0]?.url || item.product.images[0]\" [alt]=\"item.product.name\" class=\"item-image\">\n              <div class=\"item-details\">\n                <h3>{{ item.product.name }}</h3>\n                <p class=\"item-brand\">{{ item.product.brand }}</p>\n                <div class=\"item-variants\" *ngIf=\"item.size || item.color\">\n                  <span *ngIf=\"item.size\">Size: {{ item.size }}</span>\n                  <span *ngIf=\"item.color\">Color: {{ item.color }}</span>\n                </div>\n                <div class=\"item-price\">\n                  <span class=\"current-price\">₹{{ item.product.price | number:'1.0-0' }}</span>\n                  <span class=\"original-price\" *ngIf=\"item.product.originalPrice\">\n                    ₹{{ item.product.originalPrice | number:'1.0-0' }}\n                  </span>\n                </div>\n              </div>\n              <div class=\"item-quantity\">\n                <span>Qty: {{ item.quantity }}</span>\n                <span class=\"item-total\">₹{{ (item.product.price * item.quantity) | number:'1.0-0' }}</span>\n              </div>\n            </div>\n          </div>\n          <div class=\"empty-cart\" *ngIf=\"cartItems.length === 0\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            <h3>Your cart is empty</h3>\n            <p>Add some items to your cart to continue</p>\n            <button class=\"btn btn-primary\" (click)=\"goToShopping()\">Continue Shopping</button>\n          </div>\n        </div>\n\n        <!-- Step 2: Shipping Information -->\n        <div class=\"checkout-step\" *ngIf=\"currentStep === 2\">\n          <h2>Shipping Information</h2>\n          <form [formGroup]=\"shippingForm\" class=\"shipping-form\">\n            <div class=\"form-row\">\n              <div class=\"form-group\">\n                <label for=\"fullName\">Full Name *</label>\n                <input type=\"text\" id=\"fullName\" formControlName=\"fullName\" class=\"form-control\">\n                <div class=\"error-message\" *ngIf=\"shippingForm.get('fullName')?.invalid && shippingForm.get('fullName')?.touched\">\n                  Full name is required\n                </div>\n              </div>\n              <div class=\"form-group\">\n                <label for=\"phone\">Phone Number *</label>\n                <input type=\"tel\" id=\"phone\" formControlName=\"phone\" class=\"form-control\">\n                <div class=\"error-message\" *ngIf=\"shippingForm.get('phone')?.invalid && shippingForm.get('phone')?.touched\">\n                  Valid phone number is required\n                </div>\n              </div>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"addressLine1\">Address Line 1 *</label>\n              <input type=\"text\" id=\"addressLine1\" formControlName=\"addressLine1\" class=\"form-control\">\n              <div class=\"error-message\" *ngIf=\"shippingForm.get('addressLine1')?.invalid && shippingForm.get('addressLine1')?.touched\">\n                Address is required\n              </div>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"addressLine2\">Address Line 2</label>\n              <input type=\"text\" id=\"addressLine2\" formControlName=\"addressLine2\" class=\"form-control\">\n            </div>\n\n            <div class=\"form-row\">\n              <div class=\"form-group\">\n                <label for=\"city\">City *</label>\n                <input type=\"text\" id=\"city\" formControlName=\"city\" class=\"form-control\">\n                <div class=\"error-message\" *ngIf=\"shippingForm.get('city')?.invalid && shippingForm.get('city')?.touched\">\n                  City is required\n                </div>\n              </div>\n              <div class=\"form-group\">\n                <label for=\"state\">State *</label>\n                <select id=\"state\" formControlName=\"state\" class=\"form-control\">\n                  <option value=\"\">Select State</option>\n                  <option value=\"Maharashtra\">Maharashtra</option>\n                  <option value=\"Delhi\">Delhi</option>\n                  <option value=\"Karnataka\">Karnataka</option>\n                  <option value=\"Tamil Nadu\">Tamil Nadu</option>\n                  <option value=\"Gujarat\">Gujarat</option>\n                  <option value=\"Rajasthan\">Rajasthan</option>\n                  <option value=\"West Bengal\">West Bengal</option>\n                  <option value=\"Uttar Pradesh\">Uttar Pradesh</option>\n                </select>\n                <div class=\"error-message\" *ngIf=\"shippingForm.get('state')?.invalid && shippingForm.get('state')?.touched\">\n                  State is required\n                </div>\n              </div>\n              <div class=\"form-group\">\n                <label for=\"pincode\">Pincode *</label>\n                <input type=\"text\" id=\"pincode\" formControlName=\"pincode\" class=\"form-control\" maxlength=\"6\">\n                <div class=\"error-message\" *ngIf=\"shippingForm.get('pincode')?.invalid && shippingForm.get('pincode')?.touched\">\n                  Valid 6-digit pincode is required\n                </div>\n              </div>\n            </div>\n          </form>\n        </div>\n\n        <!-- Step 3: Payment Method -->\n        <div class=\"checkout-step\" *ngIf=\"currentStep === 3\">\n          <h2>Payment Method</h2>\n          <div class=\"payment-methods\">\n            <div class=\"payment-option\" \n                 *ngFor=\"let method of paymentMethods\" \n                 [class.selected]=\"selectedPaymentMethod === method.id\"\n                 (click)=\"selectPaymentMethod(method.id)\">\n              <i [class]=\"method.icon\"></i>\n              <div class=\"method-details\">\n                <h3>{{ method.name }}</h3>\n                <p>{{ method.description }}</p>\n              </div>\n              <div class=\"method-radio\">\n                <input type=\"radio\" [value]=\"method.id\" [(ngModel)]=\"selectedPaymentMethod\">\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Step 4: Order Confirmation -->\n        <div class=\"checkout-step\" *ngIf=\"currentStep === 4\">\n          <div class=\"order-confirmation\" *ngIf=\"!orderPlaced\">\n            <h2>Order Confirmation</h2>\n            <div class=\"confirmation-details\">\n              <div class=\"shipping-summary\">\n                <h3>Shipping Address</h3>\n                <div class=\"address-display\">\n                  <p><strong>{{ shippingForm.get('fullName')?.value }}</strong></p>\n                  <p>{{ shippingForm.get('phone')?.value }}</p>\n                  <p>{{ shippingForm.get('addressLine1')?.value }}</p>\n                  <p *ngIf=\"shippingForm.get('addressLine2')?.value\">{{ shippingForm.get('addressLine2')?.value }}</p>\n                  <p>{{ shippingForm.get('city')?.value }}, {{ shippingForm.get('state')?.value }} {{ shippingForm.get('pincode')?.value }}</p>\n                </div>\n              </div>\n              <div class=\"payment-summary\">\n                <h3>Payment Method</h3>\n                <p>{{ getPaymentMethodName(selectedPaymentMethod) }}</p>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"order-success\" *ngIf=\"orderPlaced\">\n            <div class=\"success-icon\">\n              <i class=\"fas fa-check-circle\"></i>\n            </div>\n            <h2>Order Placed Successfully!</h2>\n            <p>Your order #{{ orderNumber }} has been placed successfully.</p>\n            <div class=\"order-actions\">\n              <button class=\"btn btn-primary\" (click)=\"viewOrder()\">View Order</button>\n              <button class=\"btn btn-secondary\" (click)=\"continueShopping()\">Continue Shopping</button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Order Summary Sidebar -->\n        <div class=\"order-summary\">\n          <h3>Order Summary</h3>\n          <div class=\"summary-line\">\n            <span>Subtotal ({{ cartSummary?.itemCount }} items)</span>\n            <span>₹{{ cartSummary?.subtotal | number:'1.0-0' }}</span>\n          </div>\n          <div class=\"summary-line\" *ngIf=\"cartSummary && cartSummary.discount > 0\">\n            <span>Discount</span>\n            <span class=\"discount\">-₹{{ cartSummary.discount | number:'1.0-0' }}</span>\n          </div>\n          <div class=\"summary-line\">\n            <span>Tax (18% GST)</span>\n            <span>₹{{ cartSummary ? (cartSummary.subtotal * 0.18) | number:'1.0-0' : '0' }}</span>\n          </div>\n          <div class=\"summary-line\">\n            <span>Shipping</span>\n            <span>FREE</span>\n          </div>\n          <div class=\"summary-line total\">\n            <span><strong>Total</strong></span>\n            <span><strong>₹{{ cartSummary?.total | number:'1.0-0' }}</strong></span>\n          </div>\n\n          <div class=\"checkout-actions\">\n            <button class=\"btn btn-secondary\" \n                    *ngIf=\"currentStep > 1 && currentStep < 4\" \n                    (click)=\"previousStep()\"\n                    [disabled]=\"processing\">\n              Previous\n            </button>\n            <button class=\"btn btn-primary\" \n                    *ngIf=\"currentStep < 4\" \n                    (click)=\"nextStep()\"\n                    [disabled]=\"!canProceed() || processing\">\n              <span *ngIf=\"!processing\">{{ currentStep === 3 ? 'Place Order' : 'Continue' }}</span>\n              <span *ngIf=\"processing\">\n                <i class=\"fas fa-spinner fa-spin\"></i> Processing...\n              </span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .checkout-container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 20px;\n    }\n\n    .checkout-header {\n      margin-bottom: 30px;\n    }\n\n    .checkout-header h1 {\n      font-size: 2rem;\n      font-weight: 700;\n      color: #2d3436;\n      margin-bottom: 20px;\n    }\n\n    .step-indicator {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 30px;\n    }\n\n    .step {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      flex: 1;\n      position: relative;\n    }\n\n    .step:not(:last-child)::after {\n      content: '';\n      position: absolute;\n      top: 20px;\n      right: -50%;\n      width: 100%;\n      height: 2px;\n      background: #ddd;\n      z-index: 1;\n    }\n\n    .step.completed:not(:last-child)::after {\n      background: #00b894;\n    }\n\n    .step-number {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background: #ddd;\n      color: #636e72;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: 600;\n      margin-bottom: 8px;\n      position: relative;\n      z-index: 2;\n    }\n\n    .step.active .step-number {\n      background: #0984e3;\n      color: white;\n    }\n\n    .step.completed .step-number {\n      background: #00b894;\n      color: white;\n    }\n\n    .step-label {\n      font-size: 14px;\n      color: #636e72;\n      text-align: center;\n    }\n\n    .checkout-content {\n      display: grid;\n      grid-template-columns: 1fr 350px;\n      gap: 30px;\n    }\n\n    .checkout-step {\n      background: white;\n      padding: 30px;\n      border-radius: 12px;\n      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n    }\n\n    .checkout-step h2 {\n      font-size: 1.5rem;\n      font-weight: 600;\n      color: #2d3436;\n      margin-bottom: 20px;\n    }\n\n    .cart-items {\n      space-y: 16px;\n    }\n\n    .cart-item {\n      display: flex;\n      gap: 16px;\n      padding: 16px;\n      border: 1px solid #e9ecef;\n      border-radius: 8px;\n      margin-bottom: 16px;\n    }\n\n    .item-image {\n      width: 80px;\n      height: 80px;\n      object-fit: cover;\n      border-radius: 8px;\n    }\n\n    .item-details {\n      flex: 1;\n    }\n\n    .item-details h3 {\n      font-size: 16px;\n      font-weight: 600;\n      color: #2d3436;\n      margin-bottom: 4px;\n    }\n\n    .item-brand {\n      color: #636e72;\n      font-size: 14px;\n      margin-bottom: 8px;\n    }\n\n    .item-variants {\n      display: flex;\n      gap: 12px;\n      font-size: 12px;\n      color: #636e72;\n      margin-bottom: 8px;\n    }\n\n    .item-price {\n      display: flex;\n      gap: 8px;\n      align-items: center;\n    }\n\n    .current-price {\n      font-weight: 600;\n      color: #2d3436;\n    }\n\n    .original-price {\n      text-decoration: line-through;\n      color: #636e72;\n      font-size: 14px;\n    }\n\n    .item-quantity {\n      display: flex;\n      flex-direction: column;\n      align-items: flex-end;\n      gap: 8px;\n    }\n\n    .item-total {\n      font-weight: 600;\n      color: #2d3436;\n    }\n\n    .shipping-form {\n      space-y: 20px;\n    }\n\n    .form-row {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 20px;\n    }\n\n    .form-group {\n      margin-bottom: 20px;\n    }\n\n    .form-group label {\n      display: block;\n      font-weight: 600;\n      color: #2d3436;\n      margin-bottom: 8px;\n    }\n\n    .form-control {\n      width: 100%;\n      padding: 12px;\n      border: 1px solid #ddd;\n      border-radius: 8px;\n      font-size: 14px;\n      transition: border-color 0.3s ease;\n    }\n\n    .form-control:focus {\n      outline: none;\n      border-color: #0984e3;\n      box-shadow: 0 0 0 3px rgba(9, 132, 227, 0.1);\n    }\n\n    .error-message {\n      color: #e74c3c;\n      font-size: 12px;\n      margin-top: 4px;\n    }\n\n    .payment-methods {\n      space-y: 12px;\n    }\n\n    .payment-option {\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      padding: 16px;\n      border: 2px solid #e9ecef;\n      border-radius: 8px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      margin-bottom: 12px;\n    }\n\n    .payment-option:hover {\n      border-color: #0984e3;\n    }\n\n    .payment-option.selected {\n      border-color: #0984e3;\n      background: rgba(9, 132, 227, 0.05);\n    }\n\n    .payment-option i {\n      font-size: 24px;\n      color: #0984e3;\n    }\n\n    .method-details {\n      flex: 1;\n    }\n\n    .method-details h3 {\n      font-size: 16px;\n      font-weight: 600;\n      color: #2d3436;\n      margin-bottom: 4px;\n    }\n\n    .method-details p {\n      color: #636e72;\n      font-size: 14px;\n    }\n\n    .order-summary {\n      background: white;\n      padding: 24px;\n      border-radius: 12px;\n      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n      height: fit-content;\n      position: sticky;\n      top: 20px;\n    }\n\n    .order-summary h3 {\n      font-size: 1.25rem;\n      font-weight: 600;\n      color: #2d3436;\n      margin-bottom: 20px;\n    }\n\n    .summary-line {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 12px;\n      font-size: 14px;\n    }\n\n    .summary-line.total {\n      border-top: 1px solid #e9ecef;\n      padding-top: 12px;\n      margin-top: 16px;\n      font-size: 16px;\n    }\n\n    .discount {\n      color: #00b894;\n    }\n\n    .checkout-actions {\n      margin-top: 24px;\n      display: flex;\n      gap: 12px;\n    }\n\n    .btn {\n      padding: 12px 24px;\n      border: none;\n      border-radius: 8px;\n      font-weight: 600;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      flex: 1;\n    }\n\n    .btn-primary {\n      background: #0984e3;\n      color: white;\n    }\n\n    .btn-primary:hover:not(:disabled) {\n      background: #0770c2;\n    }\n\n    .btn-secondary {\n      background: #636e72;\n      color: white;\n    }\n\n    .btn-secondary:hover:not(:disabled) {\n      background: #4a5459;\n    }\n\n    .btn:disabled {\n      opacity: 0.6;\n      cursor: not-allowed;\n    }\n\n    .order-success {\n      text-align: center;\n      padding: 40px 20px;\n    }\n\n    .success-icon {\n      font-size: 4rem;\n      color: #00b894;\n      margin-bottom: 20px;\n    }\n\n    .order-success h2 {\n      color: #00b894;\n      margin-bottom: 16px;\n    }\n\n    .order-actions {\n      display: flex;\n      gap: 16px;\n      justify-content: center;\n      margin-top: 24px;\n    }\n\n    .empty-cart {\n      text-align: center;\n      padding: 60px 20px;\n    }\n\n    .empty-cart i {\n      font-size: 4rem;\n      color: #ddd;\n      margin-bottom: 20px;\n    }\n\n    .empty-cart h3 {\n      color: #636e72;\n      margin-bottom: 12px;\n    }\n\n    @media (max-width: 768px) {\n      .checkout-content {\n        grid-template-columns: 1fr;\n      }\n\n      .form-row {\n        grid-template-columns: 1fr;\n      }\n\n      .step-indicator {\n        flex-direction: column;\n        gap: 16px;\n      }\n\n      .step:not(:last-child)::after {\n        display: none;\n      }\n    }\n  `]\n})\nexport class CheckoutComponent implements OnInit {\n  currentStep = 1;\n  cartItems: CartItem[] = [];\n  cartSummary: CartSummary | null = null;\n  processing = false;\n  orderPlaced = false;\n  orderNumber = '';\n\n  shippingForm: FormGroup;\n  selectedPaymentMethod = '';\n\n  paymentMethods = [\n    {\n      id: 'card',\n      name: 'Credit/Debit Card',\n      description: 'Pay securely with your credit or debit card',\n      icon: 'fas fa-credit-card'\n    },\n    {\n      id: 'upi',\n      name: 'UPI',\n      description: 'Pay using UPI apps like GPay, PhonePe, Paytm',\n      icon: 'fas fa-mobile-alt'\n    },\n    {\n      id: 'netbanking',\n      name: 'Net Banking',\n      description: 'Pay directly from your bank account',\n      icon: 'fas fa-university'\n    },\n    {\n      id: 'wallet',\n      name: 'Digital Wallet',\n      description: 'Pay using digital wallets',\n      icon: 'fas fa-wallet'\n    },\n    {\n      id: 'cod',\n      name: 'Cash on Delivery',\n      description: 'Pay when your order is delivered',\n      icon: 'fas fa-money-bill-wave'\n    }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private cartService: CartService,\n    // private checkoutService: CheckoutService,\n    // private paymentService: PaymentService,\n    private authService: AuthService,\n    private router: Router\n  ) {\n    this.shippingForm = this.fb.group({\n      fullName: ['', Validators.required],\n      phone: ['', [Validators.required, Validators.pattern(/^[6-9]\\d{9}$/)]],\n      addressLine1: ['', Validators.required],\n      addressLine2: [''],\n      city: ['', Validators.required],\n      state: ['', Validators.required],\n      pincode: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n  }\n\n  ngOnInit() {\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/auth/login'], { queryParams: { returnUrl: '/checkout' } });\n      return;\n    }\n\n    this.loadCartData();\n  }\n\n  loadCartData() {\n    // Simulate cart data loading\n    this.cartItems = [\n      {\n        _id: '1',\n        product: {\n          _id: 'prod1',\n          name: 'Sample Product',\n          price: 999,\n          originalPrice: 1299,\n          images: [{ url: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400', isPrimary: true }],\n          brand: 'Sample Brand',\n          discount: 23\n        },\n        quantity: 1,\n        size: 'M',\n        color: 'Blue',\n        addedAt: new Date()\n      }\n    ];\n\n    this.cartSummary = {\n      itemCount: 1,\n      totalQuantity: 1,\n      subtotal: 999,\n      discount: 300,\n      total: 878.82\n    } as any;\n  }\n\n  canProceed(): boolean {\n    switch (this.currentStep) {\n      case 1:\n        return this.cartItems.length > 0;\n      case 2:\n        return this.shippingForm.valid;\n      case 3:\n        return !!this.selectedPaymentMethod;\n      default:\n        return false;\n    }\n  }\n\n  nextStep() {\n    if (!this.canProceed()) return;\n\n    if (this.currentStep === 3) {\n      this.placeOrder();\n    } else {\n      this.currentStep++;\n    }\n  }\n\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n\n  selectPaymentMethod(methodId: string) {\n    this.selectedPaymentMethod = methodId;\n  }\n\n  getPaymentMethodName(methodId: string): string {\n    const method = this.paymentMethods.find(m => m.id === methodId);\n    return method ? method.name : '';\n  }\n\n  placeOrder() {\n    this.processing = true;\n\n    const orderData = {\n      shippingAddress: this.shippingForm.value,\n      billingAddress: this.shippingForm.value,\n      paymentMethod: this.selectedPaymentMethod\n    };\n\n    // Simulate order placement for now\n    setTimeout(() => {\n      this.orderNumber = `ORD${Date.now()}`;\n      this.currentStep = 4;\n      this.orderPlaced = true;\n      this.processing = false;\n      alert('Order placed successfully!');\n    }, 2000);\n\n    // TODO: Implement actual checkout service\n    // this.checkoutService.placeOrder(orderData).subscribe({\n    //   next: (response) => {\n    //     if (response.success) {\n    //       this.orderNumber = response.data.orderNumber;\n    //       this.currentStep = 4;\n    //       this.orderPlaced = true;\n    //       this.toastr.success('Order placed successfully!');\n    //     }\n    //     this.processing = false;\n    //   },\n    //   error: (error) => {\n    //     console.error('Error placing order:', error);\n    //     this.toastr.error('Failed to place order. Please try again.');\n    //     this.processing = false;\n    //   }\n    // });\n  }\n\n  goToShopping() {\n    this.router.navigate(['/products']);\n  }\n\n  viewOrder() {\n    this.router.navigate(['/account/orders']);\n  }\n\n  continueShopping() {\n    this.router.navigate(['/']);\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,EAA0BC,UAAU,QAAQ,gBAAgB;AAMrG;AAonBO,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EA4C5BC,YACUC,EAAe,EACfC,WAAwB;EAChC;EACA;EACQC,WAAwB,EACxBC,MAAc;IALd,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IAGX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAjDhB,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,WAAW,GAAG,EAAE;IAGhB,KAAAC,qBAAqB,GAAG,EAAE;IAE1B,KAAAC,cAAc,GAAG,CACf;MACEC,EAAE,EAAE,MAAM;MACVC,IAAI,EAAE,mBAAmB;MACzBC,WAAW,EAAE,6CAA6C;MAC1DC,IAAI,EAAE;KACP,EACD;MACEH,EAAE,EAAE,KAAK;MACTC,IAAI,EAAE,KAAK;MACXC,WAAW,EAAE,8CAA8C;MAC3DC,IAAI,EAAE;KACP,EACD;MACEH,EAAE,EAAE,YAAY;MAChBC,IAAI,EAAE,aAAa;MACnBC,WAAW,EAAE,qCAAqC;MAClDC,IAAI,EAAE;KACP,EACD;MACEH,EAAE,EAAE,QAAQ;MACZC,IAAI,EAAE,gBAAgB;MACtBC,WAAW,EAAE,2BAA2B;MACxCC,IAAI,EAAE;KACP,EACD;MACEH,EAAE,EAAE,KAAK;MACTC,IAAI,EAAE,kBAAkB;MACxBC,WAAW,EAAE,kCAAkC;MAC/CC,IAAI,EAAE;KACP,CACF;IAUC,IAAI,CAACC,YAAY,GAAG,IAAI,CAAChB,EAAE,CAACiB,KAAK,CAAC;MAChCC,QAAQ,EAAE,CAAC,EAAE,EAAErB,UAAU,CAACsB,QAAQ,CAAC;MACnCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACvB,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACwB,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC;MACtEC,YAAY,EAAE,CAAC,EAAE,EAAEzB,UAAU,CAACsB,QAAQ,CAAC;MACvCI,YAAY,EAAE,CAAC,EAAE,CAAC;MAClBC,IAAI,EAAE,CAAC,EAAE,EAAE3B,UAAU,CAACsB,QAAQ,CAAC;MAC/BM,KAAK,EAAE,CAAC,EAAE,EAAE5B,UAAU,CAACsB,QAAQ,CAAC;MAChCO,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACwB,OAAO,CAAC,SAAS,CAAC,CAAC;KACnE,CAAC;EACJ;EAEAM,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACzB,WAAW,CAAC0B,eAAe,EAAE;MACrC,IAAI,CAACzB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;QAAEC,WAAW,EAAE;UAAEC,SAAS,EAAE;QAAW;MAAE,CAAE,CAAC;MAClF;;IAGF,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV;IACA,IAAI,CAAC3B,SAAS,GAAG,CACf;MACE4B,GAAG,EAAE,GAAG;MACRC,OAAO,EAAE;QACPD,GAAG,EAAE,OAAO;QACZpB,IAAI,EAAE,gBAAgB;QACtBsB,KAAK,EAAE,GAAG;QACVC,aAAa,EAAE,IAAI;QACnBC,MAAM,EAAE,CAAC;UAAEC,GAAG,EAAE,oEAAoE;UAAEC,SAAS,EAAE;QAAI,CAAE,CAAC;QACxGC,KAAK,EAAE,cAAc;QACrBC,QAAQ,EAAE;OACX;MACDC,QAAQ,EAAE,CAAC;MACXC,IAAI,EAAE,GAAG;MACTC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,IAAIC,IAAI;KAClB,CACF;IAED,IAAI,CAACxC,WAAW,GAAG;MACjByC,SAAS,EAAE,CAAC;MACZC,aAAa,EAAE,CAAC;MAChBC,QAAQ,EAAE,GAAG;MACbR,QAAQ,EAAE,GAAG;MACbS,KAAK,EAAE;KACD;EACV;EAEAC,UAAUA,CAAA;IACR,QAAQ,IAAI,CAAC/C,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACC,SAAS,CAAC+C,MAAM,GAAG,CAAC;MAClC,KAAK,CAAC;QACJ,OAAO,IAAI,CAACpC,YAAY,CAACqC,KAAK;MAChC,KAAK,CAAC;QACJ,OAAO,CAAC,CAAC,IAAI,CAAC3C,qBAAqB;MACrC;QACE,OAAO,KAAK;;EAElB;EAEA4C,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACH,UAAU,EAAE,EAAE;IAExB,IAAI,IAAI,CAAC/C,WAAW,KAAK,CAAC,EAAE;MAC1B,IAAI,CAACmD,UAAU,EAAE;KAClB,MAAM;MACL,IAAI,CAACnD,WAAW,EAAE;;EAEtB;EAEAoD,YAAYA,CAAA;IACV,IAAI,IAAI,CAACpD,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEAqD,mBAAmBA,CAACC,QAAgB;IAClC,IAAI,CAAChD,qBAAqB,GAAGgD,QAAQ;EACvC;EAEAC,oBAAoBA,CAACD,QAAgB;IACnC,MAAME,MAAM,GAAG,IAAI,CAACjD,cAAc,CAACkD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClD,EAAE,KAAK8C,QAAQ,CAAC;IAC/D,OAAOE,MAAM,GAAGA,MAAM,CAAC/C,IAAI,GAAG,EAAE;EAClC;EAEA0C,UAAUA,CAAA;IACR,IAAI,CAAChD,UAAU,GAAG,IAAI;IAEtB,MAAMwD,SAAS,GAAG;MAChBC,eAAe,EAAE,IAAI,CAAChD,YAAY,CAACiD,KAAK;MACxCC,cAAc,EAAE,IAAI,CAAClD,YAAY,CAACiD,KAAK;MACvCE,aAAa,EAAE,IAAI,CAACzD;KACrB;IAED;IACA0D,UAAU,CAAC,MAAK;MACd,IAAI,CAAC3D,WAAW,GAAG,MAAMqC,IAAI,CAACuB,GAAG,EAAE,EAAE;MACrC,IAAI,CAACjE,WAAW,GAAG,CAAC;MACpB,IAAI,CAACI,WAAW,GAAG,IAAI;MACvB,IAAI,CAACD,UAAU,GAAG,KAAK;MACvB+D,KAAK,CAAC,4BAA4B,CAAC;IACrC,CAAC,EAAE,IAAI,CAAC;IAER;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACpE,MAAM,CAAC0B,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEA2C,SAASA,CAAA;IACP,IAAI,CAACrE,MAAM,CAAC0B,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEA4C,gBAAgBA,CAAA;IACd,IAAI,CAACtE,MAAM,CAAC0B,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7B;CACD;AA5LY/B,iBAAiB,GAAA4E,UAAA,EAlnB7BjF,SAAS,CAAC;EACTkF,QAAQ,EAAE,cAAc;EACxBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACnF,YAAY,EAAEC,mBAAmB,EAAEC,WAAW,CAAC;EACzDkF,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoOT;EACDC,MAAM,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuYR;CACF,CAAC,C,EACWjF,iBAAiB,CA4L7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}