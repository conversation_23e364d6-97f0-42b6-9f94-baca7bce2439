.cart-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.cart-header {
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-main h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.select-all-btn,
.bulk-remove-btn,
.refresh-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: #fff;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.select-all-btn:hover,
.refresh-btn:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.select-all-btn.selected {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.bulk-remove-btn {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

.bulk-remove-btn:hover:not(:disabled) {
  background: #c82333;
}

.bulk-remove-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.refresh-btn {
  padding: 0.5rem;
  min-width: 40px;
  justify-content: center;
}

.cart-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.cart-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.cart-item {
  display: grid;
  grid-template-columns: auto 100px 1fr auto auto auto;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #eee;
  border-radius: 8px;
  align-items: center;
  transition: all 0.2s;
}

.cart-item.selected {
  border-color: #007bff;
  background: rgba(0, 123, 246, 0.05);
}

.item-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #007bff;
}

.item-checkbox label {
  cursor: pointer;
  margin: 0;
}

.item-image img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 8px;
}

.item-details h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.brand {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.item-options {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.option-tag {
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  color: #555;
}

.item-price-section {
  margin-top: 0.5rem;
}

.unit-price {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.price-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

.quantity-price-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.quantity-info {
  font-size: 0.9rem;
  color: #666;
}

.item-total-label {
  font-size: 0.95rem;
  color: #333;
}

.discount-badge {
  background: #28a745;
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
}

.item-price {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.current-price {
  font-weight: 600;
  color: #e91e63;
}

.original-price {
  color: #999;
  text-decoration: line-through;
  font-size: 0.9rem;
}

.item-quantity-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.quantity-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 0.25rem;
  background: white;
}

.quantity-display {
  min-width: 2.5rem;
  text-align: center;
  font-weight: 600;
  font-size: 1rem;
  color: #333;
}

.item-quantity {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.25rem;
}

.qty-btn {
  width: 30px;
  height: 30px;
  border: none;
  background: #f8f9fa;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.qty-btn:hover:not(:disabled) {
  background: #e9ecef;
}

.qty-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity {
  min-width: 2rem;
  text-align: center;
  font-weight: 600;
}

.item-total-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  text-align: center;
}

.total-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

.total-amount {
  font-weight: 700;
  font-size: 1.2rem;
  color: #e91e63;
}

.savings {
  font-size: 0.8rem;
  color: #28a745;
  font-weight: 500;
}

.item-total {
  font-weight: 600;
  font-size: 1.1rem;
  color: #333;
}

.remove-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: #f8f9fa;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dc3545;
  transition: all 0.2s;
}

.remove-btn:hover {
  background: #dc3545;
  color: white;
}

.summary-card {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  position: sticky;
  top: 2rem;
}

.summary-card h3 {
  margin-bottom: 1rem;
  color: #333;
}

.cart-breakdown {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.breakdown-item:last-child {
  margin-bottom: 0;
}

.breakdown-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.breakdown-value {
  font-size: 0.9rem;
  color: #333;
  font-weight: 600;
}

.summary-details {
  margin-top: 1rem;
}

.free-shipping {
  color: #28a745;
  font-weight: 600;
}

.savings {
  color: #28a745;
  font-weight: 600;
}

.action-buttons {
  margin-top: 1.5rem;
}

.cart-total-highlight {
  background: linear-gradient(135deg, #4834d4, #686de0);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  color: white;
}

.total-amount-display {
  display: flex;
  align-items: center;
  gap: 12px;
}

.total-amount-display i {
  font-size: 24px;
  color: #fff;
}

.amount-details {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.amount-label {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
  margin-bottom: 4px;
}

.amount-value {
  font-size: 24px;
  font-weight: 700;
  color: #fff;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.summary-row.total {
  font-weight: 700;
  font-size: 1.2rem;
  color: #333;
}

.discount {
  color: #28a745;
}

// Selection status styles
.selection-status {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  border-left: 4px solid #007bff;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-weight: 500;
  color: #666;
}

.status-value {
  font-weight: 600;
  color: #333;
}

// No selection message styles
.no-selection-message {
  text-align: center;
  padding: 24px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin-bottom: 16px;
}

.no-selection-message i {
  font-size: 24px;
  color: #856404;
  margin-bottom: 8px;
}

.no-selection-message p {
  margin: 0;
  color: #856404;
  font-weight: 500;
}

// All items summary styles
.all-items-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  margin-top: 16px;
}

.all-items-summary h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.checkout-btn {
  width: 100%;
  background: #007bff;
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 1rem;
  transition: background 0.2s;
}

.checkout-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.checkout-btn:hover:not(:disabled) {
  background: #0056b3;
}

.continue-shopping-btn {
  width: 100%;
  background: transparent;
  color: #007bff;
  border: 2px solid #007bff;
  padding: 1rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.continue-shopping-btn:hover {
  background: #007bff;
  color: white;
}

.empty-cart {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.empty-cart i {
  font-size: 4rem;
  margin-bottom: 1rem;
  color: #ddd;
}

.shop-now-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 1rem;
}

.loading-container {
  text-align: center;
  padding: 4rem 2rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .cart-content {
    grid-template-columns: 1fr;
  }

  .cart-item {
    grid-template-columns: 80px 1fr;
    grid-template-rows: auto auto auto;
    gap: 0.5rem;
  }

  .item-quantity,
  .item-total,
  .remove-btn {
    grid-column: 1 / -1;
    justify-self: start;
  }
}
