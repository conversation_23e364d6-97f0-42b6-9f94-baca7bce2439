{"ast": null, "code": "import { BehaviorSubject, combineLatest, of } from 'rxjs';\nimport { map, distinctUntilChanged, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./auth.service\";\nimport * as i2 from \"./recommendation.service\";\nimport * as i3 from \"./analytics.service\";\nimport * as i4 from \"./cart.service\";\nimport * as i5 from \"./wishlist.service\";\nimport * as i6 from \"./mobile-optimization.service\";\nexport class DataFlowService {\n  constructor(authService, recommendationService, analyticsService, cartService, wishlistService, mobileService) {\n    this.authService = authService;\n    this.recommendationService = recommendationService;\n    this.analyticsService = analyticsService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.mobileService = mobileService;\n    this.appState$ = new BehaviorSubject(this.getInitialState());\n    this.isLoading$ = new BehaviorSubject(false);\n    this.errors$ = new BehaviorSubject([]);\n    this.initializeDataFlow();\n  }\n  // Public Observables\n  getAppState$() {\n    return this.appState$.asObservable();\n  }\n  getIsLoading$() {\n    return this.isLoading$.asObservable();\n  }\n  getErrors$() {\n    return this.errors$.asObservable();\n  }\n  // Specific State Selectors\n  getUser$() {\n    return this.appState$.pipe(map(state => state.user), distinctUntilChanged());\n  }\n  getRecommendations$() {\n    return this.appState$.pipe(map(state => state.recommendations), distinctUntilChanged());\n  }\n  getUserCounts$() {\n    return this.appState$.pipe(map(state => state.userCounts), distinctUntilChanged());\n  }\n  getUIState$() {\n    return this.appState$.pipe(map(state => state.ui), distinctUntilChanged());\n  }\n  // Data Loading Methods\n  loadUserData(userId) {\n    this.setLoading(true);\n    return this.authService.currentUser$.pipe(map(user => {\n      const userData = {\n        user,\n        userCounts: {\n          cart: 0,\n          wishlist: 0,\n          total: 0\n        }\n      };\n      this.updateState(userData);\n      this.setLoading(false);\n      return userData;\n    }), catchError(error => {\n      this.addError('Failed to load user data');\n      this.setLoading(false);\n      throw error;\n    }));\n  }\n  loadRecommendations(userId) {\n    this.setLoading(true);\n    return combineLatest([this.recommendationService.getSuggestedProducts(userId, 8), this.recommendationService.getTrendingProducts(undefined, 6), this.recommendationService.getCategoryRecommendations('women', 6)]).pipe(map(([suggested, trending, categories]) => {\n      const recommendations = {\n        recommendations: {\n          suggested,\n          trending,\n          categories\n        }\n      };\n      this.updateState(recommendations);\n      this.setLoading(false);\n      return recommendations;\n    }), catchError(error => {\n      this.addError('Failed to load recommendations');\n      this.setLoading(false);\n      throw error;\n    }));\n  }\n  loadAnalytics() {\n    return this.analyticsService.getAnalyticsOverview().pipe(map(analytics => {\n      const analyticsData = {\n        analytics: {\n          userBehavior: analytics.userGrowth || [],\n          searchHistory: analytics.searchTrends || [],\n          viewHistory: []\n        }\n      };\n      this.updateState(analyticsData);\n      return analyticsData;\n    }), catchError(error => {\n      this.addError('Failed to load analytics');\n      throw error;\n    }));\n  }\n  // User Actions\n  trackUserAction(action, data) {\n    const currentState = this.appState$.value;\n    // Track in analytics\n    this.analyticsService.trackUserBehavior(action, data).subscribe();\n    // Update local analytics\n    const updatedAnalytics = {\n      ...currentState.analytics,\n      userBehavior: [...currentState.analytics.userBehavior, {\n        action,\n        data,\n        timestamp: new Date()\n      }].slice(-100) // Keep last 100 actions\n    };\n    this.updateState({\n      analytics: updatedAnalytics\n    });\n  }\n  addToCart(productId, quantity = 1) {\n    this.trackUserAction('add_to_cart', {\n      productId,\n      quantity\n    });\n    return of({\n      success: true,\n      productId,\n      quantity\n    });\n  }\n  addToWishlist(productId) {\n    this.trackUserAction('add_to_wishlist', {\n      productId\n    });\n    return of({\n      success: true,\n      productId\n    });\n  }\n  removeFromCart(productId) {\n    this.trackUserAction('remove_from_cart', {\n      productId\n    });\n    return of({\n      success: true,\n      productId\n    });\n  }\n  removeFromWishlist(productId) {\n    this.trackUserAction('remove_from_wishlist', {\n      productId\n    });\n    return of({\n      success: true,\n      productId\n    });\n  }\n  // Search and Navigation\n  performSearch(query, category) {\n    this.trackUserAction('search', {\n      query,\n      category\n    });\n    // Track search in recommendations service\n    this.recommendationService.trackSearch(query, category, 0).subscribe();\n    // Update search history\n    const currentState = this.appState$.value;\n    const updatedAnalytics = {\n      ...currentState.analytics,\n      searchHistory: [{\n        query,\n        category,\n        timestamp: new Date()\n      }, ...currentState.analytics.searchHistory].slice(0, 50) // Keep last 50 searches\n    };\n    this.updateState({\n      analytics: updatedAnalytics\n    });\n  }\n  trackProductView(productId, category, duration = 0) {\n    this.trackUserAction('product_view', {\n      productId,\n      category,\n      duration\n    });\n    // Track in recommendations service\n    this.recommendationService.trackProductView(productId, category, duration).subscribe();\n    // Update view history\n    const currentState = this.appState$.value;\n    const updatedAnalytics = {\n      ...currentState.analytics,\n      viewHistory: [{\n        productId,\n        category,\n        duration,\n        timestamp: new Date()\n      }, ...currentState.analytics.viewHistory].slice(0, 100) // Keep last 100 views\n    };\n    this.updateState({\n      analytics: updatedAnalytics\n    });\n  }\n  // Private Methods\n  initializeDataFlow() {\n    // Initialize with device info\n    this.mobileService.getDeviceInfo$().subscribe(deviceInfo => {\n      this.updateState({\n        ui: {\n          isMobile: deviceInfo.isMobile,\n          isTablet: deviceInfo.isTablet,\n          isDesktop: deviceInfo.isDesktop,\n          currentBreakpoint: this.mobileService.getCurrentBreakpoint(),\n          isKeyboardOpen: false\n        }\n      });\n    });\n    // Listen to keyboard state\n    this.mobileService.getIsKeyboardOpen$().subscribe(isOpen => {\n      const currentState = this.appState$.value;\n      this.updateState({\n        ui: {\n          ...currentState.ui,\n          isKeyboardOpen: isOpen\n        }\n      });\n    });\n    // Listen to auth changes\n    this.authService.currentUser$.subscribe(user => {\n      this.updateState({\n        user\n      });\n      if (user) {\n        // Load user-specific data\n        this.loadUserData(user._id).subscribe();\n        this.loadRecommendations(user._id).subscribe();\n      } else {\n        // Reset user-specific data\n        this.updateState({\n          userCounts: {\n            cart: 0,\n            wishlist: 0,\n            total: 0\n          },\n          recommendations: {\n            suggested: [],\n            trending: [],\n            categories: []\n          }\n        });\n      }\n    });\n  }\n  updateUserCounts() {\n    const userCounts = {\n      cart: 0,\n      wishlist: 0,\n      total: 0\n    };\n    this.updateState({\n      userCounts\n    });\n    return of(userCounts);\n  }\n  updateState(partialState) {\n    const currentState = this.appState$.value;\n    const newState = {\n      ...currentState,\n      ...partialState\n    };\n    this.appState$.next(newState);\n  }\n  setLoading(loading) {\n    this.isLoading$.next(loading);\n  }\n  addError(error) {\n    const currentErrors = this.errors$.value;\n    this.errors$.next([...currentErrors, error]);\n    // Auto-remove error after 5 seconds\n    setTimeout(() => {\n      const errors = this.errors$.value;\n      const index = errors.indexOf(error);\n      if (index > -1) {\n        errors.splice(index, 1);\n        this.errors$.next([...errors]);\n      }\n    }, 5000);\n  }\n  getInitialState() {\n    return {\n      user: null,\n      deviceInfo: null,\n      recommendations: {\n        suggested: [],\n        trending: [],\n        categories: []\n      },\n      userCounts: {\n        cart: 0,\n        wishlist: 0,\n        total: 0\n      },\n      analytics: {\n        userBehavior: [],\n        searchHistory: [],\n        viewHistory: []\n      },\n      ui: {\n        isMobile: false,\n        isTablet: false,\n        isDesktop: true,\n        currentBreakpoint: 'lg',\n        isKeyboardOpen: false\n      }\n    };\n  }\n  // Cleanup\n  destroy() {\n    this.appState$.complete();\n    this.isLoading$.complete();\n    this.errors$.complete();\n  }\n  static {\n    this.ɵfac = function DataFlowService_Factory(t) {\n      return new (t || DataFlowService)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.RecommendationService), i0.ɵɵinject(i3.AnalyticsService), i0.ɵɵinject(i4.CartService), i0.ɵɵinject(i5.WishlistService), i0.ɵɵinject(i6.MobileOptimizationService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DataFlowService,\n      factory: DataFlowService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "combineLatest", "of", "map", "distinctUntilChanged", "catchError", "DataFlowService", "constructor", "authService", "recommendationService", "analyticsService", "cartService", "wishlistService", "mobileService", "appState$", "getInitialState", "isLoading$", "errors$", "initializeDataFlow", "getAppState$", "asObservable", "getIsLoading$", "getErrors$", "getUser$", "pipe", "state", "user", "getRecommendations$", "recommendations", "getUserCounts$", "userCounts", "getUIState$", "ui", "loadUserData", "userId", "setLoading", "currentUser$", "userData", "cart", "wishlist", "total", "updateState", "error", "addError", "loadRecommendations", "getSuggestedProducts", "getTrendingProducts", "undefined", "getCategoryRecommendations", "suggested", "trending", "categories", "loadAnalytics", "getAnalyticsOverview", "analytics", "analyticsData", "userBeh<PERSON>or", "userGrowth", "searchHistory", "searchTrends", "viewHistory", "trackUserAction", "action", "data", "currentState", "value", "trackUserBehavior", "subscribe", "updatedAnalytics", "timestamp", "Date", "slice", "addToCart", "productId", "quantity", "success", "addToWishlist", "removeFromCart", "removeFromWishlist", "performSearch", "query", "category", "trackSearch", "trackProductView", "duration", "getDeviceInfo$", "deviceInfo", "isMobile", "isTablet", "isDesktop", "currentBreakpoint", "getCurrentBreakpoint", "isKeyboardOpen", "getIsKeyboardOpen$", "isOpen", "_id", "updateUserCounts", "partialState", "newState", "next", "loading", "currentErrors", "setTimeout", "errors", "index", "indexOf", "splice", "destroy", "complete", "i0", "ɵɵinject", "i1", "AuthService", "i2", "RecommendationService", "i3", "AnalyticsService", "i4", "CartService", "i5", "WishlistService", "i6", "MobileOptimizationService", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\core\\services\\data-flow.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable, combineLatest, merge, of } from 'rxjs';\nimport { map, distinctUntilChanged, shareReplay, switchMap, catchError } from 'rxjs/operators';\n\nimport { AuthService } from './auth.service';\nimport { RecommendationService } from './recommendation.service';\nimport { AnalyticsService } from './analytics.service';\nimport { CartService } from './cart.service';\nimport { WishlistService } from './wishlist.service';\nimport { MobileOptimizationService } from './mobile-optimization.service';\n\nexport interface AppState {\n  user: any;\n  deviceInfo: any;\n  recommendations: {\n    suggested: any[];\n    trending: any[];\n    categories: any[];\n  };\n  userCounts: {\n    cart: number;\n    wishlist: number;\n    total: number;\n  };\n  analytics: {\n    userBehavior: any[];\n    searchHistory: any[];\n    viewHistory: any[];\n  };\n  ui: {\n    isMobile: boolean;\n    isTablet: boolean;\n    isDesktop: boolean;\n    currentBreakpoint: string;\n    isKeyboardOpen: boolean;\n  };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class DataFlowService {\n  private appState$ = new BehaviorSubject<AppState>(this.getInitialState());\n  private isLoading$ = new BehaviorSubject<boolean>(false);\n  private errors$ = new BehaviorSubject<string[]>([]);\n\n  constructor(\n    private authService: AuthService,\n    private recommendationService: RecommendationService,\n    private analyticsService: AnalyticsService,\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private mobileService: MobileOptimizationService\n  ) {\n    this.initializeDataFlow();\n  }\n\n  // Public Observables\n  getAppState$(): Observable<AppState> {\n    return this.appState$.asObservable();\n  }\n\n  getIsLoading$(): Observable<boolean> {\n    return this.isLoading$.asObservable();\n  }\n\n  getErrors$(): Observable<string[]> {\n    return this.errors$.asObservable();\n  }\n\n  // Specific State Selectors\n  getUser$(): Observable<any> {\n    return this.appState$.pipe(\n      map(state => state.user),\n      distinctUntilChanged()\n    );\n  }\n\n  getRecommendations$(): Observable<any> {\n    return this.appState$.pipe(\n      map(state => state.recommendations),\n      distinctUntilChanged()\n    );\n  }\n\n  getUserCounts$(): Observable<any> {\n    return this.appState$.pipe(\n      map(state => state.userCounts),\n      distinctUntilChanged()\n    );\n  }\n\n  getUIState$(): Observable<any> {\n    return this.appState$.pipe(\n      map(state => state.ui),\n      distinctUntilChanged()\n    );\n  }\n\n  // Data Loading Methods\n  loadUserData(userId?: string): Observable<any> {\n    this.setLoading(true);\n\n    return this.authService.currentUser$.pipe(\n      map(user => {\n        const userData = {\n          user,\n          userCounts: {\n            cart: 0,\n            wishlist: 0,\n            total: 0\n          }\n        };\n\n        this.updateState(userData);\n        this.setLoading(false);\n        return userData;\n      }),\n      catchError(error => {\n        this.addError('Failed to load user data');\n        this.setLoading(false);\n        throw error;\n      })\n    );\n  }\n\n  loadRecommendations(userId?: string): Observable<any> {\n    this.setLoading(true);\n    \n    return combineLatest([\n      this.recommendationService.getSuggestedProducts(userId, 8),\n      this.recommendationService.getTrendingProducts(undefined, 6),\n      this.recommendationService.getCategoryRecommendations('women', 6)\n    ]).pipe(\n      map(([suggested, trending, categories]) => {\n        const recommendations = {\n          recommendations: {\n            suggested,\n            trending,\n            categories\n          }\n        };\n        \n        this.updateState(recommendations);\n        this.setLoading(false);\n        return recommendations;\n      }),\n      catchError(error => {\n        this.addError('Failed to load recommendations');\n        this.setLoading(false);\n        throw error;\n      })\n    );\n  }\n\n  loadAnalytics(): Observable<any> {\n    return this.analyticsService.getAnalyticsOverview().pipe(\n      map(analytics => {\n        const analyticsData = {\n          analytics: {\n            userBehavior: analytics.userGrowth || [],\n            searchHistory: analytics.searchTrends || [],\n            viewHistory: []\n          }\n        };\n        \n        this.updateState(analyticsData);\n        return analyticsData;\n      }),\n      catchError(error => {\n        this.addError('Failed to load analytics');\n        throw error;\n      })\n    );\n  }\n\n  // User Actions\n  trackUserAction(action: string, data: any): void {\n    const currentState = this.appState$.value;\n    \n    // Track in analytics\n    this.analyticsService.trackUserBehavior(action, data).subscribe();\n    \n    // Update local analytics\n    const updatedAnalytics = {\n      ...currentState.analytics,\n      userBehavior: [\n        ...currentState.analytics.userBehavior,\n        { action, data, timestamp: new Date() }\n      ].slice(-100) // Keep last 100 actions\n    };\n    \n    this.updateState({ analytics: updatedAnalytics });\n  }\n\n  addToCart(productId: string, quantity: number = 1): Observable<any> {\n    this.trackUserAction('add_to_cart', { productId, quantity });\n    return of({ success: true, productId, quantity });\n  }\n\n  addToWishlist(productId: string): Observable<any> {\n    this.trackUserAction('add_to_wishlist', { productId });\n    return of({ success: true, productId });\n  }\n\n  removeFromCart(productId: string): Observable<any> {\n    this.trackUserAction('remove_from_cart', { productId });\n    return of({ success: true, productId });\n  }\n\n  removeFromWishlist(productId: string): Observable<any> {\n    this.trackUserAction('remove_from_wishlist', { productId });\n    return of({ success: true, productId });\n  }\n\n  // Search and Navigation\n  performSearch(query: string, category?: string): void {\n    this.trackUserAction('search', { query, category });\n    \n    // Track search in recommendations service\n    this.recommendationService.trackSearch(query, category, 0).subscribe();\n    \n    // Update search history\n    const currentState = this.appState$.value;\n    const updatedAnalytics = {\n      ...currentState.analytics,\n      searchHistory: [\n        { query, category, timestamp: new Date() },\n        ...currentState.analytics.searchHistory\n      ].slice(0, 50) // Keep last 50 searches\n    };\n    \n    this.updateState({ analytics: updatedAnalytics });\n  }\n\n  trackProductView(productId: string, category: string, duration: number = 0): void {\n    this.trackUserAction('product_view', { productId, category, duration });\n    \n    // Track in recommendations service\n    this.recommendationService.trackProductView(productId, category, duration).subscribe();\n    \n    // Update view history\n    const currentState = this.appState$.value;\n    const updatedAnalytics = {\n      ...currentState.analytics,\n      viewHistory: [\n        { productId, category, duration, timestamp: new Date() },\n        ...currentState.analytics.viewHistory\n      ].slice(0, 100) // Keep last 100 views\n    };\n    \n    this.updateState({ analytics: updatedAnalytics });\n  }\n\n  // Private Methods\n  private initializeDataFlow(): void {\n    // Initialize with device info\n    this.mobileService.getDeviceInfo$().subscribe(deviceInfo => {\n      this.updateState({\n        ui: {\n          isMobile: deviceInfo.isMobile,\n          isTablet: deviceInfo.isTablet,\n          isDesktop: deviceInfo.isDesktop,\n          currentBreakpoint: this.mobileService.getCurrentBreakpoint(),\n          isKeyboardOpen: false\n        }\n      });\n    });\n\n    // Listen to keyboard state\n    this.mobileService.getIsKeyboardOpen$().subscribe(isOpen => {\n      const currentState = this.appState$.value;\n      this.updateState({\n        ui: {\n          ...currentState.ui,\n          isKeyboardOpen: isOpen\n        }\n      });\n    });\n\n    // Listen to auth changes\n    this.authService.currentUser$.subscribe(user => {\n      this.updateState({ user });\n      \n      if (user) {\n        // Load user-specific data\n        this.loadUserData(user._id).subscribe();\n        this.loadRecommendations(user._id).subscribe();\n      } else {\n        // Reset user-specific data\n        this.updateState({\n          userCounts: { cart: 0, wishlist: 0, total: 0 },\n          recommendations: { suggested: [], trending: [], categories: [] }\n        });\n      }\n    });\n  }\n\n  private updateUserCounts(): Observable<any> {\n    const userCounts = {\n      cart: 0,\n      wishlist: 0,\n      total: 0\n    };\n\n    this.updateState({ userCounts });\n    return of(userCounts);\n  }\n\n  private updateState(partialState: Partial<AppState>): void {\n    const currentState = this.appState$.value;\n    const newState = { ...currentState, ...partialState };\n    this.appState$.next(newState);\n  }\n\n  private setLoading(loading: boolean): void {\n    this.isLoading$.next(loading);\n  }\n\n  private addError(error: string): void {\n    const currentErrors = this.errors$.value;\n    this.errors$.next([...currentErrors, error]);\n    \n    // Auto-remove error after 5 seconds\n    setTimeout(() => {\n      const errors = this.errors$.value;\n      const index = errors.indexOf(error);\n      if (index > -1) {\n        errors.splice(index, 1);\n        this.errors$.next([...errors]);\n      }\n    }, 5000);\n  }\n\n  private getInitialState(): AppState {\n    return {\n      user: null,\n      deviceInfo: null,\n      recommendations: {\n        suggested: [],\n        trending: [],\n        categories: []\n      },\n      userCounts: {\n        cart: 0,\n        wishlist: 0,\n        total: 0\n      },\n      analytics: {\n        userBehavior: [],\n        searchHistory: [],\n        viewHistory: []\n      },\n      ui: {\n        isMobile: false,\n        isTablet: false,\n        isDesktop: true,\n        currentBreakpoint: 'lg',\n        isKeyboardOpen: false\n      }\n    };\n  }\n\n  // Cleanup\n  destroy(): void {\n    this.appState$.complete();\n    this.isLoading$.complete();\n    this.errors$.complete();\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,EAAcC,aAAa,EAASC,EAAE,QAAQ,MAAM;AAC5E,SAASC,GAAG,EAAEC,oBAAoB,EAA0BC,UAAU,QAAQ,gBAAgB;;;;;;;;AAuC9F,OAAM,MAAOC,eAAe;EAK1BC,YACUC,WAAwB,EACxBC,qBAA4C,EAC5CC,gBAAkC,EAClCC,WAAwB,EACxBC,eAAgC,EAChCC,aAAwC;IALxC,KAAAL,WAAW,GAAXA,WAAW;IACX,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IAVf,KAAAC,SAAS,GAAG,IAAId,eAAe,CAAW,IAAI,CAACe,eAAe,EAAE,CAAC;IACjE,KAAAC,UAAU,GAAG,IAAIhB,eAAe,CAAU,KAAK,CAAC;IAChD,KAAAiB,OAAO,GAAG,IAAIjB,eAAe,CAAW,EAAE,CAAC;IAUjD,IAAI,CAACkB,kBAAkB,EAAE;EAC3B;EAEA;EACAC,YAAYA,CAAA;IACV,OAAO,IAAI,CAACL,SAAS,CAACM,YAAY,EAAE;EACtC;EAEAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACL,UAAU,CAACI,YAAY,EAAE;EACvC;EAEAE,UAAUA,CAAA;IACR,OAAO,IAAI,CAACL,OAAO,CAACG,YAAY,EAAE;EACpC;EAEA;EACAG,QAAQA,CAAA;IACN,OAAO,IAAI,CAACT,SAAS,CAACU,IAAI,CACxBrB,GAAG,CAACsB,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,EACxBtB,oBAAoB,EAAE,CACvB;EACH;EAEAuB,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACb,SAAS,CAACU,IAAI,CACxBrB,GAAG,CAACsB,KAAK,IAAIA,KAAK,CAACG,eAAe,CAAC,EACnCxB,oBAAoB,EAAE,CACvB;EACH;EAEAyB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACf,SAAS,CAACU,IAAI,CACxBrB,GAAG,CAACsB,KAAK,IAAIA,KAAK,CAACK,UAAU,CAAC,EAC9B1B,oBAAoB,EAAE,CACvB;EACH;EAEA2B,WAAWA,CAAA;IACT,OAAO,IAAI,CAACjB,SAAS,CAACU,IAAI,CACxBrB,GAAG,CAACsB,KAAK,IAAIA,KAAK,CAACO,EAAE,CAAC,EACtB5B,oBAAoB,EAAE,CACvB;EACH;EAEA;EACA6B,YAAYA,CAACC,MAAe;IAC1B,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC;IAErB,OAAO,IAAI,CAAC3B,WAAW,CAAC4B,YAAY,CAACZ,IAAI,CACvCrB,GAAG,CAACuB,IAAI,IAAG;MACT,MAAMW,QAAQ,GAAG;QACfX,IAAI;QACJI,UAAU,EAAE;UACVQ,IAAI,EAAE,CAAC;UACPC,QAAQ,EAAE,CAAC;UACXC,KAAK,EAAE;;OAEV;MAED,IAAI,CAACC,WAAW,CAACJ,QAAQ,CAAC;MAC1B,IAAI,CAACF,UAAU,CAAC,KAAK,CAAC;MACtB,OAAOE,QAAQ;IACjB,CAAC,CAAC,EACFhC,UAAU,CAACqC,KAAK,IAAG;MACjB,IAAI,CAACC,QAAQ,CAAC,0BAA0B,CAAC;MACzC,IAAI,CAACR,UAAU,CAAC,KAAK,CAAC;MACtB,MAAMO,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEAE,mBAAmBA,CAACV,MAAe;IACjC,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC;IAErB,OAAOlC,aAAa,CAAC,CACnB,IAAI,CAACQ,qBAAqB,CAACoC,oBAAoB,CAACX,MAAM,EAAE,CAAC,CAAC,EAC1D,IAAI,CAACzB,qBAAqB,CAACqC,mBAAmB,CAACC,SAAS,EAAE,CAAC,CAAC,EAC5D,IAAI,CAACtC,qBAAqB,CAACuC,0BAA0B,CAAC,OAAO,EAAE,CAAC,CAAC,CAClE,CAAC,CAACxB,IAAI,CACLrB,GAAG,CAAC,CAAC,CAAC8C,SAAS,EAAEC,QAAQ,EAAEC,UAAU,CAAC,KAAI;MACxC,MAAMvB,eAAe,GAAG;QACtBA,eAAe,EAAE;UACfqB,SAAS;UACTC,QAAQ;UACRC;;OAEH;MAED,IAAI,CAACV,WAAW,CAACb,eAAe,CAAC;MACjC,IAAI,CAACO,UAAU,CAAC,KAAK,CAAC;MACtB,OAAOP,eAAe;IACxB,CAAC,CAAC,EACFvB,UAAU,CAACqC,KAAK,IAAG;MACjB,IAAI,CAACC,QAAQ,CAAC,gCAAgC,CAAC;MAC/C,IAAI,CAACR,UAAU,CAAC,KAAK,CAAC;MACtB,MAAMO,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEAU,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC1C,gBAAgB,CAAC2C,oBAAoB,EAAE,CAAC7B,IAAI,CACtDrB,GAAG,CAACmD,SAAS,IAAG;MACd,MAAMC,aAAa,GAAG;QACpBD,SAAS,EAAE;UACTE,YAAY,EAAEF,SAAS,CAACG,UAAU,IAAI,EAAE;UACxCC,aAAa,EAAEJ,SAAS,CAACK,YAAY,IAAI,EAAE;UAC3CC,WAAW,EAAE;;OAEhB;MAED,IAAI,CAACnB,WAAW,CAACc,aAAa,CAAC;MAC/B,OAAOA,aAAa;IACtB,CAAC,CAAC,EACFlD,UAAU,CAACqC,KAAK,IAAG;MACjB,IAAI,CAACC,QAAQ,CAAC,0BAA0B,CAAC;MACzC,MAAMD,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEA;EACAmB,eAAeA,CAACC,MAAc,EAAEC,IAAS;IACvC,MAAMC,YAAY,GAAG,IAAI,CAAClD,SAAS,CAACmD,KAAK;IAEzC;IACA,IAAI,CAACvD,gBAAgB,CAACwD,iBAAiB,CAACJ,MAAM,EAAEC,IAAI,CAAC,CAACI,SAAS,EAAE;IAEjE;IACA,MAAMC,gBAAgB,GAAG;MACvB,GAAGJ,YAAY,CAACV,SAAS;MACzBE,YAAY,EAAE,CACZ,GAAGQ,YAAY,CAACV,SAAS,CAACE,YAAY,EACtC;QAAEM,MAAM;QAAEC,IAAI;QAAEM,SAAS,EAAE,IAAIC,IAAI;MAAE,CAAE,CACxC,CAACC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;KACf;IAED,IAAI,CAAC9B,WAAW,CAAC;MAAEa,SAAS,EAAEc;IAAgB,CAAE,CAAC;EACnD;EAEAI,SAASA,CAACC,SAAiB,EAAEC,QAAA,GAAmB,CAAC;IAC/C,IAAI,CAACb,eAAe,CAAC,aAAa,EAAE;MAAEY,SAAS;MAAEC;IAAQ,CAAE,CAAC;IAC5D,OAAOxE,EAAE,CAAC;MAAEyE,OAAO,EAAE,IAAI;MAAEF,SAAS;MAAEC;IAAQ,CAAE,CAAC;EACnD;EAEAE,aAAaA,CAACH,SAAiB;IAC7B,IAAI,CAACZ,eAAe,CAAC,iBAAiB,EAAE;MAAEY;IAAS,CAAE,CAAC;IACtD,OAAOvE,EAAE,CAAC;MAAEyE,OAAO,EAAE,IAAI;MAAEF;IAAS,CAAE,CAAC;EACzC;EAEAI,cAAcA,CAACJ,SAAiB;IAC9B,IAAI,CAACZ,eAAe,CAAC,kBAAkB,EAAE;MAAEY;IAAS,CAAE,CAAC;IACvD,OAAOvE,EAAE,CAAC;MAAEyE,OAAO,EAAE,IAAI;MAAEF;IAAS,CAAE,CAAC;EACzC;EAEAK,kBAAkBA,CAACL,SAAiB;IAClC,IAAI,CAACZ,eAAe,CAAC,sBAAsB,EAAE;MAAEY;IAAS,CAAE,CAAC;IAC3D,OAAOvE,EAAE,CAAC;MAAEyE,OAAO,EAAE,IAAI;MAAEF;IAAS,CAAE,CAAC;EACzC;EAEA;EACAM,aAAaA,CAACC,KAAa,EAAEC,QAAiB;IAC5C,IAAI,CAACpB,eAAe,CAAC,QAAQ,EAAE;MAAEmB,KAAK;MAAEC;IAAQ,CAAE,CAAC;IAEnD;IACA,IAAI,CAACxE,qBAAqB,CAACyE,WAAW,CAACF,KAAK,EAAEC,QAAQ,EAAE,CAAC,CAAC,CAACd,SAAS,EAAE;IAEtE;IACA,MAAMH,YAAY,GAAG,IAAI,CAAClD,SAAS,CAACmD,KAAK;IACzC,MAAMG,gBAAgB,GAAG;MACvB,GAAGJ,YAAY,CAACV,SAAS;MACzBI,aAAa,EAAE,CACb;QAAEsB,KAAK;QAAEC,QAAQ;QAAEZ,SAAS,EAAE,IAAIC,IAAI;MAAE,CAAE,EAC1C,GAAGN,YAAY,CAACV,SAAS,CAACI,aAAa,CACxC,CAACa,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;KAChB;IAED,IAAI,CAAC9B,WAAW,CAAC;MAAEa,SAAS,EAAEc;IAAgB,CAAE,CAAC;EACnD;EAEAe,gBAAgBA,CAACV,SAAiB,EAAEQ,QAAgB,EAAEG,QAAA,GAAmB,CAAC;IACxE,IAAI,CAACvB,eAAe,CAAC,cAAc,EAAE;MAAEY,SAAS;MAAEQ,QAAQ;MAAEG;IAAQ,CAAE,CAAC;IAEvE;IACA,IAAI,CAAC3E,qBAAqB,CAAC0E,gBAAgB,CAACV,SAAS,EAAEQ,QAAQ,EAAEG,QAAQ,CAAC,CAACjB,SAAS,EAAE;IAEtF;IACA,MAAMH,YAAY,GAAG,IAAI,CAAClD,SAAS,CAACmD,KAAK;IACzC,MAAMG,gBAAgB,GAAG;MACvB,GAAGJ,YAAY,CAACV,SAAS;MACzBM,WAAW,EAAE,CACX;QAAEa,SAAS;QAAEQ,QAAQ;QAAEG,QAAQ;QAAEf,SAAS,EAAE,IAAIC,IAAI;MAAE,CAAE,EACxD,GAAGN,YAAY,CAACV,SAAS,CAACM,WAAW,CACtC,CAACW,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;KACjB;IAED,IAAI,CAAC9B,WAAW,CAAC;MAAEa,SAAS,EAAEc;IAAgB,CAAE,CAAC;EACnD;EAEA;EACQlD,kBAAkBA,CAAA;IACxB;IACA,IAAI,CAACL,aAAa,CAACwE,cAAc,EAAE,CAAClB,SAAS,CAACmB,UAAU,IAAG;MACzD,IAAI,CAAC7C,WAAW,CAAC;QACfT,EAAE,EAAE;UACFuD,QAAQ,EAAED,UAAU,CAACC,QAAQ;UAC7BC,QAAQ,EAAEF,UAAU,CAACE,QAAQ;UAC7BC,SAAS,EAAEH,UAAU,CAACG,SAAS;UAC/BC,iBAAiB,EAAE,IAAI,CAAC7E,aAAa,CAAC8E,oBAAoB,EAAE;UAC5DC,cAAc,EAAE;;OAEnB,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC/E,aAAa,CAACgF,kBAAkB,EAAE,CAAC1B,SAAS,CAAC2B,MAAM,IAAG;MACzD,MAAM9B,YAAY,GAAG,IAAI,CAAClD,SAAS,CAACmD,KAAK;MACzC,IAAI,CAACxB,WAAW,CAAC;QACfT,EAAE,EAAE;UACF,GAAGgC,YAAY,CAAChC,EAAE;UAClB4D,cAAc,EAAEE;;OAEnB,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACtF,WAAW,CAAC4B,YAAY,CAAC+B,SAAS,CAACzC,IAAI,IAAG;MAC7C,IAAI,CAACe,WAAW,CAAC;QAAEf;MAAI,CAAE,CAAC;MAE1B,IAAIA,IAAI,EAAE;QACR;QACA,IAAI,CAACO,YAAY,CAACP,IAAI,CAACqE,GAAG,CAAC,CAAC5B,SAAS,EAAE;QACvC,IAAI,CAACvB,mBAAmB,CAAClB,IAAI,CAACqE,GAAG,CAAC,CAAC5B,SAAS,EAAE;OAC/C,MAAM;QACL;QACA,IAAI,CAAC1B,WAAW,CAAC;UACfX,UAAU,EAAE;YAAEQ,IAAI,EAAE,CAAC;YAAEC,QAAQ,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAC,CAAE;UAC9CZ,eAAe,EAAE;YAAEqB,SAAS,EAAE,EAAE;YAAEC,QAAQ,EAAE,EAAE;YAAEC,UAAU,EAAE;UAAE;SAC/D,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEQ6C,gBAAgBA,CAAA;IACtB,MAAMlE,UAAU,GAAG;MACjBQ,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE;KACR;IAED,IAAI,CAACC,WAAW,CAAC;MAAEX;IAAU,CAAE,CAAC;IAChC,OAAO5B,EAAE,CAAC4B,UAAU,CAAC;EACvB;EAEQW,WAAWA,CAACwD,YAA+B;IACjD,MAAMjC,YAAY,GAAG,IAAI,CAAClD,SAAS,CAACmD,KAAK;IACzC,MAAMiC,QAAQ,GAAG;MAAE,GAAGlC,YAAY;MAAE,GAAGiC;IAAY,CAAE;IACrD,IAAI,CAACnF,SAAS,CAACqF,IAAI,CAACD,QAAQ,CAAC;EAC/B;EAEQ/D,UAAUA,CAACiE,OAAgB;IACjC,IAAI,CAACpF,UAAU,CAACmF,IAAI,CAACC,OAAO,CAAC;EAC/B;EAEQzD,QAAQA,CAACD,KAAa;IAC5B,MAAM2D,aAAa,GAAG,IAAI,CAACpF,OAAO,CAACgD,KAAK;IACxC,IAAI,CAAChD,OAAO,CAACkF,IAAI,CAAC,CAAC,GAAGE,aAAa,EAAE3D,KAAK,CAAC,CAAC;IAE5C;IACA4D,UAAU,CAAC,MAAK;MACd,MAAMC,MAAM,GAAG,IAAI,CAACtF,OAAO,CAACgD,KAAK;MACjC,MAAMuC,KAAK,GAAGD,MAAM,CAACE,OAAO,CAAC/D,KAAK,CAAC;MACnC,IAAI8D,KAAK,GAAG,CAAC,CAAC,EAAE;QACdD,MAAM,CAACG,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QACvB,IAAI,CAACvF,OAAO,CAACkF,IAAI,CAAC,CAAC,GAAGI,MAAM,CAAC,CAAC;;IAElC,CAAC,EAAE,IAAI,CAAC;EACV;EAEQxF,eAAeA,CAAA;IACrB,OAAO;MACLW,IAAI,EAAE,IAAI;MACV4D,UAAU,EAAE,IAAI;MAChB1D,eAAe,EAAE;QACfqB,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE;OACb;MACDrB,UAAU,EAAE;QACVQ,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;OACR;MACDc,SAAS,EAAE;QACTE,YAAY,EAAE,EAAE;QAChBE,aAAa,EAAE,EAAE;QACjBE,WAAW,EAAE;OACd;MACD5B,EAAE,EAAE;QACFuD,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE,KAAK;QACfC,SAAS,EAAE,IAAI;QACfC,iBAAiB,EAAE,IAAI;QACvBE,cAAc,EAAE;;KAEnB;EACH;EAEA;EACAe,OAAOA,CAAA;IACL,IAAI,CAAC7F,SAAS,CAAC8F,QAAQ,EAAE;IACzB,IAAI,CAAC5F,UAAU,CAAC4F,QAAQ,EAAE;IAC1B,IAAI,CAAC3F,OAAO,CAAC2F,QAAQ,EAAE;EACzB;;;uBAvUWtG,eAAe,EAAAuG,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,qBAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAS,EAAA,CAAAC,eAAA,GAAAX,EAAA,CAAAC,QAAA,CAAAW,EAAA,CAAAC,yBAAA;IAAA;EAAA;;;aAAfpH,eAAe;MAAAqH,OAAA,EAAfrH,eAAe,CAAAsH,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}