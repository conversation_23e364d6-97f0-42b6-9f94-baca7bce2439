.role-based-settings {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: #fafafa;
  min-height: 100vh;

  // Settings Header
  .settings-header {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

    .header-content {
      display: flex;
      align-items: center;
      gap: 1rem;

      .role-indicator {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .header-text {
        h1 {
          margin: 0;
          font-size: 2rem;
          font-weight: 700;
          color: #262626;
        }

        p {
          margin: 0.25rem 0 0 0;
          color: #8e8e8e;
          font-size: 1rem;
        }
      }
    }
  }

  // Settings Navigation
  .settings-navigation {
    background: white;
    border-radius: 16px;
    padding: 1rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

    .nav-tabs {
      display: flex;
      gap: 0.5rem;
      overflow-x: auto;
      padding-bottom: 0.5rem;

      &::-webkit-scrollbar {
        height: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
      }

      .nav-tab {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border: none;
        background: #f8f9fa;
        color: #6c757d;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
        font-weight: 500;

        &:hover {
          background: #e9ecef;
          color: #495057;
        }

        &.active {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        i {
          font-size: 0.9rem;
        }
      }
    }
  }

  // Settings Content
  .settings-content {
    position: relative;

    .settings-section {
      display: none;
      background: white;
      border-radius: 16px;
      padding: 2rem;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

      &.active {
        display: block;
      }

      .section-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #f0f0f0;

        h2 {
          margin: 0 0 0.5rem 0;
          font-size: 1.5rem;
          font-weight: 600;
          color: #262626;
        }

        p {
          margin: 0;
          color: #8e8e8e;
          font-size: 0.95rem;
        }
      }

      .settings-grid {
        display: grid;
        gap: 2rem;

        .setting-item {
          display: grid;
          grid-template-columns: 1fr auto;
          gap: 1rem;
          align-items: start;
          padding: 1.5rem;
          border: 1px solid #f0f0f0;
          border-radius: 12px;
          transition: all 0.3s ease;

          &:hover {
            border-color: #e0e0e0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
          }

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
            gap: 1rem;
          }

          .setting-info {
            .setting-label {
              display: block;
              font-weight: 600;
              color: #262626;
              margin-bottom: 0.25rem;
              font-size: 0.95rem;
            }

            .setting-description {
              margin: 0;
              color: #8e8e8e;
              font-size: 0.85rem;
              line-height: 1.4;
            }
          }

          .setting-control {
            min-width: 200px;

            @media (max-width: 768px) {
              min-width: auto;
              width: 100%;
            }
          }
        }
      }
    }
  }

  // Control Styles
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;

    .toggle-input {
      opacity: 0;
      width: 0;
      height: 0;

      &:checked + .toggle-label {
        background-color: #4CAF50;

        &:before {
          transform: translateX(26px);
        }
      }
    }

    .toggle-label {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      border-radius: 24px;
      transition: 0.3s;

      &:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        border-radius: 50%;
        transition: 0.3s;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
    }
  }

  .select-input,
  .text-input,
  .textarea-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;

    &:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }

  .range-container {
    display: flex;
    align-items: center;
    gap: 1rem;

    .range-input {
      flex: 1;
      height: 6px;
      border-radius: 3px;
      background: #ddd;
      outline: none;
      -webkit-appearance: none;

      &::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: #667eea;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      &::-moz-range-thumb {
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: #667eea;
        cursor: pointer;
        border: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
    }

    .range-value {
      min-width: 40px;
      text-align: center;
      font-weight: 600;
      color: #667eea;
      background: #f8f9ff;
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
      font-size: 0.85rem;
    }
  }

  .color-input {
    width: 50px;
    height: 40px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &::-webkit-color-swatch-wrapper {
      padding: 0;
    }

    &::-webkit-color-swatch {
      border: none;
      border-radius: 8px;
    }
  }

  .file-upload {
    display: flex;
    align-items: center;
    gap: 1rem;

    .file-label {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1rem;
      background: #f8f9fa;
      border: 1px dashed #ddd;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 0.9rem;
      color: #6c757d;

      &:hover {
        background: #e9ecef;
        border-color: #adb5bd;
      }

      i {
        font-size: 0.8rem;
      }
    }

    .file-name {
      font-size: 0.85rem;
      color: #28a745;
      font-weight: 500;
    }
  }

  // Settings Actions
  .settings-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

    @media (max-width: 768px) {
      flex-direction: column;
    }

    .btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 0.9rem;

      &.btn-secondary {
        background: #f8f9fa;
        color: #6c757d;
        border: 1px solid #dee2e6;

        &:hover {
          background: #e9ecef;
          color: #495057;
        }
      }

      &.btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }
      }

      i {
        font-size: 0.8rem;
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .role-based-settings {
    background: #121212;

    .settings-header,
    .settings-navigation,
    .settings-section,
    .settings-actions {
      background: #1e1e1e;
      color: #ffffff;
    }

    .section-header h2,
    .setting-label {
      color: #ffffff;
    }

    .section-header p,
    .setting-description {
      color: #b3b3b3;
    }

    .setting-item {
      border-color: #333;

      &:hover {
        border-color: #444;
      }
    }

    .nav-tab {
      background: #2a2a2a;
      color: #b3b3b3;

      &:hover {
        background: #333;
        color: #ffffff;
      }
    }

    .select-input,
    .text-input,
    .textarea-input {
      background: #2a2a2a;
      border-color: #444;
      color: #ffffff;

      &:focus {
        border-color: #667eea;
      }
    }

    .file-label {
      background: #2a2a2a;
      border-color: #444;
      color: #b3b3b3;

      &:hover {
        background: #333;
      }
    }
  }
}
