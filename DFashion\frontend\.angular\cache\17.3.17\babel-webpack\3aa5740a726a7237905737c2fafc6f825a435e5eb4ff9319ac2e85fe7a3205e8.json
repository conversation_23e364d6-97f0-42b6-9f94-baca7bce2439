{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction NoDataComponent_div_8_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function NoDataComponent_div_8_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPrimaryAction());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.primaryAction, \" \");\n  }\n}\nfunction NoDataComponent_div_8_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function NoDataComponent_div_8_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSecondaryAction());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.secondaryAction, \" \");\n  }\n}\nfunction NoDataComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtemplate(1, NoDataComponent_div_8_button_1_Template, 2, 1, \"button\", 8)(2, NoDataComponent_div_8_button_2_Template, 2, 1, \"button\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.primaryAction);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.secondaryAction);\n  }\n}\nfunction NoDataComponent_div_9_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 14);\n    i0.ɵɵlistener(\"click\", function NoDataComponent_div_9_li_4_Template_li_click_0_listener() {\n      const suggestion_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSuggestionClick(suggestion_r5));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const suggestion_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", suggestion_r5, \" \");\n  }\n}\nfunction NoDataComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"h4\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, NoDataComponent_div_9_li_4_Template, 2, 1, \"li\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.suggestionsTitle || \"Suggestions:\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.suggestions);\n  }\n}\nexport class NoDataComponent {\n  constructor() {\n    this.title = 'No Data Available';\n    this.message = 'There is no data to display at the moment.';\n    this.iconClass = 'fas fa-inbox';\n    this.containerClass = '';\n    this.showActions = false;\n  }\n  onPrimaryAction() {\n    // Emit event or handle primary action\n    console.log('Primary action clicked');\n  }\n  onSecondaryAction() {\n    // Emit event or handle secondary action\n    console.log('Secondary action clicked');\n  }\n  onSuggestionClick(suggestion) {\n    // Emit event or handle suggestion click\n    console.log('Suggestion clicked:', suggestion);\n  }\n  static {\n    this.ɵfac = function NoDataComponent_Factory(t) {\n      return new (t || NoDataComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NoDataComponent,\n      selectors: [[\"app-no-data\"]],\n      inputs: {\n        title: \"title\",\n        message: \"message\",\n        iconClass: \"iconClass\",\n        containerClass: \"containerClass\",\n        showActions: \"showActions\",\n        primaryAction: \"primaryAction\",\n        secondaryAction: \"secondaryAction\",\n        suggestions: \"suggestions\",\n        suggestionsTitle: \"suggestionsTitle\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 10,\n      vars: 8,\n      consts: [[1, \"no-data-container\"], [1, \"no-data-content\"], [1, \"no-data-icon\"], [1, \"no-data-title\"], [1, \"no-data-message\"], [\"class\", \"no-data-actions\", 4, \"ngIf\"], [\"class\", \"no-data-suggestions\", 4, \"ngIf\"], [1, \"no-data-actions\"], [\"class\", \"btn-primary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn-secondary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn-primary\", 3, \"click\"], [1, \"btn-secondary\", 3, \"click\"], [1, \"no-data-suggestions\"], [3, \"click\", 4, \"ngFor\", \"ngForOf\"], [3, \"click\"]],\n      template: function NoDataComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"i\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"h3\", 3);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 4);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, NoDataComponent_div_8_Template, 3, 2, \"div\", 5)(9, NoDataComponent_div_9_Template, 5, 2, \"div\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.containerClass);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassMap(ctx.iconClass);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.title);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.message);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showActions);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.suggestions && ctx.suggestions.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf],\n      styles: [\".no-data-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-height: 200px;\\n  padding: 2rem;\\n  text-align: center;\\n}\\n\\n.no-data-container.full-height[_ngcontent-%COMP%] {\\n  min-height: 400px;\\n}\\n\\n.no-data-container.compact[_ngcontent-%COMP%] {\\n  min-height: 150px;\\n  padding: 1rem;\\n}\\n\\n.no-data-content[_ngcontent-%COMP%] {\\n  max-width: 400px;\\n  width: 100%;\\n}\\n\\n.no-data-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.no-data-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  color: #dee2e6;\\n  opacity: 0.8;\\n}\\n\\n.no-data-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: #495057;\\n  margin: 0 0 0.75rem 0;\\n}\\n\\n.no-data-message[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #6c757d;\\n  margin: 0 0 1.5rem 0;\\n  line-height: 1.5;\\n}\\n\\n.no-data-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: center;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1.5rem;\\n  border-radius: 6px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  border: none;\\n  transition: all 0.2s ease;\\n  font-size: 0.9rem;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #6c757d;\\n  border: 1px solid #dee2e6;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n\\n.no-data-suggestions[_ngcontent-%COMP%] {\\n  text-align: left;\\n}\\n\\n.no-data-suggestions[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #495057;\\n  margin: 0 0 0.75rem 0;\\n}\\n\\n.no-data-suggestions[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n\\n.no-data-suggestions[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0.75rem;\\n  background: #f8f9fa;\\n  border-radius: 4px;\\n  margin-bottom: 0.5rem;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  color: #495057;\\n}\\n\\n.no-data-suggestions[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #007bff;\\n}\\n\\n.no-data-suggestions[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n@media (max-width: 768px) {\\n  .no-data-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .no-data-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 3rem;\\n  }\\n  .no-data-title[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n  .no-data-message[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .no-data-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n  .btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 200px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementStart", "ɵɵlistener", "NoDataComponent_div_8_button_1_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onPrimaryAction", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "primaryAction", "NoDataComponent_div_8_button_2_Template_button_click_0_listener", "_r3", "onSecondaryAction", "secondaryAction", "ɵɵtemplate", "NoDataComponent_div_8_button_1_Template", "NoDataComponent_div_8_button_2_Template", "ɵɵproperty", "NoDataComponent_div_9_li_4_Template_li_click_0_listener", "suggestion_r5", "_r4", "$implicit", "onSuggestionClick", "NoDataComponent_div_9_li_4_Template", "ɵɵtextInterpolate", "<PERSON><PERSON><PERSON>le", "suggestions", "NoDataComponent", "constructor", "title", "message", "iconClass", "containerClass", "showActions", "console", "log", "suggestion", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NoDataComponent_Template", "rf", "ctx", "ɵɵelement", "NoDataComponent_div_8_Template", "NoDataComponent_div_9_Template", "ɵɵclassMap", "length", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\shared\\components\\no-data\\no-data.component.ts"], "sourcesContent": ["import { Component, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-no-data',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"no-data-container\" [class]=\"containerClass\">\n      <div class=\"no-data-content\">\n        <div class=\"no-data-icon\">\n          <i [class]=\"iconClass\"></i>\n        </div>\n        \n        <h3 class=\"no-data-title\">{{ title }}</h3>\n        <p class=\"no-data-message\">{{ message }}</p>\n        \n        <div class=\"no-data-actions\" *ngIf=\"showActions\">\n          <button \n            *ngIf=\"primaryAction\" \n            class=\"btn-primary\"\n            (click)=\"onPrimaryAction()\"\n          >\n            {{ primaryAction }}\n          </button>\n          \n          <button \n            *ngIf=\"secondaryAction\" \n            class=\"btn-secondary\"\n            (click)=\"onSecondaryAction()\"\n          >\n            {{ secondaryAction }}\n          </button>\n        </div>\n        \n        <div class=\"no-data-suggestions\" *ngIf=\"suggestions && suggestions.length > 0\">\n          <h4>{{ suggestionsTitle || 'Suggestions:' }}</h4>\n          <ul>\n            <li *ngFor=\"let suggestion of suggestions\" (click)=\"onSuggestionClick(suggestion)\">\n              {{ suggestion }}\n            </li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .no-data-container {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      min-height: 200px;\n      padding: 2rem;\n      text-align: center;\n    }\n\n    .no-data-container.full-height {\n      min-height: 400px;\n    }\n\n    .no-data-container.compact {\n      min-height: 150px;\n      padding: 1rem;\n    }\n\n    .no-data-content {\n      max-width: 400px;\n      width: 100%;\n    }\n\n    .no-data-icon {\n      margin-bottom: 1.5rem;\n    }\n\n    .no-data-icon i {\n      font-size: 4rem;\n      color: #dee2e6;\n      opacity: 0.8;\n    }\n\n    .no-data-title {\n      font-size: 1.5rem;\n      font-weight: 600;\n      color: #495057;\n      margin: 0 0 0.75rem 0;\n    }\n\n    .no-data-message {\n      font-size: 1rem;\n      color: #6c757d;\n      margin: 0 0 1.5rem 0;\n      line-height: 1.5;\n    }\n\n    .no-data-actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: center;\n      margin-bottom: 1.5rem;\n    }\n\n    .btn-primary, .btn-secondary {\n      padding: 0.75rem 1.5rem;\n      border-radius: 6px;\n      font-weight: 500;\n      cursor: pointer;\n      border: none;\n      transition: all 0.2s ease;\n      font-size: 0.9rem;\n    }\n\n    .btn-primary {\n      background: #007bff;\n      color: white;\n    }\n\n    .btn-primary:hover {\n      background: #0056b3;\n    }\n\n    .btn-secondary {\n      background: #f8f9fa;\n      color: #6c757d;\n      border: 1px solid #dee2e6;\n    }\n\n    .btn-secondary:hover {\n      background: #e9ecef;\n      color: #495057;\n    }\n\n    .no-data-suggestions {\n      text-align: left;\n    }\n\n    .no-data-suggestions h4 {\n      font-size: 1rem;\n      font-weight: 600;\n      color: #495057;\n      margin: 0 0 0.75rem 0;\n    }\n\n    .no-data-suggestions ul {\n      list-style: none;\n      padding: 0;\n      margin: 0;\n    }\n\n    .no-data-suggestions li {\n      padding: 0.5rem 0.75rem;\n      background: #f8f9fa;\n      border-radius: 4px;\n      margin-bottom: 0.5rem;\n      cursor: pointer;\n      transition: all 0.2s ease;\n      color: #495057;\n    }\n\n    .no-data-suggestions li:hover {\n      background: #e9ecef;\n      color: #007bff;\n    }\n\n    .no-data-suggestions li:last-child {\n      margin-bottom: 0;\n    }\n\n    @media (max-width: 768px) {\n      .no-data-container {\n        padding: 1rem;\n      }\n\n      .no-data-icon i {\n        font-size: 3rem;\n      }\n\n      .no-data-title {\n        font-size: 1.25rem;\n      }\n\n      .no-data-message {\n        font-size: 0.9rem;\n      }\n\n      .no-data-actions {\n        flex-direction: column;\n        align-items: center;\n      }\n\n      .btn-primary, .btn-secondary {\n        width: 100%;\n        max-width: 200px;\n      }\n    }\n  `]\n})\nexport class NoDataComponent {\n  @Input() title: string = 'No Data Available';\n  @Input() message: string = 'There is no data to display at the moment.';\n  @Input() iconClass: string = 'fas fa-inbox';\n  @Input() containerClass: string = '';\n  @Input() showActions: boolean = false;\n  @Input() primaryAction?: string;\n  @Input() secondaryAction?: string;\n  @Input() suggestions?: string[];\n  @Input() suggestionsTitle?: string;\n\n  onPrimaryAction() {\n    // Emit event or handle primary action\n    console.log('Primary action clicked');\n  }\n\n  onSecondaryAction() {\n    // Emit event or handle secondary action\n    console.log('Secondary action clicked');\n  }\n\n  onSuggestionClick(suggestion: string) {\n    // Emit event or handle suggestion click\n    console.log('Suggestion clicked:', suggestion);\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;;IAiBpCC,EAAA,CAAAC,cAAA,iBAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IAE3BT,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;IADPX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,MACF;;;;;;IAEAd,EAAA,CAAAC,cAAA,iBAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAa,gEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAW,iBAAA,EAAmB;IAAA,EAAC;IAE7BjB,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;IADPX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAY,eAAA,MACF;;;;;IAfFlB,EAAA,CAAAC,cAAA,aAAiD;IAS/CD,EARA,CAAAmB,UAAA,IAAAC,uCAAA,oBAIC,IAAAC,uCAAA,oBAQA;IAGHrB,EAAA,CAAAW,YAAA,EAAM;;;;IAdDX,EAAA,CAAAY,SAAA,EAAmB;IAAnBZ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAQ,aAAA,CAAmB;IAQnBd,EAAA,CAAAY,SAAA,EAAqB;IAArBZ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAY,eAAA,CAAqB;;;;;;IAWtBlB,EAAA,CAAAC,cAAA,aAAmF;IAAxCD,EAAA,CAAAE,UAAA,mBAAAqB,wDAAA;MAAA,MAAAC,aAAA,GAAAxB,EAAA,CAAAI,aAAA,CAAAqB,GAAA,EAAAC,SAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqB,iBAAA,CAAAH,aAAA,CAA6B;IAAA,EAAC;IAChFxB,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IADHX,EAAA,CAAAY,SAAA,EACF;IADEZ,EAAA,CAAAa,kBAAA,MAAAW,aAAA,MACF;;;;;IAJFxB,EADF,CAAAC,cAAA,cAA+E,SACzE;IAAAD,EAAA,CAAAU,MAAA,GAAwC;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACjDX,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAmB,UAAA,IAAAS,mCAAA,iBAAmF;IAIvF5B,EADE,CAAAW,YAAA,EAAK,EACD;;;;IANAX,EAAA,CAAAY,SAAA,GAAwC;IAAxCZ,EAAA,CAAA6B,iBAAA,CAAAvB,MAAA,CAAAwB,gBAAA,mBAAwC;IAEf9B,EAAA,CAAAY,SAAA,GAAc;IAAdZ,EAAA,CAAAsB,UAAA,YAAAhB,MAAA,CAAAyB,WAAA,CAAc;;;AA8JrD,OAAM,MAAOC,eAAe;EAjM5BC,YAAA;IAkMW,KAAAC,KAAK,GAAW,mBAAmB;IACnC,KAAAC,OAAO,GAAW,4CAA4C;IAC9D,KAAAC,SAAS,GAAW,cAAc;IAClC,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,WAAW,GAAY,KAAK;;EAMrC7B,eAAeA,CAAA;IACb;IACA8B,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;EACvC;EAEAvB,iBAAiBA,CAAA;IACf;IACAsB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACzC;EAEAb,iBAAiBA,CAACc,UAAkB;IAClC;IACAF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEC,UAAU,CAAC;EAChD;;;uBAxBWT,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAU,SAAA;MAAAC,MAAA;QAAAT,KAAA;QAAAC,OAAA;QAAAC,SAAA;QAAAC,cAAA;QAAAC,WAAA;QAAAxB,aAAA;QAAAI,eAAA;QAAAa,WAAA;QAAAD,gBAAA;MAAA;MAAAc,UAAA;MAAAC,QAAA,GAAA7C,EAAA,CAAA8C,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1LpBpD,EAFJ,CAAAC,cAAA,aAAwD,aACzB,aACD;UACxBD,EAAA,CAAAsD,SAAA,QAA2B;UAC7BtD,EAAA,CAAAW,YAAA,EAAM;UAENX,EAAA,CAAAC,cAAA,YAA0B;UAAAD,EAAA,CAAAU,MAAA,GAAW;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAC1CX,EAAA,CAAAC,cAAA,WAA2B;UAAAD,EAAA,CAAAU,MAAA,GAAa;UAAAV,EAAA,CAAAW,YAAA,EAAI;UAoB5CX,EAlBA,CAAAmB,UAAA,IAAAoC,8BAAA,iBAAiD,IAAAC,8BAAA,iBAkB8B;UASnFxD,EADE,CAAAW,YAAA,EAAM,EACF;;;UApCyBX,EAAA,CAAAyD,UAAA,CAAAJ,GAAA,CAAAhB,cAAA,CAAwB;UAG9CrC,EAAA,CAAAY,SAAA,GAAmB;UAAnBZ,EAAA,CAAAyD,UAAA,CAAAJ,GAAA,CAAAjB,SAAA,CAAmB;UAGEpC,EAAA,CAAAY,SAAA,GAAW;UAAXZ,EAAA,CAAA6B,iBAAA,CAAAwB,GAAA,CAAAnB,KAAA,CAAW;UACVlC,EAAA,CAAAY,SAAA,GAAa;UAAbZ,EAAA,CAAA6B,iBAAA,CAAAwB,GAAA,CAAAlB,OAAA,CAAa;UAEVnC,EAAA,CAAAY,SAAA,EAAiB;UAAjBZ,EAAA,CAAAsB,UAAA,SAAA+B,GAAA,CAAAf,WAAA,CAAiB;UAkBbtC,EAAA,CAAAY,SAAA,EAA2C;UAA3CZ,EAAA,CAAAsB,UAAA,SAAA+B,GAAA,CAAAtB,WAAA,IAAAsB,GAAA,CAAAtB,WAAA,CAAA2B,MAAA,KAA2C;;;qBA7BzE3D,YAAY,EAAA4D,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}