{"ast": null, "code": "import { AuthGuard } from './core/guards/auth.guard';\nexport const routes = [\n// Home Route (Public)\n{\n  path: '',\n  redirectTo: '/home',\n  pathMatch: 'full'\n},\n// Authentication Routes\n{\n  path: 'auth',\n  loadChildren: () => import('./features/auth/auth.routes').then(m => m.authRoutes)\n},\n// Home with Auth\n{\n  path: 'home',\n  loadChildren: () => import('./features/home/<USER>').then(m => m.homeRoutes),\n  canActivate: [AuthGuard]\n},\n// Explore Routes\n{\n  path: 'explore',\n  loadComponent: () => import('./features/explore/explore.component').then(m => m.ExploreComponent),\n  title: 'Explore - DFashion'\n},\n// Shop Routes\n{\n  path: 'shop',\n  loadComponent: () => import('./features/shop/shop.component').then(m => m.ShopComponent),\n  title: 'Shop - DFashion'\n},\n// Category Routes\n{\n  path: 'category/:category',\n  loadComponent: () => import('./features/category/category.component').then(m => m.CategoryComponent),\n  title: 'Category - DFashion'\n},\n// Wishlist Routes\n{\n  path: 'wishlist',\n  loadComponent: () => import('./features/wishlist/wishlist.component').then(m => m.WishlistComponent),\n  title: 'My Wishlist - DFashion'\n},\n// Social Media Routes\n{\n  path: 'social',\n  loadComponent: () => import('./features/social-media/social-media.component').then(m => m.SocialMediaComponent),\n  title: 'Social Feed - DFashion'\n}, {\n  path: 'feed',\n  loadComponent: () => import('./features/posts/social-feed.component').then(m => m.SocialFeedComponent),\n  title: 'Social Feed - DFashion'\n}, {\n  path: 'stories',\n  loadComponent: () => import('./features/stories/stories-viewer.component').then(m => m.StoriesViewerComponent),\n  title: 'Stories - DFashion'\n}, {\n  path: 'stories/create',\n  loadComponent: () => import('./features/stories/story-create.component').then(m => m.StoryCreateComponent),\n  canActivate: [AuthGuard],\n  title: 'Create Story - DFashion'\n}, {\n  path: 'stories/:userId',\n  loadComponent: () => import('./features/stories/stories-viewer.component').then(m => m.StoriesViewerComponent),\n  title: 'User Stories - DFashion'\n}, {\n  path: 'post/:id',\n  loadComponent: () => import('./features/posts/post-detail.component').then(m => m.PostDetailComponent),\n  title: 'Post Detail - DFashion'\n},\n// E-commerce Hub Routes\n{\n  path: 'hub',\n  loadComponent: () => import('./features/ecommerce/ecommerce-hub.component').then(m => m.EcommerceHubComponent),\n  title: 'E-commerce Hub - DFashion'\n}, {\n  path: 'dashboard',\n  loadComponent: () => import('./features/ecommerce/ecommerce-hub.component').then(m => m.EcommerceHubComponent),\n  title: 'Dashboard - DFashion'\n},\n// Products Routes (using existing product detail)\n{\n  path: 'product/:id',\n  loadComponent: () => import('./features/product/product-detail/product-detail.component').then(m => m.ProductDetailComponent)\n}, {\n  path: 'products/:id',\n  redirectTo: 'product/:id'\n},\n// Shopping Cart & Wishlist (will be created)\n{\n  path: 'cart',\n  loadComponent: () => import('./features/shop/pages/cart/cart.component').then(m => m.CartComponent),\n  canActivate: [AuthGuard],\n  title: 'Shopping Cart - DFashion'\n},\n// Checkout Process (using existing checkout)\n{\n  path: 'checkout',\n  loadComponent: () => import('./features/checkout/checkout.component').then(m => m.CheckoutComponent),\n  canActivate: [AuthGuard],\n  title: 'Checkout - DFashion'\n},\n// Payment Routes\n{\n  path: 'payment-success',\n  loadComponent: () => import('./features/payment/payment-success/payment-success.component').then(m => m.PaymentSuccessComponent),\n  title: 'Payment Successful - DFashion'\n}, {\n  path: 'payment-failed',\n  loadComponent: () => import('./features/payment/payment-failed/payment-failed.component').then(m => m.PaymentFailedComponent),\n  title: 'Payment Failed - DFashion'\n},\n// User Account Management\n{\n  path: 'account',\n  canActivate: [AuthGuard],\n  children: [{\n    path: '',\n    redirectTo: 'profile',\n    pathMatch: 'full'\n  }, {\n    path: 'profile',\n    loadChildren: () => import('./features/profile/profile.routes').then(m => m.profileRoutes)\n  }, {\n    path: 'orders',\n    loadComponent: () => import('./features/profile/pages/profile/profile.component').then(m => m.ProfileComponent),\n    title: 'My Orders - DFashion'\n  }]\n},\n// Vendor Dashboard\n{\n  path: 'vendor',\n  loadChildren: () => import('./features/vendor/vendor.routes').then(m => m.vendorRoutes),\n  canActivate: [AuthGuard],\n  title: 'Vendor Dashboard - DFashion'\n},\n// Legacy Routes (maintain compatibility)\n{\n  path: 'shop',\n  loadChildren: () => import('./features/shop/shop.routes').then(m => m.shopRoutes),\n  canActivate: [AuthGuard]\n}, {\n  path: 'search',\n  loadChildren: () => import('./features/search/search.routes').then(m => m.searchRoutes)\n}, {\n  path: 'product/:id',\n  redirectTo: 'products/:id'\n},\n// Admin Routes\n{\n  path: 'admin',\n  loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule)\n},\n// Support & Help (using existing profile as placeholder)\n{\n  path: 'support',\n  loadComponent: () => import('./features/profile/pages/profile/profile.component').then(m => m.ProfileComponent),\n  title: 'Support - DFashion'\n},\n// Wildcard route\n{\n  path: '**',\n  redirectTo: '/'\n}];", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "authRoutes", "homeRoutes", "canActivate", "loadComponent", "ExploreComponent", "title", "ShopComponent", "CategoryComponent", "WishlistComponent", "SocialMediaComponent", "SocialFeedComponent", "StoriesViewerComponent", "StoryCreateComponent", "PostDetailComponent", "EcommerceHubComponent", "ProductDetailComponent", "CartComponent", "CheckoutComponent", "PaymentSuccessComponent", "PaymentFailedComponent", "children", "profileRoutes", "ProfileComponent", "vendorRoutes", "shopRoutes", "searchRoutes", "AdminModule"], "sources": ["E:\\DFashion\\frontend\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { AuthGuard } from './core/guards/auth.guard';\n\nexport const routes: Routes = [\n  // Home Route (Public)\n  {\n    path: '',\n    redirectTo: '/home',\n    pathMatch: 'full'\n  },\n\n  // Authentication Routes\n  {\n    path: 'auth',\n    loadChildren: () => import('./features/auth/auth.routes').then(m => m.authRoutes)\n  },\n\n  // Home with Auth\n  {\n    path: 'home',\n    loadChildren: () => import('./features/home/<USER>').then(m => m.homeRoutes),\n    canActivate: [AuthGuard]\n  },\n\n  // Explore Routes\n  {\n    path: 'explore',\n    loadComponent: () => import('./features/explore/explore.component').then(m => m.ExploreComponent),\n    title: 'Explore - DFashion'\n  },\n\n  // Shop Routes\n  {\n    path: 'shop',\n    loadComponent: () => import('./features/shop/shop.component').then(m => m.ShopComponent),\n    title: 'Shop - DFashion'\n  },\n\n  // Category Routes\n  {\n    path: 'category/:category',\n    loadComponent: () => import('./features/category/category.component').then(m => m.CategoryComponent),\n    title: 'Category - DFashion'\n  },\n\n  // Wishlist Routes\n  {\n    path: 'wishlist',\n    loadComponent: () => import('./features/wishlist/wishlist.component').then(m => m.WishlistComponent),\n    title: 'My Wishlist - DFashion'\n  },\n\n  // Social Media Routes\n  {\n    path: 'social',\n    loadComponent: () => import('./features/social-media/social-media.component').then(m => m.SocialMediaComponent),\n    title: 'Social Feed - DFashion'\n  },\n  {\n    path: 'feed',\n    loadComponent: () => import('./features/posts/social-feed.component').then(m => m.SocialFeedComponent),\n    title: 'Social Feed - DFashion'\n  },\n  {\n    path: 'stories',\n    loadComponent: () => import('./features/stories/stories-viewer.component').then(m => m.StoriesViewerComponent),\n    title: 'Stories - DFashion'\n  },\n  {\n    path: 'stories/create',\n    loadComponent: () => import('./features/stories/story-create.component').then(m => m.StoryCreateComponent),\n    canActivate: [AuthGuard],\n    title: 'Create Story - DFashion'\n  },\n  {\n    path: 'stories/:userId',\n    loadComponent: () => import('./features/stories/stories-viewer.component').then(m => m.StoriesViewerComponent),\n    title: 'User Stories - DFashion'\n  },\n  {\n    path: 'post/:id',\n    loadComponent: () => import('./features/posts/post-detail.component').then(m => m.PostDetailComponent),\n    title: 'Post Detail - DFashion'\n  },\n\n  // E-commerce Hub Routes\n  {\n    path: 'hub',\n    loadComponent: () => import('./features/ecommerce/ecommerce-hub.component').then(m => m.EcommerceHubComponent),\n    title: 'E-commerce Hub - DFashion'\n  },\n  {\n    path: 'dashboard',\n    loadComponent: () => import('./features/ecommerce/ecommerce-hub.component').then(m => m.EcommerceHubComponent),\n    title: 'Dashboard - DFashion'\n  },\n\n\n\n  // Products Routes (using existing product detail)\n  {\n    path: 'product/:id',\n    loadComponent: () => import('./features/product/product-detail/product-detail.component').then(m => m.ProductDetailComponent)\n  },\n  {\n    path: 'products/:id',\n    redirectTo: 'product/:id'\n  },\n\n  // Shopping Cart & Wishlist (will be created)\n  {\n    path: 'cart',\n    loadComponent: () => import('./features/shop/pages/cart/cart.component').then(m => m.CartComponent),\n    canActivate: [AuthGuard],\n    title: 'Shopping Cart - DFashion'\n  },\n\n  // Checkout Process (using existing checkout)\n  {\n    path: 'checkout',\n    loadComponent: () => import('./features/checkout/checkout.component').then(m => m.CheckoutComponent),\n    canActivate: [AuthGuard],\n    title: 'Checkout - DFashion'\n  },\n\n  // Payment Routes\n  {\n    path: 'payment-success',\n    loadComponent: () => import('./features/payment/payment-success/payment-success.component').then(m => m.PaymentSuccessComponent),\n    title: 'Payment Successful - DFashion'\n  },\n  {\n    path: 'payment-failed',\n    loadComponent: () => import('./features/payment/payment-failed/payment-failed.component').then(m => m.PaymentFailedComponent),\n    title: 'Payment Failed - DFashion'\n  },\n\n  // User Account Management\n  {\n    path: 'account',\n    canActivate: [AuthGuard],\n    children: [\n      {\n        path: '',\n        redirectTo: 'profile',\n        pathMatch: 'full'\n      },\n      {\n        path: 'profile',\n        loadChildren: () => import('./features/profile/profile.routes').then(m => m.profileRoutes)\n      },\n      {\n        path: 'orders',\n        loadComponent: () => import('./features/profile/pages/profile/profile.component').then(m => m.ProfileComponent),\n        title: 'My Orders - DFashion'\n      }\n    ]\n  },\n\n  // Vendor Dashboard\n  {\n    path: 'vendor',\n    loadChildren: () => import('./features/vendor/vendor.routes').then(m => m.vendorRoutes),\n    canActivate: [AuthGuard],\n    title: 'Vendor Dashboard - DFashion'\n  },\n\n  // Legacy Routes (maintain compatibility)\n  {\n    path: 'shop',\n    loadChildren: () => import('./features/shop/shop.routes').then(m => m.shopRoutes),\n    canActivate: [AuthGuard]\n  },\n  {\n    path: 'search',\n    loadChildren: () => import('./features/search/search.routes').then(m => m.searchRoutes)\n  },\n  {\n    path: 'product/:id',\n    redirectTo: 'products/:id'\n  },\n\n  // Admin Routes\n  {\n    path: 'admin',\n    loadChildren: () => import('./admin/admin.module').then(m => m.AdminModule)\n  },\n\n  // Support & Help (using existing profile as placeholder)\n  {\n    path: 'support',\n    loadComponent: () => import('./features/profile/pages/profile/profile.component').then(m => m.ProfileComponent),\n    title: 'Support - DFashion'\n  },\n\n  // Wildcard route\n  {\n    path: '**',\n    redirectTo: '/'\n  }\n];\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,0BAA0B;AAEpD,OAAO,MAAMC,MAAM,GAAW;AAC5B;AACA;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE;CACZ;AAED;AACA;EACEF,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU;CACjF;AAED;AACA;EACEN,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,UAAU,CAAC;EACjFC,WAAW,EAAE,CAACV,SAAS;CACxB;AAED;AACA;EACEE,IAAI,EAAE,SAAS;EACfS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,gBAAgB,CAAC;EACjGC,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,MAAM;EACZS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,aAAa,CAAC;EACxFD,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,oBAAoB;EAC1BS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,iBAAiB,CAAC;EACpGF,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,UAAU;EAChBS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACS,iBAAiB,CAAC;EACpGH,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,QAAQ;EACdS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,gDAAgD,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACU,oBAAoB,CAAC;EAC/GJ,KAAK,EAAE;CACR,EACD;EACEX,IAAI,EAAE,MAAM;EACZS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACW,mBAAmB,CAAC;EACtGL,KAAK,EAAE;CACR,EACD;EACEX,IAAI,EAAE,SAAS;EACfS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,6CAA6C,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACY,sBAAsB,CAAC;EAC9GN,KAAK,EAAE;CACR,EACD;EACEX,IAAI,EAAE,gBAAgB;EACtBS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,2CAA2C,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACa,oBAAoB,CAAC;EAC1GV,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBa,KAAK,EAAE;CACR,EACD;EACEX,IAAI,EAAE,iBAAiB;EACvBS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,6CAA6C,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACY,sBAAsB,CAAC;EAC9GN,KAAK,EAAE;CACR,EACD;EACEX,IAAI,EAAE,UAAU;EAChBS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACc,mBAAmB,CAAC;EACtGR,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,KAAK;EACXS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACe,qBAAqB,CAAC;EAC9GT,KAAK,EAAE;CACR,EACD;EACEX,IAAI,EAAE,WAAW;EACjBS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACe,qBAAqB,CAAC;EAC9GT,KAAK,EAAE;CACR;AAID;AACA;EACEX,IAAI,EAAE,aAAa;EACnBS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,4DAA4D,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACgB,sBAAsB;CAC7H,EACD;EACErB,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE;CACb;AAED;AACA;EACED,IAAI,EAAE,MAAM;EACZS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,2CAA2C,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACiB,aAAa,CAAC;EACnGd,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBa,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,UAAU;EAChBS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACkB,iBAAiB,CAAC;EACpGf,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBa,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,iBAAiB;EACvBS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,8DAA8D,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACmB,uBAAuB,CAAC;EAChIb,KAAK,EAAE;CACR,EACD;EACEX,IAAI,EAAE,gBAAgB;EACtBS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,4DAA4D,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACoB,sBAAsB,CAAC;EAC7Hd,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,SAAS;EACfQ,WAAW,EAAE,CAACV,SAAS,CAAC;EACxB4B,QAAQ,EAAE,CACR;IACE1B,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,SAAS;IACrBC,SAAS,EAAE;GACZ,EACD;IACEF,IAAI,EAAE,SAAS;IACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACsB,aAAa;GAC1F,EACD;IACE3B,IAAI,EAAE,QAAQ;IACdS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,oDAAoD,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACuB,gBAAgB,CAAC;IAC/GjB,KAAK,EAAE;GACR;CAEJ;AAED;AACA;EACEX,IAAI,EAAE,QAAQ;EACdG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACwB,YAAY,CAAC;EACvFrB,WAAW,EAAE,CAACV,SAAS,CAAC;EACxBa,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACyB,UAAU,CAAC;EACjFtB,WAAW,EAAE,CAACV,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,QAAQ;EACdG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC0B,YAAY;CACvF,EACD;EACE/B,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;CACb;AAED;AACA;EACED,IAAI,EAAE,OAAO;EACbG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC2B,WAAW;CAC3E;AAED;AACA;EACEhC,IAAI,EAAE,SAAS;EACfS,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,oDAAoD,CAAC,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACuB,gBAAgB,CAAC;EAC/GjB,KAAK,EAAE;CACR;AAED;AACA;EACEX,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}