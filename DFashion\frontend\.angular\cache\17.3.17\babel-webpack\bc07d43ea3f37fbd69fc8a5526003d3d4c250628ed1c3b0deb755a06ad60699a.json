{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./storage.service\";\nimport * as i3 from \"@ionic/angular\";\nexport class CartService {\n  constructor(http, storageService, toastController) {\n    this.http = http;\n    this.storageService = storageService;\n    this.toastController = toastController;\n    this.API_URL = environment.apiUrl;\n    this.cartItems = new BehaviorSubject([]);\n    this.cartSummary = new BehaviorSubject(null);\n    this.cartItemCount = new BehaviorSubject(0);\n    this.cartTotalAmount = new BehaviorSubject(0);\n    this.showCartTotalPrice = new BehaviorSubject(false);\n    this.isLoadingCart = false;\n    this.useLocalStorageOnly = false; // Temporary flag to disable API calls\n    this.cartItems$ = this.cartItems.asObservable();\n    this.cartSummary$ = this.cartSummary.asObservable();\n    this.cartItemCount$ = this.cartItemCount.asObservable();\n    this.cartTotalAmount$ = this.cartTotalAmount.asObservable();\n    this.showCartTotalPrice$ = this.showCartTotalPrice.asObservable();\n    this.loadCart();\n  }\n  // Get cart from API\n  getCart() {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.get(`${this.API_URL}/cart-new`, options);\n  }\n  // Get cart count only (lightweight endpoint)\n  getCartCount() {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.get(`${this.API_URL}/cart-new/count`, options);\n  }\n  // Get combined cart and wishlist summary\n  getCartWishlistSummary() {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.get(`${this.API_URL}/cart-new/summary`, options);\n  }\n  // Load cart and update local state\n  loadCart() {\n    // Temporary: Use local storage only to avoid API errors\n    if (this.useLocalStorageOnly) {\n      console.log('🔄 Using local storage only (API disabled)...');\n      this.loadCartFromStorage();\n      return;\n    }\n    // Check if user is logged in\n    const token = localStorage.getItem('token');\n    if (token) {\n      // User is logged in - try API first, fallback to local storage\n      console.log('🔄 User authenticated, attempting to load cart from API...');\n      this.loadCartFromAPI();\n    } else {\n      // Guest user - load from local storage\n      console.log('🔄 Guest user, loading cart from local storage...');\n      this.loadCartFromStorage();\n    }\n  }\n  // Load cart from API for logged-in users\n  loadCartFromAPI() {\n    // Check if user is authenticated\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.log('❌ No authentication token, using local storage fallback');\n      this.loadCartFromStorage();\n      return;\n    }\n    // Prevent multiple simultaneous API calls\n    if (this.isLoadingCart) {\n      console.log('🔄 Cart already loading, skipping duplicate request');\n      return;\n    }\n    this.isLoadingCart = true;\n    this.getCart().subscribe({\n      next: response => {\n        this.isLoadingCart = false;\n        if (response.success && response.cart) {\n          this.cartItems.next(response.cart.items || []);\n          this.cartSummary.next(response.summary);\n          this.updateCartCount();\n          console.log('✅ Cart loaded from API:', response.cart.items?.length || 0, 'items');\n        } else {\n          // No cart data from API, initialize empty cart\n          this.cartItems.next([]);\n          this.cartSummary.next(null);\n          this.updateCartCount();\n        }\n      },\n      error: error => {\n        this.isLoadingCart = false;\n        console.error('❌ API cart error:', error);\n        if (error.status === 401) {\n          console.log('❌ Authentication failed, clearing token');\n          localStorage.removeItem('token');\n          this.cartItems.next([]);\n          this.cartSummary.next(null);\n          this.updateCartCount();\n        } else if (error.status === 500) {\n          console.log('❌ Server error, using local storage fallback');\n          this.loadCartFromStorage();\n        } else {\n          console.log('❌ API error, using local storage fallback');\n          this.loadCartFromStorage();\n        }\n      }\n    });\n  }\n  loadCartFromStorage() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Check if storage service is available\n        if (!_this.storageService) {\n          console.log('Storage service not available, using empty cart');\n          _this.cartItems.next([]);\n          _this.updateCartCount();\n          return;\n        }\n        // Wait a bit for storage to initialize\n        yield new Promise(resolve => setTimeout(resolve, 100));\n        const cart = yield _this.storageService.getCart();\n        _this.cartItems.next(cart || []);\n        _this.updateCartCount();\n      } catch (error) {\n        console.error('Error loading cart from storage:', error);\n        _this.cartItems.next([]);\n        _this.updateCartCount();\n      }\n    })();\n  }\n  saveCartToStorage() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this2.storageService) {\n          console.log('Storage service not available, skipping cart save');\n          return;\n        }\n        yield _this2.storageService.setCart(_this2.cartItems.value);\n      } catch (error) {\n        console.error('Error saving cart to storage:', error);\n      }\n    })();\n  }\n  updateCartCount() {\n    const items = this.cartItems.value || [];\n    const count = items.reduce((total, item) => total + item.quantity, 0);\n    this.cartItemCount.next(count);\n    console.log('🛒 Cart count updated:', count);\n  }\n  // Method to refresh cart on user login\n  refreshCartOnLogin() {\n    console.log('🔄 Refreshing cart on login...');\n    this.loadCartFromAPI();\n  }\n  // Method to refresh only cart count (lightweight)\n  refreshCartCount() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.getCartCount().subscribe({\n        next: response => {\n          if (response.success) {\n            this.cartItemCount.next(response.count);\n            this.cartTotalAmount.next(response.totalAmount || 0);\n            this.showCartTotalPrice.next(response.showTotalPrice || false);\n            console.log('🛒 Cart count refreshed:', response.count, 'Total:', response.totalAmount, 'Show price:', response.showTotalPrice);\n          }\n        },\n        error: error => {\n          console.error('❌ Error refreshing cart count:', error);\n          if (error.status === 401) {\n            console.log('❌ Authentication failed, clearing token');\n            localStorage.removeItem('token');\n            this.cartItemCount.next(0);\n            this.cartTotalAmount.next(0);\n            this.showCartTotalPrice.next(false);\n          }\n        }\n      });\n    } else {\n      // No token, set count to 0\n      this.cartItemCount.next(0);\n      this.cartTotalAmount.next(0);\n      this.showCartTotalPrice.next(false);\n    }\n  }\n  // Method to clear cart on logout\n  clearCartOnLogout() {\n    console.log('🔄 Clearing cart on logout...');\n    this.cartItems.next([]);\n    this.cartSummary.next(null);\n    this.cartItemCount.next(0);\n    this.cartTotalAmount.next(0);\n    this.showCartTotalPrice.next(false);\n  }\n  // Temporary method to enable/disable API calls\n  setUseLocalStorageOnly(useLocalOnly) {\n    this.useLocalStorageOnly = useLocalOnly;\n    console.log('🔧 Cart API calls', useLocalOnly ? 'DISABLED' : 'ENABLED');\n    if (useLocalOnly) {\n      console.log('🔧 Cart will use local storage only');\n    }\n  }\n  // Add item to cart via API\n  addToCart(productId, quantity = 1, size, color) {\n    const payload = {\n      productId,\n      quantity,\n      size,\n      color\n    };\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.post(`${this.API_URL}/cart-new/add`, payload, options).pipe(tap(response => {\n      if (response.success) {\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      }\n    }));\n  }\n  // Legacy method for backward compatibility - works for guest users\n  addToCartLegacy(_x) {\n    var _this3 = this;\n    return _asyncToGenerator(function* (product, quantity = 1, size, color) {\n      try {\n        const productId = product._id || product.id;\n        // Try API first, but fallback to local storage for guest users\n        try {\n          const response = yield _this3.addToCart(productId, quantity, size, color).toPromise();\n          if (response?.success) {\n            yield _this3.showToast('Item added to cart', 'success');\n            _this3.loadCart(); // Refresh cart\n            return true;\n          }\n        } catch (apiError) {\n          console.log('API not available, using local storage');\n        }\n        // Fallback to local storage (for guest users)\n        const cartItem = {\n          _id: `${productId}_${size || 'default'}_${color || 'default'}`,\n          product: {\n            _id: productId,\n            name: product.name,\n            price: product.price,\n            originalPrice: product.originalPrice,\n            images: product.images || [],\n            brand: product.brand || '',\n            discount: product.discount\n          },\n          quantity,\n          size,\n          color,\n          addedAt: new Date()\n        };\n        const currentCart = _this3.cartItems.value;\n        const existingItemIndex = currentCart.findIndex(item => item.product._id === productId && (item.size || 'default') === (size || 'default') && (item.color || 'default') === (color || 'default'));\n        if (existingItemIndex >= 0) {\n          currentCart[existingItemIndex].quantity += quantity;\n        } else {\n          currentCart.push(cartItem);\n        }\n        _this3.cartItems.next(currentCart);\n        _this3.updateCartCount();\n        yield _this3.saveCartToStorage();\n        yield _this3.showToast('Item added to cart', 'success');\n        return true;\n      } catch (error) {\n        console.error('Error adding to cart:', error);\n        yield _this3.showToast('Failed to add item to cart', 'danger');\n        return false;\n      }\n    }).apply(this, arguments);\n  }\n  // Remove item from cart via API\n  removeFromCart(itemId) {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.delete(`${this.API_URL}/cart-new/remove/${itemId}`, options).pipe(tap(response => {\n      if (response.success) {\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      }\n    }));\n  }\n  // Bulk remove items from cart\n  bulkRemoveFromCart(itemIds) {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      body: {\n        itemIds\n      },\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {\n      body: {\n        itemIds\n      }\n    };\n    return this.http.delete(`${this.API_URL}/cart-new/bulk-remove`, options).pipe(tap(response => {\n      if (response.success) {\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      }\n    }));\n  }\n  // Legacy method\n  removeFromCartLegacy(itemId) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield _this4.removeFromCart(itemId).toPromise();\n        if (response?.success) {\n          yield _this4.showToast('Item removed from cart', 'success');\n          _this4.loadCart(); // Refresh cart\n        }\n      } catch (error) {\n        console.error('Error removing from cart:', error);\n        yield _this4.showToast('Failed to remove item from cart', 'danger');\n      }\n    })();\n  }\n  // Update cart item quantity via API\n  updateCartItem(itemId, quantity) {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.put(`${this.API_URL}/cart-new/update/${itemId}`, {\n      quantity\n    }, options).pipe(tap(response => {\n      if (response.success) {\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      }\n    }));\n  }\n  // Legacy method\n  updateQuantity(itemId, quantity) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (quantity <= 0) {\n          yield _this5.removeFromCartLegacy(itemId);\n          return;\n        }\n        const response = yield _this5.updateCartItem(itemId, quantity).toPromise();\n        if (response?.success) {\n          _this5.loadCart(); // Refresh cart\n        }\n      } catch (error) {\n        console.error('Error updating quantity:', error);\n        yield _this5.showToast('Failed to update quantity', 'danger');\n      }\n    })();\n  }\n  // Clear cart via API\n  clearCartAPI() {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.delete(`${this.API_URL}/cart-new/clear`, options);\n  }\n  clearCart() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield _this6.clearCartAPI().toPromise();\n        if (response?.success) {\n          _this6.cartItems.next([]);\n          _this6.cartSummary.next(null);\n          _this6.updateCartCount();\n          yield _this6.showToast('Cart cleared', 'success');\n        }\n      } catch (error) {\n        console.error('Error clearing cart:', error);\n        yield _this6.showToast('Failed to clear cart', 'danger');\n      }\n    })();\n  }\n  getCartTotal() {\n    const summary = this.cartSummary.value;\n    if (summary) {\n      return summary.total;\n    }\n    // Fallback calculation\n    return this.cartItems.value.reduce((total, item) => {\n      const price = item.product.price;\n      return total + price * item.quantity;\n    }, 0);\n  }\n  getCartItemCount() {\n    return this.cartItemCount.value;\n  }\n  getCartTotalAmount() {\n    return this.cartTotalAmount.value;\n  }\n  shouldShowCartTotalPrice() {\n    return this.showCartTotalPrice.value;\n  }\n  isInCart(productId, size, color) {\n    return this.cartItems.value.some(item => item.product._id === productId && (item.size || 'default') === (size || 'default') && (item.color || 'default') === (color || 'default'));\n  }\n  getCartItem(productId, size, color) {\n    return this.cartItems.value.find(item => item.product._id === productId && (item.size || 'default') === (size || 'default') && (item.color || 'default') === (color || 'default'));\n  }\n  showToast(message, color) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const toast = yield _this7.toastController.create({\n        message: message,\n        duration: 2000,\n        color: color,\n        position: 'bottom'\n      });\n      toast.present();\n    })();\n  }\n  static {\n    this.ɵfac = function CartService_Factory(t) {\n      return new (t || CartService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.StorageService), i0.ɵɵinject(i3.ToastController));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CartService,\n      factory: CartService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "environment", "CartService", "constructor", "http", "storageService", "toastController", "API_URL", "apiUrl", "cartItems", "cartSummary", "cartItemCount", "cartTotalAmount", "showCartTotalPrice", "isLoadingCart", "useLocalStorageOnly", "cartItems$", "asObservable", "cartSummary$", "cartItemCount$", "cartTotalAmount$", "showCartTotalPrice$", "loadCart", "getCart", "token", "localStorage", "getItem", "options", "headers", "get", "getCartCount", "getCartWishlist<PERSON><PERSON><PERSON>y", "console", "log", "loadCartFromStorage", "loadCartFromAPI", "subscribe", "next", "response", "success", "cart", "items", "summary", "updateCartCount", "length", "error", "status", "removeItem", "_this", "_asyncToGenerator", "Promise", "resolve", "setTimeout", "saveCartToStorage", "_this2", "setCart", "value", "count", "reduce", "total", "item", "quantity", "refreshCartOnLogin", "refreshCartCount", "totalAmount", "showTotalPrice", "clearCartOnLogout", "setUseLocalStorageOnly", "useLocalOnly", "addToCart", "productId", "size", "color", "payload", "post", "pipe", "addToCartLegacy", "_x", "_this3", "product", "_id", "id", "to<PERSON>romise", "showToast", "apiError", "cartItem", "name", "price", "originalPrice", "images", "brand", "discount", "addedAt", "Date", "currentCart", "existingItemIndex", "findIndex", "push", "apply", "arguments", "removeFromCart", "itemId", "delete", "bulkRemoveFromCart", "itemIds", "body", "removeFromCartLegacy", "_this4", "updateCartItem", "put", "updateQuantity", "_this5", "clearCartAPI", "clearCart", "_this6", "getCartTotal", "getCartItemCount", "getCartTotalAmount", "shouldShowCartTotalPrice", "isInCart", "some", "getCartItem", "find", "message", "_this7", "toast", "create", "duration", "position", "present", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "StorageService", "i3", "ToastController", "factory", "ɵfac", "providedIn"], "sources": ["E:\\DFashion\\frontend\\src\\app\\core\\services\\cart.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable, of } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport { HttpClient } from '@angular/common/http';\nimport { StorageService } from './storage.service';\nimport { ToastController } from '@ionic/angular';\nimport { environment } from '../../../environments/environment';\n\nexport interface CartItem {\n  _id: string;\n  product: {\n    _id: string;\n    name: string;\n    price: number;\n    originalPrice?: number;\n    images: { url: string; isPrimary: boolean }[];\n    brand: string;\n    discount?: number;\n  };\n  quantity: number;\n  size?: string;\n  color?: string;\n  addedAt: Date;\n}\n\nexport interface CartSummary {\n  itemCount: number;\n  totalQuantity: number;\n  subtotal: number;\n  discount: number;\n  total: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class CartService {\n  private readonly API_URL = environment.apiUrl;\n  private cartItems = new BehaviorSubject<CartItem[]>([]);\n  private cartSummary = new BehaviorSubject<CartSummary | null>(null);\n  private cartItemCount = new BehaviorSubject<number>(0);\n  private cartTotalAmount = new BehaviorSubject<number>(0);\n  private showCartTotalPrice = new BehaviorSubject<boolean>(false);\n  private isLoadingCart = false;\n  private useLocalStorageOnly = false; // Temporary flag to disable API calls\n\n  public cartItems$ = this.cartItems.asObservable();\n  public cartSummary$ = this.cartSummary.asObservable();\n  public cartItemCount$ = this.cartItemCount.asObservable();\n  public cartTotalAmount$ = this.cartTotalAmount.asObservable();\n  public showCartTotalPrice$ = this.showCartTotalPrice.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private storageService: StorageService,\n    private toastController: ToastController\n  ) {\n    this.loadCart();\n  }\n\n  // Get cart from API\n  getCart(): Observable<{ success: boolean; cart: any; summary: any }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.get<{ success: boolean; cart: any; summary: any }>(`${this.API_URL}/cart-new`, options);\n  }\n\n  // Get cart count only (lightweight endpoint)\n  getCartCount(): Observable<{ success: boolean; count: number; totalItems: number; itemCount: number; totalAmount: number; showTotalPrice: boolean }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.get<{ success: boolean; count: number; totalItems: number; itemCount: number; totalAmount: number; showTotalPrice: boolean }>(`${this.API_URL}/cart-new/count`, options);\n  }\n\n  // Get combined cart and wishlist summary\n  getCartWishlistSummary(): Observable<any> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.get<any>(`${this.API_URL}/cart-new/summary`, options);\n  }\n\n  // Load cart and update local state\n  loadCart() {\n    // Temporary: Use local storage only to avoid API errors\n    if (this.useLocalStorageOnly) {\n      console.log('🔄 Using local storage only (API disabled)...');\n      this.loadCartFromStorage();\n      return;\n    }\n\n    // Check if user is logged in\n    const token = localStorage.getItem('token');\n\n    if (token) {\n      // User is logged in - try API first, fallback to local storage\n      console.log('🔄 User authenticated, attempting to load cart from API...');\n      this.loadCartFromAPI();\n    } else {\n      // Guest user - load from local storage\n      console.log('🔄 Guest user, loading cart from local storage...');\n      this.loadCartFromStorage();\n    }\n  }\n\n  // Load cart from API for logged-in users\n  private loadCartFromAPI() {\n    // Check if user is authenticated\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.log('❌ No authentication token, using local storage fallback');\n      this.loadCartFromStorage();\n      return;\n    }\n\n    // Prevent multiple simultaneous API calls\n    if (this.isLoadingCart) {\n      console.log('🔄 Cart already loading, skipping duplicate request');\n      return;\n    }\n\n    this.isLoadingCart = true;\n\n    this.getCart().subscribe({\n      next: (response) => {\n        this.isLoadingCart = false;\n        if (response.success && response.cart) {\n          this.cartItems.next(response.cart.items || []);\n          this.cartSummary.next(response.summary);\n          this.updateCartCount();\n          console.log('✅ Cart loaded from API:', response.cart.items?.length || 0, 'items');\n        } else {\n          // No cart data from API, initialize empty cart\n          this.cartItems.next([]);\n          this.cartSummary.next(null);\n          this.updateCartCount();\n        }\n      },\n      error: (error) => {\n        this.isLoadingCart = false;\n        console.error('❌ API cart error:', error);\n\n        if (error.status === 401) {\n          console.log('❌ Authentication failed, clearing token');\n          localStorage.removeItem('token');\n          this.cartItems.next([]);\n          this.cartSummary.next(null);\n          this.updateCartCount();\n        } else if (error.status === 500) {\n          console.log('❌ Server error, using local storage fallback');\n          this.loadCartFromStorage();\n        } else {\n          console.log('❌ API error, using local storage fallback');\n          this.loadCartFromStorage();\n        }\n      }\n    });\n  }\n\n  private async loadCartFromStorage() {\n    try {\n      // Check if storage service is available\n      if (!this.storageService) {\n        console.log('Storage service not available, using empty cart');\n        this.cartItems.next([]);\n        this.updateCartCount();\n        return;\n      }\n\n      // Wait a bit for storage to initialize\n      await new Promise(resolve => setTimeout(resolve, 100));\n      const cart = await this.storageService.getCart();\n      this.cartItems.next(cart || []);\n      this.updateCartCount();\n    } catch (error) {\n      console.error('Error loading cart from storage:', error);\n      this.cartItems.next([]);\n      this.updateCartCount();\n    }\n  }\n\n  private async saveCartToStorage() {\n    try {\n      if (!this.storageService) {\n        console.log('Storage service not available, skipping cart save');\n        return;\n      }\n      await this.storageService.setCart(this.cartItems.value);\n    } catch (error) {\n      console.error('Error saving cart to storage:', error);\n    }\n  }\n\n  private updateCartCount() {\n    const items = this.cartItems.value || [];\n    const count = items.reduce((total, item) => total + item.quantity, 0);\n    this.cartItemCount.next(count);\n    console.log('🛒 Cart count updated:', count);\n  }\n\n  // Method to refresh cart on user login\n  refreshCartOnLogin() {\n    console.log('🔄 Refreshing cart on login...');\n    this.loadCartFromAPI();\n  }\n\n  // Method to refresh only cart count (lightweight)\n  refreshCartCount() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.getCartCount().subscribe({\n        next: (response) => {\n          if (response.success) {\n            this.cartItemCount.next(response.count);\n            this.cartTotalAmount.next(response.totalAmount || 0);\n            this.showCartTotalPrice.next(response.showTotalPrice || false);\n            console.log('🛒 Cart count refreshed:', response.count, 'Total:', response.totalAmount, 'Show price:', response.showTotalPrice);\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error refreshing cart count:', error);\n          if (error.status === 401) {\n            console.log('❌ Authentication failed, clearing token');\n            localStorage.removeItem('token');\n            this.cartItemCount.next(0);\n            this.cartTotalAmount.next(0);\n            this.showCartTotalPrice.next(false);\n          }\n        }\n      });\n    } else {\n      // No token, set count to 0\n      this.cartItemCount.next(0);\n      this.cartTotalAmount.next(0);\n      this.showCartTotalPrice.next(false);\n    }\n  }\n\n  // Method to clear cart on logout\n  clearCartOnLogout() {\n    console.log('🔄 Clearing cart on logout...');\n    this.cartItems.next([]);\n    this.cartSummary.next(null);\n    this.cartItemCount.next(0);\n    this.cartTotalAmount.next(0);\n    this.showCartTotalPrice.next(false);\n  }\n\n  // Temporary method to enable/disable API calls\n  setUseLocalStorageOnly(useLocalOnly: boolean) {\n    this.useLocalStorageOnly = useLocalOnly;\n    console.log('🔧 Cart API calls', useLocalOnly ? 'DISABLED' : 'ENABLED');\n    if (useLocalOnly) {\n      console.log('🔧 Cart will use local storage only');\n    }\n  }\n\n  // Add item to cart via API\n  addToCart(productId: string, quantity: number = 1, size?: string, color?: string): Observable<{ success: boolean; message: string }> {\n    const payload = { productId, quantity, size, color };\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.post<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/add`, payload, options).pipe(\n      tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      })\n    );\n  }\n\n  // Legacy method for backward compatibility - works for guest users\n  async addToCartLegacy(product: any, quantity: number = 1, size?: string, color?: string): Promise<boolean> {\n    try {\n      const productId = product._id || product.id;\n\n      // Try API first, but fallback to local storage for guest users\n      try {\n        const response = await this.addToCart(productId, quantity, size, color).toPromise();\n        if (response?.success) {\n          await this.showToast('Item added to cart', 'success');\n          this.loadCart(); // Refresh cart\n          return true;\n        }\n      } catch (apiError) {\n        console.log('API not available, using local storage');\n      }\n\n      // Fallback to local storage (for guest users)\n      const cartItem: CartItem = {\n        _id: `${productId}_${size || 'default'}_${color || 'default'}`,\n        product: {\n          _id: productId,\n          name: product.name,\n          price: product.price,\n          originalPrice: product.originalPrice,\n          images: product.images || [],\n          brand: product.brand || '',\n          discount: product.discount\n        },\n        quantity,\n        size,\n        color,\n        addedAt: new Date()\n      };\n\n      const currentCart = this.cartItems.value;\n      const existingItemIndex = currentCart.findIndex(item =>\n        item.product._id === productId &&\n        (item.size || 'default') === (size || 'default') &&\n        (item.color || 'default') === (color || 'default')\n      );\n\n      if (existingItemIndex >= 0) {\n        currentCart[existingItemIndex].quantity += quantity;\n      } else {\n        currentCart.push(cartItem);\n      }\n\n      this.cartItems.next(currentCart);\n      this.updateCartCount();\n      await this.saveCartToStorage();\n      await this.showToast('Item added to cart', 'success');\n      return true;\n\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n      await this.showToast('Failed to add item to cart', 'danger');\n      return false;\n    }\n  }\n\n  // Remove item from cart via API\n  removeFromCart(itemId: string): Observable<{ success: boolean; message: string }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.delete<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/remove/${itemId}`, options).pipe(\n      tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      })\n    );\n  }\n\n  // Bulk remove items from cart\n  bulkRemoveFromCart(itemIds: string[]): Observable<{ success: boolean; message: string; removedCount: number }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      body: { itemIds },\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {\n      body: { itemIds }\n    };\n    return this.http.delete<{ success: boolean; message: string; removedCount: number }>(`${this.API_URL}/cart-new/bulk-remove`, options).pipe(\n      tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      })\n    );\n  }\n\n  // Legacy method\n  async removeFromCartLegacy(itemId: string): Promise<void> {\n    try {\n      const response = await this.removeFromCart(itemId).toPromise();\n      if (response?.success) {\n        await this.showToast('Item removed from cart', 'success');\n        this.loadCart(); // Refresh cart\n      }\n    } catch (error) {\n      console.error('Error removing from cart:', error);\n      await this.showToast('Failed to remove item from cart', 'danger');\n    }\n  }\n\n  // Update cart item quantity via API\n  updateCartItem(itemId: string, quantity: number): Observable<{ success: boolean; message: string }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.put<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/update/${itemId}`, { quantity }, options).pipe(\n      tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      })\n    );\n  }\n\n  // Legacy method\n  async updateQuantity(itemId: string, quantity: number): Promise<void> {\n    try {\n      if (quantity <= 0) {\n        await this.removeFromCartLegacy(itemId);\n        return;\n      }\n\n      const response = await this.updateCartItem(itemId, quantity).toPromise();\n      if (response?.success) {\n        this.loadCart(); // Refresh cart\n      }\n    } catch (error) {\n      console.error('Error updating quantity:', error);\n      await this.showToast('Failed to update quantity', 'danger');\n    }\n  }\n\n  // Clear cart via API\n  clearCartAPI(): Observable<{ success: boolean; message: string }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.delete<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/clear`, options);\n  }\n\n  async clearCart(): Promise<void> {\n    try {\n      const response = await this.clearCartAPI().toPromise();\n      if (response?.success) {\n        this.cartItems.next([]);\n        this.cartSummary.next(null);\n        this.updateCartCount();\n        await this.showToast('Cart cleared', 'success');\n      }\n    } catch (error) {\n      console.error('Error clearing cart:', error);\n      await this.showToast('Failed to clear cart', 'danger');\n    }\n  }\n\n  getCartTotal(): number {\n    const summary = this.cartSummary.value;\n    if (summary) {\n      return summary.total;\n    }\n\n    // Fallback calculation\n    return this.cartItems.value.reduce((total, item) => {\n      const price = item.product.price;\n      return total + (price * item.quantity);\n    }, 0);\n  }\n\n  getCartItemCount(): number {\n    return this.cartItemCount.value;\n  }\n\n  getCartTotalAmount(): number {\n    return this.cartTotalAmount.value;\n  }\n\n  shouldShowCartTotalPrice(): boolean {\n    return this.showCartTotalPrice.value;\n  }\n\n  isInCart(productId: string, size?: string, color?: string): boolean {\n    return this.cartItems.value.some(item =>\n      item.product._id === productId &&\n      (item.size || 'default') === (size || 'default') &&\n      (item.color || 'default') === (color || 'default')\n    );\n  }\n\n  getCartItem(productId: string, size?: string, color?: string): CartItem | undefined {\n    return this.cartItems.value.find(item =>\n      item.product._id === productId &&\n      (item.size || 'default') === (size || 'default') &&\n      (item.color || 'default') === (color || 'default')\n    );\n  }\n\n  private async showToast(message: string, color: string) {\n    const toast = await this.toastController.create({\n      message: message,\n      duration: 2000,\n      color: color,\n      position: 'bottom'\n    });\n    toast.present();\n  }\n}\n"], "mappings": ";AACA,SAASA,eAAe,QAAwB,MAAM;AACtD,SAASC,GAAG,QAAQ,gBAAgB;AAIpC,SAASC,WAAW,QAAQ,mCAAmC;;;;;AA8B/D,OAAM,MAAOC,WAAW;EAgBtBC,YACUC,IAAgB,EAChBC,cAA8B,EAC9BC,eAAgC;IAFhC,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IAlBR,KAAAC,OAAO,GAAGN,WAAW,CAACO,MAAM;IACrC,KAAAC,SAAS,GAAG,IAAIV,eAAe,CAAa,EAAE,CAAC;IAC/C,KAAAW,WAAW,GAAG,IAAIX,eAAe,CAAqB,IAAI,CAAC;IAC3D,KAAAY,aAAa,GAAG,IAAIZ,eAAe,CAAS,CAAC,CAAC;IAC9C,KAAAa,eAAe,GAAG,IAAIb,eAAe,CAAS,CAAC,CAAC;IAChD,KAAAc,kBAAkB,GAAG,IAAId,eAAe,CAAU,KAAK,CAAC;IACxD,KAAAe,aAAa,GAAG,KAAK;IACrB,KAAAC,mBAAmB,GAAG,KAAK,CAAC,CAAC;IAE9B,KAAAC,UAAU,GAAG,IAAI,CAACP,SAAS,CAACQ,YAAY,EAAE;IAC1C,KAAAC,YAAY,GAAG,IAAI,CAACR,WAAW,CAACO,YAAY,EAAE;IAC9C,KAAAE,cAAc,GAAG,IAAI,CAACR,aAAa,CAACM,YAAY,EAAE;IAClD,KAAAG,gBAAgB,GAAG,IAAI,CAACR,eAAe,CAACK,YAAY,EAAE;IACtD,KAAAI,mBAAmB,GAAG,IAAI,CAACR,kBAAkB,CAACI,YAAY,EAAE;IAOjE,IAAI,CAACK,QAAQ,EAAE;EACjB;EAEA;EACAC,OAAOA,CAAA;IACL,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAACyB,GAAG,CAAgD,GAAG,IAAI,CAACtB,OAAO,WAAW,EAAEoB,OAAO,CAAC;EAC1G;EAEA;EACAG,YAAYA,CAAA;IACV,MAAMN,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAACyB,GAAG,CAA2H,GAAG,IAAI,CAACtB,OAAO,iBAAiB,EAAEoB,OAAO,CAAC;EAC3L;EAEA;EACAI,sBAAsBA,CAAA;IACpB,MAAMP,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAACyB,GAAG,CAAM,GAAG,IAAI,CAACtB,OAAO,mBAAmB,EAAEoB,OAAO,CAAC;EACxE;EAEA;EACAL,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACP,mBAAmB,EAAE;MAC5BiB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,IAAI,CAACC,mBAAmB,EAAE;MAC1B;;IAGF;IACA,MAAMV,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAIF,KAAK,EAAE;MACT;MACAQ,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzE,IAAI,CAACE,eAAe,EAAE;KACvB,MAAM;MACL;MACAH,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAChE,IAAI,CAACC,mBAAmB,EAAE;;EAE9B;EAEA;EACQC,eAAeA,CAAA;IACrB;IACA,MAAMX,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVQ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;MACtE,IAAI,CAACC,mBAAmB,EAAE;MAC1B;;IAGF;IACA,IAAI,IAAI,CAACpB,aAAa,EAAE;MACtBkB,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE;;IAGF,IAAI,CAACnB,aAAa,GAAG,IAAI;IAEzB,IAAI,CAACS,OAAO,EAAE,CAACa,SAAS,CAAC;MACvBC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACxB,aAAa,GAAG,KAAK;QAC1B,IAAIwB,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;UACrC,IAAI,CAAC/B,SAAS,CAAC4B,IAAI,CAACC,QAAQ,CAACE,IAAI,CAACC,KAAK,IAAI,EAAE,CAAC;UAC9C,IAAI,CAAC/B,WAAW,CAAC2B,IAAI,CAACC,QAAQ,CAACI,OAAO,CAAC;UACvC,IAAI,CAACC,eAAe,EAAE;UACtBX,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEK,QAAQ,CAACE,IAAI,CAACC,KAAK,EAAEG,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC;SAClF,MAAM;UACL;UACA,IAAI,CAACnC,SAAS,CAAC4B,IAAI,CAAC,EAAE,CAAC;UACvB,IAAI,CAAC3B,WAAW,CAAC2B,IAAI,CAAC,IAAI,CAAC;UAC3B,IAAI,CAACM,eAAe,EAAE;;MAE1B,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC/B,aAAa,GAAG,KAAK;QAC1BkB,OAAO,CAACa,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QAEzC,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;UACxBd,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;UACtDR,YAAY,CAACsB,UAAU,CAAC,OAAO,CAAC;UAChC,IAAI,CAACtC,SAAS,CAAC4B,IAAI,CAAC,EAAE,CAAC;UACvB,IAAI,CAAC3B,WAAW,CAAC2B,IAAI,CAAC,IAAI,CAAC;UAC3B,IAAI,CAACM,eAAe,EAAE;SACvB,MAAM,IAAIE,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;UAC/Bd,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3D,IAAI,CAACC,mBAAmB,EAAE;SAC3B,MAAM;UACLF,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;UACxD,IAAI,CAACC,mBAAmB,EAAE;;MAE9B;KACD,CAAC;EACJ;EAEcA,mBAAmBA,CAAA;IAAA,IAAAc,KAAA;IAAA,OAAAC,iBAAA;MAC/B,IAAI;QACF;QACA,IAAI,CAACD,KAAI,CAAC3C,cAAc,EAAE;UACxB2B,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;UAC9De,KAAI,CAACvC,SAAS,CAAC4B,IAAI,CAAC,EAAE,CAAC;UACvBW,KAAI,CAACL,eAAe,EAAE;UACtB;;QAGF;QACA,MAAM,IAAIO,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;QACtD,MAAMX,IAAI,SAASQ,KAAI,CAAC3C,cAAc,CAACkB,OAAO,EAAE;QAChDyB,KAAI,CAACvC,SAAS,CAAC4B,IAAI,CAACG,IAAI,IAAI,EAAE,CAAC;QAC/BQ,KAAI,CAACL,eAAe,EAAE;OACvB,CAAC,OAAOE,KAAK,EAAE;QACdb,OAAO,CAACa,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDG,KAAI,CAACvC,SAAS,CAAC4B,IAAI,CAAC,EAAE,CAAC;QACvBW,KAAI,CAACL,eAAe,EAAE;;IACvB;EACH;EAEcU,iBAAiBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MAC7B,IAAI;QACF,IAAI,CAACK,MAAI,CAACjD,cAAc,EAAE;UACxB2B,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;UAChE;;QAEF,MAAMqB,MAAI,CAACjD,cAAc,CAACkD,OAAO,CAACD,MAAI,CAAC7C,SAAS,CAAC+C,KAAK,CAAC;OACxD,CAAC,OAAOX,KAAK,EAAE;QACdb,OAAO,CAACa,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;IACtD;EACH;EAEQF,eAAeA,CAAA;IACrB,MAAMF,KAAK,GAAG,IAAI,CAAChC,SAAS,CAAC+C,KAAK,IAAI,EAAE;IACxC,MAAMC,KAAK,GAAGhB,KAAK,CAACiB,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACC,QAAQ,EAAE,CAAC,CAAC;IACrE,IAAI,CAAClD,aAAa,CAAC0B,IAAI,CAACoB,KAAK,CAAC;IAC9BzB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEwB,KAAK,CAAC;EAC9C;EAEA;EACAK,kBAAkBA,CAAA;IAChB9B,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7C,IAAI,CAACE,eAAe,EAAE;EACxB;EAEA;EACA4B,gBAAgBA,CAAA;IACd,MAAMvC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT,IAAI,CAACM,YAAY,EAAE,CAACM,SAAS,CAAC;QAC5BC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpB,IAAI,CAAC5B,aAAa,CAAC0B,IAAI,CAACC,QAAQ,CAACmB,KAAK,CAAC;YACvC,IAAI,CAAC7C,eAAe,CAACyB,IAAI,CAACC,QAAQ,CAAC0B,WAAW,IAAI,CAAC,CAAC;YACpD,IAAI,CAACnD,kBAAkB,CAACwB,IAAI,CAACC,QAAQ,CAAC2B,cAAc,IAAI,KAAK,CAAC;YAC9DjC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEK,QAAQ,CAACmB,KAAK,EAAE,QAAQ,EAAEnB,QAAQ,CAAC0B,WAAW,EAAE,aAAa,EAAE1B,QAAQ,CAAC2B,cAAc,CAAC;;QAEnI,CAAC;QACDpB,KAAK,EAAGA,KAAK,IAAI;UACfb,OAAO,CAACa,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACtD,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;YACxBd,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;YACtDR,YAAY,CAACsB,UAAU,CAAC,OAAO,CAAC;YAChC,IAAI,CAACpC,aAAa,CAAC0B,IAAI,CAAC,CAAC,CAAC;YAC1B,IAAI,CAACzB,eAAe,CAACyB,IAAI,CAAC,CAAC,CAAC;YAC5B,IAAI,CAACxB,kBAAkB,CAACwB,IAAI,CAAC,KAAK,CAAC;;QAEvC;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAAC1B,aAAa,CAAC0B,IAAI,CAAC,CAAC,CAAC;MAC1B,IAAI,CAACzB,eAAe,CAACyB,IAAI,CAAC,CAAC,CAAC;MAC5B,IAAI,CAACxB,kBAAkB,CAACwB,IAAI,CAAC,KAAK,CAAC;;EAEvC;EAEA;EACA6B,iBAAiBA,CAAA;IACflC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,IAAI,CAACxB,SAAS,CAAC4B,IAAI,CAAC,EAAE,CAAC;IACvB,IAAI,CAAC3B,WAAW,CAAC2B,IAAI,CAAC,IAAI,CAAC;IAC3B,IAAI,CAAC1B,aAAa,CAAC0B,IAAI,CAAC,CAAC,CAAC;IAC1B,IAAI,CAACzB,eAAe,CAACyB,IAAI,CAAC,CAAC,CAAC;IAC5B,IAAI,CAACxB,kBAAkB,CAACwB,IAAI,CAAC,KAAK,CAAC;EACrC;EAEA;EACA8B,sBAAsBA,CAACC,YAAqB;IAC1C,IAAI,CAACrD,mBAAmB,GAAGqD,YAAY;IACvCpC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEmC,YAAY,GAAG,UAAU,GAAG,SAAS,CAAC;IACvE,IAAIA,YAAY,EAAE;MAChBpC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;EAEtD;EAEA;EACAoC,SAASA,CAACC,SAAiB,EAAET,QAAA,GAAmB,CAAC,EAAEU,IAAa,EAAEC,KAAc;IAC9E,MAAMC,OAAO,GAAG;MAAEH,SAAS;MAAET,QAAQ;MAAEU,IAAI;MAAEC;IAAK,CAAE;IACpD,MAAMhD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAACsE,IAAI,CAAwC,GAAG,IAAI,CAACnE,OAAO,eAAe,EAAEkE,OAAO,EAAE9C,OAAO,CAAC,CAACgD,IAAI,CACjH3E,GAAG,CAACsC,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB;QACA,IAAI,CAACJ,eAAe,EAAE;;IAE1B,CAAC,CAAC,CACH;EACH;EAEA;EACMyC,eAAeA,CAAAC,EAAA,EAAkE;IAAA,IAAAC,MAAA;IAAA,OAAA7B,iBAAA,YAAjE8B,OAAY,EAAElB,QAAA,GAAmB,CAAC,EAAEU,IAAa,EAAEC,KAAc;MACrF,IAAI;QACF,MAAMF,SAAS,GAAGS,OAAO,CAACC,GAAG,IAAID,OAAO,CAACE,EAAE;QAE3C;QACA,IAAI;UACF,MAAM3C,QAAQ,SAASwC,MAAI,CAACT,SAAS,CAACC,SAAS,EAAET,QAAQ,EAAEU,IAAI,EAAEC,KAAK,CAAC,CAACU,SAAS,EAAE;UACnF,IAAI5C,QAAQ,EAAEC,OAAO,EAAE;YACrB,MAAMuC,MAAI,CAACK,SAAS,CAAC,oBAAoB,EAAE,SAAS,CAAC;YACrDL,MAAI,CAACxD,QAAQ,EAAE,CAAC,CAAC;YACjB,OAAO,IAAI;;SAEd,CAAC,OAAO8D,QAAQ,EAAE;UACjBpD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAGvD;QACA,MAAMoD,QAAQ,GAAa;UACzBL,GAAG,EAAE,GAAGV,SAAS,IAAIC,IAAI,IAAI,SAAS,IAAIC,KAAK,IAAI,SAAS,EAAE;UAC9DO,OAAO,EAAE;YACPC,GAAG,EAAEV,SAAS;YACdgB,IAAI,EAAEP,OAAO,CAACO,IAAI;YAClBC,KAAK,EAAER,OAAO,CAACQ,KAAK;YACpBC,aAAa,EAAET,OAAO,CAACS,aAAa;YACpCC,MAAM,EAAEV,OAAO,CAACU,MAAM,IAAI,EAAE;YAC5BC,KAAK,EAAEX,OAAO,CAACW,KAAK,IAAI,EAAE;YAC1BC,QAAQ,EAAEZ,OAAO,CAACY;WACnB;UACD9B,QAAQ;UACRU,IAAI;UACJC,KAAK;UACLoB,OAAO,EAAE,IAAIC,IAAI;SAClB;QAED,MAAMC,WAAW,GAAGhB,MAAI,CAACrE,SAAS,CAAC+C,KAAK;QACxC,MAAMuC,iBAAiB,GAAGD,WAAW,CAACE,SAAS,CAACpC,IAAI,IAClDA,IAAI,CAACmB,OAAO,CAACC,GAAG,KAAKV,SAAS,IAC9B,CAACV,IAAI,CAACW,IAAI,IAAI,SAAS,OAAOA,IAAI,IAAI,SAAS,CAAC,IAChD,CAACX,IAAI,CAACY,KAAK,IAAI,SAAS,OAAOA,KAAK,IAAI,SAAS,CAAC,CACnD;QAED,IAAIuB,iBAAiB,IAAI,CAAC,EAAE;UAC1BD,WAAW,CAACC,iBAAiB,CAAC,CAAClC,QAAQ,IAAIA,QAAQ;SACpD,MAAM;UACLiC,WAAW,CAACG,IAAI,CAACZ,QAAQ,CAAC;;QAG5BP,MAAI,CAACrE,SAAS,CAAC4B,IAAI,CAACyD,WAAW,CAAC;QAChChB,MAAI,CAACnC,eAAe,EAAE;QACtB,MAAMmC,MAAI,CAACzB,iBAAiB,EAAE;QAC9B,MAAMyB,MAAI,CAACK,SAAS,CAAC,oBAAoB,EAAE,SAAS,CAAC;QACrD,OAAO,IAAI;OAEZ,CAAC,OAAOtC,KAAK,EAAE;QACdb,OAAO,CAACa,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAMiC,MAAI,CAACK,SAAS,CAAC,4BAA4B,EAAE,QAAQ,CAAC;QAC5D,OAAO,KAAK;;IACb,GAAAe,KAAA,OAAAC,SAAA;EACH;EAEA;EACAC,cAAcA,CAACC,MAAc;IAC3B,MAAM7E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAACkG,MAAM,CAAwC,GAAG,IAAI,CAAC/F,OAAO,oBAAoB8F,MAAM,EAAE,EAAE1E,OAAO,CAAC,CAACgD,IAAI,CACvH3E,GAAG,CAACsC,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB;QACA,IAAI,CAACJ,eAAe,EAAE;;IAE1B,CAAC,CAAC,CACH;EACH;EAEA;EACAoE,kBAAkBA,CAACC,OAAiB;IAClC,MAAMhF,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBiF,IAAI,EAAE;QAAED;MAAO,CAAE;MACjB5E,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG;MACFiF,IAAI,EAAE;QAAED;MAAO;KAChB;IACD,OAAO,IAAI,CAACpG,IAAI,CAACkG,MAAM,CAA8D,GAAG,IAAI,CAAC/F,OAAO,uBAAuB,EAAEoB,OAAO,CAAC,CAACgD,IAAI,CACxI3E,GAAG,CAACsC,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB;QACA,IAAI,CAACJ,eAAe,EAAE;;IAE1B,CAAC,CAAC,CACH;EACH;EAEA;EACMuE,oBAAoBA,CAACL,MAAc;IAAA,IAAAM,MAAA;IAAA,OAAA1D,iBAAA;MACvC,IAAI;QACF,MAAMX,QAAQ,SAASqE,MAAI,CAACP,cAAc,CAACC,MAAM,CAAC,CAACnB,SAAS,EAAE;QAC9D,IAAI5C,QAAQ,EAAEC,OAAO,EAAE;UACrB,MAAMoE,MAAI,CAACxB,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC;UACzDwB,MAAI,CAACrF,QAAQ,EAAE,CAAC,CAAC;;OAEpB,CAAC,OAAOuB,KAAK,EAAE;QACdb,OAAO,CAACa,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAM8D,MAAI,CAACxB,SAAS,CAAC,iCAAiC,EAAE,QAAQ,CAAC;;IAClE;EACH;EAEA;EACAyB,cAAcA,CAACP,MAAc,EAAExC,QAAgB;IAC7C,MAAMrC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAACyG,GAAG,CAAwC,GAAG,IAAI,CAACtG,OAAO,oBAAoB8F,MAAM,EAAE,EAAE;MAAExC;IAAQ,CAAE,EAAElC,OAAO,CAAC,CAACgD,IAAI,CAClI3E,GAAG,CAACsC,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB;QACA,IAAI,CAACJ,eAAe,EAAE;;IAE1B,CAAC,CAAC,CACH;EACH;EAEA;EACM2E,cAAcA,CAACT,MAAc,EAAExC,QAAgB;IAAA,IAAAkD,MAAA;IAAA,OAAA9D,iBAAA;MACnD,IAAI;QACF,IAAIY,QAAQ,IAAI,CAAC,EAAE;UACjB,MAAMkD,MAAI,CAACL,oBAAoB,CAACL,MAAM,CAAC;UACvC;;QAGF,MAAM/D,QAAQ,SAASyE,MAAI,CAACH,cAAc,CAACP,MAAM,EAAExC,QAAQ,CAAC,CAACqB,SAAS,EAAE;QACxE,IAAI5C,QAAQ,EAAEC,OAAO,EAAE;UACrBwE,MAAI,CAACzF,QAAQ,EAAE,CAAC,CAAC;;OAEpB,CAAC,OAAOuB,KAAK,EAAE;QACdb,OAAO,CAACa,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAMkE,MAAI,CAAC5B,SAAS,CAAC,2BAA2B,EAAE,QAAQ,CAAC;;IAC5D;EACH;EAEA;EACA6B,YAAYA,CAAA;IACV,MAAMxF,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACpB,IAAI,CAACkG,MAAM,CAAwC,GAAG,IAAI,CAAC/F,OAAO,iBAAiB,EAAEoB,OAAO,CAAC;EAC3G;EAEMsF,SAASA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAjE,iBAAA;MACb,IAAI;QACF,MAAMX,QAAQ,SAAS4E,MAAI,CAACF,YAAY,EAAE,CAAC9B,SAAS,EAAE;QACtD,IAAI5C,QAAQ,EAAEC,OAAO,EAAE;UACrB2E,MAAI,CAACzG,SAAS,CAAC4B,IAAI,CAAC,EAAE,CAAC;UACvB6E,MAAI,CAACxG,WAAW,CAAC2B,IAAI,CAAC,IAAI,CAAC;UAC3B6E,MAAI,CAACvE,eAAe,EAAE;UACtB,MAAMuE,MAAI,CAAC/B,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC;;OAElD,CAAC,OAAOtC,KAAK,EAAE;QACdb,OAAO,CAACa,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,MAAMqE,MAAI,CAAC/B,SAAS,CAAC,sBAAsB,EAAE,QAAQ,CAAC;;IACvD;EACH;EAEAgC,YAAYA,CAAA;IACV,MAAMzE,OAAO,GAAG,IAAI,CAAChC,WAAW,CAAC8C,KAAK;IACtC,IAAId,OAAO,EAAE;MACX,OAAOA,OAAO,CAACiB,KAAK;;IAGtB;IACA,OAAO,IAAI,CAAClD,SAAS,CAAC+C,KAAK,CAACE,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAI;MACjD,MAAM2B,KAAK,GAAG3B,IAAI,CAACmB,OAAO,CAACQ,KAAK;MAChC,OAAO5B,KAAK,GAAI4B,KAAK,GAAG3B,IAAI,CAACC,QAAS;IACxC,CAAC,EAAE,CAAC,CAAC;EACP;EAEAuD,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACzG,aAAa,CAAC6C,KAAK;EACjC;EAEA6D,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACzG,eAAe,CAAC4C,KAAK;EACnC;EAEA8D,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAACzG,kBAAkB,CAAC2C,KAAK;EACtC;EAEA+D,QAAQA,CAACjD,SAAiB,EAAEC,IAAa,EAAEC,KAAc;IACvD,OAAO,IAAI,CAAC/D,SAAS,CAAC+C,KAAK,CAACgE,IAAI,CAAC5D,IAAI,IACnCA,IAAI,CAACmB,OAAO,CAACC,GAAG,KAAKV,SAAS,IAC9B,CAACV,IAAI,CAACW,IAAI,IAAI,SAAS,OAAOA,IAAI,IAAI,SAAS,CAAC,IAChD,CAACX,IAAI,CAACY,KAAK,IAAI,SAAS,OAAOA,KAAK,IAAI,SAAS,CAAC,CACnD;EACH;EAEAiD,WAAWA,CAACnD,SAAiB,EAAEC,IAAa,EAAEC,KAAc;IAC1D,OAAO,IAAI,CAAC/D,SAAS,CAAC+C,KAAK,CAACkE,IAAI,CAAC9D,IAAI,IACnCA,IAAI,CAACmB,OAAO,CAACC,GAAG,KAAKV,SAAS,IAC9B,CAACV,IAAI,CAACW,IAAI,IAAI,SAAS,OAAOA,IAAI,IAAI,SAAS,CAAC,IAChD,CAACX,IAAI,CAACY,KAAK,IAAI,SAAS,OAAOA,KAAK,IAAI,SAAS,CAAC,CACnD;EACH;EAEcW,SAASA,CAACwC,OAAe,EAAEnD,KAAa;IAAA,IAAAoD,MAAA;IAAA,OAAA3E,iBAAA;MACpD,MAAM4E,KAAK,SAASD,MAAI,CAACtH,eAAe,CAACwH,MAAM,CAAC;QAC9CH,OAAO,EAAEA,OAAO;QAChBI,QAAQ,EAAE,IAAI;QACdvD,KAAK,EAAEA,KAAK;QACZwD,QAAQ,EAAE;OACX,CAAC;MACFH,KAAK,CAACI,OAAO,EAAE;IAAC;EAClB;;;uBA5cW/H,WAAW,EAAAgI,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,eAAA;IAAA;EAAA;;;aAAXvI,WAAW;MAAAwI,OAAA,EAAXxI,WAAW,CAAAyI,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}