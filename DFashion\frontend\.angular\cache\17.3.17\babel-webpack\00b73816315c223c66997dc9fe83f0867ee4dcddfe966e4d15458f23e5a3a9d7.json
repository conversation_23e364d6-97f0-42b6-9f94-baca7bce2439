{"ast": null, "code": "import _asyncToGenerator from \"E:/Fahion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/storage-angular\";\nexport class StorageService {\n  constructor(storage) {\n    this.storage = storage;\n    this._storage = null;\n    this.init();\n  }\n  init() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // If running on the web, Storage will only use IndexedDB, LocalStorage and SessionStorage\n      const storage = yield _this.storage.create();\n      _this._storage = storage;\n    })();\n  }\n  // Create and expose methods that users of this service can call\n  set(key, value) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      return _this2._storage?.set(key, value);\n    })();\n  }\n  get(key) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      return _this3._storage?.get(key);\n    })();\n  }\n  remove(key) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      return _this4._storage?.remove(key);\n    })();\n  }\n  clear() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      return _this5._storage?.clear();\n    })();\n  }\n  keys() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      return _this6._storage?.keys() || [];\n    })();\n  }\n  length() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      return _this7._storage?.length() || 0;\n    })();\n  }\n  // Convenience methods for common operations\n  setUser(user) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      return _this8.set('user', user);\n    })();\n  }\n  getUser() {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      return _this9.get('user');\n    })();\n  }\n  removeUser() {\n    var _this0 = this;\n    return _asyncToGenerator(function* () {\n      return _this0.remove('user');\n    })();\n  }\n  setToken(token) {\n    var _this1 = this;\n    return _asyncToGenerator(function* () {\n      return _this1.set('auth_token', token);\n    })();\n  }\n  getToken() {\n    var _this10 = this;\n    return _asyncToGenerator(function* () {\n      return _this10.get('auth_token');\n    })();\n  }\n  removeToken() {\n    var _this11 = this;\n    return _asyncToGenerator(function* () {\n      return _this11.remove('auth_token');\n    })();\n  }\n  setCart(cart) {\n    var _this12 = this;\n    return _asyncToGenerator(function* () {\n      return _this12.set('cart', cart);\n    })();\n  }\n  getCart() {\n    var _this13 = this;\n    return _asyncToGenerator(function* () {\n      return _this13.get('cart') || [];\n    })();\n  }\n  clearCart() {\n    var _this14 = this;\n    return _asyncToGenerator(function* () {\n      return _this14.remove('cart');\n    })();\n  }\n  setWishlist(wishlist) {\n    var _this15 = this;\n    return _asyncToGenerator(function* () {\n      return _this15.set('wishlist', wishlist);\n    })();\n  }\n  getWishlist() {\n    var _this16 = this;\n    return _asyncToGenerator(function* () {\n      return _this16.get('wishlist') || [];\n    })();\n  }\n  clearWishlist() {\n    var _this17 = this;\n    return _asyncToGenerator(function* () {\n      return _this17.remove('wishlist');\n    })();\n  }\n  setSettings(settings) {\n    var _this18 = this;\n    return _asyncToGenerator(function* () {\n      return _this18.set('app_settings', settings);\n    })();\n  }\n  getSettings() {\n    var _this19 = this;\n    return _asyncToGenerator(function* () {\n      return _this19.get('app_settings') || {};\n    })();\n  }\n  setOnboardingCompleted(completed) {\n    var _this20 = this;\n    return _asyncToGenerator(function* () {\n      return _this20.set('onboarding_completed', completed);\n    })();\n  }\n  isOnboardingCompleted() {\n    var _this21 = this;\n    return _asyncToGenerator(function* () {\n      return _this21.get('onboarding_completed') || false;\n    })();\n  }\n  static {\n    this.ɵfac = function StorageService_Factory(t) {\n      return new (t || StorageService)(i0.ɵɵinject(i1.Storage));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: StorageService,\n      factory: StorageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["StorageService", "constructor", "storage", "_storage", "init", "_this", "_asyncToGenerator", "create", "set", "key", "value", "_this2", "get", "_this3", "remove", "_this4", "clear", "_this5", "keys", "_this6", "length", "_this7", "setUser", "user", "_this8", "getUser", "_this9", "removeUser", "_this0", "setToken", "token", "_this1", "getToken", "_this10", "removeToken", "_this11", "setCart", "cart", "_this12", "getCart", "_this13", "clearCart", "_this14", "setWishlist", "wishlist", "_this15", "getWishlist", "_this16", "clearWishlist", "_this17", "setSettings", "settings", "_this18", "getSettings", "_this19", "setOnboardingCompleted", "completed", "_this20", "isOnboardingCompleted", "_this21", "i0", "ɵɵinject", "i1", "Storage", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\core\\services\\storage.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Storage } from '@ionic/storage-angular';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class StorageService {\n  private _storage: Storage | null = null;\n\n  constructor(private storage: Storage) {\n    this.init();\n  }\n\n  async init() {\n    // If running on the web, Storage will only use IndexedDB, LocalStorage and SessionStorage\n    const storage = await this.storage.create();\n    this._storage = storage;\n  }\n\n  // Create and expose methods that users of this service can call\n  public async set(key: string, value: any): Promise<any> {\n    return this._storage?.set(key, value);\n  }\n\n  public async get(key: string): Promise<any> {\n    return this._storage?.get(key);\n  }\n\n  public async remove(key: string): Promise<any> {\n    return this._storage?.remove(key);\n  }\n\n  public async clear(): Promise<void> {\n    return this._storage?.clear();\n  }\n\n  public async keys(): Promise<string[]> {\n    return this._storage?.keys() || [];\n  }\n\n  public async length(): Promise<number> {\n    return this._storage?.length() || 0;\n  }\n\n  // Convenience methods for common operations\n  public async setUser(user: any): Promise<any> {\n    return this.set('user', user);\n  }\n\n  public async getUser(): Promise<any> {\n    return this.get('user');\n  }\n\n  public async removeUser(): Promise<any> {\n    return this.remove('user');\n  }\n\n  public async setToken(token: string): Promise<any> {\n    return this.set('auth_token', token);\n  }\n\n  public async getToken(): Promise<string> {\n    return this.get('auth_token');\n  }\n\n  public async removeToken(): Promise<any> {\n    return this.remove('auth_token');\n  }\n\n  public async setCart(cart: any[]): Promise<any> {\n    return this.set('cart', cart);\n  }\n\n  public async getCart(): Promise<any[]> {\n    return this.get('cart') || [];\n  }\n\n  public async clearCart(): Promise<any> {\n    return this.remove('cart');\n  }\n\n  public async setWishlist(wishlist: any[]): Promise<any> {\n    return this.set('wishlist', wishlist);\n  }\n\n  public async getWishlist(): Promise<any[]> {\n    return this.get('wishlist') || [];\n  }\n\n  public async clearWishlist(): Promise<any> {\n    return this.remove('wishlist');\n  }\n\n  public async setSettings(settings: any): Promise<any> {\n    return this.set('app_settings', settings);\n  }\n\n  public async getSettings(): Promise<any> {\n    return this.get('app_settings') || {};\n  }\n\n  public async setOnboardingCompleted(completed: boolean): Promise<any> {\n    return this.set('onboarding_completed', completed);\n  }\n\n  public async isOnboardingCompleted(): Promise<boolean> {\n    return this.get('onboarding_completed') || false;\n  }\n}\n"], "mappings": ";;;AAMA,OAAM,MAAOA,cAAc;EAGzBC,YAAoBC,OAAgB;IAAhB,KAAAA,OAAO,GAAPA,OAAO;IAFnB,KAAAC,QAAQ,GAAmB,IAAI;IAGrC,IAAI,CAACC,IAAI,EAAE;EACb;EAEMA,IAAIA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACR;MACA,MAAMJ,OAAO,SAASG,KAAI,CAACH,OAAO,CAACK,MAAM,EAAE;MAC3CF,KAAI,CAACF,QAAQ,GAAGD,OAAO;IAAC;EAC1B;EAEA;EACaM,GAAGA,CAACC,GAAW,EAAEC,KAAU;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MACtC,OAAOK,MAAI,CAACR,QAAQ,EAAEK,GAAG,CAACC,GAAG,EAAEC,KAAK,CAAC;IAAC;EACxC;EAEaE,GAAGA,CAACH,GAAW;IAAA,IAAAI,MAAA;IAAA,OAAAP,iBAAA;MAC1B,OAAOO,MAAI,CAACV,QAAQ,EAAES,GAAG,CAACH,GAAG,CAAC;IAAC;EACjC;EAEaK,MAAMA,CAACL,GAAW;IAAA,IAAAM,MAAA;IAAA,OAAAT,iBAAA;MAC7B,OAAOS,MAAI,CAACZ,QAAQ,EAAEW,MAAM,CAACL,GAAG,CAAC;IAAC;EACpC;EAEaO,KAAKA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAX,iBAAA;MAChB,OAAOW,MAAI,CAACd,QAAQ,EAAEa,KAAK,EAAE;IAAC;EAChC;EAEaE,IAAIA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAb,iBAAA;MACf,OAAOa,MAAI,CAAChB,QAAQ,EAAEe,IAAI,EAAE,IAAI,EAAE;IAAC;EACrC;EAEaE,MAAMA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAf,iBAAA;MACjB,OAAOe,MAAI,CAAClB,QAAQ,EAAEiB,MAAM,EAAE,IAAI,CAAC;IAAC;EACtC;EAEA;EACaE,OAAOA,CAACC,IAAS;IAAA,IAAAC,MAAA;IAAA,OAAAlB,iBAAA;MAC5B,OAAOkB,MAAI,CAAChB,GAAG,CAAC,MAAM,EAAEe,IAAI,CAAC;IAAC;EAChC;EAEaE,OAAOA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAApB,iBAAA;MAClB,OAAOoB,MAAI,CAACd,GAAG,CAAC,MAAM,CAAC;IAAC;EAC1B;EAEae,UAAUA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAtB,iBAAA;MACrB,OAAOsB,MAAI,CAACd,MAAM,CAAC,MAAM,CAAC;IAAC;EAC7B;EAEae,QAAQA,CAACC,KAAa;IAAA,IAAAC,MAAA;IAAA,OAAAzB,iBAAA;MACjC,OAAOyB,MAAI,CAACvB,GAAG,CAAC,YAAY,EAAEsB,KAAK,CAAC;IAAC;EACvC;EAEaE,QAAQA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAA3B,iBAAA;MACnB,OAAO2B,OAAI,CAACrB,GAAG,CAAC,YAAY,CAAC;IAAC;EAChC;EAEasB,WAAWA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAA7B,iBAAA;MACtB,OAAO6B,OAAI,CAACrB,MAAM,CAAC,YAAY,CAAC;IAAC;EACnC;EAEasB,OAAOA,CAACC,IAAW;IAAA,IAAAC,OAAA;IAAA,OAAAhC,iBAAA;MAC9B,OAAOgC,OAAI,CAAC9B,GAAG,CAAC,MAAM,EAAE6B,IAAI,CAAC;IAAC;EAChC;EAEaE,OAAOA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAAlC,iBAAA;MAClB,OAAOkC,OAAI,CAAC5B,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE;IAAC;EAChC;EAEa6B,SAASA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAApC,iBAAA;MACpB,OAAOoC,OAAI,CAAC5B,MAAM,CAAC,MAAM,CAAC;IAAC;EAC7B;EAEa6B,WAAWA,CAACC,QAAe;IAAA,IAAAC,OAAA;IAAA,OAAAvC,iBAAA;MACtC,OAAOuC,OAAI,CAACrC,GAAG,CAAC,UAAU,EAAEoC,QAAQ,CAAC;IAAC;EACxC;EAEaE,WAAWA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAAzC,iBAAA;MACtB,OAAOyC,OAAI,CAACnC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;IAAC;EACpC;EAEaoC,aAAaA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAA3C,iBAAA;MACxB,OAAO2C,OAAI,CAACnC,MAAM,CAAC,UAAU,CAAC;IAAC;EACjC;EAEaoC,WAAWA,CAACC,QAAa;IAAA,IAAAC,OAAA;IAAA,OAAA9C,iBAAA;MACpC,OAAO8C,OAAI,CAAC5C,GAAG,CAAC,cAAc,EAAE2C,QAAQ,CAAC;IAAC;EAC5C;EAEaE,WAAWA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAAhD,iBAAA;MACtB,OAAOgD,OAAI,CAAC1C,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE;IAAC;EACxC;EAEa2C,sBAAsBA,CAACC,SAAkB;IAAA,IAAAC,OAAA;IAAA,OAAAnD,iBAAA;MACpD,OAAOmD,OAAI,CAACjD,GAAG,CAAC,sBAAsB,EAAEgD,SAAS,CAAC;IAAC;EACrD;EAEaE,qBAAqBA,CAAA;IAAA,IAAAC,OAAA;IAAA,OAAArD,iBAAA;MAChC,OAAOqD,OAAI,CAAC/C,GAAG,CAAC,sBAAsB,CAAC,IAAI,KAAK;IAAC;EACnD;;;uBArGWZ,cAAc,EAAA4D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,OAAA;IAAA;EAAA;;;aAAd/D,cAAc;MAAAgE,OAAA,EAAdhE,cAAc,CAAAiE,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}