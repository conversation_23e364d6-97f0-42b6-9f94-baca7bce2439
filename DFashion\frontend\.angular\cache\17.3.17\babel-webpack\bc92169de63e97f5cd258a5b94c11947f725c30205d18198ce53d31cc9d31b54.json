{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class MediaService {\n  constructor() {\n    this.mediaErrors = new BehaviorSubject([]);\n    this.mediaErrors$ = this.mediaErrors.asObservable();\n    // Fallback images for different scenarios\n    this.fallbackImages = {\n      user: '/assets/images/default-avatar.svg',\n      product: '/assets/images/default-product.svg',\n      post: '/assets/images/default-post.svg',\n      story: '/assets/images/default-story.svg'\n    };\n    // Backup fallback images (simple colored placeholders)\n    this.backupFallbacks = {\n      user: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzLjk5IDcuMDEgMTUuNjIgNiAxOEMxMC4wMSAyMCAxMy45OSAyMCAxOCAxOEMxNi45OSAxNS42MiAxNC42NyAxMy45OSAxMiAxNFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+Cjwvc3ZnPgo=',\n      product: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+',\n      post: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkJGQ0ZEIi8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNFNUU3RUIiLz4KPC9zdmc+',\n      story: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkVGM0Y0Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNGQ0E1QTUiLz4KPC9zdmc+'\n    };\n    // Video library - loaded from API\n    this.videos = [];\n    // Broken URL patterns to fix\n    this.brokenUrlPatterns = ['/uploads/stories/images/', '/uploads/stories/videos/', 'sample-videos.com', 'localhost:4200/assets/', 'file://'];\n  }\n  /**\n   * Get a reliable fallback image that always works\n   */\n  getReliableFallback(type = 'post') {\n    return this.backupFallbacks[type];\n  }\n  /**\n   * Check if an image URL is likely to fail\n   */\n  isLikelyToFail(url) {\n    return this.isExternalImageUrl(url) || this.isBrokenUrl(url);\n  }\n  /**\n   * Get a safe image URL with fallback handling and broken URL fixing\n   */\n  getSafeImageUrl(url, type = 'post') {\n    if (!url || url.trim() === '') {\n      return this.backupFallbacks[type]; // Use base64 fallback for empty URLs\n    }\n    // Fix broken URLs\n    const fixedUrl = this.fixBrokenUrl(url, type);\n    if (fixedUrl !== url) {\n      return fixedUrl;\n    }\n    // Handle localhost URLs that might be broken\n    if (url.includes('localhost:4200/assets/')) {\n      const assetPath = url.split('localhost:4200')[1];\n      return assetPath;\n    }\n    // For external images that might fail, provide a more reliable fallback\n    if (this.isExternalImageUrl(url)) {\n      // Return the URL but we know it might fail and will fallback gracefully\n      return url;\n    }\n    // Check if URL is valid\n    try {\n      new URL(url);\n      return url;\n    } catch {\n      // If not a valid URL, treat as relative path\n      if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {\n        return url;\n      }\n      // Return base64 fallback for invalid URLs\n      return this.backupFallbacks[type];\n    }\n  }\n  /**\n   * Fix broken URLs by replacing them with working alternatives\n   */\n  fixBrokenUrl(url, type) {\n    // Check for broken patterns\n    for (const pattern of this.brokenUrlPatterns) {\n      if (url.includes(pattern)) {\n        // Replace with appropriate fallback or working URL\n        if (pattern === '/uploads/stories/images/' || pattern === '/uploads/stories/videos/') {\n          return this.getReplacementMediaUrl(url, type);\n        }\n        if (pattern === 'sample-videos.com') {\n          return this.getReliableFallback(type);\n        }\n        if (pattern === 'localhost:4200/assets/') {\n          // Extract the asset path and return it as relative\n          const assetPath = url.split('localhost:4200')[1];\n          return assetPath;\n        }\n        if (pattern === 'file://') {\n          return this.fallbackImages[type];\n        }\n      }\n    }\n    return url;\n  }\n  /**\n   * Get replacement media URL for broken local paths\n   */\n  getReplacementMediaUrl(originalUrl, type) {\n    // Map broken local URLs to working Unsplash URLs based on content\n    const urlMappings = {\n      'summer-collection': 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=800',\n      'behind-scenes': 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800',\n      'customer-spotlight': 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=800',\n      'styling-tips': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',\n      'design': 'https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=800'\n    };\n    // Try to match content from filename\n    for (const [key, replacementUrl] of Object.entries(urlMappings)) {\n      if (originalUrl.toLowerCase().includes(key)) {\n        return replacementUrl;\n      }\n    }\n    // Return appropriate fallback\n    return this.fallbackImages[type];\n  }\n  /**\n   * Handle image load errors with progressive fallback\n   */\n  handleImageError(event, fallbackType = 'post') {\n    const img = event.target;\n    if (!img) return;\n    const originalSrc = img.src;\n    // First try: Use SVG fallback from assets\n    if (!originalSrc.includes(this.fallbackImages[fallbackType]) && !originalSrc.startsWith('data:')) {\n      img.src = this.fallbackImages[fallbackType];\n      // Only log meaningful errors (not external image failures)\n      if (!this.isExternalImageUrl(originalSrc) && !originalSrc.includes('localhost:4200')) {\n        this.logMediaError({\n          id: originalSrc,\n          type: 'load_error',\n          message: `Failed to load image: ${originalSrc}`,\n          fallbackUrl: this.fallbackImages[fallbackType]\n        });\n      }\n      return;\n    }\n    // Second try: Use base64 backup fallback if SVG also fails\n    if (originalSrc.includes(this.fallbackImages[fallbackType])) {\n      img.src = this.backupFallbacks[fallbackType];\n      // Only warn for local asset failures, not external\n      if (!this.isExternalImageUrl(originalSrc)) {\n        console.warn(`SVG fallback failed, using backup for ${fallbackType}:`, originalSrc);\n      }\n      return;\n    }\n  }\n  /**\n   * Check if URL is an external image (Unsplash, etc.)\n   */\n  isExternalImageUrl(url) {\n    const externalDomains = ['unsplash.com', 'images.unsplash.com', 'picsum.photos', 'via.placeholder.com', 'placehold.it', 'placeholder.com'];\n    return externalDomains.some(domain => url.includes(domain));\n  }\n  /**\n   * Check if URL is a video\n   */\n  isVideoUrl(url) {\n    if (!url) return false;\n    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi'];\n    const lowerUrl = url.toLowerCase();\n    return videoExtensions.some(ext => lowerUrl.includes(ext)) || lowerUrl.includes('video') || lowerUrl.includes('.mp4');\n  }\n  /**\n   * Get video thumbnail\n   */\n  getVideoThumbnail(videoUrl) {\n    // Check if it's one of our sample videos\n    const sampleVideo = this.sampleVideos.find(v => v.url === videoUrl);\n    if (sampleVideo) {\n      return sampleVideo.thumbnail;\n    }\n    // For other videos, try to generate thumbnail URL or use fallback\n    return this.fallbackImages.post;\n  }\n  /**\n   * Get video duration\n   */\n  getVideoDuration(videoUrl) {\n    const sampleVideo = this.sampleVideos.find(v => v.url === videoUrl);\n    return sampleVideo ? sampleVideo.duration : 30; // Default 30 seconds\n  }\n  /**\n   * Process media items from database\n   */\n  processMediaItems(mediaArray) {\n    if (!mediaArray || !Array.isArray(mediaArray)) {\n      return [];\n    }\n    return mediaArray.map((media, index) => {\n      const isVideo = this.isVideoUrl(media.url);\n      return {\n        id: media._id || `media_${index}`,\n        type: isVideo ? 'video' : 'image',\n        url: this.getSafeImageUrl(media.url),\n        thumbnailUrl: isVideo ? this.getVideoThumbnail(media.url) : undefined,\n        alt: media.alt || '',\n        duration: isVideo ? this.getVideoDuration(media.url) : undefined,\n        aspectRatio: media.aspectRatio || (isVideo ? 16 / 9 : 1),\n        size: media.size\n      };\n    });\n  }\n  /**\n   * Add sample videos to existing media array with intelligent content matching\n   */\n  enhanceWithSampleVideos(mediaArray, videoCount = 2, contentHint) {\n    const processedMedia = this.processMediaItems(mediaArray);\n    // Add sample videos if we don't have enough media or if videos are broken\n    const needsVideoEnhancement = processedMedia.length < 3 || processedMedia.some(media => media.type === 'video' && this.isBrokenUrl(media.url));\n    if (needsVideoEnhancement) {\n      const videosToAdd = Math.min(videoCount, this.sampleVideos.length);\n      for (let i = 0; i < videosToAdd; i++) {\n        // Try to match video content to the hint\n        let sampleVideo;\n        if (contentHint) {\n          sampleVideo = this.getVideoByContentHint(contentHint, i);\n        } else {\n          sampleVideo = this.sampleVideos[i];\n        }\n        processedMedia.push({\n          id: `enhanced_video_${i}`,\n          type: 'video',\n          url: sampleVideo.url,\n          thumbnailUrl: sampleVideo.thumbnail,\n          alt: sampleVideo.title || `Enhanced video ${i + 1}`,\n          duration: sampleVideo.duration,\n          aspectRatio: 16 / 9\n        });\n      }\n    }\n    return processedMedia;\n  }\n  /**\n   * Get video based on content hint for better matching\n   */\n  getVideoByContentHint(hint, fallbackIndex = 0) {\n    const lowerHint = hint.toLowerCase();\n    if (lowerHint.includes('fashion') || lowerHint.includes('style')) {\n      return this.getVideoByType('fashion');\n    }\n    if (lowerHint.includes('tutorial') || lowerHint.includes('tips')) {\n      return this.getVideoByType('tutorial');\n    }\n    if (lowerHint.includes('showcase') || lowerHint.includes('collection')) {\n      return this.getVideoByType('showcase');\n    }\n    if (lowerHint.includes('story') || lowerHint.includes('behind')) {\n      return this.getVideoByType('story');\n    }\n    return this.sampleVideos[fallbackIndex] || this.sampleVideos[0];\n  }\n  /**\n   * Check if URL is broken\n   */\n  isBrokenUrl(url) {\n    return this.brokenUrlPatterns.some(pattern => url.includes(pattern));\n  }\n  /**\n   * Log media errors for debugging (with smart filtering)\n   */\n  logMediaError(error) {\n    const currentErrors = this.mediaErrors.value;\n    this.mediaErrors.next([...currentErrors, error]);\n    // Only log to console if it's not an external image failure\n    if (!this.isExternalImageUrl(error.id)) {\n      console.warn('Media Error:', error);\n    }\n  }\n  /**\n   * Clear media errors\n   */\n  clearMediaErrors() {\n    this.mediaErrors.next([]);\n  }\n  /**\n   * Preload media for better performance with graceful error handling\n   */\n  preloadMedia(mediaItems) {\n    const promises = mediaItems.map(media => {\n      return new Promise(resolve => {\n        if (media.type === 'image') {\n          const img = new Image();\n          img.onload = () => resolve();\n          img.onerror = () => {\n            // Only log errors for non-external images\n            if (!this.isExternalImageUrl(media.url)) {\n              console.warn(`Failed to preload image: ${media.url}`);\n            }\n            resolve(); // Resolve anyway to not break the promise chain\n          };\n          img.src = media.url;\n        } else if (media.type === 'video') {\n          const video = document.createElement('video');\n          video.onloadeddata = () => resolve();\n          video.onerror = () => {\n            // Only log errors for non-external videos\n            if (!this.isExternalImageUrl(media.url)) {\n              console.warn(`Failed to preload video: ${media.url}`);\n            }\n            resolve(); // Resolve anyway to not break the promise chain\n          };\n          video.src = media.url;\n          video.load();\n        } else {\n          resolve(); // Unknown type, just resolve\n        }\n      });\n    });\n    return Promise.all(promises);\n  }\n  static {\n    this.ɵfac = function MediaService_Factory(t) {\n      return new (t || MediaService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MediaService,\n      factory: MediaService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "MediaService", "constructor", "mediaErrors", "mediaErrors$", "asObservable", "fallbackImages", "user", "product", "post", "story", "backupFallbacks", "videos", "brokenUrlPatterns", "getReliable<PERSON><PERSON>back", "type", "isLikelyToFail", "url", "isExternalImageUrl", "isBrokenUrl", "getSafeImageUrl", "trim", "fixedUrl", "fixBrokenUrl", "includes", "assetPath", "split", "URL", "startsWith", "pattern", "getReplacementMediaUrl", "originalUrl", "urlMappings", "key", "replacementUrl", "Object", "entries", "toLowerCase", "handleImageError", "event", "fallbackType", "img", "target", "originalSrc", "src", "logMediaError", "id", "message", "fallbackUrl", "console", "warn", "externalDomains", "some", "domain", "isVideoUrl", "videoExtensions", "lowerUrl", "ext", "getVideoThumbnail", "videoUrl", "sampleVideo", "sampleVideos", "find", "v", "thumbnail", "getVideoDuration", "duration", "processMediaItems", "mediaArray", "Array", "isArray", "map", "media", "index", "isVideo", "_id", "thumbnailUrl", "undefined", "alt", "aspectRatio", "size", "enhanceWithSampleVideos", "videoCount", "contentHint", "processedMedia", "needsVideoEnhancement", "length", "videosToAdd", "Math", "min", "i", "getVideoByContentHint", "push", "title", "hint", "fallbackIndex", "lowerHint", "getVideoByType", "error", "currentErrors", "value", "next", "clearMediaErrors", "preloadMedia", "mediaItems", "promises", "Promise", "resolve", "Image", "onload", "onerror", "video", "document", "createElement", "onloadeddata", "load", "all", "factory", "ɵfac", "providedIn"], "sources": ["E:\\DFashion\\frontend\\src\\app\\core\\services\\media.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\n\nexport interface MediaItem {\n  id: string;\n  type: 'image' | 'video';\n  url: string;\n  thumbnailUrl?: string;\n  alt?: string;\n  duration?: number; // for videos in seconds\n  aspectRatio?: number;\n  size?: number; // file size in bytes\n}\n\nexport interface MediaError {\n  id: string;\n  type: 'load_error' | 'network_error' | 'format_error';\n  message: string;\n  fallbackUrl?: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class MediaService {\n  private mediaErrors = new BehaviorSubject<MediaError[]>([]);\n  public mediaErrors$ = this.mediaErrors.asObservable();\n\n  // Fallback images for different scenarios\n  private readonly fallbackImages = {\n    user: '/assets/images/default-avatar.svg',\n    product: '/assets/images/default-product.svg',\n    post: '/assets/images/default-post.svg',\n    story: '/assets/images/default-story.svg'\n  };\n\n  // Backup fallback images (simple colored placeholders)\n  private readonly backupFallbacks = {\n    user: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzLjk5IDcuMDEgMTUuNjIgNiAxOEMxMC4wMSAyMCAxMy45OSAyMCAxOCAxOEMxNi45OSAxNS42MiAxNC42NyAxMy45OSAxMiAxNFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+Cjwvc3ZnPgo=',\n    product: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+',\n    post: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkJGQ0ZEIi8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNFNUU3RUIiLz4KPC9zdmc+',\n    story: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkVGM0Y0Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNGQ0E1QTUiLz4KPC9zdmc+'\n  };\n\n  // Video library - loaded from API\n  private readonly videos: any[] = [];\n\n  // Broken URL patterns to fix\n  private readonly brokenUrlPatterns = [\n    '/uploads/stories/images/',\n    '/uploads/stories/videos/',\n    'sample-videos.com',\n    'localhost:4200/assets/',\n    'file://'\n  ];\n\n  constructor() {}\n\n  /**\n   * Get a reliable fallback image that always works\n   */\n  getReliableFallback(type: 'user' | 'product' | 'post' | 'story' = 'post'): string {\n    return this.backupFallbacks[type];\n  }\n\n  /**\n   * Check if an image URL is likely to fail\n   */\n  isLikelyToFail(url: string): boolean {\n    return this.isExternalImageUrl(url) || this.isBrokenUrl(url);\n  }\n\n  /**\n   * Get a safe image URL with fallback handling and broken URL fixing\n   */\n  getSafeImageUrl(url: string | undefined, type: 'user' | 'product' | 'post' | 'story' = 'post'): string {\n    if (!url || url.trim() === '') {\n      return this.backupFallbacks[type]; // Use base64 fallback for empty URLs\n    }\n\n    // Fix broken URLs\n    const fixedUrl = this.fixBrokenUrl(url, type);\n    if (fixedUrl !== url) {\n      return fixedUrl;\n    }\n\n    // Handle localhost URLs that might be broken\n    if (url.includes('localhost:4200/assets/')) {\n      const assetPath = url.split('localhost:4200')[1];\n      return assetPath;\n    }\n\n    // For external images that might fail, provide a more reliable fallback\n    if (this.isExternalImageUrl(url)) {\n      // Return the URL but we know it might fail and will fallback gracefully\n      return url;\n    }\n\n    // Check if URL is valid\n    try {\n      new URL(url);\n      return url;\n    } catch {\n      // If not a valid URL, treat as relative path\n      if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {\n        return url;\n      }\n      // Return base64 fallback for invalid URLs\n      return this.backupFallbacks[type];\n    }\n  }\n\n  /**\n   * Fix broken URLs by replacing them with working alternatives\n   */\n  private fixBrokenUrl(url: string, type: 'user' | 'product' | 'post' | 'story'): string {\n    // Check for broken patterns\n    for (const pattern of this.brokenUrlPatterns) {\n      if (url.includes(pattern)) {\n        // Replace with appropriate fallback or working URL\n        if (pattern === '/uploads/stories/images/' || pattern === '/uploads/stories/videos/') {\n          return this.getReplacementMediaUrl(url, type);\n        }\n        if (pattern === 'sample-videos.com') {\n          return this.getReliableFallback(type);\n        }\n        if (pattern === 'localhost:4200/assets/') {\n          // Extract the asset path and return it as relative\n          const assetPath = url.split('localhost:4200')[1];\n          return assetPath;\n        }\n        if (pattern === 'file://') {\n          return this.fallbackImages[type];\n        }\n      }\n    }\n    return url;\n  }\n\n  /**\n   * Get replacement media URL for broken local paths\n   */\n  private getReplacementMediaUrl(originalUrl: string, type: 'user' | 'product' | 'post' | 'story'): string {\n    // Map broken local URLs to working Unsplash URLs based on content\n    const urlMappings: { [key: string]: string } = {\n      'summer-collection': 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=800',\n      'behind-scenes': 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800',\n      'customer-spotlight': 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=800',\n      'styling-tips': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',\n      'design': 'https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=800'\n    };\n\n    // Try to match content from filename\n    for (const [key, replacementUrl] of Object.entries(urlMappings)) {\n      if (originalUrl.toLowerCase().includes(key)) {\n        return replacementUrl;\n      }\n    }\n\n    // Return appropriate fallback\n    return this.fallbackImages[type];\n  }\n\n  /**\n   * Handle image load errors with progressive fallback\n   */\n  handleImageError(event: Event, fallbackType: 'user' | 'product' | 'post' | 'story' = 'post'): void {\n    const img = event.target as HTMLImageElement;\n    if (!img) return;\n\n    const originalSrc = img.src;\n\n    // First try: Use SVG fallback from assets\n    if (!originalSrc.includes(this.fallbackImages[fallbackType]) && !originalSrc.startsWith('data:')) {\n      img.src = this.fallbackImages[fallbackType];\n\n      // Only log meaningful errors (not external image failures)\n      if (!this.isExternalImageUrl(originalSrc) && !originalSrc.includes('localhost:4200')) {\n        this.logMediaError({\n          id: originalSrc,\n          type: 'load_error',\n          message: `Failed to load image: ${originalSrc}`,\n          fallbackUrl: this.fallbackImages[fallbackType]\n        });\n      }\n      return;\n    }\n\n    // Second try: Use base64 backup fallback if SVG also fails\n    if (originalSrc.includes(this.fallbackImages[fallbackType])) {\n      img.src = this.backupFallbacks[fallbackType];\n      // Only warn for local asset failures, not external\n      if (!this.isExternalImageUrl(originalSrc)) {\n        console.warn(`SVG fallback failed, using backup for ${fallbackType}:`, originalSrc);\n      }\n      return;\n    }\n  }\n\n  /**\n   * Check if URL is an external image (Unsplash, etc.)\n   */\n  private isExternalImageUrl(url: string): boolean {\n    const externalDomains = [\n      'unsplash.com',\n      'images.unsplash.com',\n      'picsum.photos',\n      'via.placeholder.com',\n      'placehold.it',\n      'placeholder.com'\n    ];\n\n    return externalDomains.some(domain => url.includes(domain));\n  }\n\n\n\n\n\n\n\n  /**\n   * Check if URL is a video\n   */\n  isVideoUrl(url: string): boolean {\n    if (!url) return false;\n    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi'];\n    const lowerUrl = url.toLowerCase();\n    return videoExtensions.some(ext => lowerUrl.includes(ext)) || \n           lowerUrl.includes('video') || \n           lowerUrl.includes('.mp4');\n  }\n\n  /**\n   * Get video thumbnail\n   */\n  getVideoThumbnail(videoUrl: string): string {\n    // Check if it's one of our sample videos\n    const sampleVideo = this.sampleVideos.find(v => v.url === videoUrl);\n    if (sampleVideo) {\n      return sampleVideo.thumbnail;\n    }\n\n    // For other videos, try to generate thumbnail URL or use fallback\n    return this.fallbackImages.post;\n  }\n\n  /**\n   * Get video duration\n   */\n  getVideoDuration(videoUrl: string): number {\n    const sampleVideo = this.sampleVideos.find(v => v.url === videoUrl);\n    return sampleVideo ? sampleVideo.duration : 30; // Default 30 seconds\n  }\n\n  /**\n   * Process media items from database\n   */\n  processMediaItems(mediaArray: any[]): MediaItem[] {\n    if (!mediaArray || !Array.isArray(mediaArray)) {\n      return [];\n    }\n\n    return mediaArray.map((media, index) => {\n      const isVideo = this.isVideoUrl(media.url);\n      \n      return {\n        id: media._id || `media_${index}`,\n        type: isVideo ? 'video' : 'image',\n        url: this.getSafeImageUrl(media.url),\n        thumbnailUrl: isVideo ? this.getVideoThumbnail(media.url) : undefined,\n        alt: media.alt || '',\n        duration: isVideo ? this.getVideoDuration(media.url) : undefined,\n        aspectRatio: media.aspectRatio || (isVideo ? 16/9 : 1),\n        size: media.size\n      };\n    });\n  }\n\n  /**\n   * Add sample videos to existing media array with intelligent content matching\n   */\n  enhanceWithSampleVideos(mediaArray: any[], videoCount: number = 2, contentHint?: string): MediaItem[] {\n    const processedMedia = this.processMediaItems(mediaArray);\n\n    // Add sample videos if we don't have enough media or if videos are broken\n    const needsVideoEnhancement = processedMedia.length < 3 ||\n      processedMedia.some(media => media.type === 'video' && this.isBrokenUrl(media.url));\n\n    if (needsVideoEnhancement) {\n      const videosToAdd = Math.min(videoCount, this.sampleVideos.length);\n\n      for (let i = 0; i < videosToAdd; i++) {\n        // Try to match video content to the hint\n        let sampleVideo;\n        if (contentHint) {\n          sampleVideo = this.getVideoByContentHint(contentHint, i);\n        } else {\n          sampleVideo = this.sampleVideos[i];\n        }\n\n        processedMedia.push({\n          id: `enhanced_video_${i}`,\n          type: 'video',\n          url: sampleVideo.url,\n          thumbnailUrl: sampleVideo.thumbnail,\n          alt: sampleVideo.title || `Enhanced video ${i + 1}`,\n          duration: sampleVideo.duration,\n          aspectRatio: 16/9\n        });\n      }\n    }\n\n    return processedMedia;\n  }\n\n  /**\n   * Get video based on content hint for better matching\n   */\n  private getVideoByContentHint(hint: string, fallbackIndex: number = 0): any {\n    const lowerHint = hint.toLowerCase();\n\n    if (lowerHint.includes('fashion') || lowerHint.includes('style')) {\n      return this.getVideoByType('fashion');\n    }\n    if (lowerHint.includes('tutorial') || lowerHint.includes('tips')) {\n      return this.getVideoByType('tutorial');\n    }\n    if (lowerHint.includes('showcase') || lowerHint.includes('collection')) {\n      return this.getVideoByType('showcase');\n    }\n    if (lowerHint.includes('story') || lowerHint.includes('behind')) {\n      return this.getVideoByType('story');\n    }\n\n    return this.sampleVideos[fallbackIndex] || this.sampleVideos[0];\n  }\n\n  /**\n   * Check if URL is broken\n   */\n  private isBrokenUrl(url: string): boolean {\n    return this.brokenUrlPatterns.some(pattern => url.includes(pattern));\n  }\n\n  /**\n   * Log media errors for debugging (with smart filtering)\n   */\n  private logMediaError(error: MediaError): void {\n    const currentErrors = this.mediaErrors.value;\n    this.mediaErrors.next([...currentErrors, error]);\n\n    // Only log to console if it's not an external image failure\n    if (!this.isExternalImageUrl(error.id)) {\n      console.warn('Media Error:', error);\n    }\n  }\n\n  /**\n   * Clear media errors\n   */\n  clearMediaErrors(): void {\n    this.mediaErrors.next([]);\n  }\n\n  /**\n   * Preload media for better performance with graceful error handling\n   */\n  preloadMedia(mediaItems: MediaItem[]): Promise<void[]> {\n    const promises = mediaItems.map(media => {\n      return new Promise<void>((resolve) => {\n        if (media.type === 'image') {\n          const img = new Image();\n          img.onload = () => resolve();\n          img.onerror = () => {\n            // Only log errors for non-external images\n            if (!this.isExternalImageUrl(media.url)) {\n              console.warn(`Failed to preload image: ${media.url}`);\n            }\n            resolve(); // Resolve anyway to not break the promise chain\n          };\n          img.src = media.url;\n        } else if (media.type === 'video') {\n          const video = document.createElement('video');\n          video.onloadeddata = () => resolve();\n          video.onerror = () => {\n            // Only log errors for non-external videos\n            if (!this.isExternalImageUrl(media.url)) {\n              console.warn(`Failed to preload video: ${media.url}`);\n            }\n            resolve(); // Resolve anyway to not break the promise chain\n          };\n          video.src = media.url;\n          video.load();\n        } else {\n          resolve(); // Unknown type, just resolve\n        }\n      });\n    });\n\n    return Promise.all(promises);\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AAuBtC,OAAM,MAAOC,YAAY;EAgCvBC,YAAA;IA/BQ,KAAAC,WAAW,GAAG,IAAIH,eAAe,CAAe,EAAE,CAAC;IACpD,KAAAI,YAAY,GAAG,IAAI,CAACD,WAAW,CAACE,YAAY,EAAE;IAErD;IACiB,KAAAC,cAAc,GAAG;MAChCC,IAAI,EAAE,mCAAmC;MACzCC,OAAO,EAAE,oCAAoC;MAC7CC,IAAI,EAAE,iCAAiC;MACvCC,KAAK,EAAE;KACR;IAED;IACiB,KAAAC,eAAe,GAAG;MACjCJ,IAAI,EAAE,osBAAosB;MAC1sBC,OAAO,EAAE,4uBAA4uB;MACrvBC,IAAI,EAAE,4uBAA4uB;MAClvBC,KAAK,EAAE;KACR;IAED;IACiB,KAAAE,MAAM,GAAU,EAAE;IAEnC;IACiB,KAAAC,iBAAiB,GAAG,CACnC,0BAA0B,EAC1B,0BAA0B,EAC1B,mBAAmB,EACnB,wBAAwB,EACxB,SAAS,CACV;EAEc;EAEf;;;EAGAC,mBAAmBA,CAACC,IAAA,GAA8C,MAAM;IACtE,OAAO,IAAI,CAACJ,eAAe,CAACI,IAAI,CAAC;EACnC;EAEA;;;EAGAC,cAAcA,CAACC,GAAW;IACxB,OAAO,IAAI,CAACC,kBAAkB,CAACD,GAAG,CAAC,IAAI,IAAI,CAACE,WAAW,CAACF,GAAG,CAAC;EAC9D;EAEA;;;EAGAG,eAAeA,CAACH,GAAuB,EAAEF,IAAA,GAA8C,MAAM;IAC3F,IAAI,CAACE,GAAG,IAAIA,GAAG,CAACI,IAAI,EAAE,KAAK,EAAE,EAAE;MAC7B,OAAO,IAAI,CAACV,eAAe,CAACI,IAAI,CAAC,CAAC,CAAC;;IAGrC;IACA,MAAMO,QAAQ,GAAG,IAAI,CAACC,YAAY,CAACN,GAAG,EAAEF,IAAI,CAAC;IAC7C,IAAIO,QAAQ,KAAKL,GAAG,EAAE;MACpB,OAAOK,QAAQ;;IAGjB;IACA,IAAIL,GAAG,CAACO,QAAQ,CAAC,wBAAwB,CAAC,EAAE;MAC1C,MAAMC,SAAS,GAAGR,GAAG,CAACS,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAChD,OAAOD,SAAS;;IAGlB;IACA,IAAI,IAAI,CAACP,kBAAkB,CAACD,GAAG,CAAC,EAAE;MAChC;MACA,OAAOA,GAAG;;IAGZ;IACA,IAAI;MACF,IAAIU,GAAG,CAACV,GAAG,CAAC;MACZ,OAAOA,GAAG;KACX,CAAC,MAAM;MACN;MACA,IAAIA,GAAG,CAACW,UAAU,CAAC,GAAG,CAAC,IAAIX,GAAG,CAACW,UAAU,CAAC,IAAI,CAAC,IAAIX,GAAG,CAACW,UAAU,CAAC,KAAK,CAAC,EAAE;QACxE,OAAOX,GAAG;;MAEZ;MACA,OAAO,IAAI,CAACN,eAAe,CAACI,IAAI,CAAC;;EAErC;EAEA;;;EAGQQ,YAAYA,CAACN,GAAW,EAAEF,IAA2C;IAC3E;IACA,KAAK,MAAMc,OAAO,IAAI,IAAI,CAAChB,iBAAiB,EAAE;MAC5C,IAAII,GAAG,CAACO,QAAQ,CAACK,OAAO,CAAC,EAAE;QACzB;QACA,IAAIA,OAAO,KAAK,0BAA0B,IAAIA,OAAO,KAAK,0BAA0B,EAAE;UACpF,OAAO,IAAI,CAACC,sBAAsB,CAACb,GAAG,EAAEF,IAAI,CAAC;;QAE/C,IAAIc,OAAO,KAAK,mBAAmB,EAAE;UACnC,OAAO,IAAI,CAACf,mBAAmB,CAACC,IAAI,CAAC;;QAEvC,IAAIc,OAAO,KAAK,wBAAwB,EAAE;UACxC;UACA,MAAMJ,SAAS,GAAGR,GAAG,CAACS,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;UAChD,OAAOD,SAAS;;QAElB,IAAII,OAAO,KAAK,SAAS,EAAE;UACzB,OAAO,IAAI,CAACvB,cAAc,CAACS,IAAI,CAAC;;;;IAItC,OAAOE,GAAG;EACZ;EAEA;;;EAGQa,sBAAsBA,CAACC,WAAmB,EAAEhB,IAA2C;IAC7F;IACA,MAAMiB,WAAW,GAA8B;MAC7C,mBAAmB,EAAE,oEAAoE;MACzF,eAAe,EAAE,oEAAoE;MACrF,oBAAoB,EAAE,oEAAoE;MAC1F,cAAc,EAAE,oEAAoE;MACpF,QAAQ,EAAE;KACX;IAED;IACA,KAAK,MAAM,CAACC,GAAG,EAAEC,cAAc,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACJ,WAAW,CAAC,EAAE;MAC/D,IAAID,WAAW,CAACM,WAAW,EAAE,CAACb,QAAQ,CAACS,GAAG,CAAC,EAAE;QAC3C,OAAOC,cAAc;;;IAIzB;IACA,OAAO,IAAI,CAAC5B,cAAc,CAACS,IAAI,CAAC;EAClC;EAEA;;;EAGAuB,gBAAgBA,CAACC,KAAY,EAAEC,YAAA,GAAsD,MAAM;IACzF,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAA0B;IAC5C,IAAI,CAACD,GAAG,EAAE;IAEV,MAAME,WAAW,GAAGF,GAAG,CAACG,GAAG;IAE3B;IACA,IAAI,CAACD,WAAW,CAACnB,QAAQ,CAAC,IAAI,CAAClB,cAAc,CAACkC,YAAY,CAAC,CAAC,IAAI,CAACG,WAAW,CAACf,UAAU,CAAC,OAAO,CAAC,EAAE;MAChGa,GAAG,CAACG,GAAG,GAAG,IAAI,CAACtC,cAAc,CAACkC,YAAY,CAAC;MAE3C;MACA,IAAI,CAAC,IAAI,CAACtB,kBAAkB,CAACyB,WAAW,CAAC,IAAI,CAACA,WAAW,CAACnB,QAAQ,CAAC,gBAAgB,CAAC,EAAE;QACpF,IAAI,CAACqB,aAAa,CAAC;UACjBC,EAAE,EAAEH,WAAW;UACf5B,IAAI,EAAE,YAAY;UAClBgC,OAAO,EAAE,yBAAyBJ,WAAW,EAAE;UAC/CK,WAAW,EAAE,IAAI,CAAC1C,cAAc,CAACkC,YAAY;SAC9C,CAAC;;MAEJ;;IAGF;IACA,IAAIG,WAAW,CAACnB,QAAQ,CAAC,IAAI,CAAClB,cAAc,CAACkC,YAAY,CAAC,CAAC,EAAE;MAC3DC,GAAG,CAACG,GAAG,GAAG,IAAI,CAACjC,eAAe,CAAC6B,YAAY,CAAC;MAC5C;MACA,IAAI,CAAC,IAAI,CAACtB,kBAAkB,CAACyB,WAAW,CAAC,EAAE;QACzCM,OAAO,CAACC,IAAI,CAAC,yCAAyCV,YAAY,GAAG,EAAEG,WAAW,CAAC;;MAErF;;EAEJ;EAEA;;;EAGQzB,kBAAkBA,CAACD,GAAW;IACpC,MAAMkC,eAAe,GAAG,CACtB,cAAc,EACd,qBAAqB,EACrB,eAAe,EACf,qBAAqB,EACrB,cAAc,EACd,iBAAiB,CAClB;IAED,OAAOA,eAAe,CAACC,IAAI,CAACC,MAAM,IAAIpC,GAAG,CAACO,QAAQ,CAAC6B,MAAM,CAAC,CAAC;EAC7D;EAQA;;;EAGAC,UAAUA,CAACrC,GAAW;IACpB,IAAI,CAACA,GAAG,EAAE,OAAO,KAAK;IACtB,MAAMsC,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACjE,MAAMC,QAAQ,GAAGvC,GAAG,CAACoB,WAAW,EAAE;IAClC,OAAOkB,eAAe,CAACH,IAAI,CAACK,GAAG,IAAID,QAAQ,CAAChC,QAAQ,CAACiC,GAAG,CAAC,CAAC,IACnDD,QAAQ,CAAChC,QAAQ,CAAC,OAAO,CAAC,IAC1BgC,QAAQ,CAAChC,QAAQ,CAAC,MAAM,CAAC;EAClC;EAEA;;;EAGAkC,iBAAiBA,CAACC,QAAgB;IAChC;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,YAAY,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9C,GAAG,KAAK0C,QAAQ,CAAC;IACnE,IAAIC,WAAW,EAAE;MACf,OAAOA,WAAW,CAACI,SAAS;;IAG9B;IACA,OAAO,IAAI,CAAC1D,cAAc,CAACG,IAAI;EACjC;EAEA;;;EAGAwD,gBAAgBA,CAACN,QAAgB;IAC/B,MAAMC,WAAW,GAAG,IAAI,CAACC,YAAY,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9C,GAAG,KAAK0C,QAAQ,CAAC;IACnE,OAAOC,WAAW,GAAGA,WAAW,CAACM,QAAQ,GAAG,EAAE,CAAC,CAAC;EAClD;EAEA;;;EAGAC,iBAAiBA,CAACC,UAAiB;IACjC,IAAI,CAACA,UAAU,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;MAC7C,OAAO,EAAE;;IAGX,OAAOA,UAAU,CAACG,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAI;MACrC,MAAMC,OAAO,GAAG,IAAI,CAACpB,UAAU,CAACkB,KAAK,CAACvD,GAAG,CAAC;MAE1C,OAAO;QACL6B,EAAE,EAAE0B,KAAK,CAACG,GAAG,IAAI,SAASF,KAAK,EAAE;QACjC1D,IAAI,EAAE2D,OAAO,GAAG,OAAO,GAAG,OAAO;QACjCzD,GAAG,EAAE,IAAI,CAACG,eAAe,CAACoD,KAAK,CAACvD,GAAG,CAAC;QACpC2D,YAAY,EAAEF,OAAO,GAAG,IAAI,CAAChB,iBAAiB,CAACc,KAAK,CAACvD,GAAG,CAAC,GAAG4D,SAAS;QACrEC,GAAG,EAAEN,KAAK,CAACM,GAAG,IAAI,EAAE;QACpBZ,QAAQ,EAAEQ,OAAO,GAAG,IAAI,CAACT,gBAAgB,CAACO,KAAK,CAACvD,GAAG,CAAC,GAAG4D,SAAS;QAChEE,WAAW,EAAEP,KAAK,CAACO,WAAW,KAAKL,OAAO,GAAG,EAAE,GAAC,CAAC,GAAG,CAAC,CAAC;QACtDM,IAAI,EAAER,KAAK,CAACQ;OACb;IACH,CAAC,CAAC;EACJ;EAEA;;;EAGAC,uBAAuBA,CAACb,UAAiB,EAAEc,UAAA,GAAqB,CAAC,EAAEC,WAAoB;IACrF,MAAMC,cAAc,GAAG,IAAI,CAACjB,iBAAiB,CAACC,UAAU,CAAC;IAEzD;IACA,MAAMiB,qBAAqB,GAAGD,cAAc,CAACE,MAAM,GAAG,CAAC,IACrDF,cAAc,CAAChC,IAAI,CAACoB,KAAK,IAAIA,KAAK,CAACzD,IAAI,KAAK,OAAO,IAAI,IAAI,CAACI,WAAW,CAACqD,KAAK,CAACvD,GAAG,CAAC,CAAC;IAErF,IAAIoE,qBAAqB,EAAE;MACzB,MAAME,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACP,UAAU,EAAE,IAAI,CAACrB,YAAY,CAACyB,MAAM,CAAC;MAElE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,WAAW,EAAEG,CAAC,EAAE,EAAE;QACpC;QACA,IAAI9B,WAAW;QACf,IAAIuB,WAAW,EAAE;UACfvB,WAAW,GAAG,IAAI,CAAC+B,qBAAqB,CAACR,WAAW,EAAEO,CAAC,CAAC;SACzD,MAAM;UACL9B,WAAW,GAAG,IAAI,CAACC,YAAY,CAAC6B,CAAC,CAAC;;QAGpCN,cAAc,CAACQ,IAAI,CAAC;UAClB9C,EAAE,EAAE,kBAAkB4C,CAAC,EAAE;UACzB3E,IAAI,EAAE,OAAO;UACbE,GAAG,EAAE2C,WAAW,CAAC3C,GAAG;UACpB2D,YAAY,EAAEhB,WAAW,CAACI,SAAS;UACnCc,GAAG,EAAElB,WAAW,CAACiC,KAAK,IAAI,kBAAkBH,CAAC,GAAG,CAAC,EAAE;UACnDxB,QAAQ,EAAEN,WAAW,CAACM,QAAQ;UAC9Ba,WAAW,EAAE,EAAE,GAAC;SACjB,CAAC;;;IAIN,OAAOK,cAAc;EACvB;EAEA;;;EAGQO,qBAAqBA,CAACG,IAAY,EAAEC,aAAA,GAAwB,CAAC;IACnE,MAAMC,SAAS,GAAGF,IAAI,CAACzD,WAAW,EAAE;IAEpC,IAAI2D,SAAS,CAACxE,QAAQ,CAAC,SAAS,CAAC,IAAIwE,SAAS,CAACxE,QAAQ,CAAC,OAAO,CAAC,EAAE;MAChE,OAAO,IAAI,CAACyE,cAAc,CAAC,SAAS,CAAC;;IAEvC,IAAID,SAAS,CAACxE,QAAQ,CAAC,UAAU,CAAC,IAAIwE,SAAS,CAACxE,QAAQ,CAAC,MAAM,CAAC,EAAE;MAChE,OAAO,IAAI,CAACyE,cAAc,CAAC,UAAU,CAAC;;IAExC,IAAID,SAAS,CAACxE,QAAQ,CAAC,UAAU,CAAC,IAAIwE,SAAS,CAACxE,QAAQ,CAAC,YAAY,CAAC,EAAE;MACtE,OAAO,IAAI,CAACyE,cAAc,CAAC,UAAU,CAAC;;IAExC,IAAID,SAAS,CAACxE,QAAQ,CAAC,OAAO,CAAC,IAAIwE,SAAS,CAACxE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC/D,OAAO,IAAI,CAACyE,cAAc,CAAC,OAAO,CAAC;;IAGrC,OAAO,IAAI,CAACpC,YAAY,CAACkC,aAAa,CAAC,IAAI,IAAI,CAAClC,YAAY,CAAC,CAAC,CAAC;EACjE;EAEA;;;EAGQ1C,WAAWA,CAACF,GAAW;IAC7B,OAAO,IAAI,CAACJ,iBAAiB,CAACuC,IAAI,CAACvB,OAAO,IAAIZ,GAAG,CAACO,QAAQ,CAACK,OAAO,CAAC,CAAC;EACtE;EAEA;;;EAGQgB,aAAaA,CAACqD,KAAiB;IACrC,MAAMC,aAAa,GAAG,IAAI,CAAChG,WAAW,CAACiG,KAAK;IAC5C,IAAI,CAACjG,WAAW,CAACkG,IAAI,CAAC,CAAC,GAAGF,aAAa,EAAED,KAAK,CAAC,CAAC;IAEhD;IACA,IAAI,CAAC,IAAI,CAAChF,kBAAkB,CAACgF,KAAK,CAACpD,EAAE,CAAC,EAAE;MACtCG,OAAO,CAACC,IAAI,CAAC,cAAc,EAAEgD,KAAK,CAAC;;EAEvC;EAEA;;;EAGAI,gBAAgBA,CAAA;IACd,IAAI,CAACnG,WAAW,CAACkG,IAAI,CAAC,EAAE,CAAC;EAC3B;EAEA;;;EAGAE,YAAYA,CAACC,UAAuB;IAClC,MAAMC,QAAQ,GAAGD,UAAU,CAACjC,GAAG,CAACC,KAAK,IAAG;MACtC,OAAO,IAAIkC,OAAO,CAAQC,OAAO,IAAI;QACnC,IAAInC,KAAK,CAACzD,IAAI,KAAK,OAAO,EAAE;UAC1B,MAAM0B,GAAG,GAAG,IAAImE,KAAK,EAAE;UACvBnE,GAAG,CAACoE,MAAM,GAAG,MAAMF,OAAO,EAAE;UAC5BlE,GAAG,CAACqE,OAAO,GAAG,MAAK;YACjB;YACA,IAAI,CAAC,IAAI,CAAC5F,kBAAkB,CAACsD,KAAK,CAACvD,GAAG,CAAC,EAAE;cACvCgC,OAAO,CAACC,IAAI,CAAC,4BAA4BsB,KAAK,CAACvD,GAAG,EAAE,CAAC;;YAEvD0F,OAAO,EAAE,CAAC,CAAC;UACb,CAAC;UACDlE,GAAG,CAACG,GAAG,GAAG4B,KAAK,CAACvD,GAAG;SACpB,MAAM,IAAIuD,KAAK,CAACzD,IAAI,KAAK,OAAO,EAAE;UACjC,MAAMgG,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;UAC7CF,KAAK,CAACG,YAAY,GAAG,MAAMP,OAAO,EAAE;UACpCI,KAAK,CAACD,OAAO,GAAG,MAAK;YACnB;YACA,IAAI,CAAC,IAAI,CAAC5F,kBAAkB,CAACsD,KAAK,CAACvD,GAAG,CAAC,EAAE;cACvCgC,OAAO,CAACC,IAAI,CAAC,4BAA4BsB,KAAK,CAACvD,GAAG,EAAE,CAAC;;YAEvD0F,OAAO,EAAE,CAAC,CAAC;UACb,CAAC;UACDI,KAAK,CAACnE,GAAG,GAAG4B,KAAK,CAACvD,GAAG;UACrB8F,KAAK,CAACI,IAAI,EAAE;SACb,MAAM;UACLR,OAAO,EAAE,CAAC,CAAC;;MAEf,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOD,OAAO,CAACU,GAAG,CAACX,QAAQ,CAAC;EAC9B;;;uBAzXWxG,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAAoH,OAAA,EAAZpH,YAAY,CAAAqH,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}