{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/admin-api.service\";\nimport * as i2 from \"../services/admin-auth.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/progress-spinner\";\nfunction AdminDashboardComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"mat-spinner\", 4);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading dashboard data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AdminDashboardComponent_div_2_mat_card_14_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const stat_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(stat_r3.prefix);\n  }\n}\nfunction AdminDashboardComponent_div_2_mat_card_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 36)(1, \"mat-card-content\")(2, \"div\", 37)(3, \"div\", 38)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 39);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 40)(9, \"div\", 41);\n    i0.ɵɵtemplate(10, AdminDashboardComponent_div_2_mat_card_14_span_10_Template, 2, 1, \"span\", 42);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 43);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const stat_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"border-left-color\", stat_r3.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-color\", stat_r3.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r3.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getChangeClass(stat_r3.changeType));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", stat_r3.change, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", stat_r3.prefix);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", stat_r3.title === \"Revenue\" ? ctx_r1.formatCurrency(stat_r3.value) : ctx_r1.formatNumber(stat_r3.value), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r3.title);\n  }\n}\nfunction AdminDashboardComponent_div_2_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 46)(5, \"div\", 47);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 48);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const activity_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", activity_r4.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r4.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r4.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r4.time);\n  }\n}\nfunction AdminDashboardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"h1\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Here's what's happening with your business today.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 8)(9, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function AdminDashboardComponent_div_2_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.refreshData());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Refresh Data \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 10);\n    i0.ɵɵtemplate(14, AdminDashboardComponent_div_2_mat_card_14_Template, 14, 10, \"mat-card\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 13)(17, \"mat-card\", 14)(18, \"mat-card-header\")(19, \"mat-card-title\");\n    i0.ɵɵtext(20, \"User Growth\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"mat-card-subtitle\");\n    i0.ɵɵtext(22, \"Monthly new user registrations\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"mat-card-content\")(24, \"div\", 15)(25, \"div\", 16)(26, \"mat-icon\");\n    i0.ɵɵtext(27, \"show_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"h3\");\n    i0.ɵɵtext(29, \"User Growth Chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"p\");\n    i0.ɵɵtext(31, \"Chart visualization will be displayed here\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 17)(33, \"small\");\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(35, \"mat-card\", 14)(36, \"mat-card-header\")(37, \"mat-card-title\");\n    i0.ɵɵtext(38, \"Order Trends\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"mat-card-subtitle\");\n    i0.ɵɵtext(40, \"Daily orders this week\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"mat-card-content\")(42, \"div\", 15)(43, \"div\", 16)(44, \"mat-icon\");\n    i0.ɵɵtext(45, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"h3\");\n    i0.ɵɵtext(47, \"Order Trends Chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"p\");\n    i0.ɵɵtext(49, \"Chart visualization will be displayed here\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 17)(51, \"small\");\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(53, \"mat-card\", 14)(54, \"mat-card-header\")(55, \"mat-card-title\");\n    i0.ɵɵtext(56, \"Revenue Distribution\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"mat-card-subtitle\");\n    i0.ɵɵtext(58, \"Revenue by category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(59, \"mat-card-content\")(60, \"div\", 15)(61, \"div\", 16)(62, \"mat-icon\");\n    i0.ɵɵtext(63, \"donut_large\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"h3\");\n    i0.ɵɵtext(65, \"Revenue Distribution Chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"p\");\n    i0.ɵɵtext(67, \"Chart visualization will be displayed here\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"div\", 17)(69, \"small\");\n    i0.ɵɵtext(70);\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵelementStart(71, \"div\", 18)(72, \"mat-card\", 19)(73, \"mat-card-header\")(74, \"mat-card-title\");\n    i0.ɵɵtext(75, \"Recent Activities\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"mat-card-subtitle\");\n    i0.ɵɵtext(77, \"Latest system activities\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(78, \"mat-card-content\")(79, \"div\", 20);\n    i0.ɵɵtemplate(80, AdminDashboardComponent_div_2_div_80_Template, 9, 5, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"div\", 22)(82, \"button\", 23);\n    i0.ɵɵtext(83, \"View All Activities\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(84, \"mat-card\", 24)(85, \"mat-card-header\")(86, \"mat-card-title\");\n    i0.ɵɵtext(87, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"mat-card-subtitle\");\n    i0.ɵɵtext(89, \"Common administrative tasks\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(90, \"mat-card-content\")(91, \"div\", 25)(92, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function AdminDashboardComponent_div_2_Template_button_click_92_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToSection(\"users\"));\n    });\n    i0.ɵɵelementStart(93, \"mat-icon\");\n    i0.ɵɵtext(94, \"person_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(95, \"span\");\n    i0.ɵɵtext(96, \"Add User\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(97, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function AdminDashboardComponent_div_2_Template_button_click_97_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToSection(\"products\"));\n    });\n    i0.ɵɵelementStart(98, \"mat-icon\");\n    i0.ɵɵtext(99, \"add_box\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"span\");\n    i0.ɵɵtext(101, \"Add Product\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(102, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function AdminDashboardComponent_div_2_Template_button_click_102_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToSection(\"orders\"));\n    });\n    i0.ɵɵelementStart(103, \"mat-icon\");\n    i0.ɵɵtext(104, \"list_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(105, \"span\");\n    i0.ɵɵtext(106, \"View Orders\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(107, \"button\", 27)(108, \"mat-icon\");\n    i0.ɵɵtext(109, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(110, \"span\");\n    i0.ɵɵtext(111, \"View Reports\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(112, \"button\", 27)(113, \"mat-icon\");\n    i0.ɵɵtext(114, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(115, \"span\");\n    i0.ɵɵtext(116, \"Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(117, \"button\", 27)(118, \"mat-icon\");\n    i0.ɵɵtext(119, \"help\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(120, \"span\");\n    i0.ɵɵtext(121, \"Help Center\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(122, \"mat-card\", 28)(123, \"mat-card-header\")(124, \"mat-card-title\");\n    i0.ɵɵtext(125, \"System Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(126, \"mat-card-subtitle\");\n    i0.ɵɵtext(127, \"Current system health and performance\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(128, \"mat-card-content\")(129, \"div\", 29)(130, \"div\", 30);\n    i0.ɵɵelement(131, \"div\", 31);\n    i0.ɵɵelementStart(132, \"div\", 32)(133, \"div\", 33);\n    i0.ɵɵtext(134, \"API Server\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(135, \"div\", 34);\n    i0.ɵɵtext(136, \"Online\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(137, \"div\", 30);\n    i0.ɵɵelement(138, \"div\", 31);\n    i0.ɵɵelementStart(139, \"div\", 32)(140, \"div\", 33);\n    i0.ɵɵtext(141, \"Database\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(142, \"div\", 34);\n    i0.ɵɵtext(143, \"Connected\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(144, \"div\", 30);\n    i0.ɵɵelement(145, \"div\", 35);\n    i0.ɵɵelementStart(146, \"div\", 32)(147, \"div\", 33);\n    i0.ɵɵtext(148, \"Storage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(149, \"div\", 34);\n    i0.ɵɵtext(150, \"85% Used\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(151, \"div\", 30);\n    i0.ɵɵelement(152, \"div\", 31);\n    i0.ɵɵelementStart(153, \"div\", 32)(154, \"div\", 33);\n    i0.ɵɵtext(155, \"Payment Gateway\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(156, \"div\", 34);\n    i0.ɵɵtext(157, \"Active\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Welcome back, \", (tmp_1_0 = i0.ɵɵpipeBind1(5, 6, ctx_r1.currentUser$)) == null ? null : tmp_1_0.fullName, \"!\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.quickStats);\n    i0.ɵɵadvance(20);\n    i0.ɵɵtextInterpolate1(\"Sample data: \", ctx_r1.userGrowthChartData.data.join(\", \"), \"\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate1(\"Sample data: \", ctx_r1.orderTrendsChartData.data.join(\", \"), \"\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate1(\"Sample data: \", ctx_r1.revenueChartData.data.join(\", \"), \"\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.recentActivities);\n  }\n}\n// Chart.js imports removed - using simple chart placeholders instead\nexport class AdminDashboardComponent {\n  constructor(apiService, authService) {\n    this.apiService = apiService;\n    this.authService = authService;\n    this.destroy$ = new Subject();\n    this.isLoading = true;\n    this.dashboardStats = null;\n    this.currentUser$ = this.authService.currentUser$;\n    // Chart data (simplified without Chart.js dependency)\n    this.userGrowthChartData = {\n      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],\n      data: [65, 78, 90, 81, 95, 105]\n    };\n    this.orderTrendsChartData = {\n      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n      data: [12, 19, 15, 25, 22, 30, 28]\n    };\n    this.revenueChartData = {\n      labels: ['Products', 'Services', 'Subscriptions'],\n      data: [65, 25, 10]\n    };\n    // Chart options for template compatibility\n    this.chartOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          position: 'bottom'\n        }\n      }\n    };\n    // Quick stats cards\n    this.quickStats = [{\n      title: 'Total Users',\n      value: 0,\n      change: '+12%',\n      changeType: 'positive',\n      icon: 'people',\n      color: '#2196f3'\n    }, {\n      title: 'Active Products',\n      value: 0,\n      change: '+8%',\n      changeType: 'positive',\n      icon: 'inventory',\n      color: '#4caf50'\n    }, {\n      title: 'Total Orders',\n      value: 0,\n      change: '+15%',\n      changeType: 'positive',\n      icon: 'shopping_cart',\n      color: '#ff9800'\n    }, {\n      title: 'Revenue',\n      value: 0,\n      change: '+23%',\n      changeType: 'positive',\n      icon: 'attach_money',\n      color: '#9c27b0',\n      prefix: '₹'\n    }];\n    // Recent activities\n    this.recentActivities = [{\n      type: 'order',\n      message: 'New order #DF12345 received',\n      time: '2 minutes ago',\n      icon: 'shopping_cart',\n      color: '#4caf50'\n    }, {\n      type: 'user',\n      message: 'New user registration: John Doe',\n      time: '5 minutes ago',\n      icon: 'person_add',\n      color: '#2196f3'\n    }, {\n      type: 'product',\n      message: 'Product \"Summer Dress\" approved',\n      time: '10 minutes ago',\n      icon: 'check_circle',\n      color: '#4caf50'\n    }, {\n      type: 'payment',\n      message: 'Payment of ₹2,500 received',\n      time: '15 minutes ago',\n      icon: 'payment',\n      color: '#9c27b0'\n    }];\n  }\n  ngOnInit() {\n    this.loadDashboardData();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadDashboardData() {\n    this.isLoading = true;\n    this.apiService.getDashboardStats().pipe(takeUntil(this.destroy$), finalize(() => this.isLoading = false)).subscribe({\n      next: stats => {\n        this.dashboardStats = stats;\n        this.updateQuickStats(stats);\n        this.updateCharts(stats);\n      },\n      error: error => {\n        console.error('Failed to load dashboard data:', error);\n        // Initialize empty dashboard data\n        this.dashboardStats = null;\n      }\n    });\n  }\n  updateQuickStats(stats) {\n    this.quickStats[0].value = stats.overview.users.total;\n    this.quickStats[1].value = stats.overview.products.active;\n    this.quickStats[2].value = stats.overview.orders.total;\n    this.quickStats[3].value = stats.revenue.totalRevenue;\n  }\n  updateCharts(stats) {\n    // Update chart data with actual stats\n    this.userGrowthChartData.data = [65, 78, 90, 81, 95, 105];\n    this.orderTrendsChartData.data = [12, 19, 15, 25, 22, 30, 28];\n    this.revenueChartData.data = [65, 25, 10];\n  }\n  loadMockData() {\n    // Load real data from API - empty for now\n    this.quickStats[0].value = 0;\n    this.quickStats[1].value = 0;\n    this.quickStats[2].value = 0;\n    this.quickStats[3].value = 0;\n    // Update charts with empty data\n    this.userGrowthChartData.data = [];\n    this.orderTrendsChartData.data = [];\n    this.revenueChartData.data = [];\n  }\n  formatCurrency(value) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(value);\n  }\n  formatNumber(value) {\n    return new Intl.NumberFormat('en-IN').format(value);\n  }\n  getChangeClass(changeType) {\n    return changeType === 'positive' ? 'positive-change' : 'negative-change';\n  }\n  refreshData() {\n    this.loadDashboardData();\n  }\n  navigateToSection(section) {\n    // Navigation logic based on user permissions\n    switch (section) {\n      case 'users':\n        if (this.authService.hasPermission('users', 'view')) {\n          // Navigate to users\n        }\n        break;\n      case 'products':\n        if (this.authService.hasPermission('products', 'view')) {\n          // Navigate to products\n        }\n        break;\n      case 'orders':\n        if (this.authService.hasPermission('orders', 'view')) {\n          // Navigate to orders\n        }\n        break;\n    }\n  }\n  static {\n    this.ɵfac = function AdminDashboardComponent_Factory(t) {\n      return new (t || AdminDashboardComponent)(i0.ɵɵdirectiveInject(i1.AdminApiService), i0.ɵɵdirectiveInject(i2.AdminAuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminDashboardComponent,\n      selectors: [[\"app-admin-dashboard\"]],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"dashboard-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"dashboard-content\", 4, \"ngIf\"], [1, \"loading-container\"], [\"diameter\", \"50\"], [1, \"dashboard-content\"], [1, \"welcome-section\"], [1, \"welcome-text\"], [1, \"welcome-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"stats-grid\"], [\"class\", \"stat-card\", 3, \"border-left-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"charts-section\"], [1, \"charts-grid\"], [1, \"chart-card\"], [1, \"chart-container\"], [1, \"chart-placeholder\"], [1, \"mock-data\"], [1, \"bottom-section\"], [1, \"activities-card\"], [1, \"activities-list\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"activities-footer\"], [\"mat-button\", \"\", \"color\", \"primary\"], [1, \"actions-card\"], [1, \"actions-grid\"], [\"mat-raised-button\", \"\", 1, \"action-button\", 3, \"click\"], [\"mat-raised-button\", \"\", 1, \"action-button\"], [1, \"status-card\"], [1, \"status-grid\"], [1, \"status-item\"], [1, \"status-indicator\", \"online\"], [1, \"status-info\"], [1, \"status-label\"], [1, \"status-value\"], [1, \"status-indicator\", \"warning\"], [1, \"stat-card\"], [1, \"stat-header\"], [1, \"stat-icon\"], [1, \"stat-change\", 3, \"ngClass\"], [1, \"stat-content\"], [1, \"stat-value\"], [4, \"ngIf\"], [1, \"stat-title\"], [1, \"activity-item\"], [1, \"activity-icon\"], [1, \"activity-content\"], [1, \"activity-message\"], [1, \"activity-time\"]],\n      template: function AdminDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, AdminDashboardComponent_div_1_Template, 4, 0, \"div\", 1)(2, AdminDashboardComponent_div_2_Template, 158, 8, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i4.MatIcon, i5.MatButton, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatProgressSpinner, i3.AsyncPipe],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  min-height: 100%;\\n  background: #f5f5f5;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 400px;\\n  gap: 1rem;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.dashboard-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2rem;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 2rem;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.welcome-section[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 2rem;\\n  font-weight: 600;\\n}\\n.welcome-section[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  opacity: 0.9;\\n  font-size: 1.1rem;\\n}\\n.welcome-section[_ngcontent-%COMP%]   .welcome-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n}\\n.welcome-section[_ngcontent-%COMP%]   .welcome-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 1.5rem;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%] {\\n  border-left: 4px solid;\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]   .stat-change[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 600;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 6px;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]   .stat-change.positive-change[_ngcontent-%COMP%] {\\n  background: rgba(76, 175, 80, 0.1);\\n  color: #4caf50;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]   .stat-change.negative-change[_ngcontent-%COMP%] {\\n  background: rgba(244, 67, 54, 0.1);\\n  color: #f44336;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: #333;\\n  margin-bottom: 0.25rem;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-title[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.95rem;\\n  font-weight: 500;\\n}\\n\\n.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: 1.5rem;\\n}\\n.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-top: 0.25rem;\\n}\\n.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%] {\\n  height: 300px;\\n  position: relative;\\n}\\n.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  color: #666;\\n  text-align: center;\\n  padding: 2rem;\\n}\\n.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  margin-bottom: 1rem;\\n  opacity: 0.5;\\n}\\n.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.125rem;\\n  font-weight: 500;\\n}\\n.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  font-size: 0.875rem;\\n}\\n.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   .mock-data[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #999;\\n}\\n\\n.bottom-section[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 1.5rem;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1rem 0;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-message[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 0.25rem;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-time[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.85rem;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-footer[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 1rem;\\n  padding-top: 1rem;\\n  border-top: 1px solid #f0f0f0;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 1rem;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 1.5rem 1rem;\\n  height: auto;\\n  background: #f8f9fa;\\n  color: #333;\\n  border: 1px solid #e9ecef;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  transform: translateY(-1px);\\n}\\n.bottom-section[_ngcontent-%COMP%]   .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 500;\\n}\\n\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 1.5rem;\\n}\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1rem;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n}\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n}\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-indicator.online[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n}\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-indicator.warning[_ngcontent-%COMP%] {\\n  background: #ff9800;\\n}\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-indicator.error[_ngcontent-%COMP%] {\\n  background: #f44336;\\n}\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-info[_ngcontent-%COMP%]   .status-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 0.25rem;\\n}\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-info[_ngcontent-%COMP%]   .status-value[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n@media (max-width: 1200px) {\\n  .charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .bottom-section[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .welcome-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    text-align: center;\\n  }\\n  .welcome-section[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%] {\\n    height: 250px;\\n  }\\n  .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .dashboard-content[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n  .welcome-section[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n  }\\n  .status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n  .mat-card {\\n  border-radius: 12px !important;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;\\n}\\n  .mat-card-header {\\n  padding-bottom: 0 !important;\\n}\\n  .mat-spinner circle {\\n  stroke: #667eea;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "finalize", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "stat_r3", "prefix", "ɵɵtemplate", "AdminDashboardComponent_div_2_mat_card_14_span_10_Template", "ɵɵstyleProp", "color", "icon", "ɵɵproperty", "ctx_r1", "getChangeClass", "changeType", "ɵɵtextInterpolate1", "change", "title", "formatCurrency", "value", "formatNumber", "activity_r4", "message", "time", "ɵɵlistener", "AdminDashboardComponent_div_2_Template_button_click_9_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "refreshData", "AdminDashboardComponent_div_2_mat_card_14_Template", "AdminDashboardComponent_div_2_div_80_Template", "AdminDashboardComponent_div_2_Template_button_click_92_listener", "navigateToSection", "AdminDashboardComponent_div_2_Template_button_click_97_listener", "AdminDashboardComponent_div_2_Template_button_click_102_listener", "tmp_1_0", "ɵɵpipeBind1", "currentUser$", "fullName", "quickStats", "userGrowthChartData", "data", "join", "orderTrendsChartData", "revenueChartData", "recentActivities", "AdminDashboardComponent", "constructor", "apiService", "authService", "destroy$", "isLoading", "dashboardStats", "labels", "chartOptions", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "type", "ngOnInit", "loadDashboardData", "ngOnDestroy", "next", "complete", "getDashboardStats", "pipe", "subscribe", "stats", "updateQuickStats", "updateCharts", "error", "console", "overview", "users", "total", "products", "active", "orders", "revenue", "totalRevenue", "loadMockData", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "section", "hasPermission", "ɵɵdirectiveInject", "i1", "AdminApiService", "i2", "AdminAuthService", "selectors", "decls", "vars", "consts", "template", "AdminDashboardComponent_Template", "rf", "ctx", "AdminDashboardComponent_div_1_Template", "AdminDashboardComponent_div_2_Template"], "sources": ["E:\\DFashion\\frontend\\src\\app\\admin\\dashboard\\admin-dashboard.component.ts", "E:\\DFashion\\frontend\\src\\app\\admin\\dashboard\\admin-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil, finalize } from 'rxjs/operators';\nimport { AdminApiService, DashboardStats } from '../services/admin-api.service';\nimport { AdminAuthService } from '../services/admin-auth.service';\n// Chart.js imports removed - using simple chart placeholders instead\n\n@Component({\n  selector: 'app-admin-dashboard',\n  templateUrl: './admin-dashboard.component.html',\n  styleUrls: ['./admin-dashboard.component.scss']\n})\nexport class AdminDashboardComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  \n  isLoading = true;\n  dashboardStats: DashboardStats | null = null;\n  currentUser$ = this.authService.currentUser$;\n\n  // Chart data (simplified without Chart.js dependency)\n  userGrowthChartData = {\n    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],\n    data: [65, 78, 90, 81, 95, 105]\n  };\n\n  orderTrendsChartData = {\n    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n    data: [12, 19, 15, 25, 22, 30, 28]\n  };\n\n  revenueChartData = {\n    labels: ['Products', 'Services', 'Subscriptions'],\n    data: [65, 25, 10]\n  };\n\n  // Chart options for template compatibility\n  chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'bottom'\n      }\n    }\n  };\n\n  // Quick stats cards\n  quickStats = [\n    {\n      title: 'Total Users',\n      value: 0,\n      change: '+12%',\n      changeType: 'positive',\n      icon: 'people',\n      color: '#2196f3'\n    },\n    {\n      title: 'Active Products',\n      value: 0,\n      change: '+8%',\n      changeType: 'positive',\n      icon: 'inventory',\n      color: '#4caf50'\n    },\n    {\n      title: 'Total Orders',\n      value: 0,\n      change: '+15%',\n      changeType: 'positive',\n      icon: 'shopping_cart',\n      color: '#ff9800'\n    },\n    {\n      title: 'Revenue',\n      value: 0,\n      change: '+23%',\n      changeType: 'positive',\n      icon: 'attach_money',\n      color: '#9c27b0',\n      prefix: '₹'\n    }\n  ];\n\n  // Recent activities\n  recentActivities = [\n    {\n      type: 'order',\n      message: 'New order #DF12345 received',\n      time: '2 minutes ago',\n      icon: 'shopping_cart',\n      color: '#4caf50'\n    },\n    {\n      type: 'user',\n      message: 'New user registration: John Doe',\n      time: '5 minutes ago',\n      icon: 'person_add',\n      color: '#2196f3'\n    },\n    {\n      type: 'product',\n      message: 'Product \"Summer Dress\" approved',\n      time: '10 minutes ago',\n      icon: 'check_circle',\n      color: '#4caf50'\n    },\n    {\n      type: 'payment',\n      message: 'Payment of ₹2,500 received',\n      time: '15 minutes ago',\n      icon: 'payment',\n      color: '#9c27b0'\n    }\n  ];\n\n  constructor(\n    private apiService: AdminApiService,\n    private authService: AdminAuthService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadDashboardData();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadDashboardData(): void {\n    this.isLoading = true;\n    \n    this.apiService.getDashboardStats()\n      .pipe(\n        takeUntil(this.destroy$),\n        finalize(() => this.isLoading = false)\n      )\n      .subscribe({\n        next: (stats) => {\n          this.dashboardStats = stats;\n          this.updateQuickStats(stats);\n          this.updateCharts(stats);\n        },\n        error: (error) => {\n          console.error('Failed to load dashboard data:', error);\n          // Initialize empty dashboard data\n          this.dashboardStats = null;\n        }\n      });\n  }\n\n  private updateQuickStats(stats: DashboardStats): void {\n    this.quickStats[0].value = stats.overview.users.total;\n    this.quickStats[1].value = stats.overview.products.active;\n    this.quickStats[2].value = stats.overview.orders.total;\n    this.quickStats[3].value = stats.revenue.totalRevenue;\n  }\n\n  private updateCharts(stats: DashboardStats): void {\n    // Update chart data with actual stats\n    this.userGrowthChartData.data = [65, 78, 90, 81, 95, 105];\n    this.orderTrendsChartData.data = [12, 19, 15, 25, 22, 30, 28];\n    this.revenueChartData.data = [65, 25, 10];\n  }\n\n  private loadMockData(): void {\n    // Load real data from API - empty for now\n    this.quickStats[0].value = 0;\n    this.quickStats[1].value = 0;\n    this.quickStats[2].value = 0;\n    this.quickStats[3].value = 0;\n\n    // Update charts with empty data\n    this.userGrowthChartData.data = [];\n    this.orderTrendsChartData.data = [];\n    this.revenueChartData.data = [];\n  }\n\n  formatCurrency(value: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(value);\n  }\n\n  formatNumber(value: number): string {\n    return new Intl.NumberFormat('en-IN').format(value);\n  }\n\n  getChangeClass(changeType: string): string {\n    return changeType === 'positive' ? 'positive-change' : 'negative-change';\n  }\n\n  refreshData(): void {\n    this.loadDashboardData();\n  }\n\n  navigateToSection(section: string): void {\n    // Navigation logic based on user permissions\n    switch (section) {\n      case 'users':\n        if (this.authService.hasPermission('users', 'view')) {\n          // Navigate to users\n        }\n        break;\n      case 'products':\n        if (this.authService.hasPermission('products', 'view')) {\n          // Navigate to products\n        }\n        break;\n      case 'orders':\n        if (this.authService.hasPermission('orders', 'view')) {\n          // Navigate to orders\n        }\n        break;\n    }\n  }\n}\n", "<div class=\"dashboard-container\">\n  <!-- Loading Spinner -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <mat-spinner diameter=\"50\"></mat-spinner>\n    <p>Loading dashboard data...</p>\n  </div>\n\n  <!-- Dashboard Content -->\n  <div *ngIf=\"!isLoading\" class=\"dashboard-content\">\n    <!-- Welcome Section -->\n    <div class=\"welcome-section\">\n      <div class=\"welcome-text\">\n        <h1>Welcome back, {{ (currentUser$ | async)?.fullName }}!</h1>\n        <p>Here's what's happening with your business today.</p>\n      </div>\n      <div class=\"welcome-actions\">\n        <button mat-raised-button color=\"primary\" (click)=\"refreshData()\">\n          <mat-icon>refresh</mat-icon>\n          Refresh Data\n        </button>\n      </div>\n    </div>\n\n    <!-- Quick Stats Cards -->\n    <div class=\"stats-grid\">\n      <mat-card *ngFor=\"let stat of quickStats\" class=\"stat-card\" [style.border-left-color]=\"stat.color\">\n        <mat-card-content>\n          <div class=\"stat-header\">\n            <div class=\"stat-icon\" [style.background-color]=\"stat.color\">\n              <mat-icon>{{ stat.icon }}</mat-icon>\n            </div>\n            <div class=\"stat-change\" [ngClass]=\"getChangeClass(stat.changeType)\">\n              {{ stat.change }}\n            </div>\n          </div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">\n              <span *ngIf=\"stat.prefix\">{{ stat.prefix }}</span>\n              {{ stat.title === 'Revenue' ? formatCurrency(stat.value) : formatNumber(stat.value) }}\n            </div>\n            <div class=\"stat-title\">{{ stat.title }}</div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Charts Section -->\n    <div class=\"charts-section\">\n      <div class=\"charts-grid\">\n        <!-- User Growth Chart -->\n        <mat-card class=\"chart-card\">\n          <mat-card-header>\n            <mat-card-title>User Growth</mat-card-title>\n            <mat-card-subtitle>Monthly new user registrations</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"chart-container\">\n              <div class=\"chart-placeholder\">\n                <mat-icon>show_chart</mat-icon>\n                <h3>User Growth Chart</h3>\n                <p>Chart visualization will be displayed here</p>\n                <div class=\"mock-data\">\n                  <small>Sample data: {{ userGrowthChartData.data.join(', ') }}</small>\n                </div>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Order Trends Chart -->\n        <mat-card class=\"chart-card\">\n          <mat-card-header>\n            <mat-card-title>Order Trends</mat-card-title>\n            <mat-card-subtitle>Daily orders this week</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"chart-container\">\n              <div class=\"chart-placeholder\">\n                <mat-icon>bar_chart</mat-icon>\n                <h3>Order Trends Chart</h3>\n                <p>Chart visualization will be displayed here</p>\n                <div class=\"mock-data\">\n                  <small>Sample data: {{ orderTrendsChartData.data.join(', ') }}</small>\n                </div>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Revenue Distribution Chart -->\n        <mat-card class=\"chart-card\">\n          <mat-card-header>\n            <mat-card-title>Revenue Distribution</mat-card-title>\n            <mat-card-subtitle>Revenue by category</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"chart-container\">\n              <div class=\"chart-placeholder\">\n                <mat-icon>donut_large</mat-icon>\n                <h3>Revenue Distribution Chart</h3>\n                <p>Chart visualization will be displayed here</p>\n                <div class=\"mock-data\">\n                  <small>Sample data: {{ revenueChartData.data.join(', ') }}</small>\n                </div>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n\n    <!-- Bottom Section -->\n    <div class=\"bottom-section\">\n      <!-- Recent Activities -->\n      <mat-card class=\"activities-card\">\n        <mat-card-header>\n          <mat-card-title>Recent Activities</mat-card-title>\n          <mat-card-subtitle>Latest system activities</mat-card-subtitle>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"activities-list\">\n            <div *ngFor=\"let activity of recentActivities\" class=\"activity-item\">\n              <div class=\"activity-icon\" [style.background-color]=\"activity.color\">\n                <mat-icon>{{ activity.icon }}</mat-icon>\n              </div>\n              <div class=\"activity-content\">\n                <div class=\"activity-message\">{{ activity.message }}</div>\n                <div class=\"activity-time\">{{ activity.time }}</div>\n              </div>\n            </div>\n          </div>\n          <div class=\"activities-footer\">\n            <button mat-button color=\"primary\">View All Activities</button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Quick Actions -->\n      <mat-card class=\"actions-card\">\n        <mat-card-header>\n          <mat-card-title>Quick Actions</mat-card-title>\n          <mat-card-subtitle>Common administrative tasks</mat-card-subtitle>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"actions-grid\">\n            <button mat-raised-button class=\"action-button\" (click)=\"navigateToSection('users')\">\n              <mat-icon>person_add</mat-icon>\n              <span>Add User</span>\n            </button>\n            <button mat-raised-button class=\"action-button\" (click)=\"navigateToSection('products')\">\n              <mat-icon>add_box</mat-icon>\n              <span>Add Product</span>\n            </button>\n            <button mat-raised-button class=\"action-button\" (click)=\"navigateToSection('orders')\">\n              <mat-icon>list_alt</mat-icon>\n              <span>View Orders</span>\n            </button>\n            <button mat-raised-button class=\"action-button\">\n              <mat-icon>analytics</mat-icon>\n              <span>View Reports</span>\n            </button>\n            <button mat-raised-button class=\"action-button\">\n              <mat-icon>settings</mat-icon>\n              <span>Settings</span>\n            </button>\n            <button mat-raised-button class=\"action-button\">\n              <mat-icon>help</mat-icon>\n              <span>Help Center</span>\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- System Status -->\n    <mat-card class=\"status-card\">\n      <mat-card-header>\n        <mat-card-title>System Status</mat-card-title>\n        <mat-card-subtitle>Current system health and performance</mat-card-subtitle>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"status-grid\">\n          <div class=\"status-item\">\n            <div class=\"status-indicator online\"></div>\n            <div class=\"status-info\">\n              <div class=\"status-label\">API Server</div>\n              <div class=\"status-value\">Online</div>\n            </div>\n          </div>\n          <div class=\"status-item\">\n            <div class=\"status-indicator online\"></div>\n            <div class=\"status-info\">\n              <div class=\"status-label\">Database</div>\n              <div class=\"status-value\">Connected</div>\n            </div>\n          </div>\n          <div class=\"status-item\">\n            <div class=\"status-indicator warning\"></div>\n            <div class=\"status-info\">\n              <div class=\"status-label\">Storage</div>\n              <div class=\"status-value\">85% Used</div>\n            </div>\n          </div>\n          <div class=\"status-item\">\n            <div class=\"status-indicator online\"></div>\n            <div class=\"status-info\">\n              <div class=\"status-label\">Payment Gateway</div>\n              <div class=\"status-value\">Active</div>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;;;;;;;;;;;ICAlDC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,qBAAyC;IACzCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAC9BH,EAD8B,CAAAI,YAAA,EAAI,EAC5B;;;;;IAgCMJ,EAAA,CAAAC,cAAA,WAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAxBJ,EAAA,CAAAK,SAAA,EAAiB;IAAjBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAC,MAAA,CAAiB;;;;;IAR3CR,EAJR,CAAAC,cAAA,mBAAmG,uBAC/E,cACS,cACsC,eACjD;IAAAD,EAAA,CAAAG,MAAA,GAAe;IAC3BH,EAD2B,CAAAI,YAAA,EAAW,EAChC;IACNJ,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IAEJJ,EADF,CAAAC,cAAA,cAA0B,cACA;IACtBD,EAAA,CAAAS,UAAA,KAAAC,0DAAA,mBAA0B;IAC1BV,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAG,MAAA,IAAgB;IAG9CH,EAH8C,CAAAI,YAAA,EAAM,EAC1C,EACW,EACV;;;;;IAlBiDJ,EAAA,CAAAW,WAAA,sBAAAJ,OAAA,CAAAK,KAAA,CAAsC;IAGrEZ,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAW,WAAA,qBAAAJ,OAAA,CAAAK,KAAA,CAAqC;IAChDZ,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAM,IAAA,CAAe;IAEFb,EAAA,CAAAK,SAAA,EAA2C;IAA3CL,EAAA,CAAAc,UAAA,YAAAC,MAAA,CAAAC,cAAA,CAAAT,OAAA,CAAAU,UAAA,EAA2C;IAClEjB,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAkB,kBAAA,MAAAX,OAAA,CAAAY,MAAA,MACF;IAISnB,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAc,UAAA,SAAAP,OAAA,CAAAC,MAAA,CAAiB;IACxBR,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAkB,kBAAA,MAAAX,OAAA,CAAAa,KAAA,iBAAAL,MAAA,CAAAM,cAAA,CAAAd,OAAA,CAAAe,KAAA,IAAAP,MAAA,CAAAQ,YAAA,CAAAhB,OAAA,CAAAe,KAAA,OACF;IACwBtB,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAa,KAAA,CAAgB;;;;;IAmFpCpB,EAFJ,CAAAC,cAAA,cAAqE,cACE,eACzD;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAC/BH,EAD+B,CAAAI,YAAA,EAAW,EACpC;IAEJJ,EADF,CAAAC,cAAA,cAA8B,cACE;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC1DJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAElDH,EAFkD,CAAAI,YAAA,EAAM,EAChD,EACF;;;;IAPuBJ,EAAA,CAAAK,SAAA,EAAyC;IAAzCL,EAAA,CAAAW,WAAA,qBAAAa,WAAA,CAAAZ,KAAA,CAAyC;IACxDZ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAkB,WAAA,CAAAX,IAAA,CAAmB;IAGCb,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,iBAAA,CAAAkB,WAAA,CAAAC,OAAA,CAAsB;IACzBzB,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAkB,WAAA,CAAAE,IAAA,CAAmB;;;;;;IAnHtD1B,EAJN,CAAAC,cAAA,aAAkD,aAEnB,aACD,SACpB;IAAAD,EAAA,CAAAG,MAAA,GAAqD;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9DJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,wDAAiD;IACtDH,EADsD,CAAAI,YAAA,EAAI,EACpD;IAEJJ,EADF,CAAAC,cAAA,aAA6B,gBACuC;IAAxBD,EAAA,CAAA2B,UAAA,mBAAAC,+DAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAC,GAAA;MAAA,MAAAf,MAAA,GAAAf,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAASjB,MAAA,CAAAkB,WAAA,EAAa;IAAA,EAAC;IAC/DjC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,sBACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;IAGNJ,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAS,UAAA,KAAAyB,kDAAA,yBAAmG;IAmBrGlC,EAAA,CAAAI,YAAA,EAAM;IAQEJ,EALR,CAAAC,cAAA,eAA4B,eACD,oBAEM,uBACV,sBACC;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAC5CJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,sCAA8B;IACnDH,EADmD,CAAAI,YAAA,EAAoB,EACrD;IAIZJ,EAHN,CAAAC,cAAA,wBAAkB,eACa,eACI,gBACnB;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,kDAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE/CJ,EADF,CAAAC,cAAA,eAAuB,aACd;IAAAD,EAAA,CAAAG,MAAA,IAAsD;IAKvEH,EALuE,CAAAI,YAAA,EAAQ,EACjE,EACF,EACF,EACW,EACV;IAKPJ,EAFJ,CAAAC,cAAA,oBAA6B,uBACV,sBACC;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAC7CJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,8BAAsB;IAC3CH,EAD2C,CAAAI,YAAA,EAAoB,EAC7C;IAIZJ,EAHN,CAAAC,cAAA,wBAAkB,eACa,eACI,gBACnB;IAAAD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC9BJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3BJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,kDAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE/CJ,EADF,CAAAC,cAAA,eAAuB,aACd;IAAAD,EAAA,CAAAG,MAAA,IAAuD;IAKxEH,EALwE,CAAAI,YAAA,EAAQ,EAClE,EACF,EACF,EACW,EACV;IAKPJ,EAFJ,CAAAC,cAAA,oBAA6B,uBACV,sBACC;IAAAD,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IACrDJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,2BAAmB;IACxCH,EADwC,CAAAI,YAAA,EAAoB,EAC1C;IAIZJ,EAHN,CAAAC,cAAA,wBAAkB,eACa,eACI,gBACnB;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAChCJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,kCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnCJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,kDAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE/CJ,EADF,CAAAC,cAAA,eAAuB,aACd;IAAAD,EAAA,CAAAG,MAAA,IAAmD;IAOxEH,EAPwE,CAAAI,YAAA,EAAQ,EAC9D,EACF,EACF,EACW,EACV,EACP,EACF;IAOAJ,EAJN,CAAAC,cAAA,eAA4B,oBAEQ,uBACf,sBACC;IAAAD,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAClDJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,gCAAwB;IAC7CH,EAD6C,CAAAI,YAAA,EAAoB,EAC/C;IAEhBJ,EADF,CAAAC,cAAA,wBAAkB,eACa;IAC3BD,EAAA,CAAAS,UAAA,KAAA0B,6CAAA,kBAAqE;IASvEnC,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,eAA+B,kBACM;IAAAD,EAAA,CAAAG,MAAA,2BAAmB;IAG5DH,EAH4D,CAAAI,YAAA,EAAS,EAC3D,EACW,EACV;IAKPJ,EAFJ,CAAAC,cAAA,oBAA+B,uBACZ,sBACC;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAC9CJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,mCAA2B;IAChDH,EADgD,CAAAI,YAAA,EAAoB,EAClD;IAGdJ,EAFJ,CAAAC,cAAA,wBAAkB,eACU,kBAC6D;IAArCD,EAAA,CAAA2B,UAAA,mBAAAS,gEAAA;MAAApC,EAAA,CAAA6B,aAAA,CAAAC,GAAA;MAAA,MAAAf,MAAA,GAAAf,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAASjB,MAAA,CAAAsB,iBAAA,CAAkB,OAAO,CAAC;IAAA,EAAC;IAClFrC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAChBH,EADgB,CAAAI,YAAA,EAAO,EACd;IACTJ,EAAA,CAAAC,cAAA,kBAAwF;IAAxCD,EAAA,CAAA2B,UAAA,mBAAAW,gEAAA;MAAAtC,EAAA,CAAA6B,aAAA,CAAAC,GAAA;MAAA,MAAAf,MAAA,GAAAf,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAASjB,MAAA,CAAAsB,iBAAA,CAAkB,UAAU,CAAC;IAAA,EAAC;IACrFrC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAG,MAAA,oBAAW;IACnBH,EADmB,CAAAI,YAAA,EAAO,EACjB;IACTJ,EAAA,CAAAC,cAAA,mBAAsF;IAAtCD,EAAA,CAAA2B,UAAA,mBAAAY,iEAAA;MAAAvC,EAAA,CAAA6B,aAAA,CAAAC,GAAA;MAAA,MAAAf,MAAA,GAAAf,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAASjB,MAAA,CAAAsB,iBAAA,CAAkB,QAAQ,CAAC;IAAA,EAAC;IACnFrC,EAAA,CAAAC,cAAA,iBAAU;IAAAD,EAAA,CAAAG,MAAA,iBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAG,MAAA,oBAAW;IACnBH,EADmB,CAAAI,YAAA,EAAO,EACjB;IAEPJ,EADF,CAAAC,cAAA,mBAAgD,iBACpC;IAAAD,EAAA,CAAAG,MAAA,kBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC9BJ,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAG,MAAA,qBAAY;IACpBH,EADoB,CAAAI,YAAA,EAAO,EAClB;IAEPJ,EADF,CAAAC,cAAA,mBAAgD,iBACpC;IAAAD,EAAA,CAAAG,MAAA,iBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAG,MAAA,iBAAQ;IAChBH,EADgB,CAAAI,YAAA,EAAO,EACd;IAEPJ,EADF,CAAAC,cAAA,mBAAgD,iBACpC;IAAAD,EAAA,CAAAG,MAAA,aAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAG,MAAA,oBAAW;IAK3BH,EAL2B,CAAAI,YAAA,EAAO,EACjB,EACL,EACW,EACV,EACP;IAKFJ,EAFJ,CAAAC,cAAA,qBAA8B,wBACX,uBACC;IAAAD,EAAA,CAAAG,MAAA,sBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAC9CJ,EAAA,CAAAC,cAAA,0BAAmB;IAAAD,EAAA,CAAAG,MAAA,8CAAqC;IAC1DH,EAD0D,CAAAI,YAAA,EAAoB,EAC5D;IAGdJ,EAFJ,CAAAC,cAAA,yBAAkB,gBACS,gBACE;IACvBD,EAAA,CAAAE,SAAA,gBAA2C;IAEzCF,EADF,CAAAC,cAAA,gBAAyB,gBACG;IAAAD,EAAA,CAAAG,MAAA,mBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC1CJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,eAAM;IAEpCH,EAFoC,CAAAI,YAAA,EAAM,EAClC,EACF;IACNJ,EAAA,CAAAC,cAAA,gBAAyB;IACvBD,EAAA,CAAAE,SAAA,gBAA2C;IAEzCF,EADF,CAAAC,cAAA,gBAAyB,gBACG;IAAAD,EAAA,CAAAG,MAAA,iBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACxCJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,kBAAS;IAEvCH,EAFuC,CAAAI,YAAA,EAAM,EACrC,EACF;IACNJ,EAAA,CAAAC,cAAA,gBAAyB;IACvBD,EAAA,CAAAE,SAAA,gBAA4C;IAE1CF,EADF,CAAAC,cAAA,gBAAyB,gBACG;IAAAD,EAAA,CAAAG,MAAA,gBAAO;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACvCJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,iBAAQ;IAEtCH,EAFsC,CAAAI,YAAA,EAAM,EACpC,EACF;IACNJ,EAAA,CAAAC,cAAA,gBAAyB;IACvBD,EAAA,CAAAE,SAAA,gBAA2C;IAEzCF,EADF,CAAAC,cAAA,gBAAyB,gBACG;IAAAD,EAAA,CAAAG,MAAA,wBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC/CJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,eAAM;IAM5CH,EAN4C,CAAAI,YAAA,EAAM,EAClC,EACF,EACF,EACW,EACV,EACP;;;;;IAzMIJ,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAAkB,kBAAA,oBAAAsB,OAAA,GAAAxC,EAAA,CAAAyC,WAAA,OAAA1B,MAAA,CAAA2B,YAAA,oBAAAF,OAAA,CAAAG,QAAA,MAAqD;IAahC3C,EAAA,CAAAK,SAAA,IAAa;IAAbL,EAAA,CAAAc,UAAA,YAAAC,MAAA,CAAA6B,UAAA,CAAa;IAqCrB5C,EAAA,CAAAK,SAAA,IAAsD;IAAtDL,EAAA,CAAAkB,kBAAA,kBAAAH,MAAA,CAAA8B,mBAAA,CAAAC,IAAA,CAAAC,IAAA,WAAsD;IAoBtD/C,EAAA,CAAAK,SAAA,IAAuD;IAAvDL,EAAA,CAAAkB,kBAAA,kBAAAH,MAAA,CAAAiC,oBAAA,CAAAF,IAAA,CAAAC,IAAA,WAAuD;IAoBvD/C,EAAA,CAAAK,SAAA,IAAmD;IAAnDL,EAAA,CAAAkB,kBAAA,kBAAAH,MAAA,CAAAkC,gBAAA,CAAAH,IAAA,CAAAC,IAAA,WAAmD;IAmBtC/C,EAAA,CAAAK,SAAA,IAAmB;IAAnBL,EAAA,CAAAc,UAAA,YAAAC,MAAA,CAAAmC,gBAAA,CAAmB;;;ADpHzD;AAOA,OAAM,MAAOC,uBAAuB;EAuGlCC,YACUC,UAA2B,EAC3BC,WAA6B;IAD7B,KAAAD,UAAU,GAAVA,UAAU;IACV,KAAAC,WAAW,GAAXA,WAAW;IAxGb,KAAAC,QAAQ,GAAG,IAAI1D,OAAO,EAAQ;IAEtC,KAAA2D,SAAS,GAAG,IAAI;IAChB,KAAAC,cAAc,GAA0B,IAAI;IAC5C,KAAAf,YAAY,GAAG,IAAI,CAACY,WAAW,CAACZ,YAAY;IAE5C;IACA,KAAAG,mBAAmB,GAAG;MACpBa,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MAClDZ,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG;KAC/B;IAED,KAAAE,oBAAoB,GAAG;MACrBU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MACzDZ,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;KAClC;IAED,KAAAG,gBAAgB,GAAG;MACjBS,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,eAAe,CAAC;MACjDZ,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;KAClB;IAED;IACA,KAAAa,YAAY,GAAG;MACbC,UAAU,EAAE,IAAI;MAChBC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,QAAQ,EAAE;;;KAGf;IAED;IACA,KAAApB,UAAU,GAAG,CACX;MACExB,KAAK,EAAE,aAAa;MACpBE,KAAK,EAAE,CAAC;MACRH,MAAM,EAAE,MAAM;MACdF,UAAU,EAAE,UAAU;MACtBJ,IAAI,EAAE,QAAQ;MACdD,KAAK,EAAE;KACR,EACD;MACEQ,KAAK,EAAE,iBAAiB;MACxBE,KAAK,EAAE,CAAC;MACRH,MAAM,EAAE,KAAK;MACbF,UAAU,EAAE,UAAU;MACtBJ,IAAI,EAAE,WAAW;MACjBD,KAAK,EAAE;KACR,EACD;MACEQ,KAAK,EAAE,cAAc;MACrBE,KAAK,EAAE,CAAC;MACRH,MAAM,EAAE,MAAM;MACdF,UAAU,EAAE,UAAU;MACtBJ,IAAI,EAAE,eAAe;MACrBD,KAAK,EAAE;KACR,EACD;MACEQ,KAAK,EAAE,SAAS;MAChBE,KAAK,EAAE,CAAC;MACRH,MAAM,EAAE,MAAM;MACdF,UAAU,EAAE,UAAU;MACtBJ,IAAI,EAAE,cAAc;MACpBD,KAAK,EAAE,SAAS;MAChBJ,MAAM,EAAE;KACT,CACF;IAED;IACA,KAAA0C,gBAAgB,GAAG,CACjB;MACEe,IAAI,EAAE,OAAO;MACbxC,OAAO,EAAE,6BAA6B;MACtCC,IAAI,EAAE,eAAe;MACrBb,IAAI,EAAE,eAAe;MACrBD,KAAK,EAAE;KACR,EACD;MACEqD,IAAI,EAAE,MAAM;MACZxC,OAAO,EAAE,iCAAiC;MAC1CC,IAAI,EAAE,eAAe;MACrBb,IAAI,EAAE,YAAY;MAClBD,KAAK,EAAE;KACR,EACD;MACEqD,IAAI,EAAE,SAAS;MACfxC,OAAO,EAAE,iCAAiC;MAC1CC,IAAI,EAAE,gBAAgB;MACtBb,IAAI,EAAE,cAAc;MACpBD,KAAK,EAAE;KACR,EACD;MACEqD,IAAI,EAAE,SAAS;MACfxC,OAAO,EAAE,4BAA4B;MACrCC,IAAI,EAAE,gBAAgB;MACtBb,IAAI,EAAE,SAAS;MACfD,KAAK,EAAE;KACR,CACF;EAKE;EAEHsD,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACb,QAAQ,CAACc,IAAI,EAAE;IACpB,IAAI,CAACd,QAAQ,CAACe,QAAQ,EAAE;EAC1B;EAEAH,iBAAiBA,CAAA;IACf,IAAI,CAACX,SAAS,GAAG,IAAI;IAErB,IAAI,CAACH,UAAU,CAACkB,iBAAiB,EAAE,CAChCC,IAAI,CACH1E,SAAS,CAAC,IAAI,CAACyD,QAAQ,CAAC,EACxBxD,QAAQ,CAAC,MAAM,IAAI,CAACyD,SAAS,GAAG,KAAK,CAAC,CACvC,CACAiB,SAAS,CAAC;MACTJ,IAAI,EAAGK,KAAK,IAAI;QACd,IAAI,CAACjB,cAAc,GAAGiB,KAAK;QAC3B,IAAI,CAACC,gBAAgB,CAACD,KAAK,CAAC;QAC5B,IAAI,CAACE,YAAY,CAACF,KAAK,CAAC;MAC1B,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD;QACA,IAAI,CAACpB,cAAc,GAAG,IAAI;MAC5B;KACD,CAAC;EACN;EAEQkB,gBAAgBA,CAACD,KAAqB;IAC5C,IAAI,CAAC9B,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAGoD,KAAK,CAACK,QAAQ,CAACC,KAAK,CAACC,KAAK;IACrD,IAAI,CAACrC,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAGoD,KAAK,CAACK,QAAQ,CAACG,QAAQ,CAACC,MAAM;IACzD,IAAI,CAACvC,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAGoD,KAAK,CAACK,QAAQ,CAACK,MAAM,CAACH,KAAK;IACtD,IAAI,CAACrC,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAGoD,KAAK,CAACW,OAAO,CAACC,YAAY;EACvD;EAEQV,YAAYA,CAACF,KAAqB;IACxC;IACA,IAAI,CAAC7B,mBAAmB,CAACC,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IACzD,IAAI,CAACE,oBAAoB,CAACF,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC7D,IAAI,CAACG,gBAAgB,CAACH,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC3C;EAEQyC,YAAYA,CAAA;IAClB;IACA,IAAI,CAAC3C,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAG,CAAC;IAC5B,IAAI,CAACsB,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAG,CAAC;IAC5B,IAAI,CAACsB,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAG,CAAC;IAC5B,IAAI,CAACsB,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAG,CAAC;IAE5B;IACA,IAAI,CAACuB,mBAAmB,CAACC,IAAI,GAAG,EAAE;IAClC,IAAI,CAACE,oBAAoB,CAACF,IAAI,GAAG,EAAE;IACnC,IAAI,CAACG,gBAAgB,CAACH,IAAI,GAAG,EAAE;EACjC;EAEAzB,cAAcA,CAACC,KAAa;IAC1B,OAAO,IAAIkE,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACvE,KAAK,CAAC;EAClB;EAEAC,YAAYA,CAACD,KAAa;IACxB,OAAO,IAAIkE,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACI,MAAM,CAACvE,KAAK,CAAC;EACrD;EAEAN,cAAcA,CAACC,UAAkB;IAC/B,OAAOA,UAAU,KAAK,UAAU,GAAG,iBAAiB,GAAG,iBAAiB;EAC1E;EAEAgB,WAAWA,CAAA;IACT,IAAI,CAACkC,iBAAiB,EAAE;EAC1B;EAEA9B,iBAAiBA,CAACyD,OAAe;IAC/B;IACA,QAAQA,OAAO;MACb,KAAK,OAAO;QACV,IAAI,IAAI,CAACxC,WAAW,CAACyC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE;UACnD;QAAA;QAEF;MACF,KAAK,UAAU;QACb,IAAI,IAAI,CAACzC,WAAW,CAACyC,aAAa,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;UACtD;QAAA;QAEF;MACF,KAAK,QAAQ;QACX,IAAI,IAAI,CAACzC,WAAW,CAACyC,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE;UACpD;QAAA;QAEF;;EAEN;;;uBA7MW5C,uBAAuB,EAAAnD,EAAA,CAAAgG,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAlG,EAAA,CAAAgG,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAvBjD,uBAAuB;MAAAkD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZpC3G,EAAA,CAAAC,cAAA,aAAiC;UAQ/BD,EANA,CAAAS,UAAA,IAAAoG,sCAAA,iBAAiD,IAAAC,sCAAA,mBAMC;UA8MpD9G,EAAA,CAAAI,YAAA,EAAM;;;UApNEJ,EAAA,CAAAK,SAAA,EAAe;UAAfL,EAAA,CAAAc,UAAA,SAAA8F,GAAA,CAAApD,SAAA,CAAe;UAMfxD,EAAA,CAAAK,SAAA,EAAgB;UAAhBL,EAAA,CAAAc,UAAA,UAAA8F,GAAA,CAAApD,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}