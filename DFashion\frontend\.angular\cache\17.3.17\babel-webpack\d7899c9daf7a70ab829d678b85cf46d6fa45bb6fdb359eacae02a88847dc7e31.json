{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*\n Stencil Client Platform v4.33.1 | MIT Licensed | https://stenciljs.com\n */\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\n\n// src/client/client-build.ts\nimport { BUILD } from \"@stencil/core/internal/app-data\";\nvar Build = {\n  isDev: BUILD.isDev ? true : false,\n  isBrowser: true,\n  isServer: false,\n  isTesting: BUILD.isTesting ? true : false\n};\n\n// src/client/client-host-ref.ts\nimport { BUILD as BUILD3 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/es2022-rewire-class-members.ts\nimport { BUILD as BUILD2 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/constants.ts\nvar SVG_NS = \"http://www.w3.org/2000/svg\";\nvar HTML_NS = \"http://www.w3.org/1999/xhtml\";\nvar PrimitiveType = /* @__PURE__ */(PrimitiveType2 => {\n  PrimitiveType2[\"Undefined\"] = \"undefined\";\n  PrimitiveType2[\"Null\"] = \"null\";\n  PrimitiveType2[\"String\"] = \"string\";\n  PrimitiveType2[\"Number\"] = \"number\";\n  PrimitiveType2[\"SpecialNumber\"] = \"number\";\n  PrimitiveType2[\"Boolean\"] = \"boolean\";\n  PrimitiveType2[\"BigInt\"] = \"bigint\";\n  return PrimitiveType2;\n})(PrimitiveType || {});\nvar NonPrimitiveType = /* @__PURE__ */(NonPrimitiveType2 => {\n  NonPrimitiveType2[\"Array\"] = \"array\";\n  NonPrimitiveType2[\"Date\"] = \"date\";\n  NonPrimitiveType2[\"Map\"] = \"map\";\n  NonPrimitiveType2[\"Object\"] = \"object\";\n  NonPrimitiveType2[\"RegularExpression\"] = \"regexp\";\n  NonPrimitiveType2[\"Set\"] = \"set\";\n  NonPrimitiveType2[\"Channel\"] = \"channel\";\n  NonPrimitiveType2[\"Symbol\"] = \"symbol\";\n  return NonPrimitiveType2;\n})(NonPrimitiveType || {});\nvar TYPE_CONSTANT = \"type\";\nvar VALUE_CONSTANT = \"value\";\nvar SERIALIZED_PREFIX = \"serialized:\";\n\n// src/utils/es2022-rewire-class-members.ts\nvar reWireGetterSetter = (instance, hostRef) => {\n  var _a;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});\n  members.map(([memberName, [memberFlags]]) => {\n    if ((BUILD2.state || BUILD2.prop) && (memberFlags & 31 /* Prop */ || memberFlags & 32 /* State */)) {\n      const ogValue = instance[memberName];\n      const ogDescriptor = Object.getOwnPropertyDescriptor(Object.getPrototypeOf(instance), memberName);\n      Object.defineProperty(instance, memberName, {\n        get() {\n          return ogDescriptor.get.call(this);\n        },\n        set(newValue) {\n          ogDescriptor.set.call(this, newValue);\n        },\n        configurable: true,\n        enumerable: true\n      });\n      instance[memberName] = hostRef.$instanceValues$.has(memberName) ? hostRef.$instanceValues$.get(memberName) : ogValue;\n    }\n  });\n};\n\n// src/client/client-host-ref.ts\nvar getHostRef = ref => {\n  if (ref.__stencil__getHostRef) {\n    return ref.__stencil__getHostRef();\n  }\n  return void 0;\n};\nvar registerInstance = (lazyInstance, hostRef) => {\n  lazyInstance.__stencil__getHostRef = () => hostRef;\n  hostRef.$lazyInstance$ = lazyInstance;\n  if (BUILD3.modernPropertyDecls && (BUILD3.state || BUILD3.prop)) {\n    reWireGetterSetter(lazyInstance, hostRef);\n  }\n};\nvar registerHost = (hostElement, cmpMeta) => {\n  const hostRef = {\n    $flags$: 0,\n    $hostElement$: hostElement,\n    $cmpMeta$: cmpMeta,\n    $instanceValues$: /* @__PURE__ */new Map()\n  };\n  if (BUILD3.isDev) {\n    hostRef.$renderCount$ = 0;\n  }\n  if (BUILD3.method && BUILD3.lazyLoad) {\n    hostRef.$onInstancePromise$ = new Promise(r => hostRef.$onInstanceResolve$ = r);\n  }\n  if (BUILD3.asyncLoading) {\n    hostRef.$onReadyPromise$ = new Promise(r => hostRef.$onReadyResolve$ = r);\n    hostElement[\"s-p\"] = [];\n    hostElement[\"s-rc\"] = [];\n  }\n  const ref = hostRef;\n  hostElement.__stencil__getHostRef = () => ref;\n  if (!BUILD3.lazyLoad && BUILD3.modernPropertyDecls && (BUILD3.state || BUILD3.prop)) {\n    reWireGetterSetter(hostElement, hostRef);\n  }\n  return ref;\n};\nvar isMemberInElement = (elm, memberName) => memberName in elm;\n\n// src/client/client-load-module.ts\nimport { BUILD as BUILD5 } from \"@stencil/core/internal/app-data\";\n\n// src/client/client-log.ts\nimport { BUILD as BUILD4 } from \"@stencil/core/internal/app-data\";\nvar customError;\nvar consoleError = (e, el) => (customError || console.error)(e, el);\nvar STENCIL_DEV_MODE = BUILD4.isTesting ? [\"STENCIL:\"] : [\"%cstencil\", \"color: white;background:#4c47ff;font-weight: bold; font-size:10px; padding:2px 6px; border-radius: 5px\"];\nvar consoleDevError = (...m) => console.error(...STENCIL_DEV_MODE, ...m);\nvar consoleDevWarn = (...m) => console.warn(...STENCIL_DEV_MODE, ...m);\nvar consoleDevInfo = (...m) => console.info(...STENCIL_DEV_MODE, ...m);\nvar setErrorHandler = handler => customError = handler;\n\n// src/client/client-load-module.ts\nvar cmpModules = /* @__PURE__ */new Map();\nvar MODULE_IMPORT_PREFIX = \"./\";\nvar loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n  const exportName = cmpMeta.$tagName$.replace(/-/g, \"_\");\n  const bundleId = cmpMeta.$lazyBundleId$;\n  if (BUILD5.isDev && typeof bundleId !== \"string\") {\n    consoleDevError(`Trying to lazily load component <${cmpMeta.$tagName$}> with style mode \"${hostRef.$modeName$}\", but it does not exist.`);\n    return void 0;\n  } else if (!bundleId) {\n    return void 0;\n  }\n  const module = !BUILD5.hotModuleReplacement ? cmpModules.get(bundleId) : false;\n  if (module) {\n    return module[exportName];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import(/* @vite-ignore */\n  /* webpackInclude: /\\.entry\\.js$/ */\n  /* webpackExclude: /\\.system\\.entry\\.js$/ */\n  /* webpackMode: \"lazy\" */\n  `./${bundleId}.entry.js${BUILD5.hotModuleReplacement && hmrVersionId ? \"?s-hmr=\" + hmrVersionId : \"\"}`).then(importedModule => {\n    if (!BUILD5.hotModuleReplacement) {\n      cmpModules.set(bundleId, importedModule);\n    }\n    return importedModule[exportName];\n  }, e => {\n    consoleError(e, hostRef.$hostElement$);\n  });\n};\n\n// src/client/client-style.ts\nvar styles = /* @__PURE__ */new Map();\nvar modeResolutionChain = [];\n\n// src/client/client-task-queue.ts\nimport { BUILD as BUILD7 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/runtime-constants.ts\nvar CONTENT_REF_ID = \"r\";\nvar ORG_LOCATION_ID = \"o\";\nvar SLOT_NODE_ID = \"s\";\nvar TEXT_NODE_ID = \"t\";\nvar COMMENT_NODE_ID = \"c\";\nvar HYDRATE_ID = \"s-id\";\nvar HYDRATED_STYLE_ID = \"sty-id\";\nvar HYDRATE_CHILD_ID = \"c-id\";\nvar HYDRATED_CSS = \"{visibility:hidden}.hydrated{visibility:inherit}\";\nvar STENCIL_DOC_DATA = \"_stencilDocData\";\nvar DEFAULT_DOC_DATA = {\n  hostIds: 0,\n  rootLevelIds: 0,\n  staticComponents: /* @__PURE__ */new Set()\n};\nvar SLOT_FB_CSS = \"slot-fb{display:contents}slot-fb[hidden]{display:none}\";\nvar XLINK_NS = \"http://www.w3.org/1999/xlink\";\nvar FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS = [\"formAssociatedCallback\", \"formResetCallback\", \"formDisabledCallback\", \"formStateRestoreCallback\"];\n\n// src/client/client-window.ts\nimport { BUILD as BUILD6 } from \"@stencil/core/internal/app-data\";\nvar win = typeof window !== \"undefined\" ? window : {};\nvar H = win.HTMLElement || class {};\nvar plt = {\n  $flags$: 0,\n  $resourcesUrl$: \"\",\n  jmp: h2 => h2(),\n  raf: h2 => requestAnimationFrame(h2),\n  ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n  rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n  ce: (eventName, opts) => new CustomEvent(eventName, opts)\n};\nvar setPlatformHelpers = helpers => {\n  Object.assign(plt, helpers);\n};\nvar supportsShadow = BUILD6.shadowDom;\nvar supportsListenerOptions = /* @__PURE__ */(() => {\n  var _a;\n  let supportsListenerOptions2 = false;\n  try {\n    (_a = win.document) == null ? void 0 : _a.addEventListener(\"e\", null, Object.defineProperty({}, \"passive\", {\n      get() {\n        supportsListenerOptions2 = true;\n      }\n    }));\n  } catch (e) {}\n  return supportsListenerOptions2;\n})();\nvar promiseResolve = v => Promise.resolve(v);\nvar supportsConstructableStylesheets = BUILD6.constructableCSS ? /* @__PURE__ */(() => {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === \"function\";\n  } catch (e) {}\n  return false;\n})() : false;\n\n// src/client/client-task-queue.ts\nvar queueCongestion = 0;\nvar queuePending = false;\nvar queueDomReads = [];\nvar queueDomWrites = [];\nvar queueDomWritesLow = [];\nvar queueTask = (queue, write) => cb => {\n  queue.push(cb);\n  if (!queuePending) {\n    queuePending = true;\n    if (write && plt.$flags$ & 4 /* queueSync */) {\n      nextTick(flush);\n    } else {\n      plt.raf(flush);\n    }\n  }\n};\nvar consume = queue => {\n  for (let i2 = 0; i2 < queue.length; i2++) {\n    try {\n      queue[i2](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  queue.length = 0;\n};\nvar consumeTimeout = (queue, timeout) => {\n  let i2 = 0;\n  let ts = 0;\n  while (i2 < queue.length && (ts = performance.now()) < timeout) {\n    try {\n      queue[i2++](ts);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  if (i2 === queue.length) {\n    queue.length = 0;\n  } else if (i2 !== 0) {\n    queue.splice(0, i2);\n  }\n};\nvar flush = () => {\n  if (BUILD7.asyncQueue) {\n    queueCongestion++;\n  }\n  consume(queueDomReads);\n  if (BUILD7.asyncQueue) {\n    const timeout = (plt.$flags$ & 6 /* queueMask */) === 2 /* appLoaded */ ? performance.now() + 14 * Math.ceil(queueCongestion * (1 / 10)) : Infinity;\n    consumeTimeout(queueDomWrites, timeout);\n    consumeTimeout(queueDomWritesLow, timeout);\n    if (queueDomWrites.length > 0) {\n      queueDomWritesLow.push(...queueDomWrites);\n      queueDomWrites.length = 0;\n    }\n    if (queuePending = queueDomReads.length + queueDomWrites.length + queueDomWritesLow.length > 0) {\n      plt.raf(flush);\n    } else {\n      queueCongestion = 0;\n    }\n  } else {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      plt.raf(flush);\n    }\n  }\n};\nvar nextTick = cb => promiseResolve().then(cb);\nvar readTask = /* @__PURE__ */queueTask(queueDomReads, false);\nvar writeTask = /* @__PURE__ */queueTask(queueDomWrites, true);\n\n// src/client/index.ts\nimport { BUILD as BUILD30, Env, NAMESPACE as NAMESPACE2 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/asset-path.ts\nvar getAssetPath = path => {\n  const assetUrl = new URL(path, plt.$resourcesUrl$);\n  return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\nvar setAssetPath = path => plt.$resourcesUrl$ = path;\n\n// src/runtime/bootstrap-custom-element.ts\nimport { BUILD as BUILD27 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/helpers.ts\nvar isDef = v => v != null && v !== void 0;\nvar isComplexType = o => {\n  o = typeof o;\n  return o === \"object\" || o === \"function\";\n};\n\n// src/utils/query-nonce-meta-tag-content.ts\nfunction queryNonceMetaTagContent(doc) {\n  var _a, _b, _c;\n  return (_c = (_b = (_a = doc.head) == null ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) == null ? void 0 : _b.getAttribute(\"content\")) != null ? _c : void 0;\n}\n\n// src/utils/regular-expression.ts\nvar escapeRegExpSpecialCharacters = text => {\n  return text.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n};\n\n// src/utils/remote-value.ts\nvar RemoteValue = class _RemoteValue {\n  /**\n   * Deserializes a LocalValue serialized object back to its original JavaScript representation\n   *\n   * @param serialized The serialized LocalValue object\n   * @returns The original JavaScript value/object\n   */\n  static fromLocalValue(serialized) {\n    const type = serialized[TYPE_CONSTANT];\n    const value = VALUE_CONSTANT in serialized ? serialized[VALUE_CONSTANT] : void 0;\n    switch (type) {\n      case \"string\" /* String */:\n        return value;\n      case \"boolean\" /* Boolean */:\n        return value;\n      case \"bigint\" /* BigInt */:\n        return BigInt(value);\n      case \"undefined\" /* Undefined */:\n        return void 0;\n      case \"null\" /* Null */:\n        return null;\n      case \"number\" /* Number */:\n        if (value === \"NaN\") return NaN;\n        if (value === \"-0\") return -0;\n        if (value === \"Infinity\") return Infinity;\n        if (value === \"-Infinity\") return -Infinity;\n        return value;\n      case \"array\" /* Array */:\n        return value.map(item => _RemoteValue.fromLocalValue(item));\n      case \"date\" /* Date */:\n        return new Date(value);\n      case \"map\" /* Map */:\n        const map2 = /* @__PURE__ */new Map();\n        for (const [key, val] of value) {\n          const deserializedKey = typeof key === \"object\" && key !== null ? _RemoteValue.fromLocalValue(key) : key;\n          const deserializedValue = _RemoteValue.fromLocalValue(val);\n          map2.set(deserializedKey, deserializedValue);\n        }\n        return map2;\n      case \"object\" /* Object */:\n        const obj = {};\n        for (const [key, val] of value) {\n          obj[key] = _RemoteValue.fromLocalValue(val);\n        }\n        return obj;\n      case \"regexp\" /* RegularExpression */:\n        const {\n          pattern,\n          flags\n        } = value;\n        return new RegExp(pattern, flags);\n      case \"set\" /* Set */:\n        const set = /* @__PURE__ */new Set();\n        for (const item of value) {\n          set.add(_RemoteValue.fromLocalValue(item));\n        }\n        return set;\n      case \"symbol\" /* Symbol */:\n        return Symbol(value);\n      default:\n        throw new Error(`Unsupported type: ${type}`);\n    }\n  }\n  /**\n   * Utility method to deserialize multiple LocalValues at once\n   *\n   * @param serializedValues Array of serialized LocalValue objects\n   * @returns Array of deserialized JavaScript values\n   */\n  static fromLocalValueArray(serializedValues) {\n    return serializedValues.map(value => _RemoteValue.fromLocalValue(value));\n  }\n  /**\n   * Verifies if the given object matches the structure of a serialized LocalValue\n   *\n   * @param obj Object to verify\n   * @returns boolean indicating if the object has LocalValue structure\n   */\n  static isLocalValueObject(obj) {\n    if (typeof obj !== \"object\" || obj === null) {\n      return false;\n    }\n    if (!obj.hasOwnProperty(TYPE_CONSTANT)) {\n      return false;\n    }\n    const type = obj[TYPE_CONSTANT];\n    const hasTypeProperty = Object.values({\n      ...PrimitiveType,\n      ...NonPrimitiveType\n    }).includes(type);\n    if (!hasTypeProperty) {\n      return false;\n    }\n    if (type !== \"null\" /* Null */ && type !== \"undefined\" /* Undefined */) {\n      return obj.hasOwnProperty(VALUE_CONSTANT);\n    }\n    return true;\n  }\n};\n\n// src/utils/result.ts\nvar result_exports = {};\n__export(result_exports, {\n  err: () => err,\n  map: () => map,\n  ok: () => ok,\n  unwrap: () => unwrap,\n  unwrapErr: () => unwrapErr\n});\nvar ok = value => ({\n  isOk: true,\n  isErr: false,\n  value\n});\nvar err = value => ({\n  isOk: false,\n  isErr: true,\n  value\n});\nfunction map(result, fn) {\n  if (result.isOk) {\n    const val = fn(result.value);\n    if (val instanceof Promise) {\n      return val.then(newVal => ok(newVal));\n    } else {\n      return ok(val);\n    }\n  }\n  if (result.isErr) {\n    const value = result.value;\n    return err(value);\n  }\n  throw \"should never get here\";\n}\nvar unwrap = result => {\n  if (result.isOk) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\nvar unwrapErr = result => {\n  if (result.isErr) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\n\n// src/utils/serialize.ts\nfunction deserializeProperty(value) {\n  if (typeof value !== \"string\" || !value.startsWith(SERIALIZED_PREFIX)) {\n    return value;\n  }\n  return RemoteValue.fromLocalValue(JSON.parse(atob(value.slice(SERIALIZED_PREFIX.length))));\n}\n\n// src/utils/shadow-root.ts\nimport { BUILD as BUILD8 } from \"@stencil/core/internal/app-data\";\nimport { globalStyles } from \"@stencil/core/internal/app-globals\";\nfunction createShadowRoot(cmpMeta) {\n  const shadowRoot = BUILD8.shadowDelegatesFocus ? this.attachShadow({\n    mode: \"open\",\n    delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n  }) : this.attachShadow({\n    mode: \"open\"\n  });\n  if (supportsConstructableStylesheets) {\n    const sheet = new CSSStyleSheet();\n    sheet.replaceSync(globalStyles);\n    shadowRoot.adoptedStyleSheets.push(sheet);\n  }\n}\n\n// src/utils/util.ts\nvar lowerPathParam = fn => p => fn(p.toLowerCase());\nvar isDtsFile = lowerPathParam(p => p.endsWith(\".d.ts\") || p.endsWith(\".d.mts\") || p.endsWith(\".d.cts\"));\nvar isTsFile = lowerPathParam(p => !isDtsFile(p) && (p.endsWith(\".ts\") || p.endsWith(\".mts\") || p.endsWith(\".cts\")));\nvar isTsxFile = lowerPathParam(p => p.endsWith(\".tsx\") || p.endsWith(\".mtsx\") || p.endsWith(\".ctsx\"));\nvar isJsxFile = lowerPathParam(p => p.endsWith(\".jsx\") || p.endsWith(\".mjsx\") || p.endsWith(\".cjsx\"));\nvar isJsFile = lowerPathParam(p => p.endsWith(\".js\") || p.endsWith(\".mjs\") || p.endsWith(\".cjs\"));\n\n// src/runtime/connected-callback.ts\nimport { BUILD as BUILD25 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/client-hydrate.ts\nimport { BUILD as BUILD13 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/dom-extras.ts\nimport { BUILD as BUILD10 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/slot-polyfill-utils.ts\nimport { BUILD as BUILD9 } from \"@stencil/core/internal/app-data\";\nvar updateFallbackSlotVisibility = elm => {\n  const childNodes = internalCall(elm, \"childNodes\");\n  if (elm.tagName && elm.tagName.includes(\"-\") && elm[\"s-cr\"] && elm.tagName !== \"SLOT-FB\") {\n    getHostSlotNodes(childNodes, elm.tagName).forEach(slotNode => {\n      if (slotNode.nodeType === 1 /* ElementNode */ && slotNode.tagName === \"SLOT-FB\") {\n        if (getSlotChildSiblings(slotNode, getSlotName(slotNode), false).length) {\n          slotNode.hidden = true;\n        } else {\n          slotNode.hidden = false;\n        }\n      }\n    });\n  }\n  let i2 = 0;\n  for (i2 = 0; i2 < childNodes.length; i2++) {\n    const childNode = childNodes[i2];\n    if (childNode.nodeType === 1 /* ElementNode */ && internalCall(childNode, \"childNodes\").length) {\n      updateFallbackSlotVisibility(childNode);\n    }\n  }\n};\nvar getSlottedChildNodes = childNodes => {\n  const result = [];\n  for (let i2 = 0; i2 < childNodes.length; i2++) {\n    const slottedNode = childNodes[i2][\"s-nr\"] || void 0;\n    if (slottedNode && slottedNode.isConnected) {\n      result.push(slottedNode);\n    }\n  }\n  return result;\n};\nfunction getHostSlotNodes(childNodes, hostName, slotName) {\n  let i2 = 0;\n  let slottedNodes = [];\n  let childNode;\n  for (; i2 < childNodes.length; i2++) {\n    childNode = childNodes[i2];\n    if (childNode[\"s-sr\"] && (!hostName || childNode[\"s-hn\"] === hostName) && (slotName === void 0 || getSlotName(childNode) === slotName)) {\n      slottedNodes.push(childNode);\n      if (typeof slotName !== \"undefined\") return slottedNodes;\n    }\n    slottedNodes = [...slottedNodes, ...getHostSlotNodes(childNode.childNodes, hostName, slotName)];\n  }\n  return slottedNodes;\n}\nvar getSlotChildSiblings = (slot, slotName, includeSlot = true) => {\n  const childNodes = [];\n  if (includeSlot && slot[\"s-sr\"] || !slot[\"s-sr\"]) childNodes.push(slot);\n  let node = slot;\n  while (node = node.nextSibling) {\n    if (getSlotName(node) === slotName && (includeSlot || !node[\"s-sr\"])) childNodes.push(node);\n  }\n  return childNodes;\n};\nvar isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n  if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n    if (nodeToRelocate.getAttribute(\"slot\") === null && slotName === \"\") {\n      return true;\n    }\n    if (nodeToRelocate.getAttribute(\"slot\") === slotName) {\n      return true;\n    }\n    return false;\n  }\n  if (nodeToRelocate[\"s-sn\"] === slotName) {\n    return true;\n  }\n  return slotName === \"\";\n};\nvar addSlotRelocateNode = (newChild, slotNode, prepend, position) => {\n  if (newChild[\"s-ol\"] && newChild[\"s-ol\"].isConnected) {\n    return;\n  }\n  const slottedNodeLocation = document.createTextNode(\"\");\n  slottedNodeLocation[\"s-nr\"] = newChild;\n  if (!slotNode[\"s-cr\"] || !slotNode[\"s-cr\"].parentNode) return;\n  const parent = slotNode[\"s-cr\"].parentNode;\n  const appendMethod = prepend ? internalCall(parent, \"prepend\") : internalCall(parent, \"appendChild\");\n  if (BUILD9.hydrateClientSide && typeof position !== \"undefined\") {\n    slottedNodeLocation[\"s-oo\"] = position;\n    const childNodes = internalCall(parent, \"childNodes\");\n    const slotRelocateNodes = [slottedNodeLocation];\n    childNodes.forEach(n => {\n      if (n[\"s-nr\"]) slotRelocateNodes.push(n);\n    });\n    slotRelocateNodes.sort((a, b) => {\n      if (!a[\"s-oo\"] || a[\"s-oo\"] < (b[\"s-oo\"] || 0)) return -1;else if (!b[\"s-oo\"] || b[\"s-oo\"] < a[\"s-oo\"]) return 1;\n      return 0;\n    });\n    slotRelocateNodes.forEach(n => appendMethod.call(parent, n));\n  } else {\n    appendMethod.call(parent, slottedNodeLocation);\n  }\n  newChild[\"s-ol\"] = slottedNodeLocation;\n  newChild[\"s-sh\"] = slotNode[\"s-hn\"];\n};\nvar getSlotName = node => typeof node[\"s-sn\"] === \"string\" ? node[\"s-sn\"] : node.nodeType === 1 && node.getAttribute(\"slot\") || void 0;\nfunction patchSlotNode(node) {\n  if (node.assignedElements || node.assignedNodes || !node[\"s-sr\"]) return;\n  const assignedFactory = elementsOnly => function (opts) {\n    const toReturn = [];\n    const slotName = this[\"s-sn\"];\n    if (opts == null ? void 0 : opts.flatten) {\n      console.error(`\n          Flattening is not supported for Stencil non-shadow slots. \n          You can use \\`.childNodes\\` to nested slot fallback content.\n          If you have a particular use case, please open an issue on the Stencil repo.\n        `);\n    }\n    const parent = this[\"s-cr\"].parentElement;\n    const slottedNodes = parent.__childNodes ? parent.childNodes : getSlottedChildNodes(parent.childNodes);\n    slottedNodes.forEach(n => {\n      if (slotName === getSlotName(n)) {\n        toReturn.push(n);\n      }\n    });\n    if (elementsOnly) {\n      return toReturn.filter(n => n.nodeType === 1 /* ElementNode */);\n    }\n    return toReturn;\n  }.bind(node);\n  node.assignedElements = assignedFactory(true);\n  node.assignedNodes = assignedFactory(false);\n}\nfunction dispatchSlotChangeEvent(elm) {\n  elm.dispatchEvent(new CustomEvent(\"slotchange\", {\n    bubbles: false,\n    cancelable: false,\n    composed: false\n  }));\n}\nfunction findSlotFromSlottedNode(slottedNode, parentHost) {\n  var _a;\n  parentHost = parentHost || ((_a = slottedNode[\"s-ol\"]) == null ? void 0 : _a.parentElement);\n  if (!parentHost) return {\n    slotNode: null,\n    slotName: \"\"\n  };\n  const slotName = slottedNode[\"s-sn\"] = getSlotName(slottedNode) || \"\";\n  const childNodes = internalCall(parentHost, \"childNodes\");\n  const slotNode = getHostSlotNodes(childNodes, parentHost.tagName, slotName)[0];\n  return {\n    slotNode,\n    slotName\n  };\n}\n\n// src/runtime/dom-extras.ts\nvar patchPseudoShadowDom = hostElementPrototype => {\n  patchCloneNode(hostElementPrototype);\n  patchSlotAppendChild(hostElementPrototype);\n  patchSlotAppend(hostElementPrototype);\n  patchSlotPrepend(hostElementPrototype);\n  patchSlotInsertAdjacentElement(hostElementPrototype);\n  patchSlotInsertAdjacentHTML(hostElementPrototype);\n  patchSlotInsertAdjacentText(hostElementPrototype);\n  patchInsertBefore(hostElementPrototype);\n  patchTextContent(hostElementPrototype);\n  patchChildSlotNodes(hostElementPrototype);\n  patchSlotRemoveChild(hostElementPrototype);\n};\nvar patchCloneNode = HostElementPrototype => {\n  const orgCloneNode = HostElementPrototype.cloneNode;\n  HostElementPrototype.cloneNode = function (deep) {\n    const srcNode = this;\n    const isShadowDom = BUILD10.shadowDom ? srcNode.shadowRoot && supportsShadow : false;\n    const clonedNode = orgCloneNode.call(srcNode, isShadowDom ? deep : false);\n    if (BUILD10.slot && !isShadowDom && deep) {\n      let i2 = 0;\n      let slotted, nonStencilNode;\n      const stencilPrivates = [\"s-id\", \"s-cr\", \"s-lr\", \"s-rc\", \"s-sc\", \"s-p\", \"s-cn\", \"s-sr\", \"s-sn\", \"s-hn\", \"s-ol\", \"s-nr\", \"s-si\", \"s-rf\", \"s-scs\"];\n      const childNodes = this.__childNodes || this.childNodes;\n      for (; i2 < childNodes.length; i2++) {\n        slotted = childNodes[i2][\"s-nr\"];\n        nonStencilNode = stencilPrivates.every(privateField => !childNodes[i2][privateField]);\n        if (slotted) {\n          if (BUILD10.appendChildSlotFix && clonedNode.__appendChild) {\n            clonedNode.__appendChild(slotted.cloneNode(true));\n          } else {\n            clonedNode.appendChild(slotted.cloneNode(true));\n          }\n        }\n        if (nonStencilNode) {\n          clonedNode.appendChild(childNodes[i2].cloneNode(true));\n        }\n      }\n    }\n    return clonedNode;\n  };\n};\nvar patchSlotAppendChild = HostElementPrototype => {\n  HostElementPrototype.__appendChild = HostElementPrototype.appendChild;\n  HostElementPrototype.appendChild = function (newChild) {\n    const {\n      slotName,\n      slotNode\n    } = findSlotFromSlottedNode(newChild, this);\n    if (slotNode) {\n      addSlotRelocateNode(newChild, slotNode);\n      const slotChildNodes = getSlotChildSiblings(slotNode, slotName);\n      const appendAfter = slotChildNodes[slotChildNodes.length - 1];\n      const parent = internalCall(appendAfter, \"parentNode\");\n      const insertedNode = internalCall(parent, \"insertBefore\")(newChild, appendAfter.nextSibling);\n      dispatchSlotChangeEvent(slotNode);\n      updateFallbackSlotVisibility(this);\n      return insertedNode;\n    }\n    return this.__appendChild(newChild);\n  };\n};\nvar patchSlotRemoveChild = ElementPrototype => {\n  ElementPrototype.__removeChild = ElementPrototype.removeChild;\n  ElementPrototype.removeChild = function (toRemove) {\n    if (toRemove && typeof toRemove[\"s-sn\"] !== \"undefined\") {\n      const childNodes = this.__childNodes || this.childNodes;\n      const slotNode = getHostSlotNodes(childNodes, this.tagName, toRemove[\"s-sn\"]);\n      if (slotNode && toRemove.isConnected) {\n        toRemove.remove();\n        updateFallbackSlotVisibility(this);\n        return;\n      }\n    }\n    return this.__removeChild(toRemove);\n  };\n};\nvar patchSlotPrepend = HostElementPrototype => {\n  HostElementPrototype.__prepend = HostElementPrototype.prepend;\n  HostElementPrototype.prepend = function (...newChildren) {\n    newChildren.forEach(newChild => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      const slotName = (newChild[\"s-sn\"] = getSlotName(newChild)) || \"\";\n      const childNodes = internalCall(this, \"childNodes\");\n      const slotNode = getHostSlotNodes(childNodes, this.tagName, slotName)[0];\n      if (slotNode) {\n        addSlotRelocateNode(newChild, slotNode, true);\n        const slotChildNodes = getSlotChildSiblings(slotNode, slotName);\n        const appendAfter = slotChildNodes[0];\n        const parent = internalCall(appendAfter, \"parentNode\");\n        const toReturn = internalCall(parent, \"insertBefore\")(newChild, internalCall(appendAfter, \"nextSibling\"));\n        dispatchSlotChangeEvent(slotNode);\n        return toReturn;\n      }\n      if (newChild.nodeType === 1 && !!newChild.getAttribute(\"slot\")) {\n        newChild.hidden = true;\n      }\n      return HostElementPrototype.__prepend(newChild);\n    });\n  };\n};\nvar patchSlotAppend = HostElementPrototype => {\n  HostElementPrototype.__append = HostElementPrototype.append;\n  HostElementPrototype.append = function (...newChildren) {\n    newChildren.forEach(newChild => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      this.appendChild(newChild);\n    });\n  };\n};\nvar patchSlotInsertAdjacentHTML = HostElementPrototype => {\n  const originalInsertAdjacentHtml = HostElementPrototype.insertAdjacentHTML;\n  HostElementPrototype.insertAdjacentHTML = function (position, text) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentHtml.call(this, position, text);\n    }\n    const container = this.ownerDocument.createElement(\"_\");\n    let node;\n    container.innerHTML = text;\n    if (position === \"afterbegin\") {\n      while (node = container.firstChild) {\n        this.prepend(node);\n      }\n    } else if (position === \"beforeend\") {\n      while (node = container.firstChild) {\n        this.append(node);\n      }\n    }\n  };\n};\nvar patchSlotInsertAdjacentText = HostElementPrototype => {\n  HostElementPrototype.insertAdjacentText = function (position, text) {\n    this.insertAdjacentHTML(position, text);\n  };\n};\nvar patchInsertBefore = HostElementPrototype => {\n  const eleProto = HostElementPrototype;\n  if (eleProto.__insertBefore) return;\n  eleProto.__insertBefore = HostElementPrototype.insertBefore;\n  HostElementPrototype.insertBefore = function (newChild, currentChild) {\n    const {\n      slotName,\n      slotNode\n    } = findSlotFromSlottedNode(newChild, this);\n    const slottedNodes = this.__childNodes ? this.childNodes : getSlottedChildNodes(this.childNodes);\n    if (slotNode) {\n      let found = false;\n      slottedNodes.forEach(childNode => {\n        if (childNode === currentChild || currentChild === null) {\n          found = true;\n          if (currentChild === null || slotName !== currentChild[\"s-sn\"]) {\n            this.appendChild(newChild);\n            return;\n          }\n          if (slotName === currentChild[\"s-sn\"]) {\n            addSlotRelocateNode(newChild, slotNode);\n            const parent = internalCall(currentChild, \"parentNode\");\n            internalCall(parent, \"insertBefore\")(newChild, currentChild);\n            dispatchSlotChangeEvent(slotNode);\n          }\n          return;\n        }\n      });\n      if (found) return newChild;\n    }\n    const parentNode = currentChild == null ? void 0 : currentChild.__parentNode;\n    if (parentNode && !this.isSameNode(parentNode)) {\n      return this.appendChild(newChild);\n    }\n    return this.__insertBefore(newChild, currentChild);\n  };\n};\nvar patchSlotInsertAdjacentElement = HostElementPrototype => {\n  const originalInsertAdjacentElement = HostElementPrototype.insertAdjacentElement;\n  HostElementPrototype.insertAdjacentElement = function (position, element) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentElement.call(this, position, element);\n    }\n    if (position === \"afterbegin\") {\n      this.prepend(element);\n      return element;\n    } else if (position === \"beforeend\") {\n      this.append(element);\n      return element;\n    }\n    return element;\n  };\n};\nvar patchTextContent = hostElementPrototype => {\n  patchHostOriginalAccessor(\"textContent\", hostElementPrototype);\n  Object.defineProperty(hostElementPrototype, \"textContent\", {\n    get: function () {\n      let text = \"\";\n      const childNodes = this.__childNodes ? this.childNodes : getSlottedChildNodes(this.childNodes);\n      childNodes.forEach(node => text += node.textContent || \"\");\n      return text;\n    },\n    set: function (value) {\n      const childNodes = this.__childNodes ? this.childNodes : getSlottedChildNodes(this.childNodes);\n      childNodes.forEach(node => {\n        if (node[\"s-ol\"]) node[\"s-ol\"].remove();\n        node.remove();\n      });\n      this.insertAdjacentHTML(\"beforeend\", value);\n    }\n  });\n};\nvar patchChildSlotNodes = elm => {\n  class FakeNodeList extends Array {\n    item(n) {\n      return this[n];\n    }\n  }\n  patchHostOriginalAccessor(\"children\", elm);\n  Object.defineProperty(elm, \"children\", {\n    get() {\n      return this.childNodes.filter(n => n.nodeType === 1);\n    }\n  });\n  Object.defineProperty(elm, \"childElementCount\", {\n    get() {\n      return this.children.length;\n    }\n  });\n  patchHostOriginalAccessor(\"firstChild\", elm);\n  Object.defineProperty(elm, \"firstChild\", {\n    get() {\n      return this.childNodes[0];\n    }\n  });\n  patchHostOriginalAccessor(\"lastChild\", elm);\n  Object.defineProperty(elm, \"lastChild\", {\n    get() {\n      return this.childNodes[this.childNodes.length - 1];\n    }\n  });\n  patchHostOriginalAccessor(\"childNodes\", elm);\n  Object.defineProperty(elm, \"childNodes\", {\n    get() {\n      const result = new FakeNodeList();\n      result.push(...getSlottedChildNodes(this.__childNodes));\n      return result;\n    }\n  });\n};\nvar patchSlottedNode = node => {\n  if (!node || node.__nextSibling !== void 0 || !globalThis.Node) return;\n  patchNextSibling(node);\n  patchPreviousSibling(node);\n  patchParentNode(node);\n  if (node.nodeType === Node.ELEMENT_NODE) {\n    patchNextElementSibling(node);\n    patchPreviousElementSibling(node);\n  }\n};\nvar patchNextSibling = node => {\n  if (!node || node.__nextSibling) return;\n  patchHostOriginalAccessor(\"nextSibling\", node);\n  Object.defineProperty(node, \"nextSibling\", {\n    get: function () {\n      var _a;\n      const parentNodes = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.childNodes;\n      const index = parentNodes == null ? void 0 : parentNodes.indexOf(this);\n      if (parentNodes && index > -1) {\n        return parentNodes[index + 1];\n      }\n      return this.__nextSibling;\n    }\n  });\n};\nvar patchNextElementSibling = element => {\n  if (!element || element.__nextElementSibling) return;\n  patchHostOriginalAccessor(\"nextElementSibling\", element);\n  Object.defineProperty(element, \"nextElementSibling\", {\n    get: function () {\n      var _a;\n      const parentEles = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.children;\n      const index = parentEles == null ? void 0 : parentEles.indexOf(this);\n      if (parentEles && index > -1) {\n        return parentEles[index + 1];\n      }\n      return this.__nextElementSibling;\n    }\n  });\n};\nvar patchPreviousSibling = node => {\n  if (!node || node.__previousSibling) return;\n  patchHostOriginalAccessor(\"previousSibling\", node);\n  Object.defineProperty(node, \"previousSibling\", {\n    get: function () {\n      var _a;\n      const parentNodes = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.childNodes;\n      const index = parentNodes == null ? void 0 : parentNodes.indexOf(this);\n      if (parentNodes && index > -1) {\n        return parentNodes[index - 1];\n      }\n      return this.__previousSibling;\n    }\n  });\n};\nvar patchPreviousElementSibling = element => {\n  if (!element || element.__previousElementSibling) return;\n  patchHostOriginalAccessor(\"previousElementSibling\", element);\n  Object.defineProperty(element, \"previousElementSibling\", {\n    get: function () {\n      var _a;\n      const parentNodes = (_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode.children;\n      const index = parentNodes == null ? void 0 : parentNodes.indexOf(this);\n      if (parentNodes && index > -1) {\n        return parentNodes[index - 1];\n      }\n      return this.__previousElementSibling;\n    }\n  });\n};\nvar patchParentNode = node => {\n  if (!node || node.__parentNode) return;\n  patchHostOriginalAccessor(\"parentNode\", node);\n  Object.defineProperty(node, \"parentNode\", {\n    get: function () {\n      var _a;\n      return ((_a = this[\"s-ol\"]) == null ? void 0 : _a.parentNode) || this.__parentNode;\n    },\n    set: function (value) {\n      this.__parentNode = value;\n    }\n  });\n};\nvar validElementPatches = [\"children\", \"nextElementSibling\", \"previousElementSibling\"];\nvar validNodesPatches = [\"childNodes\", \"firstChild\", \"lastChild\", \"nextSibling\", \"previousSibling\", \"textContent\", \"parentNode\"];\nfunction patchHostOriginalAccessor(accessorName, node) {\n  let accessor;\n  if (validElementPatches.includes(accessorName)) {\n    accessor = Object.getOwnPropertyDescriptor(Element.prototype, accessorName);\n  } else if (validNodesPatches.includes(accessorName)) {\n    accessor = Object.getOwnPropertyDescriptor(Node.prototype, accessorName);\n  }\n  if (!accessor) {\n    accessor = Object.getOwnPropertyDescriptor(node, accessorName);\n  }\n  if (accessor) Object.defineProperty(node, \"__\" + accessorName, accessor);\n}\nfunction internalCall(node, method) {\n  if (\"__\" + method in node) {\n    const toReturn = node[\"__\" + method];\n    if (typeof toReturn !== \"function\") return toReturn;\n    return toReturn.bind(node);\n  } else {\n    if (typeof node[method] !== \"function\") return node[method];\n    return node[method].bind(node);\n  }\n}\n\n// src/runtime/profile.ts\nimport { BUILD as BUILD11 } from \"@stencil/core/internal/app-data\";\nvar i = 0;\nvar createTime = (fnName, tagName = \"\") => {\n  if (BUILD11.profile && performance.mark) {\n    const key = `st:${fnName}:${tagName}:${i++}`;\n    performance.mark(key);\n    return () => performance.measure(`[Stencil] ${fnName}() <${tagName}>`, key);\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar uniqueTime = (key, measureText) => {\n  if (BUILD11.profile && performance.mark) {\n    if (performance.getEntriesByName(key, \"mark\").length === 0) {\n      performance.mark(key);\n    }\n    return () => {\n      if (performance.getEntriesByName(measureText, \"measure\").length === 0) {\n        performance.measure(measureText, key);\n      }\n    };\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar inspect = ref => {\n  const hostRef = getHostRef(ref);\n  if (!hostRef) {\n    return void 0;\n  }\n  const flags = hostRef.$flags$;\n  const hostElement = hostRef.$hostElement$;\n  return {\n    renderCount: hostRef.$renderCount$,\n    flags: {\n      hasRendered: !!(flags & 2 /* hasRendered */),\n      hasConnected: !!(flags & 1 /* hasConnected */),\n      isWaitingForChildren: !!(flags & 4 /* isWaitingForChildren */),\n      isConstructingInstance: !!(flags & 8 /* isConstructingInstance */),\n      isQueuedForUpdate: !!(flags & 16 /* isQueuedForUpdate */),\n      hasInitializedComponent: !!(flags & 32 /* hasInitializedComponent */),\n      hasLoadedComponent: !!(flags & 64 /* hasLoadedComponent */),\n      isWatchReady: !!(flags & 128 /* isWatchReady */),\n      isListenReady: !!(flags & 256 /* isListenReady */),\n      needsRerender: !!(flags & 512 /* needsRerender */)\n    },\n    instanceValues: hostRef.$instanceValues$,\n    ancestorComponent: hostRef.$ancestorComponent$,\n    hostElement,\n    lazyInstance: hostRef.$lazyInstance$,\n    vnode: hostRef.$vnode$,\n    modeName: hostRef.$modeName$,\n    onReadyPromise: hostRef.$onReadyPromise$,\n    onReadyResolve: hostRef.$onReadyResolve$,\n    onInstancePromise: hostRef.$onInstancePromise$,\n    onInstanceResolve: hostRef.$onInstanceResolve$,\n    onRenderResolve: hostRef.$onRenderResolve$,\n    queuedListeners: hostRef.$queuedListeners$,\n    rmListeners: hostRef.$rmListeners$,\n    [\"s-id\"]: hostElement[\"s-id\"],\n    [\"s-cr\"]: hostElement[\"s-cr\"],\n    [\"s-lr\"]: hostElement[\"s-lr\"],\n    [\"s-p\"]: hostElement[\"s-p\"],\n    [\"s-rc\"]: hostElement[\"s-rc\"],\n    [\"s-sc\"]: hostElement[\"s-sc\"]\n  };\n};\nvar installDevTools = () => {\n  if (BUILD11.devTools) {\n    const stencil = win.stencil = win.stencil || {};\n    const originalInspect = stencil.inspect;\n    stencil.inspect = ref => {\n      let result = inspect(ref);\n      if (!result && typeof originalInspect === \"function\") {\n        result = originalInspect(ref);\n      }\n      return result;\n    };\n  }\n};\n\n// src/runtime/vdom/h.ts\nimport { BUILD as BUILD12 } from \"@stencil/core/internal/app-data\";\nvar h = (nodeName, vnodeData, ...children) => {\n  let child = null;\n  let key = null;\n  let slotName = null;\n  let simple = false;\n  let lastSimple = false;\n  const vNodeChildren = [];\n  const walk = c => {\n    for (let i2 = 0; i2 < c.length; i2++) {\n      child = c[i2];\n      if (Array.isArray(child)) {\n        walk(child);\n      } else if (child != null && typeof child !== \"boolean\") {\n        if (simple = typeof nodeName !== \"function\" && !isComplexType(child)) {\n          child = String(child);\n        } else if (BUILD12.isDev && typeof nodeName !== \"function\" && child.$flags$ === void 0) {\n          consoleDevError(`vNode passed as children has unexpected type.\nMake sure it's using the correct h() function.\nEmpty objects can also be the cause, look for JSX comments that became objects.`);\n        }\n        if (simple && lastSimple) {\n          vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n        } else {\n          vNodeChildren.push(simple ? newVNode(null, child) : child);\n        }\n        lastSimple = simple;\n      }\n    }\n  };\n  walk(children);\n  if (vnodeData) {\n    if (BUILD12.isDev && nodeName === \"input\") {\n      validateInputProperties(vnodeData);\n    }\n    if (BUILD12.vdomKey && vnodeData.key) {\n      key = vnodeData.key;\n    }\n    if (BUILD12.slotRelocation && vnodeData.name) {\n      slotName = vnodeData.name;\n    }\n    if (BUILD12.vdomClass) {\n      const classData = vnodeData.className || vnodeData.class;\n      if (classData) {\n        vnodeData.class = typeof classData !== \"object\" ? classData : Object.keys(classData).filter(k => classData[k]).join(\" \");\n      }\n    }\n  }\n  if (BUILD12.isDev && vNodeChildren.some(isHost)) {\n    consoleDevError(`The <Host> must be the single root component. Make sure:\n- You are NOT using hostData() and <Host> in the same component.\n- <Host> is used once, and it's the single root component of the render() function.`);\n  }\n  if (BUILD12.vdomFunctional && typeof nodeName === \"function\") {\n    return nodeName(vnodeData === null ? {} : vnodeData, vNodeChildren, vdomFnUtils);\n  }\n  const vnode = newVNode(nodeName, null);\n  vnode.$attrs$ = vnodeData;\n  if (vNodeChildren.length > 0) {\n    vnode.$children$ = vNodeChildren;\n  }\n  if (BUILD12.vdomKey) {\n    vnode.$key$ = key;\n  }\n  if (BUILD12.slotRelocation) {\n    vnode.$name$ = slotName;\n  }\n  return vnode;\n};\nvar newVNode = (tag, text) => {\n  const vnode = {\n    $flags$: 0,\n    $tag$: tag,\n    $text$: text,\n    $elm$: null,\n    $children$: null\n  };\n  if (BUILD12.vdomAttribute) {\n    vnode.$attrs$ = null;\n  }\n  if (BUILD12.vdomKey) {\n    vnode.$key$ = null;\n  }\n  if (BUILD12.slotRelocation) {\n    vnode.$name$ = null;\n  }\n  return vnode;\n};\nvar Host = {};\nvar isHost = node => node && node.$tag$ === Host;\nvar vdomFnUtils = {\n  forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n  map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)\n};\nvar convertToPublic = node => ({\n  vattrs: node.$attrs$,\n  vchildren: node.$children$,\n  vkey: node.$key$,\n  vname: node.$name$,\n  vtag: node.$tag$,\n  vtext: node.$text$\n});\nvar convertToPrivate = node => {\n  if (typeof node.vtag === \"function\") {\n    const vnodeData = {\n      ...node.vattrs\n    };\n    if (node.vkey) {\n      vnodeData.key = node.vkey;\n    }\n    if (node.vname) {\n      vnodeData.name = node.vname;\n    }\n    return h(node.vtag, vnodeData, ...(node.vchildren || []));\n  }\n  const vnode = newVNode(node.vtag, node.vtext);\n  vnode.$attrs$ = node.vattrs;\n  vnode.$children$ = node.vchildren;\n  vnode.$key$ = node.vkey;\n  vnode.$name$ = node.vname;\n  return vnode;\n};\nvar validateInputProperties = inputElm => {\n  const props = Object.keys(inputElm);\n  const value = props.indexOf(\"value\");\n  if (value === -1) {\n    return;\n  }\n  const typeIndex = props.indexOf(\"type\");\n  const minIndex = props.indexOf(\"min\");\n  const maxIndex = props.indexOf(\"max\");\n  const stepIndex = props.indexOf(\"step\");\n  if (value < typeIndex || value < minIndex || value < maxIndex || value < stepIndex) {\n    consoleDevWarn(`The \"value\" prop of <input> should be set after \"min\", \"max\", \"type\" and \"step\"`);\n  }\n};\n\n// src/runtime/client-hydrate.ts\nvar initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n  var _a;\n  const endHydrate = createTime(\"hydrateClient\", tagName);\n  const shadowRoot = hostElm.shadowRoot;\n  const childRenderNodes = [];\n  const slotNodes = [];\n  const slottedNodes = [];\n  const shadowRootNodes = BUILD13.shadowDom && shadowRoot ? [] : null;\n  const vnode = newVNode(tagName, null);\n  vnode.$elm$ = hostElm;\n  const members = Object.entries(((_a = hostRef.$cmpMeta$) == null ? void 0 : _a.$members$) || {});\n  members.forEach(([memberName, [memberFlags, metaAttributeName]]) => {\n    var _a2;\n    if (!(memberFlags & 31 /* Prop */)) {\n      return;\n    }\n    const attributeName = metaAttributeName || memberName;\n    const attrVal = hostElm.getAttribute(attributeName);\n    if (attrVal !== null) {\n      const attrPropVal = parsePropertyValue(attrVal, memberFlags);\n      (_a2 = hostRef == null ? void 0 : hostRef.$instanceValues$) == null ? void 0 : _a2.set(memberName, attrPropVal);\n    }\n  });\n  let scopeId2;\n  if (BUILD13.scoped) {\n    const cmpMeta = hostRef.$cmpMeta$;\n    if (cmpMeta && cmpMeta.$flags$ & 10 /* needsScopedEncapsulation */ && hostElm[\"s-sc\"]) {\n      scopeId2 = hostElm[\"s-sc\"];\n      hostElm.classList.add(scopeId2 + \"-h\");\n    } else if (hostElm[\"s-sc\"]) {\n      delete hostElm[\"s-sc\"];\n    }\n  }\n  if (win.document && (!plt.$orgLocNodes$ || !plt.$orgLocNodes$.size)) {\n    initializeDocumentHydrate(win.document.body, plt.$orgLocNodes$ = /* @__PURE__ */new Map());\n  }\n  hostElm[HYDRATE_ID] = hostId;\n  hostElm.removeAttribute(HYDRATE_ID);\n  hostRef.$vnode$ = clientHydrate(vnode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, hostElm, hostId, slottedNodes);\n  let crIndex = 0;\n  const crLength = childRenderNodes.length;\n  let childRenderNode;\n  for (crIndex; crIndex < crLength; crIndex++) {\n    childRenderNode = childRenderNodes[crIndex];\n    const orgLocationId = childRenderNode.$hostId$ + \".\" + childRenderNode.$nodeId$;\n    const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n    const node = childRenderNode.$elm$;\n    if (!shadowRoot) {\n      node[\"s-hn\"] = tagName.toUpperCase();\n      if (childRenderNode.$tag$ === \"slot\") {\n        node[\"s-cr\"] = hostElm[\"s-cr\"];\n      }\n    }\n    if (childRenderNode.$tag$ === \"slot\") {\n      childRenderNode.$name$ = childRenderNode.$elm$[\"s-sn\"] || childRenderNode.$elm$[\"name\"] || null;\n      if (childRenderNode.$children$) {\n        childRenderNode.$flags$ |= 2 /* isSlotFallback */;\n        if (!childRenderNode.$elm$.childNodes.length) {\n          childRenderNode.$children$.forEach(c => {\n            childRenderNode.$elm$.appendChild(c.$elm$);\n          });\n        }\n      } else {\n        childRenderNode.$flags$ |= 1 /* isSlotReference */;\n      }\n    }\n    if (orgLocationNode && orgLocationNode.isConnected) {\n      if (shadowRoot && orgLocationNode[\"s-en\"] === \"\") {\n        orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n      }\n      orgLocationNode.parentNode.removeChild(orgLocationNode);\n      if (!shadowRoot) {\n        node[\"s-oo\"] = parseInt(childRenderNode.$nodeId$);\n      }\n    }\n    plt.$orgLocNodes$.delete(orgLocationId);\n  }\n  const hosts = [];\n  const snLen = slottedNodes.length;\n  let snIndex = 0;\n  let slotGroup;\n  let snGroupIdx;\n  let snGroupLen;\n  let slottedItem;\n  for (snIndex; snIndex < snLen; snIndex++) {\n    slotGroup = slottedNodes[snIndex];\n    if (!slotGroup || !slotGroup.length) continue;\n    snGroupLen = slotGroup.length;\n    snGroupIdx = 0;\n    for (snGroupIdx; snGroupIdx < snGroupLen; snGroupIdx++) {\n      slottedItem = slotGroup[snGroupIdx];\n      if (!hosts[slottedItem.hostId]) {\n        hosts[slottedItem.hostId] = plt.$orgLocNodes$.get(slottedItem.hostId);\n      }\n      if (!hosts[slottedItem.hostId]) continue;\n      const hostEle = hosts[slottedItem.hostId];\n      if (!hostEle.shadowRoot || !shadowRoot) {\n        slottedItem.slot[\"s-cr\"] = hostEle[\"s-cr\"];\n        if (!slottedItem.slot[\"s-cr\"] && hostEle.shadowRoot) {\n          slottedItem.slot[\"s-cr\"] = hostEle;\n        } else {\n          slottedItem.slot[\"s-cr\"] = (hostEle.__childNodes || hostEle.childNodes)[0];\n        }\n        addSlotRelocateNode(slottedItem.node, slottedItem.slot, false, slottedItem.node[\"s-oo\"]);\n        if (BUILD13.experimentalSlotFixes) {\n          patchSlottedNode(slottedItem.node);\n        }\n      }\n      if (hostEle.shadowRoot && slottedItem.node.parentElement !== hostEle) {\n        hostEle.appendChild(slottedItem.node);\n      }\n    }\n  }\n  if (BUILD13.scoped && scopeId2 && slotNodes.length) {\n    slotNodes.forEach(slot => {\n      slot.$elm$.parentElement.classList.add(scopeId2 + \"-s\");\n    });\n  }\n  if (BUILD13.shadowDom && shadowRoot && !shadowRoot.childNodes.length) {\n    let rnIdex = 0;\n    const rnLen = shadowRootNodes.length;\n    if (rnLen) {\n      for (rnIdex; rnIdex < rnLen; rnIdex++) {\n        shadowRoot.appendChild(shadowRootNodes[rnIdex]);\n      }\n      Array.from(hostElm.childNodes).forEach(node => {\n        if (typeof node[\"s-sn\"] !== \"string\") {\n          if (node.nodeType === 1 /* ElementNode */ && node.slot && node.hidden) {\n            node.removeAttribute(\"hidden\");\n          } else if (node.nodeType === 8 /* CommentNode */ || node.nodeType === 3 /* TextNode */ && !node.wholeText.trim()) {\n            node.parentNode.removeChild(node);\n          }\n        }\n      });\n    }\n  }\n  plt.$orgLocNodes$.delete(hostElm[\"s-id\"]);\n  hostRef.$hostElement$ = hostElm;\n  endHydrate();\n};\nvar clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId, slottedNodes = []) => {\n  let childNodeType;\n  let childIdSplt;\n  let childVNode;\n  let i2;\n  const scopeId2 = hostElm[\"s-sc\"];\n  if (node.nodeType === 1 /* ElementNode */) {\n    childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n    if (childNodeType) {\n      childIdSplt = childNodeType.split(\".\");\n      if (childIdSplt[0] === hostId || childIdSplt[0] === \"0\") {\n        childVNode = createSimpleVNode({\n          $flags$: 0,\n          $hostId$: childIdSplt[0],\n          $nodeId$: childIdSplt[1],\n          $depth$: childIdSplt[2],\n          $index$: childIdSplt[3],\n          $tag$: node.tagName.toLowerCase(),\n          $elm$: node,\n          // If we don't add the initial classes to the VNode, the first `vdom-render.ts` patch\n          // won't try to reconcile them. Classes set on the node will be blown away.\n          $attrs$: {\n            class: node.className || \"\"\n          }\n        });\n        childRenderNodes.push(childVNode);\n        node.removeAttribute(HYDRATE_CHILD_ID);\n        if (!parentVNode.$children$) {\n          parentVNode.$children$ = [];\n        }\n        if (BUILD13.scoped && scopeId2) {\n          node[\"s-si\"] = scopeId2;\n          childVNode.$attrs$.class += \" \" + scopeId2;\n        }\n        const slotName = childVNode.$elm$.getAttribute(\"s-sn\");\n        if (typeof slotName === \"string\") {\n          if (childVNode.$tag$ === \"slot-fb\") {\n            addSlot(slotName, childIdSplt[2], childVNode, node, parentVNode, childRenderNodes, slotNodes, shadowRootNodes, slottedNodes);\n            if (BUILD13.scoped && scopeId2) {\n              node.classList.add(scopeId2);\n            }\n          }\n          childVNode.$elm$[\"s-sn\"] = slotName;\n          childVNode.$elm$.removeAttribute(\"s-sn\");\n        }\n        if (childVNode.$index$ !== void 0) {\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n        }\n        parentVNode = childVNode;\n        if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n          shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n        }\n      }\n    }\n    if (node.shadowRoot) {\n      for (i2 = node.shadowRoot.childNodes.length - 1; i2 >= 0; i2--) {\n        clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.shadowRoot.childNodes[i2], hostId, slottedNodes);\n      }\n    }\n    const nonShadowNodes = node.__childNodes || node.childNodes;\n    for (i2 = nonShadowNodes.length - 1; i2 >= 0; i2--) {\n      clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, nonShadowNodes[i2], hostId, slottedNodes);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[1] === hostId || childIdSplt[1] === \"0\") {\n      childNodeType = childIdSplt[0];\n      childVNode = createSimpleVNode({\n        $hostId$: childIdSplt[1],\n        $nodeId$: childIdSplt[2],\n        $depth$: childIdSplt[3],\n        $index$: childIdSplt[4] || \"0\",\n        $elm$: node,\n        $attrs$: null,\n        $children$: null,\n        $key$: null,\n        $name$: null,\n        $tag$: null,\n        $text$: null\n      });\n      if (childNodeType === TEXT_NODE_ID) {\n        childVNode.$elm$ = findCorrespondingNode(node, 3 /* TextNode */);\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* TextNode */) {\n          childVNode.$text$ = childVNode.$elm$.textContent;\n          childRenderNodes.push(childVNode);\n          node.remove();\n          if (hostId === childVNode.$hostId$) {\n            if (!parentVNode.$children$) {\n              parentVNode.$children$ = [];\n            }\n            parentVNode.$children$[childVNode.$index$] = childVNode;\n          }\n          if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n          }\n        }\n      } else if (childNodeType === COMMENT_NODE_ID) {\n        childVNode.$elm$ = findCorrespondingNode(node, 8 /* CommentNode */);\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 8 /* CommentNode */) {\n          childRenderNodes.push(childVNode);\n          node.remove();\n        }\n      } else if (childVNode.$hostId$ === hostId) {\n        if (childNodeType === SLOT_NODE_ID) {\n          const slotName = node[\"s-sn\"] = childIdSplt[5] || \"\";\n          addSlot(slotName, childIdSplt[2], childVNode, node, parentVNode, childRenderNodes, slotNodes, shadowRootNodes, slottedNodes);\n        } else if (childNodeType === CONTENT_REF_ID) {\n          if (BUILD13.shadowDom && shadowRootNodes) {\n            node.remove();\n          } else if (BUILD13.slotRelocation) {\n            hostElm[\"s-cr\"] = node;\n            node[\"s-cn\"] = true;\n          }\n        }\n      }\n    }\n  } else if (parentVNode && parentVNode.$tag$ === \"style\") {\n    const vnode = newVNode(null, node.textContent);\n    vnode.$elm$ = node;\n    vnode.$index$ = \"0\";\n    parentVNode.$children$ = [vnode];\n  } else {\n    if (node.nodeType === 3 /* TextNode */ && !node.wholeText.trim()) {\n      node.remove();\n    }\n  }\n  return parentVNode;\n};\nvar initializeDocumentHydrate = (node, orgLocNodes) => {\n  if (node.nodeType === 1 /* ElementNode */) {\n    const componentId = node[HYDRATE_ID] || node.getAttribute(HYDRATE_ID);\n    if (componentId) {\n      orgLocNodes.set(componentId, node);\n    }\n    let i2 = 0;\n    if (node.shadowRoot) {\n      for (; i2 < node.shadowRoot.childNodes.length; i2++) {\n        initializeDocumentHydrate(node.shadowRoot.childNodes[i2], orgLocNodes);\n      }\n    }\n    const nonShadowNodes = node.__childNodes || node.childNodes;\n    for (i2 = 0; i2 < nonShadowNodes.length; i2++) {\n      initializeDocumentHydrate(nonShadowNodes[i2], orgLocNodes);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    const childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[0] === ORG_LOCATION_ID) {\n      orgLocNodes.set(childIdSplt[1] + \".\" + childIdSplt[2], node);\n      node.nodeValue = \"\";\n      node[\"s-en\"] = childIdSplt[3];\n    }\n  }\n};\nvar createSimpleVNode = vnode => {\n  const defaultVNode = {\n    $flags$: 0,\n    $hostId$: null,\n    $nodeId$: null,\n    $depth$: null,\n    $index$: \"0\",\n    $elm$: null,\n    $attrs$: null,\n    $children$: null,\n    $key$: null,\n    $name$: null,\n    $tag$: null,\n    $text$: null\n  };\n  return {\n    ...defaultVNode,\n    ...vnode\n  };\n};\nfunction addSlot(slotName, slotId, childVNode, node, parentVNode, childRenderNodes, slotNodes, shadowRootNodes, slottedNodes) {\n  node[\"s-sr\"] = true;\n  childVNode.$name$ = slotName || null;\n  childVNode.$tag$ = \"slot\";\n  const parentNodeId = (parentVNode == null ? void 0 : parentVNode.$elm$) ? parentVNode.$elm$[\"s-id\"] || parentVNode.$elm$.getAttribute(\"s-id\") : \"\";\n  if (BUILD13.shadowDom && shadowRootNodes && win.document) {\n    const slot = childVNode.$elm$ = win.document.createElement(childVNode.$tag$);\n    if (childVNode.$name$) {\n      childVNode.$elm$.setAttribute(\"name\", slotName);\n    }\n    if (parentNodeId && parentNodeId !== childVNode.$hostId$) {\n      parentVNode.$elm$.insertBefore(slot, parentVNode.$elm$.children[0]);\n    } else {\n      node.parentNode.insertBefore(childVNode.$elm$, node);\n    }\n    addSlottedNodes(slottedNodes, slotId, slotName, node, childVNode.$hostId$);\n    node.remove();\n    if (childVNode.$depth$ === \"0\") {\n      shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n    }\n  } else {\n    const slot = childVNode.$elm$;\n    const shouldMove = parentNodeId && parentNodeId !== childVNode.$hostId$ && parentVNode.$elm$.shadowRoot;\n    addSlottedNodes(slottedNodes, slotId, slotName, node, shouldMove ? parentNodeId : childVNode.$hostId$);\n    patchSlotNode(node);\n    if (shouldMove) {\n      parentVNode.$elm$.insertBefore(slot, parentVNode.$elm$.children[0]);\n    }\n    childRenderNodes.push(childVNode);\n  }\n  slotNodes.push(childVNode);\n  if (!parentVNode.$children$) {\n    parentVNode.$children$ = [];\n  }\n  parentVNode.$children$[childVNode.$index$] = childVNode;\n}\nvar addSlottedNodes = (slottedNodes, slotNodeId, slotName, slotNode, hostId) => {\n  let slottedNode = slotNode.nextSibling;\n  slottedNodes[slotNodeId] = slottedNodes[slotNodeId] || [];\n  while (slottedNode && ((slottedNode[\"getAttribute\"] && slottedNode.getAttribute(\"slot\") || slottedNode[\"s-sn\"]) === slotName || slotName === \"\" && !slottedNode[\"s-sn\"] && (slottedNode.nodeType === 8 /* CommentNode */ && slottedNode.nodeValue.indexOf(\".\") !== 1 || slottedNode.nodeType === 3 /* TextNode */))) {\n    slottedNode[\"s-sn\"] = slotName;\n    slottedNodes[slotNodeId].push({\n      slot: slotNode,\n      node: slottedNode,\n      hostId\n    });\n    slottedNode = slottedNode.nextSibling;\n  }\n};\nvar findCorrespondingNode = (node, type) => {\n  let sibling = node;\n  do {\n    sibling = sibling.nextSibling;\n  } while (sibling && (sibling.nodeType !== type || !sibling.nodeValue));\n  return sibling;\n};\n\n// src/runtime/initialize-component.ts\nimport { BUILD as BUILD24 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/shadow-css.ts\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n *\n * This file is a port of shadowCSS from `webcomponents.js` to TypeScript.\n * https://github.com/webcomponents/webcomponentsjs/blob/4efecd7e0e/src/ShadowCSS/ShadowCSS.js\n * https://github.com/angular/angular/blob/master/packages/compiler/src/shadow_css.ts\n */\nvar safeSelector = selector => {\n  const placeholders = [];\n  let index = 0;\n  selector = selector.replace(/(\\[[^\\]]*\\])/g, (_, keep) => {\n    const replaceBy = `__ph-${index}__`;\n    placeholders.push(keep);\n    index++;\n    return replaceBy;\n  });\n  const content = selector.replace(/(:nth-[-\\w]+)(\\([^)]+\\))/g, (_, pseudo, exp) => {\n    const replaceBy = `__ph-${index}__`;\n    placeholders.push(exp);\n    index++;\n    return pseudo + replaceBy;\n  });\n  const ss = {\n    content,\n    placeholders\n  };\n  return ss;\n};\nvar restoreSafeSelector = (placeholders, content) => {\n  return content.replace(/__ph-(\\d+)__/g, (_, index) => placeholders[+index]);\n};\nvar _polyfillHost = \"-shadowcsshost\";\nvar _polyfillSlotted = \"-shadowcssslotted\";\nvar _polyfillHostContext = \"-shadowcsscontext\";\nvar _parenSuffix = \")(?:\\\\(((?:\\\\([^)(]*\\\\)|[^)(]*)+?)\\\\))?([^,{]*)\";\nvar _cssColonHostRe = new RegExp(\"(\" + _polyfillHost + _parenSuffix, \"gim\");\nvar _cssColonHostContextRe = new RegExp(\"(\" + _polyfillHostContext + _parenSuffix, \"gim\");\nvar _cssColonSlottedRe = new RegExp(\"(\" + _polyfillSlotted + _parenSuffix, \"gim\");\nvar _polyfillHostNoCombinator = _polyfillHost + \"-no-combinator\";\nvar _polyfillHostNoCombinatorRe = /-shadowcsshost-no-combinator([^\\s]*)/;\nvar _shadowDOMSelectorsRe = [/::shadow/g, /::content/g];\nvar _selectorReSuffix = \"([>\\\\s~+[.,{:][\\\\s\\\\S]*)?$\";\nvar _polyfillHostRe = /-shadowcsshost/gim;\nvar createSupportsRuleRe = selector => {\n  const safeSelector2 = escapeRegExpSpecialCharacters(selector);\n  return new RegExp(\n  // First capture group: match any context before the selector that's not inside @supports selector()\n  // Using negative lookahead to avoid matching inside @supports selector(...) condition\n  `(^|[^@]|@(?!supports\\\\s+selector\\\\s*\\\\([^{]*?${safeSelector2}))(${safeSelector2}\\\\b)`, \"g\");\n};\nvar _colonSlottedRe = createSupportsRuleRe(\"::slotted\");\nvar _colonHostRe = createSupportsRuleRe(\":host\");\nvar _colonHostContextRe = createSupportsRuleRe(\":host-context\");\nvar _commentRe = /\\/\\*\\s*[\\s\\S]*?\\*\\//g;\nvar stripComments = input => {\n  return input.replace(_commentRe, \"\");\n};\nvar _commentWithHashRe = /\\/\\*\\s*#\\s*source(Mapping)?URL=[\\s\\S]+?\\*\\//g;\nvar extractCommentsWithHash = input => {\n  return input.match(_commentWithHashRe) || [];\n};\nvar _ruleRe = /(\\s*)([^;\\{\\}]+?)(\\s*)((?:{%BLOCK%}?\\s*;?)|(?:\\s*;))/g;\nvar _curlyRe = /([{}])/g;\nvar _selectorPartsRe = /(^.*?[^\\\\])??((:+)(.*)|$)/;\nvar OPEN_CURLY = \"{\";\nvar CLOSE_CURLY = \"}\";\nvar BLOCK_PLACEHOLDER = \"%BLOCK%\";\nvar processRules = (input, ruleCallback) => {\n  const inputWithEscapedBlocks = escapeBlocks(input);\n  let nextBlockIndex = 0;\n  return inputWithEscapedBlocks.escapedString.replace(_ruleRe, (...m) => {\n    const selector = m[2];\n    let content = \"\";\n    let suffix = m[4];\n    let contentPrefix = \"\";\n    if (suffix && suffix.startsWith(\"{\" + BLOCK_PLACEHOLDER)) {\n      content = inputWithEscapedBlocks.blocks[nextBlockIndex++];\n      suffix = suffix.substring(BLOCK_PLACEHOLDER.length + 1);\n      contentPrefix = \"{\";\n    }\n    const cssRule = {\n      selector,\n      content\n    };\n    const rule = ruleCallback(cssRule);\n    return `${m[1]}${rule.selector}${m[3]}${contentPrefix}${rule.content}${suffix}`;\n  });\n};\nvar escapeBlocks = input => {\n  const inputParts = input.split(_curlyRe);\n  const resultParts = [];\n  const escapedBlocks = [];\n  let bracketCount = 0;\n  let currentBlockParts = [];\n  for (let partIndex = 0; partIndex < inputParts.length; partIndex++) {\n    const part = inputParts[partIndex];\n    if (part === CLOSE_CURLY) {\n      bracketCount--;\n    }\n    if (bracketCount > 0) {\n      currentBlockParts.push(part);\n    } else {\n      if (currentBlockParts.length > 0) {\n        escapedBlocks.push(currentBlockParts.join(\"\"));\n        resultParts.push(BLOCK_PLACEHOLDER);\n        currentBlockParts = [];\n      }\n      resultParts.push(part);\n    }\n    if (part === OPEN_CURLY) {\n      bracketCount++;\n    }\n  }\n  if (currentBlockParts.length > 0) {\n    escapedBlocks.push(currentBlockParts.join(\"\"));\n    resultParts.push(BLOCK_PLACEHOLDER);\n  }\n  const strEscapedBlocks = {\n    escapedString: resultParts.join(\"\"),\n    blocks: escapedBlocks\n  };\n  return strEscapedBlocks;\n};\nvar insertPolyfillHostInCssText = cssText => {\n  const supportsBlocks = [];\n  cssText = cssText.replace(/@supports\\s+selector\\s*\\(\\s*([^)]*)\\s*\\)/g, (_, selectorContent) => {\n    const placeholder = `__supports_${supportsBlocks.length}__`;\n    supportsBlocks.push(selectorContent);\n    return `@supports selector(${placeholder})`;\n  });\n  cssText = cssText.replace(_colonHostContextRe, `$1${_polyfillHostContext}`).replace(_colonHostRe, `$1${_polyfillHost}`).replace(_colonSlottedRe, `$1${_polyfillSlotted}`);\n  supportsBlocks.forEach((originalSelector, index) => {\n    cssText = cssText.replace(`__supports_${index}__`, originalSelector);\n  });\n  return cssText;\n};\nvar convertColonRule = (cssText, regExp, partReplacer) => {\n  return cssText.replace(regExp, (...m) => {\n    if (m[2]) {\n      const parts = m[2].split(\",\");\n      const r = [];\n      for (let i2 = 0; i2 < parts.length; i2++) {\n        const p = parts[i2].trim();\n        if (!p) break;\n        r.push(partReplacer(_polyfillHostNoCombinator, p, m[3]));\n      }\n      return r.join(\",\");\n    } else {\n      return _polyfillHostNoCombinator + m[3];\n    }\n  });\n};\nvar colonHostPartReplacer = (host, part, suffix) => {\n  return host + part.replace(_polyfillHost, \"\") + suffix;\n};\nvar convertColonHost = cssText => {\n  return convertColonRule(cssText, _cssColonHostRe, colonHostPartReplacer);\n};\nvar colonHostContextPartReplacer = (host, part, suffix) => {\n  if (part.indexOf(_polyfillHost) > -1) {\n    return colonHostPartReplacer(host, part, suffix);\n  } else {\n    return host + part + suffix + \", \" + part + \" \" + host + suffix;\n  }\n};\nvar convertColonSlotted = (cssText, slotScopeId) => {\n  const slotClass = \".\" + slotScopeId + \" > \";\n  const selectors = [];\n  cssText = cssText.replace(_cssColonSlottedRe, (...m) => {\n    if (m[2]) {\n      const compound = m[2].trim();\n      const suffix = m[3];\n      const slottedSelector = slotClass + compound + suffix;\n      let prefixSelector = \"\";\n      for (let i2 = m[4] - 1; i2 >= 0; i2--) {\n        const char = m[5][i2];\n        if (char === \"}\" || char === \",\") {\n          break;\n        }\n        prefixSelector = char + prefixSelector;\n      }\n      const orgSelector = (prefixSelector + slottedSelector).trim();\n      const addedSelector = `${prefixSelector.trimEnd()}${slottedSelector.trim()}`.trim();\n      if (orgSelector !== addedSelector) {\n        const updatedSelector = `${addedSelector}, ${orgSelector}`;\n        selectors.push({\n          orgSelector,\n          updatedSelector\n        });\n      }\n      return slottedSelector;\n    } else {\n      return _polyfillHostNoCombinator + m[3];\n    }\n  });\n  return {\n    selectors,\n    cssText\n  };\n};\nvar convertColonHostContext = cssText => {\n  return convertColonRule(cssText, _cssColonHostContextRe, colonHostContextPartReplacer);\n};\nvar convertShadowDOMSelectors = cssText => {\n  return _shadowDOMSelectorsRe.reduce((result, pattern) => result.replace(pattern, \" \"), cssText);\n};\nvar makeScopeMatcher = scopeSelector2 => {\n  const lre = /\\[/g;\n  const rre = /\\]/g;\n  scopeSelector2 = scopeSelector2.replace(lre, \"\\\\[\").replace(rre, \"\\\\]\");\n  return new RegExp(\"^(\" + scopeSelector2 + \")\" + _selectorReSuffix, \"m\");\n};\nvar selectorNeedsScoping = (selector, scopeSelector2) => {\n  const re = makeScopeMatcher(scopeSelector2);\n  return !re.test(selector);\n};\nvar injectScopingSelector = (selector, scopingSelector) => {\n  return selector.replace(_selectorPartsRe, (_, before = \"\", _colonGroup, colon = \"\", after = \"\") => {\n    return before + scopingSelector + colon + after;\n  });\n};\nvar applySimpleSelectorScope = (selector, scopeSelector2, hostSelector) => {\n  _polyfillHostRe.lastIndex = 0;\n  if (_polyfillHostRe.test(selector)) {\n    const replaceBy = `.${hostSelector}`;\n    return selector.replace(_polyfillHostNoCombinatorRe, (_, selector2) => injectScopingSelector(selector2, replaceBy)).replace(_polyfillHostRe, replaceBy + \" \");\n  }\n  return scopeSelector2 + \" \" + selector;\n};\nvar applyStrictSelectorScope = (selector, scopeSelector2, hostSelector) => {\n  const isRe = /\\[is=([^\\]]*)\\]/g;\n  scopeSelector2 = scopeSelector2.replace(isRe, (_, ...parts) => parts[0]);\n  const className = \".\" + scopeSelector2;\n  const _scopeSelectorPart = p => {\n    let scopedP = p.trim();\n    if (!scopedP) {\n      return \"\";\n    }\n    if (p.indexOf(_polyfillHostNoCombinator) > -1) {\n      scopedP = applySimpleSelectorScope(p, scopeSelector2, hostSelector);\n    } else {\n      const t = p.replace(_polyfillHostRe, \"\");\n      if (t.length > 0) {\n        scopedP = injectScopingSelector(t, className);\n      }\n    }\n    return scopedP;\n  };\n  const safeContent = safeSelector(selector);\n  selector = safeContent.content;\n  let scopedSelector = \"\";\n  let startIndex = 0;\n  let res;\n  const sep = /( |>|\\+|~(?!=))\\s*/g;\n  const hasHost = selector.indexOf(_polyfillHostNoCombinator) > -1;\n  let shouldScope = !hasHost;\n  while ((res = sep.exec(selector)) !== null) {\n    const separator = res[1];\n    const part2 = selector.slice(startIndex, res.index).trim();\n    shouldScope = shouldScope || part2.indexOf(_polyfillHostNoCombinator) > -1;\n    const scopedPart = shouldScope ? _scopeSelectorPart(part2) : part2;\n    scopedSelector += `${scopedPart} ${separator} `;\n    startIndex = sep.lastIndex;\n  }\n  const part = selector.substring(startIndex);\n  shouldScope = shouldScope || part.indexOf(_polyfillHostNoCombinator) > -1;\n  scopedSelector += shouldScope ? _scopeSelectorPart(part) : part;\n  return restoreSafeSelector(safeContent.placeholders, scopedSelector);\n};\nvar scopeSelector = (selector, scopeSelectorText, hostSelector, slotSelector) => {\n  return selector.split(\",\").map(shallowPart => {\n    if (slotSelector && shallowPart.indexOf(\".\" + slotSelector) > -1) {\n      return shallowPart.trim();\n    }\n    if (selectorNeedsScoping(shallowPart, scopeSelectorText)) {\n      return applyStrictSelectorScope(shallowPart, scopeSelectorText, hostSelector).trim();\n    } else {\n      return shallowPart.trim();\n    }\n  }).join(\", \");\n};\nvar scopeSelectors = (cssText, scopeSelectorText, hostSelector, slotSelector, commentOriginalSelector) => {\n  return processRules(cssText, rule => {\n    let selector = rule.selector;\n    let content = rule.content;\n    if (rule.selector[0] !== \"@\") {\n      selector = scopeSelector(rule.selector, scopeSelectorText, hostSelector, slotSelector);\n    } else if (rule.selector.startsWith(\"@media\") || rule.selector.startsWith(\"@supports\") || rule.selector.startsWith(\"@page\") || rule.selector.startsWith(\"@document\")) {\n      content = scopeSelectors(rule.content, scopeSelectorText, hostSelector, slotSelector, commentOriginalSelector);\n    }\n    const cssRule = {\n      selector: selector.replace(/\\s{2,}/g, \" \").trim(),\n      content\n    };\n    return cssRule;\n  });\n};\nvar scopeCssText = (cssText, scopeId2, hostScopeId, slotScopeId, commentOriginalSelector) => {\n  cssText = insertPolyfillHostInCssText(cssText);\n  cssText = convertColonHost(cssText);\n  cssText = convertColonHostContext(cssText);\n  const slotted = convertColonSlotted(cssText, slotScopeId);\n  cssText = slotted.cssText;\n  cssText = convertShadowDOMSelectors(cssText);\n  if (scopeId2) {\n    cssText = scopeSelectors(cssText, scopeId2, hostScopeId, slotScopeId, commentOriginalSelector);\n  }\n  cssText = replaceShadowCssHost(cssText, hostScopeId);\n  cssText = cssText.replace(/>\\s*\\*\\s+([^{, ]+)/gm, \" $1 \");\n  return {\n    cssText: cssText.trim(),\n    // We need to replace the shadow CSS host string in each of these selectors since we created\n    // them prior to the replacement happening in the components CSS text.\n    slottedSelectors: slotted.selectors.map(ref => ({\n      orgSelector: replaceShadowCssHost(ref.orgSelector, hostScopeId),\n      updatedSelector: replaceShadowCssHost(ref.updatedSelector, hostScopeId)\n    }))\n  };\n};\nvar replaceShadowCssHost = (cssText, hostScopeId) => {\n  return cssText.replace(/-shadowcsshost-no-combinator/g, `.${hostScopeId}`);\n};\nvar scopeCss = (cssText, scopeId2, commentOriginalSelector) => {\n  const hostScopeId = scopeId2 + \"-h\";\n  const slotScopeId = scopeId2 + \"-s\";\n  const commentsWithHash = extractCommentsWithHash(cssText);\n  cssText = stripComments(cssText);\n  const orgSelectors = [];\n  if (commentOriginalSelector) {\n    const processCommentedSelector = rule => {\n      const placeholder = `/*!@___${orgSelectors.length}___*/`;\n      const comment = `/*!@${rule.selector}*/`;\n      orgSelectors.push({\n        placeholder,\n        comment\n      });\n      rule.selector = placeholder + rule.selector;\n      return rule;\n    };\n    cssText = processRules(cssText, rule => {\n      if (rule.selector[0] !== \"@\") {\n        return processCommentedSelector(rule);\n      } else if (rule.selector.startsWith(\"@media\") || rule.selector.startsWith(\"@supports\") || rule.selector.startsWith(\"@page\") || rule.selector.startsWith(\"@document\")) {\n        rule.content = processRules(rule.content, processCommentedSelector);\n        return rule;\n      }\n      return rule;\n    });\n  }\n  const scoped = scopeCssText(cssText, scopeId2, hostScopeId, slotScopeId, commentOriginalSelector);\n  cssText = [scoped.cssText, ...commentsWithHash].join(\"\\n\");\n  if (commentOriginalSelector) {\n    orgSelectors.forEach(({\n      placeholder,\n      comment\n    }) => {\n      cssText = cssText.replace(placeholder, comment);\n    });\n  }\n  scoped.slottedSelectors.forEach(slottedSelector => {\n    const regex = new RegExp(escapeRegExpSpecialCharacters(slottedSelector.orgSelector), \"g\");\n    cssText = cssText.replace(regex, slottedSelector.updatedSelector);\n  });\n  return cssText;\n};\n\n// src/runtime/mode.ts\nvar computeMode = elm => modeResolutionChain.map(h2 => h2(elm)).find(m => !!m);\nvar setMode = handler => modeResolutionChain.push(handler);\nvar getMode = ref => getHostRef(ref).$modeName$;\n\n// src/runtime/proxy-component.ts\nimport { BUILD as BUILD23 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/set-value.ts\nimport { BUILD as BUILD22 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/parse-property-value.ts\nimport { BUILD as BUILD14 } from \"@stencil/core/internal/app-data\";\nvar parsePropertyValue = (propValue, propType) => {\n  if ((BUILD14.hydrateClientSide || BUILD14.hydrateServerSide) && typeof propValue === \"string\" && (propValue.startsWith(\"{\") && propValue.endsWith(\"}\") || propValue.startsWith(\"[\") && propValue.endsWith(\"]\"))) {\n    try {\n      propValue = JSON.parse(propValue);\n      return propValue;\n    } catch (e) {}\n  }\n  if ((BUILD14.hydrateClientSide || BUILD14.hydrateServerSide) && typeof propValue === \"string\" && propValue.startsWith(SERIALIZED_PREFIX)) {\n    propValue = deserializeProperty(propValue);\n    return propValue;\n  }\n  if (propValue != null && !isComplexType(propValue)) {\n    if (BUILD14.propBoolean && propType & 4 /* Boolean */) {\n      return propValue === \"false\" ? false : propValue === \"\" || !!propValue;\n    }\n    if (BUILD14.propNumber && propType & 2 /* Number */) {\n      return typeof propValue === \"string\" ? parseFloat(propValue) : typeof propValue === \"number\" ? propValue : NaN;\n    }\n    if (BUILD14.propString && propType & 1 /* String */) {\n      return String(propValue);\n    }\n    return propValue;\n  }\n  return propValue;\n};\n\n// src/runtime/update-component.ts\nimport { BUILD as BUILD21, NAMESPACE } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/event-emitter.ts\nimport { BUILD as BUILD16 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/element.ts\nimport { BUILD as BUILD15 } from \"@stencil/core/internal/app-data\";\nvar getElement = ref => BUILD15.lazyLoad ? getHostRef(ref).$hostElement$ : ref;\n\n// src/runtime/event-emitter.ts\nvar createEvent = (ref, name, flags) => {\n  const elm = getElement(ref);\n  return {\n    emit: detail => {\n      if (BUILD16.isDev && !elm.isConnected) {\n        consoleDevWarn(`The \"${name}\" event was emitted, but the dispatcher node is no longer connected to the dom.`);\n      }\n      return emitEvent(elm, name, {\n        bubbles: !!(flags & 4 /* Bubbles */),\n        composed: !!(flags & 2 /* Composed */),\n        cancelable: !!(flags & 1 /* Cancellable */),\n        detail\n      });\n    }\n  };\n};\nvar emitEvent = (elm, name, opts) => {\n  const ev = plt.ce(name, opts);\n  elm.dispatchEvent(ev);\n  return ev;\n};\n\n// src/runtime/styles.ts\nimport { BUILD as BUILD17 } from \"@stencil/core/internal/app-data\";\nvar rootAppliedStyles = /* @__PURE__ */new WeakMap();\nvar registerStyle = (scopeId2, cssText, allowCS) => {\n  let style = styles.get(scopeId2);\n  if (supportsConstructableStylesheets && allowCS) {\n    style = style || new CSSStyleSheet();\n    if (typeof style === \"string\") {\n      style = cssText;\n    } else {\n      style.replaceSync(cssText);\n    }\n  } else {\n    style = cssText;\n  }\n  styles.set(scopeId2, style);\n};\nvar addStyle = (styleContainerNode, cmpMeta, mode) => {\n  var _a;\n  const scopeId2 = getScopeId(cmpMeta, mode);\n  const style = styles.get(scopeId2);\n  if (!BUILD17.attachStyles || !win.document) {\n    return scopeId2;\n  }\n  styleContainerNode = styleContainerNode.nodeType === 11 /* DocumentFragment */ ? styleContainerNode : win.document;\n  if (style) {\n    if (typeof style === \"string\") {\n      styleContainerNode = styleContainerNode.head || styleContainerNode;\n      let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n      let styleElm;\n      if (!appliedStyles) {\n        rootAppliedStyles.set(styleContainerNode, appliedStyles = /* @__PURE__ */new Set());\n      }\n      if (!appliedStyles.has(scopeId2)) {\n        if (BUILD17.hydrateClientSide && styleContainerNode.host && (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId2}\"]`))) {\n          styleElm.innerHTML = style;\n        } else {\n          styleElm = document.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId2}\"]`) || win.document.createElement(\"style\");\n          styleElm.innerHTML = style;\n          const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(win.document);\n          if (nonce != null) {\n            styleElm.setAttribute(\"nonce\", nonce);\n          }\n          if ((BUILD17.hydrateServerSide || BUILD17.hotModuleReplacement) && (cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */ || cmpMeta.$flags$ & 128 /* shadowNeedsScopedCss */)) {\n            styleElm.setAttribute(HYDRATED_STYLE_ID, scopeId2);\n          }\n          if (!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */)) {\n            if (styleContainerNode.nodeName === \"HEAD\") {\n              const preconnectLinks = styleContainerNode.querySelectorAll(\"link[rel=preconnect]\");\n              const referenceNode2 = preconnectLinks.length > 0 ? preconnectLinks[preconnectLinks.length - 1].nextSibling : styleContainerNode.querySelector(\"style\");\n              styleContainerNode.insertBefore(styleElm, (referenceNode2 == null ? void 0 : referenceNode2.parentNode) === styleContainerNode ? referenceNode2 : null);\n            } else if (\"host\" in styleContainerNode) {\n              if (supportsConstructableStylesheets) {\n                const stylesheet = new CSSStyleSheet();\n                stylesheet.replaceSync(style);\n                styleContainerNode.adoptedStyleSheets = [stylesheet, ...styleContainerNode.adoptedStyleSheets];\n              } else {\n                const existingStyleContainer = styleContainerNode.querySelector(\"style\");\n                if (existingStyleContainer) {\n                  existingStyleContainer.innerHTML = style + existingStyleContainer.innerHTML;\n                } else {\n                  styleContainerNode.prepend(styleElm);\n                }\n              }\n            } else {\n              styleContainerNode.append(styleElm);\n            }\n          }\n          if (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            styleContainerNode.insertBefore(styleElm, null);\n          }\n        }\n        if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n          styleElm.innerHTML += SLOT_FB_CSS;\n        }\n        if (appliedStyles) {\n          appliedStyles.add(scopeId2);\n        }\n      }\n    } else if (BUILD17.constructableCSS && !styleContainerNode.adoptedStyleSheets.includes(style)) {\n      styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n    }\n  }\n  return scopeId2;\n};\nvar attachStyles = hostRef => {\n  const cmpMeta = hostRef.$cmpMeta$;\n  const elm = hostRef.$hostElement$;\n  const flags = cmpMeta.$flags$;\n  const endAttachStyles = createTime(\"attachStyles\", cmpMeta.$tagName$);\n  const scopeId2 = addStyle(BUILD17.shadowDom && supportsShadow && elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(), cmpMeta, hostRef.$modeName$);\n  if ((BUILD17.shadowDom || BUILD17.scoped) && BUILD17.cssAnnotations && flags & 10 /* needsScopedEncapsulation */) {\n    elm[\"s-sc\"] = scopeId2;\n    elm.classList.add(scopeId2 + \"-h\");\n  }\n  endAttachStyles();\n};\nvar getScopeId = (cmp, mode) => \"sc-\" + (BUILD17.mode && mode && cmp.$flags$ & 32 /* hasMode */ ? cmp.$tagName$ + \"-\" + mode : cmp.$tagName$);\nvar convertScopedToShadow = css => css.replace(/\\/\\*!@([^\\/]+)\\*\\/[^\\{]+\\{/g, \"$1{\");\nvar hydrateScopedToShadow = () => {\n  if (!win.document) {\n    return;\n  }\n  const styles2 = win.document.querySelectorAll(`[${HYDRATED_STYLE_ID}]`);\n  let i2 = 0;\n  for (; i2 < styles2.length; i2++) {\n    registerStyle(styles2[i2].getAttribute(HYDRATED_STYLE_ID), convertScopedToShadow(styles2[i2].innerHTML), true);\n  }\n};\n\n// src/runtime/vdom/vdom-render.ts\nimport { BUILD as BUILD20 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/update-element.ts\nimport { BUILD as BUILD19 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/set-accessor.ts\nimport { BUILD as BUILD18 } from \"@stencil/core/internal/app-data\";\nvar setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags, initialRender) => {\n  if (oldValue === newValue) {\n    return;\n  }\n  let isProp = isMemberInElement(elm, memberName);\n  let ln = memberName.toLowerCase();\n  if (BUILD18.vdomClass && memberName === \"class\") {\n    const classList = elm.classList;\n    const oldClasses = parseClassList(oldValue);\n    let newClasses = parseClassList(newValue);\n    if (BUILD18.hydrateClientSide && elm[\"s-si\"] && initialRender) {\n      newClasses.push(elm[\"s-si\"]);\n      oldClasses.forEach(c => {\n        if (c.startsWith(elm[\"s-si\"])) newClasses.push(c);\n      });\n      newClasses = [...new Set(newClasses)];\n      classList.add(...newClasses);\n    } else {\n      classList.remove(...oldClasses.filter(c => c && !newClasses.includes(c)));\n      classList.add(...newClasses.filter(c => c && !oldClasses.includes(c)));\n    }\n  } else if (BUILD18.vdomStyle && memberName === \"style\") {\n    if (BUILD18.updatable) {\n      for (const prop in oldValue) {\n        if (!newValue || newValue[prop] == null) {\n          if (!BUILD18.hydrateServerSide && prop.includes(\"-\")) {\n            elm.style.removeProperty(prop);\n          } else {\n            elm.style[prop] = \"\";\n          }\n        }\n      }\n    }\n    for (const prop in newValue) {\n      if (!oldValue || newValue[prop] !== oldValue[prop]) {\n        if (!BUILD18.hydrateServerSide && prop.includes(\"-\")) {\n          elm.style.setProperty(prop, newValue[prop]);\n        } else {\n          elm.style[prop] = newValue[prop];\n        }\n      }\n    }\n  } else if (BUILD18.vdomKey && memberName === \"key\") {} else if (BUILD18.vdomRef && memberName === \"ref\") {\n    if (newValue) {\n      newValue(elm);\n    }\n  } else if (BUILD18.vdomListener && (BUILD18.lazyLoad ? !isProp : !elm.__lookupSetter__(memberName)) && memberName[0] === \"o\" && memberName[1] === \"n\") {\n    if (memberName[2] === \"-\") {\n      memberName = memberName.slice(3);\n    } else if (isMemberInElement(win, ln)) {\n      memberName = ln.slice(2);\n    } else {\n      memberName = ln[2] + memberName.slice(3);\n    }\n    if (oldValue || newValue) {\n      const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n      memberName = memberName.replace(CAPTURE_EVENT_REGEX, \"\");\n      if (oldValue) {\n        plt.rel(elm, memberName, oldValue, capture);\n      }\n      if (newValue) {\n        plt.ael(elm, memberName, newValue, capture);\n      }\n    }\n  } else if (BUILD18.vdomPropOrAttr) {\n    const isComplex = isComplexType(newValue);\n    if ((isProp || isComplex && newValue !== null) && !isSvg) {\n      try {\n        if (!elm.tagName.includes(\"-\")) {\n          const n = newValue == null ? \"\" : newValue;\n          if (memberName === \"list\") {\n            isProp = false;\n          } else if (oldValue == null || elm[memberName] != n) {\n            if (typeof elm.__lookupSetter__(memberName) === \"function\") {\n              elm[memberName] = n;\n            } else {\n              elm.setAttribute(memberName, n);\n            }\n          }\n        } else if (elm[memberName] !== newValue) {\n          elm[memberName] = newValue;\n        }\n      } catch (e) {}\n    }\n    let xlink = false;\n    if (BUILD18.vdomXlink) {\n      if (ln !== (ln = ln.replace(/^xlink\\:?/, \"\"))) {\n        memberName = ln;\n        xlink = true;\n      }\n    }\n    if (newValue == null || newValue === false) {\n      if (newValue !== false || elm.getAttribute(memberName) === \"\") {\n        if (BUILD18.vdomXlink && xlink) {\n          elm.removeAttributeNS(XLINK_NS, memberName);\n        } else {\n          elm.removeAttribute(memberName);\n        }\n      }\n    } else if ((!isProp || flags & 4 /* isHost */ || isSvg) && !isComplex && elm.nodeType === 1 /* ElementNode */) {\n      newValue = newValue === true ? \"\" : newValue;\n      if (BUILD18.vdomXlink && xlink) {\n        elm.setAttributeNS(XLINK_NS, memberName, newValue);\n      } else {\n        elm.setAttribute(memberName, newValue);\n      }\n    }\n  }\n};\nvar parseClassListRegex = /\\s/;\nvar parseClassList = value => {\n  if (typeof value === \"object\" && value && \"baseVal\" in value) {\n    value = value.baseVal;\n  }\n  if (!value || typeof value !== \"string\") {\n    return [];\n  }\n  return value.split(parseClassListRegex);\n};\nvar CAPTURE_EVENT_SUFFIX = \"Capture\";\nvar CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + \"$\");\n\n// src/runtime/vdom/update-element.ts\nvar updateElement = (oldVnode, newVnode, isSvgMode2, isInitialRender) => {\n  const elm = newVnode.$elm$.nodeType === 11 /* DocumentFragment */ && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;\n  const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || {};\n  const newVnodeAttrs = newVnode.$attrs$ || {};\n  if (BUILD19.updatable) {\n    for (const memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {\n      if (!(memberName in newVnodeAttrs)) {\n        setAccessor(elm, memberName, oldVnodeAttrs[memberName], void 0, isSvgMode2, newVnode.$flags$, isInitialRender);\n      }\n    }\n  }\n  for (const memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {\n    setAccessor(elm, memberName, oldVnodeAttrs[memberName], newVnodeAttrs[memberName], isSvgMode2, newVnode.$flags$, isInitialRender);\n  }\n};\nfunction sortedAttrNames(attrNames) {\n  return attrNames.includes(\"ref\") ?\n  // we need to sort these to ensure that `'ref'` is the last attr\n  [...attrNames.filter(attr => attr !== \"ref\"), \"ref\"] :\n  // no need to sort, return the original array\n  attrNames;\n}\n\n// src/runtime/vdom/vdom-render.ts\nvar scopeId;\nvar contentRef;\nvar hostTagName;\nvar useNativeShadowDom = false;\nvar checkSlotFallbackVisibility = false;\nvar checkSlotRelocate = false;\nvar isSvgMode = false;\nvar createElm = (oldParentVNode, newParentVNode, childIndex) => {\n  var _a;\n  const newVNode2 = newParentVNode.$children$[childIndex];\n  let i2 = 0;\n  let elm;\n  let childNode;\n  let oldVNode;\n  if (BUILD20.slotRelocation && !useNativeShadowDom) {\n    checkSlotRelocate = true;\n    if (newVNode2.$tag$ === \"slot\") {\n      newVNode2.$flags$ |= newVNode2.$children$ ?\n      // slot element has fallback content\n      // still create an element that \"mocks\" the slot element\n      2 /* isSlotFallback */ :\n      // slot element does not have fallback content\n      // create an html comment we'll use to always reference\n      // where actual slot content should sit next to\n      1 /* isSlotReference */;\n    }\n  }\n  if (BUILD20.isDev && newVNode2.$elm$) {\n    consoleDevError(`The JSX ${newVNode2.$text$ !== null ? `\"${newVNode2.$text$}\" text` : `\"${newVNode2.$tag$}\" element`} node should not be shared within the same renderer. The renderer caches element lookups in order to improve performance. However, a side effect from this is that the exact same JSX node should not be reused. For more information please see https://stenciljs.com/docs/templating-jsx#avoid-shared-jsx-nodes`);\n  }\n  if (BUILD20.vdomText && newVNode2.$text$ !== null) {\n    elm = newVNode2.$elm$ = win.document.createTextNode(newVNode2.$text$);\n  } else if (BUILD20.slotRelocation && newVNode2.$flags$ & 1 /* isSlotReference */) {\n    elm = newVNode2.$elm$ = BUILD20.isDebug || BUILD20.hydrateServerSide ? slotReferenceDebugNode(newVNode2) : win.document.createTextNode(\"\");\n    if (BUILD20.vdomAttribute) {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n  } else {\n    if (BUILD20.svg && !isSvgMode) {\n      isSvgMode = newVNode2.$tag$ === \"svg\";\n    }\n    if (!win.document) {\n      throw new Error(\"You are trying to render a Stencil component in an environment that doesn't support the DOM. Make sure to populate the [`window`](https://developer.mozilla.org/en-US/docs/Web/API/Window/window) object before rendering a component.\");\n    }\n    elm = newVNode2.$elm$ = BUILD20.svg ? win.document.createElementNS(isSvgMode ? SVG_NS : HTML_NS, !useNativeShadowDom && BUILD20.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$) : win.document.createElement(!useNativeShadowDom && BUILD20.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$);\n    if (BUILD20.svg && isSvgMode && newVNode2.$tag$ === \"foreignObject\") {\n      isSvgMode = false;\n    }\n    if (BUILD20.vdomAttribute) {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n    if ((BUILD20.scoped || BUILD20.hydrateServerSide && 128 /* shadowNeedsScopedCss */) && isDef(scopeId) && elm[\"s-si\"] !== scopeId) {\n      elm.classList.add(elm[\"s-si\"] = scopeId);\n    }\n    if (newVNode2.$children$) {\n      for (i2 = 0; i2 < newVNode2.$children$.length; ++i2) {\n        childNode = createElm(oldParentVNode, newVNode2, i2);\n        if (childNode) {\n          elm.appendChild(childNode);\n        }\n      }\n    }\n    if (BUILD20.svg) {\n      if (newVNode2.$tag$ === \"svg\") {\n        isSvgMode = false;\n      } else if (elm.tagName === \"foreignObject\") {\n        isSvgMode = true;\n      }\n    }\n  }\n  elm[\"s-hn\"] = hostTagName;\n  if (BUILD20.slotRelocation) {\n    if (newVNode2.$flags$ & (2 /* isSlotFallback */ | 1 /* isSlotReference */)) {\n      elm[\"s-sr\"] = true;\n      elm[\"s-cr\"] = contentRef;\n      elm[\"s-sn\"] = newVNode2.$name$ || \"\";\n      elm[\"s-rf\"] = (_a = newVNode2.$attrs$) == null ? void 0 : _a.ref;\n      patchSlotNode(elm);\n      oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n      if (oldVNode && oldVNode.$tag$ === newVNode2.$tag$ && oldParentVNode.$elm$) {\n        if (BUILD20.experimentalSlotFixes) {\n          relocateToHostRoot(oldParentVNode.$elm$);\n        } else {\n          putBackInOriginalLocation(oldParentVNode.$elm$, false);\n        }\n      }\n      if (BUILD20.scoped || BUILD20.hydrateServerSide && 128 /* shadowNeedsScopedCss */) {\n        addRemoveSlotScopedClass(contentRef, elm, newParentVNode.$elm$, oldParentVNode == null ? void 0 : oldParentVNode.$elm$);\n      }\n    }\n  }\n  return elm;\n};\nvar relocateToHostRoot = parentElm => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const host = parentElm.closest(hostTagName.toLowerCase());\n  if (host != null) {\n    const contentRefNode = Array.from(host.__childNodes || host.childNodes).find(ref => ref[\"s-cr\"]);\n    const childNodeArray = Array.from(parentElm.__childNodes || parentElm.childNodes);\n    for (const childNode of contentRefNode ? childNodeArray.reverse() : childNodeArray) {\n      if (childNode[\"s-sh\"] != null) {\n        insertBefore(host, childNode, contentRefNode != null ? contentRefNode : null);\n        childNode[\"s-sh\"] = void 0;\n        checkSlotRelocate = true;\n      }\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar putBackInOriginalLocation = (parentElm, recursive) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const oldSlotChildNodes = Array.from(parentElm.__childNodes || parentElm.childNodes);\n  if (parentElm[\"s-sr\"] && BUILD20.experimentalSlotFixes) {\n    let node = parentElm;\n    while (node = node.nextSibling) {\n      if (node && node[\"s-sn\"] === parentElm[\"s-sn\"] && node[\"s-sh\"] === hostTagName) {\n        oldSlotChildNodes.push(node);\n      }\n    }\n  }\n  for (let i2 = oldSlotChildNodes.length - 1; i2 >= 0; i2--) {\n    const childNode = oldSlotChildNodes[i2];\n    if (childNode[\"s-hn\"] !== hostTagName && childNode[\"s-ol\"]) {\n      insertBefore(referenceNode(childNode).parentNode, childNode, referenceNode(childNode));\n      childNode[\"s-ol\"].remove();\n      childNode[\"s-ol\"] = void 0;\n      childNode[\"s-sh\"] = void 0;\n      checkSlotRelocate = true;\n    }\n    if (recursive) {\n      putBackInOriginalLocation(childNode, recursive);\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n  let containerElm = BUILD20.slotRelocation && parentElm[\"s-cr\"] && parentElm[\"s-cr\"].parentNode || parentElm;\n  let childNode;\n  if (BUILD20.shadowDom && containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n    containerElm = containerElm.shadowRoot;\n  }\n  for (; startIdx <= endIdx; ++startIdx) {\n    if (vnodes[startIdx]) {\n      childNode = createElm(null, parentVNode, startIdx);\n      if (childNode) {\n        vnodes[startIdx].$elm$ = childNode;\n        insertBefore(containerElm, childNode, BUILD20.slotRelocation ? referenceNode(before) : before);\n      }\n    }\n  }\n};\nvar removeVnodes = (vnodes, startIdx, endIdx) => {\n  for (let index = startIdx; index <= endIdx; ++index) {\n    const vnode = vnodes[index];\n    if (vnode) {\n      const elm = vnode.$elm$;\n      nullifyVNodeRefs(vnode);\n      if (elm) {\n        if (BUILD20.slotRelocation) {\n          checkSlotFallbackVisibility = true;\n          if (elm[\"s-ol\"]) {\n            elm[\"s-ol\"].remove();\n          } else {\n            putBackInOriginalLocation(elm, true);\n          }\n        }\n        elm.remove();\n      }\n    }\n  }\n};\nvar updateChildren = (parentElm, oldCh, newVNode2, newCh, isInitialRender = false) => {\n  let oldStartIdx = 0;\n  let newStartIdx = 0;\n  let idxInOld = 0;\n  let i2 = 0;\n  let oldEndIdx = oldCh.length - 1;\n  let oldStartVnode = oldCh[0];\n  let oldEndVnode = oldCh[oldEndIdx];\n  let newEndIdx = newCh.length - 1;\n  let newStartVnode = newCh[0];\n  let newEndVnode = newCh[newEndIdx];\n  let node;\n  let elmToMove;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n      patch(oldStartVnode, newStartVnode, isInitialRender);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n      patch(oldEndVnode, newEndVnode, isInitialRender);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n      if (BUILD20.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n      }\n      patch(oldStartVnode, newEndVnode, isInitialRender);\n      insertBefore(parentElm, oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n      if (BUILD20.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n      }\n      patch(oldEndVnode, newStartVnode, isInitialRender);\n      insertBefore(parentElm, oldEndVnode.$elm$, oldStartVnode.$elm$);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      idxInOld = -1;\n      if (BUILD20.vdomKey) {\n        for (i2 = oldStartIdx; i2 <= oldEndIdx; ++i2) {\n          if (oldCh[i2] && oldCh[i2].$key$ !== null && oldCh[i2].$key$ === newStartVnode.$key$) {\n            idxInOld = i2;\n            break;\n          }\n        }\n      }\n      if (BUILD20.vdomKey && idxInOld >= 0) {\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n          node = createElm(oldCh && oldCh[newStartIdx], newVNode2, idxInOld);\n        } else {\n          patch(elmToMove, newStartVnode, isInitialRender);\n          oldCh[idxInOld] = void 0;\n          node = elmToMove.$elm$;\n        }\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        node = createElm(oldCh && oldCh[newStartIdx], newVNode2, newStartIdx);\n        newStartVnode = newCh[++newStartIdx];\n      }\n      if (node) {\n        if (BUILD20.slotRelocation) {\n          insertBefore(referenceNode(oldStartVnode.$elm$).parentNode, node, referenceNode(oldStartVnode.$elm$));\n        } else {\n          insertBefore(oldStartVnode.$elm$.parentNode, node, oldStartVnode.$elm$);\n        }\n      }\n    }\n  }\n  if (oldStartIdx > oldEndIdx) {\n    addVnodes(parentElm, newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$, newVNode2, newCh, newStartIdx, newEndIdx);\n  } else if (BUILD20.updatable && newStartIdx > newEndIdx) {\n    removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n  }\n};\nvar isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n  if (leftVNode.$tag$ === rightVNode.$tag$) {\n    if (BUILD20.slotRelocation && leftVNode.$tag$ === \"slot\") {\n      return leftVNode.$name$ === rightVNode.$name$;\n    }\n    if (BUILD20.vdomKey && !isInitialRender) {\n      return leftVNode.$key$ === rightVNode.$key$;\n    }\n    if (isInitialRender && !leftVNode.$key$ && rightVNode.$key$) {\n      leftVNode.$key$ = rightVNode.$key$;\n    }\n    return true;\n  }\n  return false;\n};\nvar referenceNode = node => node && node[\"s-ol\"] || node;\nvar patch = (oldVNode, newVNode2, isInitialRender = false) => {\n  const elm = newVNode2.$elm$ = oldVNode.$elm$;\n  const oldChildren = oldVNode.$children$;\n  const newChildren = newVNode2.$children$;\n  const tag = newVNode2.$tag$;\n  const text = newVNode2.$text$;\n  let defaultHolder;\n  if (!BUILD20.vdomText || text === null) {\n    if (BUILD20.svg) {\n      isSvgMode = tag === \"svg\" ? true : tag === \"foreignObject\" ? false : isSvgMode;\n    }\n    if (BUILD20.vdomAttribute || BUILD20.reflect) {\n      if (BUILD20.slot && tag === \"slot\" && !useNativeShadowDom) {\n        if (BUILD20.experimentalSlotFixes && oldVNode.$name$ !== newVNode2.$name$) {\n          newVNode2.$elm$[\"s-sn\"] = newVNode2.$name$ || \"\";\n          relocateToHostRoot(newVNode2.$elm$.parentElement);\n        }\n      }\n      updateElement(oldVNode, newVNode2, isSvgMode, isInitialRender);\n    }\n    if (BUILD20.updatable && oldChildren !== null && newChildren !== null) {\n      updateChildren(elm, oldChildren, newVNode2, newChildren, isInitialRender);\n    } else if (newChildren !== null) {\n      if (BUILD20.updatable && BUILD20.vdomText && oldVNode.$text$ !== null) {\n        elm.textContent = \"\";\n      }\n      addVnodes(elm, null, newVNode2, newChildren, 0, newChildren.length - 1);\n    } else if (\n    // don't do this on initial render as it can cause non-hydrated content to be removed\n    !isInitialRender && BUILD20.updatable && oldChildren !== null) {\n      removeVnodes(oldChildren, 0, oldChildren.length - 1);\n    }\n    if (BUILD20.svg && isSvgMode && tag === \"svg\") {\n      isSvgMode = false;\n    }\n  } else if (BUILD20.vdomText && BUILD20.slotRelocation && (defaultHolder = elm[\"s-cr\"])) {\n    defaultHolder.parentNode.textContent = text;\n  } else if (BUILD20.vdomText && oldVNode.$text$ !== text) {\n    elm.data = text;\n  }\n};\nvar relocateNodes = [];\nvar markSlotContentForRelocation = elm => {\n  let node;\n  let hostContentNodes;\n  let j;\n  const children = elm.__childNodes || elm.childNodes;\n  for (const childNode of children) {\n    if (childNode[\"s-sr\"] && (node = childNode[\"s-cr\"]) && node.parentNode) {\n      hostContentNodes = node.parentNode.__childNodes || node.parentNode.childNodes;\n      const slotName = childNode[\"s-sn\"];\n      for (j = hostContentNodes.length - 1; j >= 0; j--) {\n        node = hostContentNodes[j];\n        if (!node[\"s-cn\"] && !node[\"s-nr\"] && node[\"s-hn\"] !== childNode[\"s-hn\"] && (!BUILD20.experimentalSlotFixes || !node[\"s-sh\"] || node[\"s-sh\"] !== childNode[\"s-hn\"])) {\n          if (isNodeLocatedInSlot(node, slotName)) {\n            let relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n            checkSlotFallbackVisibility = true;\n            node[\"s-sn\"] = node[\"s-sn\"] || slotName;\n            if (relocateNodeData) {\n              relocateNodeData.$nodeToRelocate$[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodeData.$slotRefNode$ = childNode;\n            } else {\n              node[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodes.push({\n                $slotRefNode$: childNode,\n                $nodeToRelocate$: node\n              });\n            }\n            if (node[\"s-sr\"]) {\n              relocateNodes.map(relocateNode => {\n                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node[\"s-sn\"])) {\n                  relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n                  if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                    relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                  }\n                }\n              });\n            }\n          } else if (!relocateNodes.some(r => r.$nodeToRelocate$ === node)) {\n            relocateNodes.push({\n              $nodeToRelocate$: node\n            });\n          }\n        }\n      }\n    }\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      markSlotContentForRelocation(childNode);\n    }\n  }\n};\nvar nullifyVNodeRefs = vNode => {\n  if (BUILD20.vdomRef) {\n    vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n    vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n  }\n};\nvar insertBefore = (parent, newNode, reference) => {\n  if (BUILD20.scoped && typeof newNode[\"s-sn\"] === \"string\" && !!newNode[\"s-sr\"] && !!newNode[\"s-cr\"]) {\n    addRemoveSlotScopedClass(newNode[\"s-cr\"], newNode, parent, newNode.parentElement);\n  } else if (BUILD20.experimentalSlotFixes && typeof newNode[\"s-sn\"] === \"string\") {\n    if (parent.getRootNode().nodeType !== 11 /* DOCUMENT_FRAGMENT_NODE */) {\n      patchParentNode(newNode);\n    }\n    parent.insertBefore(newNode, reference);\n    const {\n      slotNode\n    } = findSlotFromSlottedNode(newNode);\n    if (slotNode) dispatchSlotChangeEvent(slotNode);\n    return newNode;\n  }\n  if (BUILD20.experimentalSlotFixes && parent.__insertBefore) {\n    return parent.__insertBefore(newNode, reference);\n  } else {\n    return parent == null ? void 0 : parent.insertBefore(newNode, reference);\n  }\n};\nfunction addRemoveSlotScopedClass(reference, slotNode, newParent, oldParent) {\n  var _a, _b;\n  let scopeId2;\n  if (reference && typeof slotNode[\"s-sn\"] === \"string\" && !!slotNode[\"s-sr\"] && reference.parentNode && reference.parentNode[\"s-sc\"] && (scopeId2 = slotNode[\"s-si\"] || reference.parentNode[\"s-sc\"])) {\n    const scopeName = slotNode[\"s-sn\"];\n    const hostName = slotNode[\"s-hn\"];\n    (_a = newParent.classList) == null ? void 0 : _a.add(scopeId2 + \"-s\");\n    if (oldParent && ((_b = oldParent.classList) == null ? void 0 : _b.contains(scopeId2 + \"-s\"))) {\n      let child = (oldParent.__childNodes || oldParent.childNodes)[0];\n      let found = false;\n      while (child) {\n        if (child[\"s-sn\"] !== scopeName && child[\"s-hn\"] === hostName && !!child[\"s-sr\"]) {\n          found = true;\n          break;\n        }\n        child = child.nextSibling;\n      }\n      if (!found) oldParent.classList.remove(scopeId2 + \"-s\");\n    }\n  }\n}\nvar renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n  var _a, _b, _c, _d, _e;\n  const hostElm = hostRef.$hostElement$;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n  const isHostElement = isHost(renderFnResults);\n  const rootVnode = isHostElement ? renderFnResults : h(null, null, renderFnResults);\n  hostTagName = hostElm.tagName;\n  if (BUILD20.isDev && Array.isArray(renderFnResults) && renderFnResults.some(isHost)) {\n    throw new Error(`The <Host> must be the single root component.\nLooks like the render() function of \"${hostTagName.toLowerCase()}\" is returning an array that contains the <Host>.\n\nThe render() function should look like this instead:\n\nrender() {\n  // Do not return an array\n  return (\n    <Host>{content}</Host>\n  );\n}\n  `);\n  }\n  if (BUILD20.reflect && cmpMeta.$attrsToReflect$) {\n    rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n    cmpMeta.$attrsToReflect$.map(([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]);\n  }\n  if (isInitialLoad && rootVnode.$attrs$) {\n    for (const key of Object.keys(rootVnode.$attrs$)) {\n      if (hostElm.hasAttribute(key) && ![\"key\", \"ref\", \"style\", \"class\"].includes(key)) {\n        rootVnode.$attrs$[key] = hostElm[key];\n      }\n    }\n  }\n  rootVnode.$tag$ = null;\n  rootVnode.$flags$ |= 4 /* isHost */;\n  hostRef.$vnode$ = rootVnode;\n  rootVnode.$elm$ = oldVNode.$elm$ = BUILD20.shadowDom ? hostElm.shadowRoot || hostElm : hostElm;\n  if (BUILD20.scoped || BUILD20.shadowDom) {\n    scopeId = hostElm[\"s-sc\"];\n  }\n  useNativeShadowDom = supportsShadow && !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) && !(cmpMeta.$flags$ & 128 /* shadowNeedsScopedCss */);\n  if (BUILD20.slotRelocation) {\n    contentRef = hostElm[\"s-cr\"];\n    checkSlotFallbackVisibility = false;\n  }\n  patch(oldVNode, rootVnode, isInitialLoad);\n  if (BUILD20.slotRelocation) {\n    plt.$flags$ |= 1 /* isTmpDisconnected */;\n    if (checkSlotRelocate) {\n      markSlotContentForRelocation(rootVnode.$elm$);\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (!nodeToRelocate[\"s-ol\"] && win.document) {\n          const orgLocationNode = BUILD20.isDebug || BUILD20.hydrateServerSide ? originalLocationDebugNode(nodeToRelocate) : win.document.createTextNode(\"\");\n          orgLocationNode[\"s-nr\"] = nodeToRelocate;\n          insertBefore(nodeToRelocate.parentNode, nodeToRelocate[\"s-ol\"] = orgLocationNode, nodeToRelocate);\n        }\n      }\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        const slotRefNode = relocateData.$slotRefNode$;\n        if (slotRefNode) {\n          const parentNodeRef = slotRefNode.parentNode;\n          let insertBeforeNode = slotRefNode.nextSibling;\n          if (!BUILD20.hydrateServerSide && (!BUILD20.experimentalSlotFixes || insertBeforeNode && insertBeforeNode.nodeType === 1 /* ElementNode */)) {\n            let orgLocationNode = (_a = nodeToRelocate[\"s-ol\"]) == null ? void 0 : _a.previousSibling;\n            while (orgLocationNode) {\n              let refNode = (_b = orgLocationNode[\"s-nr\"]) != null ? _b : null;\n              if (refNode && refNode[\"s-sn\"] === nodeToRelocate[\"s-sn\"] && parentNodeRef === (refNode.__parentNode || refNode.parentNode)) {\n                refNode = refNode.nextSibling;\n                while (refNode === nodeToRelocate || (refNode == null ? void 0 : refNode[\"s-sr\"])) {\n                  refNode = refNode == null ? void 0 : refNode.nextSibling;\n                }\n                if (!refNode || !refNode[\"s-nr\"]) {\n                  insertBeforeNode = refNode;\n                  break;\n                }\n              }\n              orgLocationNode = orgLocationNode.previousSibling;\n            }\n          }\n          const parent = nodeToRelocate.__parentNode || nodeToRelocate.parentNode;\n          const nextSibling = nodeToRelocate.__nextSibling || nodeToRelocate.nextSibling;\n          if (!insertBeforeNode && parentNodeRef !== parent || nextSibling !== insertBeforeNode) {\n            if (nodeToRelocate !== insertBeforeNode) {\n              if (!BUILD20.experimentalSlotFixes && !nodeToRelocate[\"s-hn\"] && nodeToRelocate[\"s-ol\"]) {\n                nodeToRelocate[\"s-hn\"] = nodeToRelocate[\"s-ol\"].parentNode.nodeName;\n              }\n              insertBefore(parentNodeRef, nodeToRelocate, insertBeforeNode);\n              if (nodeToRelocate.nodeType === 1 /* ElementNode */ && nodeToRelocate.tagName !== \"SLOT-FB\") {\n                nodeToRelocate.hidden = (_c = nodeToRelocate[\"s-ih\"]) != null ? _c : false;\n              }\n            }\n          }\n          nodeToRelocate && typeof slotRefNode[\"s-rf\"] === \"function\" && slotRefNode[\"s-rf\"](slotRefNode);\n        } else {\n          if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n            if (isInitialLoad) {\n              nodeToRelocate[\"s-ih\"] = (_d = nodeToRelocate.hidden) != null ? _d : false;\n            }\n            nodeToRelocate.hidden = true;\n          }\n        }\n      }\n    }\n    if (checkSlotFallbackVisibility) {\n      updateFallbackSlotVisibility(rootVnode.$elm$);\n    }\n    plt.$flags$ &= ~1 /* isTmpDisconnected */;\n    relocateNodes.length = 0;\n  }\n  if (BUILD20.experimentalScopedSlotChanges && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n    const children = rootVnode.$elm$.__childNodes || rootVnode.$elm$.childNodes;\n    for (const childNode of children) {\n      if (childNode[\"s-hn\"] !== hostTagName && !childNode[\"s-sh\"]) {\n        if (isInitialLoad && childNode[\"s-ih\"] == null) {\n          childNode[\"s-ih\"] = (_e = childNode.hidden) != null ? _e : false;\n        }\n        childNode.hidden = true;\n      }\n    }\n  }\n  contentRef = void 0;\n};\nvar slotReferenceDebugNode = slotVNode => {\n  var _a;\n  return (_a = win.document) == null ? void 0 : _a.createComment(`<slot${slotVNode.$name$ ? ' name=\"' + slotVNode.$name$ + '\"' : \"\"}> (host=${hostTagName.toLowerCase()})`);\n};\nvar originalLocationDebugNode = nodeToRelocate => {\n  var _a;\n  return (_a = win.document) == null ? void 0 : _a.createComment(`org-location for ` + (nodeToRelocate.localName ? `<${nodeToRelocate.localName}> (host=${nodeToRelocate[\"s-hn\"]})` : `[${nodeToRelocate.textContent}]`));\n};\n\n// src/runtime/update-component.ts\nvar attachToAncestor = (hostRef, ancestorComponent) => {\n  if (BUILD21.asyncLoading && ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent[\"s-p\"]) {\n    const index = ancestorComponent[\"s-p\"].push(new Promise(r => hostRef.$onRenderResolve$ = () => {\n      ancestorComponent[\"s-p\"].splice(index - 1, 1);\n      r();\n    }));\n  }\n};\nvar scheduleUpdate = (hostRef, isInitialLoad) => {\n  if (BUILD21.taskQueue && BUILD21.updatable) {\n    hostRef.$flags$ |= 16 /* isQueuedForUpdate */;\n  }\n  if (BUILD21.asyncLoading && hostRef.$flags$ & 4 /* isWaitingForChildren */) {\n    hostRef.$flags$ |= 512 /* needsRerender */;\n    return;\n  }\n  attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n  const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n  return BUILD21.taskQueue ? writeTask(dispatch) : dispatch();\n};\nvar dispatchHooks = (hostRef, isInitialLoad) => {\n  const elm = hostRef.$hostElement$;\n  const endSchedule = createTime(\"scheduleUpdate\", hostRef.$cmpMeta$.$tagName$);\n  const instance = BUILD21.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  if (!instance) {\n    throw new Error(`Can't render component <${elm.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \\`externalRuntime: true\\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`);\n  }\n  let maybePromise;\n  if (isInitialLoad) {\n    if (BUILD21.lazyLoad && BUILD21.hostListener) {\n      hostRef.$flags$ |= 256 /* isListenReady */;\n      if (hostRef.$queuedListeners$) {\n        hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event, elm));\n        hostRef.$queuedListeners$ = void 0;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentWillLoad\");\n    maybePromise = safeCall(instance, \"componentWillLoad\", void 0, elm);\n  } else {\n    emitLifecycleEvent(elm, \"componentWillUpdate\");\n    maybePromise = safeCall(instance, \"componentWillUpdate\", void 0, elm);\n  }\n  emitLifecycleEvent(elm, \"componentWillRender\");\n  maybePromise = enqueue(maybePromise, () => safeCall(instance, \"componentWillRender\", void 0, elm));\n  endSchedule();\n  return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\nvar enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn).catch(err2 => {\n  console.error(err2);\n  fn();\n}) : fn();\nvar isPromisey = maybePromise => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === \"function\";\nvar updateComponent = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (hostRef, instance, isInitialLoad) {\n    var _a;\n    const elm = hostRef.$hostElement$;\n    const endUpdate = createTime(\"update\", hostRef.$cmpMeta$.$tagName$);\n    const rc = elm[\"s-rc\"];\n    if (BUILD21.style && isInitialLoad) {\n      attachStyles(hostRef);\n    }\n    const endRender = createTime(\"render\", hostRef.$cmpMeta$.$tagName$);\n    if (BUILD21.isDev) {\n      hostRef.$flags$ |= 1024 /* devOnRender */;\n    }\n    if (BUILD21.hydrateServerSide) {\n      yield callRender(hostRef, instance, elm, isInitialLoad);\n    } else {\n      callRender(hostRef, instance, elm, isInitialLoad);\n    }\n    if (BUILD21.isDev) {\n      hostRef.$renderCount$ = hostRef.$renderCount$ === void 0 ? 1 : hostRef.$renderCount$ + 1;\n      hostRef.$flags$ &= ~1024 /* devOnRender */;\n    }\n    if (BUILD21.hydrateServerSide) {\n      try {\n        serverSideConnected(elm);\n        if (isInitialLoad) {\n          if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n            elm[\"s-en\"] = \"\";\n          } else if (hostRef.$cmpMeta$.$flags$ & 2 /* scopedCssEncapsulation */) {\n            elm[\"s-en\"] = \"c\";\n          }\n        }\n      } catch (e) {\n        consoleError(e, elm);\n      }\n    }\n    if (BUILD21.asyncLoading && rc) {\n      rc.map(cb => cb());\n      elm[\"s-rc\"] = void 0;\n    }\n    endRender();\n    endUpdate();\n    if (BUILD21.asyncLoading) {\n      const childrenPromises = (_a = elm[\"s-p\"]) != null ? _a : [];\n      const postUpdate = () => postUpdateComponent(hostRef);\n      if (childrenPromises.length === 0) {\n        postUpdate();\n      } else {\n        Promise.all(childrenPromises).then(postUpdate);\n        hostRef.$flags$ |= 4 /* isWaitingForChildren */;\n        childrenPromises.length = 0;\n      }\n    } else {\n      postUpdateComponent(hostRef);\n    }\n  });\n  return function updateComponent(_x, _x2, _x3) {\n    return _ref.apply(this, arguments);\n  };\n}();\nvar renderingRef = null;\nvar callRender = (hostRef, instance, elm, isInitialLoad) => {\n  const allRenderFn = BUILD21.allRenderFn ? true : false;\n  const lazyLoad = BUILD21.lazyLoad ? true : false;\n  const taskQueue = BUILD21.taskQueue ? true : false;\n  const updatable = BUILD21.updatable ? true : false;\n  try {\n    renderingRef = instance;\n    instance = allRenderFn ? instance.render() : instance.render && instance.render();\n    if (updatable && taskQueue) {\n      hostRef.$flags$ &= ~16 /* isQueuedForUpdate */;\n    }\n    if (updatable || lazyLoad) {\n      hostRef.$flags$ |= 2 /* hasRendered */;\n    }\n    if (BUILD21.hasRenderFn || BUILD21.reflect) {\n      if (BUILD21.vdomRender || BUILD21.reflect) {\n        if (BUILD21.hydrateServerSide) {\n          return Promise.resolve(instance).then(value => renderVdom(hostRef, value, isInitialLoad));\n        } else {\n          renderVdom(hostRef, instance, isInitialLoad);\n        }\n      } else {\n        const shadowRoot = elm.shadowRoot;\n        if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n          shadowRoot.textContent = instance;\n        } else {\n          elm.textContent = instance;\n        }\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n  renderingRef = null;\n  return null;\n};\nvar getRenderingRef = () => renderingRef;\nvar postUpdateComponent = hostRef => {\n  const tagName = hostRef.$cmpMeta$.$tagName$;\n  const elm = hostRef.$hostElement$;\n  const endPostUpdate = createTime(\"postUpdate\", tagName);\n  const instance = BUILD21.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  if (BUILD21.isDev) {\n    hostRef.$flags$ |= 1024 /* devOnRender */;\n  }\n  safeCall(instance, \"componentDidRender\", void 0, elm);\n  if (BUILD21.isDev) {\n    hostRef.$flags$ &= ~1024 /* devOnRender */;\n  }\n  emitLifecycleEvent(elm, \"componentDidRender\");\n  if (!(hostRef.$flags$ & 64 /* hasLoadedComponent */)) {\n    hostRef.$flags$ |= 64 /* hasLoadedComponent */;\n    if (BUILD21.asyncLoading && BUILD21.cssAnnotations) {\n      addHydratedFlag(elm);\n    }\n    if (BUILD21.isDev) {\n      hostRef.$flags$ |= 2048 /* devOnDidLoad */;\n    }\n    safeCall(instance, \"componentDidLoad\", void 0, elm);\n    if (BUILD21.isDev) {\n      hostRef.$flags$ &= ~2048 /* devOnDidLoad */;\n    }\n    emitLifecycleEvent(elm, \"componentDidLoad\");\n    endPostUpdate();\n    if (BUILD21.asyncLoading) {\n      hostRef.$onReadyResolve$(elm);\n      if (!ancestorComponent) {\n        appDidLoad(tagName);\n      }\n    }\n  } else {\n    if (BUILD21.isDev) {\n      hostRef.$flags$ |= 1024 /* devOnRender */;\n    }\n    safeCall(instance, \"componentDidUpdate\", void 0, elm);\n    if (BUILD21.isDev) {\n      hostRef.$flags$ &= ~1024 /* devOnRender */;\n    }\n    emitLifecycleEvent(elm, \"componentDidUpdate\");\n    endPostUpdate();\n  }\n  if (BUILD21.method && BUILD21.lazyLoad) {\n    hostRef.$onInstanceResolve$(elm);\n  }\n  if (BUILD21.asyncLoading) {\n    if (hostRef.$onRenderResolve$) {\n      hostRef.$onRenderResolve$();\n      hostRef.$onRenderResolve$ = void 0;\n    }\n    if (hostRef.$flags$ & 512 /* needsRerender */) {\n      nextTick(() => scheduleUpdate(hostRef, false));\n    }\n    hostRef.$flags$ &= ~(4 /* isWaitingForChildren */ | 512 /* needsRerender */);\n  }\n};\nvar forceUpdate = ref => {\n  if (BUILD21.updatable && (Build.isBrowser || Build.isTesting)) {\n    const hostRef = getHostRef(ref);\n    const isConnected = hostRef.$hostElement$.isConnected;\n    if (isConnected && (hostRef.$flags$ & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n      scheduleUpdate(hostRef, false);\n    }\n    return isConnected;\n  }\n  return false;\n};\nvar appDidLoad = who => {\n  if (BUILD21.asyncQueue) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  nextTick(() => emitEvent(win, \"appload\", {\n    detail: {\n      namespace: NAMESPACE\n    }\n  }));\n  if (BUILD21.profile && performance.measure) {\n    performance.measure(`[Stencil] ${NAMESPACE} initial load (by ${who})`, \"st:app:start\");\n  }\n};\nvar safeCall = (instance, method, arg, elm) => {\n  if (instance && instance[method]) {\n    try {\n      return instance[method](arg);\n    } catch (e) {\n      consoleError(e, elm);\n    }\n  }\n  return void 0;\n};\nvar emitLifecycleEvent = (elm, lifecycleName) => {\n  if (BUILD21.lifecycleDOMEvents) {\n    emitEvent(elm, \"stencil_\" + lifecycleName, {\n      bubbles: true,\n      composed: true,\n      detail: {\n        namespace: NAMESPACE\n      }\n    });\n  }\n};\nvar addHydratedFlag = elm => {\n  var _a, _b;\n  return BUILD21.hydratedClass ? elm.classList.add((_a = BUILD21.hydratedSelectorName) != null ? _a : \"hydrated\") : BUILD21.hydratedAttribute ? elm.setAttribute((_b = BUILD21.hydratedSelectorName) != null ? _b : \"hydrated\", \"\") : void 0;\n};\nvar serverSideConnected = elm => {\n  const children = elm.children;\n  if (children != null) {\n    for (let i2 = 0, ii = children.length; i2 < ii; i2++) {\n      const childElm = children[i2];\n      if (typeof childElm.connectedCallback === \"function\") {\n        childElm.connectedCallback();\n      }\n      serverSideConnected(childElm);\n    }\n  }\n};\n\n// src/runtime/set-value.ts\nvar getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nvar setValue = (ref, propName, newVal, cmpMeta) => {\n  const hostRef = getHostRef(ref);\n  if (BUILD22.lazyLoad && !hostRef) {\n    throw new Error(`Couldn't find host element for \"${cmpMeta.$tagName$}\" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/stenciljs/core/issues/5457).`);\n  }\n  const elm = BUILD22.lazyLoad ? hostRef.$hostElement$ : ref;\n  const oldVal = hostRef.$instanceValues$.get(propName);\n  const flags = hostRef.$flags$;\n  const instance = BUILD22.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);\n  const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n  const didValueChange = newVal !== oldVal && !areBothNaN;\n  if ((!BUILD22.lazyLoad || !(flags & 8 /* isConstructingInstance */) || oldVal === void 0) && didValueChange) {\n    hostRef.$instanceValues$.set(propName, newVal);\n    if (BUILD22.isDev) {\n      if (hostRef.$flags$ & 1024 /* devOnRender */) {\n        consoleDevWarn(`The state/prop \"${propName}\" changed during rendering. This can potentially lead to infinite-loops and other bugs.`, \"\\nElement\", elm, \"\\nNew value\", newVal, \"\\nOld value\", oldVal);\n      } else if (hostRef.$flags$ & 2048 /* devOnDidLoad */) {\n        consoleDevWarn(`The state/prop \"${propName}\" changed during \"componentDidLoad()\", this triggers extra re-renders, try to setup on \"componentWillLoad()\"`, \"\\nElement\", elm, \"\\nNew value\", newVal, \"\\nOld value\", oldVal);\n      }\n    }\n    if (!BUILD22.lazyLoad || instance) {\n      if (BUILD22.watchCallback && cmpMeta.$watchers$ && flags & 128 /* isWatchReady */) {\n        const watchMethods = cmpMeta.$watchers$[propName];\n        if (watchMethods) {\n          watchMethods.map(watchMethodName => {\n            try {\n              instance[watchMethodName](newVal, oldVal, propName);\n            } catch (e) {\n              consoleError(e, elm);\n            }\n          });\n        }\n      }\n      if (BUILD22.updatable && (flags & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n        if (instance.componentShouldUpdate) {\n          if (instance.componentShouldUpdate(newVal, oldVal, propName) === false) {\n            return;\n          }\n        }\n        scheduleUpdate(hostRef, false);\n      }\n    }\n  }\n};\n\n// src/runtime/proxy-component.ts\nvar proxyComponent = (Cstr, cmpMeta, flags) => {\n  var _a, _b;\n  const prototype = Cstr.prototype;\n  if (BUILD23.isTesting) {\n    if (prototype.__stencilAugmented) {\n      return;\n    }\n    prototype.__stencilAugmented = true;\n  }\n  if (BUILD23.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */ && flags & 1 /* isElementConstructor */) {\n    FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS.forEach(cbName => {\n      const originalFormAssociatedCallback = prototype[cbName];\n      Object.defineProperty(prototype, cbName, {\n        value(...args) {\n          const hostRef = getHostRef(this);\n          const instance = BUILD23.lazyLoad ? hostRef.$lazyInstance$ : this;\n          if (!instance) {\n            hostRef.$onReadyPromise$.then(asyncInstance => {\n              const cb = asyncInstance[cbName];\n              typeof cb === \"function\" && cb.call(asyncInstance, ...args);\n            });\n          } else {\n            const cb = BUILD23.lazyLoad ? instance[cbName] : originalFormAssociatedCallback;\n            typeof cb === \"function\" && cb.call(instance, ...args);\n          }\n        }\n      });\n    });\n  }\n  if (BUILD23.member && cmpMeta.$members$ || BUILD23.watchCallback && (cmpMeta.$watchers$ || Cstr.watchers)) {\n    if (BUILD23.watchCallback && Cstr.watchers && !cmpMeta.$watchers$) {\n      cmpMeta.$watchers$ = Cstr.watchers;\n    }\n    const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});\n    members.map(([memberName, [memberFlags]]) => {\n      if ((BUILD23.prop || BUILD23.state) && (memberFlags & 31 /* Prop */ || (!BUILD23.lazyLoad || flags & 2 /* proxyState */) && memberFlags & 32 /* State */)) {\n        const {\n          get: origGetter,\n          set: origSetter\n        } = Object.getOwnPropertyDescriptor(prototype, memberName) || {};\n        if (origGetter) cmpMeta.$members$[memberName][0] |= 2048 /* Getter */;\n        if (origSetter) cmpMeta.$members$[memberName][0] |= 4096 /* Setter */;\n        if (flags & 1 /* isElementConstructor */ || !origGetter) {\n          Object.defineProperty(prototype, memberName, {\n            get() {\n              if (BUILD23.lazyLoad) {\n                if ((cmpMeta.$members$[memberName][0] & 2048 /* Getter */) === 0) {\n                  return getValue(this, memberName);\n                }\n                const ref = getHostRef(this);\n                const instance = ref ? ref.$lazyInstance$ : prototype;\n                if (!instance) return;\n                return instance[memberName];\n              }\n              if (!BUILD23.lazyLoad) {\n                return origGetter ? origGetter.apply(this) : getValue(this, memberName);\n              }\n            },\n            configurable: true,\n            enumerable: true\n          });\n        }\n        Object.defineProperty(prototype, memberName, {\n          set(newValue) {\n            const ref = getHostRef(this);\n            if (BUILD23.isDev) {\n              if (\n              // we are proxying the instance (not element)\n              (flags & 1 /* isElementConstructor */) === 0 &&\n              // if the class has a setter, then the Element can update instance values, so ignore\n              (cmpMeta.$members$[memberName][0] & 4096 /* Setter */) === 0 &&\n              // the element is not constructing\n              (ref && ref.$flags$ & 8 /* isConstructingInstance */) === 0 &&\n              // the member is a prop\n              (memberFlags & 31 /* Prop */) !== 0 &&\n              // the member is not mutable\n              (memberFlags & 1024 /* Mutable */) === 0) {\n                consoleDevWarn(`@Prop() \"${memberName}\" on <${cmpMeta.$tagName$}> is immutable but was modified from within the component.\nMore information: https://stenciljs.com/docs/properties#prop-mutability`);\n              }\n            }\n            if (origSetter) {\n              const currentValue = memberFlags & 32 /* State */ ? this[memberName] : ref.$hostElement$[memberName];\n              if (typeof currentValue === \"undefined\" && ref.$instanceValues$.get(memberName)) {\n                newValue = ref.$instanceValues$.get(memberName);\n              } else if (!ref.$instanceValues$.get(memberName) && currentValue) {\n                ref.$instanceValues$.set(memberName, currentValue);\n              }\n              origSetter.apply(this, [parsePropertyValue(newValue, memberFlags)]);\n              newValue = memberFlags & 32 /* State */ ? this[memberName] : ref.$hostElement$[memberName];\n              setValue(this, memberName, newValue, cmpMeta);\n              return;\n            }\n            if (!BUILD23.lazyLoad) {\n              setValue(this, memberName, newValue, cmpMeta);\n              return;\n            }\n            if (BUILD23.lazyLoad) {\n              if ((flags & 1 /* isElementConstructor */) === 0 || (cmpMeta.$members$[memberName][0] & 4096 /* Setter */) === 0) {\n                setValue(this, memberName, newValue, cmpMeta);\n                if (flags & 1 /* isElementConstructor */ && !ref.$lazyInstance$) {\n                  ref.$onReadyPromise$.then(() => {\n                    if (cmpMeta.$members$[memberName][0] & 4096 /* Setter */ && ref.$lazyInstance$[memberName] !== ref.$instanceValues$.get(memberName)) {\n                      ref.$lazyInstance$[memberName] = newValue;\n                    }\n                  });\n                }\n                return;\n              }\n              const setterSetVal = () => {\n                const currentValue = ref.$lazyInstance$[memberName];\n                if (!ref.$instanceValues$.get(memberName) && currentValue) {\n                  ref.$instanceValues$.set(memberName, currentValue);\n                }\n                ref.$lazyInstance$[memberName] = parsePropertyValue(newValue, memberFlags);\n                setValue(this, memberName, ref.$lazyInstance$[memberName], cmpMeta);\n              };\n              if (ref.$lazyInstance$) {\n                setterSetVal();\n              } else {\n                ref.$onReadyPromise$.then(() => setterSetVal());\n              }\n            }\n          }\n        });\n      } else if (BUILD23.lazyLoad && BUILD23.method && flags & 1 /* isElementConstructor */ && memberFlags & 64 /* Method */) {\n        Object.defineProperty(prototype, memberName, {\n          value(...args) {\n            var _a2;\n            const ref = getHostRef(this);\n            return (_a2 = ref == null ? void 0 : ref.$onInstancePromise$) == null ? void 0 : _a2.then(() => {\n              var _a3;\n              return (_a3 = ref.$lazyInstance$) == null ? void 0 : _a3[memberName](...args);\n            });\n          }\n        });\n      }\n    });\n    if (BUILD23.observeAttribute && (!BUILD23.lazyLoad || flags & 1 /* isElementConstructor */)) {\n      const attrNameToPropName = /* @__PURE__ */new Map();\n      prototype.attributeChangedCallback = function (attrName, oldValue, newValue) {\n        plt.jmp(() => {\n          var _a2;\n          const propName = attrNameToPropName.get(attrName);\n          if (this.hasOwnProperty(propName) && BUILD23.lazyLoad) {\n            newValue = this[propName];\n            delete this[propName];\n          } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === \"number\" &&\n          // cast type to number to avoid TS compiler issues\n          this[propName] == newValue) {\n            return;\n          } else if (propName == null) {\n            const hostRef = getHostRef(this);\n            const flags2 = hostRef == null ? void 0 : hostRef.$flags$;\n            if (flags2 && !(flags2 & 8 /* isConstructingInstance */) && flags2 & 128 /* isWatchReady */ && newValue !== oldValue) {\n              const elm = BUILD23.lazyLoad ? hostRef.$hostElement$ : this;\n              const instance = BUILD23.lazyLoad ? hostRef.$lazyInstance$ : elm;\n              const entry = (_a2 = cmpMeta.$watchers$) == null ? void 0 : _a2[attrName];\n              entry == null ? void 0 : entry.forEach(callbackName => {\n                if (instance[callbackName] != null) {\n                  instance[callbackName].call(instance, newValue, oldValue, attrName);\n                }\n              });\n            }\n            return;\n          }\n          const propDesc = Object.getOwnPropertyDescriptor(prototype, propName);\n          newValue = newValue === null && typeof this[propName] === \"boolean\" ? false : newValue;\n          if (newValue !== this[propName] && (!propDesc.get || !!propDesc.set)) {\n            this[propName] = newValue;\n          }\n        });\n      };\n      Cstr.observedAttributes = Array.from(/* @__PURE__ */new Set([...Object.keys((_b = cmpMeta.$watchers$) != null ? _b : {}), ...members.filter(([_, m]) => m[0] & 15 /* HasAttribute */).map(([propName, m]) => {\n        var _a2;\n        const attrName = m[1] || propName;\n        attrNameToPropName.set(attrName, propName);\n        if (BUILD23.reflect && m[0] & 512 /* ReflectAttr */) {\n          (_a2 = cmpMeta.$attrsToReflect$) == null ? void 0 : _a2.push([propName, attrName]);\n        }\n        return attrName;\n      })]));\n    }\n  }\n  return Cstr;\n};\n\n// src/runtime/initialize-component.ts\nvar initializeComponent = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (elm, hostRef, cmpMeta, hmrVersionId) {\n    let Cstr;\n    if ((hostRef.$flags$ & 32 /* hasInitializedComponent */) === 0) {\n      hostRef.$flags$ |= 32 /* hasInitializedComponent */;\n      const bundleId = cmpMeta.$lazyBundleId$;\n      if (BUILD24.lazyLoad && bundleId) {\n        const CstrImport = loadModule(cmpMeta, hostRef, hmrVersionId);\n        if (CstrImport && \"then\" in CstrImport) {\n          const endLoad = uniqueTime(`st:load:${cmpMeta.$tagName$}:${hostRef.$modeName$}`, `[Stencil] Load module for <${cmpMeta.$tagName$}>`);\n          Cstr = yield CstrImport;\n          endLoad();\n        } else {\n          Cstr = CstrImport;\n        }\n        if (!Cstr) {\n          throw new Error(`Constructor for \"${cmpMeta.$tagName$}#${hostRef.$modeName$}\" was not found`);\n        }\n        if (BUILD24.member && !Cstr.isProxied) {\n          if (BUILD24.watchCallback) {\n            cmpMeta.$watchers$ = Cstr.watchers;\n          }\n          proxyComponent(Cstr, cmpMeta, 2 /* proxyState */);\n          Cstr.isProxied = true;\n        }\n        const endNewInstance = createTime(\"createInstance\", cmpMeta.$tagName$);\n        if (BUILD24.member) {\n          hostRef.$flags$ |= 8 /* isConstructingInstance */;\n        }\n        try {\n          new Cstr(hostRef);\n        } catch (e) {\n          consoleError(e, elm);\n        }\n        if (BUILD24.member) {\n          hostRef.$flags$ &= ~8 /* isConstructingInstance */;\n        }\n        if (BUILD24.watchCallback) {\n          hostRef.$flags$ |= 128 /* isWatchReady */;\n        }\n        endNewInstance();\n        fireConnectedCallback(hostRef.$lazyInstance$, elm);\n      } else {\n        Cstr = elm.constructor;\n        const cmpTag = elm.localName;\n        customElements.whenDefined(cmpTag).then(() => hostRef.$flags$ |= 128 /* isWatchReady */);\n      }\n      if (BUILD24.style && Cstr && Cstr.style) {\n        let style;\n        if (typeof Cstr.style === \"string\") {\n          style = Cstr.style;\n        } else if (BUILD24.mode && typeof Cstr.style !== \"string\") {\n          hostRef.$modeName$ = computeMode(elm);\n          if (hostRef.$modeName$) {\n            style = Cstr.style[hostRef.$modeName$];\n          }\n          if (BUILD24.hydrateServerSide && hostRef.$modeName$) {\n            elm.setAttribute(\"s-mode\", hostRef.$modeName$);\n          }\n        }\n        const scopeId2 = getScopeId(cmpMeta, hostRef.$modeName$);\n        if (!styles.has(scopeId2)) {\n          const endRegisterStyles = createTime(\"registerStyles\", cmpMeta.$tagName$);\n          if (BUILD24.hydrateServerSide && BUILD24.shadowDom && cmpMeta.$flags$ & 128 /* shadowNeedsScopedCss */) {\n            style = scopeCss(style, scopeId2, true);\n          }\n          registerStyle(scopeId2, style, !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */));\n          endRegisterStyles();\n        }\n      }\n    }\n    const ancestorComponent = hostRef.$ancestorComponent$;\n    const schedule = () => scheduleUpdate(hostRef, true);\n    if (BUILD24.asyncLoading && ancestorComponent && ancestorComponent[\"s-rc\"]) {\n      ancestorComponent[\"s-rc\"].push(schedule);\n    } else {\n      schedule();\n    }\n  });\n  return function initializeComponent(_x4, _x5, _x6, _x7) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nvar fireConnectedCallback = (instance, elm) => {\n  if (BUILD24.lazyLoad) {\n    safeCall(instance, \"connectedCallback\", void 0, elm);\n  }\n};\n\n// src/runtime/connected-callback.ts\nvar connectedCallback = elm => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    const cmpMeta = hostRef.$cmpMeta$;\n    const endConnected = createTime(\"connectedCallback\", cmpMeta.$tagName$);\n    if (BUILD25.hostListenerTargetParent) {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, true);\n    }\n    if (!(hostRef.$flags$ & 1 /* hasConnected */)) {\n      hostRef.$flags$ |= 1 /* hasConnected */;\n      let hostId;\n      if (BUILD25.hydrateClientSide) {\n        hostId = elm.getAttribute(HYDRATE_ID);\n        if (hostId) {\n          if (BUILD25.shadowDom && supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            const scopeId2 = BUILD25.mode ? addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute(\"s-mode\")) : addStyle(elm.shadowRoot, cmpMeta);\n            elm.classList.remove(scopeId2 + \"-h\", scopeId2 + \"-s\");\n          } else if (BUILD25.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n            const scopeId2 = getScopeId(cmpMeta, BUILD25.mode ? elm.getAttribute(\"s-mode\") : void 0);\n            elm[\"s-sc\"] = scopeId2;\n          }\n          initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n        }\n      }\n      if (BUILD25.slotRelocation && !hostId) {\n        if (BUILD25.hydrateServerSide || (BUILD25.slot || BUILD25.shadowDom) &&\n        // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ & (4 /* hasSlotRelocation */ | 8 /* needsShadowDomShim */)) {\n          setContentReference(elm);\n        }\n      }\n      if (BUILD25.asyncLoading) {\n        let ancestorComponent = elm;\n        while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {\n          if (BUILD25.hydrateClientSide && ancestorComponent.nodeType === 1 /* ElementNode */ && ancestorComponent.hasAttribute(\"s-id\") && ancestorComponent[\"s-p\"] || ancestorComponent[\"s-p\"]) {\n            attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);\n            break;\n          }\n        }\n      }\n      if (BUILD25.prop && !BUILD25.hydrateServerSide && cmpMeta.$members$) {\n        Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n          if (memberFlags & 31 /* Prop */ && elm.hasOwnProperty(memberName)) {\n            const value = elm[memberName];\n            delete elm[memberName];\n            elm[memberName] = value;\n          }\n        });\n      }\n      if (BUILD25.initializeNextTick) {\n        nextTick(() => initializeComponent(elm, hostRef, cmpMeta));\n      } else {\n        initializeComponent(elm, hostRef, cmpMeta);\n      }\n    } else {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, false);\n      if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        fireConnectedCallback(hostRef.$lazyInstance$, elm);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$, elm));\n      }\n    }\n    endConnected();\n  }\n};\nvar setContentReference = elm => {\n  if (!win.document) {\n    return;\n  }\n  const contentRefElm = elm[\"s-cr\"] = win.document.createComment(BUILD25.isDebug ? `content-ref (host=${elm.localName})` : \"\");\n  contentRefElm[\"s-cn\"] = true;\n  insertBefore(elm, contentRefElm, elm.firstChild);\n};\n\n// src/runtime/disconnected-callback.ts\nimport { BUILD as BUILD26 } from \"@stencil/core/internal/app-data\";\nvar disconnectInstance = (instance, elm) => {\n  if (BUILD26.lazyLoad) {\n    safeCall(instance, \"disconnectedCallback\", void 0, elm || instance);\n  }\n};\nvar disconnectedCallback = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(function* (elm) {\n    if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n      const hostRef = getHostRef(elm);\n      if (BUILD26.hostListener) {\n        if (hostRef.$rmListeners$) {\n          hostRef.$rmListeners$.map(rmListener => rmListener());\n          hostRef.$rmListeners$ = void 0;\n        }\n      }\n      if (!BUILD26.lazyLoad) {\n        disconnectInstance(elm);\n      } else if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        disconnectInstance(hostRef.$lazyInstance$, elm);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$, elm));\n      }\n    }\n    if (rootAppliedStyles.has(elm)) {\n      rootAppliedStyles.delete(elm);\n    }\n    if (elm.shadowRoot && rootAppliedStyles.has(elm.shadowRoot)) {\n      rootAppliedStyles.delete(elm.shadowRoot);\n    }\n  });\n  return function disconnectedCallback(_x8) {\n    return _ref3.apply(this, arguments);\n  };\n}();\n\n// src/runtime/bootstrap-custom-element.ts\nvar defineCustomElement = (Cstr, compactMeta) => {\n  customElements.define(compactMeta[1], proxyCustomElement(Cstr, compactMeta));\n};\nvar proxyCustomElement = (Cstr, compactMeta) => {\n  const cmpMeta = {\n    $flags$: compactMeta[0],\n    $tagName$: compactMeta[1]\n  };\n  if (BUILD27.member) {\n    cmpMeta.$members$ = compactMeta[2];\n  }\n  if (BUILD27.hostListener) {\n    cmpMeta.$listeners$ = compactMeta[3];\n  }\n  if (BUILD27.watchCallback) {\n    cmpMeta.$watchers$ = Cstr.$watchers$;\n  }\n  if (BUILD27.reflect) {\n    cmpMeta.$attrsToReflect$ = [];\n  }\n  if (BUILD27.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n    cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n  }\n  if (BUILD27.experimentalSlotFixes) {\n    if (BUILD27.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchPseudoShadowDom(Cstr.prototype);\n    }\n  } else {\n    if (BUILD27.slotChildNodesFix) {\n      patchChildSlotNodes(Cstr.prototype);\n    }\n    if (BUILD27.cloneNodeFix) {\n      patchCloneNode(Cstr.prototype);\n    }\n    if (BUILD27.appendChildSlotFix) {\n      patchSlotAppendChild(Cstr.prototype);\n    }\n    if (BUILD27.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchTextContent(Cstr.prototype);\n    }\n  }\n  if (BUILD27.hydrateClientSide && BUILD27.shadowDom) {\n    hydrateScopedToShadow();\n  }\n  const originalConnectedCallback = Cstr.prototype.connectedCallback;\n  const originalDisconnectedCallback = Cstr.prototype.disconnectedCallback;\n  Object.assign(Cstr.prototype, {\n    __hasHostListenerAttached: false,\n    __registerHost() {\n      registerHost(this, cmpMeta);\n    },\n    connectedCallback() {\n      if (!this.__hasHostListenerAttached) {\n        const hostRef = getHostRef(this);\n        addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n        this.__hasHostListenerAttached = true;\n      }\n      connectedCallback(this);\n      if (originalConnectedCallback) {\n        originalConnectedCallback.call(this);\n      }\n    },\n    disconnectedCallback() {\n      disconnectedCallback(this);\n      if (originalDisconnectedCallback) {\n        originalDisconnectedCallback.call(this);\n      }\n    },\n    __attachShadow() {\n      if (supportsShadow) {\n        if (!this.shadowRoot) {\n          createShadowRoot.call(this, cmpMeta);\n        } else {\n          if (this.shadowRoot.mode !== \"open\") {\n            throw new Error(`Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${this.shadowRoot.mode} but Stencil only supports open shadow roots.`);\n          }\n        }\n      } else {\n        this.shadowRoot = this;\n      }\n    }\n  });\n  Cstr.is = cmpMeta.$tagName$;\n  return proxyComponent(Cstr, cmpMeta, 1 /* isElementConstructor */ | 2 /* proxyState */);\n};\nvar forceModeUpdate = elm => {\n  if (BUILD27.style && BUILD27.mode && !BUILD27.lazyLoad) {\n    const mode = computeMode(elm);\n    const hostRef = getHostRef(elm);\n    if (hostRef.$modeName$ !== mode) {\n      const cmpMeta = hostRef.$cmpMeta$;\n      const oldScopeId = elm[\"s-sc\"];\n      const scopeId2 = getScopeId(cmpMeta, mode);\n      const style = elm.constructor.style[mode];\n      const flags = cmpMeta.$flags$;\n      if (style) {\n        if (!styles.has(scopeId2)) {\n          registerStyle(scopeId2, style, !!(flags & 1 /* shadowDomEncapsulation */));\n        }\n        hostRef.$modeName$ = mode;\n        elm.classList.remove(oldScopeId + \"-h\", oldScopeId + \"-s\");\n        attachStyles(hostRef);\n        forceUpdate(elm);\n      }\n    }\n  }\n};\n\n// src/runtime/bootstrap-lazy.ts\nimport { BUILD as BUILD28 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/hmr-component.ts\nvar hmrStart = (hostElement, cmpMeta, hmrVersionId) => {\n  const hostRef = getHostRef(hostElement);\n  hostRef.$flags$ = 1 /* hasConnected */;\n  initializeComponent(hostElement, hostRef, cmpMeta, hmrVersionId);\n};\n\n// src/runtime/bootstrap-lazy.ts\nvar bootstrapLazy = (lazyBundles, options = {}) => {\n  var _a;\n  if (BUILD28.profile && performance.mark) {\n    performance.mark(\"st:app:start\");\n  }\n  installDevTools();\n  if (!win.document) {\n    console.warn(\"Stencil: No document found. Skipping bootstrapping lazy components.\");\n    return;\n  }\n  const endBootstrap = createTime(\"bootstrapLazy\");\n  const cmpTags = [];\n  const exclude = options.exclude || [];\n  const customElements2 = win.customElements;\n  const head = win.document.head;\n  const metaCharset = /* @__PURE__ */head.querySelector(\"meta[charset]\");\n  const dataStyles = /* @__PURE__ */win.document.createElement(\"style\");\n  const deferredConnectedCallbacks = [];\n  let appLoadFallback;\n  let isBootstrapping = true;\n  Object.assign(plt, options);\n  plt.$resourcesUrl$ = new URL(options.resourcesUrl || \"./\", win.document.baseURI).href;\n  if (BUILD28.asyncQueue) {\n    if (options.syncQueue) {\n      plt.$flags$ |= 4 /* queueSync */;\n    }\n  }\n  if (BUILD28.hydrateClientSide) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  if (BUILD28.hydrateClientSide && BUILD28.shadowDom) {\n    hydrateScopedToShadow();\n  }\n  let hasSlotRelocation = false;\n  lazyBundles.map(lazyBundle => {\n    lazyBundle[1].map(compactMeta => {\n      var _a2;\n      const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n        $members$: compactMeta[2],\n        $listeners$: compactMeta[3]\n      };\n      if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n        hasSlotRelocation = true;\n      }\n      if (BUILD28.member) {\n        cmpMeta.$members$ = compactMeta[2];\n      }\n      if (BUILD28.hostListener) {\n        cmpMeta.$listeners$ = compactMeta[3];\n      }\n      if (BUILD28.reflect) {\n        cmpMeta.$attrsToReflect$ = [];\n      }\n      if (BUILD28.watchCallback) {\n        cmpMeta.$watchers$ = (_a2 = compactMeta[4]) != null ? _a2 : {};\n      }\n      if (BUILD28.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n        cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n      }\n      const tagName = BUILD28.transformTagName && options.transformTagName ? options.transformTagName(cmpMeta.$tagName$) : cmpMeta.$tagName$;\n      const HostElement = class extends HTMLElement {\n        // StencilLazyHost\n        constructor(self) {\n          super(self);\n          this.hasRegisteredEventListeners = false;\n          self = this;\n          registerHost(self, cmpMeta);\n          if (BUILD28.shadowDom && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            if (supportsShadow) {\n              if (!self.shadowRoot) {\n                createShadowRoot.call(self, cmpMeta);\n              } else {\n                if (self.shadowRoot.mode !== \"open\") {\n                  throw new Error(`Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${self.shadowRoot.mode} but Stencil only supports open shadow roots.`);\n                }\n              }\n            } else if (!BUILD28.hydrateServerSide && !(\"shadowRoot\" in self)) {\n              self.shadowRoot = self;\n            }\n          }\n        }\n        connectedCallback() {\n          const hostRef = getHostRef(this);\n          if (!this.hasRegisteredEventListeners) {\n            this.hasRegisteredEventListeners = true;\n            addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n          }\n          if (appLoadFallback) {\n            clearTimeout(appLoadFallback);\n            appLoadFallback = null;\n          }\n          if (isBootstrapping) {\n            deferredConnectedCallbacks.push(this);\n          } else {\n            plt.jmp(() => connectedCallback(this));\n          }\n        }\n        disconnectedCallback() {\n          plt.jmp(() => disconnectedCallback(this));\n          plt.raf(() => {\n            var _a3;\n            const hostRef = getHostRef(this);\n            const i2 = deferredConnectedCallbacks.findIndex(host => host === this);\n            if (i2 > -1) {\n              deferredConnectedCallbacks.splice(i2, 1);\n            }\n            if (((_a3 = hostRef == null ? void 0 : hostRef.$vnode$) == null ? void 0 : _a3.$elm$) instanceof Node && !hostRef.$vnode$.$elm$.isConnected) {\n              delete hostRef.$vnode$.$elm$;\n            }\n          });\n        }\n        componentOnReady() {\n          return getHostRef(this).$onReadyPromise$;\n        }\n      };\n      if (BUILD28.experimentalSlotFixes) {\n        if (BUILD28.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchPseudoShadowDom(HostElement.prototype);\n        }\n      } else {\n        if (BUILD28.slotChildNodesFix) {\n          patchChildSlotNodes(HostElement.prototype);\n        }\n        if (BUILD28.cloneNodeFix) {\n          patchCloneNode(HostElement.prototype);\n        }\n        if (BUILD28.appendChildSlotFix) {\n          patchSlotAppendChild(HostElement.prototype);\n        }\n        if (BUILD28.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchTextContent(HostElement.prototype);\n        }\n      }\n      if (BUILD28.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */) {\n        HostElement.formAssociated = true;\n      }\n      if (BUILD28.hotModuleReplacement) {\n        HostElement.prototype[\"s-hmr\"] = function (hmrVersionId) {\n          hmrStart(this, cmpMeta, hmrVersionId);\n        };\n      }\n      cmpMeta.$lazyBundleId$ = lazyBundle[0];\n      if (!exclude.includes(tagName) && !customElements2.get(tagName)) {\n        cmpTags.push(tagName);\n        customElements2.define(tagName, proxyComponent(HostElement, cmpMeta, 1 /* isElementConstructor */));\n      }\n    });\n  });\n  if (cmpTags.length > 0) {\n    if (hasSlotRelocation) {\n      dataStyles.textContent += SLOT_FB_CSS;\n    }\n    if (BUILD28.invisiblePrehydration && (BUILD28.hydratedClass || BUILD28.hydratedAttribute)) {\n      dataStyles.textContent += cmpTags.sort() + HYDRATED_CSS;\n    }\n    if (dataStyles.innerHTML.length) {\n      dataStyles.setAttribute(\"data-styles\", \"\");\n      const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(win.document);\n      if (nonce != null) {\n        dataStyles.setAttribute(\"nonce\", nonce);\n      }\n      head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n    }\n  }\n  isBootstrapping = false;\n  if (deferredConnectedCallbacks.length) {\n    deferredConnectedCallbacks.map(host => host.connectedCallback());\n  } else {\n    if (BUILD28.profile) {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30, \"timeout\"));\n    } else {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));\n    }\n  }\n  endBootstrap();\n};\n\n// src/runtime/fragment.ts\nvar Fragment = (_, children) => children;\n\n// src/runtime/host-listener.ts\nimport { BUILD as BUILD29 } from \"@stencil/core/internal/app-data\";\nvar addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n  if (BUILD29.hostListener && listeners && win.document) {\n    if (BUILD29.hostListenerTargetParent) {\n      if (attachParentListeners) {\n        listeners = listeners.filter(([flags]) => flags & 32 /* TargetParent */);\n      } else {\n        listeners = listeners.filter(([flags]) => !(flags & 32 /* TargetParent */));\n      }\n    }\n    listeners.map(([flags, name, method]) => {\n      const target = BUILD29.hostListenerTarget ? getHostListenerTarget(win.document, elm, flags) : elm;\n      const handler = hostListenerProxy(hostRef, method);\n      const opts = hostListenerOpts(flags);\n      plt.ael(target, name, handler, opts);\n      (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n    });\n  }\n};\nvar hostListenerProxy = (hostRef, methodName) => ev => {\n  var _a;\n  try {\n    if (BUILD29.lazyLoad) {\n      if (hostRef.$flags$ & 256 /* isListenReady */) {\n        (_a = hostRef.$lazyInstance$) == null ? void 0 : _a[methodName](ev);\n      } else {\n        (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n      }\n    } else {\n      hostRef.$hostElement$[methodName](ev);\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n};\nvar getHostListenerTarget = (doc, elm, flags) => {\n  if (BUILD29.hostListenerTargetDocument && flags & 4 /* TargetDocument */) {\n    return doc;\n  }\n  if (BUILD29.hostListenerTargetWindow && flags & 8 /* TargetWindow */) {\n    return win;\n  }\n  if (BUILD29.hostListenerTargetBody && flags & 16 /* TargetBody */) {\n    return doc.body;\n  }\n  if (BUILD29.hostListenerTargetParent && flags & 32 /* TargetParent */ && elm.parentElement) {\n    return elm.parentElement;\n  }\n  return elm;\n};\nvar hostListenerOpts = flags => supportsListenerOptions ? {\n  passive: (flags & 1 /* Passive */) !== 0,\n  capture: (flags & 2 /* Capture */) !== 0\n} : (flags & 2 /* Capture */) !== 0;\n\n// src/runtime/nonce.ts\nvar setNonce = nonce => plt.$nonce$ = nonce;\n\n// src/runtime/platform-options.ts\nvar setPlatformOptions = opts => Object.assign(plt, opts);\n\n// src/runtime/render.ts\nfunction render(vnode, container) {\n  const cmpMeta = {\n    $flags$: 0,\n    $tagName$: container.tagName\n  };\n  const ref = {\n    $flags$: 0,\n    $cmpMeta$: cmpMeta,\n    $hostElement$: container\n  };\n  renderVdom(ref, vnode);\n}\n\n// src/runtime/vdom/vdom-annotations.ts\nvar insertVdomAnnotations = (doc, staticComponents) => {\n  if (doc != null) {\n    const docData = STENCIL_DOC_DATA in doc ? doc[STENCIL_DOC_DATA] : {\n      ...DEFAULT_DOC_DATA\n    };\n    docData.staticComponents = new Set(staticComponents);\n    const orgLocationNodes = [];\n    parseVNodeAnnotations(doc, doc.body, docData, orgLocationNodes);\n    orgLocationNodes.forEach(orgLocationNode => {\n      var _a;\n      if (orgLocationNode != null && orgLocationNode[\"s-nr\"]) {\n        const nodeRef = orgLocationNode[\"s-nr\"];\n        let hostId = nodeRef[\"s-host-id\"];\n        let nodeId = nodeRef[\"s-node-id\"];\n        let childId = `${hostId}.${nodeId}`;\n        if (hostId == null) {\n          hostId = 0;\n          docData.rootLevelIds++;\n          nodeId = docData.rootLevelIds;\n          childId = `${hostId}.${nodeId}`;\n          if (nodeRef.nodeType === 1 /* ElementNode */) {\n            nodeRef.setAttribute(HYDRATE_CHILD_ID, childId);\n            if (typeof nodeRef[\"s-sn\"] === \"string\" && !nodeRef.getAttribute(\"slot\")) {\n              nodeRef.setAttribute(\"s-sn\", nodeRef[\"s-sn\"]);\n            }\n          } else if (nodeRef.nodeType === 3 /* TextNode */) {\n            if (hostId === 0) {\n              const textContent = (_a = nodeRef.nodeValue) == null ? void 0 : _a.trim();\n              if (textContent === \"\") {\n                orgLocationNode.remove();\n                return;\n              }\n            }\n            const commentBeforeTextNode = doc.createComment(childId);\n            commentBeforeTextNode.nodeValue = `${TEXT_NODE_ID}.${childId}`;\n            insertBefore(nodeRef.parentNode, commentBeforeTextNode, nodeRef);\n          } else if (nodeRef.nodeType === 8 /* CommentNode */) {\n            const commentBeforeTextNode = doc.createComment(childId);\n            commentBeforeTextNode.nodeValue = `${COMMENT_NODE_ID}.${childId}`;\n            nodeRef.parentNode.insertBefore(commentBeforeTextNode, nodeRef);\n          }\n        }\n        let orgLocationNodeId = `${ORG_LOCATION_ID}.${childId}`;\n        const orgLocationParentNode = orgLocationNode.parentElement;\n        if (orgLocationParentNode) {\n          if (orgLocationParentNode[\"s-en\"] === \"\") {\n            orgLocationNodeId += `.`;\n          } else if (orgLocationParentNode[\"s-en\"] === \"c\") {\n            orgLocationNodeId += `.c`;\n          }\n        }\n        orgLocationNode.nodeValue = orgLocationNodeId;\n      }\n    });\n  }\n};\nvar parseVNodeAnnotations = (doc, node, docData, orgLocationNodes) => {\n  var _a;\n  if (node == null) {\n    return;\n  }\n  if (node[\"s-nr\"] != null) {\n    orgLocationNodes.push(node);\n  }\n  if (node.nodeType === 1 /* ElementNode */) {\n    const childNodes = [...Array.from(node.childNodes), ...Array.from(((_a = node.shadowRoot) == null ? void 0 : _a.childNodes) || [])];\n    childNodes.forEach(childNode => {\n      const hostRef = getHostRef(childNode);\n      if (hostRef != null && !docData.staticComponents.has(childNode.nodeName.toLowerCase())) {\n        const cmpData = {\n          nodeIds: 0\n        };\n        insertVNodeAnnotations(doc, childNode, hostRef.$vnode$, docData, cmpData);\n      }\n      parseVNodeAnnotations(doc, childNode, docData, orgLocationNodes);\n    });\n  }\n};\nvar insertVNodeAnnotations = (doc, hostElm, vnode, docData, cmpData) => {\n  if (vnode != null) {\n    const hostId = ++docData.hostIds;\n    hostElm.setAttribute(HYDRATE_ID, hostId);\n    if (hostElm[\"s-cr\"] != null) {\n      hostElm[\"s-cr\"].nodeValue = `${CONTENT_REF_ID}.${hostId}`;\n    }\n    if (vnode.$children$ != null) {\n      const depth = 0;\n      vnode.$children$.forEach((vnodeChild, index) => {\n        insertChildVNodeAnnotations(doc, vnodeChild, cmpData, hostId, depth, index);\n      });\n    }\n    if (hostElm && vnode && vnode.$elm$ && !hostElm.hasAttribute(HYDRATE_CHILD_ID)) {\n      const parent = hostElm.parentElement;\n      if (parent && parent.childNodes) {\n        const parentChildNodes = Array.from(parent.childNodes);\n        const comment = parentChildNodes.find(node => node.nodeType === 8 /* CommentNode */ && node[\"s-sr\"]);\n        if (comment) {\n          const index = parentChildNodes.indexOf(hostElm) - 1;\n          vnode.$elm$.setAttribute(HYDRATE_CHILD_ID, `${comment[\"s-host-id\"]}.${comment[\"s-node-id\"]}.0.${index}`);\n        }\n      }\n    }\n  }\n};\nvar insertChildVNodeAnnotations = (doc, vnodeChild, cmpData, hostId, depth, index) => {\n  const childElm = vnodeChild.$elm$;\n  if (childElm == null) {\n    return;\n  }\n  const nodeId = cmpData.nodeIds++;\n  const childId = `${hostId}.${nodeId}.${depth}.${index}`;\n  childElm[\"s-host-id\"] = hostId;\n  childElm[\"s-node-id\"] = nodeId;\n  if (childElm.nodeType === 1 /* ElementNode */) {\n    childElm.setAttribute(HYDRATE_CHILD_ID, childId);\n    if (typeof childElm[\"s-sn\"] === \"string\" && !childElm.getAttribute(\"slot\")) {\n      childElm.setAttribute(\"s-sn\", childElm[\"s-sn\"]);\n    }\n  } else if (childElm.nodeType === 3 /* TextNode */) {\n    const parentNode = childElm.parentNode;\n    const nodeName = parentNode == null ? void 0 : parentNode.nodeName;\n    if (nodeName !== \"STYLE\" && nodeName !== \"SCRIPT\") {\n      const textNodeId = `${TEXT_NODE_ID}.${childId}`;\n      const commentBeforeTextNode = doc.createComment(textNodeId);\n      insertBefore(parentNode, commentBeforeTextNode, childElm);\n    }\n  } else if (childElm.nodeType === 8 /* CommentNode */) {\n    if (childElm[\"s-sr\"]) {\n      const slotName = childElm[\"s-sn\"] || \"\";\n      const slotNodeId = `${SLOT_NODE_ID}.${childId}.${slotName}`;\n      childElm.nodeValue = slotNodeId;\n    }\n  }\n  if (vnodeChild.$children$ != null) {\n    const childDepth = depth + 1;\n    vnodeChild.$children$.forEach((vnode, index2) => {\n      insertChildVNodeAnnotations(doc, vnode, cmpData, hostId, childDepth, index2);\n    });\n  }\n};\nexport { BUILD30 as BUILD, Build, Env, Fragment, H, H as HTMLElement, HYDRATED_STYLE_ID, Host, NAMESPACE2 as NAMESPACE, STENCIL_DEV_MODE, addHostEventListeners, bootstrapLazy, cmpModules, connectedCallback, consoleDevError, consoleDevInfo, consoleDevWarn, consoleError, createEvent, defineCustomElement, disconnectedCallback, forceModeUpdate, forceUpdate, getAssetPath, getElement, getHostRef, getMode, getRenderingRef, getValue, h, insertVdomAnnotations, isMemberInElement, loadModule, modeResolutionChain, nextTick, parsePropertyValue, plt, postUpdateComponent, promiseResolve, proxyComponent, proxyCustomElement, readTask, registerHost, registerInstance, render, renderVdom, setAssetPath, setErrorHandler, setMode, setNonce, setPlatformHelpers, setPlatformOptions, setValue, styles, supportsConstructableStylesheets, supportsListenerOptions, supportsShadow, win, writeTask };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}