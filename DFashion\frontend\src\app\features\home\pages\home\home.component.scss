// Instagram-style Home Layout
.instagram-home-container {
  min-height: calc(100vh - 80px);
  background: #fafafa;
  padding-top: 20px;
}

.instagram-layout {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 30px;
  max-width: 975px;
  margin: 0 auto;
  padding: 0 20px;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    max-width: 614px;
    gap: 0;
    padding: 0;
  }

  @media (max-width: 768px) {
    padding: 0;
    gap: 0;
  }
}

// Loading State
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 16px;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e3e3e3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  p {
    color: #6c757d;
    font-size: 14px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Main Feed Styles
.main-feed {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
  max-width: 614px;
}

.stories-section {
  background: white;
  border: 1px solid #dbdbdb;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 24px;

  // Override the stories component styles to make it more compact
  ::ng-deep {
    .stories-container {
      margin: 0;
      padding: 16px;
      background: white;
      border-radius: 0;
      box-shadow: none;
      border: none;
      backdrop-filter: none;
    }

    .stories-header {
      display: none; // Hide the header to make it more Instagram-like
    }

    .stories-slider-wrapper {
      gap: 8px;
    }

    .nav-arrow {
      display: none; // Hide arrows for cleaner look
    }

    .story-item {
      margin: 0 4px;
    }

    .story-avatar {
      width: 56px;
      height: 56px;

      @media (max-width: 768px) {
        width: 56px;
        height: 56px;
      }
    }

    .story-avatar-inner {
      width: 50px;
      height: 50px;

      @media (max-width: 768px) {
        width: 50px;
        height: 50px;
      }
    }

    .story-username {
      font-size: 12px;
      color: #262626;
      text-shadow: none;
      max-width: 64px;

      @media (max-width: 768px) {
        max-width: 64px;
      }
    }
  }

  @media (max-width: 768px) {
    border-radius: 0;
    border-left: none;
    border-right: none;
    margin-bottom: 0;
  }
}

.posts-feed {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

// Instagram Post Styles
.instagram-post {
  background: white;
  border: 1px solid #dbdbdb;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 24px;
  max-width: 614px;
  width: 100%;

  @media (max-width: 768px) {
    border-radius: 0;
    border-left: none;
    border-right: none;
    margin-bottom: 0;
    border-bottom: none;
  }
}

.post-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;

  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
  }

  .user-details {
    .username {
      font-size: 14px;
      font-weight: 600;
      margin: 0;
      color: #262626;
    }

    .location {
      font-size: 12px;
      color: #8e8e8e;
    }
  }

  .more-options {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    color: #262626;

    &:hover {
      color: #8e8e8e;
    }
  }
}

.post-media {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  overflow: hidden;

  .post-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
}

// Product Tags Overlay
.product-tags-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;

  .product-tag-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    pointer-events: all;
    transition: all 0.3s ease;
    font-size: 16px;
    backdrop-filter: blur(10px);
    position: relative;

    &:hover {
      background: rgba(0, 0, 0, 0.9);
      transform: translate(-50%, -50%) scale(1.1);
    }

    &.active {
      background: rgba(0, 149, 246, 0.9);
      transform: translate(-50%, -50%) scale(1.1);
    }

    i {
      font-size: 16px;
    }

    .product-count {
      position: absolute;
      top: -8px;
      right: -8px;
      background: #ff3040;
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: 600;
      border: 2px solid white;
      animation: pulse-count 2s infinite;
    }
  }
}

.product-tag {
  position: absolute;
  pointer-events: all;
  cursor: pointer;
  z-index: 5;

  .tag-dot {
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    border: 2px solid #262626;
    position: relative;
    animation: pulse 2s infinite;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 6px;
      height: 6px;
      background: #262626;
      border-radius: 50%;
    }
  }

  .product-preview {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    border-radius: 8px;
    padding: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 200px;
    backdrop-filter: blur(10px);

    img {
      width: 40px;
      height: 40px;
      border-radius: 4px;
      object-fit: cover;
    }

    .product-info {
      color: white;

      .product-name {
        display: block;
        font-size: 12px;
        font-weight: 500;
      }

      .product-price {
        display: block;
        font-size: 14px;
        font-weight: 600;
        color: #f5f5f5;
      }
    }
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes pulse-count {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

// Post Actions
.post-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px 8px;

  .primary-actions {
    display: flex;
    gap: 16px;
  }

  .action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    font-size: 24px;
    color: #262626;
    transition: all 0.3s ease;

    &:hover {
      color: #8e8e8e;
    }

    &.liked {
      color: #ed4956;
      animation: heartBeat 0.6s ease;
    }

    &.saved {
      color: #262626;
    }
  }
}

@keyframes heartBeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

// Post Content Sections
.likes-section {
  padding: 0 16px 8px;

  .likes-count {
    font-size: 14px;
    font-weight: 600;
    color: #262626;
  }
}

.post-caption {
  padding: 0 16px 8px;
  font-size: 14px;
  line-height: 1.4;

  .username {
    font-weight: 600;
    color: #262626;
    margin-right: 8px;
  }

  .caption-text {
    color: #262626;
  }
}

// E-commerce Actions
.ecommerce-actions {
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #efefef;

  .product-showcase {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;

    .featured-product {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .product-thumbnail {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        object-fit: cover;
      }

      .product-details {
        flex: 1;

        .product-name {
          display: block;
          font-size: 12px;
          font-weight: 500;
          color: #262626;
          line-height: 1.2;
        }

        .product-price {
          display: block;
          font-size: 14px;
          font-weight: 600;
          color: #667eea;
        }
      }
    }
  }

  .shopping-buttons {
    display: flex;
    gap: 8px;

    .shop-btn {
      flex: 1;
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;

      &.buy-btn {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
      }

      &.wishlist-btn {
        background: white;
        border: 1px solid #dbdbdb;
        color: #262626;

        &:hover {
          background: #f8f9fa;
          border-color: #c6c6c6;
        }
      }

      &.cart-btn {
        background: linear-gradient(135deg, #f093fb, #f5576c);
        color: white;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(240, 147, 251, 0.4);
        }
      }

      i {
        font-size: 10px;
      }
    }
  }
}

// Comments Section
.comments-preview {
  padding: 0 16px 8px;

  .view-comments-btn {
    background: none;
    border: none;
    color: #8e8e8e;
    font-size: 14px;
    cursor: pointer;
    margin-bottom: 4px;

    &:hover {
      color: #262626;
    }
  }

  .recent-comments {
    .comment {
      font-size: 14px;
      line-height: 1.4;
      margin-bottom: 2px;

      .comment-username {
        font-weight: 600;
        color: #262626;
        margin-right: 8px;
      }

      .comment-text {
        color: #262626;
      }
    }
  }
}

.post-time {
  padding: 0 16px 8px;
  font-size: 10px;
  color: #8e8e8e;
  text-transform: uppercase;
  letter-spacing: 0.2px;
}

.add-comment-section {
  padding: 12px 16px;
  border-top: 1px solid #efefef;
  display: flex;
  align-items: center;
  gap: 8px;

  .comment-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 14px;
    color: #262626;

    &::placeholder {
      color: #8e8e8e;
    }
  }

  .post-comment-btn {
    background: none;
    border: none;
    color: #0095f6;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;

    &:disabled {
      color: #c7c7c7;
      cursor: not-allowed;
    }

    &:not(:disabled):hover {
      color: #00376b;
    }
  }
}

// Instagram Sidebar
.instagram-sidebar {
  position: sticky;
  top: 84px; // Account for header height
  height: fit-content;
  padding-left: 0;

  @media (max-width: 1024px) {
    display: none;
  }
}

.profile-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0 24px 0;
  margin-bottom: 8px;

  .profile-avatar {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    object-fit: cover;
  }

  .profile-info {
    flex: 1;

    .profile-username {
      font-size: 14px;
      font-weight: 600;
      color: #262626;
      margin: 0 0 2px 0;
    }

    .profile-name {
      font-size: 14px;
      color: #8e8e8e;
    }
  }

  .switch-btn {
    background: none;
    border: none;
    color: #0095f6;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;

    &:hover {
      color: #00376b;
    }
  }
}

.sidebar-section {
  margin-bottom: 32px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      font-size: 14px;
      font-weight: 600;
      color: #8e8e8e;
      margin: 0;
      text-transform: none;
    }

    .see-all-btn {
      background: none;
      border: none;
      color: #262626;
      font-size: 12px;
      font-weight: 400;
      cursor: pointer;

      &:hover {
        color: #8e8e8e;
      }
    }
  }
}

// Collection Items
.collection-items, .featured-items, .trending-items, .arrivals-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.collection-item, .featured-item, .trending-item, .arrival-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px 0;
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.05);
  }

  img {
    width: 44px;
    height: 44px;
    border-radius: 3px;
    object-fit: cover;
  }

  .collection-info, .featured-info, .trending-info, .arrival-info {
    flex: 1;

    .collection-name, .featured-name, .trending-name, .arrival-name {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: #262626;
      line-height: 1.2;
      margin-bottom: 2px;
    }

    .featured-brand, .arrival-brand {
      display: block;
      font-size: 12px;
      color: #8e8e8e;
      margin-bottom: 2px;
    }

    .collection-price, .featured-price, .trending-price, .arrival-price {
      font-size: 12px;
      font-weight: 600;
      color: #667eea;
    }

    .featured-price {
      display: flex;
      align-items: center;
      gap: 6px;

      .current-price {
        color: #667eea;
      }

      .original-price {
        color: #8e8e8e;
        text-decoration: line-through;
        font-size: 10px;
      }
    }

    .trending-stats {
      display: flex;
      gap: 8px;
      margin-bottom: 2px;

      span {
        display: flex;
        align-items: center;
        gap: 2px;
        font-size: 10px;
        color: #8e8e8e;

        i {
          font-size: 8px;
        }
      }
    }
  }
}

// Special badges
.trending-badge, .new-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 8px;
  font-weight: 600;
  color: white;
}

.trending-badge {
  background: linear-gradient(135deg, #ff6b6b, #ffa500);
  display: flex;
  align-items: center;
  gap: 2px;

  i {
    font-size: 6px;
  }
}

.new-badge {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.trending-item, .arrival-item {
  position: relative;
}

// Suggested Users
.suggested-users {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suggested-user {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;

  .suggested-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
  }

  .suggested-info {
    flex: 1;

    .suggested-username {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: #262626;
      line-height: 1.2;
    }

    .suggested-followers {
      font-size: 12px;
      color: #8e8e8e;
    }
  }

  .follow-btn {
    background: #0095f6;
    border: none;
    color: white;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background: #00376b;
    }
  }
}

// Sidebar Footer
.sidebar-footer {
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #efefef;

  .footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;

    a {
      font-size: 11px;
      color: #c7c7c7;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .copyright {
    font-size: 11px;
    color: #c7c7c7;
  }
}

// Mobile Responsiveness
@media (max-width: 768px) {
  .instagram-home-container {
    padding-top: 0;
  }

  .instagram-layout {
    padding: 0;
    max-width: 100%;
  }

  .main-feed {
    gap: 0;
  }

  .instagram-post {
    margin-bottom: 0;
    border-radius: 0;
    border-left: none;
    border-right: none;
    max-width: 100%;

    &:last-child {
      border-bottom: 1px solid #dbdbdb;
    }
  }

  .stories-section {
    border-radius: 0;
    border-left: none;
    border-right: none;
    margin-bottom: 0;
    border-bottom: 1px solid #dbdbdb;
  }

  .post-header {
    padding: 14px 16px;
  }

  .post-actions {
    padding: 6px 16px 4px;
  }

  .ecommerce-actions {
    padding: 12px 16px;

    .shopping-buttons {
      .shop-btn {
        padding: 8px 12px;
        font-size: 12px;
      }
    }
  }

  .add-comment-section {
    padding: 12px 16px;
  }

  .likes-section, .post-caption, .comments-preview, .post-time {
    padding-left: 16px;
    padding-right: 16px;
  }
}

@media (max-width: 480px) {
  .ecommerce-actions {
    .product-showcase {
      flex-direction: column;
      gap: 8px;

      .featured-product {
        .product-thumbnail {
          width: 32px;
          height: 32px;
        }
      }
    }

    .shopping-buttons {
      flex-direction: column;
      gap: 6px;

      .shop-btn {
        padding: 8px 16px;
        font-size: 12px;
      }
    }
  }
}
