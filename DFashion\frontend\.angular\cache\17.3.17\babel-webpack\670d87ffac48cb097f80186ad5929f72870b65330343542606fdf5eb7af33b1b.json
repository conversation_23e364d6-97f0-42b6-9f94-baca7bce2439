{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/shop-data.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6, 7, 8];\nfunction NewArrivalsComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_button_10_Template_button_click_0_listener() {\n      const filter_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.setActiveTimeFilter(filter_r2.value));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r2.activeTimeFilter === filter_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", filter_r2.label, \" \");\n  }\n}\nfunction NewArrivalsComponent_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"div\", 20);\n    i0.ɵɵelementStart(2, \"div\", 21);\n    i0.ɵɵelement(3, \"div\", 22)(4, \"div\", 23)(5, \"div\", 22);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NewArrivalsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17);\n    i0.ɵɵtemplate(2, NewArrivalsComponent_div_15_div_2_Template, 6, 0, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", product_r5.pricing.discountPercentage, \"% OFF \");\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_div_22_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 51);\n  }\n  if (rf & 2) {\n    const star_r6 = ctx.$implicit;\n    const product_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r6 <= product_r5.rating.average);\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48);\n    i0.ɵɵtemplate(2, NewArrivalsComponent_div_16_div_1_div_22_i_2_Template, 1, 2, \"i\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 50);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getStarArray(product_r5.rating.average));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r5.rating.count, \")\");\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, product_r5.pricing.mrp, \"1.0-0\"), \" \");\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 53);\n    i0.ɵɵtext(2, \" In Stock \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵtext(2, \" Limited Stock \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 55);\n    i0.ɵɵtext(2, \" Out of Stock \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NewArrivalsComponent_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_16_div_1_Template_div_click_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToProduct(product_r5));\n    })(\"keydown.enter\", function NewArrivalsComponent_div_16_div_1_Template_div_keydown_enter_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToProduct(product_r5));\n    })(\"keydown.space\", function NewArrivalsComponent_div_16_div_1_Template_div_keydown_space_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToProduct(product_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 26)(2, \"img\", 27);\n    i0.ɵɵlistener(\"error\", function NewArrivalsComponent_div_16_div_1_Template_img_error_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onImageError($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 28);\n    i0.ɵɵelement(4, \"i\", 4);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"NEW\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, NewArrivalsComponent_div_16_div_1_div_7_Template, 2, 1, \"div\", 29);\n    i0.ɵɵelementStart(8, \"div\", 30);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 31)(11, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_16_div_1_Template_button_click_11_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.toggleWishlist(product_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(12, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_16_div_1_Template_button_click_13_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.quickView(product_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(14, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_16_div_1_Template_button_click_15_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.addToCart(product_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(16, \"i\", 36);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 37)(18, \"div\", 38);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"h3\", 39);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, NewArrivalsComponent_div_16_div_1_div_22_Template, 5, 2, \"div\", 40);\n    i0.ɵɵelementStart(23, \"div\", 41)(24, \"span\", 42);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, NewArrivalsComponent_div_16_div_1_span_27_Template, 3, 4, \"span\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 44);\n    i0.ɵɵtemplate(29, NewArrivalsComponent_div_16_div_1_span_29_Template, 3, 0, \"span\", 45)(30, NewArrivalsComponent_div_16_div_1_span_30_Template, 3, 0, \"span\", 45)(31, NewArrivalsComponent_div_16_div_1_span_31_Template, 3, 0, \"span\", 45);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-label\", \"View \" + product_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.getProductImage(product_r5), i0.ɵɵsanitizeUrl)(\"alt\", product_r5.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", product_r5.pricing.discountPercentage > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getArrivalDateText(product_r5.createdAt), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", \"Add to wishlist\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"wishlisted\", ctx_r2.isInWishlist(product_r5._id));\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Quick view\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", product_r5.availability.status !== \"in-stock\");\n    i0.ɵɵattribute(\"aria-label\", \"Add to cart\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r5.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r5.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.rating.count > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(26, 21, product_r5.pricing.sellingPrice, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r5.pricing.discountPercentage > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(product_r5.availability.status);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.availability.status === \"in-stock\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.availability.status === \"low-stock\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.availability.status === \"out-of-stock\");\n  }\n}\nfunction NewArrivalsComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, NewArrivalsComponent_div_16_div_1_Template, 32, 24, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.products)(\"ngForTrackBy\", ctx_r2.trackByProductId);\n  }\n}\nfunction NewArrivalsComponent_div_17_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 60);\n  }\n}\nfunction NewArrivalsComponent_div_17_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 61);\n  }\n}\nfunction NewArrivalsComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_17_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.loadMoreProducts());\n    });\n    i0.ɵɵtemplate(2, NewArrivalsComponent_div_17_i_2_Template, 1, 0, \"i\", 58)(3, NewArrivalsComponent_div_17_i_3_Template, 1, 0, \"i\", 59);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isLoadingMore);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoadingMore);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingMore);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.isLoadingMore ? \"Loading...\" : \"Load More\");\n  }\n}\nfunction NewArrivalsComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63);\n    i0.ɵɵelement(2, \"i\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Check back soon for the latest fashion!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_18_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.loadProducts());\n    });\n    i0.ɵɵelement(8, \"i\", 66);\n    i0.ɵɵtext(9, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class NewArrivalsComponent {\n  constructor(shopDataService, router) {\n    this.shopDataService = shopDataService;\n    this.router = router;\n    this.maxProducts = 8;\n    this.showHeader = true;\n    this.showLoadMore = true;\n    this.products = [];\n    this.isLoading = true;\n    this.isLoadingMore = false;\n    this.hasMoreProducts = true;\n    this.activeTimeFilter = 'all';\n    this.wishlistItems = new Set();\n    this.currentPage = 1;\n    this.timeFilters = [{\n      label: 'All Time',\n      value: 'all'\n    }, {\n      label: 'Today',\n      value: 'today'\n    }, {\n      label: 'This Week',\n      value: 'week'\n    }, {\n      label: 'This Month',\n      value: 'month'\n    }];\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.loadProducts();\n    this.loadWishlist();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadProducts() {\n    this.isLoading = true;\n    this.currentPage = 1;\n    this.shopDataService.loadNewArrivals(this.maxProducts).pipe(takeUntil(this.destroy$)).subscribe({\n      next: products => {\n        this.products = products;\n        this.hasMoreProducts = products.length === this.maxProducts;\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading new arrivals:', error);\n        this.isLoading = false;\n      }\n    });\n  }\n  loadMoreProducts() {\n    if (this.isLoadingMore || !this.hasMoreProducts) return;\n    this.isLoadingMore = true;\n    this.currentPage++;\n    // In a real implementation, this would load the next page\n    setTimeout(() => {\n      // Mock loading more products\n      this.isLoadingMore = false;\n      this.hasMoreProducts = false; // For demo purposes\n    }, 1000);\n  }\n  setActiveTimeFilter(filter) {\n    this.activeTimeFilter = filter;\n    this.loadProducts();\n  }\n  navigateToProduct(product) {\n    this.trackProductClick(product);\n    this.router.navigate(['/product', product._id]);\n  }\n  viewAllNewArrivals() {\n    this.router.navigate(['/shop/new-arrivals']);\n  }\n  toggleWishlist(product) {\n    if (this.wishlistItems.has(product._id)) {\n      this.wishlistItems.delete(product._id);\n    } else {\n      this.wishlistItems.add(product._id);\n    }\n    this.saveWishlist();\n  }\n  isInWishlist(productId) {\n    return this.wishlistItems.has(productId);\n  }\n  quickView(product) {\n    console.log('Quick view:', product);\n  }\n  addToCart(product) {\n    if (product.availability.status !== 'in-stock') {\n      return;\n    }\n    console.log('Add to cart:', product);\n    this.showAddToCartSuccess(product);\n  }\n  getProductImage(product) {\n    const primaryImage = product.images.find(img => img.isPrimary);\n    return primaryImage ? primaryImage.url : product.images[0]?.url || '';\n  }\n  getStarArray(rating) {\n    return Array(5).fill(0).map((_, i) => i + 1);\n  }\n  getArrivalDateText(dateString) {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now.getTime() - date.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) {\n      return 'Today';\n    } else if (diffDays <= 7) {\n      return `${diffDays} days ago`;\n    } else if (diffDays <= 30) {\n      const weeks = Math.floor(diffDays / 7);\n      return weeks === 1 ? '1 week ago' : `${weeks} weeks ago`;\n    } else {\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric'\n      });\n    }\n  }\n  onImageError(event) {\n    event.target.src = 'https://via.placeholder.com/300x400/f0f0f0/666?text=Product+Image';\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  trackProductClick(product) {\n    if (typeof window.gtag !== 'undefined') {\n      window.gtag('event', 'product_click', {\n        product_name: product.name,\n        product_id: product._id,\n        product_brand: product.brand,\n        product_category: product.category,\n        event_category: 'new_arrivals'\n      });\n    }\n  }\n  loadWishlist() {\n    const stored = localStorage.getItem('dfashion_wishlist');\n    if (stored) {\n      try {\n        const wishlist = JSON.parse(stored);\n        this.wishlistItems = new Set(wishlist);\n      } catch (e) {\n        console.warn('Failed to load wishlist');\n      }\n    }\n  }\n  saveWishlist() {\n    localStorage.setItem('dfashion_wishlist', JSON.stringify(Array.from(this.wishlistItems)));\n  }\n  showAddToCartSuccess(product) {\n    console.log(`${product.name} added to cart!`);\n  }\n  static {\n    this.ɵfac = function NewArrivalsComponent_Factory(t) {\n      return new (t || NewArrivalsComponent)(i0.ɵɵdirectiveInject(i1.ShopDataService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NewArrivalsComponent,\n      selectors: [[\"app-new-arrivals\"]],\n      inputs: {\n        maxProducts: \"maxProducts\",\n        showHeader: \"showHeader\",\n        showLoadMore: \"showLoadMore\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 19,\n      vars: 5,\n      consts: [[1, \"new-arrivals-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [1, \"fas\", \"fa-sparkles\"], [1, \"section-subtitle\"], [1, \"header-actions\"], [1, \"time-filters\"], [\"class\", \"filter-btn\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"products-grid\", 4, \"ngIf\"], [\"class\", \"load-more-container\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"filter-btn\", 3, \"click\"], [1, \"loading-container\"], [1, \"products-grid\"], [\"class\", \"product-card-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card-skeleton\"], [1, \"skeleton-image\"], [1, \"skeleton-content\"], [1, \"skeleton-text\"], [1, \"skeleton-text\", \"short\"], [\"class\", \"product-card\", \"tabindex\", \"0\", 3, \"click\", \"keydown.enter\", \"keydown.space\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"tabindex\", \"0\", 1, \"product-card\", 3, \"click\", \"keydown.enter\", \"keydown.space\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"error\", \"src\", \"alt\"], [1, \"new-badge\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"arrival-date\"], [1, \"quick-actions\"], [1, \"quick-action-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"fas\", \"fa-eye\"], [1, \"quick-action-btn\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [\"class\", \"product-rating\", 4, \"ngIf\"], [1, \"product-pricing\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"stock-status\"], [4, \"ngIf\"], [1, \"discount-badge\"], [1, \"product-rating\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 3, \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"fas\", \"fa-star\"], [1, \"original-price\"], [1, \"fas\", \"fa-check-circle\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"fas\", \"fa-times-circle\"], [1, \"load-more-container\"], [1, \"load-more-btn\", 3, \"click\", \"disabled\"], [\"class\", \"fas fa-plus\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-plus\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"empty-state\"], [1, \"empty-icon\"], [1, \"fas\", \"fa-box-open\"], [1, \"retry-btn\", 3, \"click\"], [1, \"fas\", \"fa-refresh\"]],\n      template: function NewArrivalsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵelement(4, \"i\", 4);\n          i0.ɵɵtext(5, \" New Arrivals \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Fresh styles just landed\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7);\n          i0.ɵɵtemplate(10, NewArrivalsComponent_button_10_Template, 2, 3, \"button\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function NewArrivalsComponent_Template_button_click_11_listener() {\n            return ctx.viewAllNewArrivals();\n          });\n          i0.ɵɵelementStart(12, \"span\");\n          i0.ɵɵtext(13, \"View All\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"i\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(15, NewArrivalsComponent_div_15_Template, 3, 2, \"div\", 11)(16, NewArrivalsComponent_div_16_Template, 2, 2, \"div\", 12)(17, NewArrivalsComponent_div_17_Template, 6, 4, \"div\", 13)(18, NewArrivalsComponent_div_18_Template, 10, 0, \"div\", 14);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngForOf\", ctx.timeFilters);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.products.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.products.length > 0 && ctx.hasMoreProducts);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.products.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.DecimalPipe],\n      styles: [\".new-arrivals-section[_ngcontent-%COMP%] {\\n  padding: 2rem 0;\\n  background: #f8f9fa;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 2rem;\\n  padding: 0 1rem;\\n  gap: 1rem;\\n}\\n@media (max-width: 768px) {\\n  .new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: #262626;\\n  margin: 0 0 0.5rem 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #00bcd4;\\n  font-size: 1.8rem;\\n  animation: _ngcontent-%COMP%_sparkle 3s infinite;\\n}\\n@media (max-width: 768px) {\\n  .new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n@media (max-width: 768px) {\\n  .new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n    align-items: stretch;\\n  }\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  background: white;\\n  padding: 0.25rem;\\n  border-radius: 25px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\\n}\\n@media (max-width: 768px) {\\n  .new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  border: none;\\n  background: transparent;\\n  color: #666;\\n  border-radius: 20px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  font-weight: 500;\\n  font-size: 0.85rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 188, 212, 0.1);\\n  color: #00bcd4;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%]   .filter-btn.active[_ngcontent-%COMP%] {\\n  background: #00bcd4;\\n  color: white;\\n  box-shadow: 0 2px 8px rgba(0, 188, 212, 0.3);\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1.5rem;\\n  background: linear-gradient(135deg, #00bcd4 0%, #26c6da 100%);\\n  color: white;\\n  border: none;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);\\n  white-space: nowrap;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(0, 188, 212, 0.4);\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: translateX(3px);\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%] {\\n  padding: 0 1rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 1.5rem;\\n}\\n@media (max-width: 768px) {\\n  .new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n    gap: 1rem;\\n  }\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 300px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-text[_ngcontent-%COMP%] {\\n  height: 16px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 4px;\\n  margin-bottom: 0.5rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-text.short[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 1.5rem;\\n  padding: 0 1rem;\\n}\\n@media (max-width: 768px) {\\n  .new-arrivals-section[_ngcontent-%COMP%]   .products-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n    gap: 1rem;\\n  }\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover   .quick-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:focus {\\n  outline: 3px solid #00bcd4;\\n  outline-offset: 2px;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 300px;\\n  overflow: hidden;\\n  background: #f8f9fa;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  left: 12px;\\n  background: linear-gradient(135deg, #00bcd4, #26c6da);\\n  color: white;\\n  padding: 0.5rem 0.75rem;\\n  border-radius: 20px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  box-shadow: 0 2px 8px rgba(0, 188, 212, 0.3);\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_sparkle 3s infinite;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  background: #e91e63;\\n  color: white;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .arrival-date[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 12px;\\n  left: 12px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 12px;\\n  right: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  opacity: 0;\\n  transform: translateY(10px);\\n  transition: all 0.3s ease;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]:hover {\\n  background: #00bcd4;\\n  color: white;\\n  transform: scale(1.1);\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]   .fa-heart.wishlisted[_ngcontent-%COMP%] {\\n  color: #e91e63;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 0.25rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #262626;\\n  margin: 0 0 0.5rem 0;\\n  line-height: 1.3;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.125rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .fa-star[_ngcontent-%COMP%] {\\n  color: #ddd;\\n  font-size: 0.8rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .fa-star.filled[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 0.8rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 700;\\n  color: #262626;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #8e8e8e;\\n  text-decoration: line-through;\\n  margin-left: 0.5rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status.in-stock[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status.low-stock[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status.out-of-stock[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .load-more-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 2rem;\\n  padding: 0 1rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 1rem 2rem;\\n  background: linear-gradient(135deg, #00bcd4 0%, #26c6da 100%);\\n  color: white;\\n  border: none;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(0, 188, 212, 0.4);\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .load-more-container[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 3rem 1rem;\\n  color: #8e8e8e;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  margin-bottom: 1rem;\\n  color: #ddd;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #666;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 1.5rem 0;\\n}\\n.new-arrivals-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: #00bcd4;\\n  color: white;\\n  border: none;\\n  padding: 0.75rem 1.5rem;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  margin: 0 auto;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_sparkle {\\n  0%, 100% {\\n    transform: scale(1) rotate(0deg);\\n    opacity: 1;\\n  }\\n  25% {\\n    transform: scale(1.1) rotate(90deg);\\n    opacity: 0.8;\\n  }\\n  50% {\\n    transform: scale(1) rotate(180deg);\\n    opacity: 1;\\n  }\\n  75% {\\n    transform: scale(1.1) rotate(270deg);\\n    opacity: 0.8;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .new-arrivals-section[_ngcontent-%COMP%] {\\n    background: #121212;\\n  }\\n  .new-arrivals-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    background: #1e1e1e;\\n  }\\n  .new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%] {\\n    background: #2a2a2a;\\n  }\\n  .new-arrivals-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%] {\\n    background: rgba(30, 30, 30, 0.9);\\n    color: #ffffff;\\n  }\\n  .new-arrivals-section[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%] {\\n    background: #1e1e1e;\\n  }\\n  .new-arrivals-section[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%] {\\n    color: #ffffff;\\n  }\\n  .new-arrivals-section[_ngcontent-%COMP%]   .time-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]:hover {\\n    background: rgba(0, 188, 212, 0.2);\\n  }\\n  .new-arrivals-section[_ngcontent-%COMP%]   .load-more-btn[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #00bcd4 0%, #26c6da 100%);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵlistener", "NewArrivalsComponent_button_10_Template_button_click_0_listener", "filter_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "setActiveTimeFilter", "value", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "activeTimeFilter", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵelement", "ɵɵtemplate", "NewArrivalsComponent_div_15_div_2_Template", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "product_r5", "pricing", "discountPercentage", "star_r6", "rating", "average", "NewArrivalsComponent_div_16_div_1_div_22_i_2_Template", "getStarArray", "count", "ɵɵpipeBind2", "mrp", "NewArrivalsComponent_div_16_div_1_Template_div_click_0_listener", "_r4", "navigateToProduct", "NewArrivalsComponent_div_16_div_1_Template_div_keydown_enter_0_listener", "NewArrivalsComponent_div_16_div_1_Template_div_keydown_space_0_listener", "NewArrivalsComponent_div_16_div_1_Template_img_error_2_listener", "$event", "onImageError", "NewArrivalsComponent_div_16_div_1_div_7_Template", "NewArrivalsComponent_div_16_div_1_Template_button_click_11_listener", "toggleWishlist", "stopPropagation", "NewArrivalsComponent_div_16_div_1_Template_button_click_13_listener", "quickView", "NewArrivalsComponent_div_16_div_1_Template_button_click_15_listener", "addToCart", "NewArrivalsComponent_div_16_div_1_div_22_Template", "NewArrivalsComponent_div_16_div_1_span_27_Template", "NewArrivalsComponent_div_16_div_1_span_29_Template", "NewArrivalsComponent_div_16_div_1_span_30_Template", "NewArrivalsComponent_div_16_div_1_span_31_Template", "getProductImage", "ɵɵsanitizeUrl", "name", "getArrivalDateText", "createdAt", "isInWishlist", "_id", "availability", "status", "ɵɵtextInterpolate", "brand", "sellingPrice", "ɵɵclassMap", "NewArrivalsComponent_div_16_div_1_Template", "products", "trackByProductId", "NewArrivalsComponent_div_17_Template_button_click_1_listener", "_r7", "loadMoreProducts", "NewArrivalsComponent_div_17_i_2_Template", "NewArrivalsComponent_div_17_i_3_Template", "isLoadingMore", "NewArrivalsComponent_div_18_Template_button_click_7_listener", "_r8", "loadProducts", "NewArrivalsComponent", "constructor", "shopDataService", "router", "maxProducts", "showHeader", "showLoadMore", "isLoading", "hasMoreProducts", "wishlistItems", "Set", "currentPage", "timeFilters", "destroy$", "ngOnInit", "loadWishlist", "ngOnDestroy", "next", "complete", "loadNewArrivals", "pipe", "subscribe", "length", "error", "console", "setTimeout", "filter", "product", "trackProductClick", "navigate", "viewAllNewArrivals", "has", "delete", "add", "saveWishlist", "productId", "log", "showAddToCartSuccess", "primaryImage", "images", "find", "img", "isPrimary", "url", "Array", "fill", "map", "_", "i", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "getTime", "diffDays", "ceil", "weeks", "floor", "toLocaleDateString", "month", "day", "event", "target", "src", "index", "window", "gtag", "product_name", "product_id", "product_brand", "product_category", "category", "event_category", "stored", "localStorage", "getItem", "wishlist", "JSON", "parse", "e", "warn", "setItem", "stringify", "from", "ɵɵdirectiveInject", "i1", "ShopDataService", "i2", "Router", "selectors", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NewArrivalsComponent_Template", "rf", "ctx", "NewArrivalsComponent_button_10_Template", "NewArrivalsComponent_Template_button_click_11_listener", "NewArrivalsComponent_div_15_Template", "NewArrivalsComponent_div_16_Template", "NewArrivalsComponent_div_17_Template", "NewArrivalsComponent_div_18_Template", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\shared\\components\\new-arrivals\\new-arrivals.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subject, takeUntil } from 'rxjs';\nimport { ShopDataService, NewArrival } from '../../../core/services/shop-data.service';\n\n@Component({\n  selector: 'app-new-arrivals',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"new-arrivals-section\">\n      <!-- Section Header -->\n      <div class=\"section-header\">\n        <div class=\"header-content\">\n          <h2 class=\"section-title\">\n            <i class=\"fas fa-sparkles\"></i>\n            New Arrivals\n          </h2>\n          <p class=\"section-subtitle\">Fresh styles just landed</p>\n        </div>\n        <div class=\"header-actions\">\n          <div class=\"time-filters\">\n            <button \n              *ngFor=\"let filter of timeFilters\"\n              class=\"filter-btn\"\n              [class.active]=\"activeTimeFilter === filter.value\"\n              (click)=\"setActiveTimeFilter(filter.value)\">\n              {{ filter.label }}\n            </button>\n          </div>\n          <button class=\"view-all-btn\" (click)=\"viewAllNewArrivals()\">\n            <span>View All</span>\n            <i class=\"fas fa-arrow-right\"></i>\n          </button>\n        </div>\n      </div>\n\n      <!-- Loading State -->\n      <div *ngIf=\"isLoading\" class=\"loading-container\">\n        <div class=\"products-grid\">\n          <div *ngFor=\"let item of [1,2,3,4,5,6,7,8]\" class=\"product-card-skeleton\">\n            <div class=\"skeleton-image\"></div>\n            <div class=\"skeleton-content\">\n              <div class=\"skeleton-text\"></div>\n              <div class=\"skeleton-text short\"></div>\n              <div class=\"skeleton-text\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Products Grid -->\n      <div *ngIf=\"!isLoading && products.length > 0\" class=\"products-grid\">\n        <div \n          *ngFor=\"let product of products; trackBy: trackByProductId\" \n          class=\"product-card\"\n          (click)=\"navigateToProduct(product)\"\n          [attr.aria-label]=\"'View ' + product.name\"\n          tabindex=\"0\"\n          (keydown.enter)=\"navigateToProduct(product)\"\n          (keydown.space)=\"navigateToProduct(product)\">\n          \n          <!-- Product Image -->\n          <div class=\"product-image-container\">\n            <img \n              [src]=\"getProductImage(product)\" \n              [alt]=\"product.name\"\n              class=\"product-image\"\n              loading=\"lazy\"\n              (error)=\"onImageError($event)\">\n            \n            <!-- New Badge -->\n            <div class=\"new-badge\">\n              <i class=\"fas fa-sparkles\"></i>\n              <span>NEW</span>\n            </div>\n            \n            <!-- Discount Badge -->\n            <div class=\"discount-badge\" *ngIf=\"product.pricing.discountPercentage > 0\">\n              {{ product.pricing.discountPercentage }}% OFF\n            </div>\n            \n            <!-- Arrival Date -->\n            <div class=\"arrival-date\">\n              {{ getArrivalDateText(product.createdAt) }}\n            </div>\n            \n            <!-- Quick Actions -->\n            <div class=\"quick-actions\">\n              <button \n                class=\"quick-action-btn\"\n                (click)=\"toggleWishlist(product); $event.stopPropagation()\"\n                [attr.aria-label]=\"'Add to wishlist'\">\n                <i class=\"fas fa-heart\" [class.wishlisted]=\"isInWishlist(product._id)\"></i>\n              </button>\n              <button \n                class=\"quick-action-btn\"\n                (click)=\"quickView(product); $event.stopPropagation()\"\n                [attr.aria-label]=\"'Quick view'\">\n                <i class=\"fas fa-eye\"></i>\n              </button>\n              <button \n                class=\"quick-action-btn\"\n                (click)=\"addToCart(product); $event.stopPropagation()\"\n                [attr.aria-label]=\"'Add to cart'\"\n                [disabled]=\"product.availability.status !== 'in-stock'\">\n                <i class=\"fas fa-shopping-cart\"></i>\n              </button>\n            </div>\n          </div>\n\n          <!-- Product Info -->\n          <div class=\"product-info\">\n            <div class=\"product-brand\">{{ product.brand }}</div>\n            <h3 class=\"product-name\">{{ product.name }}</h3>\n            \n            <!-- Rating -->\n            <div class=\"product-rating\" *ngIf=\"product.rating.count > 0\">\n              <div class=\"stars\">\n                <i *ngFor=\"let star of getStarArray(product.rating.average)\" \n                   class=\"fas fa-star\" \n                   [class.filled]=\"star <= product.rating.average\"></i>\n              </div>\n              <span class=\"rating-text\">({{ product.rating.count }})</span>\n            </div>\n            \n            <!-- Pricing -->\n            <div class=\"product-pricing\">\n              <span class=\"current-price\">₹{{ product.pricing.sellingPrice | number:'1.0-0' }}</span>\n              <span class=\"original-price\" *ngIf=\"product.pricing.discountPercentage > 0\">\n                ₹{{ product.pricing.mrp | number:'1.0-0' }}\n              </span>\n            </div>\n            \n            <!-- Stock Status -->\n            <div class=\"stock-status\" [class]=\"product.availability.status\">\n              <span *ngIf=\"product.availability.status === 'in-stock'\">\n                <i class=\"fas fa-check-circle\"></i>\n                In Stock\n              </span>\n              <span *ngIf=\"product.availability.status === 'low-stock'\">\n                <i class=\"fas fa-exclamation-triangle\"></i>\n                Limited Stock\n              </span>\n              <span *ngIf=\"product.availability.status === 'out-of-stock'\">\n                <i class=\"fas fa-times-circle\"></i>\n                Out of Stock\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Load More Button -->\n      <div class=\"load-more-container\" *ngIf=\"!isLoading && products.length > 0 && hasMoreProducts\">\n        <button class=\"load-more-btn\" (click)=\"loadMoreProducts()\" [disabled]=\"isLoadingMore\">\n          <i class=\"fas fa-plus\" *ngIf=\"!isLoadingMore\"></i>\n          <i class=\"fas fa-spinner fa-spin\" *ngIf=\"isLoadingMore\"></i>\n          <span>{{ isLoadingMore ? 'Loading...' : 'Load More' }}</span>\n        </button>\n      </div>\n\n      <!-- Empty State -->\n      <div *ngIf=\"!isLoading && products.length === 0\" class=\"empty-state\">\n        <div class=\"empty-icon\">\n          <i class=\"fas fa-box-open\"></i>\n        </div>\n        <h3>No New Arrivals</h3>\n        <p>Check back soon for the latest fashion!</p>\n        <button class=\"retry-btn\" (click)=\"loadProducts()\">\n          <i class=\"fas fa-refresh\"></i>\n          Try Again\n        </button>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./new-arrivals.component.scss']\n})\nexport class NewArrivalsComponent implements OnInit, OnDestroy {\n  @Input() maxProducts: number = 8;\n  @Input() showHeader: boolean = true;\n  @Input() showLoadMore: boolean = true;\n  \n  products: NewArrival[] = [];\n  isLoading: boolean = true;\n  isLoadingMore: boolean = false;\n  hasMoreProducts: boolean = true;\n  activeTimeFilter: string = 'all';\n  wishlistItems: Set<string> = new Set();\n  currentPage: number = 1;\n  \n  timeFilters = [\n    { label: 'All Time', value: 'all' },\n    { label: 'Today', value: 'today' },\n    { label: 'This Week', value: 'week' },\n    { label: 'This Month', value: 'month' }\n  ];\n  \n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private shopDataService: ShopDataService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadProducts();\n    this.loadWishlist();\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadProducts(): void {\n    this.isLoading = true;\n    this.currentPage = 1;\n    \n    this.shopDataService.loadNewArrivals(this.maxProducts)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (products) => {\n          this.products = products;\n          this.hasMoreProducts = products.length === this.maxProducts;\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading new arrivals:', error);\n          this.isLoading = false;\n        }\n      });\n  }\n\n  loadMoreProducts(): void {\n    if (this.isLoadingMore || !this.hasMoreProducts) return;\n    \n    this.isLoadingMore = true;\n    this.currentPage++;\n    \n    // In a real implementation, this would load the next page\n    setTimeout(() => {\n      // Mock loading more products\n      this.isLoadingMore = false;\n      this.hasMoreProducts = false; // For demo purposes\n    }, 1000);\n  }\n\n  setActiveTimeFilter(filter: string): void {\n    this.activeTimeFilter = filter;\n    this.loadProducts();\n  }\n\n  navigateToProduct(product: NewArrival): void {\n    this.trackProductClick(product);\n    this.router.navigate(['/product', product._id]);\n  }\n\n  viewAllNewArrivals(): void {\n    this.router.navigate(['/shop/new-arrivals']);\n  }\n\n  toggleWishlist(product: NewArrival): void {\n    if (this.wishlistItems.has(product._id)) {\n      this.wishlistItems.delete(product._id);\n    } else {\n      this.wishlistItems.add(product._id);\n    }\n    this.saveWishlist();\n  }\n\n  isInWishlist(productId: string): boolean {\n    return this.wishlistItems.has(productId);\n  }\n\n  quickView(product: NewArrival): void {\n    console.log('Quick view:', product);\n  }\n\n  addToCart(product: NewArrival): void {\n    if (product.availability.status !== 'in-stock') {\n      return;\n    }\n    \n    console.log('Add to cart:', product);\n    this.showAddToCartSuccess(product);\n  }\n\n  getProductImage(product: NewArrival): string {\n    const primaryImage = product.images.find(img => img.isPrimary);\n    return primaryImage ? primaryImage.url : product.images[0]?.url || '';\n  }\n\n  getStarArray(rating: number): number[] {\n    return Array(5).fill(0).map((_, i) => i + 1);\n  }\n\n  getArrivalDateText(dateString: string): string {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now.getTime() - date.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 1) {\n      return 'Today';\n    } else if (diffDays <= 7) {\n      return `${diffDays} days ago`;\n    } else if (diffDays <= 30) {\n      const weeks = Math.floor(diffDays / 7);\n      return weeks === 1 ? '1 week ago' : `${weeks} weeks ago`;\n    } else {\n      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });\n    }\n  }\n\n  onImageError(event: any): void {\n    event.target.src = 'https://via.placeholder.com/300x400/f0f0f0/666?text=Product+Image';\n  }\n\n  trackByProductId(index: number, product: NewArrival): string {\n    return product._id;\n  }\n\n  private trackProductClick(product: NewArrival): void {\n    if (typeof (window as any).gtag !== 'undefined') {\n      (window as any).gtag('event', 'product_click', {\n        product_name: product.name,\n        product_id: product._id,\n        product_brand: product.brand,\n        product_category: product.category,\n        event_category: 'new_arrivals'\n      });\n    }\n  }\n\n  private loadWishlist(): void {\n    const stored = localStorage.getItem('dfashion_wishlist');\n    if (stored) {\n      try {\n        const wishlist = JSON.parse(stored);\n        this.wishlistItems = new Set(wishlist);\n      } catch (e) {\n        console.warn('Failed to load wishlist');\n      }\n    }\n  }\n\n  private saveWishlist(): void {\n    localStorage.setItem('dfashion_wishlist', JSON.stringify(Array.from(this.wishlistItems)));\n  }\n\n  private showAddToCartSuccess(product: NewArrival): void {\n    console.log(`${product.name} added to cart!`);\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;IAoB7BC,EAAA,CAAAC,cAAA,iBAI8C;IAA5CD,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAA,MAAAC,SAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,mBAAA,CAAAP,SAAA,CAAAQ,KAAA,CAAiC;IAAA,EAAC;IAC3CZ,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;IAHPd,EAAA,CAAAe,WAAA,WAAAP,MAAA,CAAAQ,gBAAA,KAAAZ,SAAA,CAAAQ,KAAA,CAAkD;IAElDZ,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAd,SAAA,CAAAe,KAAA,MACF;;;;;IAYFnB,EAAA,CAAAC,cAAA,cAA0E;IACxED,EAAA,CAAAoB,SAAA,cAAkC;IAClCpB,EAAA,CAAAC,cAAA,cAA8B;IAG5BD,EAFA,CAAAoB,SAAA,cAAiC,cACM,cACN;IAErCpB,EADE,CAAAc,YAAA,EAAM,EACF;;;;;IARRd,EADF,CAAAC,cAAA,cAAiD,cACpB;IACzBD,EAAA,CAAAqB,UAAA,IAAAC,0CAAA,kBAA0E;IAS9EtB,EADE,CAAAc,YAAA,EAAM,EACF;;;IAToBd,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAuB,UAAA,YAAAvB,EAAA,CAAAwB,eAAA,IAAAC,GAAA,EAAoB;;;;;IAsCxCzB,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;IADJd,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAQ,UAAA,CAAAC,OAAA,CAAAC,kBAAA,WACF;;;;;IAuCI5B,EAAA,CAAAoB,SAAA,YAEuD;;;;;IAApDpB,EAAA,CAAAe,WAAA,WAAAc,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,CAA+C;;;;;IAHpD/B,EADF,CAAAC,cAAA,cAA6D,cACxC;IACjBD,EAAA,CAAAqB,UAAA,IAAAW,qDAAA,gBAEmD;IACrDhC,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAa,MAAA,GAA4B;IACxDb,EADwD,CAAAc,YAAA,EAAO,EACzD;;;;;IALkBd,EAAA,CAAAiB,SAAA,GAAuC;IAAvCjB,EAAA,CAAAuB,UAAA,YAAAf,MAAA,CAAAyB,YAAA,CAAAP,UAAA,CAAAI,MAAA,CAAAC,OAAA,EAAuC;IAInC/B,EAAA,CAAAiB,SAAA,GAA4B;IAA5BjB,EAAA,CAAAkB,kBAAA,MAAAQ,UAAA,CAAAI,MAAA,CAAAI,KAAA,MAA4B;;;;;IAMtDlC,EAAA,CAAAC,cAAA,eAA4E;IAC1ED,EAAA,CAAAa,MAAA,GACF;;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;IADLd,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAkB,kBAAA,YAAAlB,EAAA,CAAAmC,WAAA,OAAAT,UAAA,CAAAC,OAAA,CAAAS,GAAA,gBACF;;;;;IAKApC,EAAA,CAAAC,cAAA,WAAyD;IACvDD,EAAA,CAAAoB,SAAA,YAAmC;IACnCpB,EAAA,CAAAa,MAAA,iBACF;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;;IACPd,EAAA,CAAAC,cAAA,WAA0D;IACxDD,EAAA,CAAAoB,SAAA,YAA2C;IAC3CpB,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;;IACPd,EAAA,CAAAC,cAAA,WAA6D;IAC3DD,EAAA,CAAAoB,SAAA,YAAmC;IACnCpB,EAAA,CAAAa,MAAA,qBACF;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;;;IA9Fbd,EAAA,CAAAC,cAAA,cAO+C;IAA7CD,EAJA,CAAAE,UAAA,mBAAAmC,gEAAA;MAAA,MAAAX,UAAA,GAAA1B,EAAA,CAAAK,aAAA,CAAAiC,GAAA,EAAA/B,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA+B,iBAAA,CAAAb,UAAA,CAA0B;IAAA,EAAC,2BAAAc,wEAAA;MAAA,MAAAd,UAAA,GAAA1B,EAAA,CAAAK,aAAA,CAAAiC,GAAA,EAAA/B,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAGnBF,MAAA,CAAA+B,iBAAA,CAAAb,UAAA,CAA0B;IAAA,EAAC,2BAAAe,wEAAA;MAAA,MAAAf,UAAA,GAAA1B,EAAA,CAAAK,aAAA,CAAAiC,GAAA,EAAA/B,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAC3BF,MAAA,CAAA+B,iBAAA,CAAAb,UAAA,CAA0B;IAAA,EAAC;IAI1C1B,EADF,CAAAC,cAAA,cAAqC,cAMF;IAA/BD,EAAA,CAAAE,UAAA,mBAAAwC,gEAAAC,MAAA;MAAA3C,EAAA,CAAAK,aAAA,CAAAiC,GAAA;MAAA,MAAA9B,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAoC,YAAA,CAAAD,MAAA,CAAoB;IAAA,EAAC;IALhC3C,EAAA,CAAAc,YAAA,EAKiC;IAGjCd,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAoB,SAAA,WAA+B;IAC/BpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAa,MAAA,UAAG;IACXb,EADW,CAAAc,YAAA,EAAO,EACZ;IAGNd,EAAA,CAAAqB,UAAA,IAAAwB,gDAAA,kBAA2E;IAK3E7C,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAM;IAIJd,EADF,CAAAC,cAAA,eAA2B,kBAIe;IADtCD,EAAA,CAAAE,UAAA,mBAAA4C,oEAAAH,MAAA;MAAA,MAAAjB,UAAA,GAAA1B,EAAA,CAAAK,aAAA,CAAAiC,GAAA,EAAA/B,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAASD,MAAA,CAAAuC,cAAA,CAAArB,UAAA,CAAuB;MAAA,OAAA1B,EAAA,CAAAU,WAAA,CAAEiC,MAAA,CAAAK,eAAA,EAAwB;IAAA,EAAC;IAE3DhD,EAAA,CAAAoB,SAAA,aAA2E;IAC7EpB,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAGmC;IADjCD,EAAA,CAAAE,UAAA,mBAAA+C,oEAAAN,MAAA;MAAA,MAAAjB,UAAA,GAAA1B,EAAA,CAAAK,aAAA,CAAAiC,GAAA,EAAA/B,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAASD,MAAA,CAAA0C,SAAA,CAAAxB,UAAA,CAAkB;MAAA,OAAA1B,EAAA,CAAAU,WAAA,CAAEiC,MAAA,CAAAK,eAAA,EAAwB;IAAA,EAAC;IAEtDhD,EAAA,CAAAoB,SAAA,aAA0B;IAC5BpB,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAI0D;IAFxDD,EAAA,CAAAE,UAAA,mBAAAiD,oEAAAR,MAAA;MAAA,MAAAjB,UAAA,GAAA1B,EAAA,CAAAK,aAAA,CAAAiC,GAAA,EAAA/B,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAASD,MAAA,CAAA4C,SAAA,CAAA1B,UAAA,CAAkB;MAAA,OAAA1B,EAAA,CAAAU,WAAA,CAAEiC,MAAA,CAAAK,eAAA,EAAwB;IAAA,EAAC;IAGtDhD,EAAA,CAAAoB,SAAA,aAAoC;IAG1CpB,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;IAIJd,EADF,CAAAC,cAAA,eAA0B,eACG;IAAAD,EAAA,CAAAa,MAAA,IAAmB;IAAAb,EAAA,CAAAc,YAAA,EAAM;IACpDd,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAa,MAAA,IAAkB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAGhDd,EAAA,CAAAqB,UAAA,KAAAgC,iDAAA,kBAA6D;IAW3DrD,EADF,CAAAC,cAAA,eAA6B,gBACC;IAAAD,EAAA,CAAAa,MAAA,IAAoD;;IAAAb,EAAA,CAAAc,YAAA,EAAO;IACvFd,EAAA,CAAAqB,UAAA,KAAAiC,kDAAA,mBAA4E;IAG9EtD,EAAA,CAAAc,YAAA,EAAM;IAGNd,EAAA,CAAAC,cAAA,eAAgE;IAS9DD,EARA,CAAAqB,UAAA,KAAAkC,kDAAA,mBAAyD,KAAAC,kDAAA,mBAIC,KAAAC,kDAAA,mBAIG;IAMnEzD,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;;;;;;IArFAd,EAAA,CAAAiB,SAAA,GAAgC;IAChCjB,EADA,CAAAuB,UAAA,QAAAf,MAAA,CAAAkD,eAAA,CAAAhC,UAAA,GAAA1B,EAAA,CAAA2D,aAAA,CAAgC,QAAAjC,UAAA,CAAAkC,IAAA,CACZ;IAYO5D,EAAA,CAAAiB,SAAA,GAA4C;IAA5CjB,EAAA,CAAAuB,UAAA,SAAAG,UAAA,CAAAC,OAAA,CAAAC,kBAAA,KAA4C;IAMvE5B,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAV,MAAA,CAAAqD,kBAAA,CAAAnC,UAAA,CAAAoC,SAAA,OACF;IAOI9D,EAAA,CAAAiB,SAAA,GAAqC;;IACbjB,EAAA,CAAAiB,SAAA,EAA8C;IAA9CjB,EAAA,CAAAe,WAAA,eAAAP,MAAA,CAAAuD,YAAA,CAAArC,UAAA,CAAAsC,GAAA,EAA8C;IAKtEhE,EAAA,CAAAiB,SAAA,EAAgC;;IAOhCjB,EAAA,CAAAiB,SAAA,GAAuD;IAAvDjB,EAAA,CAAAuB,UAAA,aAAAG,UAAA,CAAAuC,YAAA,CAAAC,MAAA,gBAAuD;;IAQhClE,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAmE,iBAAA,CAAAzC,UAAA,CAAA0C,KAAA,CAAmB;IACrBpE,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAmE,iBAAA,CAAAzC,UAAA,CAAAkC,IAAA,CAAkB;IAGd5D,EAAA,CAAAiB,SAAA,EAA8B;IAA9BjB,EAAA,CAAAuB,UAAA,SAAAG,UAAA,CAAAI,MAAA,CAAAI,KAAA,KAA8B;IAW7BlC,EAAA,CAAAiB,SAAA,GAAoD;IAApDjB,EAAA,CAAAkB,kBAAA,WAAAlB,EAAA,CAAAmC,WAAA,SAAAT,UAAA,CAAAC,OAAA,CAAA0C,YAAA,eAAoD;IAClDrE,EAAA,CAAAiB,SAAA,GAA4C;IAA5CjB,EAAA,CAAAuB,UAAA,SAAAG,UAAA,CAAAC,OAAA,CAAAC,kBAAA,KAA4C;IAMlD5B,EAAA,CAAAiB,SAAA,EAAqC;IAArCjB,EAAA,CAAAsE,UAAA,CAAA5C,UAAA,CAAAuC,YAAA,CAAAC,MAAA,CAAqC;IACtDlE,EAAA,CAAAiB,SAAA,EAAgD;IAAhDjB,EAAA,CAAAuB,UAAA,SAAAG,UAAA,CAAAuC,YAAA,CAAAC,MAAA,gBAAgD;IAIhDlE,EAAA,CAAAiB,SAAA,EAAiD;IAAjDjB,EAAA,CAAAuB,UAAA,SAAAG,UAAA,CAAAuC,YAAA,CAAAC,MAAA,iBAAiD;IAIjDlE,EAAA,CAAAiB,SAAA,EAAoD;IAApDjB,EAAA,CAAAuB,UAAA,SAAAG,UAAA,CAAAuC,YAAA,CAAAC,MAAA,oBAAoD;;;;;IA5FnElE,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAqB,UAAA,IAAAkD,0CAAA,oBAO+C;IA2FjDvE,EAAA,CAAAc,YAAA,EAAM;;;;IAjGkBd,EAAA,CAAAiB,SAAA,EAAa;IAAAjB,EAAb,CAAAuB,UAAA,YAAAf,MAAA,CAAAgE,QAAA,CAAa,iBAAAhE,MAAA,CAAAiE,gBAAA,CAAyB;;;;;IAsG1DzE,EAAA,CAAAoB,SAAA,YAAkD;;;;;IAClDpB,EAAA,CAAAoB,SAAA,YAA4D;;;;;;IAF9DpB,EADF,CAAAC,cAAA,cAA8F,iBACN;IAAxDD,EAAA,CAAAE,UAAA,mBAAAwE,6DAAA;MAAA1E,EAAA,CAAAK,aAAA,CAAAsE,GAAA;MAAA,MAAAnE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAoE,gBAAA,EAAkB;IAAA,EAAC;IAExD5E,EADA,CAAAqB,UAAA,IAAAwD,wCAAA,gBAA8C,IAAAC,wCAAA,gBACU;IACxD9E,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAa,MAAA,GAAgD;IAE1Db,EAF0D,CAAAc,YAAA,EAAO,EACtD,EACL;;;;IALuDd,EAAA,CAAAiB,SAAA,EAA0B;IAA1BjB,EAAA,CAAAuB,UAAA,aAAAf,MAAA,CAAAuE,aAAA,CAA0B;IAC3D/E,EAAA,CAAAiB,SAAA,EAAoB;IAApBjB,EAAA,CAAAuB,UAAA,UAAAf,MAAA,CAAAuE,aAAA,CAAoB;IACT/E,EAAA,CAAAiB,SAAA,EAAmB;IAAnBjB,EAAA,CAAAuB,UAAA,SAAAf,MAAA,CAAAuE,aAAA,CAAmB;IAChD/E,EAAA,CAAAiB,SAAA,GAAgD;IAAhDjB,EAAA,CAAAmE,iBAAA,CAAA3D,MAAA,CAAAuE,aAAA,8BAAgD;;;;;;IAMxD/E,EADF,CAAAC,cAAA,cAAqE,cAC3C;IACtBD,EAAA,CAAAoB,SAAA,YAA+B;IACjCpB,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,sBAAe;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACxBd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAa,MAAA,8CAAuC;IAAAb,EAAA,CAAAc,YAAA,EAAI;IAC9Cd,EAAA,CAAAC,cAAA,iBAAmD;IAAzBD,EAAA,CAAAE,UAAA,mBAAA8E,6DAAA;MAAAhF,EAAA,CAAAK,aAAA,CAAA4E,GAAA;MAAA,MAAAzE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA0E,YAAA,EAAc;IAAA,EAAC;IAChDlF,EAAA,CAAAoB,SAAA,YAA8B;IAC9BpB,EAAA,CAAAa,MAAA,kBACF;IACFb,EADE,CAAAc,YAAA,EAAS,EACL;;;AAKZ,OAAM,MAAOqE,oBAAoB;EAsB/BC,YACUC,eAAgC,EAChCC,MAAc;IADd,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAvBP,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,UAAU,GAAY,IAAI;IAC1B,KAAAC,YAAY,GAAY,IAAI;IAErC,KAAAjB,QAAQ,GAAiB,EAAE;IAC3B,KAAAkB,SAAS,GAAY,IAAI;IACzB,KAAAX,aAAa,GAAY,KAAK;IAC9B,KAAAY,eAAe,GAAY,IAAI;IAC/B,KAAA3E,gBAAgB,GAAW,KAAK;IAChC,KAAA4E,aAAa,GAAgB,IAAIC,GAAG,EAAE;IACtC,KAAAC,WAAW,GAAW,CAAC;IAEvB,KAAAC,WAAW,GAAG,CACZ;MAAE5E,KAAK,EAAE,UAAU;MAAEP,KAAK,EAAE;IAAK,CAAE,EACnC;MAAEO,KAAK,EAAE,OAAO;MAAEP,KAAK,EAAE;IAAO,CAAE,EAClC;MAAEO,KAAK,EAAE,WAAW;MAAEP,KAAK,EAAE;IAAM,CAAE,EACrC;MAAEO,KAAK,EAAE,YAAY;MAAEP,KAAK,EAAE;IAAO,CAAE,CACxC;IAEO,KAAAoF,QAAQ,GAAG,IAAIlG,OAAO,EAAQ;EAKnC;EAEHmG,QAAQA,CAAA;IACN,IAAI,CAACf,YAAY,EAAE;IACnB,IAAI,CAACgB,YAAY,EAAE;EACrB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACH,QAAQ,CAACI,IAAI,EAAE;IACpB,IAAI,CAACJ,QAAQ,CAACK,QAAQ,EAAE;EAC1B;EAEAnB,YAAYA,CAAA;IACV,IAAI,CAACQ,SAAS,GAAG,IAAI;IACrB,IAAI,CAACI,WAAW,GAAG,CAAC;IAEpB,IAAI,CAACT,eAAe,CAACiB,eAAe,CAAC,IAAI,CAACf,WAAW,CAAC,CACnDgB,IAAI,CAACxG,SAAS,CAAC,IAAI,CAACiG,QAAQ,CAAC,CAAC,CAC9BQ,SAAS,CAAC;MACTJ,IAAI,EAAG5B,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACmB,eAAe,GAAGnB,QAAQ,CAACiC,MAAM,KAAK,IAAI,CAAClB,WAAW;QAC3D,IAAI,CAACG,SAAS,GAAG,KAAK;MACxB,CAAC;MACDgB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAAChB,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEAd,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACG,aAAa,IAAI,CAAC,IAAI,CAACY,eAAe,EAAE;IAEjD,IAAI,CAACZ,aAAa,GAAG,IAAI;IACzB,IAAI,CAACe,WAAW,EAAE;IAElB;IACAc,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAAC7B,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACY,eAAe,GAAG,KAAK,CAAC,CAAC;IAChC,CAAC,EAAE,IAAI,CAAC;EACV;EAEAhF,mBAAmBA,CAACkG,MAAc;IAChC,IAAI,CAAC7F,gBAAgB,GAAG6F,MAAM;IAC9B,IAAI,CAAC3B,YAAY,EAAE;EACrB;EAEA3C,iBAAiBA,CAACuE,OAAmB;IACnC,IAAI,CAACC,iBAAiB,CAACD,OAAO,CAAC;IAC/B,IAAI,CAACxB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,UAAU,EAAEF,OAAO,CAAC9C,GAAG,CAAC,CAAC;EACjD;EAEAiD,kBAAkBA,CAAA;IAChB,IAAI,CAAC3B,MAAM,CAAC0B,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEAjE,cAAcA,CAAC+D,OAAmB;IAChC,IAAI,IAAI,CAAClB,aAAa,CAACsB,GAAG,CAACJ,OAAO,CAAC9C,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC4B,aAAa,CAACuB,MAAM,CAACL,OAAO,CAAC9C,GAAG,CAAC;KACvC,MAAM;MACL,IAAI,CAAC4B,aAAa,CAACwB,GAAG,CAACN,OAAO,CAAC9C,GAAG,CAAC;;IAErC,IAAI,CAACqD,YAAY,EAAE;EACrB;EAEAtD,YAAYA,CAACuD,SAAiB;IAC5B,OAAO,IAAI,CAAC1B,aAAa,CAACsB,GAAG,CAACI,SAAS,CAAC;EAC1C;EAEApE,SAASA,CAAC4D,OAAmB;IAC3BH,OAAO,CAACY,GAAG,CAAC,aAAa,EAAET,OAAO,CAAC;EACrC;EAEA1D,SAASA,CAAC0D,OAAmB;IAC3B,IAAIA,OAAO,CAAC7C,YAAY,CAACC,MAAM,KAAK,UAAU,EAAE;MAC9C;;IAGFyC,OAAO,CAACY,GAAG,CAAC,cAAc,EAAET,OAAO,CAAC;IACpC,IAAI,CAACU,oBAAoB,CAACV,OAAO,CAAC;EACpC;EAEApD,eAAeA,CAACoD,OAAmB;IACjC,MAAMW,YAAY,GAAGX,OAAO,CAACY,MAAM,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,SAAS,CAAC;IAC9D,OAAOJ,YAAY,GAAGA,YAAY,CAACK,GAAG,GAAGhB,OAAO,CAACY,MAAM,CAAC,CAAC,CAAC,EAAEI,GAAG,IAAI,EAAE;EACvE;EAEA7F,YAAYA,CAACH,MAAc;IACzB,OAAOiG,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;EAC9C;EAEAtE,kBAAkBA,CAACuE,UAAkB;IACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,CAACI,OAAO,EAAE,GAAGN,IAAI,CAACM,OAAO,EAAE,CAAC;IACzD,MAAMC,QAAQ,GAAGH,IAAI,CAACI,IAAI,CAACL,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAII,QAAQ,KAAK,CAAC,EAAE;MAClB,OAAO,OAAO;KACf,MAAM,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACxB,OAAO,GAAGA,QAAQ,WAAW;KAC9B,MAAM,IAAIA,QAAQ,IAAI,EAAE,EAAE;MACzB,MAAME,KAAK,GAAGL,IAAI,CAACM,KAAK,CAACH,QAAQ,GAAG,CAAC,CAAC;MACtC,OAAOE,KAAK,KAAK,CAAC,GAAG,YAAY,GAAG,GAAGA,KAAK,YAAY;KACzD,MAAM;MACL,OAAOT,IAAI,CAACW,kBAAkB,CAAC,OAAO,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAS,CAAE,CAAC;;EAE/E;EAEAtG,YAAYA,CAACuG,KAAU;IACrBA,KAAK,CAACC,MAAM,CAACC,GAAG,GAAG,mEAAmE;EACxF;EAEA5E,gBAAgBA,CAAC6E,KAAa,EAAExC,OAAmB;IACjD,OAAOA,OAAO,CAAC9C,GAAG;EACpB;EAEQ+C,iBAAiBA,CAACD,OAAmB;IAC3C,IAAI,OAAQyC,MAAc,CAACC,IAAI,KAAK,WAAW,EAAE;MAC9CD,MAAc,CAACC,IAAI,CAAC,OAAO,EAAE,eAAe,EAAE;QAC7CC,YAAY,EAAE3C,OAAO,CAAClD,IAAI;QAC1B8F,UAAU,EAAE5C,OAAO,CAAC9C,GAAG;QACvB2F,aAAa,EAAE7C,OAAO,CAAC1C,KAAK;QAC5BwF,gBAAgB,EAAE9C,OAAO,CAAC+C,QAAQ;QAClCC,cAAc,EAAE;OACjB,CAAC;;EAEN;EAEQ5D,YAAYA,CAAA;IAClB,MAAM6D,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;IACxD,IAAIF,MAAM,EAAE;MACV,IAAI;QACF,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,MAAM,CAAC;QACnC,IAAI,CAACnE,aAAa,GAAG,IAAIC,GAAG,CAACqE,QAAQ,CAAC;OACvC,CAAC,OAAOG,CAAC,EAAE;QACV1D,OAAO,CAAC2D,IAAI,CAAC,yBAAyB,CAAC;;;EAG7C;EAEQjD,YAAYA,CAAA;IAClB2C,YAAY,CAACO,OAAO,CAAC,mBAAmB,EAAEJ,IAAI,CAACK,SAAS,CAACzC,KAAK,CAAC0C,IAAI,CAAC,IAAI,CAAC7E,aAAa,CAAC,CAAC,CAAC;EAC3F;EAEQ4B,oBAAoBA,CAACV,OAAmB;IAC9CH,OAAO,CAACY,GAAG,CAAC,GAAGT,OAAO,CAAClD,IAAI,iBAAiB,CAAC;EAC/C;;;uBA/KWuB,oBAAoB,EAAAnF,EAAA,CAAA0K,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAA5K,EAAA,CAAA0K,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApB3F,oBAAoB;MAAA4F,SAAA;MAAAC,MAAA;QAAAzF,WAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;MAAAwF,UAAA;MAAAC,QAAA,GAAAlL,EAAA,CAAAmL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApKvBzL,EAJN,CAAAC,cAAA,aAAkC,aAEJ,aACE,YACA;UACxBD,EAAA,CAAAoB,SAAA,WAA+B;UAC/BpB,EAAA,CAAAa,MAAA,qBACF;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACLd,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAa,MAAA,+BAAwB;UACtDb,EADsD,CAAAc,YAAA,EAAI,EACpD;UAEJd,EADF,CAAAC,cAAA,aAA4B,aACA;UACxBD,EAAA,CAAAqB,UAAA,KAAAsK,uCAAA,oBAI8C;UAGhD3L,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAC,cAAA,iBAA4D;UAA/BD,EAAA,CAAAE,UAAA,mBAAA0L,uDAAA;YAAA,OAASF,GAAA,CAAAzE,kBAAA,EAAoB;UAAA,EAAC;UACzDjH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAa,MAAA,gBAAQ;UAAAb,EAAA,CAAAc,YAAA,EAAO;UACrBd,EAAA,CAAAoB,SAAA,aAAkC;UAGxCpB,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;UAgINd,EA7HA,CAAAqB,UAAA,KAAAwK,oCAAA,kBAAiD,KAAAC,oCAAA,kBAcoB,KAAAC,oCAAA,kBAsGyB,KAAAC,oCAAA,mBASzB;UAWvEhM,EAAA,CAAAc,YAAA,EAAM;;;UAvJuBd,EAAA,CAAAiB,SAAA,IAAc;UAAdjB,EAAA,CAAAuB,UAAA,YAAAmK,GAAA,CAAA3F,WAAA,CAAc;UAenC/F,EAAA,CAAAiB,SAAA,GAAe;UAAfjB,EAAA,CAAAuB,UAAA,SAAAmK,GAAA,CAAAhG,SAAA,CAAe;UAcf1F,EAAA,CAAAiB,SAAA,EAAuC;UAAvCjB,EAAA,CAAAuB,UAAA,UAAAmK,GAAA,CAAAhG,SAAA,IAAAgG,GAAA,CAAAlH,QAAA,CAAAiC,MAAA,KAAuC;UAsGXzG,EAAA,CAAAiB,SAAA,EAA0D;UAA1DjB,EAAA,CAAAuB,UAAA,UAAAmK,GAAA,CAAAhG,SAAA,IAAAgG,GAAA,CAAAlH,QAAA,CAAAiC,MAAA,QAAAiF,GAAA,CAAA/F,eAAA,CAA0D;UAStF3F,EAAA,CAAAiB,SAAA,EAAyC;UAAzCjB,EAAA,CAAAuB,UAAA,UAAAmK,GAAA,CAAAhG,SAAA,IAAAgG,GAAA,CAAAlH,QAAA,CAAAiC,MAAA,OAAyC;;;qBA3JzC5G,YAAY,EAAAoM,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}