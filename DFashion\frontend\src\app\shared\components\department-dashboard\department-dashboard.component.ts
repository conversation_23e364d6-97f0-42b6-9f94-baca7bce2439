import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { RoleManagementService, UserRole, Department } from '../../../core/services/role-management.service';
import { PermissionManagementService } from '../../../core/services/permission-management.service';

export interface DashboardWidget {
  id: string;
  title: string;
  type: 'metric' | 'chart' | 'list' | 'progress' | 'calendar' | 'activity';
  size: 'small' | 'medium' | 'large' | 'full';
  data: any;
  permission?: string;
  refreshInterval?: number;
}

export interface DepartmentConfig {
  department: Department;
  name: string;
  color: string;
  icon: string;
  widgets: DashboardWidget[];
  quickActions: QuickAction[];
}

export interface QuickAction {
  id: string;
  label: string;
  icon: string;
  color: string;
  action: string;
  permission?: string;
}

@Component({
  selector: 'app-department-dashboard',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="department-dashboard" [attr.data-department]="department">
      <!-- Department Header -->
      <div class="department-header" [style.background]="getDepartmentGradient()">
        <div class="header-content">
          <div class="department-icon">
            <i [class]="departmentConfig?.icon"></i>
          </div>
          <div class="header-text">
            <h1>{{ departmentConfig?.name }} Dashboard</h1>
            <p>{{ getRoleDisplayName() }} - {{ getWelcomeMessage() }}</p>
          </div>
          <div class="header-stats">
            <div class="stat-item" *ngFor="let stat of getHeaderStats()">
              <span class="stat-value">{{ stat.value }}</span>
              <span class="stat-label">{{ stat.label }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions" *ngIf="getAvailableQuickActions().length > 0">
        <h3>Quick Actions</h3>
        <div class="actions-grid">
          <button 
            *ngFor="let action of getAvailableQuickActions()"
            class="action-btn"
            [style.background]="action.color"
            (click)="handleQuickAction(action.action)"
            [title]="action.label">
            <i [class]="action.icon"></i>
            <span>{{ action.label }}</span>
          </button>
        </div>
      </div>

      <!-- Dashboard Widgets -->
      <div class="dashboard-widgets">
        <div class="widgets-grid">
          <div 
            *ngFor="let widget of getAvailableWidgets()"
            class="widget-container"
            [attr.data-size]="widget.size"
            [attr.data-type]="widget.type">
            
            <!-- Metric Widget -->
            <div *ngIf="widget.type === 'metric'" class="metric-widget">
              <div class="metric-header">
                <h4>{{ widget.title }}</h4>
                <i class="fas fa-chart-line"></i>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ widget.data.value }}</div>
                <div class="metric-change" [class]="widget.data.trend">
                  <i [class]="getTrendIcon(widget.data.trend)"></i>
                  <span>{{ widget.data.change }}</span>
                </div>
                <div class="metric-label">{{ widget.data.label }}</div>
              </div>
            </div>

            <!-- Chart Widget -->
            <div *ngIf="widget.type === 'chart'" class="chart-widget">
              <div class="chart-header">
                <h4>{{ widget.title }}</h4>
                <div class="chart-controls">
                  <button class="chart-btn" (click)="refreshWidget(widget.id)">
                    <i class="fas fa-sync-alt"></i>
                  </button>
                </div>
              </div>
              <div class="chart-content">
                <!-- Chart implementation would go here -->
                <div class="chart-placeholder">
                  <i class="fas fa-chart-bar"></i>
                  <p>{{ widget.title }} Chart</p>
                </div>
              </div>
            </div>

            <!-- List Widget -->
            <div *ngIf="widget.type === 'list'" class="list-widget">
              <div class="list-header">
                <h4>{{ widget.title }}</h4>
                <a href="#" class="view-all">View All</a>
              </div>
              <div class="list-content">
                <div 
                  *ngFor="let item of widget.data.items" 
                  class="list-item"
                  (click)="handleListItemClick(item)">
                  <div class="item-icon" [style.background]="item.color">
                    <i [class]="item.icon"></i>
                  </div>
                  <div class="item-content">
                    <div class="item-title">{{ item.title }}</div>
                    <div class="item-subtitle">{{ item.subtitle }}</div>
                  </div>
                  <div class="item-meta">
                    <span class="item-time">{{ item.time }}</span>
                    <span class="item-status" [class]="item.status">{{ item.statusText }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Progress Widget -->
            <div *ngIf="widget.type === 'progress'" class="progress-widget">
              <div class="progress-header">
                <h4>{{ widget.title }}</h4>
                <span class="progress-percentage">{{ widget.data.percentage }}%</span>
              </div>
              <div class="progress-content">
                <div class="progress-bar">
                  <div 
                    class="progress-fill" 
                    [style.width.%]="widget.data.percentage"
                    [style.background]="widget.data.color">
                  </div>
                </div>
                <div class="progress-details">
                  <span>{{ widget.data.current }} / {{ widget.data.target }}</span>
                  <span class="progress-label">{{ widget.data.label }}</span>
                </div>
              </div>
            </div>

            <!-- Activity Widget -->
            <div *ngIf="widget.type === 'activity'" class="activity-widget">
              <div class="activity-header">
                <h4>{{ widget.title }}</h4>
                <div class="activity-filter">
                  <select (change)="filterActivity($event)">
                    <option value="all">All Activities</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                  </select>
                </div>
              </div>
              <div class="activity-content">
                <div class="activity-timeline">
                  <div 
                    *ngFor="let activity of widget.data.activities" 
                    class="activity-item">
                    <div class="activity-time">{{ activity.time }}</div>
                    <div class="activity-dot" [style.background]="activity.color"></div>
                    <div class="activity-details">
                      <div class="activity-title">{{ activity.title }}</div>
                      <div class="activity-description">{{ activity.description }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./department-dashboard.component.scss']
})
export class DepartmentDashboardComponent implements OnInit, OnDestroy {
  @Input() department: Department | null = null;
  @Input() userRole: UserRole | null = null;
  
  departmentConfig: DepartmentConfig | null = null;
  
  private destroy$ = new Subject<void>();

  private departmentConfigs: Record<Department, DepartmentConfig> = {
    administration: {
      department: 'administration',
      name: 'Administration',
      color: '#FF6B6B',
      icon: 'fas fa-shield-alt',
      widgets: [
        {
          id: 'user_stats',
          title: 'User Statistics',
          type: 'metric',
          size: 'small',
          data: { value: '1,247', change: '+12%', trend: 'up', label: 'Total Users' }
        },
        {
          id: 'system_health',
          title: 'System Health',
          type: 'progress',
          size: 'medium',
          data: { percentage: 94, current: 94, target: 100, label: 'System Performance', color: '#4CAF50' }
        },
        {
          id: 'recent_activities',
          title: 'Recent Activities',
          type: 'activity',
          size: 'large',
          data: {
            activities: [
              { time: '2 min ago', title: 'New user registered', description: '<EMAIL>', color: '#4CAF50' },
              { time: '15 min ago', title: 'System backup completed', description: 'Daily backup successful', color: '#2196F3' }
            ]
          }
        }
      ],
      quickActions: [
        { id: 'add_user', label: 'Add User', icon: 'fas fa-user-plus', color: '#4CAF50', action: 'add_user' },
        { id: 'system_settings', label: 'System Settings', icon: 'fas fa-cogs', color: '#FF9800', action: 'system_settings' },
        { id: 'backup', label: 'Backup System', icon: 'fas fa-download', color: '#2196F3', action: 'backup' }
      ]
    },
    sales: {
      department: 'sales',
      name: 'Sales',
      color: '#45B7D1',
      icon: 'fas fa-chart-line',
      widgets: [
        {
          id: 'monthly_revenue',
          title: 'Monthly Revenue',
          type: 'metric',
          size: 'small',
          data: { value: '₹2.4M', change: '+18%', trend: 'up', label: 'This Month' }
        },
        {
          id: 'sales_target',
          title: 'Sales Target',
          type: 'progress',
          size: 'medium',
          data: { percentage: 87, current: 87, target: 100, label: 'Monthly Target', color: '#45B7D1' }
        },
        {
          id: 'top_performers',
          title: 'Top Performers',
          type: 'list',
          size: 'medium',
          data: {
            items: [
              { title: 'Rahul Sharma', subtitle: '₹450K revenue', icon: 'fas fa-trophy', color: '#FFD700', time: 'This month', status: 'top', statusText: 'Top' },
              { title: 'Priya Patel', subtitle: '₹380K revenue', icon: 'fas fa-medal', color: '#C0C0C0', time: 'This month', status: 'second', statusText: '2nd' }
            ]
          }
        }
      ],
      quickActions: [
        { id: 'add_lead', label: 'Add Lead', icon: 'fas fa-user-plus', color: '#4CAF50', action: 'add_lead' },
        { id: 'create_quote', label: 'Create Quote', icon: 'fas fa-file-invoice', color: '#FF9800', action: 'create_quote' },
        { id: 'view_pipeline', label: 'Sales Pipeline', icon: 'fas fa-funnel-dollar', color: '#2196F3', action: 'view_pipeline' }
      ]
    },
    marketing: {
      department: 'marketing',
      name: 'Marketing',
      color: '#F38BA8',
      icon: 'fas fa-bullhorn',
      widgets: [
        {
          id: 'campaign_reach',
          title: 'Campaign Reach',
          type: 'metric',
          size: 'small',
          data: { value: '1.2M', change: '+25%', trend: 'up', label: 'This Week' }
        },
        {
          id: 'engagement_rate',
          title: 'Engagement Rate',
          type: 'metric',
          size: 'small',
          data: { value: '4.8%', change: '+0.3%', trend: 'up', label: 'Average' }
        },
        {
          id: 'active_campaigns',
          title: 'Active Campaigns',
          type: 'list',
          size: 'large',
          data: {
            items: [
              { title: 'Summer Sale 2024', subtitle: 'Instagram & Facebook', icon: 'fas fa-fire', color: '#FF5722', time: '2 days left', status: 'active', statusText: 'Active' },
              { title: 'New Collection Launch', subtitle: 'Multi-platform', icon: 'fas fa-rocket', color: '#9C27B0', time: '1 week left', status: 'scheduled', statusText: 'Scheduled' }
            ]
          }
        }
      ],
      quickActions: [
        { id: 'create_campaign', label: 'Create Campaign', icon: 'fas fa-plus', color: '#4CAF50', action: 'create_campaign' },
        { id: 'content_calendar', label: 'Content Calendar', icon: 'fas fa-calendar', color: '#FF9800', action: 'content_calendar' },
        { id: 'analytics', label: 'Analytics', icon: 'fas fa-chart-bar', color: '#2196F3', action: 'analytics' }
      ]
    },
    accounting: {
      department: 'accounting',
      name: 'Accounting',
      color: '#FFD93D',
      icon: 'fas fa-calculator',
      widgets: [
        {
          id: 'monthly_profit',
          title: 'Monthly Profit',
          type: 'metric',
          size: 'small',
          data: { value: '₹580K', change: '+12%', trend: 'up', label: 'Net Profit' }
        },
        {
          id: 'pending_invoices',
          title: 'Pending Invoices',
          type: 'metric',
          size: 'small',
          data: { value: '23', change: '-5', trend: 'down', label: 'Outstanding' }
        },
        {
          id: 'expense_breakdown',
          title: 'Expense Breakdown',
          type: 'chart',
          size: 'medium',
          data: { chartType: 'pie', categories: ['Operations', 'Marketing', 'Salaries', 'Other'] }
        }
      ],
      quickActions: [
        { id: 'create_invoice', label: 'Create Invoice', icon: 'fas fa-file-invoice', color: '#4CAF50', action: 'create_invoice' },
        { id: 'expense_report', label: 'Expense Report', icon: 'fas fa-receipt', color: '#FF9800', action: 'expense_report' },
        { id: 'financial_summary', label: 'Financial Summary', icon: 'fas fa-chart-pie', color: '#2196F3', action: 'financial_summary' }
      ]
    },
    support: {
      department: 'support',
      name: 'Customer Support',
      color: '#FF8C42',
      icon: 'fas fa-headset',
      widgets: [
        {
          id: 'open_tickets',
          title: 'Open Tickets',
          type: 'metric',
          size: 'small',
          data: { value: '47', change: '-8', trend: 'down', label: 'Active Tickets' }
        },
        {
          id: 'response_time',
          title: 'Avg Response Time',
          type: 'metric',
          size: 'small',
          data: { value: '2.3h', change: '-0.5h', trend: 'down', label: 'This Week' }
        },
        {
          id: 'recent_tickets',
          title: 'Recent Tickets',
          type: 'list',
          size: 'large',
          data: {
            items: [
              { title: 'Payment Issue', subtitle: 'Customer: John Doe', icon: 'fas fa-credit-card', color: '#F44336', time: '5 min ago', status: 'urgent', statusText: 'Urgent' },
              { title: 'Product Question', subtitle: 'Customer: Jane Smith', icon: 'fas fa-question-circle', color: '#FF9800', time: '15 min ago', status: 'normal', statusText: 'Normal' }
            ]
          }
        }
      ],
      quickActions: [
        { id: 'create_ticket', label: 'Create Ticket', icon: 'fas fa-ticket-alt', color: '#4CAF50', action: 'create_ticket' },
        { id: 'knowledge_base', label: 'Knowledge Base', icon: 'fas fa-book', color: '#FF9800', action: 'knowledge_base' },
        { id: 'customer_feedback', label: 'Customer Feedback', icon: 'fas fa-comments', color: '#2196F3', action: 'customer_feedback' }
      ]
    },
    content: {
      department: 'content',
      name: 'Content Management',
      color: '#B19CD9',
      icon: 'fas fa-edit',
      widgets: [
        {
          id: 'published_content',
          title: 'Published This Month',
          type: 'metric',
          size: 'small',
          data: { value: '156', change: '+23', trend: 'up', label: 'Articles' }
        },
        {
          id: 'content_performance',
          title: 'Content Performance',
          type: 'chart',
          size: 'medium',
          data: { chartType: 'line', metric: 'engagement' }
        },
        {
          id: 'editorial_calendar',
          title: 'Editorial Calendar',
          type: 'calendar',
          size: 'large',
          data: { events: [] }
        }
      ],
      quickActions: [
        { id: 'create_article', label: 'Create Article', icon: 'fas fa-pen', color: '#4CAF50', action: 'create_article' },
        { id: 'schedule_post', label: 'Schedule Post', icon: 'fas fa-clock', color: '#FF9800', action: 'schedule_post' },
        { id: 'content_analytics', label: 'Content Analytics', icon: 'fas fa-chart-line', color: '#2196F3', action: 'content_analytics' }
      ]
    },
    vendor_management: {
      department: 'vendor_management',
      name: 'Vendor Management',
      color: '#FFB6C1',
      icon: 'fas fa-store',
      widgets: [
        {
          id: 'active_vendors',
          title: 'Active Vendors',
          type: 'metric',
          size: 'small',
          data: { value: '89', change: '+5', trend: 'up', label: 'Partners' }
        },
        {
          id: 'contract_renewals',
          title: 'Contract Renewals',
          type: 'metric',
          size: 'small',
          data: { value: '12', change: '0', trend: 'stable', label: 'Due This Month' }
        },
        {
          id: 'vendor_performance',
          title: 'Vendor Performance',
          type: 'chart',
          size: 'medium',
          data: { chartType: 'bar', metric: 'rating' }
        }
      ],
      quickActions: [
        { id: 'add_vendor', label: 'Add Vendor', icon: 'fas fa-plus', color: '#4CAF50', action: 'add_vendor' },
        { id: 'review_contracts', label: 'Review Contracts', icon: 'fas fa-file-contract', color: '#FF9800', action: 'review_contracts' },
        { id: 'vendor_reports', label: 'Vendor Reports', icon: 'fas fa-chart-bar', color: '#2196F3', action: 'vendor_reports' }
      ]
    }
  };

  constructor(
    private roleManagementService: RoleManagementService,
    private permissionManagementService: PermissionManagementService
  ) {}

  ngOnInit() {
    if (this.department) {
      this.departmentConfig = this.departmentConfigs[this.department];
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getDepartmentGradient(): string {
    if (!this.departmentConfig) return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    
    const color = this.departmentConfig.color;
    return `linear-gradient(135deg, ${color}40 0%, ${color}80 100%)`;
  }

  getRoleDisplayName(): string {
    if (!this.userRole) return '';
    return this.roleManagementService.getRoleConfig(this.userRole).displayName;
  }

  getWelcomeMessage(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  }

  getHeaderStats(): any[] {
    // Mock data - would be replaced with real data
    return [
      { value: '94%', label: 'Performance' },
      { value: '23', label: 'Active Tasks' },
      { value: '5.2K', label: 'This Month' }
    ];
  }

  getAvailableQuickActions(): QuickAction[] {
    if (!this.departmentConfig || !this.userRole) return [];
    
    return this.departmentConfig.quickActions.filter(action => {
      if (!action.permission) return true;
      const [module, actionName] = action.permission.split('.');
      return this.permissionManagementService.hasPermission(this.userRole!, module, actionName);
    });
  }

  getAvailableWidgets(): DashboardWidget[] {
    if (!this.departmentConfig || !this.userRole) return [];
    
    return this.departmentConfig.widgets.filter(widget => {
      if (!widget.permission) return true;
      const [module, action] = widget.permission.split('.');
      return this.permissionManagementService.hasPermission(this.userRole!, module, action);
    });
  }

  handleQuickAction(action: string): void {
    console.log('Quick action triggered:', action);
    // Implement action handling
  }

  handleListItemClick(item: any): void {
    console.log('List item clicked:', item);
    // Implement item click handling
  }

  refreshWidget(widgetId: string): void {
    console.log('Refreshing widget:', widgetId);
    // Implement widget refresh
  }

  filterActivity(event: any): void {
    console.log('Filtering activity:', event.target.value);
    // Implement activity filtering
  }

  getTrendIcon(trend: string): string {
    switch (trend) {
      case 'up': return 'fas fa-arrow-up';
      case 'down': return 'fas fa-arrow-down';
      default: return 'fas fa-minus';
    }
  }
}
