{"ast": null, "code": "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n  constructor(reason, description, context) {\n    super(reason);\n    this.description = description;\n    this.context = context;\n    this.type = \"TransportError\";\n  }\n}\nexport class Transport extends Emitter {\n  /**\n   * Transport abstract constructor.\n   *\n   * @param {Object} opts - options\n   * @protected\n   */\n  constructor(opts) {\n    super();\n    this.writable = false;\n    installTimerFunctions(this, opts);\n    this.opts = opts;\n    this.query = opts.query;\n    this.socket = opts.socket;\n    this.supportsBinary = !opts.forceBase64;\n  }\n  /**\n   * Emits an error.\n   *\n   * @param {String} reason\n   * @param description\n   * @param context - the error context\n   * @return {Transport} for chaining\n   * @protected\n   */\n  onError(reason, description, context) {\n    super.emitReserved(\"error\", new TransportError(reason, description, context));\n    return this;\n  }\n  /**\n   * Opens the transport.\n   */\n  open() {\n    this.readyState = \"opening\";\n    this.doOpen();\n    return this;\n  }\n  /**\n   * Closes the transport.\n   */\n  close() {\n    if (this.readyState === \"opening\" || this.readyState === \"open\") {\n      this.doClose();\n      this.onClose();\n    }\n    return this;\n  }\n  /**\n   * Sends multiple packets.\n   *\n   * @param {Array} packets\n   */\n  send(packets) {\n    if (this.readyState === \"open\") {\n      this.write(packets);\n    } else {\n      // this might happen if the transport was silently closed in the beforeunload event handler\n    }\n  }\n  /**\n   * Called upon open\n   *\n   * @protected\n   */\n  onOpen() {\n    this.readyState = \"open\";\n    this.writable = true;\n    super.emitReserved(\"open\");\n  }\n  /**\n   * Called with data.\n   *\n   * @param {String} data\n   * @protected\n   */\n  onData(data) {\n    const packet = decodePacket(data, this.socket.binaryType);\n    this.onPacket(packet);\n  }\n  /**\n   * Called with a decoded packet.\n   *\n   * @protected\n   */\n  onPacket(packet) {\n    super.emitReserved(\"packet\", packet);\n  }\n  /**\n   * Called upon close.\n   *\n   * @protected\n   */\n  onClose(details) {\n    this.readyState = \"closed\";\n    super.emitReserved(\"close\", details);\n  }\n  /**\n   * Pauses the transport, in order not to lose packets during an upgrade.\n   *\n   * @param onPause\n   */\n  pause(onPause) {}\n  createUri(schema, query = {}) {\n    return schema + \"://\" + this._hostname() + this._port() + this.opts.path + this._query(query);\n  }\n  _hostname() {\n    const hostname = this.opts.hostname;\n    return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n  }\n  _port() {\n    if (this.opts.port && (this.opts.secure && Number(this.opts.port !== 443) || !this.opts.secure && Number(this.opts.port) !== 80)) {\n      return \":\" + this.opts.port;\n    } else {\n      return \"\";\n    }\n  }\n  _query(query) {\n    const encodedQuery = encode(query);\n    return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}