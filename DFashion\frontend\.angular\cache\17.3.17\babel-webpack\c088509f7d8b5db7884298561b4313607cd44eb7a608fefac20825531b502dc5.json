{"ast": null, "code": "import { of } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/admin-auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class AdminAuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    return this.checkAuth(route, state);\n  }\n  canActivateChild(route, state) {\n    return this.checkAuth(route, state);\n  }\n  checkAuth(route, state) {\n    // Check if user is authenticated\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/admin/login'], {\n        queryParams: {\n          returnUrl: state.url\n        }\n      });\n      return of(false);\n    }\n    // Check if user can access admin panel\n    if (!this.authService.canAccessAdmin()) {\n      this.router.navigate(['/admin/login']);\n      return of(false);\n    }\n    // Check specific permission if required\n    const requiredPermission = route.data?.['permission'];\n    if (requiredPermission) {\n      const [module, action] = requiredPermission.split(':');\n      if (!this.authService.hasPermission(module, action)) {\n        // Redirect to dashboard with error message\n        this.router.navigate(['/admin/dashboard'], {\n          queryParams: {\n            error: 'insufficient_permissions'\n          }\n        });\n        return of(false);\n      }\n    }\n    // Verify token with server\n    return this.authService.verifyToken().pipe(map(() => true), catchError(() => {\n      this.router.navigate(['/admin/login'], {\n        queryParams: {\n          returnUrl: state.url,\n          error: 'session_expired'\n        }\n      });\n      return of(false);\n    }));\n  }\n  static {\n    this.ɵfac = function AdminAuthGuard_Factory(t) {\n      return new (t || AdminAuthGuard)(i0.ɵɵinject(i1.AdminAuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AdminAuthGuard,\n      factory: AdminAuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "map", "catchError", "Ad<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "route", "state", "checkAuth", "canActivateChild", "isAuthenticated", "navigate", "queryParams", "returnUrl", "url", "canAccessAdmin", "requiredPermission", "data", "module", "action", "split", "hasPermission", "error", "verifyToken", "pipe", "i0", "ɵɵinject", "i1", "AdminAuthService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\admin\\guards\\admin-auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';\nimport { Observable, of } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { AdminAuthService } from '../services/admin-auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AdminAuthGuard implements CanActivate, CanActivateChild {\n\n  constructor(\n    private authService: AdminAuthService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> | Promise<boolean> | boolean {\n    return this.checkAuth(route, state);\n  }\n\n  canActivateChild(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> | Promise<boolean> | boolean {\n    return this.checkAuth(route, state);\n  }\n\n  private checkAuth(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {\n    // Check if user is authenticated\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/admin/login'], {\n        queryParams: { returnUrl: state.url }\n      });\n      return of(false);\n    }\n\n    // Check if user can access admin panel\n    if (!this.authService.canAccessAdmin()) {\n      this.router.navigate(['/admin/login']);\n      return of(false);\n    }\n\n    // Check specific permission if required\n    const requiredPermission = route.data?.['permission'];\n    if (requiredPermission) {\n      const [module, action] = requiredPermission.split(':');\n      if (!this.authService.hasPermission(module, action)) {\n        // Redirect to dashboard with error message\n        this.router.navigate(['/admin/dashboard'], {\n          queryParams: { error: 'insufficient_permissions' }\n        });\n        return of(false);\n      }\n    }\n\n    // Verify token with server\n    return this.authService.verifyToken().pipe(\n      map(() => true),\n      catchError(() => {\n        this.router.navigate(['/admin/login'], {\n          queryParams: { returnUrl: state.url, error: 'session_expired' }\n        });\n        return of(false);\n      })\n    );\n  }\n}\n"], "mappings": "AAEA,SAAqBA,EAAE,QAAQ,MAAM;AACrC,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;;AAMhD,OAAM,MAAOC,cAAc;EAEzBC,YACUC,WAA6B,EAC7BC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAE1B,OAAO,IAAI,CAACC,SAAS,CAACF,KAAK,EAAEC,KAAK,CAAC;EACrC;EAEAE,gBAAgBA,CACdH,KAA6B,EAC7BC,KAA0B;IAE1B,OAAO,IAAI,CAACC,SAAS,CAACF,KAAK,EAAEC,KAAK,CAAC;EACrC;EAEQC,SAASA,CAACF,KAA6B,EAAEC,KAA0B;IACzE;IACA,IAAI,CAAC,IAAI,CAACJ,WAAW,CAACO,eAAe,EAAE,EAAE;MACvC,IAAI,CAACN,MAAM,CAACO,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;QACrCC,WAAW,EAAE;UAAEC,SAAS,EAAEN,KAAK,CAACO;QAAG;OACpC,CAAC;MACF,OAAOhB,EAAE,CAAC,KAAK,CAAC;;IAGlB;IACA,IAAI,CAAC,IAAI,CAACK,WAAW,CAACY,cAAc,EAAE,EAAE;MACtC,IAAI,CAACX,MAAM,CAACO,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;MACtC,OAAOb,EAAE,CAAC,KAAK,CAAC;;IAGlB;IACA,MAAMkB,kBAAkB,GAAGV,KAAK,CAACW,IAAI,GAAG,YAAY,CAAC;IACrD,IAAID,kBAAkB,EAAE;MACtB,MAAM,CAACE,MAAM,EAAEC,MAAM,CAAC,GAAGH,kBAAkB,CAACI,KAAK,CAAC,GAAG,CAAC;MACtD,IAAI,CAAC,IAAI,CAACjB,WAAW,CAACkB,aAAa,CAACH,MAAM,EAAEC,MAAM,CAAC,EAAE;QACnD;QACA,IAAI,CAACf,MAAM,CAACO,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE;UACzCC,WAAW,EAAE;YAAEU,KAAK,EAAE;UAA0B;SACjD,CAAC;QACF,OAAOxB,EAAE,CAAC,KAAK,CAAC;;;IAIpB;IACA,OAAO,IAAI,CAACK,WAAW,CAACoB,WAAW,EAAE,CAACC,IAAI,CACxCzB,GAAG,CAAC,MAAM,IAAI,CAAC,EACfC,UAAU,CAAC,MAAK;MACd,IAAI,CAACI,MAAM,CAACO,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;QACrCC,WAAW,EAAE;UAAEC,SAAS,EAAEN,KAAK,CAACO,GAAG;UAAEQ,KAAK,EAAE;QAAiB;OAC9D,CAAC;MACF,OAAOxB,EAAE,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC,CACH;EACH;;;uBA3DWG,cAAc,EAAAwB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAd7B,cAAc;MAAA8B,OAAA,EAAd9B,cAAc,CAAA+B,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}