{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement, i as forceUpdate } from './index-a1a47f01.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { c as createNotchController } from './notch-controller-6bd3e0f9.js';\nimport { j as debounceEvent, i as inheritAriaAttributes, k as inheritAttributes, c as componentOnReady, h as findItemLabel } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { c as createSlotMutationController, g as getCounterText } from './input.utils-a445f677.js';\nimport { h as hostContext, c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as closeCircle, d as closeSharp } from './index-f7dc70ba.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nimport './index-a5d50daf.js';\nconst inputIosCss = \".sc-ion-input-ios-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:0.6;--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}.legacy-input.sc-ion-input-ios-h{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;background:var(--background)}.legacy-input.sc-ion-input-ios-h .native-input.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius)}ion-item.sc-ion-input-ios-h:not(.item-label):not(.item-has-modern-input),ion-item:not(.item-label):not(.item-has-modern-input) .sc-ion-input-ios-h{--padding-start:0}ion-item[slot=start].sc-ion-input-ios-h,ion-item [slot=start].sc-ion-input-ios-h,ion-item[slot=end].sc-ion-input-ios-h,ion-item [slot=end].sc-ion-input-ios-h{width:auto}.legacy-input.ion-color.sc-ion-input-ios-h{color:var(--ion-color-base)}.ion-color.sc-ion-input-ios-h{--highlight-color-focused:var(--ion-color-base)}.sc-ion-input-ios-h:not(.legacy-input){min-height:44px}.input-label-placement-floating.sc-ion-input-ios-h,.input-label-placement-stacked.sc-ion-input-ios-h{min-height:56px}.native-input.sc-ion-input-ios{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-ios::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-ios:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-ios::-ms-clear{display:none}.cloned-input.sc-ion-input-ios{top:0;bottom:0;position:absolute;pointer-events:none}@supports (inset-inline-start: 0){.cloned-input.sc-ion-input-ios{inset-inline-start:0}}@supports not (inset-inline-start: 0){.cloned-input.sc-ion-input-ios{left:0}[dir=rtl].sc-ion-input-ios-h .cloned-input.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h .cloned-input.sc-ion-input-ios{left:unset;right:unset;right:0}[dir=rtl].sc-ion-input-ios .cloned-input.sc-ion-input-ios{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.cloned-input.sc-ion-input-ios:dir(rtl){left:unset;right:unset;right:0}}}.cloned-input.sc-ion-input-ios:disabled{opacity:1}.legacy-input.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.input-clear-icon.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, #666666);visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{color:inherit}.input-clear-icon.sc-ion-input-ios:focus{opacity:0.5}.has-value.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{visibility:visible}.has-focus.legacy-input.sc-ion-input-ios-h{pointer-events:none}.has-focus.legacy-input.sc-ion-input-ios-h input.sc-ion-input-ios,.has-focus.legacy-input.sc-ion-input-ios-h a.sc-ion-input-ios,.has-focus.legacy-input.sc-ion-input-ios-h button.sc-ion-input-ios{pointer-events:auto}.item-label-floating.item-has-placeholder.sc-ion-input-ios-h:not(.item-has-value),.item-label-floating.item-has-placeholder:not(.item-has-value) .sc-ion-input-ios-h{opacity:0}.item-label-floating.item-has-placeholder.sc-ion-input-ios-h:not(.item-has-value).item-has-focus,.item-label-floating.item-has-placeholder:not(.item-has-value).item-has-focus .sc-ion-input-ios-h{-webkit-transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);opacity:1}.input-wrapper.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem}.has-focus.ion-valid.sc-ion-input-ios-h,.ion-touched.ion-invalid.sc-ion-input-ios-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:block;color:var(--ion-color-step-550, #737373)}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:block}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:none}.input-bottom.sc-ion-input-ios .counter.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, #737373);white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-ios-h input.sc-ion-input-ios{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-ios,.sc-ion-input-ios-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-ios,.input-outline-notch-hidden.sc-ion-input-ios{display:none}.input-wrapper.sc-ion-input-ios input.sc-ion-input-ios{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text.sc-ion-input-ios{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-ios-h input.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios,.has-value.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:1}.label-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-ios-s>[slot=start]{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-ios-s>[slot=end]{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-input-ios-h{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));font-size:inherit}.legacy-input.sc-ion-input-ios-h{--padding-top:10px;--padding-end:8px;--padding-bottom:10px;--padding-start:0}.item-label-stacked.sc-ion-input-ios-h,.item-label-stacked .sc-ion-input-ios-h,.item-label-floating.sc-ion-input-ios-h,.item-label-floating .sc-ion-input-ios-h{--padding-top:8px;--padding-bottom:8px;--padding-start:0px}.input-clear-icon.sc-ion-input-ios ion-icon.sc-ion-input-ios{width:18px;height:18px}.legacy-input.sc-ion-input-ios-h .native-input[disabled].sc-ion-input-ios,.input-disabled.sc-ion-input-ios-h{opacity:0.3}.sc-ion-input-ios-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-ios-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonInputIosStyle0 = inputIosCss;\nconst inputMdCss = \".sc-ion-input-md-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:0.6;--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}.legacy-input.sc-ion-input-md-h{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;background:var(--background)}.legacy-input.sc-ion-input-md-h .native-input.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius)}ion-item.sc-ion-input-md-h:not(.item-label):not(.item-has-modern-input),ion-item:not(.item-label):not(.item-has-modern-input) .sc-ion-input-md-h{--padding-start:0}ion-item[slot=start].sc-ion-input-md-h,ion-item [slot=start].sc-ion-input-md-h,ion-item[slot=end].sc-ion-input-md-h,ion-item [slot=end].sc-ion-input-md-h{width:auto}.legacy-input.ion-color.sc-ion-input-md-h{color:var(--ion-color-base)}.ion-color.sc-ion-input-md-h{--highlight-color-focused:var(--ion-color-base)}.sc-ion-input-md-h:not(.legacy-input){min-height:44px}.input-label-placement-floating.sc-ion-input-md-h,.input-label-placement-stacked.sc-ion-input-md-h{min-height:56px}.native-input.sc-ion-input-md{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-md::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-md:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-md::-ms-clear{display:none}.cloned-input.sc-ion-input-md{top:0;bottom:0;position:absolute;pointer-events:none}@supports (inset-inline-start: 0){.cloned-input.sc-ion-input-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.cloned-input.sc-ion-input-md{left:0}[dir=rtl].sc-ion-input-md-h .cloned-input.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h .cloned-input.sc-ion-input-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-input-md .cloned-input.sc-ion-input-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.cloned-input.sc-ion-input-md:dir(rtl){left:unset;right:unset;right:0}}}.cloned-input.sc-ion-input-md:disabled{opacity:1}.legacy-input.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.input-clear-icon.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, #666666);visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{color:inherit}.input-clear-icon.sc-ion-input-md:focus{opacity:0.5}.has-value.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{visibility:visible}.has-focus.legacy-input.sc-ion-input-md-h{pointer-events:none}.has-focus.legacy-input.sc-ion-input-md-h input.sc-ion-input-md,.has-focus.legacy-input.sc-ion-input-md-h a.sc-ion-input-md,.has-focus.legacy-input.sc-ion-input-md-h button.sc-ion-input-md{pointer-events:auto}.item-label-floating.item-has-placeholder.sc-ion-input-md-h:not(.item-has-value),.item-label-floating.item-has-placeholder:not(.item-has-value) .sc-ion-input-md-h{opacity:0}.item-label-floating.item-has-placeholder.sc-ion-input-md-h:not(.item-has-value).item-has-focus,.item-label-floating.item-has-placeholder:not(.item-has-value).item-has-focus .sc-ion-input-md-h{-webkit-transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);opacity:1}.input-wrapper.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:block;color:var(--ion-color-step-550, #737373)}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:block}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:none}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, #737373);white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-md-h input.sc-ion-input-md{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-md,.sc-ion-input-md-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-md,.input-outline-notch-hidden.sc-ion-input-md{display:none}.input-wrapper.sc-ion-input-md input.sc-ion-input-md{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text.sc-ion-input-md{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md,.has-value.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:1}.label-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-md-s>[slot=start]{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-md-s>[slot=end]{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.input-fill-solid.sc-ion-input-md-h{--background:var(--ion-color-step-50, #f2f2f2);--border-color:var(--ion-color-step-500, gray);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:var(--border-width) var(--border-style) var(--border-color)}.has-focus.input-fill-solid.ion-valid.sc-ion-input-md-h,.input-fill-solid.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-fill-solid.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}@media (any-hover: hover){.input-fill-solid.sc-ion-input-md-h:hover{--background:var(--ion-color-step-100, #e6e6e6);--border-color:var(--ion-color-step-750, #404040)}}.input-fill-solid.has-focus.sc-ion-input-md-h{--background:var(--ion-color-step-150, #d9d9d9);--border-color:var(--ion-color-step-750, #404040)}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,[dir=rtl].input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}@supports selector(:dir(rtl)){.input-fill-solid.sc-ion-input-md-h:dir(rtl) .input-wrapper.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}}.label-floating.input-fill-solid.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{max-width:calc(100% / 0.75)}.input-fill-outline.sc-ion-input-md-h{--border-color:var(--ion-color-step-300, #b3b3b3);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-outline.input-shape-round.sc-ion-input-md-h{--border-radius:28px;--padding-start:32px;--padding-end:32px}.has-focus.input-fill-outline.ion-valid.sc-ion-input-md-h,.input-fill-outline.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}@media (any-hover: hover){.input-fill-outline.sc-ion-input-md-h:hover{--border-color:var(--ion-color-step-750, #404040)}}.input-fill-outline.has-focus.sc-ion-input-md-h{--border-width:2px;--border-color:var(--highlight-color)}.input-fill-outline.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}.input-fill-outline.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:none}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{position:relative}.label-floating.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}.input-fill-outline.sc-ion-input-md-h .input-outline-container.sc-ion-input-md{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{pointer-events:none}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color)}.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{max-width:calc(100% - var(--padding-start) - var(--padding-end))}.input-fill-outline.sc-ion-input-md-h .notch-spacer.sc-ion-input-md{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none;-webkit-box-sizing:content-box;box-sizing:content-box}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:0px;border-bottom-right-radius:0px;border-bottom-left-radius:var(--border-radius);-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color);width:calc(var(--padding-start) - 4px)}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,[dir=rtl].input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,[dir=rtl] .input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md{border-top-left-radius:0px;border-top-right-radius:var(--border-radius);border-bottom-right-radius:var(--border-radius);border-bottom-left-radius:0px}@supports selector(:dir(rtl)){.input-fill-outline.sc-ion-input-md-h:dir(rtl) .input-outline-start.sc-ion-input-md{border-top-left-radius:0px;border-top-right-radius:var(--border-radius);border-bottom-right-radius:var(--border-radius);border-bottom-left-radius:0px}}.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color);border-top-left-radius:0px;border-top-right-radius:var(--border-radius);border-bottom-right-radius:var(--border-radius);border-bottom-left-radius:0px;-ms-flex-positive:1;flex-grow:1}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md,[dir=rtl].input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md,[dir=rtl] .input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:0px;border-bottom-right-radius:0px;border-bottom-left-radius:var(--border-radius)}@supports selector(:dir(rtl)){.input-fill-outline.sc-ion-input-md-h:dir(rtl) .input-outline-end.sc-ion-input-md{border-top-left-radius:var(--border-radius);border-top-right-radius:0px;border-bottom-right-radius:0px;border-bottom-left-radius:var(--border-radius)}}.label-floating.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{border-top:none}.sc-ion-input-md-h{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));font-size:inherit}.legacy-input.sc-ion-input-md-h{--padding-top:10px;--padding-end:0;--padding-bottom:10px;--padding-start:8px}.item-label-stacked.sc-ion-input-md-h,.item-label-stacked .sc-ion-input-md-h,.item-label-floating.sc-ion-input-md-h,.item-label-floating .sc-ion-input-md-h{--padding-top:8px;--padding-bottom:8px;--padding-start:0}.input-clear-icon.sc-ion-input-md ion-icon.sc-ion-input-md{width:22px;height:22px}.legacy-input.sc-ion-input-md-h .native-input[disabled].sc-ion-input-md,.input-disabled.sc-ion-input-md-h{opacity:0.38}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{letter-spacing:0.0333333333em}.input-label-placement-floating.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.has-focus.input-label-placement-floating.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.has-focus.input-label-placement-stacked.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.input-highlight.sc-ion-input-md{bottom:-1px;position:absolute;width:100%;height:2px;-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}@supports (inset-inline-start: 0){.input-highlight.sc-ion-input-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.input-highlight.sc-ion-input-md{left:0}[dir=rtl].sc-ion-input-md-h .input-highlight.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h .input-highlight.sc-ion-input-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-input-md .input-highlight.sc-ion-input-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.input-highlight.sc-ion-input-md:dir(rtl){left:unset;right:unset;right:0}}}.has-focus.sc-ion-input-md-h .input-highlight.sc-ion-input-md{-webkit-transform:scale(1);transform:scale(1)}.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{bottom:0}@supports (inset-inline-start: 0){.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{left:0}[dir=rtl].sc-ion-input-md-h -no-combinator.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md,[dir=rtl].in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md,[dir=rtl] .in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.in-item.sc-ion-input-md-h:dir(rtl) .input-highlight.sc-ion-input-md{left:unset;right:unset;right:0}}}.input-shape-round.sc-ion-input-md-h{--border-radius:16px}.sc-ion-input-md-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-md-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonInputMdStyle0 = inputMdCss;\nconst Input = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionInput = createEvent(this, \"ionInput\", 7);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.inputId = `ion-input-${inputIds++}`;\n    this.inheritedAttributes = {};\n    this.isComposing = false;\n    // This flag ensures we log the deprecation warning at most once.\n    this.hasLoggedDeprecationWarning = false;\n    /**\n     * `true` if the input was cleared as a result of the user typing\n     * with `clearOnEdit` enabled.\n     *\n     * Resets when the input loses focus.\n     */\n    this.didInputClearOnEdit = false;\n    this.onInput = ev => {\n      const input = ev.target;\n      if (input) {\n        this.value = input.value || '';\n      }\n      this.emitInputChange(ev);\n    };\n    this.onChange = ev => {\n      this.emitValueChange(ev);\n    };\n    this.onBlur = ev => {\n      this.hasFocus = false;\n      this.emitStyle();\n      if (this.focusedValue !== this.value) {\n        /**\n         * Emits the `ionChange` event when the input value\n         * is different than the value when the input was focused.\n         */\n        this.emitValueChange(ev);\n      }\n      this.didInputClearOnEdit = false;\n      this.ionBlur.emit(ev);\n    };\n    this.onFocus = ev => {\n      this.hasFocus = true;\n      this.focusedValue = this.value;\n      this.emitStyle();\n      this.ionFocus.emit(ev);\n    };\n    this.onKeydown = ev => {\n      this.checkClearOnEdit(ev);\n    };\n    this.onCompositionStart = () => {\n      this.isComposing = true;\n    };\n    this.onCompositionEnd = () => {\n      this.isComposing = false;\n    };\n    this.clearTextInput = ev => {\n      if (this.clearInput && !this.readonly && !this.disabled && ev) {\n        ev.preventDefault();\n        ev.stopPropagation();\n        // Attempt to focus input again after pressing clear button\n        this.setFocus();\n      }\n      this.value = '';\n      this.emitInputChange(ev);\n    };\n    this.hasFocus = false;\n    this.color = undefined;\n    this.accept = undefined;\n    this.autocapitalize = 'off';\n    this.autocomplete = 'off';\n    this.autocorrect = 'off';\n    this.autofocus = false;\n    this.clearInput = false;\n    this.clearOnEdit = undefined;\n    this.counter = false;\n    this.counterFormatter = undefined;\n    this.debounce = undefined;\n    this.disabled = false;\n    this.enterkeyhint = undefined;\n    this.errorText = undefined;\n    this.fill = undefined;\n    this.inputmode = undefined;\n    this.helperText = undefined;\n    this.label = undefined;\n    this.labelPlacement = 'start';\n    this.legacy = undefined;\n    this.max = undefined;\n    this.maxlength = undefined;\n    this.min = undefined;\n    this.minlength = undefined;\n    this.multiple = undefined;\n    this.name = this.inputId;\n    this.pattern = undefined;\n    this.placeholder = undefined;\n    this.readonly = false;\n    this.required = false;\n    this.shape = undefined;\n    this.spellcheck = false;\n    this.step = undefined;\n    this.size = undefined;\n    this.type = 'text';\n    this.value = '';\n  }\n  debounceChanged() {\n    const {\n      ionInput,\n      debounce,\n      originalIonInput\n    } = this;\n    /**\n     * If debounce is undefined, we have to manually revert the ionInput emitter in case\n     * debounce used to be set to a number. Otherwise, the event would stay debounced.\n     */\n    this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n  }\n  disabledChanged() {\n    this.emitStyle();\n  }\n  /**\n   * Update the item classes when the placeholder changes\n   */\n  placeholderChanged() {\n    this.emitStyle();\n  }\n  /**\n   * Update the native input element when the value changes\n   */\n  valueChanged() {\n    const nativeInput = this.nativeInput;\n    const value = this.getValue();\n    if (nativeInput && nativeInput.value !== value && !this.isComposing) {\n      /**\n       * Assigning the native input's value on attribute\n       * value change, allows `ionInput` implementations\n       * to override the control's value.\n       *\n       * Used for patterns such as input trimming (removing whitespace),\n       * or input masking.\n       */\n      nativeInput.value = value;\n    }\n    this.emitStyle();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign(Object.assign({}, inheritAriaAttributes(this.el)), inheritAttributes(this.el, ['tabindex', 'title', 'data-form-type']));\n  }\n  connectedCallback() {\n    const {\n      el\n    } = this;\n    this.legacyFormController = createLegacyFormController(el);\n    this.slotMutationController = createSlotMutationController(el, ['label', 'start', 'end'], () => forceUpdate(this));\n    this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n    this.emitStyle();\n    this.debounceChanged();\n    {\n      document.dispatchEvent(new CustomEvent('ionInputDidLoad', {\n        detail: this.el\n      }));\n    }\n  }\n  componentDidLoad() {\n    this.originalIonInput = this.ionInput;\n  }\n  componentDidRender() {\n    var _a;\n    (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n  }\n  disconnectedCallback() {\n    {\n      document.dispatchEvent(new CustomEvent('ionInputDidUnload', {\n        detail: this.el\n      }));\n    }\n    if (this.slotMutationController) {\n      this.slotMutationController.destroy();\n      this.slotMutationController = undefined;\n    }\n    if (this.notchController) {\n      this.notchController.destroy();\n      this.notchController = undefined;\n    }\n  }\n  /**\n   * Sets focus on the native `input` in `ion-input`. Use this method instead of the global\n   * `input.focus()`.\n   *\n   * Developers who wish to focus an input when a page enters\n   * should call `setFocus()` in the `ionViewDidEnter()` lifecycle method.\n   *\n   * Developers who wish to focus an input when an overlay is presented\n   * should call `setFocus` after `didPresent` has resolved.\n   *\n   * See [managing focus](/docs/developing/managing-focus) for more information.\n   */\n  setFocus() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.nativeInput) {\n        _this.nativeInput.focus();\n      }\n    })();\n  }\n  /**\n   * Returns the native `<input>` element used under the hood.\n   */\n  getInputElement() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      /**\n       * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n       * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n       */\n      if (!_this2.nativeInput) {\n        yield new Promise(resolve => componentOnReady(_this2.el, resolve));\n      }\n      return Promise.resolve(_this2.nativeInput);\n    })();\n  }\n  /**\n   * Emits an `ionChange` event.\n   *\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitValueChange(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    // Emitting a value change should update the internal state for tracking the focused value\n    this.focusedValue = newValue;\n    this.ionChange.emit({\n      value: newValue,\n      event\n    });\n  }\n  /**\n   * Emits an `ionInput` event.\n   */\n  emitInputChange(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    this.ionInput.emit({\n      value: newValue,\n      event\n    });\n  }\n  shouldClearOnEdit() {\n    const {\n      type,\n      clearOnEdit\n    } = this;\n    return clearOnEdit === undefined ? type === 'password' : clearOnEdit;\n  }\n  getValue() {\n    return typeof this.value === 'number' ? this.value.toString() : (this.value || '').toString();\n  }\n  emitStyle() {\n    if (this.legacyFormController.hasLegacyControl()) {\n      this.ionStyle.emit({\n        interactive: true,\n        input: true,\n        'has-placeholder': this.placeholder !== undefined,\n        'has-value': this.hasValue(),\n        'has-focus': this.hasFocus,\n        'interactive-disabled': this.disabled,\n        // TODO(FW-2764): remove this\n        legacy: !!this.legacy\n      });\n    }\n  }\n  checkClearOnEdit(ev) {\n    if (!this.shouldClearOnEdit()) {\n      return;\n    }\n    /**\n     * The following keys do not modify the\n     * contents of the input. As a result, pressing\n     * them should not edit the input.\n     *\n     * We can't check to see if the value of the input\n     * was changed because we call checkClearOnEdit\n     * in a keydown listener, and the key has not yet\n     * been added to the input.\n     */\n    const IGNORED_KEYS = ['Enter', 'Tab', 'Shift', 'Meta', 'Alt', 'Control'];\n    const pressedIgnoredKey = IGNORED_KEYS.includes(ev.key);\n    /**\n     * Clear the input if the control has not been previously cleared during focus.\n     * Do not clear if the user hitting enter to submit a form.\n     */\n    if (!this.didInputClearOnEdit && this.hasValue() && !pressedIgnoredKey) {\n      this.value = '';\n      this.emitInputChange(ev);\n    }\n    /**\n     * Pressing an IGNORED_KEYS first and\n     * then an allowed key will cause the input to not\n     * be cleared.\n     */\n    if (!pressedIgnoredKey) {\n      this.didInputClearOnEdit = true;\n    }\n  }\n  hasValue() {\n    return this.getValue().length > 0;\n  }\n  /**\n   * Renders the helper text or error text values\n   */\n  renderHintText() {\n    const {\n      helperText,\n      errorText\n    } = this;\n    return [h(\"div\", {\n      class: \"helper-text\"\n    }, helperText), h(\"div\", {\n      class: \"error-text\"\n    }, errorText)];\n  }\n  renderCounter() {\n    const {\n      counter,\n      maxlength,\n      counterFormatter,\n      value\n    } = this;\n    if (counter !== true || maxlength === undefined) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"counter\"\n    }, getCounterText(value, maxlength, counterFormatter));\n  }\n  /**\n   * Responsible for rendering helper text,\n   * error text, and counter. This element should only\n   * be rendered if hint text is set or counter is enabled.\n   */\n  renderBottomContent() {\n    const {\n      counter,\n      helperText,\n      errorText,\n      maxlength\n    } = this;\n    /**\n     * undefined and empty string values should\n     * be treated as not having helper/error text.\n     */\n    const hasHintText = !!helperText || !!errorText;\n    const hasCounter = counter === true && maxlength !== undefined;\n    if (!hasHintText && !hasCounter) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"input-bottom\"\n    }, this.renderHintText(), this.renderCounter());\n  }\n  renderLabel() {\n    const {\n      label\n    } = this;\n    return h(\"div\", {\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !this.hasLabel\n      }\n    }, label === undefined ? h(\"slot\", {\n      name: \"label\"\n    }) : h(\"div\", {\n      class: \"label-text\"\n    }, label));\n  }\n  /**\n   * Gets any content passed into the `label` slot,\n   * not the <slot> definition.\n   */\n  get labelSlot() {\n    return this.el.querySelector('[slot=\"label\"]');\n  }\n  /**\n   * Returns `true` if label content is provided\n   * either by a prop or a content. If you want\n   * to get the plaintext value of the label use\n   * the `labelText` getter instead.\n   */\n  get hasLabel() {\n    return this.label !== undefined || this.labelSlot !== null;\n  }\n  /**\n   * Renders the border container\n   * when fill=\"outline\".\n   */\n  renderLabelContainer() {\n    const mode = getIonMode(this);\n    const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n    if (hasOutlineFill) {\n      /**\n       * The outline fill has a special outline\n       * that appears around the input and the label.\n       * Certain stacked and floating label placements cause the\n       * label to translate up and create a \"cut out\"\n       * inside of that border by using the notch-spacer element.\n       */\n      return [h(\"div\", {\n        class: \"input-outline-container\"\n      }, h(\"div\", {\n        class: \"input-outline-start\"\n      }), h(\"div\", {\n        class: {\n          'input-outline-notch': true,\n          'input-outline-notch-hidden': !this.hasLabel\n        }\n      }, h(\"div\", {\n        class: \"notch-spacer\",\n        \"aria-hidden\": \"true\",\n        ref: el => this.notchSpacerEl = el\n      }, this.label)), h(\"div\", {\n        class: \"input-outline-end\"\n      })), this.renderLabel()];\n    }\n    /**\n     * If not using the outline style,\n     * we can render just the label.\n     */\n    return this.renderLabel();\n  }\n  renderInput() {\n    const {\n      disabled,\n      fill,\n      readonly,\n      shape,\n      inputId,\n      labelPlacement,\n      el,\n      hasFocus\n    } = this;\n    const mode = getIonMode(this);\n    const value = this.getValue();\n    const inItem = hostContext('ion-item', this.el);\n    const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n    const hasValue = this.hasValue();\n    const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n    /**\n     * If the label is stacked, it should always sit above the input.\n     * For floating labels, the label should move above the input if\n     * the input has a value, is focused, or has anything in either\n     * the start or end slot.\n     *\n     * If there is content in the start slot, the label would overlap\n     * it if not forced to float. This is also applied to the end slot\n     * because with the default or solid fills, the input is not\n     * vertically centered in the container, but the label is. This\n     * causes the slots and label to appear vertically offset from each\n     * other when the label isn't floating above the input. This doesn't\n     * apply to the outline fill, but this was not accounted for to keep\n     * things consistent.\n     *\n     * TODO(FW-5592): Remove hasStartEndSlots condition\n     */\n    const labelShouldFloat = labelPlacement === 'stacked' || labelPlacement === 'floating' && (hasValue || hasFocus || hasStartEndSlots);\n    return h(Host, {\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'has-value': hasValue,\n        'has-focus': hasFocus,\n        'label-floating': labelShouldFloat,\n        [`input-fill-${fill}`]: fill !== undefined,\n        [`input-shape-${shape}`]: shape !== undefined,\n        [`input-label-placement-${labelPlacement}`]: true,\n        'in-item': inItem,\n        'in-item-color': hostContext('ion-item.ion-color', this.el),\n        'input-disabled': disabled\n      })\n    }, h(\"label\", {\n      class: \"input-wrapper\",\n      htmlFor: inputId\n    }, this.renderLabelContainer(), h(\"div\", {\n      class: \"native-wrapper\"\n    }, h(\"slot\", {\n      name: \"start\"\n    }), h(\"input\", Object.assign({\n      class: \"native-input\",\n      ref: input => this.nativeInput = input,\n      id: inputId,\n      disabled: disabled,\n      accept: this.accept,\n      autoCapitalize: this.autocapitalize,\n      autoComplete: this.autocomplete,\n      autoCorrect: this.autocorrect,\n      autoFocus: this.autofocus,\n      enterKeyHint: this.enterkeyhint,\n      inputMode: this.inputmode,\n      min: this.min,\n      max: this.max,\n      minLength: this.minlength,\n      maxLength: this.maxlength,\n      multiple: this.multiple,\n      name: this.name,\n      pattern: this.pattern,\n      placeholder: this.placeholder || '',\n      readOnly: readonly,\n      required: this.required,\n      spellcheck: this.spellcheck,\n      step: this.step,\n      size: this.size,\n      type: this.type,\n      value: value,\n      onInput: this.onInput,\n      onChange: this.onChange,\n      onBlur: this.onBlur,\n      onFocus: this.onFocus,\n      onKeyDown: this.onKeydown,\n      onCompositionstart: this.onCompositionStart,\n      onCompositionend: this.onCompositionEnd\n    }, this.inheritedAttributes)), this.clearInput && !readonly && !disabled && h(\"button\", {\n      \"aria-label\": \"reset\",\n      type: \"button\",\n      class: \"input-clear-icon\",\n      onPointerDown: ev => {\n        /**\n         * This prevents mobile browsers from\n         * blurring the input when the clear\n         * button is activated.\n         */\n        ev.preventDefault();\n      },\n      onFocusin: ev => {\n        /**\n         * Prevent the focusin event from bubbling otherwise it will cause the focusin\n         * event listener in scroll assist to fire. When this fires, focus will be moved\n         * back to the input even if the clear button was never tapped. This poses issues\n         * for screen readers as it means users would be unable to swipe past the clear button.\n         */\n        ev.stopPropagation();\n      },\n      onClick: this.clearTextInput\n    }, h(\"ion-icon\", {\n      \"aria-hidden\": \"true\",\n      icon: mode === 'ios' ? closeCircle : closeSharp\n    })), h(\"slot\", {\n      name: \"end\"\n    })), shouldRenderHighlight && h(\"div\", {\n      class: \"input-highlight\"\n    })), this.renderBottomContent());\n  }\n  // TODO FW-2764 Remove this\n  renderLegacyInput() {\n    if (!this.hasLoggedDeprecationWarning) {\n      printIonWarning(`ion-input now requires providing a label with either the \"label\" property or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the \"label\" property or the \"aria-label\" attribute.\n\nExample: <ion-input label=\"Email\"></ion-input>\nExample with aria-label: <ion-input aria-label=\"Email\"></ion-input>\n\nFor inputs that do not render the label immediately next to the input, developers may continue to use \"ion-label\" but must manually associate the label with the input by using \"aria-labelledby\".\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n      if (this.legacy) {\n        printIonWarning(`ion-input is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\n\nDevelopers can dismiss this warning by removing their usage of the \"legacy\" property and using the new input syntax.`, this.el);\n      }\n      this.hasLoggedDeprecationWarning = true;\n    }\n    const mode = getIonMode(this);\n    const value = this.getValue();\n    const labelId = this.inputId + '-lbl';\n    const label = findItemLabel(this.el);\n    if (label) {\n      label.id = labelId;\n    }\n    return h(Host, {\n      \"aria-disabled\": this.disabled ? 'true' : null,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'has-value': this.hasValue(),\n        'has-focus': this.hasFocus,\n        'legacy-input': true,\n        'in-item-color': hostContext('ion-item.ion-color', this.el)\n      })\n    }, h(\"input\", Object.assign({\n      class: \"native-input\",\n      ref: input => this.nativeInput = input,\n      \"aria-labelledby\": label ? label.id : null,\n      disabled: this.disabled,\n      accept: this.accept,\n      autoCapitalize: this.autocapitalize,\n      autoComplete: this.autocomplete,\n      autoCorrect: this.autocorrect,\n      autoFocus: this.autofocus,\n      enterKeyHint: this.enterkeyhint,\n      inputMode: this.inputmode,\n      min: this.min,\n      max: this.max,\n      minLength: this.minlength,\n      maxLength: this.maxlength,\n      multiple: this.multiple,\n      name: this.name,\n      pattern: this.pattern,\n      placeholder: this.placeholder || '',\n      readOnly: this.readonly,\n      required: this.required,\n      spellcheck: this.spellcheck,\n      step: this.step,\n      size: this.size,\n      type: this.type,\n      value: value,\n      onInput: this.onInput,\n      onChange: this.onChange,\n      onBlur: this.onBlur,\n      onFocus: this.onFocus,\n      onKeyDown: this.onKeydown\n    }, this.inheritedAttributes)), this.clearInput && !this.readonly && !this.disabled && h(\"button\", {\n      \"aria-label\": \"reset\",\n      type: \"button\",\n      class: \"input-clear-icon\",\n      onPointerDown: ev => {\n        /**\n         * This prevents mobile browsers from\n         * blurring the input when the clear\n         * button is activated.\n         */\n        ev.preventDefault();\n      },\n      onClick: this.clearTextInput\n    }, h(\"ion-icon\", {\n      \"aria-hidden\": \"true\",\n      icon: mode === 'ios' ? closeCircle : closeSharp\n    })));\n  }\n  render() {\n    const {\n      legacyFormController\n    } = this;\n    return legacyFormController.hasLegacyControl() ? this.renderLegacyInput() : this.renderInput();\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"debounce\": [\"debounceChanged\"],\n      \"disabled\": [\"disabledChanged\"],\n      \"placeholder\": [\"placeholderChanged\"],\n      \"value\": [\"valueChanged\"]\n    };\n  }\n};\nlet inputIds = 0;\nInput.style = {\n  ios: IonInputIosStyle0,\n  md: IonInputMdStyle0\n};\nexport { Input as ion_input };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}