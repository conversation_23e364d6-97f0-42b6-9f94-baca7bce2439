.price-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;

  &.small {
    .current-price {
      font-size: 0.875rem;
    }
    .original-price {
      font-size: 0.75rem;
    }
    .discount-badge {
      font-size: 0.625rem;
      padding: 0.125rem 0.25rem;
    }
    .savings {
      font-size: 0.75rem;
    }
  }

  &.medium {
    .current-price {
      font-size: 1rem;
    }
    .original-price {
      font-size: 0.875rem;
    }
    .discount-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.375rem;
    }
    .savings {
      font-size: 0.875rem;
    }
  }

  &.large {
    .current-price {
      font-size: 1.25rem;
    }
    .original-price {
      font-size: 1rem;
    }
    .discount-badge {
      font-size: 0.875rem;
      padding: 0.375rem 0.5rem;
    }
    .savings {
      font-size: 1rem;
    }
  }
}

.current-price {
  font-weight: 600;
  color: var(--ion-color-success);
}

.original-price {
  text-decoration: line-through;
  color: var(--ion-color-medium);
  font-weight: 400;
}

.discount-badge {
  background: var(--ion-color-danger);
  color: white;
  border-radius: 4px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.savings {
  color: var(--ion-color-success);
  font-weight: 500;
}
