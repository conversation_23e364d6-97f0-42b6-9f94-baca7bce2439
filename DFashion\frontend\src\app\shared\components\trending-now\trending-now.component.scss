.trending-now-section {
  padding: 2rem 0;
  background: white;

  // Section Header
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 0 1rem;
    gap: 1rem;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
    }

    .header-content {
      .section-title {
        font-size: 2rem;
        font-weight: 700;
        color: #262626;
        margin: 0 0 0.5rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        i {
          color: #ff6b6b;
          font-size: 1.8rem;
          animation: pulse 2s infinite;
        }

        @media (max-width: 768px) {
          font-size: 1.5rem;
        }
      }

      .section-subtitle {
        color: #8e8e8e;
        font-size: 1rem;
        margin: 0;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 1rem;

      @media (max-width: 768px) {
        flex-direction: column;
        width: 100%;
        align-items: stretch;
      }

      .trend-filters {
        display: flex;
        gap: 0.5rem;
        background: #f8f9fa;
        padding: 0.25rem;
        border-radius: 25px;

        @media (max-width: 768px) {
          justify-content: center;
        }

        .filter-btn {
          padding: 0.5rem 1rem;
          border: none;
          background: transparent;
          color: #666;
          border-radius: 20px;
          cursor: pointer;
          transition: all 0.3s ease;
          font-weight: 500;
          font-size: 0.85rem;

          &:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
          }

          &.active {
            background: #667eea;
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
          }
        }
      }

      .view-all-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
        color: white;
        border: none;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        white-space: nowrap;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        i {
          transition: transform 0.3s ease;
        }

        &:hover i {
          transform: translateX(3px);
        }
      }
    }
  }

  // Loading State
  .loading-container {
    padding: 0 1rem;

    .products-scroll {
      display: flex;
      gap: 1rem;
      overflow-x: hidden;

      .product-card-skeleton {
        min-width: 280px;
        background: #f8f9fa;
        border-radius: 16px;
        overflow: hidden;

        .skeleton-image {
          width: 100%;
          height: 300px;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: loading 1.5s infinite;
        }

        .skeleton-content {
          padding: 1rem;

          .skeleton-text {
            height: 16px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
            margin-bottom: 0.5rem;

            &.short {
              width: 60%;
            }
          }
        }
      }
    }
  }

  // Products Container
  .products-container {
    position: relative;
    padding: 0 1rem;

    .products-scroll {
      display: flex;
      gap: 1rem;
      overflow-x: auto;
      scroll-behavior: smooth;
      padding-bottom: 1rem;

      &::-webkit-scrollbar {
        height: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a1a1a1;
        }
      }
    }

    .scroll-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 48px;
      height: 48px;
      background: white;
      border: 1px solid #e0e0e0;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      z-index: 2;

      &:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background: #f8f9fa;
      }

      &.scroll-left {
        left: -24px;
      }

      &.scroll-right {
        right: -24px;
      }

      @media (max-width: 768px) {
        display: none;
      }
    }
  }

  // Product Card
  .product-card {
    min-width: 280px;
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);

      .quick-actions {
        opacity: 1;
        transform: translateY(0);
      }

      .product-image {
        transform: scale(1.05);
      }
    }

    &:focus {
      outline: 3px solid #667eea;
      outline-offset: 2px;
    }

    .product-image-container {
      position: relative;
      height: 300px;
      overflow: hidden;
      background: #f8f9fa;

      .product-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .trending-badge {
        position: absolute;
        top: 12px;
        left: 12px;
        background: linear-gradient(135deg, #ff6b6b, #ff8e53);
        color: white;
        padding: 0.5rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);

        i {
          animation: pulse 2s infinite;
        }
      }

      .discount-badge {
        position: absolute;
        top: 12px;
        right: 12px;
        background: #e91e63;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
      }

      .quick-actions {
        position: absolute;
        bottom: 12px;
        right: 12px;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.3s ease;

        .quick-action-btn {
          width: 40px;
          height: 40px;
          background: rgba(255, 255, 255, 0.9);
          border: none;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);

          &:hover {
            background: #667eea;
            color: white;
            transform: scale(1.1);
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          .fa-heart.wishlisted {
            color: #e91e63;
          }
        }
      }
    }

    .product-info {
      padding: 1rem;

      .product-brand {
        color: #8e8e8e;
        font-size: 0.8rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.25rem;
      }

      .product-name {
        font-size: 1rem;
        font-weight: 600;
        color: #262626;
        margin: 0 0 0.5rem 0;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .product-rating {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;

        .stars {
          display: flex;
          gap: 0.125rem;

          .fa-star {
            color: #ddd;
            font-size: 0.8rem;

            &.filled {
              color: #ffc107;
            }
          }
        }

        .rating-text {
          color: #8e8e8e;
          font-size: 0.8rem;
        }
      }

      .product-pricing {
        margin-bottom: 0.75rem;

        .current-price {
          font-size: 1.2rem;
          font-weight: 700;
          color: #262626;
        }

        .original-price {
          font-size: 0.9rem;
          color: #8e8e8e;
          text-decoration: line-through;
          margin-left: 0.5rem;
        }
      }

      .trending-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.75rem;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          font-size: 0.75rem;
          color: #666;

          i {
            color: #ff6b6b;
            width: 12px;
          }
        }
      }

      .stock-status {
        font-size: 0.8rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.25rem;

        &.in-stock {
          color: #4caf50;
        }

        &.low-stock {
          color: #ff9800;
        }

        &.out-of-stock {
          color: #f44336;
        }

        i {
          font-size: 0.7rem;
        }
      }
    }
  }

  // Empty State
  .empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #8e8e8e;

    .empty-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      color: #ddd;
    }

    h3 {
      margin: 0 0 0.5rem 0;
      color: #666;
    }

    p {
      margin: 0 0 1.5rem 0;
    }

    .retry-btn {
      background: #ff6b6b;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin: 0 auto;
    }
  }
}

// Animations
@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .trending-now-section {
    background: #121212;

    .section-title {
      color: #ffffff;
    }

    .product-card {
      background: #1e1e1e;

      .product-name {
        color: #ffffff;
      }

      .product-image-container {
        background: #2a2a2a;
      }

      .quick-action-btn {
        background: rgba(30, 30, 30, 0.9);
        color: #ffffff;
      }
    }

    .scroll-btn {
      background: #1e1e1e;
      border-color: #333;
      color: #ffffff;

      &:hover {
        background: #667eea;
      }
    }

    .trend-filters {
      background: #2a2a2a;

      .filter-btn {
        color: #ffffff;

        &:hover {
          background: rgba(102, 126, 234, 0.2);
        }
      }
    }
  }
}
