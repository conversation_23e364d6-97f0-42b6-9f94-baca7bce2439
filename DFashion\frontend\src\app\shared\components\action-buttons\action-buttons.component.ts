import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { ButtonActionsService, ProductActionData, PostActionData } from '../../../core/services/button-actions.service';

export interface ActionButtonsConfig {
  showAddToCart?: boolean;
  showAddToWishlist?: boolean;
  showBuyNow?: boolean;
  showLike?: boolean;
  showComment?: boolean;
  showShare?: boolean;
  showSave?: boolean;
  size?: 'small' | 'medium' | 'large';
  layout?: 'horizontal' | 'vertical' | 'grid';
  style?: 'filled' | 'outline' | 'clear';
}

@Component({
  selector: 'app-action-buttons',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="action-buttons" [class]="'layout-' + config.layout" [class.size-small]="config.size === 'small'" [class.size-large]="config.size === 'large'">
      
      <!-- Add to Cart Button -->
      <ion-button 
        *ngIf="config.showAddToCart"
        [fill]="config.style || 'filled'"
        [disabled]="isLoading || isInCart"
        (click)="handleAddToCart()"
        class="action-btn cart-btn"
        [class.in-cart]="isInCart">
        <ion-icon name="bag-add" slot="start"></ion-icon>
        <span>{{ isInCart ? 'In Cart' : 'Add to Cart' }}</span>
        <ion-spinner *ngIf="isLoading && currentAction === 'cart'" name="crescent" size="small"></ion-spinner>
      </ion-button>

      <!-- Add to Wishlist Button -->
      <ion-button 
        *ngIf="config.showAddToWishlist"
        [fill]="config.style || 'outline'"
        [disabled]="isLoading"
        (click)="handleWishlistToggle()"
        class="action-btn wishlist-btn"
        [class.in-wishlist]="isInWishlist">
        <ion-icon [name]="isInWishlist ? 'heart' : 'heart-outline'" slot="start"></ion-icon>
        <span>{{ isInWishlist ? 'In Wishlist' : 'Add to Wishlist' }}</span>
        <ion-spinner *ngIf="isLoading && currentAction === 'wishlist'" name="crescent" size="small"></ion-spinner>
      </ion-button>

      <!-- Buy Now Button -->
      <ion-button 
        *ngIf="config.showBuyNow"
        [fill]="config.style || 'filled'"
        [disabled]="isLoading"
        (click)="handleBuyNow()"
        class="action-btn buy-btn"
        color="success">
        <ion-icon name="flash" slot="start"></ion-icon>
        <span>Buy Now</span>
        <ion-spinner *ngIf="isLoading && currentAction === 'buy'" name="crescent" size="small"></ion-spinner>
      </ion-button>

      <!-- Like Button -->
      <ion-button 
        *ngIf="config.showLike"
        [fill]="config.style || 'clear'"
        [disabled]="isLoading"
        (click)="handleLikeToggle()"
        class="action-btn like-btn"
        [class.liked]="isLiked">
        <ion-icon [name]="isLiked ? 'heart' : 'heart-outline'" slot="start"></ion-icon>
        <span>{{ isLiked ? 'Liked' : 'Like' }}</span>
        <ion-spinner *ngIf="isLoading && currentAction === 'like'" name="crescent" size="small"></ion-spinner>
      </ion-button>

      <!-- Comment Button -->
      <ion-button 
        *ngIf="config.showComment"
        [fill]="config.style || 'clear'"
        [disabled]="isLoading"
        (click)="handleComment()"
        class="action-btn comment-btn">
        <ion-icon name="chatbubble-outline" slot="start"></ion-icon>
        <span>Comment</span>
      </ion-button>

      <!-- Share Button -->
      <ion-button 
        *ngIf="config.showShare"
        [fill]="config.style || 'clear'"
        [disabled]="isLoading"
        (click)="handleShare()"
        class="action-btn share-btn">
        <ion-icon name="share-outline" slot="start"></ion-icon>
        <span>Share</span>
      </ion-button>

      <!-- Save Button -->
      <ion-button 
        *ngIf="config.showSave"
        [fill]="config.style || 'clear'"
        [disabled]="isLoading"
        (click)="handleSaveToggle()"
        class="action-btn save-btn"
        [class.saved]="isSaved">
        <ion-icon [name]="isSaved ? 'bookmark' : 'bookmark-outline'" slot="start"></ion-icon>
        <span>{{ isSaved ? 'Saved' : 'Save' }}</span>
        <ion-spinner *ngIf="isLoading && currentAction === 'save'" name="crescent" size="small"></ion-spinner>
      </ion-button>

    </div>
  `,
  styleUrls: ['./action-buttons.component.scss']
})
export class ActionButtonsComponent implements OnInit, OnDestroy {
  @Input() config: ActionButtonsConfig = {
    showAddToCart: true,
    showAddToWishlist: true,
    showBuyNow: false,
    showLike: false,
    showComment: false,
    showShare: false,
    showSave: false,
    size: 'medium',
    layout: 'horizontal',
    style: 'filled'
  };

  // Product-related inputs
  @Input() productId?: string;
  @Input() productSize?: string;
  @Input() productColor?: string;
  @Input() productQuantity: number = 1;
  @Input() addedFrom: string = 'manual';

  // Post-related inputs
  @Input() postId?: string;
  @Input() storyId?: string;

  // State inputs (can be overridden by service state)
  @Input() initialIsInCart: boolean = false;
  @Input() initialIsInWishlist: boolean = false;
  @Input() initialIsLiked: boolean = false;
  @Input() initialIsSaved: boolean = false;

  // Events
  @Output() actionCompleted = new EventEmitter<{action: string, success: boolean, data?: any}>();
  @Output() commentRequested = new EventEmitter<void>();

  // Component state
  isLoading = false;
  currentAction = '';
  isInCart = false;
  isInWishlist = false;
  isLiked = false;
  isSaved = false;

  private destroy$ = new Subject<void>();

  constructor(private buttonActionsService: ButtonActionsService) {}

  ngOnInit() {
    this.initializeState();
    this.subscribeToStateChanges();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeState() {
    // Set initial state from inputs
    this.isInCart = this.initialIsInCart;
    this.isInWishlist = this.initialIsInWishlist;
    this.isLiked = this.initialIsLiked;
    this.isSaved = this.initialIsSaved;

    // Override with service state if available
    if (this.productId) {
      this.isInCart = this.buttonActionsService.isInCart(this.productId);
      this.isInWishlist = this.buttonActionsService.isInWishlist(this.productId);
    }

    if (this.postId) {
      this.isLiked = this.buttonActionsService.isPostLiked(this.postId);
      this.isSaved = this.buttonActionsService.isPostSaved(this.postId);
    }
  }

  private subscribeToStateChanges() {
    // Subscribe to cart changes
    this.buttonActionsService.cartItems$
      .pipe(takeUntil(this.destroy$))
      .subscribe(cartItems => {
        if (this.productId) {
          this.isInCart = cartItems.has(this.productId);
        }
      });

    // Subscribe to wishlist changes
    this.buttonActionsService.wishlistItems$
      .pipe(takeUntil(this.destroy$))
      .subscribe(wishlistItems => {
        if (this.productId) {
          this.isInWishlist = wishlistItems.has(this.productId);
        }
      });

    // Subscribe to liked posts changes
    this.buttonActionsService.likedPosts$
      .pipe(takeUntil(this.destroy$))
      .subscribe(likedPosts => {
        if (this.postId) {
          this.isLiked = likedPosts.has(this.postId);
        }
      });

    // Subscribe to saved posts changes
    this.buttonActionsService.savedPosts$
      .pipe(takeUntil(this.destroy$))
      .subscribe(savedPosts => {
        if (this.postId) {
          this.isSaved = savedPosts.has(this.postId);
        }
      });
  }

  // ==================== ACTION HANDLERS ====================

  handleAddToCart() {
    if (!this.productId) return;

    this.setLoading('cart', true);
    
    const data: ProductActionData = {
      productId: this.productId,
      size: this.productSize,
      color: this.productColor,
      quantity: this.productQuantity,
      addedFrom: this.addedFrom
    };

    this.buttonActionsService.addToCart(data).subscribe({
      next: (result) => {
        this.setLoading('cart', false);
        this.actionCompleted.emit({action: 'addToCart', success: result.success, data: result.data});
      },
      error: (error) => {
        this.setLoading('cart', false);
        this.actionCompleted.emit({action: 'addToCart', success: false});
      }
    });
  }

  handleWishlistToggle() {
    if (!this.productId) return;

    this.setLoading('wishlist', true);

    const action = this.isInWishlist ? 'removeFromWishlist' : 'addToWishlist';
    const observable = this.isInWishlist 
      ? this.buttonActionsService.removeFromWishlist(this.productId)
      : this.buttonActionsService.addToWishlist({
          productId: this.productId,
          size: this.productSize,
          color: this.productColor,
          addedFrom: this.addedFrom
        });

    observable.subscribe({
      next: (result) => {
        this.setLoading('wishlist', false);
        this.actionCompleted.emit({action, success: result.success, data: result.data});
      },
      error: (error) => {
        this.setLoading('wishlist', false);
        this.actionCompleted.emit({action, success: false});
      }
    });
  }

  handleBuyNow() {
    if (!this.productId) return;

    this.setLoading('buy', true);
    
    const data: ProductActionData = {
      productId: this.productId,
      size: this.productSize,
      color: this.productColor,
      quantity: this.productQuantity,
      addedFrom: this.addedFrom
    };

    this.buttonActionsService.buyNow(data).subscribe({
      next: (result) => {
        this.setLoading('buy', false);
        this.actionCompleted.emit({action: 'buyNow', success: result.success, data: result.data});
      },
      error: (error) => {
        this.setLoading('buy', false);
        this.actionCompleted.emit({action: 'buyNow', success: false});
      }
    });
  }

  handleLikeToggle() {
    if (!this.postId) return;

    this.setLoading('like', true);

    const action = this.isLiked ? 'unlikePost' : 'likePost';
    const observable = this.isLiked 
      ? this.buttonActionsService.unlikePost(this.postId)
      : this.buttonActionsService.likePost(this.postId);

    observable.subscribe({
      next: (result) => {
        this.setLoading('like', false);
        this.actionCompleted.emit({action, success: result.success, data: result.data});
      },
      error: (error) => {
        this.setLoading('like', false);
        this.actionCompleted.emit({action, success: false});
      }
    });
  }

  handleComment() {
    this.commentRequested.emit();
  }

  handleShare() {
    if (!this.postId) return;

    this.buttonActionsService.sharePost(this.postId).subscribe({
      next: (result) => {
        this.actionCompleted.emit({action: 'sharePost', success: result.success, data: result.data});
      },
      error: (error) => {
        this.actionCompleted.emit({action: 'sharePost', success: false});
      }
    });
  }

  handleSaveToggle() {
    if (!this.postId) return;

    this.setLoading('save', true);

    this.buttonActionsService.savePost(this.postId).subscribe({
      next: (result) => {
        this.setLoading('save', false);
        this.actionCompleted.emit({action: 'savePost', success: result.success, data: result.data});
      },
      error: (error) => {
        this.setLoading('save', false);
        this.actionCompleted.emit({action: 'savePost', success: false});
      }
    });
  }

  private setLoading(action: string, loading: boolean) {
    this.isLoading = loading;
    this.currentAction = loading ? action : '';
  }
}
