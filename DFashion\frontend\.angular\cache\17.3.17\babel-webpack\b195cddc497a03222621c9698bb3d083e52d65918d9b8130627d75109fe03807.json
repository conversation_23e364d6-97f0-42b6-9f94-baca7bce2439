{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport { SlickCarouselModule } from 'ngx-slick-carousel';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"ngx-slick-carousel\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nconst _c2 = [\"storiesSlider\"];\nconst _c3 = () => [1, 2, 3, 4, 5];\nfunction ViewAddStoriesComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"div\", 6)(2, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_div_1_Template, 3, 0, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nfunction ViewAddStoriesComponent_ngx_slick_carousel_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 10)(2, \"div\", 18)(3, \"div\", 12);\n    i0.ɵɵelement(4, \"img\", 19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 15);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r3 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", story_r3.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", story_r3.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r3.user.username);\n  }\n}\nfunction ViewAddStoriesComponent_ngx_slick_carousel_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ngx-slick-carousel\", 8)(1, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_ngx_slick_carousel_2_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAdd());\n    });\n    i0.ɵɵelementStart(2, \"div\", 10)(3, \"div\", 11)(4, \"div\", 12);\n    i0.ɵɵelement(5, \"img\", 13);\n    i0.ɵɵelementStart(6, \"span\", 14);\n    i0.ɵɵtext(7, \"+\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"div\", 15);\n    i0.ɵɵtext(9, \"Your Story\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, ViewAddStoriesComponent_ngx_slick_carousel_2_div_10_Template, 7, 3, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"config\", ctx_r1.storySliderConfig);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", (ctx_r1.currentUser == null ? null : ctx_r1.currentUser.avatar) || \"assets/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, authService) {\n    this.router = router;\n    this.http = http;\n    this.authService = authService;\n    this.currentUser = null;\n    this.stories = [];\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    this.subscriptions = [];\n    this.canScrollStoriesLeft = false;\n    this.canScrollStoriesRight = false;\n    this.storySliderConfig = {\n      slidesToShow: 6,\n      slidesToScroll: 2,\n      infinite: false,\n      arrows: false,\n      dots: false,\n      variableWidth: false,\n      swipeToSlide: true,\n      responsive: [{\n        breakpoint: 900,\n        settings: {\n          slidesToShow: 4\n        }\n      }, {\n        breakpoint: 600,\n        settings: {\n          slidesToShow: 3\n        }\n      }]\n    };\n    // Handler for Add Story button\n    // Modal state\n    this.showAddModal = false;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    // Reel recording state\n    this.isRecording = false;\n    this.recordedChunks = [];\n    this.mediaRecorder = null;\n    this.videoStream = null;\n    this.reelPreviewUrl = null;\n    this.isUploadingReel = false;\n    this.newReelCaption = '';\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.isUploadingStory = false;\n  }\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  // --- Slider Arrow Logic ---\n  scrollStoriesLeft() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({\n        left: -200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  scrollStoriesRight() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({\n        left: 200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  updateStoriesArrows() {\n    if (this.storiesSlider) {\n      const el = this.storiesSlider.nativeElement;\n      this.canScrollStoriesLeft = el.scrollLeft > 0;\n      this.canScrollStoriesRight = el.scrollLeft < el.scrollWidth - el.clientWidth - 1;\n    }\n  }\n  ngAfterViewInit() {\n    setTimeout(() => this.updateStoriesArrows(), 500);\n  }\n  // --- Story Logic (unchanged) ---\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(this.http.get(`${environment.apiUrl}/stories`).subscribe({\n      next: response => {\n        if (response.success && response.storyGroups) {\n          this.stories = response.storyGroups;\n        } else {\n          this.loadFallbackStories();\n        }\n        this.isLoadingStories = false;\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.loadFallbackStories();\n        this.isLoadingStories = false;\n      }\n    }));\n  }\n  loadFallbackStories() {\n    this.stories = [\n      // ... (same as before, omitted for brevity)\n    ];\n  }\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n  onTouchEnd(event) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n  updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = dragDelta / window.innerWidth * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  update() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' ? this.currentIndex + 1 : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.update());\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach(video => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  setupEventListeners() {}\n  removeEventListeners() {}\n  getCurrentStory() {\n    return this.stories[this.currentIndex];\n  }\n  getStoryProgress() {\n    return (this.currentIndex + 1) / this.stories.length * 100;\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  startCameraForReel() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.videoStream = yield navigator.mediaDevices.getUserMedia({\n          video: true,\n          audio: true\n        });\n        const video = document.getElementById('reel-video');\n        if (video) {\n          video.srcObject = _this.videoStream;\n          video.play();\n        }\n      } catch (err) {\n        alert('Could not access camera.');\n        _this.showAddReelModal = false;\n      }\n    })();\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new window.MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = e => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, {\n        type: 'video/webm'\n      });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  submitNewReel() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.reelPreviewUrl) return;\n      _this2.isUploadingReel = true;\n      try {\n        const blob = new Blob(_this2.recordedChunks, {\n          type: 'video/webm'\n        });\n        const formData = new FormData();\n        formData.append('media', blob, 'reel.webm');\n        const uploadRes = yield _this2.http.post('/api/stories/upload', formData).toPromise();\n        const reelPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this2.newReelCaption,\n          isReel: true\n        };\n        yield _this2.http.post('/api/stories', reelPayload).toPromise();\n        _this2.showAddReelModal = false;\n        _this2.loadStories();\n      } catch (err) {\n        alert('Failed to upload reel.');\n      } finally {\n        _this2.isUploadingReel = false;\n        _this2.cleanupReelStream();\n      }\n    })();\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  submitNewStory() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.newStoryFile) return;\n      _this3.isUploadingStory = true;\n      try {\n        const uploadForm = new FormData();\n        uploadForm.append('media', _this3.newStoryFile);\n        const uploadRes = yield _this3.http.post('/api/stories/upload', uploadForm).toPromise();\n        const storyPayload = {\n          media: {\n            type: uploadRes.type,\n            url: uploadRes.url\n          },\n          caption: _this3.newStoryCaption\n        };\n        yield _this3.http.post('/api/stories', storyPayload).toPromise();\n        _this3.showAddStoryModal = false;\n        _this3.loadStories();\n      } catch (err) {\n        alert('Failed to upload story.');\n      } finally {\n        _this3.isUploadingStory = false;\n      }\n    })();\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"stories-container\"], [\"class\", \"stories-loading\", 4, \"ngIf\"], [\"class\", \"stories-slider\", 3, \"config\", 4, \"ngIf\"], [1, \"stories-loading\"], [\"class\", \"story-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-skeleton\"], [1, \"skeleton-avatar\"], [1, \"skeleton-name\"], [1, \"stories-slider\", 3, \"config\"], [\"ngxSlickItem\", \"\", 1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"story-avatar\", \"add-avatar\"], [1, \"story-avatar-inner\"], [\"alt\", \"Your Story\", 1, \"story-avatar-img\", 3, \"src\"], [1, \"add-story-plus\"], [1, \"story-username\"], [\"ngxSlickItem\", \"\", \"class\", \"story-item\", 4, \"ngFor\", \"ngForOf\"], [\"ngxSlickItem\", \"\", 1, \"story-item\"], [1, \"story-avatar\"], [1, \"story-avatar-img\", 3, \"src\", \"alt\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_Template, 2, 2, \"div\", 1)(2, ViewAddStoriesComponent_ngx_slick_carousel_2_Template, 11, 3, \"ngx-slick-carousel\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingStories);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, FormsModule, SlickCarouselModule, i5.SlickCarouselComponent, i5.SlickItemDirective],\n      styles: [\".stories-container[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  background-color: #fff;\\n  border-bottom: 1px solid #ddd;\\n  overflow-x: hidden;\\n}\\n\\n.stories-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n\\n.story-skeleton[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 70px;\\n}\\n\\n.skeleton-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  background: #e0e0e0;\\n  margin-bottom: 6px;\\n}\\n\\n.skeleton-name[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 10px;\\n  background: #e0e0e0;\\n  border-radius: 4px;\\n}\\n\\n.stories-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.story-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  width: 70px;\\n  margin: 0 6px;\\n  cursor: pointer;\\n}\\n\\n.story-avatar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);\\n  padding: 2px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.story-avatar-inner[_ngcontent-%COMP%] {\\n  width: 54px;\\n  height: 54px;\\n  border-radius: 50%;\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n}\\n\\n.story-avatar-img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.story-username[_ngcontent-%COMP%] {\\n  margin-top: 6px;\\n  font-size: 12px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n\\n\\n.add-avatar[_ngcontent-%COMP%] {\\n  background: #c5c5c5;\\n}\\n\\n.add-story-plus[_ngcontent-%COMP%] {\\n  position: absolute;\\n  font-size: 20px;\\n  font-weight: bold;\\n  color: #fff;\\n  background: #007bff;\\n  border-radius: 50%;\\n  padding: 2px 6px;\\n  bottom: -2px;\\n  right: -2px;\\n  z-index: 1;\\n}\\n\\n\\n\\n.add-story-item[_ngcontent-%COMP%]   .story-avatar-inner[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "environment", "SlickCarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "ViewAddStoriesComponent_div_1_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c3", "ɵɵtext", "story_r3", "user", "avatar", "ɵɵsanitizeUrl", "username", "ɵɵtextInterpolate", "ɵɵlistener", "ViewAddStoriesComponent_ngx_slick_carousel_2_Template_div_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onAdd", "ViewAddStoriesComponent_ngx_slick_carousel_2_div_10_Template", "storySliderConfig", "currentUser", "stories", "ViewAddStoriesComponent", "constructor", "router", "http", "authService", "isLoadingStories", "currentIndex", "isOpen", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "subscriptions", "canScrollStoriesLeft", "canScrollStoriesRight", "slidesToShow", "slidesToScroll", "infinite", "arrows", "dots", "variableWidth", "swipeToSlide", "responsive", "breakpoint", "settings", "showAddModal", "showAddStoryModal", "showAddReelModal", "showPermissionModal", "showCameraOrGallery", "permissionDenied", "isRecording", "recordedChunks", "mediaRecorder", "videoStream", "reelPreviewUrl", "isUploadingReel", "newReelCaption", "newStoryFile", "newStoryCaption", "isUploadingStory", "ngOnInit", "loadStories", "setupEventListeners", "currentUser$", "subscribe", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "scrollStoriesLeft", "storiesSlider", "nativeElement", "scrollBy", "left", "behavior", "setTimeout", "updateStoriesArrows", "scrollStoriesRight", "el", "scrollLeft", "scrollWidth", "clientWidth", "ngAfterViewInit", "push", "get", "apiUrl", "next", "response", "success", "storyGroups", "loadFallbackStories", "error", "console", "openStories", "index", "showStory", "document", "body", "style", "overflow", "closeStories", "pauseAllVideos", "storiesContainer", "classList", "add", "remove", "transform", "nextStory", "length", "update", "previousStory", "handleKeydown", "event", "key", "onStoryClick", "clickX", "clientX", "windowWidth", "window", "innerWidth", "onTouchStart", "touches", "onTouchMove", "updateDragPosition", "onTouchEnd", "<PERSON><PERSON><PERSON><PERSON>", "threshold", "Math", "abs", "newIndex", "requestAnimationFrame", "videos", "querySelectorAll", "video", "pause", "getCurrentStory", "getStoryProgress", "getTimeAgo", "dateString", "now", "Date", "date", "diffInMinutes", "floor", "getTime", "diffInHours", "diffInDays", "formatNumber", "num", "toFixed", "toString", "formatPrice", "price", "Intl", "NumberFormat", "currency", "minimumFractionDigits", "format", "viewProduct", "product", "navigate", "_id", "hasProducts", "story", "products", "getStoryProducts", "onAddStory", "onAddReel", "startCameraForReel", "_this", "_asyncToGenerator", "navigator", "mediaDevices", "getUserMedia", "audio", "getElementById", "srcObject", "play", "err", "alert", "startRecording", "MediaRecorder", "ondataavailable", "e", "data", "size", "onstop", "blob", "Blob", "type", "URL", "createObjectURL", "start", "stopRecording", "stop", "submitNewReel", "_this2", "formData", "FormData", "append", "uploadRes", "post", "to<PERSON>romise", "reelPayload", "media", "url", "caption", "isReel", "cleanupReelStream", "getTracks", "track", "closeAddReelModal", "handlePermissionResponse", "allow", "openCamera", "input", "setAttribute", "click", "openGallery", "removeAttribute", "onStoryFileSelected", "file", "target", "files", "submitNewStory", "_this3", "uploadForm", "storyPayload", "closeAddStoryModal", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "AuthService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_keydown_HostBindingHandler", "$event", "ɵɵresolveDocument", "ViewAddStoriesComponent_div_1_Template", "ViewAddStoriesComponent_ngx_slick_carousel_2_Template", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "SlickCarouselComponent", "SlickItemDirective", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.ts", "E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\instagram-stories\\view-add-stories.component.html"], "sourcesContent": ["import { Component, On<PERSON>nit, OnD<PERSON>roy, ElementRef, ViewChild, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { AuthService } from 'src/app/core/services/auth.service';\n\nimport { SlickCarouselModule } from 'ngx-slick-carousel';\n\ninterface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, FormsModule, SlickCarouselModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n  currentUser: any = null;\n\n  stories: Story[] = [];\n  isLoadingStories = true;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n  private subscriptions: Subscription[] = [];\n\n  // For slider arrows\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef<HTMLDivElement>;\n  canScrollStoriesLeft = false;\n  canScrollStoriesRight = false;\nstorySliderConfig = {\n  slidesToShow: 6,\n  slidesToScroll: 2,\n  infinite: false,\n  arrows: false,\n  dots: false,\n  variableWidth: false,\n  swipeToSlide: true,\n  responsive: [\n    { breakpoint: 900, settings: { slidesToShow: 4 } },\n    { breakpoint: 600, settings: { slidesToShow: 3 } }\n  ]\n};\n  constructor(\n    private router: Router,\n    private http: HttpClient,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit() {\n    this.loadStories();\n    this.setupEventListeners();\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  // --- Slider Arrow Logic ---\n  scrollStoriesLeft() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({ left: -200, behavior: 'smooth' });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  scrollStoriesRight() {\n    if (this.storiesSlider) {\n      this.storiesSlider.nativeElement.scrollBy({ left: 200, behavior: 'smooth' });\n      setTimeout(() => this.updateStoriesArrows(), 300);\n    }\n  }\n  updateStoriesArrows() {\n    if (this.storiesSlider) {\n      const el = this.storiesSlider.nativeElement;\n      this.canScrollStoriesLeft = el.scrollLeft > 0;\n      this.canScrollStoriesRight = el.scrollLeft < el.scrollWidth - el.clientWidth - 1;\n    }\n  }\n  ngAfterViewInit() {\n    setTimeout(() => this.updateStoriesArrows(), 500);\n  }\n\n  // --- Story Logic (unchanged) ---\n  loadStories() {\n    this.isLoadingStories = true;\n    this.subscriptions.push(\n      this.http.get<any>(`${environment.apiUrl}/stories`).subscribe({\n        next: (response) => {\n          if (response.success && response.storyGroups) {\n            this.stories = response.storyGroups;\n          } else {\n            this.loadFallbackStories();\n          }\n          this.isLoadingStories = false;\n        },\n        error: (error) => {\n          console.error('Error loading stories:', error);\n          this.loadFallbackStories();\n          this.isLoadingStories = false;\n        }\n      })\n    );\n  }\n\n  loadFallbackStories() {\n    this.stories = [\n      // ... (same as before, omitted for brevity)\n    ];\n  }\n\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index: number) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n    const clickX = event.clientX;\n    const windowWidth = window.innerWidth;\n    if (clickX < windowWidth / 3) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    this.updateDragPosition();\n  }\n  onTouchEnd(event: TouchEvent) {\n    if (!this.isDragging) return;\n    this.isDragging = false;\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    const threshold = window.innerWidth * this.minDragPercentToTransition;\n    if (Math.abs(dragDelta) > threshold) {\n      if (dragDelta > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n    } else {\n      this.targetRotateY = 0;\n      this.isRotating = true;\n      this.update();\n    }\n  }\n  private updateDragPosition() {\n    const dragDelta = this.dragCurrentX - this.dragStartX;\n    this.rotateY = (dragDelta / window.innerWidth) * 90;\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n  }\n  private update() {\n    if (!this.isRotating) return;\n    this.rotateY += (this.targetRotateY - this.rotateY) / this.transitionSpeed;\n    if (Math.abs(this.rotateY - this.targetRotateY) < 0.5) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection) {\n        const newIndex = this.targetDirection === 'forward' \n          ? this.currentIndex + 1 \n          : this.currentIndex - 1;\n        this.showStory(newIndex);\n        this.targetDirection = null;\n      }\n      return;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = \n        `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    requestAnimationFrame(() => this.update());\n  }\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('.story__video');\n    videos.forEach((video: any) => {\n      if (video.pause) {\n        video.pause();\n      }\n    });\n  }\n  private setupEventListeners() {}\n  private removeEventListeners() {}\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex];\n  }\n  getStoryProgress(): number {\n    return ((this.currentIndex + 1) / this.stories.length) * 100;\n  }\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  viewProduct(product: any) {\n    this.router.navigate(['/product', product._id]);\n  }\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story && story.products && story.products.length > 0);\n  }\n  getStoryProducts(): any[] {\n    const story = this.getCurrentStory();\n    return story?.products || [];\n  }\n  // Handler for Add Story button\n  // Modal state\n  showAddModal = false;\n  showAddStoryModal = false;\n  showAddReelModal = false;\n  showPermissionModal = false;\n  showCameraOrGallery = false;\n  permissionDenied = false;\n  // Reel recording state\n  isRecording = false;\n  recordedChunks: Blob[] = [];\n  mediaRecorder: any = null;\n  videoStream: MediaStream | null = null;\n  reelPreviewUrl: string | null = null;\n  isUploadingReel = false;\n  newReelCaption = '';\n  newStoryFile: File | null = null;\n  newStoryCaption = '';\n  isUploadingStory = false;\n  onAdd() {\n    this.showAddModal = true;\n    this.showAddStoryModal = false;\n    this.showAddReelModal = false;\n    this.showPermissionModal = false;\n    this.showCameraOrGallery = false;\n    this.permissionDenied = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n  }\n  onAddStory() {\n    this.showAddModal = false;\n    this.showPermissionModal = true;\n    this.permissionDenied = false;\n    this.showAddStoryModal = false;\n    this.showCameraOrGallery = false;\n    this.newStoryFile = null;\n    this.newStoryCaption = '';\n  }\n  onAddReel() {\n    this.showAddModal = false;\n    this.showAddReelModal = true;\n    this.reelPreviewUrl = null;\n    this.newReelCaption = '';\n    setTimeout(() => this.startCameraForReel(), 100);\n  }\n  async startCameraForReel() {\n    try {\n      this.videoStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });\n      const video: any = document.getElementById('reel-video');\n      if (video) {\n        video.srcObject = this.videoStream;\n        video.play();\n      }\n    } catch (err) {\n      alert('Could not access camera.');\n      this.showAddReelModal = false;\n    }\n  }\n  startRecording() {\n    if (!this.videoStream) return;\n    this.recordedChunks = [];\n    this.mediaRecorder = new (window as any).MediaRecorder(this.videoStream);\n    this.mediaRecorder.ondataavailable = (e: any) => {\n      if (e.data.size > 0) this.recordedChunks.push(e.data);\n    };\n    this.mediaRecorder.onstop = () => {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      this.reelPreviewUrl = URL.createObjectURL(blob);\n    };\n    this.mediaRecorder.start();\n    this.isRecording = true;\n  }\n  stopRecording() {\n    if (this.mediaRecorder && this.isRecording) {\n      this.mediaRecorder.stop();\n      this.isRecording = false;\n    }\n  }\n  async submitNewReel() {\n    if (!this.reelPreviewUrl) return;\n    this.isUploadingReel = true;\n    try {\n      const blob = new Blob(this.recordedChunks, { type: 'video/webm' });\n      const formData = new FormData();\n      formData.append('media', blob, 'reel.webm');\n      const uploadRes: any = await this.http.post('/api/stories/upload', formData).toPromise();\n      const reelPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newReelCaption,\n        isReel: true\n      };\n      await this.http.post('/api/stories', reelPayload).toPromise();\n      this.showAddReelModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload reel.');\n    } finally {\n      this.isUploadingReel = false;\n      this.cleanupReelStream();\n    }\n  }\n  cleanupReelStream() {\n    if (this.videoStream) {\n      this.videoStream.getTracks().forEach(track => track.stop());\n      this.videoStream = null;\n    }\n    this.mediaRecorder = null;\n    this.isRecording = false;\n    this.reelPreviewUrl = null;\n  }\n  closeAddReelModal() {\n    this.showAddReelModal = false;\n    this.cleanupReelStream();\n  }\n  handlePermissionResponse(allow: boolean) {\n    this.showPermissionModal = false;\n    if (allow) {\n      this.showCameraOrGallery = true;\n    } else {\n      this.permissionDenied = true;\n    }\n  }\n  openCamera() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.setAttribute('capture', 'environment');\n        input.click();\n      }\n    }, 100);\n  }\n  openGallery() {\n    this.showCameraOrGallery = false;\n    this.showAddStoryModal = true;\n    setTimeout(() => {\n      const input: any = document.getElementById('story-file-input');\n      if (input) {\n        input.removeAttribute('capture');\n        input.click();\n      }\n    }, 100);\n  }\n  onStoryFileSelected(event: any) {\n    const file = event.target.files[0];\n    if (file) {\n      this.newStoryFile = file;\n    }\n  }\n  async submitNewStory() {\n    if (!this.newStoryFile) return;\n    this.isUploadingStory = true;\n    try {\n      const uploadForm = new FormData();\n      uploadForm.append('media', this.newStoryFile);\n      const uploadRes: any = await this.http.post('/api/stories/upload', uploadForm).toPromise();\n      const storyPayload = {\n        media: {\n          type: uploadRes.type,\n          url: uploadRes.url\n        },\n        caption: this.newStoryCaption\n      };\n      await this.http.post('/api/stories', storyPayload).toPromise();\n      this.showAddStoryModal = false;\n      this.loadStories();\n    } catch (err) {\n      alert('Failed to upload story.');\n    } finally {\n      this.isUploadingStory = false;\n    }\n  }\n  closeAddStoryModal() {\n    this.showAddStoryModal = false;\n  }\n}\n", "<!-- Instagram-style Stories Bar -->\n<div class=\"stories-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoadingStories\" class=\"stories-loading\">\n    <div *ngFor=\"let item of [1, 2, 3, 4, 5]\" class=\"story-skeleton\">\n      <div class=\"skeleton-avatar\"></div>\n      <div class=\"skeleton-name\"></div>\n    </div>\n  </div>\n\n  <!-- Stories Carousel -->\n  <ngx-slick-carousel\n    class=\"stories-slider\"\n    [config]=\"storySliderConfig\"\n    *ngIf=\"!isLoadingStories\"\n  >\n    <!-- Add Story -->\n    <div ngxSlickItem class=\"story-item add-story-item\" (click)=\"onAdd()\">\n      <div class=\"story-avatar-container\">\n        <div class=\"story-avatar add-avatar\">\n          <div class=\"story-avatar-inner\">\n            <img\n              class=\"story-avatar-img\"\n              [src]=\"currentUser?.avatar || 'assets/default-avatar.png'\"\n              alt=\"Your Story\"\n            />\n            <span class=\"add-story-plus\">+</span>\n          </div>\n        </div>\n      </div>\n      <div class=\"story-username\">Your Story</div>\n    </div>\n\n    <!-- Other Stories -->\n    <div ngxSlickItem class=\"story-item\" *ngFor=\"let story of stories\">\n      <div class=\"story-avatar-container\">\n        <div class=\"story-avatar\">\n          <div class=\"story-avatar-inner\">\n            <img\n              class=\"story-avatar-img\"\n              [src]=\"story.user.avatar\"\n              [alt]=\"story.user.username\"\n            />\n          </div>\n        </div>\n      </div>\n      <div class=\"story-username\">{{ story.user.username }}</div>\n    </div>\n  </ngx-slick-carousel>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,WAAW,QAAQ,8BAA8B;AAG1D,SAASC,mBAAmB,QAAQ,oBAAoB;;;;;;;;;;;;;ICLpDC,EAAA,CAAAC,cAAA,aAAiE;IAE/DD,EADA,CAAAE,SAAA,aAAmC,aACF;IACnCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAI,UAAA,IAAAC,4CAAA,iBAAiE;IAInEL,EAAA,CAAAG,YAAA,EAAM;;;IAJkBH,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAkB;;;;;IAiClCT,EAHN,CAAAC,cAAA,cAAmE,cAC7B,cACR,cACQ;IAC9BD,EAAA,CAAAE,SAAA,cAIE;IAGRF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAU,MAAA,GAAyB;IACvDV,EADuD,CAAAG,YAAA,EAAM,EACvD;;;;IAPIH,EAAA,CAAAM,SAAA,GAAyB;IACzBN,EADA,CAAAO,UAAA,QAAAI,QAAA,CAAAC,IAAA,CAAAC,MAAA,EAAAb,EAAA,CAAAc,aAAA,CAAyB,QAAAH,QAAA,CAAAC,IAAA,CAAAG,QAAA,CACE;IAKPf,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAgB,iBAAA,CAAAL,QAAA,CAAAC,IAAA,CAAAG,QAAA,CAAyB;;;;;;IA7BvDf,EANF,CAAAC,cAAA,4BAIC,aAEuE;IAAlBD,EAAA,CAAAiB,UAAA,mBAAAC,2EAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAG,KAAA,EAAO;IAAA,EAAC;IAG/DxB,EAFJ,CAAAC,cAAA,cAAoC,cACG,cACH;IAC9BD,EAAA,CAAAE,SAAA,cAIE;IACFF,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAU,MAAA,QAAC;IAGpCV,EAHoC,CAAAG,YAAA,EAAO,EACjC,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAU,MAAA,iBAAU;IACxCV,EADwC,CAAAG,YAAA,EAAM,EACxC;IAGNH,EAAA,CAAAI,UAAA,KAAAqB,4DAAA,kBAAmE;IAcrEzB,EAAA,CAAAG,YAAA,EAAqB;;;;IAnCnBH,EAAA,CAAAO,UAAA,WAAAc,MAAA,CAAAK,iBAAA,CAA4B;IAUlB1B,EAAA,CAAAM,SAAA,GAA0D;IAA1DN,EAAA,CAAAO,UAAA,SAAAc,MAAA,CAAAM,WAAA,kBAAAN,MAAA,CAAAM,WAAA,CAAAd,MAAA,kCAAAb,EAAA,CAAAc,aAAA,CAA0D;IAWbd,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAAO,UAAA,YAAAc,MAAA,CAAAO,OAAA,CAAU;;;ADOrE,OAAM,MAAOC,uBAAuB;EAuClCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB;IAFxB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IAvCrB,KAAAN,WAAW,GAAQ,IAAI;IAEvB,KAAAC,OAAO,GAAY,EAAE;IACrB,KAAAM,gBAAgB,GAAG,IAAI;IAEvB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IACjD,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IACX,KAAAC,aAAa,GAAmB,EAAE;IAI1C,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,qBAAqB,GAAG,KAAK;IAC/B,KAAAvB,iBAAiB,GAAG;MAClBwB,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC;MACjBC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,KAAK;MACXC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,CACV;QAAEC,UAAU,EAAE,GAAG;QAAEC,QAAQ,EAAE;UAAET,YAAY,EAAE;QAAC;MAAE,CAAE,EAClD;QAAEQ,UAAU,EAAE,GAAG;QAAEC,QAAQ,EAAE;UAAET,YAAY,EAAE;QAAC;MAAE,CAAE;KAErD;IA4PC;IACA;IACA,KAAAU,YAAY,GAAG,KAAK;IACpB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,gBAAgB,GAAG,KAAK;IACxB;IACA,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,aAAa,GAAQ,IAAI;IACzB,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,cAAc,GAAkB,IAAI;IACpC,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,YAAY,GAAgB,IAAI;IAChC,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,gBAAgB,GAAG,KAAK;EAzQrB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAAC7C,WAAW,CAAC8C,YAAY,CAACC,SAAS,CAACpE,IAAI,IAAG;MAC7C,IAAI,CAACe,WAAW,GAAGf,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAqE,WAAWA,CAAA;IACT,IAAI,CAAClC,aAAa,CAACmC,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEA;EACAC,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,QAAQ,CAAC;QAAEC,IAAI,EAAE,CAAC,GAAG;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAC7EC,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EACAC,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACP,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,QAAQ,CAAC;QAAEC,IAAI,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAC5EC,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EACAA,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACN,aAAa,EAAE;MACtB,MAAMQ,EAAE,GAAG,IAAI,CAACR,aAAa,CAACC,aAAa;MAC3C,IAAI,CAACxC,oBAAoB,GAAG+C,EAAE,CAACC,UAAU,GAAG,CAAC;MAC7C,IAAI,CAAC/C,qBAAqB,GAAG8C,EAAE,CAACC,UAAU,GAAGD,EAAE,CAACE,WAAW,GAAGF,EAAE,CAACG,WAAW,GAAG,CAAC;;EAEpF;EACAC,eAAeA,CAAA;IACbP,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;EACnD;EAEA;EACAhB,WAAWA,CAAA;IACT,IAAI,CAAC3C,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACa,aAAa,CAACqD,IAAI,CACrB,IAAI,CAACpE,IAAI,CAACqE,GAAG,CAAM,GAAGvG,WAAW,CAACwG,MAAM,UAAU,CAAC,CAACtB,SAAS,CAAC;MAC5DuB,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,WAAW,EAAE;UAC5C,IAAI,CAAC9E,OAAO,GAAG4E,QAAQ,CAACE,WAAW;SACpC,MAAM;UACL,IAAI,CAACC,mBAAmB,EAAE;;QAE5B,IAAI,CAACzE,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACD0E,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACD,mBAAmB,EAAE;QAC1B,IAAI,CAACzE,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC,CACH;EACH;EAEAyE,mBAAmBA,CAAA;IACjB,IAAI,CAAC/E,OAAO,GAAG;MACb;IAAA,CACD;EACH;EAEAkF,WAAWA,CAACC,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAAC5E,YAAY,GAAG4E,KAAK;IACzB,IAAI,CAAC3E,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC4E,SAAS,CAACD,KAAK,CAAC;IACrBE,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EACzC;EACAC,YAAYA,CAAA;IACV,IAAI,CAACjF,MAAM,GAAG,KAAK;IACnB,IAAI,CAACkF,cAAc,EAAE;IACrBL,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IACrC,IAAI,IAAI,CAACG,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC/B,aAAa,CAACgC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAEhE7B,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAAC2B,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAAC/B,aAAa,CAACgC,SAAS,CAACE,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EACAV,SAASA,CAACD,KAAa;IACrB,IAAI,CAAC5E,YAAY,GAAG4E,KAAK;IACzB,IAAI,CAACxE,OAAO,GAAG,CAAC;IAChB,IAAI,IAAI,CAACgF,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC/B,aAAa,CAAC2B,KAAK,CAACQ,SAAS,GAAG,mBAAmB;;EAE7E;EACAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACzF,YAAY,GAAG,IAAI,CAACP,OAAO,CAACiG,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAACrF,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACyF,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACT,YAAY,EAAE;;EAEvB;EACAU,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC5F,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACK,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACyF,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAACT,YAAY,EAAE;;EAEvB;EAEAW,aAAaA,CAACC,KAAoB;IAChC,IAAI,CAAC,IAAI,CAAC7F,MAAM,EAAE;IAClB,QAAQ6F,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACH,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACH,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACP,YAAY,EAAE;QACnB;;EAEN;EACAc,YAAYA,CAACF,KAAiB;IAC5B,IAAI,IAAI,CAAC5F,UAAU,EAAE;IACrB,MAAM+F,MAAM,GAAGH,KAAK,CAACI,OAAO;IAC5B,MAAMC,WAAW,GAAGC,MAAM,CAACC,UAAU;IACrC,IAAIJ,MAAM,GAAGE,WAAW,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACP,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACH,SAAS,EAAE;;EAEpB;EACAa,YAAYA,CAACR,KAAiB;IAC5B,IAAI,CAAC3F,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAGuF,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACL,OAAO;IAC1C,IAAI,CAAC1F,YAAY,GAAG,IAAI,CAACD,UAAU;EACrC;EACAiG,WAAWA,CAACV,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAAC3F,UAAU,EAAE;IACtB,IAAI,CAACK,YAAY,GAAGsF,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACL,OAAO;IAC5C,IAAI,CAACO,kBAAkB,EAAE;EAC3B;EACAC,UAAUA,CAACZ,KAAiB;IAC1B,IAAI,CAAC,IAAI,CAAC3F,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,GAAG,KAAK;IACvB,MAAMwG,SAAS,GAAG,IAAI,CAACnG,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,MAAMqG,SAAS,GAAGR,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC5F,0BAA0B;IACrE,IAAIoG,IAAI,CAACC,GAAG,CAACH,SAAS,CAAC,GAAGC,SAAS,EAAE;MACnC,IAAID,SAAS,GAAG,CAAC,EAAE;QACjB,IAAI,CAACf,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACH,SAAS,EAAE;;KAEnB,MAAM;MACL,IAAI,CAACpF,aAAa,GAAG,CAAC;MACtB,IAAI,CAACH,UAAU,GAAG,IAAI;MACtB,IAAI,CAACyF,MAAM,EAAE;;EAEjB;EACQc,kBAAkBA,CAAA;IACxB,MAAME,SAAS,GAAG,IAAI,CAACnG,YAAY,GAAG,IAAI,CAACD,UAAU;IACrD,IAAI,CAACH,OAAO,GAAIuG,SAAS,GAAGP,MAAM,CAACC,UAAU,GAAI,EAAE;IACnD,IAAI,IAAI,CAACjB,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC/B,aAAa,CAAC2B,KAAK,CAACQ,SAAS,GACjD,6BAA6B,IAAI,CAACpF,OAAO,MAAM;;EAErD;EACQuF,MAAMA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACzF,UAAU,EAAE;IACtB,IAAI,CAACE,OAAO,IAAI,CAAC,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,OAAO,IAAI,IAAI,CAACO,eAAe;IAC1E,IAAIkG,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC1G,OAAO,GAAG,IAAI,CAACC,aAAa,CAAC,GAAG,GAAG,EAAE;MACrD,IAAI,CAACD,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MACvB,IAAI,IAAI,CAACI,eAAe,EAAE;QACxB,MAAMyG,QAAQ,GAAG,IAAI,CAACzG,eAAe,KAAK,SAAS,GAC/C,IAAI,CAACN,YAAY,GAAG,CAAC,GACrB,IAAI,CAACA,YAAY,GAAG,CAAC;QACzB,IAAI,CAAC6E,SAAS,CAACkC,QAAQ,CAAC;QACxB,IAAI,CAACzG,eAAe,GAAG,IAAI;;MAE7B;;IAEF,IAAI,IAAI,CAAC8E,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAC/B,aAAa,CAAC2B,KAAK,CAACQ,SAAS,GACjD,6BAA6B,IAAI,CAACpF,OAAO,MAAM;;IAEnD4G,qBAAqB,CAAC,MAAM,IAAI,CAACrB,MAAM,EAAE,CAAC;EAC5C;EACQR,cAAcA,CAAA;IACpB,MAAM8B,MAAM,GAAGnC,QAAQ,CAACoC,gBAAgB,CAAC,eAAe,CAAC;IACzDD,MAAM,CAAClE,OAAO,CAAEoE,KAAU,IAAI;MAC5B,IAAIA,KAAK,CAACC,KAAK,EAAE;QACfD,KAAK,CAACC,KAAK,EAAE;;IAEjB,CAAC,CAAC;EACJ;EACQzE,mBAAmBA,CAAA,GAAI;EACvBO,oBAAoBA,CAAA,GAAI;EAChCmE,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC5H,OAAO,CAAC,IAAI,CAACO,YAAY,CAAC;EACxC;EACAsH,gBAAgBA,CAAA;IACd,OAAQ,CAAC,IAAI,CAACtH,YAAY,GAAG,CAAC,IAAI,IAAI,CAACP,OAAO,CAACiG,MAAM,GAAI,GAAG;EAC9D;EACA6B,UAAUA,CAACC,UAAkB;IAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMI,aAAa,GAAGf,IAAI,CAACgB,KAAK,CAAC,CAACJ,GAAG,CAACK,OAAO,EAAE,GAAGH,IAAI,CAACG,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAChF,IAAIF,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAClD,MAAMG,WAAW,GAAGlB,IAAI,CAACgB,KAAK,CAACD,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIG,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAC9C,MAAMC,UAAU,GAAGnB,IAAI,CAACgB,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EACAC,YAAYA,CAACC,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EACAC,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCxD,KAAK,EAAE,UAAU;MACjByD,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EAClB;EACAM,WAAWA,CAACC,OAAY;IACtB,IAAI,CAACjJ,MAAM,CAACkJ,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACE,GAAG,CAAC,CAAC;EACjD;EACAC,WAAWA,CAAA;IACT,MAAMC,KAAK,GAAG,IAAI,CAAC5B,eAAe,EAAE;IACpC,OAAO,CAAC,EAAE4B,KAAK,IAAIA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACxD,MAAM,GAAG,CAAC,CAAC;EACjE;EACAyD,gBAAgBA,CAAA;IACd,MAAMF,KAAK,GAAG,IAAI,CAAC5B,eAAe,EAAE;IACpC,OAAO4B,KAAK,EAAEC,QAAQ,IAAI,EAAE;EAC9B;EAoBA7J,KAAKA,CAAA;IACH,IAAI,CAACoC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACQ,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;EAC1B;EACA+G,UAAUA,CAAA;IACR,IAAI,CAAC3H,YAAY,GAAG,KAAK;IACzB,IAAI,CAACG,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACJ,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACG,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACS,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,eAAe,GAAG,EAAE;EAC3B;EACA8G,SAASA,CAAA;IACP,IAAI,CAAC5H,YAAY,GAAG,KAAK;IACzB,IAAI,CAACE,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACQ,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACE,cAAc,GAAG,EAAE;IACxBoB,UAAU,CAAC,MAAM,IAAI,CAAC6F,kBAAkB,EAAE,EAAE,GAAG,CAAC;EAClD;EACMA,kBAAkBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,IAAI;QACFD,KAAI,CAACrH,WAAW,SAASuH,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UAAExC,KAAK,EAAE,IAAI;UAAEyC,KAAK,EAAE;QAAI,CAAE,CAAC;QAC1F,MAAMzC,KAAK,GAAQrC,QAAQ,CAAC+E,cAAc,CAAC,YAAY,CAAC;QACxD,IAAI1C,KAAK,EAAE;UACTA,KAAK,CAAC2C,SAAS,GAAGP,KAAI,CAACrH,WAAW;UAClCiF,KAAK,CAAC4C,IAAI,EAAE;;OAEf,CAAC,OAAOC,GAAG,EAAE;QACZC,KAAK,CAAC,0BAA0B,CAAC;QACjCV,KAAI,CAAC5H,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACAuI,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAChI,WAAW,EAAE;IACvB,IAAI,CAACF,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,aAAa,GAAG,IAAKmE,MAAc,CAAC+D,aAAa,CAAC,IAAI,CAACjI,WAAW,CAAC;IACxE,IAAI,CAACD,aAAa,CAACmI,eAAe,GAAIC,CAAM,IAAI;MAC9C,IAAIA,CAAC,CAACC,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE,IAAI,CAACvI,cAAc,CAACiC,IAAI,CAACoG,CAAC,CAACC,IAAI,CAAC;IACvD,CAAC;IACD,IAAI,CAACrI,aAAa,CAACuI,MAAM,GAAG,MAAK;MAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,IAAI,CAAC1I,cAAc,EAAE;QAAE2I,IAAI,EAAE;MAAY,CAAE,CAAC;MAClE,IAAI,CAACxI,cAAc,GAAGyI,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACjD,CAAC;IACD,IAAI,CAACxI,aAAa,CAAC6I,KAAK,EAAE;IAC1B,IAAI,CAAC/I,WAAW,GAAG,IAAI;EACzB;EACAgJ,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC9I,aAAa,IAAI,IAAI,CAACF,WAAW,EAAE;MAC1C,IAAI,CAACE,aAAa,CAAC+I,IAAI,EAAE;MACzB,IAAI,CAACjJ,WAAW,GAAG,KAAK;;EAE5B;EACMkJ,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA1B,iBAAA;MACjB,IAAI,CAAC0B,MAAI,CAAC/I,cAAc,EAAE;MAC1B+I,MAAI,CAAC9I,eAAe,GAAG,IAAI;MAC3B,IAAI;QACF,MAAMqI,IAAI,GAAG,IAAIC,IAAI,CAACQ,MAAI,CAAClJ,cAAc,EAAE;UAAE2I,IAAI,EAAE;QAAY,CAAE,CAAC;QAClE,MAAMQ,QAAQ,GAAG,IAAIC,QAAQ,EAAE;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEZ,IAAI,EAAE,WAAW,CAAC;QAC3C,MAAMa,SAAS,SAAcJ,MAAI,CAACrL,IAAI,CAAC0L,IAAI,CAAC,qBAAqB,EAAEJ,QAAQ,CAAC,CAACK,SAAS,EAAE;QACxF,MAAMC,WAAW,GAAG;UAClBC,KAAK,EAAE;YACLf,IAAI,EAAEW,SAAS,CAACX,IAAI;YACpBgB,GAAG,EAAEL,SAAS,CAACK;WAChB;UACDC,OAAO,EAAEV,MAAI,CAAC7I,cAAc;UAC5BwJ,MAAM,EAAE;SACT;QACD,MAAMX,MAAI,CAACrL,IAAI,CAAC0L,IAAI,CAAC,cAAc,EAAEE,WAAW,CAAC,CAACD,SAAS,EAAE;QAC7DN,MAAI,CAACvJ,gBAAgB,GAAG,KAAK;QAC7BuJ,MAAI,CAACxI,WAAW,EAAE;OACnB,CAAC,OAAOsH,GAAG,EAAE;QACZC,KAAK,CAAC,wBAAwB,CAAC;OAChC,SAAS;QACRiB,MAAI,CAAC9I,eAAe,GAAG,KAAK;QAC5B8I,MAAI,CAACY,iBAAiB,EAAE;;IACzB;EACH;EACAA,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC5J,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC6J,SAAS,EAAE,CAAChJ,OAAO,CAACiJ,KAAK,IAAIA,KAAK,CAAChB,IAAI,EAAE,CAAC;MAC3D,IAAI,CAAC9I,WAAW,GAAG,IAAI;;IAEzB,IAAI,CAACD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACF,WAAW,GAAG,KAAK;IACxB,IAAI,CAACI,cAAc,GAAG,IAAI;EAC5B;EACA8J,iBAAiBA,CAAA;IACf,IAAI,CAACtK,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACmK,iBAAiB,EAAE;EAC1B;EACAI,wBAAwBA,CAACC,KAAc;IACrC,IAAI,CAACvK,mBAAmB,GAAG,KAAK;IAChC,IAAIuK,KAAK,EAAE;MACT,IAAI,CAACtK,mBAAmB,GAAG,IAAI;KAChC,MAAM;MACL,IAAI,CAACC,gBAAgB,GAAG,IAAI;;EAEhC;EACAsK,UAAUA,CAAA;IACR,IAAI,CAACvK,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,iBAAiB,GAAG,IAAI;IAC7B+B,UAAU,CAAC,MAAK;MACd,MAAM4I,KAAK,GAAQvH,QAAQ,CAAC+E,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIwC,KAAK,EAAE;QACTA,KAAK,CAACC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC;QAC5CD,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACAC,WAAWA,CAAA;IACT,IAAI,CAAC3K,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,iBAAiB,GAAG,IAAI;IAC7B+B,UAAU,CAAC,MAAK;MACd,MAAM4I,KAAK,GAAQvH,QAAQ,CAAC+E,cAAc,CAAC,kBAAkB,CAAC;MAC9D,IAAIwC,KAAK,EAAE;QACTA,KAAK,CAACI,eAAe,CAAC,SAAS,CAAC;QAChCJ,KAAK,CAACE,KAAK,EAAE;;IAEjB,CAAC,EAAE,GAAG,CAAC;EACT;EACAG,mBAAmBA,CAAC5G,KAAU;IAC5B,MAAM6G,IAAI,GAAG7G,KAAK,CAAC8G,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAI,CAACrK,YAAY,GAAGqK,IAAI;;EAE5B;EACMG,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAvD,iBAAA;MAClB,IAAI,CAACuD,MAAI,CAACzK,YAAY,EAAE;MACxByK,MAAI,CAACvK,gBAAgB,GAAG,IAAI;MAC5B,IAAI;QACF,MAAMwK,UAAU,GAAG,IAAI5B,QAAQ,EAAE;QACjC4B,UAAU,CAAC3B,MAAM,CAAC,OAAO,EAAE0B,MAAI,CAACzK,YAAY,CAAC;QAC7C,MAAMgJ,SAAS,SAAcyB,MAAI,CAAClN,IAAI,CAAC0L,IAAI,CAAC,qBAAqB,EAAEyB,UAAU,CAAC,CAACxB,SAAS,EAAE;QAC1F,MAAMyB,YAAY,GAAG;UACnBvB,KAAK,EAAE;YACLf,IAAI,EAAEW,SAAS,CAACX,IAAI;YACpBgB,GAAG,EAAEL,SAAS,CAACK;WAChB;UACDC,OAAO,EAAEmB,MAAI,CAACxK;SACf;QACD,MAAMwK,MAAI,CAAClN,IAAI,CAAC0L,IAAI,CAAC,cAAc,EAAE0B,YAAY,CAAC,CAACzB,SAAS,EAAE;QAC9DuB,MAAI,CAACrL,iBAAiB,GAAG,KAAK;QAC9BqL,MAAI,CAACrK,WAAW,EAAE;OACnB,CAAC,OAAOsH,GAAG,EAAE;QACZC,KAAK,CAAC,yBAAyB,CAAC;OACjC,SAAS;QACR8C,MAAI,CAACvK,gBAAgB,GAAG,KAAK;;IAC9B;EACH;EACA0K,kBAAkBA,CAAA;IAChB,IAAI,CAACxL,iBAAiB,GAAG,KAAK;EAChC;;;uBAtdWhC,uBAAuB,EAAA7B,EAAA,CAAAsP,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAxP,EAAA,CAAAsP,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA1P,EAAA,CAAAsP,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAvB/N,uBAAuB;MAAAgO,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UAAvBhQ,EAAA,CAAAiB,UAAA,qBAAAiP,mDAAAC,MAAA;YAAA,OAAAF,GAAA,CAAAjI,aAAA,CAAAmI,MAAA,CAAqB;UAAA,UAAAnQ,EAAA,CAAAoQ,iBAAA,CAAE;;;;;;;;;;UCxCpCpQ,EAAA,CAAAC,cAAA,aAA+B;UAU7BD,EARA,CAAAI,UAAA,IAAAiQ,sCAAA,iBAAsD,IAAAC,qDAAA,iCAYrD;UAkCHtQ,EAAA,CAAAG,YAAA,EAAM;;;UA9CEH,EAAA,CAAAM,SAAA,EAAsB;UAAtBN,EAAA,CAAAO,UAAA,SAAA0P,GAAA,CAAA/N,gBAAA,CAAsB;UAWzBlC,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAO,UAAA,UAAA0P,GAAA,CAAA/N,gBAAA,CAAuB;;;qBDuBhBtC,YAAY,EAAA2Q,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE5Q,WAAW,EAAEE,mBAAmB,EAAA2Q,EAAA,CAAAC,sBAAA,EAAAD,EAAA,CAAAE,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}