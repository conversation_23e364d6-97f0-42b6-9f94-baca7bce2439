{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Validators, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../../../core/services/notification.service\";\nimport * as i5 from \"@angular/common\";\nfunction LoginComponent_div_15_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_15_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Please enter a valid email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, LoginComponent_div_15_span_1_Template, 2, 0, \"span\", 19)(2, LoginComponent_div_15_span_2_Template, 2, 0, \"span\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.loginForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.loginForm.get(\"email\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"email\"]);\n  }\n}\nfunction LoginComponent_div_18_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_18_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password must be at least 6 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, LoginComponent_div_18_span_1_Template, 2, 0, \"span\", 19)(2, LoginComponent_div_18_span_2_Template, 2, 0, \"span\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.loginForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.loginForm.get(\"password\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"minlength\"]);\n  }\n}\nfunction LoginComponent_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n}\nfunction LoginComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.errorMessage, \" \");\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(fb, authService, router, notificationService) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.notificationService = notificationService;\n      this.loading = false;\n      this.errorMessage = '';\n      this.loginForm = this.fb.group({\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', [Validators.required, Validators.minLength(6)]],\n        rememberMe: [false]\n      });\n    }\n    onSubmit() {\n      if (this.loginForm.valid) {\n        this.loading = true;\n        this.errorMessage = '';\n        // Trim whitespace from form values\n        const formData = {\n          ...this.loginForm.value,\n          email: this.loginForm.value.email?.trim(),\n          password: this.loginForm.value.password?.trim()\n        };\n        this.authService.login(formData).subscribe({\n          next: response => {\n            this.loading = false;\n            this.notificationService.success('Login Successful!', `Welcome back, ${response.user.fullName}!`);\n            // Role-based redirect\n            if (response.user.role === 'admin') {\n              this.router.navigate(['/admin']);\n            } else if (response.user.role === 'vendor') {\n              this.router.navigate(['/vendor/dashboard']);\n            } else {\n              this.router.navigate(['/home']);\n            }\n          },\n          error: error => {\n            this.loading = false;\n            this.errorMessage = error.error?.message || 'Invalid credentials. Please check your email and password.';\n            this.notificationService.error('Login Failed', 'Please check your credentials and try again.');\n          }\n        });\n      }\n    }\n    static {\n      this.ɵfac = function LoginComponent_Factory(t) {\n        return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.NotificationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoginComponent,\n        selectors: [[\"app-login\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 33,\n        vars: 11,\n        consts: [[1, \"auth-container\"], [1, \"auth-card\"], [1, \"logo\"], [1, \"gradient-text\"], [1, \"login-header\"], [1, \"auth-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"form-group\"], [\"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Email\", 1, \"form-control\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"type\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Password\", 1, \"form-control\"], [1, \"form-group\", \"remember-me\"], [1, \"checkbox-label\"], [\"type\", \"checkbox\", \"formControlName\", \"rememberMe\"], [1, \"checkmark\"], [\"type\", \"submit\", 1, \"btn-primary\", \"auth-btn\", 3, \"disabled\"], [\"class\", \"loading-spinner\", 4, \"ngIf\"], [1, \"auth-link\"], [\"routerLink\", \"/auth/register\"], [1, \"error-message\"], [4, \"ngIf\"], [1, \"loading-spinner\"]],\n        template: function LoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n            i0.ɵɵtext(4, \"DFashion\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\");\n            i0.ɵɵtext(6, \"Social E-commerce Platform\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 4)(8, \"h3\");\n            i0.ɵɵtext(9, \"Welcome Back\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"p\");\n            i0.ɵɵtext(11, \"Sign in to your account\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(12, \"form\", 5);\n            i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_12_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(13, \"div\", 6);\n            i0.ɵɵelement(14, \"input\", 7);\n            i0.ɵɵtemplate(15, LoginComponent_div_15_Template, 3, 2, \"div\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"div\", 6);\n            i0.ɵɵelement(17, \"input\", 9);\n            i0.ɵɵtemplate(18, LoginComponent_div_18_Template, 3, 2, \"div\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"div\", 10)(20, \"label\", 11);\n            i0.ɵɵelement(21, \"input\", 12)(22, \"span\", 13);\n            i0.ɵɵtext(23, \" Remember me \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"button\", 14);\n            i0.ɵɵtemplate(25, LoginComponent_span_25_Template, 1, 0, \"span\", 15);\n            i0.ɵɵtext(26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(27, LoginComponent_div_27_Template, 2, 1, \"div\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"div\", 16)(29, \"p\");\n            i0.ɵɵtext(30, \"Don't have an account? \");\n            i0.ɵɵelementStart(31, \"a\", 17);\n            i0.ɵɵtext(32, \"Sign up\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            let tmp_1_0;\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_4_0;\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"error\", ((tmp_1_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_1_0.touched));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"error\", ((tmp_3_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_4_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Signing in...\" : \"Sign In\", \" \");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          }\n        },\n        dependencies: [CommonModule, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, RouterModule, i3.RouterLink],\n        styles: [\".auth-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#667eea,#764ba2);padding:20px}.auth-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:40px;box-shadow:0 10px 25px #0000001a;width:100%;max-width:400px}.logo[_ngcontent-%COMP%]{text-align:center;margin-bottom:32px}.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:32px;font-weight:700;margin-bottom:8px}.logo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#8e8e8e;font-size:14px}.auth-form[_ngcontent-%COMP%]{margin-bottom:24px}.form-group[_ngcontent-%COMP%]{margin-bottom:20px}.form-control[_ngcontent-%COMP%]{width:100%;padding:12px 16px;border:1px solid #dbdbdb;border-radius:8px;font-size:14px;outline:none;transition:all .2s}.form-control[_ngcontent-%COMP%]:focus{border-color:var(--primary-color);box-shadow:0 0 0 3px #0095f61a}.form-control.error[_ngcontent-%COMP%]{border-color:#ef4444}.error-message[_ngcontent-%COMP%]{color:#ef4444;font-size:12px;margin-top:4px}.auth-btn[_ngcontent-%COMP%]{width:100%;padding:12px;font-size:16px;font-weight:600;margin-bottom:16px;display:flex;align-items:center;justify-content:center;gap:8px}.auth-btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.login-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:32px}.login-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:24px;font-weight:600;margin-bottom:8px;color:#262626}.login-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#8e8e8e;font-size:14px}.remember-me[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:24px}.checkbox-label[_ngcontent-%COMP%]{display:flex;align-items:center;cursor:pointer;font-size:14px;color:#262626}.checkbox-label[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]{margin-right:8px;width:16px;height:16px;accent-color:var(--primary-color)}.checkmark[_ngcontent-%COMP%]{margin-right:8px}.auth-link[_ngcontent-%COMP%]{text-align:center}.auth-link[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:#8e8e8e}.auth-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:var(--primary-color);text-decoration:none;font-weight:600}.auth-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}@media (max-width: 480px){.auth-card[_ngcontent-%COMP%]{padding:24px}.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:28px}}\"]\n      });\n    }\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}