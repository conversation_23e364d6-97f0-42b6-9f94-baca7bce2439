import { Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';

export interface AppError {
  id: string;
  type: 'network' | 'validation' | 'authentication' | 'authorization' | 'server' | 'client' | 'unknown';
  message: string;
  details?: any;
  timestamp: Date;
  context?: string;
  retryable: boolean;
  userFriendly: boolean;
}

export interface ErrorState {
  hasError: boolean;
  errors: AppError[];
  lastError?: AppError;
}

@Injectable({
  providedIn: 'root'
})
export class ErrorHandlerService {
  private errorStateSubject = new BehaviorSubject<ErrorState>({
    hasError: false,
    errors: [],
    lastError: undefined
  });

  public errorState$ = this.errorStateSubject.asObservable();

  constructor() {}

  // Handle HTTP errors
  handleHttpError(error: HttpErrorResponse, context?: string): Observable<never> {
    const appError = this.createAppError(error, context);
    this.addError(appError);
    return throwError(() => appError);
  }

  // Handle general errors
  handleError(error: any, context?: string): AppError {
    const appError = this.createAppError(error, context);
    this.addError(appError);
    return appError;
  }

  // Create standardized error object
  private createAppError(error: any, context?: string): AppError {
    const errorId = this.generateErrorId();
    let appError: AppError;

    if (error instanceof HttpErrorResponse) {
      appError = {
        id: errorId,
        type: this.getHttpErrorType(error.status),
        message: this.getHttpErrorMessage(error),
        details: {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          body: error.error
        },
        timestamp: new Date(),
        context,
        retryable: this.isRetryableHttpError(error.status),
        userFriendly: this.isUserFriendlyHttpError(error.status)
      };
    } else if (error instanceof Error) {
      appError = {
        id: errorId,
        type: 'client',
        message: error.message,
        details: {
          name: error.name,
          stack: error.stack
        },
        timestamp: new Date(),
        context,
        retryable: false,
        userFriendly: false
      };
    } else {
      appError = {
        id: errorId,
        type: 'unknown',
        message: 'An unexpected error occurred',
        details: error,
        timestamp: new Date(),
        context,
        retryable: false,
        userFriendly: true
      };
    }

    return appError;
  }

  // Get error type based on HTTP status
  private getHttpErrorType(status: number): AppError['type'] {
    if (status >= 400 && status < 500) {
      if (status === 401) return 'authentication';
      if (status === 403) return 'authorization';
      if (status === 422) return 'validation';
      return 'client';
    } else if (status >= 500) {
      return 'server';
    } else if (status === 0) {
      return 'network';
    }
    return 'unknown';
  }

  // Get user-friendly error message
  private getHttpErrorMessage(error: HttpErrorResponse): string {
    // Try to extract message from error response
    if (error.error?.message) {
      return error.error.message;
    }

    // Default messages based on status code
    switch (error.status) {
      case 0:
        return 'Unable to connect to the server. Please check your internet connection.';
      case 400:
        return 'Invalid request. Please check your input and try again.';
      case 401:
        return 'You need to log in to access this resource.';
      case 403:
        return 'You do not have permission to access this resource.';
      case 404:
        return 'The requested resource was not found.';
      case 422:
        return 'Please check your input and try again.';
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      case 500:
        return 'A server error occurred. Please try again later.';
      case 502:
        return 'Service temporarily unavailable. Please try again later.';
      case 503:
        return 'Service temporarily unavailable. Please try again later.';
      default:
        return `An error occurred (${error.status}). Please try again.`;
    }
  }

  // Check if HTTP error is retryable
  private isRetryableHttpError(status: number): boolean {
    return [0, 408, 429, 500, 502, 503, 504].includes(status);
  }

  // Check if HTTP error should show user-friendly message
  private isUserFriendlyHttpError(status: number): boolean {
    return status >= 400 && status < 500;
  }

  // Add error to state
  private addError(error: AppError): void {
    const currentState = this.errorStateSubject.value;
    const newErrors = [...currentState.errors, error];
    
    // Keep only last 10 errors
    if (newErrors.length > 10) {
      newErrors.splice(0, newErrors.length - 10);
    }

    this.errorStateSubject.next({
      hasError: true,
      errors: newErrors,
      lastError: error
    });

    // Log error for debugging
    console.error('App Error:', error);
  }

  // Clear specific error
  clearError(errorId: string): void {
    const currentState = this.errorStateSubject.value;
    const filteredErrors = currentState.errors.filter(e => e.id !== errorId);
    
    this.errorStateSubject.next({
      hasError: filteredErrors.length > 0,
      errors: filteredErrors,
      lastError: filteredErrors[filteredErrors.length - 1]
    });
  }

  // Clear all errors
  clearAllErrors(): void {
    this.errorStateSubject.next({
      hasError: false,
      errors: [],
      lastError: undefined
    });
  }

  // Get current error state
  getCurrentErrorState(): ErrorState {
    return this.errorStateSubject.value;
  }

  // Check if there are any errors
  hasErrors(): boolean {
    return this.errorStateSubject.value.hasError;
  }

  // Get errors by type
  getErrorsByType(type: AppError['type']): AppError[] {
    return this.errorStateSubject.value.errors.filter(e => e.type === type);
  }

  // Get retryable errors
  getRetryableErrors(): AppError[] {
    return this.errorStateSubject.value.errors.filter(e => e.retryable);
  }

  // Generate unique error ID
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Show user notification for error
  showErrorNotification(error: AppError): void {
    if (error.userFriendly) {
      // Show user-friendly notification
      this.showToast(error.message, 'error');
    } else {
      // Show generic error message
      this.showToast('An unexpected error occurred. Please try again.', 'error');
    }
  }

  // Simple toast notification (can be replaced with proper toast service)
  private showToast(message: string, type: 'error' | 'warning' | 'info'): void {
    // This is a simple implementation - replace with your preferred toast library
    console.log(`${type.toUpperCase()}: ${message}`);
    
    // You can integrate with libraries like:
    // - Angular Material Snackbar
    // - ngx-toastr
    // - Custom toast component
  }

  // Retry mechanism for retryable errors
  retryOperation<T>(operation: () => Observable<T>, maxRetries: number = 3, delay: number = 1000): Observable<T> {
    return new Observable(observer => {
      let retryCount = 0;
      
      const attemptOperation = () => {
        operation().subscribe({
          next: (value) => observer.next(value),
          complete: () => observer.complete(),
          error: (error) => {
            const appError = this.createAppError(error);
            
            if (appError.retryable && retryCount < maxRetries) {
              retryCount++;
              console.log(`Retrying operation (attempt ${retryCount}/${maxRetries})`);
              setTimeout(attemptOperation, delay * retryCount);
            } else {
              this.addError(appError);
              observer.error(appError);
            }
          }
        });
      };
      
      attemptOperation();
    });
  }
}
