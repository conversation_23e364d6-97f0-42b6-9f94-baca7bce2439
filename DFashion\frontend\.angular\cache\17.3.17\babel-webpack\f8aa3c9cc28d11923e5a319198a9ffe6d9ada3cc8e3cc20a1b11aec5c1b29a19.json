{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Validators, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nlet LoginComponent = class LoginComponent {\n  constructor(fb, authService, router) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.loading = false;\n    this.errorMessage = '';\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.loading = true;\n      this.errorMessage = '';\n      this.authService.login(this.loginForm.value).subscribe({\n        next: response => {\n          this.loading = false;\n          this.router.navigate(['/home']);\n        },\n        error: error => {\n          this.loading = false;\n          this.errorMessage = error.error?.message || 'Invalid credentials. Try demo accounts below.';\n        }\n      });\n    }\n  }\n  loginAsDemo(role) {\n    const demoCredentials = {\n      customer: {\n        email: '<EMAIL>',\n        password: 'password123'\n      },\n      vendor: {\n        email: '<EMAIL>',\n        password: 'password123'\n      },\n      admin: {\n        email: '<EMAIL>',\n        password: 'admin123'\n      }\n    };\n    const credentials = demoCredentials[role];\n    this.loginForm.patchValue(credentials);\n    this.onSubmit();\n  }\n};\nLoginComponent = __decorate([Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, RouterModule],\n  template: `\n    <div class=\"auth-container\">\n      <div class=\"auth-card\">\n        <!-- Logo -->\n        <div class=\"logo\">\n          <h1 class=\"gradient-text\">DFashion</h1>\n          <p>Social E-commerce Platform</p>\n        </div>\n\n        <!-- Login Form -->\n        <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"auth-form\">\n          <div class=\"form-group\">\n            <input\n              type=\"email\"\n              formControlName=\"email\"\n              placeholder=\"Email\"\n              class=\"form-control\"\n              [class.error]=\"loginForm.get('email')?.invalid && loginForm.get('email')?.touched\"\n            >\n            <div *ngIf=\"loginForm.get('email')?.invalid && loginForm.get('email')?.touched\" class=\"error-message\">\n              <span *ngIf=\"loginForm.get('email')?.errors?.['required']\">Email is required</span>\n              <span *ngIf=\"loginForm.get('email')?.errors?.['email']\">Please enter a valid email</span>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <input\n              type=\"password\"\n              formControlName=\"password\"\n              placeholder=\"Password\"\n              class=\"form-control\"\n              [class.error]=\"loginForm.get('password')?.invalid && loginForm.get('password')?.touched\"\n            >\n            <div *ngIf=\"loginForm.get('password')?.invalid && loginForm.get('password')?.touched\" class=\"error-message\">\n              <span *ngIf=\"loginForm.get('password')?.errors?.['required']\">Password is required</span>\n              <span *ngIf=\"loginForm.get('password')?.errors?.['minlength']\">Password must be at least 6 characters</span>\n            </div>\n          </div>\n\n          <button \n            type=\"submit\" \n            class=\"btn-primary auth-btn\"\n            [disabled]=\"loginForm.invalid || loading\"\n          >\n            <span *ngIf=\"loading\" class=\"loading-spinner\"></span>\n            {{ loading ? 'Signing in...' : 'Sign In' }}\n          </button>\n\n          <div *ngIf=\"errorMessage\" class=\"error-message\">\n            {{ errorMessage }}\n          </div>\n        </form>\n\n        <!-- Demo Accounts -->\n        <div class=\"demo-accounts\">\n          <h4>Demo Accounts (Backend Not Required)</h4>\n          <p class=\"demo-note\">Click to auto-fill credentials or use them manually:</p>\n          <div class=\"demo-buttons\">\n            <button (click)=\"loginAsDemo('customer')\" class=\"demo-btn\">\n              <i class=\"fas fa-user\"></i>\n              <div class=\"demo-info\">\n                <strong>Customer Demo</strong>\n                <small><EMAIL> / password123</small>\n              </div>\n            </button>\n            <button (click)=\"loginAsDemo('vendor')\" class=\"demo-btn\">\n              <i class=\"fas fa-store\"></i>\n              <div class=\"demo-info\">\n                <strong>Vendor Demo</strong>\n                <small><EMAIL> / password123</small>\n              </div>\n            </button>\n            <button (click)=\"loginAsDemo('admin')\" class=\"demo-btn\">\n              <i class=\"fas fa-shield-alt\"></i>\n              <div class=\"demo-info\">\n                <strong>Admin Demo</strong>\n                <small><EMAIL> / admin123</small>\n              </div>\n            </button>\n          </div>\n          <div class=\"backend-status\">\n            <p><i class=\"fas fa-info-circle\"></i> Backend not running - using demo mode</p>\n          </div>\n        </div>\n\n        <!-- Register Link -->\n        <div class=\"auth-link\">\n          <p>Don't have an account? <a routerLink=\"/auth/register\">Sign up</a></p>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .auth-container {\n      min-height: 100vh;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 20px;\n    }\n\n    .auth-card {\n      background: #fff;\n      border-radius: 12px;\n      padding: 40px;\n      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n      width: 100%;\n      max-width: 400px;\n    }\n\n    .logo {\n      text-align: center;\n      margin-bottom: 32px;\n    }\n\n    .logo h1 {\n      font-size: 32px;\n      font-weight: 700;\n      margin-bottom: 8px;\n    }\n\n    .logo p {\n      color: #8e8e8e;\n      font-size: 14px;\n    }\n\n    .auth-form {\n      margin-bottom: 24px;\n    }\n\n    .form-group {\n      margin-bottom: 20px;\n    }\n\n    .form-control {\n      width: 100%;\n      padding: 12px 16px;\n      border: 1px solid #dbdbdb;\n      border-radius: 8px;\n      font-size: 14px;\n      outline: none;\n      transition: all 0.2s;\n    }\n\n    .form-control:focus {\n      border-color: var(--primary-color);\n      box-shadow: 0 0 0 3px rgba(0, 149, 246, 0.1);\n    }\n\n    .form-control.error {\n      border-color: #ef4444;\n    }\n\n    .error-message {\n      color: #ef4444;\n      font-size: 12px;\n      margin-top: 4px;\n    }\n\n    .auth-btn {\n      width: 100%;\n      padding: 12px;\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 16px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 8px;\n    }\n\n    .auth-btn:disabled {\n      opacity: 0.6;\n      cursor: not-allowed;\n    }\n\n    .demo-accounts {\n      margin-bottom: 24px;\n      padding: 20px;\n      background: #f8fafc;\n      border-radius: 8px;\n    }\n\n    .demo-accounts h4 {\n      font-size: 14px;\n      font-weight: 600;\n      margin-bottom: 8px;\n      text-align: center;\n      color: #64748b;\n    }\n\n    .demo-note {\n      font-size: 12px;\n      color: #8e8e8e;\n      text-align: center;\n      margin-bottom: 12px;\n    }\n\n    .demo-buttons {\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n      margin-bottom: 12px;\n    }\n\n    .demo-btn {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      padding: 12px;\n      background: #fff;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      cursor: pointer;\n      transition: all 0.2s;\n      text-align: left;\n    }\n\n    .demo-btn:hover {\n      background: #f1f5f9;\n      border-color: var(--primary-color);\n      transform: translateY(-1px);\n    }\n\n    .demo-btn i {\n      font-size: 16px;\n      color: var(--primary-color);\n      min-width: 16px;\n    }\n\n    .demo-info {\n      flex: 1;\n    }\n\n    .demo-info strong {\n      display: block;\n      font-size: 13px;\n      color: #262626;\n      margin-bottom: 2px;\n    }\n\n    .demo-info small {\n      font-size: 11px;\n      color: #8e8e8e;\n      font-family: monospace;\n    }\n\n    .backend-status {\n      padding: 8px;\n      background: #fef3c7;\n      border: 1px solid #f59e0b;\n      border-radius: 6px;\n      text-align: center;\n    }\n\n    .backend-status p {\n      font-size: 11px;\n      color: #92400e;\n      margin: 0;\n    }\n\n    .backend-status i {\n      margin-right: 4px;\n    }\n\n    .auth-link {\n      text-align: center;\n    }\n\n    .auth-link p {\n      font-size: 14px;\n      color: #8e8e8e;\n    }\n\n    .auth-link a {\n      color: var(--primary-color);\n      text-decoration: none;\n      font-weight: 600;\n    }\n\n    .auth-link a:hover {\n      text-decoration: underline;\n    }\n\n    @media (max-width: 480px) {\n      .auth-card {\n        padding: 24px;\n      }\n\n      .logo h1 {\n        font-size: 28px;\n      }\n    }\n  `]\n})], LoginComponent);\nexport { LoginComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "Validators", "ReactiveFormsModule", "RouterModule", "LoginComponent", "constructor", "fb", "authService", "router", "loading", "errorMessage", "loginForm", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "onSubmit", "valid", "login", "value", "subscribe", "next", "response", "navigate", "error", "message", "loginAsDemo", "role", "demoCredentials", "customer", "vendor", "admin", "credentials", "patchValue", "__decorate", "selector", "standalone", "imports", "template", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\auth\\pages\\login\\login.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { Router, RouterModule } from '@angular/router';\n\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\n\n@Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, RouterModule],\n  template: `\n    <div class=\"auth-container\">\n      <div class=\"auth-card\">\n        <!-- Logo -->\n        <div class=\"logo\">\n          <h1 class=\"gradient-text\">DFashion</h1>\n          <p>Social E-commerce Platform</p>\n        </div>\n\n        <!-- Login Form -->\n        <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"auth-form\">\n          <div class=\"form-group\">\n            <input\n              type=\"email\"\n              formControlName=\"email\"\n              placeholder=\"Email\"\n              class=\"form-control\"\n              [class.error]=\"loginForm.get('email')?.invalid && loginForm.get('email')?.touched\"\n            >\n            <div *ngIf=\"loginForm.get('email')?.invalid && loginForm.get('email')?.touched\" class=\"error-message\">\n              <span *ngIf=\"loginForm.get('email')?.errors?.['required']\">Email is required</span>\n              <span *ngIf=\"loginForm.get('email')?.errors?.['email']\">Please enter a valid email</span>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <input\n              type=\"password\"\n              formControlName=\"password\"\n              placeholder=\"Password\"\n              class=\"form-control\"\n              [class.error]=\"loginForm.get('password')?.invalid && loginForm.get('password')?.touched\"\n            >\n            <div *ngIf=\"loginForm.get('password')?.invalid && loginForm.get('password')?.touched\" class=\"error-message\">\n              <span *ngIf=\"loginForm.get('password')?.errors?.['required']\">Password is required</span>\n              <span *ngIf=\"loginForm.get('password')?.errors?.['minlength']\">Password must be at least 6 characters</span>\n            </div>\n          </div>\n\n          <button \n            type=\"submit\" \n            class=\"btn-primary auth-btn\"\n            [disabled]=\"loginForm.invalid || loading\"\n          >\n            <span *ngIf=\"loading\" class=\"loading-spinner\"></span>\n            {{ loading ? 'Signing in...' : 'Sign In' }}\n          </button>\n\n          <div *ngIf=\"errorMessage\" class=\"error-message\">\n            {{ errorMessage }}\n          </div>\n        </form>\n\n        <!-- Demo Accounts -->\n        <div class=\"demo-accounts\">\n          <h4>Demo Accounts (Backend Not Required)</h4>\n          <p class=\"demo-note\">Click to auto-fill credentials or use them manually:</p>\n          <div class=\"demo-buttons\">\n            <button (click)=\"loginAsDemo('customer')\" class=\"demo-btn\">\n              <i class=\"fas fa-user\"></i>\n              <div class=\"demo-info\">\n                <strong>Customer Demo</strong>\n                <small><EMAIL> / password123</small>\n              </div>\n            </button>\n            <button (click)=\"loginAsDemo('vendor')\" class=\"demo-btn\">\n              <i class=\"fas fa-store\"></i>\n              <div class=\"demo-info\">\n                <strong>Vendor Demo</strong>\n                <small><EMAIL> / password123</small>\n              </div>\n            </button>\n            <button (click)=\"loginAsDemo('admin')\" class=\"demo-btn\">\n              <i class=\"fas fa-shield-alt\"></i>\n              <div class=\"demo-info\">\n                <strong>Admin Demo</strong>\n                <small><EMAIL> / admin123</small>\n              </div>\n            </button>\n          </div>\n          <div class=\"backend-status\">\n            <p><i class=\"fas fa-info-circle\"></i> Backend not running - using demo mode</p>\n          </div>\n        </div>\n\n        <!-- Register Link -->\n        <div class=\"auth-link\">\n          <p>Don't have an account? <a routerLink=\"/auth/register\">Sign up</a></p>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .auth-container {\n      min-height: 100vh;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 20px;\n    }\n\n    .auth-card {\n      background: #fff;\n      border-radius: 12px;\n      padding: 40px;\n      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n      width: 100%;\n      max-width: 400px;\n    }\n\n    .logo {\n      text-align: center;\n      margin-bottom: 32px;\n    }\n\n    .logo h1 {\n      font-size: 32px;\n      font-weight: 700;\n      margin-bottom: 8px;\n    }\n\n    .logo p {\n      color: #8e8e8e;\n      font-size: 14px;\n    }\n\n    .auth-form {\n      margin-bottom: 24px;\n    }\n\n    .form-group {\n      margin-bottom: 20px;\n    }\n\n    .form-control {\n      width: 100%;\n      padding: 12px 16px;\n      border: 1px solid #dbdbdb;\n      border-radius: 8px;\n      font-size: 14px;\n      outline: none;\n      transition: all 0.2s;\n    }\n\n    .form-control:focus {\n      border-color: var(--primary-color);\n      box-shadow: 0 0 0 3px rgba(0, 149, 246, 0.1);\n    }\n\n    .form-control.error {\n      border-color: #ef4444;\n    }\n\n    .error-message {\n      color: #ef4444;\n      font-size: 12px;\n      margin-top: 4px;\n    }\n\n    .auth-btn {\n      width: 100%;\n      padding: 12px;\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 16px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 8px;\n    }\n\n    .auth-btn:disabled {\n      opacity: 0.6;\n      cursor: not-allowed;\n    }\n\n    .demo-accounts {\n      margin-bottom: 24px;\n      padding: 20px;\n      background: #f8fafc;\n      border-radius: 8px;\n    }\n\n    .demo-accounts h4 {\n      font-size: 14px;\n      font-weight: 600;\n      margin-bottom: 8px;\n      text-align: center;\n      color: #64748b;\n    }\n\n    .demo-note {\n      font-size: 12px;\n      color: #8e8e8e;\n      text-align: center;\n      margin-bottom: 12px;\n    }\n\n    .demo-buttons {\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n      margin-bottom: 12px;\n    }\n\n    .demo-btn {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      padding: 12px;\n      background: #fff;\n      border: 1px solid #e2e8f0;\n      border-radius: 6px;\n      cursor: pointer;\n      transition: all 0.2s;\n      text-align: left;\n    }\n\n    .demo-btn:hover {\n      background: #f1f5f9;\n      border-color: var(--primary-color);\n      transform: translateY(-1px);\n    }\n\n    .demo-btn i {\n      font-size: 16px;\n      color: var(--primary-color);\n      min-width: 16px;\n    }\n\n    .demo-info {\n      flex: 1;\n    }\n\n    .demo-info strong {\n      display: block;\n      font-size: 13px;\n      color: #262626;\n      margin-bottom: 2px;\n    }\n\n    .demo-info small {\n      font-size: 11px;\n      color: #8e8e8e;\n      font-family: monospace;\n    }\n\n    .backend-status {\n      padding: 8px;\n      background: #fef3c7;\n      border: 1px solid #f59e0b;\n      border-radius: 6px;\n      text-align: center;\n    }\n\n    .backend-status p {\n      font-size: 11px;\n      color: #92400e;\n      margin: 0;\n    }\n\n    .backend-status i {\n      margin-right: 4px;\n    }\n\n    .auth-link {\n      text-align: center;\n    }\n\n    .auth-link p {\n      font-size: 14px;\n      color: #8e8e8e;\n    }\n\n    .auth-link a {\n      color: var(--primary-color);\n      text-decoration: none;\n      font-weight: 600;\n    }\n\n    .auth-link a:hover {\n      text-decoration: underline;\n    }\n\n    @media (max-width: 480px) {\n      .auth-card {\n        padding: 24px;\n      }\n\n      .logo h1 {\n        font-size: 28px;\n      }\n    }\n  `]\n})\nexport class LoginComponent {\n  loginForm: FormGroup;\n  loading = false;\n  errorMessage = '';\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router\n  ) {\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.loading = true;\n      this.errorMessage = '';\n\n      this.authService.login(this.loginForm.value).subscribe({\n        next: (response) => {\n          this.loading = false;\n          this.router.navigate(['/home']);\n        },\n        error: (error) => {\n          this.loading = false;\n          this.errorMessage = error.error?.message || 'Invalid credentials. Try demo accounts below.';\n        }\n      });\n    }\n  }\n\n  loginAsDemo(role: 'customer' | 'vendor' | 'admin') {\n    const demoCredentials = {\n      customer: { email: '<EMAIL>', password: 'password123' },\n      vendor: { email: '<EMAIL>', password: 'password123' },\n      admin: { email: '<EMAIL>', password: 'admin123' }\n    };\n\n    const credentials = demoCredentials[role];\n    this.loginForm.patchValue(credentials);\n    this.onSubmit();\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAAiCC,UAAU,EAAEC,mBAAmB,QAAQ,gBAAgB;AACxF,SAAiBC,YAAY,QAAQ,iBAAiB;AAiT/C,IAAMC,cAAc,GAApB,MAAMA,cAAc;EAKzBC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IANhB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,YAAY,GAAG,EAAE;IAOf,IAAI,CAACC,SAAS,GAAG,IAAI,CAACL,EAAE,CAACM,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACZ,UAAU,CAACa,QAAQ,EAAEb,UAAU,CAACY,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACd,UAAU,CAACa,QAAQ,EAAEb,UAAU,CAACe,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACN,SAAS,CAACO,KAAK,EAAE;MACxB,IAAI,CAACT,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,YAAY,GAAG,EAAE;MAEtB,IAAI,CAACH,WAAW,CAACY,KAAK,CAAC,IAAI,CAACR,SAAS,CAACS,KAAK,CAAC,CAACC,SAAS,CAAC;QACrDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACd,OAAO,GAAG,KAAK;UACpB,IAAI,CAACD,MAAM,CAACgB,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChB,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,YAAY,GAAGe,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,+CAA+C;QAC7F;OACD,CAAC;;EAEN;EAEAC,WAAWA,CAACC,IAAqC;IAC/C,MAAMC,eAAe,GAAG;MACtBC,QAAQ,EAAE;QAAEjB,KAAK,EAAE,kBAAkB;QAAEE,QAAQ,EAAE;MAAa,CAAE;MAChEgB,MAAM,EAAE;QAAElB,KAAK,EAAE,iBAAiB;QAAEE,QAAQ,EAAE;MAAa,CAAE;MAC7DiB,KAAK,EAAE;QAAEnB,KAAK,EAAE,oBAAoB;QAAEE,QAAQ,EAAE;MAAU;KAC3D;IAED,MAAMkB,WAAW,GAAGJ,eAAe,CAACD,IAAI,CAAC;IACzC,IAAI,CAACjB,SAAS,CAACuB,UAAU,CAACD,WAAW,CAAC;IACtC,IAAI,CAAChB,QAAQ,EAAE;EACjB;CACD;AA7CYb,cAAc,GAAA+B,UAAA,EA5S1BpC,SAAS,CAAC;EACTqC,QAAQ,EAAE,WAAW;EACrBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACtC,YAAY,EAAEE,mBAAmB,EAAEC,YAAY,CAAC;EAC1DoC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2FT;EACDC,MAAM,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0MR;CACF,CAAC,C,EACWpC,cAAc,CA6C1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}