{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/admin-api.service\";\nimport * as i2 from \"../services/admin-auth.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/progress-spinner\";\nfunction AdminDashboardComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"mat-spinner\", 4);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading dashboard data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AdminDashboardComponent_div_2_mat_card_14_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const stat_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(stat_r3.prefix);\n  }\n}\nfunction AdminDashboardComponent_div_2_mat_card_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 36)(1, \"mat-card-content\")(2, \"div\", 37)(3, \"div\", 38)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 39);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 40)(9, \"div\", 41);\n    i0.ɵɵtemplate(10, AdminDashboardComponent_div_2_mat_card_14_span_10_Template, 2, 1, \"span\", 42);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 43);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const stat_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"border-left-color\", stat_r3.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-color\", stat_r3.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r3.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getChangeClass(stat_r3.changeType));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", stat_r3.change, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", stat_r3.prefix);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", stat_r3.title === \"Revenue\" ? ctx_r1.formatCurrency(stat_r3.value) : ctx_r1.formatNumber(stat_r3.value), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r3.title);\n  }\n}\nfunction AdminDashboardComponent_div_2_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 46)(5, \"div\", 47);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 48);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const activity_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", activity_r4.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r4.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r4.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r4.time);\n  }\n}\nfunction AdminDashboardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"h1\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Here's what's happening with your business today.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 8)(9, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function AdminDashboardComponent_div_2_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.refreshData());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Refresh Data \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 10);\n    i0.ɵɵtemplate(14, AdminDashboardComponent_div_2_mat_card_14_Template, 14, 10, \"mat-card\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 13)(17, \"mat-card\", 14)(18, \"mat-card-header\")(19, \"mat-card-title\");\n    i0.ɵɵtext(20, \"User Growth\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"mat-card-subtitle\");\n    i0.ɵɵtext(22, \"Monthly new user registrations\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"mat-card-content\")(24, \"div\", 15)(25, \"div\", 16)(26, \"mat-icon\");\n    i0.ɵɵtext(27, \"show_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"h3\");\n    i0.ɵɵtext(29, \"User Growth Chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"p\");\n    i0.ɵɵtext(31, \"Chart visualization will be displayed here\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 17)(33, \"small\");\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(35, \"mat-card\", 14)(36, \"mat-card-header\")(37, \"mat-card-title\");\n    i0.ɵɵtext(38, \"Order Trends\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"mat-card-subtitle\");\n    i0.ɵɵtext(40, \"Daily orders this week\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"mat-card-content\")(42, \"div\", 15)(43, \"div\", 16)(44, \"mat-icon\");\n    i0.ɵɵtext(45, \"bar_chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"h3\");\n    i0.ɵɵtext(47, \"Order Trends Chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"p\");\n    i0.ɵɵtext(49, \"Chart visualization will be displayed here\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 17)(51, \"small\");\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(53, \"mat-card\", 14)(54, \"mat-card-header\")(55, \"mat-card-title\");\n    i0.ɵɵtext(56, \"Revenue Distribution\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"mat-card-subtitle\");\n    i0.ɵɵtext(58, \"Revenue by category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(59, \"mat-card-content\")(60, \"div\", 15)(61, \"div\", 16)(62, \"mat-icon\");\n    i0.ɵɵtext(63, \"donut_large\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"h3\");\n    i0.ɵɵtext(65, \"Revenue Distribution Chart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"p\");\n    i0.ɵɵtext(67, \"Chart visualization will be displayed here\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"div\", 17)(69, \"small\");\n    i0.ɵɵtext(70);\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵelementStart(71, \"div\", 18)(72, \"mat-card\", 19)(73, \"mat-card-header\")(74, \"mat-card-title\");\n    i0.ɵɵtext(75, \"Recent Activities\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"mat-card-subtitle\");\n    i0.ɵɵtext(77, \"Latest system activities\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(78, \"mat-card-content\")(79, \"div\", 20);\n    i0.ɵɵtemplate(80, AdminDashboardComponent_div_2_div_80_Template, 9, 5, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"div\", 22)(82, \"button\", 23);\n    i0.ɵɵtext(83, \"View All Activities\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(84, \"mat-card\", 24)(85, \"mat-card-header\")(86, \"mat-card-title\");\n    i0.ɵɵtext(87, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"mat-card-subtitle\");\n    i0.ɵɵtext(89, \"Common administrative tasks\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(90, \"mat-card-content\")(91, \"div\", 25)(92, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function AdminDashboardComponent_div_2_Template_button_click_92_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToSection(\"users\"));\n    });\n    i0.ɵɵelementStart(93, \"mat-icon\");\n    i0.ɵɵtext(94, \"person_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(95, \"span\");\n    i0.ɵɵtext(96, \"Add User\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(97, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function AdminDashboardComponent_div_2_Template_button_click_97_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToSection(\"products\"));\n    });\n    i0.ɵɵelementStart(98, \"mat-icon\");\n    i0.ɵɵtext(99, \"add_box\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"span\");\n    i0.ɵɵtext(101, \"Add Product\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(102, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function AdminDashboardComponent_div_2_Template_button_click_102_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToSection(\"orders\"));\n    });\n    i0.ɵɵelementStart(103, \"mat-icon\");\n    i0.ɵɵtext(104, \"list_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(105, \"span\");\n    i0.ɵɵtext(106, \"View Orders\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(107, \"button\", 27)(108, \"mat-icon\");\n    i0.ɵɵtext(109, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(110, \"span\");\n    i0.ɵɵtext(111, \"View Reports\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(112, \"button\", 27)(113, \"mat-icon\");\n    i0.ɵɵtext(114, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(115, \"span\");\n    i0.ɵɵtext(116, \"Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(117, \"button\", 27)(118, \"mat-icon\");\n    i0.ɵɵtext(119, \"help\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(120, \"span\");\n    i0.ɵɵtext(121, \"Help Center\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(122, \"mat-card\", 28)(123, \"mat-card-header\")(124, \"mat-card-title\");\n    i0.ɵɵtext(125, \"System Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(126, \"mat-card-subtitle\");\n    i0.ɵɵtext(127, \"Current system health and performance\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(128, \"mat-card-content\")(129, \"div\", 29)(130, \"div\", 30);\n    i0.ɵɵelement(131, \"div\", 31);\n    i0.ɵɵelementStart(132, \"div\", 32)(133, \"div\", 33);\n    i0.ɵɵtext(134, \"API Server\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(135, \"div\", 34);\n    i0.ɵɵtext(136, \"Online\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(137, \"div\", 30);\n    i0.ɵɵelement(138, \"div\", 31);\n    i0.ɵɵelementStart(139, \"div\", 32)(140, \"div\", 33);\n    i0.ɵɵtext(141, \"Database\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(142, \"div\", 34);\n    i0.ɵɵtext(143, \"Connected\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(144, \"div\", 30);\n    i0.ɵɵelement(145, \"div\", 35);\n    i0.ɵɵelementStart(146, \"div\", 32)(147, \"div\", 33);\n    i0.ɵɵtext(148, \"Storage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(149, \"div\", 34);\n    i0.ɵɵtext(150, \"85% Used\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(151, \"div\", 30);\n    i0.ɵɵelement(152, \"div\", 31);\n    i0.ɵɵelementStart(153, \"div\", 32)(154, \"div\", 33);\n    i0.ɵɵtext(155, \"Payment Gateway\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(156, \"div\", 34);\n    i0.ɵɵtext(157, \"Active\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Welcome back, \", (tmp_1_0 = i0.ɵɵpipeBind1(5, 6, ctx_r1.currentUser$)) == null ? null : tmp_1_0.fullName, \"!\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.quickStats);\n    i0.ɵɵadvance(20);\n    i0.ɵɵtextInterpolate1(\"Sample data: \", ctx_r1.userGrowthChartData.data.join(\", \"), \"\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate1(\"Sample data: \", ctx_r1.orderTrendsChartData.data.join(\", \"), \"\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate1(\"Sample data: \", ctx_r1.revenueChartData.data.join(\", \"), \"\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.recentActivities);\n  }\n}\n// Chart.js imports removed - using simple chart placeholders instead\nexport let AdminDashboardComponent = /*#__PURE__*/(() => {\n  class AdminDashboardComponent {\n    constructor(apiService, authService) {\n      this.apiService = apiService;\n      this.authService = authService;\n      this.destroy$ = new Subject();\n      this.isLoading = true;\n      this.dashboardStats = null;\n      this.currentUser$ = this.authService.currentUser$;\n      // Chart data (simplified without Chart.js dependency)\n      this.userGrowthChartData = {\n        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],\n        data: [65, 78, 90, 81, 95, 105]\n      };\n      this.orderTrendsChartData = {\n        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n        data: [12, 19, 15, 25, 22, 30, 28]\n      };\n      this.revenueChartData = {\n        labels: ['Products', 'Services', 'Subscriptions'],\n        data: [65, 25, 10]\n      };\n      // Chart options for template compatibility\n      this.chartOptions = {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          legend: {\n            position: 'bottom'\n          }\n        }\n      };\n      // Quick stats cards\n      this.quickStats = [{\n        title: 'Total Users',\n        value: 0,\n        change: '+12%',\n        changeType: 'positive',\n        icon: 'people',\n        color: '#2196f3'\n      }, {\n        title: 'Active Products',\n        value: 0,\n        change: '+8%',\n        changeType: 'positive',\n        icon: 'inventory',\n        color: '#4caf50'\n      }, {\n        title: 'Total Orders',\n        value: 0,\n        change: '+15%',\n        changeType: 'positive',\n        icon: 'shopping_cart',\n        color: '#ff9800'\n      }, {\n        title: 'Revenue',\n        value: 0,\n        change: '+23%',\n        changeType: 'positive',\n        icon: 'attach_money',\n        color: '#9c27b0',\n        prefix: '₹'\n      }];\n      // Recent activities\n      this.recentActivities = [{\n        type: 'order',\n        message: 'New order #DF12345 received',\n        time: '2 minutes ago',\n        icon: 'shopping_cart',\n        color: '#4caf50'\n      }, {\n        type: 'user',\n        message: 'New user registration: John Doe',\n        time: '5 minutes ago',\n        icon: 'person_add',\n        color: '#2196f3'\n      }, {\n        type: 'product',\n        message: 'Product \"Summer Dress\" approved',\n        time: '10 minutes ago',\n        icon: 'check_circle',\n        color: '#4caf50'\n      }, {\n        type: 'payment',\n        message: 'Payment of ₹2,500 received',\n        time: '15 minutes ago',\n        icon: 'payment',\n        color: '#9c27b0'\n      }];\n    }\n    ngOnInit() {\n      this.loadDashboardData();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    loadDashboardData() {\n      this.isLoading = true;\n      this.apiService.getDashboardStats().pipe(takeUntil(this.destroy$), finalize(() => this.isLoading = false)).subscribe({\n        next: stats => {\n          this.dashboardStats = stats;\n          this.updateQuickStats(stats);\n          this.updateCharts(stats);\n        },\n        error: error => {\n          console.error('Failed to load dashboard data:', error);\n          // Initialize empty dashboard data\n          this.dashboardStats = null;\n        }\n      });\n    }\n    updateQuickStats(stats) {\n      this.quickStats[0].value = stats.overview.users.total;\n      this.quickStats[1].value = stats.overview.products.active;\n      this.quickStats[2].value = stats.overview.orders.total;\n      this.quickStats[3].value = stats.revenue.totalRevenue;\n    }\n    updateCharts(stats) {\n      // Update chart data with actual stats\n      this.userGrowthChartData.data = [65, 78, 90, 81, 95, 105];\n      this.orderTrendsChartData.data = [12, 19, 15, 25, 22, 30, 28];\n      this.revenueChartData.data = [65, 25, 10];\n    }\n    formatCurrency(value) {\n      return new Intl.NumberFormat('en-IN', {\n        style: 'currency',\n        currency: 'INR',\n        minimumFractionDigits: 0\n      }).format(value);\n    }\n    formatNumber(value) {\n      return new Intl.NumberFormat('en-IN').format(value);\n    }\n    getChangeClass(changeType) {\n      return changeType === 'positive' ? 'positive-change' : 'negative-change';\n    }\n    refreshData() {\n      this.loadDashboardData();\n    }\n    navigateToSection(section) {\n      // Navigation logic based on user permissions\n      switch (section) {\n        case 'users':\n          if (this.authService.hasPermission('users', 'view')) {\n            // Navigate to users\n          }\n          break;\n        case 'products':\n          if (this.authService.hasPermission('products', 'view')) {\n            // Navigate to products\n          }\n          break;\n        case 'orders':\n          if (this.authService.hasPermission('orders', 'view')) {\n            // Navigate to orders\n          }\n          break;\n      }\n    }\n    static {\n      this.ɵfac = function AdminDashboardComponent_Factory(t) {\n        return new (t || AdminDashboardComponent)(i0.ɵɵdirectiveInject(i1.AdminApiService), i0.ɵɵdirectiveInject(i2.AdminAuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AdminDashboardComponent,\n        selectors: [[\"app-admin-dashboard\"]],\n        decls: 3,\n        vars: 2,\n        consts: [[1, \"dashboard-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"dashboard-content\", 4, \"ngIf\"], [1, \"loading-container\"], [\"diameter\", \"50\"], [1, \"dashboard-content\"], [1, \"welcome-section\"], [1, \"welcome-text\"], [1, \"welcome-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"stats-grid\"], [\"class\", \"stat-card\", 3, \"border-left-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"charts-section\"], [1, \"charts-grid\"], [1, \"chart-card\"], [1, \"chart-container\"], [1, \"chart-placeholder\"], [1, \"mock-data\"], [1, \"bottom-section\"], [1, \"activities-card\"], [1, \"activities-list\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"activities-footer\"], [\"mat-button\", \"\", \"color\", \"primary\"], [1, \"actions-card\"], [1, \"actions-grid\"], [\"mat-raised-button\", \"\", 1, \"action-button\", 3, \"click\"], [\"mat-raised-button\", \"\", 1, \"action-button\"], [1, \"status-card\"], [1, \"status-grid\"], [1, \"status-item\"], [1, \"status-indicator\", \"online\"], [1, \"status-info\"], [1, \"status-label\"], [1, \"status-value\"], [1, \"status-indicator\", \"warning\"], [1, \"stat-card\"], [1, \"stat-header\"], [1, \"stat-icon\"], [1, \"stat-change\", 3, \"ngClass\"], [1, \"stat-content\"], [1, \"stat-value\"], [4, \"ngIf\"], [1, \"stat-title\"], [1, \"activity-item\"], [1, \"activity-icon\"], [1, \"activity-content\"], [1, \"activity-message\"], [1, \"activity-time\"]],\n        template: function AdminDashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, AdminDashboardComponent_div_1_Template, 4, 0, \"div\", 1)(2, AdminDashboardComponent_div_2_Template, 158, 8, \"div\", 2);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          }\n        },\n        dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i4.MatIcon, i5.MatButton, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatProgressSpinner, i3.AsyncPipe],\n        styles: [\".dashboard-container[_ngcontent-%COMP%]{min-height:100%;background:#f5f5f5}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:400px;gap:1rem}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin:0}.dashboard-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:2rem}.welcome-section[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;padding:2rem;border-radius:12px;box-shadow:0 4px 12px #0000001a}.welcome-section[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:2rem;font-weight:600}.welcome-section[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;opacity:.9;font-size:1.1rem}.welcome-section[_ngcontent-%COMP%]   .welcome-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:#fff3;color:#fff;border:1px solid rgba(255,255,255,.3)}.welcome-section[_ngcontent-%COMP%]   .welcome-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background:#ffffff4d}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:1.5rem}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]{border-left:4px solid;transition:transform .2s ease,box-shadow .2s ease}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 24px #0000001a}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]{padding:1.5rem}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:12px;display:flex;align-items:center;justify-content:center;color:#fff}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.5rem}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]   .stat-change[_ngcontent-%COMP%]{font-size:.85rem;font-weight:600;padding:.25rem .5rem;border-radius:6px}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]   .stat-change.positive-change[_ngcontent-%COMP%]{background:#4caf501a;color:#4caf50}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]   .stat-change.negative-change[_ngcontent-%COMP%]{background:#f443361a;color:#f44336}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#333;margin-bottom:.25rem}.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-title[_ngcontent-%COMP%]{color:#666;font-size:.95rem;font-weight:500}.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(400px,1fr));gap:1.5rem}.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]{margin-bottom:1rem}.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#333}.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]{color:#666;margin-top:.25rem}.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]{height:300px;position:relative}.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;background:#f8f9fa;border-radius:8px;color:#666;text-align:center;padding:2rem}.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:4rem;width:4rem;height:4rem;margin-bottom:1rem;opacity:.5}.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:1.125rem;font-weight:500}.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 1rem;font-size:.875rem}.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   .mock-data[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:.75rem;color:#999}.bottom-section[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:1.5rem}.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;padding:1rem 0;border-bottom:1px solid #f0f0f0}.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff}.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.25rem}.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]{flex:1}.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-message[_ngcontent-%COMP%]{font-weight:500;color:#333;margin-bottom:.25rem}.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-time[_ngcontent-%COMP%]{color:#666;font-size:.85rem}.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-footer[_ngcontent-%COMP%]{text-align:center;margin-top:1rem;padding-top:1rem;border-top:1px solid #f0f0f0}.bottom-section[_ngcontent-%COMP%]   .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:1rem}.bottom-section[_ngcontent-%COMP%]   .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:.5rem;padding:1.5rem 1rem;height:auto;background:#f8f9fa;color:#333;border:1px solid #e9ecef}.bottom-section[_ngcontent-%COMP%]   .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]:hover{background:#e9ecef;transform:translateY(-1px)}.bottom-section[_ngcontent-%COMP%]   .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.5rem;width:1.5rem;height:1.5rem}.bottom-section[_ngcontent-%COMP%]   .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.85rem;font-weight:500}.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1.5rem}.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;padding:1rem;background:#f8f9fa;border-radius:8px}.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{width:12px;height:12px;border-radius:50%}.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-indicator.online[_ngcontent-%COMP%]{background:#4caf50}.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-indicator.warning[_ngcontent-%COMP%]{background:#ff9800}.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-indicator.error[_ngcontent-%COMP%]{background:#f44336}.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-info[_ngcontent-%COMP%]   .status-label[_ngcontent-%COMP%]{font-weight:500;color:#333;margin-bottom:.25rem}.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-info[_ngcontent-%COMP%]   .status-value[_ngcontent-%COMP%]{color:#666;font-size:.9rem}@media (max-width: 1200px){.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%], .bottom-section[_ngcontent-%COMP%]{grid-template-columns:1fr}}@media (max-width: 768px){.welcome-section[_ngcontent-%COMP%]{flex-direction:column;gap:1rem;text-align:center}.welcome-section[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem}.stats-grid[_ngcontent-%COMP%], .charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]{height:250px}.actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}}@media (max-width: 480px){.dashboard-content[_ngcontent-%COMP%]{gap:1rem}.welcome-section[_ngcontent-%COMP%]{padding:1.5rem}.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}  .mat-card{border-radius:12px!important;box-shadow:0 2px 8px #0000001a!important}  .mat-card-header{padding-bottom:0!important}  .mat-spinner circle{stroke:#667eea}\"]\n      });\n    }\n  }\n  return AdminDashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}