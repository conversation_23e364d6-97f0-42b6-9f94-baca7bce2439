{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nexport * from '../dist/esm/polyfills/index.js';\nexport * from '../dist/esm/loader.js';", "map": {"version": 3, "names": [], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@ionic/core/loader/index.es2017.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nexport * from '../dist/esm/polyfills/index.js';\nexport * from '../dist/esm/loader.js';\n"], "mappings": "AAAA;AACA;AACA;AACA,cAAc,gCAAgC;AAC9C,cAAc,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}