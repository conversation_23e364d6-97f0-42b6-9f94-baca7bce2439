{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let RoleManagementService = /*#__PURE__*/(() => {\n  class RoleManagementService {\n    constructor() {\n      this.currentUserRoleSubject = new BehaviorSubject(null);\n      this.currentUserRole$ = this.currentUserRoleSubject.asObservable();\n      this.roleConfigurations = {\n        super_admin: {\n          role: 'super_admin',\n          department: 'administration',\n          hierarchy: 1,\n          displayName: 'Super Administrator',\n          description: 'Full system access and control',\n          color: '#FF6B6B',\n          icon: 'fas fa-crown',\n          permissions: [{\n            module: '*',\n            actions: ['*'],\n            scope: 'global'\n          }],\n          profileLayout: 'admin',\n          dashboardWidgets: ['system_overview', 'user_management', 'analytics', 'security', 'audit_logs']\n        },\n        admin: {\n          role: 'admin',\n          department: 'administration',\n          hierarchy: 2,\n          displayName: 'Administrator',\n          description: 'System administration and user management',\n          color: '#4ECDC4',\n          icon: 'fas fa-user-shield',\n          permissions: [{\n            module: 'users',\n            actions: ['create', 'read', 'update', 'delete'],\n            scope: 'global'\n          }, {\n            module: 'roles',\n            actions: ['read', 'update'],\n            scope: 'global'\n          }, {\n            module: 'system',\n            actions: ['read', 'update'],\n            scope: 'global'\n          }],\n          profileLayout: 'admin',\n          dashboardWidgets: ['user_stats', 'role_management', 'system_health', 'recent_activities']\n        },\n        sales_manager: {\n          role: 'sales_manager',\n          department: 'sales',\n          hierarchy: 3,\n          displayName: 'Sales Manager',\n          description: 'Sales team leadership and strategy',\n          color: '#45B7D1',\n          icon: 'fas fa-chart-line',\n          permissions: [{\n            module: 'sales',\n            actions: ['create', 'read', 'update', 'delete'],\n            scope: 'department'\n          }, {\n            module: 'customers',\n            actions: ['read', 'update'],\n            scope: 'department'\n          }, {\n            module: 'reports',\n            actions: ['read'],\n            scope: 'department'\n          }, {\n            module: 'team',\n            actions: ['read', 'update'],\n            scope: 'department'\n          }],\n          profileLayout: 'manager',\n          dashboardWidgets: ['sales_overview', 'team_performance', 'revenue_trends', 'customer_insights']\n        },\n        sales_executive: {\n          role: 'sales_executive',\n          department: 'sales',\n          hierarchy: 6,\n          displayName: 'Sales Executive',\n          description: 'Direct sales and customer relationship management',\n          color: '#96CEB4',\n          icon: 'fas fa-handshake',\n          permissions: [{\n            module: 'sales',\n            actions: ['create', 'read', 'update'],\n            scope: 'self'\n          }, {\n            module: 'customers',\n            actions: ['create', 'read', 'update'],\n            scope: 'self'\n          }, {\n            module: 'leads',\n            actions: ['create', 'read', 'update'],\n            scope: 'self'\n          }],\n          profileLayout: 'specialist',\n          dashboardWidgets: ['my_sales', 'my_customers', 'targets', 'commission']\n        },\n        marketing_manager: {\n          role: 'marketing_manager',\n          department: 'marketing',\n          hierarchy: 3,\n          displayName: 'Marketing Manager',\n          description: 'Marketing strategy and campaign management',\n          color: '#F38BA8',\n          icon: 'fas fa-bullhorn',\n          permissions: [{\n            module: 'marketing',\n            actions: ['create', 'read', 'update', 'delete'],\n            scope: 'department'\n          }, {\n            module: 'campaigns',\n            actions: ['create', 'read', 'update', 'delete'],\n            scope: 'department'\n          }, {\n            module: 'content',\n            actions: ['read', 'update'],\n            scope: 'department'\n          }, {\n            module: 'analytics',\n            actions: ['read'],\n            scope: 'department'\n          }],\n          profileLayout: 'manager',\n          dashboardWidgets: ['campaign_performance', 'audience_insights', 'content_metrics', 'roi_analysis']\n        },\n        marketing_executive: {\n          role: 'marketing_executive',\n          department: 'marketing',\n          hierarchy: 6,\n          displayName: 'Marketing Executive',\n          description: 'Campaign execution and content creation',\n          color: '#DDA0DD',\n          icon: 'fas fa-palette',\n          permissions: [{\n            module: 'campaigns',\n            actions: ['create', 'read', 'update'],\n            scope: 'self'\n          }, {\n            module: 'content',\n            actions: ['create', 'read', 'update'],\n            scope: 'self'\n          }, {\n            module: 'social_media',\n            actions: ['create', 'read', 'update'],\n            scope: 'self'\n          }],\n          profileLayout: 'specialist',\n          dashboardWidgets: ['my_campaigns', 'content_calendar', 'social_metrics', 'creative_assets']\n        },\n        account_manager: {\n          role: 'account_manager',\n          department: 'accounting',\n          hierarchy: 4,\n          displayName: 'Account Manager',\n          description: 'Financial oversight and accounting management',\n          color: '#FFD93D',\n          icon: 'fas fa-calculator',\n          permissions: [{\n            module: 'accounting',\n            actions: ['create', 'read', 'update', 'delete'],\n            scope: 'department'\n          }, {\n            module: 'financial_reports',\n            actions: ['create', 'read'],\n            scope: 'department'\n          }, {\n            module: 'budgets',\n            actions: ['create', 'read', 'update'],\n            scope: 'department'\n          }],\n          profileLayout: 'manager',\n          dashboardWidgets: ['financial_overview', 'budget_tracking', 'expense_analysis', 'profit_margins']\n        },\n        accountant: {\n          role: 'accountant',\n          department: 'accounting',\n          hierarchy: 7,\n          displayName: 'Accountant',\n          description: 'Financial record keeping and transaction management',\n          color: '#6BCF7F',\n          icon: 'fas fa-file-invoice-dollar',\n          permissions: [{\n            module: 'transactions',\n            actions: ['create', 'read', 'update'],\n            scope: 'self'\n          }, {\n            module: 'invoices',\n            actions: ['create', 'read', 'update'],\n            scope: 'self'\n          }, {\n            module: 'expenses',\n            actions: ['create', 'read', 'update'],\n            scope: 'self'\n          }],\n          profileLayout: 'specialist',\n          dashboardWidgets: ['daily_transactions', 'pending_invoices', 'expense_tracker', 'tax_summary']\n        },\n        support_manager: {\n          role: 'support_manager',\n          department: 'support',\n          hierarchy: 4,\n          displayName: 'Support Manager',\n          description: 'Customer support team leadership',\n          color: '#FF8C42',\n          icon: 'fas fa-headset',\n          permissions: [{\n            module: 'support',\n            actions: ['create', 'read', 'update', 'delete'],\n            scope: 'department'\n          }, {\n            module: 'tickets',\n            actions: ['read', 'update', 'assign'],\n            scope: 'department'\n          }, {\n            module: 'knowledge_base',\n            actions: ['create', 'read', 'update'],\n            scope: 'department'\n          }],\n          profileLayout: 'manager',\n          dashboardWidgets: ['support_overview', 'ticket_analytics', 'team_workload', 'satisfaction_scores']\n        },\n        support_agent: {\n          role: 'support_agent',\n          department: 'support',\n          hierarchy: 8,\n          displayName: 'Support Agent',\n          description: 'Direct customer support and issue resolution',\n          color: '#A8E6CF',\n          icon: 'fas fa-life-ring',\n          permissions: [{\n            module: 'tickets',\n            actions: ['read', 'update'],\n            scope: 'self'\n          }, {\n            module: 'knowledge_base',\n            actions: ['read'],\n            scope: 'department'\n          }, {\n            module: 'customer_communication',\n            actions: ['create', 'read'],\n            scope: 'self'\n          }],\n          profileLayout: 'specialist',\n          dashboardWidgets: ['my_tickets', 'response_times', 'customer_feedback', 'knowledge_search']\n        },\n        content_manager: {\n          role: 'content_manager',\n          department: 'content',\n          hierarchy: 5,\n          displayName: 'Content Manager',\n          description: 'Content strategy and editorial oversight',\n          color: '#B19CD9',\n          icon: 'fas fa-edit',\n          permissions: [{\n            module: 'content',\n            actions: ['create', 'read', 'update', 'delete'],\n            scope: 'department'\n          }, {\n            module: 'editorial',\n            actions: ['create', 'read', 'update'],\n            scope: 'department'\n          }, {\n            module: 'publishing',\n            actions: ['approve', 'schedule'],\n            scope: 'department'\n          }],\n          profileLayout: 'manager',\n          dashboardWidgets: ['content_pipeline', 'editorial_calendar', 'engagement_metrics', 'seo_performance']\n        },\n        vendor_manager: {\n          role: 'vendor_manager',\n          department: 'vendor_management',\n          hierarchy: 5,\n          displayName: 'Vendor Manager',\n          description: 'Vendor relationship and partnership management',\n          color: '#FFB6C1',\n          icon: 'fas fa-store',\n          permissions: [{\n            module: 'vendors',\n            actions: ['create', 'read', 'update', 'delete'],\n            scope: 'department'\n          }, {\n            module: 'contracts',\n            actions: ['create', 'read', 'update'],\n            scope: 'department'\n          }, {\n            module: 'vendor_performance',\n            actions: ['read', 'evaluate'],\n            scope: 'department'\n          }],\n          profileLayout: 'manager',\n          dashboardWidgets: ['vendor_overview', 'contract_status', 'performance_metrics', 'payment_tracking']\n        }\n      };\n    }\n    setCurrentUserRole(role) {\n      this.currentUserRoleSubject.next(role);\n    }\n    getCurrentUserRole() {\n      return this.currentUserRoleSubject.value;\n    }\n    getRoleConfig(role) {\n      return this.roleConfigurations[role];\n    }\n    getAllRoles() {\n      return Object.values(this.roleConfigurations);\n    }\n    getRolesByDepartment(department) {\n      return Object.values(this.roleConfigurations).filter(config => config.department === department).sort((a, b) => a.hierarchy - b.hierarchy);\n    }\n    hasPermission(role, module, action) {\n      const config = this.getRoleConfig(role);\n      // Super admin has all permissions\n      if (role === 'super_admin') return true;\n      return config.permissions.some(permission => {\n        const moduleMatch = permission.module === '*' || permission.module === module;\n        const actionMatch = permission.actions.includes('*') || permission.actions.includes(action);\n        return moduleMatch && actionMatch;\n      });\n    }\n    getAccessibleModules(role) {\n      const config = this.getRoleConfig(role);\n      if (role === 'super_admin') return ['*'];\n      return config.permissions.map(p => p.module);\n    }\n    isManager(role) {\n      return role.includes('_manager') || role === 'super_admin' || role === 'admin';\n    }\n    getSubordinateRoles(role) {\n      const config = this.getRoleConfig(role);\n      return Object.values(this.roleConfigurations).filter(c => c.department === config.department && c.hierarchy > config.hierarchy).map(c => c.role);\n    }\n    getDepartmentHierarchy(department) {\n      return this.getRolesByDepartment(department);\n    }\n    static {\n      this.ɵfac = function RoleManagementService_Factory(t) {\n        return new (t || RoleManagementService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: RoleManagementService,\n        factory: RoleManagementService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return RoleManagementService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}