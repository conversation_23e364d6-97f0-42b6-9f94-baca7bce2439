{"ast": null, "code": "import _asyncToGenerator from \"E:/Fahion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ViewAddStoriesComponent } from '../../components/instagram-stories/view-add-stories.component';\nimport { PaymentModalComponent } from '../../../../shared/components/payment-modal/payment-modal.component';\nimport { SidebarComponent } from 'src/app/admin/layout/sidebar.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { InstagramStoriesComponent } from '../../components/instagram-stories/instagram-stories.component';\nlet HomeComponent = class HomeComponent {\n  constructor(router, productService, authService, buttonActionsService, postsService) {\n    this.router = router;\n    this.productService = productService;\n    this.authService = authService;\n    this.buttonActionsService = buttonActionsService;\n    this.postsService = postsService;\n    this.featuredProducts = [];\n    this.trendingProducts = [];\n    this.newArrivals = [];\n    this.trendingPosts = [];\n    this.categories = [];\n    this.isLoading = true;\n    this.isAuthenticated = false;\n    // Instagram-style data\n    this.instagramPosts = [];\n    this.summerCollection = [];\n    this.suggestedUsers = [];\n    this.currentUser = null;\n    this.newComment = '';\n    // Payment Modal\n    this.showPaymentModal = false;\n    this.paymentModalData = null;\n  }\n  ngOnInit() {\n    this.loadHomeData();\n    this.checkAuthStatus();\n    this.loadInstagramData();\n  }\n  checkAuthStatus() {\n    this.authService.currentUser$.subscribe(user => {\n      this.isAuthenticated = !!user;\n      this.currentUser = user;\n    });\n  }\n  loadInstagramData() {\n    this.loadInstagramPosts();\n    this.loadSummerCollection();\n    this.loadSuggestedUsers();\n  }\n  loadInstagramPosts() {\n    // Load real Instagram-style posts from API\n    this.postsService.getPosts(1, 10).subscribe({\n      next: response => {\n        if (response.success) {\n          this.instagramPosts = response.posts;\n          console.log('✅ Instagram posts loaded:', this.instagramPosts.length);\n        } else {\n          this.instagramPosts = [];\n          console.log('⚠️ No Instagram posts available');\n        }\n      },\n      error: error => {\n        console.error('❌ Error loading Instagram posts:', error);\n        this.instagramPosts = [];\n      }\n    });\n  }\n  loadSummerCollection() {\n    // Load summer collection from API using category products\n    this.productService.getCategoryProducts('summer').subscribe({\n      next: response => {\n        this.summerCollection = response.products || [];\n        console.log('✅ Summer collection loaded:', this.summerCollection.length);\n      },\n      error: error => {\n        console.error('❌ Error loading summer collection:', error);\n        this.summerCollection = [];\n      }\n    });\n  }\n  loadSuggestedUsers() {\n    // Load suggested users from API\n    // For now, set empty array until users API is implemented\n    this.suggestedUsers = [];\n    console.log('✅ Suggested users loaded:', this.suggestedUsers.length);\n  }\n  loadHomeData() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        // Load all data in parallel\n        const [featured, trending, arrivals] = yield Promise.all([_this.productService.getFeaturedProducts().toPromise(), _this.productService.getTrendingProducts().toPromise(), _this.productService.getNewArrivals().toPromise()]);\n        _this.featuredProducts = (featured?.products || featured?.data || featured || []).slice(0, 8);\n        _this.trendingProducts = (trending?.products || trending?.data || trending || []).slice(0, 8);\n        _this.newArrivals = (arrivals?.products || arrivals?.data || arrivals || []).slice(0, 8);\n        // Log loaded data counts\n        console.log('✅ Featured products loaded:', _this.featuredProducts.length);\n        console.log('✅ Trending products loaded:', _this.trendingProducts.length);\n        console.log('✅ New arrivals loaded:', _this.newArrivals.length);\n        // Load categories\n        _this.loadCategories();\n        // Load trending posts (mock data for now)\n        _this.loadTrendingPosts();\n      } catch (error) {\n        console.error('❌ Error loading home data:', error);\n        // Set empty arrays instead of fallback data\n        _this.featuredProducts = [];\n        _this.trendingProducts = [];\n        _this.newArrivals = [];\n      } finally {\n        _this.isLoading = false;\n      }\n    })();\n  }\n  loadCategories() {\n    // Load categories from API\n    this.productService.getCategories().subscribe({\n      next: response => {\n        this.categories = response.data || [];\n        console.log('✅ Categories loaded:', this.categories.length);\n      },\n      error: error => {\n        console.error('❌ Error loading categories:', error);\n        this.categories = [];\n      }\n    });\n  }\n  loadTrendingPosts() {\n    // Load trending posts from API\n    this.postsService.getTrendingPosts(6).subscribe({\n      next: response => {\n        if (response.success) {\n          this.trendingPosts = response.posts;\n          console.log('✅ Trending posts loaded:', this.trendingPosts.length);\n        } else {\n          this.trendingPosts = [];\n          console.log('⚠️ No trending posts available');\n        }\n      },\n      error: error => {\n        console.error('❌ Error loading trending posts:', error);\n        this.trendingPosts = [];\n      }\n    });\n  }\n  // Navigation methods\n  onProductClick(product) {\n    this.router.navigate(['/product', product._id || product.id]);\n  }\n  onCategoryClick(category) {\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        category: category.name.toLowerCase()\n      }\n    });\n  }\n  viewAllCategories() {\n    this.router.navigate(['/shop']);\n  }\n  // Instagram-style interaction methods\n  toggleLike(post) {\n    if (!post._id) return;\n    // Optimistic update\n    const wasLiked = post.isLiked;\n    post.isLiked = !post.isLiked;\n    post.analytics.likes += post.isLiked ? 1 : -1;\n    this.postsService.toggleLike(post._id).subscribe({\n      next: result => {\n        if (result.success && result.likesCount !== undefined) {\n          post.analytics.likes = result.likesCount;\n          console.log('✅ Post like toggled successfully');\n        } else {\n          // Revert on failure\n          post.isLiked = wasLiked;\n          post.analytics.likes += wasLiked ? 1 : -1;\n          console.error('❌ Failed to toggle like:', result.message);\n        }\n      },\n      error: error => {\n        // Revert on error\n        post.isLiked = wasLiked;\n        post.analytics.likes += wasLiked ? 1 : -1;\n        console.error('❌ Error toggling like:', error);\n      }\n    });\n  }\n  toggleSave(post) {\n    if (!post.id && !post._id) return;\n    const postId = post.id || post._id;\n    // Optimistic update\n    post.isSaved = !post.isSaved;\n    this.buttonActionsService.savePost(postId).subscribe({\n      next: result => {\n        if (!result.success) {\n          // Revert on failure\n          post.isSaved = !post.isSaved;\n          console.error('Failed to save post:', result.message);\n        }\n      },\n      error: error => {\n        // Revert on error\n        post.isSaved = !post.isSaved;\n        console.error('Error saving post:', error);\n      }\n    });\n  }\n  sharePost(post) {\n    if (!post.id && !post._id) return;\n    const postId = post.id || post._id;\n    this.buttonActionsService.sharePost(postId).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Post shared successfully');\n        } else {\n          console.error('Failed to share post:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error sharing post:', error);\n      }\n    });\n  }\n  focusCommentInput(post) {\n    // Focus on comment input for this post\n    console.log('Focus comment for post:', post.id);\n  }\n  addComment(post) {\n    if (!this.newComment || !this.newComment.trim()) return;\n    if (!post._id) return;\n    const commentText = this.newComment.trim();\n    this.postsService.addComment(post._id, commentText).subscribe({\n      next: result => {\n        if (result.success && result.comment) {\n          // Add comment to local state\n          post.comments.push(result.comment);\n          post.analytics.comments += 1;\n          this.newComment = '';\n          console.log('✅ Comment added successfully');\n        } else {\n          console.error('❌ Failed to add comment:', result.message);\n        }\n      },\n      error: error => {\n        console.error('❌ Error adding comment:', error);\n      }\n    });\n  }\n  showProductDetails(product) {\n    console.log('🛍️ Show product details:', product);\n    if (product._id) {\n      this.router.navigate(['/product', product._id]);\n    } else {\n      // Toggle product preview for posts\n      product.showPreview = !product.showPreview;\n    }\n  }\n  // View full post\n  viewPost(post) {\n    console.log('📱 View post:', post._id);\n    this.router.navigate(['/post', post._id]);\n  }\n  // View user profile\n  viewUserProfile(user) {\n    console.log('👤 View user profile:', user.username);\n    this.router.navigate(['/profile', user.username]);\n  }\n  // E-commerce methods\n  buyNow(product) {\n    console.log('Buy now:', product);\n    if (!product.id && !product._id) return;\n    const productId = product.id || product._id;\n    this.buttonActionsService.buyNow({\n      productId: productId,\n      quantity: 1,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Buy now successful, redirecting to checkout');\n          // Navigation will be handled by the service\n        } else {\n          console.error('Failed to buy now:', result.message);\n          // Fallback to payment modal if needed\n          this.showPaymentModalFallback(product);\n        }\n      },\n      error: error => {\n        console.error('Error in buy now:', error);\n        // Fallback to payment modal\n        this.showPaymentModalFallback(product);\n      }\n    });\n  }\n  showPaymentModalFallback(product) {\n    if (!this.currentUser) {\n      this.router.navigate(['/auth/login'], {\n        queryParams: {\n          returnUrl: '/home'\n        }\n      });\n      return;\n    }\n    // Prepare payment modal data for single product purchase\n    this.paymentModalData = {\n      amount: product.price,\n      orderData: {\n        items: [{\n          id: product.id,\n          name: product.name,\n          price: product.price,\n          quantity: 1,\n          image: product.image\n        }],\n        subtotal: product.price,\n        tax: product.price * 0.18,\n        shipping: 0,\n        discount: 0,\n        total: product.price + product.price * 0.18\n      },\n      userDetails: {\n        name: this.currentUser.fullName || this.currentUser.username,\n        email: this.currentUser.email,\n        phone: this.currentUser.phone || ''\n      }\n    };\n    this.showPaymentModal = true;\n  }\n  addToWishlist(product) {\n    console.log('Add to wishlist:', product);\n    if (!product.id && !product._id) return;\n    const productId = product.id || product._id;\n    this.buttonActionsService.addToWishlist({\n      productId: productId,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Product added to wishlist successfully');\n        } else {\n          console.error('Failed to add to wishlist:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error adding to wishlist:', error);\n      }\n    });\n  }\n  addToCart(product) {\n    console.log('Add to cart:', product);\n    if (!product.id && !product._id) return;\n    const productId = product.id || product._id;\n    this.buttonActionsService.addToCart({\n      productId: productId,\n      quantity: 1,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Product added to cart successfully');\n        } else {\n          console.error('Failed to add to cart:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error adding to cart:', error);\n      }\n    });\n  }\n  followUser(user) {\n    console.log('Follow user:', user.username);\n    // Implement follow functionality\n  }\n  viewSummerCollection() {\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        collection: 'summer2024'\n      }\n    });\n  }\n  toggleComments(post) {\n    console.log('Toggle comments for post:', post.id);\n    // Implement comments toggle\n  }\n  // Utility methods\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatLikesCount(likes) {\n    return this.formatNumber(likes);\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;\n    return `${Math.floor(diffInSeconds / 604800)}w`;\n  }\n  trackByPostId(index, post) {\n    return post.id;\n  }\n  // Payment Modal handlers\n  onPaymentCompleted(paymentResult) {\n    this.showPaymentModal = false;\n    if (paymentResult.status === 'success') {\n      // Navigate to success page\n      this.router.navigate(['/payment-success'], {\n        queryParams: {\n          orderId: paymentResult.orderId || this.generateOrderId(),\n          method: paymentResult.method\n        }\n      });\n    } else {\n      // Show error message\n      alert('Payment failed. Please try again.');\n    }\n  }\n  onPaymentModalClose() {\n    this.showPaymentModal = false;\n  }\n  generateOrderId() {\n    return 'ORD' + Date.now().toString();\n  }\n  // Enhanced product tag methods\n  onProductTagClick(productTag) {\n    if (!productTag) return;\n    const navigationType = productTag.navigationType || 'product';\n    const targetData = productTag.product || productTag.category || productTag.vendor || productTag.brand;\n    switch (navigationType) {\n      case 'product':\n        this.navigateToProduct(targetData);\n        break;\n      case 'category':\n        this.navigateToCategory(targetData);\n        break;\n      case 'vendor':\n        this.navigateToVendor(targetData);\n        break;\n      case 'brand':\n        this.navigateToBrand(targetData);\n        break;\n      default:\n        this.navigateToProduct(targetData);\n    }\n  }\n  navigateToProduct(product) {\n    if (product?._id || product?.id) {\n      const productId = product._id || product.id;\n      this.router.navigate(['/product', productId]);\n    }\n  }\n  navigateToCategory(category) {\n    if (category?.slug || category?.name) {\n      const categorySlug = category.slug || category.name.toLowerCase().replace(/\\s+/g, '-');\n      this.router.navigate(['/shop'], {\n        queryParams: {\n          category: categorySlug\n        }\n      });\n    }\n  }\n  navigateToVendor(vendor) {\n    if (vendor?._id || vendor?.id || vendor?.username) {\n      const vendorId = vendor._id || vendor.id || vendor.username;\n      this.router.navigate(['/vendor', vendorId]);\n    }\n  }\n  navigateToBrand(brand) {\n    if (brand?.slug || brand?.name) {\n      const brandSlug = brand.slug || brand.name.toLowerCase().replace(/\\s+/g, '-');\n      this.router.navigate(['/shop'], {\n        queryParams: {\n          brand: brandSlug\n        }\n      });\n    }\n  }\n  getTagTooltip(productTag) {\n    const type = productTag.navigationType || 'product';\n    const name = this.getTagName(productTag);\n    switch (type) {\n      case 'product':\n        return `View product: ${name}`;\n      case 'category':\n        return `Browse category: ${name}`;\n      case 'vendor':\n        return `Visit vendor: ${name}`;\n      case 'brand':\n        return `Shop brand: ${name}`;\n      default:\n        return `View: ${name}`;\n    }\n  }\n  getTagIcon(navigationType) {\n    switch (navigationType) {\n      case 'product':\n        return 'fas fa-tag';\n      case 'category':\n        return 'fas fa-th-large';\n      case 'vendor':\n        return 'fas fa-store';\n      case 'brand':\n        return 'fas fa-crown';\n      default:\n        return 'fas fa-tag';\n    }\n  }\n  getTagName(productTag) {\n    const type = productTag.navigationType || 'product';\n    switch (type) {\n      case 'product':\n        return productTag.product?.name || 'Product';\n      case 'category':\n        return productTag.category?.name || 'Category';\n      case 'vendor':\n        return productTag.vendor?.fullName || productTag.vendor?.username || 'Vendor';\n      case 'brand':\n        return productTag.brand?.name || 'Brand';\n      default:\n        return productTag.product?.name || 'Item';\n    }\n  }\n  getTagSubtitle(productTag) {\n    const type = productTag.navigationType || 'product';\n    switch (type) {\n      case 'product':\n        return productTag.product?.price ? `$${productTag.product.price}` : '';\n      case 'category':\n        return productTag.category?.productCount ? `${productTag.category.productCount} products` : 'Browse category';\n      case 'vendor':\n        return productTag.vendor?.location || 'Visit store';\n      case 'brand':\n        return productTag.brand?.productCount ? `${productTag.brand.productCount} products` : 'Shop brand';\n      default:\n        return '';\n    }\n  }\n  getTagImageUrl(productTag) {\n    const type = productTag.navigationType || 'product';\n    switch (type) {\n      case 'product':\n        return productTag.product?.images?.[0]?.url || '/assets/images/product-placeholder.jpg';\n      case 'category':\n        return productTag.category?.image || '/assets/images/category-placeholder.jpg';\n      case 'vendor':\n        return productTag.vendor?.avatar || '/assets/images/vendor-placeholder.jpg';\n      case 'brand':\n        return productTag.brand?.logo || '/assets/images/brand-placeholder.jpg';\n      default:\n        return productTag.product?.images?.[0]?.url || '/assets/images/product-placeholder.jpg';\n    }\n  }\n};\nHomeComponent = __decorate([Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, FormsModule, RouterModule, ViewAddStoriesComponent, InstagramStoriesComponent, FeedComponent, SidebarComponent, PaymentModalComponent],\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})], HomeComponent);\nexport { HomeComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "FormsModule", "RouterModule", "ViewAddStoriesComponent", "PaymentModalComponent", "SidebarComponent", "FeedComponent", "InstagramStoriesComponent", "HomeComponent", "constructor", "router", "productService", "authService", "buttonActionsService", "postsService", "featuredProducts", "trendingProducts", "newArrivals", "trendingPosts", "categories", "isLoading", "isAuthenticated", "instagramPosts", "summerCollection", "suggestedUsers", "currentUser", "newComment", "showPaymentModal", "paymentModalData", "ngOnInit", "loadHomeData", "checkAuthStatus", "loadInstagramData", "currentUser$", "subscribe", "user", "loadInstagramPosts", "loadSummerCollection", "loadSuggestedUsers", "getPosts", "next", "response", "success", "posts", "console", "log", "length", "error", "getCategoryProducts", "products", "_this", "_asyncToGenerator", "featured", "trending", "arrivals", "Promise", "all", "getFeaturedProducts", "to<PERSON>romise", "getTrendingProducts", "getNewArrivals", "data", "slice", "loadCategories", "loadTrendingPosts", "getCategories", "getTrendingPosts", "onProductClick", "product", "navigate", "_id", "id", "onCategoryClick", "category", "queryParams", "name", "toLowerCase", "viewAllCategories", "toggleLike", "post", "wasLiked", "isLiked", "analytics", "likes", "result", "likesCount", "undefined", "message", "toggleSave", "postId", "isSaved", "savePost", "sharePost", "focusCommentInput", "addComment", "trim", "commentText", "comment", "comments", "push", "showProductDetails", "showPreview", "viewPost", "viewUserProfile", "username", "buyNow", "productId", "quantity", "addedFrom", "showPaymentModalFallback", "returnUrl", "amount", "price", "orderData", "items", "image", "subtotal", "tax", "shipping", "discount", "total", "userDetails", "fullName", "email", "phone", "addToWishlist", "addToCart", "followUser", "viewSummerCollection", "collection", "toggleComments", "formatPrice", "Intl", "NumberFormat", "style", "currency", "format", "formatNumber", "num", "toFixed", "toString", "formatLikesCount", "getTimeAgo", "date", "now", "Date", "diffInSeconds", "Math", "floor", "getTime", "trackByPostId", "index", "onPaymentCompleted", "paymentResult", "status", "orderId", "generateOrderId", "method", "alert", "onPaymentModalClose", "onProductTagClick", "productTag", "navigationType", "targetData", "vendor", "brand", "navigateToProduct", "navigateToCategory", "navigateToVendor", "navigate<PERSON><PERSON>Brand", "slug", "categorySlug", "replace", "vendorId", "brandSlug", "getTagTooltip", "type", "getTagName", "getTagIcon", "getTagSubtitle", "productCount", "location", "getTagImageUrl", "images", "url", "avatar", "logo", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router, RouterModule } from '@angular/router';\nimport { ProductService } from '../../../../core/services/product.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { ButtonActionsService } from '../../../../core/services/button-actions.service';\nimport { PostsService, InstagramPost } from '../../../../core/services/posts.service';\nimport { ViewAddStoriesComponent } from '../../components/instagram-stories/view-add-stories.component';\nimport { PaymentModalComponent, PaymentModalData } from '../../../../shared/components/payment-modal/payment-modal.component';\nimport { HomeSidebarComponent } from '../../components/sidebar/home-sidebar.component';\nimport { SidebarComponent } from 'src/app/admin/layout/sidebar.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { InstagramStoriesComponent } from '../../components/instagram-stories/instagram-stories.component';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, FormsModule, RouterModule, ViewAddStoriesComponent, \n    InstagramStoriesComponent, FeedComponent, SidebarComponent,\n    PaymentModalComponent],\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit {\n  featuredProducts: any[] = [];\n  trendingProducts: any[] = [];\n  newArrivals: any[] = [];\n  trendingPosts: any[] = [];\n  categories: any[] = [];\n  isLoading = true;\n  isAuthenticated = false;\n\n  // Instagram-style data\n  instagramPosts: any[] = [];\n  summerCollection: any[] = [];\n  suggestedUsers: any[] = [];\n  currentUser: any = null;\n  newComment = '';\n\n  // Payment Modal\n  showPaymentModal = false;\n  paymentModalData: PaymentModalData | null = null;\n\n  constructor(\n    private router: Router,\n    private productService: ProductService,\n    private authService: AuthService,\n    private buttonActionsService: ButtonActionsService,\n    private postsService: PostsService\n  ) {}\n\n  ngOnInit() {\n    this.loadHomeData();\n    this.checkAuthStatus();\n    this.loadInstagramData();\n  }\n\n  checkAuthStatus() {\n    this.authService.currentUser$.subscribe(user => {\n      this.isAuthenticated = !!user;\n      this.currentUser = user;\n    });\n  }\n\n  loadInstagramData() {\n    this.loadInstagramPosts();\n    this.loadSummerCollection();\n    this.loadSuggestedUsers();\n  }\n\n  loadInstagramPosts() {\n    // Load real Instagram-style posts from API\n    this.postsService.getPosts(1, 10).subscribe({\n      next: (response) => {\n        if (response.success) {\n          this.instagramPosts = response.posts;\n          console.log('✅ Instagram posts loaded:', this.instagramPosts.length);\n        } else {\n          this.instagramPosts = [];\n          console.log('⚠️ No Instagram posts available');\n        }\n      },\n      error: (error) => {\n        console.error('❌ Error loading Instagram posts:', error);\n        this.instagramPosts = [];\n      }\n    });\n  }\n\n  loadSummerCollection() {\n    // Load summer collection from API using category products\n    this.productService.getCategoryProducts('summer').subscribe({\n      next: (response: any) => {\n        this.summerCollection = response.products || [];\n        console.log('✅ Summer collection loaded:', this.summerCollection.length);\n      },\n      error: (error: any) => {\n        console.error('❌ Error loading summer collection:', error);\n        this.summerCollection = [];\n      }\n    });\n  }\n\n  loadSuggestedUsers() {\n    // Load suggested users from API\n    // For now, set empty array until users API is implemented\n    this.suggestedUsers = [];\n    console.log('✅ Suggested users loaded:', this.suggestedUsers.length);\n  }\n\n  async loadHomeData() {\n    try {\n      this.isLoading = true;\n\n      // Load all data in parallel\n      const [featured, trending, arrivals] = await Promise.all([\n        this.productService.getFeaturedProducts().toPromise(),\n        this.productService.getTrendingProducts().toPromise(),\n        this.productService.getNewArrivals().toPromise()\n      ]);\n\n      this.featuredProducts = ((featured as any)?.products || (featured as any)?.data || featured || []).slice(0, 8);\n      this.trendingProducts = ((trending as any)?.products || (trending as any)?.data || trending || []).slice(0, 8);\n      this.newArrivals = ((arrivals as any)?.products || (arrivals as any)?.data || arrivals || []).slice(0, 8);\n\n      // Log loaded data counts\n      console.log('✅ Featured products loaded:', this.featuredProducts.length);\n      console.log('✅ Trending products loaded:', this.trendingProducts.length);\n      console.log('✅ New arrivals loaded:', this.newArrivals.length);\n\n      // Load categories\n      this.loadCategories();\n\n      // Load trending posts (mock data for now)\n      this.loadTrendingPosts();\n\n    } catch (error) {\n      console.error('❌ Error loading home data:', error);\n      // Set empty arrays instead of fallback data\n      this.featuredProducts = [];\n      this.trendingProducts = [];\n      this.newArrivals = [];\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  loadCategories() {\n    // Load categories from API\n    this.productService.getCategories().subscribe({\n      next: (response: any) => {\n        this.categories = response.data || [];\n        console.log('✅ Categories loaded:', this.categories.length);\n      },\n      error: (error: any) => {\n        console.error('❌ Error loading categories:', error);\n        this.categories = [];\n      }\n    });\n  }\n\n  loadTrendingPosts() {\n    // Load trending posts from API\n    this.postsService.getTrendingPosts(6).subscribe({\n      next: (response) => {\n        if (response.success) {\n          this.trendingPosts = response.posts;\n          console.log('✅ Trending posts loaded:', this.trendingPosts.length);\n        } else {\n          this.trendingPosts = [];\n          console.log('⚠️ No trending posts available');\n        }\n      },\n      error: (error) => {\n        console.error('❌ Error loading trending posts:', error);\n        this.trendingPosts = [];\n      }\n    });\n  }\n\n\n\n  // Navigation methods\n  onProductClick(product: any) {\n    this.router.navigate(['/product', product._id || product.id]);\n  }\n\n  onCategoryClick(category: any) {\n    this.router.navigate(['/shop'], {\n      queryParams: { category: category.name.toLowerCase() }\n    });\n  }\n\n  viewAllCategories() {\n    this.router.navigate(['/shop']);\n  }\n\n  // Instagram-style interaction methods\n  toggleLike(post: InstagramPost) {\n    if (!post._id) return;\n\n    // Optimistic update\n    const wasLiked = post.isLiked;\n    post.isLiked = !post.isLiked;\n    post.analytics.likes += post.isLiked ? 1 : -1;\n\n    this.postsService.toggleLike(post._id).subscribe({\n      next: (result) => {\n        if (result.success && result.likesCount !== undefined) {\n          post.analytics.likes = result.likesCount;\n          console.log('✅ Post like toggled successfully');\n        } else {\n          // Revert on failure\n          post.isLiked = wasLiked;\n          post.analytics.likes += wasLiked ? 1 : -1;\n          console.error('❌ Failed to toggle like:', result.message);\n        }\n      },\n      error: (error) => {\n        // Revert on error\n        post.isLiked = wasLiked;\n        post.analytics.likes += wasLiked ? 1 : -1;\n        console.error('❌ Error toggling like:', error);\n      }\n    });\n  }\n\n  toggleSave(post: any) {\n    if (!post.id && !post._id) return;\n\n    const postId = post.id || post._id;\n\n    // Optimistic update\n    post.isSaved = !post.isSaved;\n\n    this.buttonActionsService.savePost(postId).subscribe({\n      next: (result) => {\n        if (!result.success) {\n          // Revert on failure\n          post.isSaved = !post.isSaved;\n          console.error('Failed to save post:', result.message);\n        }\n      },\n      error: (error) => {\n        // Revert on error\n        post.isSaved = !post.isSaved;\n        console.error('Error saving post:', error);\n      }\n    });\n  }\n\n  sharePost(post: any) {\n    if (!post.id && !post._id) return;\n\n    const postId = post.id || post._id;\n\n    this.buttonActionsService.sharePost(postId).subscribe({\n      next: (result) => {\n        if (result.success) {\n          console.log('Post shared successfully');\n        } else {\n          console.error('Failed to share post:', result.message);\n        }\n      },\n      error: (error) => {\n        console.error('Error sharing post:', error);\n      }\n    });\n  }\n\n  focusCommentInput(post: any) {\n    // Focus on comment input for this post\n    console.log('Focus comment for post:', post.id);\n  }\n\n  addComment(post: InstagramPost) {\n    if (!this.newComment || !this.newComment.trim()) return;\n    if (!post._id) return;\n\n    const commentText = this.newComment.trim();\n\n    this.postsService.addComment(post._id, commentText).subscribe({\n      next: (result) => {\n        if (result.success && result.comment) {\n          // Add comment to local state\n          post.comments.push(result.comment);\n          post.analytics.comments += 1;\n          this.newComment = '';\n          console.log('✅ Comment added successfully');\n        } else {\n          console.error('❌ Failed to add comment:', result.message);\n        }\n      },\n      error: (error) => {\n        console.error('❌ Error adding comment:', error);\n      }\n    });\n  }\n\n  showProductDetails(product: any) {\n    console.log('🛍️ Show product details:', product);\n    if (product._id) {\n      this.router.navigate(['/product', product._id]);\n    } else {\n      // Toggle product preview for posts\n      product.showPreview = !product.showPreview;\n    }\n  }\n\n  // View full post\n  viewPost(post: InstagramPost) {\n    console.log('📱 View post:', post._id);\n    this.router.navigate(['/post', post._id]);\n  }\n\n  // View user profile\n  viewUserProfile(user: any) {\n    console.log('👤 View user profile:', user.username);\n    this.router.navigate(['/profile', user.username]);\n  }\n\n  // E-commerce methods\n  buyNow(product: any) {\n    console.log('Buy now:', product);\n\n    if (!product.id && !product._id) return;\n\n    const productId = product.id || product._id;\n\n    this.buttonActionsService.buyNow({\n      productId: productId,\n      quantity: 1,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: (result) => {\n        if (result.success) {\n          console.log('Buy now successful, redirecting to checkout');\n          // Navigation will be handled by the service\n        } else {\n          console.error('Failed to buy now:', result.message);\n          // Fallback to payment modal if needed\n          this.showPaymentModalFallback(product);\n        }\n      },\n      error: (error) => {\n        console.error('Error in buy now:', error);\n        // Fallback to payment modal\n        this.showPaymentModalFallback(product);\n      }\n    });\n  }\n\n  private showPaymentModalFallback(product: any) {\n    if (!this.currentUser) {\n      this.router.navigate(['/auth/login'], { queryParams: { returnUrl: '/home' } });\n      return;\n    }\n\n    // Prepare payment modal data for single product purchase\n    this.paymentModalData = {\n      amount: product.price,\n      orderData: {\n        items: [{\n          id: product.id,\n          name: product.name,\n          price: product.price,\n          quantity: 1,\n          image: product.image\n        }],\n        subtotal: product.price,\n        tax: product.price * 0.18,\n        shipping: 0,\n        discount: 0,\n        total: product.price + (product.price * 0.18)\n      },\n      userDetails: {\n        name: this.currentUser.fullName || this.currentUser.username,\n        email: this.currentUser.email,\n        phone: this.currentUser.phone || ''\n      }\n    };\n\n    this.showPaymentModal = true;\n  }\n\n  addToWishlist(product: any) {\n    console.log('Add to wishlist:', product);\n\n    if (!product.id && !product._id) return;\n\n    const productId = product.id || product._id;\n\n    this.buttonActionsService.addToWishlist({\n      productId: productId,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: (result) => {\n        if (result.success) {\n          console.log('Product added to wishlist successfully');\n        } else {\n          console.error('Failed to add to wishlist:', result.message);\n        }\n      },\n      error: (error) => {\n        console.error('Error adding to wishlist:', error);\n      }\n    });\n  }\n\n  addToCart(product: any) {\n    console.log('Add to cart:', product);\n\n    if (!product.id && !product._id) return;\n\n    const productId = product.id || product._id;\n\n    this.buttonActionsService.addToCart({\n      productId: productId,\n      quantity: 1,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: (result) => {\n        if (result.success) {\n          console.log('Product added to cart successfully');\n        } else {\n          console.error('Failed to add to cart:', result.message);\n        }\n      },\n      error: (error) => {\n        console.error('Error adding to cart:', error);\n      }\n    });\n  }\n\n  followUser(user: any) {\n    console.log('Follow user:', user.username);\n    // Implement follow functionality\n  }\n\n  viewSummerCollection() {\n    this.router.navigate(['/shop'], { queryParams: { collection: 'summer2024' } });\n  }\n\n  toggleComments(post: any) {\n    console.log('Toggle comments for post:', post.id);\n    // Implement comments toggle\n  }\n\n  // Utility methods\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  formatLikesCount(likes: number): string {\n    return this.formatNumber(likes);\n  }\n\n  getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;\n    return `${Math.floor(diffInSeconds / 604800)}w`;\n  }\n\n  trackByPostId(index: number, post: any): string {\n    return post.id;\n  }\n\n  // Payment Modal handlers\n  onPaymentCompleted(paymentResult: any) {\n    this.showPaymentModal = false;\n\n    if (paymentResult.status === 'success') {\n      // Navigate to success page\n      this.router.navigate(['/payment-success'], {\n        queryParams: {\n          orderId: paymentResult.orderId || this.generateOrderId(),\n          method: paymentResult.method\n        }\n      });\n    } else {\n      // Show error message\n      alert('Payment failed. Please try again.');\n    }\n  }\n\n  onPaymentModalClose() {\n    this.showPaymentModal = false;\n  }\n\n  private generateOrderId(): string {\n    return 'ORD' + Date.now().toString();\n  }\n\n  // Enhanced product tag methods\n  onProductTagClick(productTag: any): void {\n    if (!productTag) return;\n\n    const navigationType = productTag.navigationType || 'product';\n    const targetData = productTag.product || productTag.category || productTag.vendor || productTag.brand;\n\n    switch (navigationType) {\n      case 'product':\n        this.navigateToProduct(targetData);\n        break;\n      case 'category':\n        this.navigateToCategory(targetData);\n        break;\n      case 'vendor':\n        this.navigateToVendor(targetData);\n        break;\n      case 'brand':\n        this.navigateToBrand(targetData);\n        break;\n      default:\n        this.navigateToProduct(targetData);\n    }\n  }\n\n  private navigateToProduct(product: any): void {\n    if (product?._id || product?.id) {\n      const productId = product._id || product.id;\n      this.router.navigate(['/product', productId]);\n    }\n  }\n\n  private navigateToCategory(category: any): void {\n    if (category?.slug || category?.name) {\n      const categorySlug = category.slug || category.name.toLowerCase().replace(/\\s+/g, '-');\n      this.router.navigate(['/shop'], {\n        queryParams: { category: categorySlug }\n      });\n    }\n  }\n\n  private navigateToVendor(vendor: any): void {\n    if (vendor?._id || vendor?.id || vendor?.username) {\n      const vendorId = vendor._id || vendor.id || vendor.username;\n      this.router.navigate(['/vendor', vendorId]);\n    }\n  }\n\n  private navigateToBrand(brand: any): void {\n    if (brand?.slug || brand?.name) {\n      const brandSlug = brand.slug || brand.name.toLowerCase().replace(/\\s+/g, '-');\n      this.router.navigate(['/shop'], {\n        queryParams: { brand: brandSlug }\n      });\n    }\n  }\n\n  getTagTooltip(productTag: any): string {\n    const type = productTag.navigationType || 'product';\n    const name = this.getTagName(productTag);\n\n    switch (type) {\n      case 'product':\n        return `View product: ${name}`;\n      case 'category':\n        return `Browse category: ${name}`;\n      case 'vendor':\n        return `Visit vendor: ${name}`;\n      case 'brand':\n        return `Shop brand: ${name}`;\n      default:\n        return `View: ${name}`;\n    }\n  }\n\n  getTagIcon(navigationType: string): string {\n    switch (navigationType) {\n      case 'product':\n        return 'fas fa-tag';\n      case 'category':\n        return 'fas fa-th-large';\n      case 'vendor':\n        return 'fas fa-store';\n      case 'brand':\n        return 'fas fa-crown';\n      default:\n        return 'fas fa-tag';\n    }\n  }\n\n  getTagName(productTag: any): string {\n    const type = productTag.navigationType || 'product';\n\n    switch (type) {\n      case 'product':\n        return productTag.product?.name || 'Product';\n      case 'category':\n        return productTag.category?.name || 'Category';\n      case 'vendor':\n        return productTag.vendor?.fullName || productTag.vendor?.username || 'Vendor';\n      case 'brand':\n        return productTag.brand?.name || 'Brand';\n      default:\n        return productTag.product?.name || 'Item';\n    }\n  }\n\n  getTagSubtitle(productTag: any): string {\n    const type = productTag.navigationType || 'product';\n\n    switch (type) {\n      case 'product':\n        return productTag.product?.price ? `$${productTag.product.price}` : '';\n      case 'category':\n        return productTag.category?.productCount ? `${productTag.category.productCount} products` : 'Browse category';\n      case 'vendor':\n        return productTag.vendor?.location || 'Visit store';\n      case 'brand':\n        return productTag.brand?.productCount ? `${productTag.brand.productCount} products` : 'Shop brand';\n      default:\n        return '';\n    }\n  }\n\n  getTagImageUrl(productTag: any): string {\n    const type = productTag.navigationType || 'product';\n\n    switch (type) {\n      case 'product':\n        return productTag.product?.images?.[0]?.url || '/assets/images/product-placeholder.jpg';\n      case 'category':\n        return productTag.category?.image || '/assets/images/category-placeholder.jpg';\n      case 'vendor':\n        return productTag.vendor?.avatar || '/assets/images/vendor-placeholder.jpg';\n      case 'brand':\n        return productTag.brand?.logo || '/assets/images/brand-placeholder.jpg';\n      default:\n        return productTag.product?.images?.[0]?.url || '/assets/images/product-placeholder.jpg';\n    }\n  }\n}\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAAiBC,YAAY,QAAQ,iBAAiB;AAKtD,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAASC,qBAAqB,QAA0B,qEAAqE;AAE7H,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,yBAAyB,QAAQ,gEAAgE;AAWnG,IAAMC,aAAa,GAAnB,MAAMA,aAAa;EAoBxBC,YACUC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,oBAA0C,EAC1CC,YAA0B;IAJ1B,KAAAJ,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,YAAY,GAAZA,YAAY;IAxBtB,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,UAAU,GAAU,EAAE;IACtB,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAAC,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAC,UAAU,GAAG,EAAE;IAEf;IACA,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,gBAAgB,GAA4B,IAAI;EAQ7C;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAD,eAAeA,CAAA;IACb,IAAI,CAACnB,WAAW,CAACqB,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACd,eAAe,GAAG,CAAC,CAACc,IAAI;MAC7B,IAAI,CAACV,WAAW,GAAGU,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAH,iBAAiBA,CAAA;IACf,IAAI,CAACI,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAF,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACtB,YAAY,CAACyB,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAACL,SAAS,CAAC;MAC1CM,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACpB,cAAc,GAAGmB,QAAQ,CAACE,KAAK;UACpCC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACvB,cAAc,CAACwB,MAAM,CAAC;SACrE,MAAM;UACL,IAAI,CAACxB,cAAc,GAAG,EAAE;UACxBsB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;;MAElD,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACzB,cAAc,GAAG,EAAE;MAC1B;KACD,CAAC;EACJ;EAEAe,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAAC1B,cAAc,CAACqC,mBAAmB,CAAC,QAAQ,CAAC,CAACd,SAAS,CAAC;MAC1DM,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAAClB,gBAAgB,GAAGkB,QAAQ,CAACQ,QAAQ,IAAI,EAAE;QAC/CL,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACtB,gBAAgB,CAACuB,MAAM,CAAC;MAC1E,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpBH,OAAO,CAACG,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,IAAI,CAACxB,gBAAgB,GAAG,EAAE;MAC5B;KACD,CAAC;EACJ;EAEAe,kBAAkBA,CAAA;IAChB;IACA;IACA,IAAI,CAACd,cAAc,GAAG,EAAE;IACxBoB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACrB,cAAc,CAACsB,MAAM,CAAC;EACtE;EAEMhB,YAAYA,CAAA;IAAA,IAAAoB,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAI;QACFD,KAAI,CAAC9B,SAAS,GAAG,IAAI;QAErB;QACA,MAAM,CAACgC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,CAAC,SAASC,OAAO,CAACC,GAAG,CAAC,CACvDN,KAAI,CAACvC,cAAc,CAAC8C,mBAAmB,EAAE,CAACC,SAAS,EAAE,EACrDR,KAAI,CAACvC,cAAc,CAACgD,mBAAmB,EAAE,CAACD,SAAS,EAAE,EACrDR,KAAI,CAACvC,cAAc,CAACiD,cAAc,EAAE,CAACF,SAAS,EAAE,CACjD,CAAC;QAEFR,KAAI,CAACnC,gBAAgB,GAAG,CAAEqC,QAAgB,EAAEH,QAAQ,IAAKG,QAAgB,EAAES,IAAI,IAAIT,QAAQ,IAAI,EAAE,EAAEU,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9GZ,KAAI,CAAClC,gBAAgB,GAAG,CAAEqC,QAAgB,EAAEJ,QAAQ,IAAKI,QAAgB,EAAEQ,IAAI,IAAIR,QAAQ,IAAI,EAAE,EAAES,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9GZ,KAAI,CAACjC,WAAW,GAAG,CAAEqC,QAAgB,EAAEL,QAAQ,IAAKK,QAAgB,EAAEO,IAAI,IAAIP,QAAQ,IAAI,EAAE,EAAEQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAEzG;QACAlB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEK,KAAI,CAACnC,gBAAgB,CAAC+B,MAAM,CAAC;QACxEF,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEK,KAAI,CAAClC,gBAAgB,CAAC8B,MAAM,CAAC;QACxEF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEK,KAAI,CAACjC,WAAW,CAAC6B,MAAM,CAAC;QAE9D;QACAI,KAAI,CAACa,cAAc,EAAE;QAErB;QACAb,KAAI,CAACc,iBAAiB,EAAE;OAEzB,CAAC,OAAOjB,KAAK,EAAE;QACdH,OAAO,CAACG,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD;QACAG,KAAI,CAACnC,gBAAgB,GAAG,EAAE;QAC1BmC,KAAI,CAAClC,gBAAgB,GAAG,EAAE;QAC1BkC,KAAI,CAACjC,WAAW,GAAG,EAAE;OACtB,SAAS;QACRiC,KAAI,CAAC9B,SAAS,GAAG,KAAK;;IACvB;EACH;EAEA2C,cAAcA,CAAA;IACZ;IACA,IAAI,CAACpD,cAAc,CAACsD,aAAa,EAAE,CAAC/B,SAAS,CAAC;MAC5CM,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACtB,UAAU,GAAGsB,QAAQ,CAACoB,IAAI,IAAI,EAAE;QACrCjB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC1B,UAAU,CAAC2B,MAAM,CAAC;MAC7D,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpBH,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAAC5B,UAAU,GAAG,EAAE;MACtB;KACD,CAAC;EACJ;EAEA6C,iBAAiBA,CAAA;IACf;IACA,IAAI,CAAClD,YAAY,CAACoD,gBAAgB,CAAC,CAAC,CAAC,CAAChC,SAAS,CAAC;MAC9CM,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACxB,aAAa,GAAGuB,QAAQ,CAACE,KAAK;UACnCC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC3B,aAAa,CAAC4B,MAAM,CAAC;SACnE,MAAM;UACL,IAAI,CAAC5B,aAAa,GAAG,EAAE;UACvB0B,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAEjD,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,IAAI,CAAC7B,aAAa,GAAG,EAAE;MACzB;KACD,CAAC;EACJ;EAIA;EACAiD,cAAcA,CAACC,OAAY;IACzB,IAAI,CAAC1D,MAAM,CAAC2D,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACE,GAAG,IAAIF,OAAO,CAACG,EAAE,CAAC,CAAC;EAC/D;EAEAC,eAAeA,CAACC,QAAa;IAC3B,IAAI,CAAC/D,MAAM,CAAC2D,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAC9BK,WAAW,EAAE;QAAED,QAAQ,EAAEA,QAAQ,CAACE,IAAI,CAACC,WAAW;MAAE;KACrD,CAAC;EACJ;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACnE,MAAM,CAAC2D,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEA;EACAS,UAAUA,CAACC,IAAmB;IAC5B,IAAI,CAACA,IAAI,CAACT,GAAG,EAAE;IAEf;IACA,MAAMU,QAAQ,GAAGD,IAAI,CAACE,OAAO;IAC7BF,IAAI,CAACE,OAAO,GAAG,CAACF,IAAI,CAACE,OAAO;IAC5BF,IAAI,CAACG,SAAS,CAACC,KAAK,IAAIJ,IAAI,CAACE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;IAE7C,IAAI,CAACnE,YAAY,CAACgE,UAAU,CAACC,IAAI,CAACT,GAAG,CAAC,CAACpC,SAAS,CAAC;MAC/CM,IAAI,EAAG4C,MAAM,IAAI;QACf,IAAIA,MAAM,CAAC1C,OAAO,IAAI0C,MAAM,CAACC,UAAU,KAAKC,SAAS,EAAE;UACrDP,IAAI,CAACG,SAAS,CAACC,KAAK,GAAGC,MAAM,CAACC,UAAU;UACxCzC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;SAChD,MAAM;UACL;UACAkC,IAAI,CAACE,OAAO,GAAGD,QAAQ;UACvBD,IAAI,CAACG,SAAS,CAACC,KAAK,IAAIH,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;UACzCpC,OAAO,CAACG,KAAK,CAAC,0BAA0B,EAAEqC,MAAM,CAACG,OAAO,CAAC;;MAE7D,CAAC;MACDxC,KAAK,EAAGA,KAAK,IAAI;QACf;QACAgC,IAAI,CAACE,OAAO,GAAGD,QAAQ;QACvBD,IAAI,CAACG,SAAS,CAACC,KAAK,IAAIH,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;QACzCpC,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEAyC,UAAUA,CAACT,IAAS;IAClB,IAAI,CAACA,IAAI,CAACR,EAAE,IAAI,CAACQ,IAAI,CAACT,GAAG,EAAE;IAE3B,MAAMmB,MAAM,GAAGV,IAAI,CAACR,EAAE,IAAIQ,IAAI,CAACT,GAAG;IAElC;IACAS,IAAI,CAACW,OAAO,GAAG,CAACX,IAAI,CAACW,OAAO;IAE5B,IAAI,CAAC7E,oBAAoB,CAAC8E,QAAQ,CAACF,MAAM,CAAC,CAACvD,SAAS,CAAC;MACnDM,IAAI,EAAG4C,MAAM,IAAI;QACf,IAAI,CAACA,MAAM,CAAC1C,OAAO,EAAE;UACnB;UACAqC,IAAI,CAACW,OAAO,GAAG,CAACX,IAAI,CAACW,OAAO;UAC5B9C,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAEqC,MAAM,CAACG,OAAO,CAAC;;MAEzD,CAAC;MACDxC,KAAK,EAAGA,KAAK,IAAI;QACf;QACAgC,IAAI,CAACW,OAAO,GAAG,CAACX,IAAI,CAACW,OAAO;QAC5B9C,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;KACD,CAAC;EACJ;EAEA6C,SAASA,CAACb,IAAS;IACjB,IAAI,CAACA,IAAI,CAACR,EAAE,IAAI,CAACQ,IAAI,CAACT,GAAG,EAAE;IAE3B,MAAMmB,MAAM,GAAGV,IAAI,CAACR,EAAE,IAAIQ,IAAI,CAACT,GAAG;IAElC,IAAI,CAACzD,oBAAoB,CAAC+E,SAAS,CAACH,MAAM,CAAC,CAACvD,SAAS,CAAC;MACpDM,IAAI,EAAG4C,MAAM,IAAI;QACf,IAAIA,MAAM,CAAC1C,OAAO,EAAE;UAClBE,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;SACxC,MAAM;UACLD,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEqC,MAAM,CAACG,OAAO,CAAC;;MAE1D,CAAC;MACDxC,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC7C;KACD,CAAC;EACJ;EAEA8C,iBAAiBA,CAACd,IAAS;IACzB;IACAnC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEkC,IAAI,CAACR,EAAE,CAAC;EACjD;EAEAuB,UAAUA,CAACf,IAAmB;IAC5B,IAAI,CAAC,IAAI,CAACrD,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACqE,IAAI,EAAE,EAAE;IACjD,IAAI,CAAChB,IAAI,CAACT,GAAG,EAAE;IAEf,MAAM0B,WAAW,GAAG,IAAI,CAACtE,UAAU,CAACqE,IAAI,EAAE;IAE1C,IAAI,CAACjF,YAAY,CAACgF,UAAU,CAACf,IAAI,CAACT,GAAG,EAAE0B,WAAW,CAAC,CAAC9D,SAAS,CAAC;MAC5DM,IAAI,EAAG4C,MAAM,IAAI;QACf,IAAIA,MAAM,CAAC1C,OAAO,IAAI0C,MAAM,CAACa,OAAO,EAAE;UACpC;UACAlB,IAAI,CAACmB,QAAQ,CAACC,IAAI,CAACf,MAAM,CAACa,OAAO,CAAC;UAClClB,IAAI,CAACG,SAAS,CAACgB,QAAQ,IAAI,CAAC;UAC5B,IAAI,CAACxE,UAAU,GAAG,EAAE;UACpBkB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;SAC5C,MAAM;UACLD,OAAO,CAACG,KAAK,CAAC,0BAA0B,EAAEqC,MAAM,CAACG,OAAO,CAAC;;MAE7D,CAAC;MACDxC,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;KACD,CAAC;EACJ;EAEAqD,kBAAkBA,CAAChC,OAAY;IAC7BxB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEuB,OAAO,CAAC;IACjD,IAAIA,OAAO,CAACE,GAAG,EAAE;MACf,IAAI,CAAC5D,MAAM,CAAC2D,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACE,GAAG,CAAC,CAAC;KAChD,MAAM;MACL;MACAF,OAAO,CAACiC,WAAW,GAAG,CAACjC,OAAO,CAACiC,WAAW;;EAE9C;EAEA;EACAC,QAAQA,CAACvB,IAAmB;IAC1BnC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEkC,IAAI,CAACT,GAAG,CAAC;IACtC,IAAI,CAAC5D,MAAM,CAAC2D,QAAQ,CAAC,CAAC,OAAO,EAAEU,IAAI,CAACT,GAAG,CAAC,CAAC;EAC3C;EAEA;EACAiC,eAAeA,CAACpE,IAAS;IACvBS,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEV,IAAI,CAACqE,QAAQ,CAAC;IACnD,IAAI,CAAC9F,MAAM,CAAC2D,QAAQ,CAAC,CAAC,UAAU,EAAElC,IAAI,CAACqE,QAAQ,CAAC,CAAC;EACnD;EAEA;EACAC,MAAMA,CAACrC,OAAY;IACjBxB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEuB,OAAO,CAAC;IAEhC,IAAI,CAACA,OAAO,CAACG,EAAE,IAAI,CAACH,OAAO,CAACE,GAAG,EAAE;IAEjC,MAAMoC,SAAS,GAAGtC,OAAO,CAACG,EAAE,IAAIH,OAAO,CAACE,GAAG;IAE3C,IAAI,CAACzD,oBAAoB,CAAC4F,MAAM,CAAC;MAC/BC,SAAS,EAAEA,SAAS;MACpBC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE;KACZ,CAAC,CAAC1E,SAAS,CAAC;MACXM,IAAI,EAAG4C,MAAM,IAAI;QACf,IAAIA,MAAM,CAAC1C,OAAO,EAAE;UAClBE,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;UAC1D;SACD,MAAM;UACLD,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEqC,MAAM,CAACG,OAAO,CAAC;UACnD;UACA,IAAI,CAACsB,wBAAwB,CAACzC,OAAO,CAAC;;MAE1C,CAAC;MACDrB,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QACzC;QACA,IAAI,CAAC8D,wBAAwB,CAACzC,OAAO,CAAC;MACxC;KACD,CAAC;EACJ;EAEQyC,wBAAwBA,CAACzC,OAAY;IAC3C,IAAI,CAAC,IAAI,CAAC3C,WAAW,EAAE;MACrB,IAAI,CAACf,MAAM,CAAC2D,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;QAAEK,WAAW,EAAE;UAAEoC,SAAS,EAAE;QAAO;MAAE,CAAE,CAAC;MAC9E;;IAGF;IACA,IAAI,CAAClF,gBAAgB,GAAG;MACtBmF,MAAM,EAAE3C,OAAO,CAAC4C,KAAK;MACrBC,SAAS,EAAE;QACTC,KAAK,EAAE,CAAC;UACN3C,EAAE,EAAEH,OAAO,CAACG,EAAE;UACdI,IAAI,EAAEP,OAAO,CAACO,IAAI;UAClBqC,KAAK,EAAE5C,OAAO,CAAC4C,KAAK;UACpBL,QAAQ,EAAE,CAAC;UACXQ,KAAK,EAAE/C,OAAO,CAAC+C;SAChB,CAAC;QACFC,QAAQ,EAAEhD,OAAO,CAAC4C,KAAK;QACvBK,GAAG,EAAEjD,OAAO,CAAC4C,KAAK,GAAG,IAAI;QACzBM,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAEpD,OAAO,CAAC4C,KAAK,GAAI5C,OAAO,CAAC4C,KAAK,GAAG;OACzC;MACDS,WAAW,EAAE;QACX9C,IAAI,EAAE,IAAI,CAAClD,WAAW,CAACiG,QAAQ,IAAI,IAAI,CAACjG,WAAW,CAAC+E,QAAQ;QAC5DmB,KAAK,EAAE,IAAI,CAAClG,WAAW,CAACkG,KAAK;QAC7BC,KAAK,EAAE,IAAI,CAACnG,WAAW,CAACmG,KAAK,IAAI;;KAEpC;IAED,IAAI,CAACjG,gBAAgB,GAAG,IAAI;EAC9B;EAEAkG,aAAaA,CAACzD,OAAY;IACxBxB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEuB,OAAO,CAAC;IAExC,IAAI,CAACA,OAAO,CAACG,EAAE,IAAI,CAACH,OAAO,CAACE,GAAG,EAAE;IAEjC,MAAMoC,SAAS,GAAGtC,OAAO,CAACG,EAAE,IAAIH,OAAO,CAACE,GAAG;IAE3C,IAAI,CAACzD,oBAAoB,CAACgH,aAAa,CAAC;MACtCnB,SAAS,EAAEA,SAAS;MACpBE,SAAS,EAAE;KACZ,CAAC,CAAC1E,SAAS,CAAC;MACXM,IAAI,EAAG4C,MAAM,IAAI;QACf,IAAIA,MAAM,CAAC1C,OAAO,EAAE;UAClBE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;SACtD,MAAM;UACLD,OAAO,CAACG,KAAK,CAAC,4BAA4B,EAAEqC,MAAM,CAACG,OAAO,CAAC;;MAE/D,CAAC;MACDxC,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;KACD,CAAC;EACJ;EAEA+E,SAASA,CAAC1D,OAAY;IACpBxB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEuB,OAAO,CAAC;IAEpC,IAAI,CAACA,OAAO,CAACG,EAAE,IAAI,CAACH,OAAO,CAACE,GAAG,EAAE;IAEjC,MAAMoC,SAAS,GAAGtC,OAAO,CAACG,EAAE,IAAIH,OAAO,CAACE,GAAG;IAE3C,IAAI,CAACzD,oBAAoB,CAACiH,SAAS,CAAC;MAClCpB,SAAS,EAAEA,SAAS;MACpBC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE;KACZ,CAAC,CAAC1E,SAAS,CAAC;MACXM,IAAI,EAAG4C,MAAM,IAAI;QACf,IAAIA,MAAM,CAAC1C,OAAO,EAAE;UAClBE,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;SAClD,MAAM;UACLD,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEqC,MAAM,CAACG,OAAO,CAAC;;MAE3D,CAAC;MACDxC,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C;KACD,CAAC;EACJ;EAEAgF,UAAUA,CAAC5F,IAAS;IAClBS,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEV,IAAI,CAACqE,QAAQ,CAAC;IAC1C;EACF;EAEAwB,oBAAoBA,CAAA;IAClB,IAAI,CAACtH,MAAM,CAAC2D,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAAEK,WAAW,EAAE;QAAEuD,UAAU,EAAE;MAAY;IAAE,CAAE,CAAC;EAChF;EAEAC,cAAcA,CAACnD,IAAS;IACtBnC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEkC,IAAI,CAACR,EAAE,CAAC;IACjD;EACF;EAEA;EACA4D,WAAWA,CAACnB,KAAa;IACvB,OAAO,IAAIoB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAACxB,KAAK,CAAC;EAClB;EAEAyB,YAAYA,CAACC,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEAC,gBAAgBA,CAAC1D,KAAa;IAC5B,OAAO,IAAI,CAACsD,YAAY,CAACtD,KAAK,CAAC;EACjC;EAEA2D,UAAUA,CAACC,IAAU;IACnB,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,GAAG,CAACK,OAAO,EAAE,GAAGN,IAAI,CAACM,OAAO,EAAE,IAAI,IAAI,CAAC;IAEzE,IAAIH,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC,GAAG;IACrE,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAC,GAAG;IACxE,IAAIA,aAAa,GAAG,MAAM,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAC,GAAG;IAC1E,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,MAAM,CAAC,GAAG;EACjD;EAEAI,aAAaA,CAACC,KAAa,EAAExE,IAAS;IACpC,OAAOA,IAAI,CAACR,EAAE;EAChB;EAEA;EACAiF,kBAAkBA,CAACC,aAAkB;IACnC,IAAI,CAAC9H,gBAAgB,GAAG,KAAK;IAE7B,IAAI8H,aAAa,CAACC,MAAM,KAAK,SAAS,EAAE;MACtC;MACA,IAAI,CAAChJ,MAAM,CAAC2D,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE;QACzCK,WAAW,EAAE;UACXiF,OAAO,EAAEF,aAAa,CAACE,OAAO,IAAI,IAAI,CAACC,eAAe,EAAE;UACxDC,MAAM,EAAEJ,aAAa,CAACI;;OAEzB,CAAC;KACH,MAAM;MACL;MACAC,KAAK,CAAC,mCAAmC,CAAC;;EAE9C;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAACpI,gBAAgB,GAAG,KAAK;EAC/B;EAEQiI,eAAeA,CAAA;IACrB,OAAO,KAAK,GAAGX,IAAI,CAACD,GAAG,EAAE,CAACJ,QAAQ,EAAE;EACtC;EAEA;EACAoB,iBAAiBA,CAACC,UAAe;IAC/B,IAAI,CAACA,UAAU,EAAE;IAEjB,MAAMC,cAAc,GAAGD,UAAU,CAACC,cAAc,IAAI,SAAS;IAC7D,MAAMC,UAAU,GAAGF,UAAU,CAAC7F,OAAO,IAAI6F,UAAU,CAACxF,QAAQ,IAAIwF,UAAU,CAACG,MAAM,IAAIH,UAAU,CAACI,KAAK;IAErG,QAAQH,cAAc;MACpB,KAAK,SAAS;QACZ,IAAI,CAACI,iBAAiB,CAACH,UAAU,CAAC;QAClC;MACF,KAAK,UAAU;QACb,IAAI,CAACI,kBAAkB,CAACJ,UAAU,CAAC;QACnC;MACF,KAAK,QAAQ;QACX,IAAI,CAACK,gBAAgB,CAACL,UAAU,CAAC;QACjC;MACF,KAAK,OAAO;QACV,IAAI,CAACM,eAAe,CAACN,UAAU,CAAC;QAChC;MACF;QACE,IAAI,CAACG,iBAAiB,CAACH,UAAU,CAAC;;EAExC;EAEQG,iBAAiBA,CAAClG,OAAY;IACpC,IAAIA,OAAO,EAAEE,GAAG,IAAIF,OAAO,EAAEG,EAAE,EAAE;MAC/B,MAAMmC,SAAS,GAAGtC,OAAO,CAACE,GAAG,IAAIF,OAAO,CAACG,EAAE;MAC3C,IAAI,CAAC7D,MAAM,CAAC2D,QAAQ,CAAC,CAAC,UAAU,EAAEqC,SAAS,CAAC,CAAC;;EAEjD;EAEQ6D,kBAAkBA,CAAC9F,QAAa;IACtC,IAAIA,QAAQ,EAAEiG,IAAI,IAAIjG,QAAQ,EAAEE,IAAI,EAAE;MACpC,MAAMgG,YAAY,GAAGlG,QAAQ,CAACiG,IAAI,IAAIjG,QAAQ,CAACE,IAAI,CAACC,WAAW,EAAE,CAACgG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;MACtF,IAAI,CAAClK,MAAM,CAAC2D,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;QAC9BK,WAAW,EAAE;UAAED,QAAQ,EAAEkG;QAAY;OACtC,CAAC;;EAEN;EAEQH,gBAAgBA,CAACJ,MAAW;IAClC,IAAIA,MAAM,EAAE9F,GAAG,IAAI8F,MAAM,EAAE7F,EAAE,IAAI6F,MAAM,EAAE5D,QAAQ,EAAE;MACjD,MAAMqE,QAAQ,GAAGT,MAAM,CAAC9F,GAAG,IAAI8F,MAAM,CAAC7F,EAAE,IAAI6F,MAAM,CAAC5D,QAAQ;MAC3D,IAAI,CAAC9F,MAAM,CAAC2D,QAAQ,CAAC,CAAC,SAAS,EAAEwG,QAAQ,CAAC,CAAC;;EAE/C;EAEQJ,eAAeA,CAACJ,KAAU;IAChC,IAAIA,KAAK,EAAEK,IAAI,IAAIL,KAAK,EAAE1F,IAAI,EAAE;MAC9B,MAAMmG,SAAS,GAAGT,KAAK,CAACK,IAAI,IAAIL,KAAK,CAAC1F,IAAI,CAACC,WAAW,EAAE,CAACgG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;MAC7E,IAAI,CAAClK,MAAM,CAAC2D,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;QAC9BK,WAAW,EAAE;UAAE2F,KAAK,EAAES;QAAS;OAChC,CAAC;;EAEN;EAEAC,aAAaA,CAACd,UAAe;IAC3B,MAAMe,IAAI,GAAGf,UAAU,CAACC,cAAc,IAAI,SAAS;IACnD,MAAMvF,IAAI,GAAG,IAAI,CAACsG,UAAU,CAAChB,UAAU,CAAC;IAExC,QAAQe,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,iBAAiBrG,IAAI,EAAE;MAChC,KAAK,UAAU;QACb,OAAO,oBAAoBA,IAAI,EAAE;MACnC,KAAK,QAAQ;QACX,OAAO,iBAAiBA,IAAI,EAAE;MAChC,KAAK,OAAO;QACV,OAAO,eAAeA,IAAI,EAAE;MAC9B;QACE,OAAO,SAASA,IAAI,EAAE;;EAE5B;EAEAuG,UAAUA,CAAChB,cAAsB;IAC/B,QAAQA,cAAc;MACpB,KAAK,SAAS;QACZ,OAAO,YAAY;MACrB,KAAK,UAAU;QACb,OAAO,iBAAiB;MAC1B,KAAK,QAAQ;QACX,OAAO,cAAc;MACvB,KAAK,OAAO;QACV,OAAO,cAAc;MACvB;QACE,OAAO,YAAY;;EAEzB;EAEAe,UAAUA,CAAChB,UAAe;IACxB,MAAMe,IAAI,GAAGf,UAAU,CAACC,cAAc,IAAI,SAAS;IAEnD,QAAQc,IAAI;MACV,KAAK,SAAS;QACZ,OAAOf,UAAU,CAAC7F,OAAO,EAAEO,IAAI,IAAI,SAAS;MAC9C,KAAK,UAAU;QACb,OAAOsF,UAAU,CAACxF,QAAQ,EAAEE,IAAI,IAAI,UAAU;MAChD,KAAK,QAAQ;QACX,OAAOsF,UAAU,CAACG,MAAM,EAAE1C,QAAQ,IAAIuC,UAAU,CAACG,MAAM,EAAE5D,QAAQ,IAAI,QAAQ;MAC/E,KAAK,OAAO;QACV,OAAOyD,UAAU,CAACI,KAAK,EAAE1F,IAAI,IAAI,OAAO;MAC1C;QACE,OAAOsF,UAAU,CAAC7F,OAAO,EAAEO,IAAI,IAAI,MAAM;;EAE/C;EAEAwG,cAAcA,CAAClB,UAAe;IAC5B,MAAMe,IAAI,GAAGf,UAAU,CAACC,cAAc,IAAI,SAAS;IAEnD,QAAQc,IAAI;MACV,KAAK,SAAS;QACZ,OAAOf,UAAU,CAAC7F,OAAO,EAAE4C,KAAK,GAAG,IAAIiD,UAAU,CAAC7F,OAAO,CAAC4C,KAAK,EAAE,GAAG,EAAE;MACxE,KAAK,UAAU;QACb,OAAOiD,UAAU,CAACxF,QAAQ,EAAE2G,YAAY,GAAG,GAAGnB,UAAU,CAACxF,QAAQ,CAAC2G,YAAY,WAAW,GAAG,iBAAiB;MAC/G,KAAK,QAAQ;QACX,OAAOnB,UAAU,CAACG,MAAM,EAAEiB,QAAQ,IAAI,aAAa;MACrD,KAAK,OAAO;QACV,OAAOpB,UAAU,CAACI,KAAK,EAAEe,YAAY,GAAG,GAAGnB,UAAU,CAACI,KAAK,CAACe,YAAY,WAAW,GAAG,YAAY;MACpG;QACE,OAAO,EAAE;;EAEf;EAEAE,cAAcA,CAACrB,UAAe;IAC5B,MAAMe,IAAI,GAAGf,UAAU,CAACC,cAAc,IAAI,SAAS;IAEnD,QAAQc,IAAI;MACV,KAAK,SAAS;QACZ,OAAOf,UAAU,CAAC7F,OAAO,EAAEmH,MAAM,GAAG,CAAC,CAAC,EAAEC,GAAG,IAAI,wCAAwC;MACzF,KAAK,UAAU;QACb,OAAOvB,UAAU,CAACxF,QAAQ,EAAE0C,KAAK,IAAI,yCAAyC;MAChF,KAAK,QAAQ;QACX,OAAO8C,UAAU,CAACG,MAAM,EAAEqB,MAAM,IAAI,uCAAuC;MAC7E,KAAK,OAAO;QACV,OAAOxB,UAAU,CAACI,KAAK,EAAEqB,IAAI,IAAI,sCAAsC;MACzE;QACE,OAAOzB,UAAU,CAAC7F,OAAO,EAAEmH,MAAM,GAAG,CAAC,CAAC,EAAEC,GAAG,IAAI,wCAAwC;;EAE7F;CACD;AAnnBYhL,aAAa,GAAAmL,UAAA,EATzB5L,SAAS,CAAC;EACT6L,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC9L,YAAY,EAAEC,WAAW,EAAEC,YAAY,EAAEC,uBAAuB,EACxEI,yBAAyB,EAAED,aAAa,EAAED,gBAAgB,EAC1DD,qBAAqB,CAAC;EACxB2L,WAAW,EAAE,uBAAuB;EACpCC,SAAS,EAAE,CAAC,uBAAuB;CACpC,CAAC,C,EACWxL,aAAa,CAmnBzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}