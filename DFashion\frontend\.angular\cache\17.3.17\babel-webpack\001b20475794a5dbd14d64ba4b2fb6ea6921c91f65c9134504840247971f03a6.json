{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Validators, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../../../core/services/notification.service\";\nimport * as i5 from \"@angular/common\";\nfunction LoginComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"h5\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 21)(5, \"strong\");\n    i0.ɵɵtext(6, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelement(8, \"br\");\n    i0.ɵɵelementStart(9, \"strong\");\n    i0.ɵɵtext(10, \"Password:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_23_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.autoFillCredentials());\n    });\n    i0.ɵɵelement(13, \"i\", 23);\n    i0.ɵɵtext(14, \" Auto Fill \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Demo Credentials for \", i0.ɵɵpipeBind1(3, 3, ctx_r1.selectedLoginType), \":\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDemoCredentials().email, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDemoCredentials().password, \" \");\n  }\n}\nfunction LoginComponent_div_27_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_27_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Please enter a valid email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, LoginComponent_div_27_span_1_Template, 2, 0, \"span\", 25)(2, LoginComponent_div_27_span_2_Template, 2, 0, \"span\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.loginForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r1.loginForm.get(\"email\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"email\"]);\n  }\n}\nfunction LoginComponent_div_30_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_30_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password must be at least 6 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, LoginComponent_div_30_span_1_Template, 2, 0, \"span\", 25)(2, LoginComponent_div_30_span_2_Template, 2, 0, \"span\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.loginForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r1.loginForm.get(\"password\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"minlength\"]);\n  }\n}\nfunction LoginComponent_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 26);\n  }\n}\nfunction LoginComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.errorMessage, \" \");\n  }\n}\nexport class LoginComponent {\n  constructor(fb, authService, router, notificationService) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.notificationService = notificationService;\n    this.loading = false;\n    this.errorMessage = '';\n    this.selectedLoginType = 'customer';\n    this.demoCredentials = {\n      customer: {\n        email: '<EMAIL>',\n        password: 'password123'\n      },\n      admin: {\n        email: '<EMAIL>',\n        password: 'admin123'\n      },\n      vendor: {\n        email: '<EMAIL>',\n        password: 'vendor123'\n      }\n    };\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  selectLoginType(type) {\n    this.selectedLoginType = type;\n    this.errorMessage = '';\n  }\n  getDemoCredentials() {\n    return this.demoCredentials[this.selectedLoginType];\n  }\n  autoFillCredentials() {\n    const credentials = this.getDemoCredentials();\n    this.loginForm.patchValue({\n      email: credentials.email,\n      password: credentials.password\n    });\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.loading = true;\n      this.errorMessage = '';\n      this.authService.login(this.loginForm.value).subscribe({\n        next: response => {\n          this.loading = false;\n          this.notificationService.success('Login Successful!', `Welcome back, ${response.user.fullName}!`);\n          // Role-based redirect\n          if (response.user.role === 'admin') {\n            this.router.navigate(['/admin']);\n          } else if (response.user.role === 'vendor') {\n            this.router.navigate(['/vendor/dashboard']);\n          } else {\n            this.router.navigate(['/home']);\n          }\n        },\n        error: error => {\n          this.loading = false;\n          this.errorMessage = error.error?.message || 'Invalid credentials. Please check your email and password.';\n          this.notificationService.error('Login Failed', 'Please check your credentials and try again.');\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 40,\n      vars: 18,\n      consts: [[1, \"auth-container\"], [1, \"auth-card\"], [1, \"logo\"], [1, \"gradient-text\"], [1, \"login-type-selector\"], [1, \"login-type-buttons\"], [\"type\", \"button\", 1, \"login-type-btn\", 3, \"click\"], [1, \"fas\", \"fa-user\"], [1, \"fas\", \"fa-shield-alt\"], [1, \"fas\", \"fa-store\"], [\"class\", \"demo-credentials\", 4, \"ngIf\"], [1, \"auth-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"form-group\"], [\"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Email\", 1, \"form-control\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"type\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Password\", 1, \"form-control\"], [\"type\", \"submit\", 1, \"btn-primary\", \"auth-btn\", 3, \"disabled\"], [\"class\", \"loading-spinner\", 4, \"ngIf\"], [1, \"auth-link\"], [\"routerLink\", \"/auth/register\"], [1, \"demo-credentials\"], [1, \"credential-info\"], [\"type\", \"button\", 1, \"auto-fill-btn\", 3, \"click\"], [1, \"fas\", \"fa-magic\"], [1, \"error-message\"], [4, \"ngIf\"], [1, \"loading-spinner\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"DFashion\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\");\n          i0.ɵɵtext(6, \"Social E-commerce Platform\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"h4\");\n          i0.ɵɵtext(9, \"Login as:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 5)(11, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_11_listener() {\n            return ctx.selectLoginType(\"customer\");\n          });\n          i0.ɵɵelement(12, \"i\", 7);\n          i0.ɵɵelementStart(13, \"span\");\n          i0.ɵɵtext(14, \"Customer\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_15_listener() {\n            return ctx.selectLoginType(\"admin\");\n          });\n          i0.ɵɵelement(16, \"i\", 8);\n          i0.ɵɵelementStart(17, \"span\");\n          i0.ɵɵtext(18, \"Admin\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_19_listener() {\n            return ctx.selectLoginType(\"vendor\");\n          });\n          i0.ɵɵelement(20, \"i\", 9);\n          i0.ɵɵelementStart(21, \"span\");\n          i0.ɵɵtext(22, \"Vendor\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(23, LoginComponent_div_23_Template, 15, 5, \"div\", 10);\n          i0.ɵɵelementStart(24, \"form\", 11);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_24_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(25, \"div\", 12);\n          i0.ɵɵelement(26, \"input\", 13);\n          i0.ɵɵtemplate(27, LoginComponent_div_27_Template, 3, 2, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 12);\n          i0.ɵɵelement(29, \"input\", 15);\n          i0.ɵɵtemplate(30, LoginComponent_div_30_Template, 3, 2, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"button\", 16);\n          i0.ɵɵtemplate(32, LoginComponent_span_32_Template, 1, 0, \"span\", 17);\n          i0.ɵɵtext(33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(34, LoginComponent_div_34_Template, 2, 1, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 18)(36, \"p\");\n          i0.ɵɵtext(37, \"Don't have an account? \");\n          i0.ɵɵelementStart(38, \"a\", 19);\n          i0.ɵɵtext(39, \"Sign up\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_5_0;\n          let tmp_6_0;\n          let tmp_7_0;\n          let tmp_8_0;\n          i0.ɵɵadvance(11);\n          i0.ɵɵclassProp(\"active\", ctx.selectedLoginType === \"customer\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.selectedLoginType === \"admin\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.selectedLoginType === \"vendor\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedLoginType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"error\", ((tmp_5_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_6_0.touched));\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"error\", ((tmp_7_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_7_0.touched));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_8_0.touched));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Signing in...\" : \"Sign In\", \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, i5.TitleCasePipe, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, RouterModule, i3.RouterLink],\n      styles: [\".auth-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n}\\n\\n.auth-card[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 12px;\\n  padding: 40px;\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\\n  width: 100%;\\n  max-width: 400px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  font-weight: 700;\\n  margin-bottom: 8px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  font-size: 14px;\\n}\\n\\n.auth-form[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 16px;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  outline: none;\\n  transition: all 0.2s;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  border-color: var(--primary-color);\\n  box-shadow: 0 0 0 3px rgba(0, 149, 246, 0.1);\\n}\\n\\n.form-control.error[_ngcontent-%COMP%] {\\n  border-color: #ef4444;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  font-size: 12px;\\n  margin-top: 4px;\\n}\\n\\n.auth-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n}\\n\\n.auth-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n.login-type-selector[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  text-align: center;\\n}\\n\\n.login-type-selector[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  color: #262626;\\n}\\n\\n.login-type-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: center;\\n}\\n\\n.login-type-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 16px 12px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 8px;\\n  background: #fff;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n  min-width: 80px;\\n}\\n\\n.login-type-btn[_ngcontent-%COMP%]:hover {\\n  border-color: var(--primary-color);\\n  background: #f8fafc;\\n}\\n\\n.login-type-btn.active[_ngcontent-%COMP%] {\\n  border-color: var(--primary-color);\\n  background: var(--primary-color);\\n  color: white;\\n}\\n\\n.login-type-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n\\n.login-type-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n.demo-credentials[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 24px;\\n  text-align: center;\\n}\\n\\n.demo-credentials[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 12px;\\n  color: #475569;\\n}\\n\\n.credential-info[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  color: #64748b;\\n  margin-bottom: 12px;\\n  font-family: monospace;\\n  background: #fff;\\n  padding: 8px;\\n  border-radius: 4px;\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.auto-fill-btn[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin: 0 auto;\\n}\\n\\n.auto-fill-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-dark);\\n}\\n\\n.auth-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.auth-link[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #8e8e8e;\\n}\\n\\n.auth-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  text-decoration: none;\\n  font-weight: 600;\\n}\\n\\n.auth-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n@media (max-width: 480px) {\\n  .auth-card[_ngcontent-%COMP%] {\\n    padding: 24px;\\n  }\\n  .logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 28px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Validators", "ReactiveFormsModule", "RouterModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "LoginComponent_div_23_Template_button_click_12_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "autoFillCredentials", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "selectedLoginType", "getDemoCredentials", "email", "password", "ɵɵtemplate", "LoginComponent_div_27_span_1_Template", "LoginComponent_div_27_span_2_Template", "ɵɵproperty", "tmp_1_0", "loginForm", "get", "errors", "tmp_2_0", "LoginComponent_div_30_span_1_Template", "LoginComponent_div_30_span_2_Template", "errorMessage", "LoginComponent", "constructor", "fb", "authService", "router", "notificationService", "loading", "demoCredentials", "customer", "admin", "vendor", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "selectLoginType", "type", "credentials", "patchValue", "onSubmit", "valid", "login", "value", "subscribe", "next", "response", "success", "user", "fullName", "role", "navigate", "error", "message", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "NotificationService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "LoginComponent_Template_button_click_11_listener", "LoginComponent_Template_button_click_15_listener", "LoginComponent_Template_button_click_19_listener", "LoginComponent_div_23_Template", "LoginComponent_Template_form_ngSubmit_24_listener", "LoginComponent_div_27_Template", "LoginComponent_div_30_Template", "LoginComponent_span_32_Template", "LoginComponent_div_34_Template", "ɵɵclassProp", "tmp_5_0", "invalid", "touched", "tmp_6_0", "tmp_7_0", "tmp_8_0", "i5", "NgIf", "TitleCasePipe", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "RouterLink", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\auth\\pages\\login\\login.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule, TitleCasePipe } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { Router, RouterModule } from '@angular/router';\n\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { NotificationService } from '../../../../core/services/notification.service';\n\n@Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, RouterModule],\n  template: `\n    <div class=\"auth-container\">\n      <div class=\"auth-card\">\n        <!-- Logo -->\n        <div class=\"logo\">\n          <h1 class=\"gradient-text\">DFashion</h1>\n          <p>Social E-commerce Platform</p>\n        </div>\n\n        <!-- Login Type Selection -->\n        <div class=\"login-type-selector\">\n          <h4>Login as:</h4>\n          <div class=\"login-type-buttons\">\n            <button\n              type=\"button\"\n              class=\"login-type-btn\"\n              [class.active]=\"selectedLoginType === 'customer'\"\n              (click)=\"selectLoginType('customer')\"\n            >\n              <i class=\"fas fa-user\"></i>\n              <span>Customer</span>\n            </button>\n            <button\n              type=\"button\"\n              class=\"login-type-btn\"\n              [class.active]=\"selectedLoginType === 'admin'\"\n              (click)=\"selectLoginType('admin')\"\n            >\n              <i class=\"fas fa-shield-alt\"></i>\n              <span>Admin</span>\n            </button>\n            <button\n              type=\"button\"\n              class=\"login-type-btn\"\n              [class.active]=\"selectedLoginType === 'vendor'\"\n              (click)=\"selectLoginType('vendor')\"\n            >\n              <i class=\"fas fa-store\"></i>\n              <span>Vendor</span>\n            </button>\n          </div>\n        </div>\n\n        <!-- Demo Credentials Display -->\n        <div class=\"demo-credentials\" *ngIf=\"selectedLoginType\">\n          <h5>Demo Credentials for {{ selectedLoginType | titlecase }}:</h5>\n          <div class=\"credential-info\">\n            <strong>Email:</strong> {{ getDemoCredentials().email }}<br>\n            <strong>Password:</strong> {{ getDemoCredentials().password }}\n          </div>\n          <button type=\"button\" class=\"auto-fill-btn\" (click)=\"autoFillCredentials()\">\n            <i class=\"fas fa-magic\"></i> Auto Fill\n          </button>\n        </div>\n\n        <!-- Login Form -->\n        <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"auth-form\">\n          <div class=\"form-group\">\n            <input\n              type=\"email\"\n              formControlName=\"email\"\n              placeholder=\"Email\"\n              class=\"form-control\"\n              [class.error]=\"loginForm.get('email')?.invalid && loginForm.get('email')?.touched\"\n            >\n            <div *ngIf=\"loginForm.get('email')?.invalid && loginForm.get('email')?.touched\" class=\"error-message\">\n              <span *ngIf=\"loginForm.get('email')?.errors?.['required']\">Email is required</span>\n              <span *ngIf=\"loginForm.get('email')?.errors?.['email']\">Please enter a valid email</span>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <input\n              type=\"password\"\n              formControlName=\"password\"\n              placeholder=\"Password\"\n              class=\"form-control\"\n              [class.error]=\"loginForm.get('password')?.invalid && loginForm.get('password')?.touched\"\n            >\n            <div *ngIf=\"loginForm.get('password')?.invalid && loginForm.get('password')?.touched\" class=\"error-message\">\n              <span *ngIf=\"loginForm.get('password')?.errors?.['required']\">Password is required</span>\n              <span *ngIf=\"loginForm.get('password')?.errors?.['minlength']\">Password must be at least 6 characters</span>\n            </div>\n          </div>\n\n          <button \n            type=\"submit\" \n            class=\"btn-primary auth-btn\"\n            [disabled]=\"loginForm.invalid || loading\"\n          >\n            <span *ngIf=\"loading\" class=\"loading-spinner\"></span>\n            {{ loading ? 'Signing in...' : 'Sign In' }}\n          </button>\n\n          <div *ngIf=\"errorMessage\" class=\"error-message\">\n            {{ errorMessage }}\n          </div>\n        </form>\n\n\n\n        <!-- Register Link -->\n        <div class=\"auth-link\">\n          <p>Don't have an account? <a routerLink=\"/auth/register\">Sign up</a></p>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .auth-container {\n      min-height: 100vh;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 20px;\n    }\n\n    .auth-card {\n      background: #fff;\n      border-radius: 12px;\n      padding: 40px;\n      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n      width: 100%;\n      max-width: 400px;\n    }\n\n    .logo {\n      text-align: center;\n      margin-bottom: 32px;\n    }\n\n    .logo h1 {\n      font-size: 32px;\n      font-weight: 700;\n      margin-bottom: 8px;\n    }\n\n    .logo p {\n      color: #8e8e8e;\n      font-size: 14px;\n    }\n\n    .auth-form {\n      margin-bottom: 24px;\n    }\n\n    .form-group {\n      margin-bottom: 20px;\n    }\n\n    .form-control {\n      width: 100%;\n      padding: 12px 16px;\n      border: 1px solid #dbdbdb;\n      border-radius: 8px;\n      font-size: 14px;\n      outline: none;\n      transition: all 0.2s;\n    }\n\n    .form-control:focus {\n      border-color: var(--primary-color);\n      box-shadow: 0 0 0 3px rgba(0, 149, 246, 0.1);\n    }\n\n    .form-control.error {\n      border-color: #ef4444;\n    }\n\n    .error-message {\n      color: #ef4444;\n      font-size: 12px;\n      margin-top: 4px;\n    }\n\n    .auth-btn {\n      width: 100%;\n      padding: 12px;\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 16px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 8px;\n    }\n\n    .auth-btn:disabled {\n      opacity: 0.6;\n      cursor: not-allowed;\n    }\n\n    .login-type-selector {\n      margin-bottom: 24px;\n      text-align: center;\n    }\n\n    .login-type-selector h4 {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 16px;\n      color: #262626;\n    }\n\n    .login-type-buttons {\n      display: flex;\n      gap: 12px;\n      justify-content: center;\n    }\n\n    .login-type-btn {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 8px;\n      padding: 16px 12px;\n      border: 2px solid #e2e8f0;\n      border-radius: 8px;\n      background: #fff;\n      cursor: pointer;\n      transition: all 0.2s;\n      min-width: 80px;\n    }\n\n    .login-type-btn:hover {\n      border-color: var(--primary-color);\n      background: #f8fafc;\n    }\n\n    .login-type-btn.active {\n      border-color: var(--primary-color);\n      background: var(--primary-color);\n      color: white;\n    }\n\n    .login-type-btn i {\n      font-size: 20px;\n    }\n\n    .login-type-btn span {\n      font-size: 12px;\n      font-weight: 500;\n    }\n\n    .demo-credentials {\n      background: #f8fafc;\n      border: 1px solid #e2e8f0;\n      border-radius: 8px;\n      padding: 16px;\n      margin-bottom: 24px;\n      text-align: center;\n    }\n\n    .demo-credentials h5 {\n      font-size: 14px;\n      font-weight: 600;\n      margin-bottom: 12px;\n      color: #475569;\n    }\n\n    .credential-info {\n      font-size: 13px;\n      color: #64748b;\n      margin-bottom: 12px;\n      font-family: monospace;\n      background: #fff;\n      padding: 8px;\n      border-radius: 4px;\n      border: 1px solid #e2e8f0;\n    }\n\n    .auto-fill-btn {\n      background: var(--primary-color);\n      color: white;\n      border: none;\n      padding: 8px 16px;\n      border-radius: 6px;\n      font-size: 12px;\n      font-weight: 500;\n      cursor: pointer;\n      transition: all 0.2s;\n      display: flex;\n      align-items: center;\n      gap: 6px;\n      margin: 0 auto;\n    }\n\n    .auto-fill-btn:hover {\n      background: var(--primary-dark);\n    }\n\n    .auth-link {\n      text-align: center;\n    }\n\n    .auth-link p {\n      font-size: 14px;\n      color: #8e8e8e;\n    }\n\n    .auth-link a {\n      color: var(--primary-color);\n      text-decoration: none;\n      font-weight: 600;\n    }\n\n    .auth-link a:hover {\n      text-decoration: underline;\n    }\n\n    @media (max-width: 480px) {\n      .auth-card {\n        padding: 24px;\n      }\n\n      .logo h1 {\n        font-size: 28px;\n      }\n    }\n  `]\n})\nexport class LoginComponent {\n  loginForm: FormGroup;\n  loading = false;\n  errorMessage = '';\n  selectedLoginType: 'customer' | 'admin' | 'vendor' = 'customer';\n\n  private demoCredentials = {\n    customer: { email: '<EMAIL>', password: 'password123' },\n    admin: { email: '<EMAIL>', password: 'admin123' },\n    vendor: { email: '<EMAIL>', password: 'vendor123' }\n  };\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private notificationService: NotificationService\n  ) {\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  selectLoginType(type: 'customer' | 'admin' | 'vendor') {\n    this.selectedLoginType = type;\n    this.errorMessage = '';\n  }\n\n  getDemoCredentials() {\n    return this.demoCredentials[this.selectedLoginType];\n  }\n\n  autoFillCredentials() {\n    const credentials = this.getDemoCredentials();\n    this.loginForm.patchValue({\n      email: credentials.email,\n      password: credentials.password\n    });\n  }\n\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.loading = true;\n      this.errorMessage = '';\n\n      this.authService.login(this.loginForm.value).subscribe({\n        next: (response) => {\n          this.loading = false;\n          this.notificationService.success(\n            'Login Successful!',\n            `Welcome back, ${response.user.fullName}!`\n          );\n\n          // Role-based redirect\n          if (response.user.role === 'admin') {\n            this.router.navigate(['/admin']);\n          } else if (response.user.role === 'vendor') {\n            this.router.navigate(['/vendor/dashboard']);\n          } else {\n            this.router.navigate(['/home']);\n          }\n        },\n        error: (error) => {\n          this.loading = false;\n          this.errorMessage = error.error?.message || 'Invalid credentials. Please check your email and password.';\n          this.notificationService.error(\n            'Login Failed',\n            'Please check your credentials and try again.'\n          );\n        }\n      });\n    }\n  }\n\n\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAuB,iBAAiB;AAC7D,SAAiCC,UAAU,EAAEC,mBAAmB,QAAQ,gBAAgB;AACxF,SAAiBC,YAAY,QAAQ,iBAAiB;;;;;;;;;;IAsD5CC,EADF,CAAAC,cAAA,cAAwD,SAClD;IAAAD,EAAA,CAAAE,MAAA,GAAyD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEhEH,EADF,CAAAC,cAAA,cAA6B,aACnB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAI,SAAA,SAAI;IAC5DJ,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAC7B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAA4E;IAAhCD,EAAA,CAAAK,UAAA,mBAAAC,wDAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,mBAAA,EAAqB;IAAA,EAAC;IACzEZ,EAAA,CAAAI,SAAA,aAA4B;IAACJ,EAAA,CAAAE,MAAA,mBAC/B;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IARAH,EAAA,CAAAa,SAAA,GAAyD;IAAzDb,EAAA,CAAAc,kBAAA,0BAAAd,EAAA,CAAAe,WAAA,OAAAN,MAAA,CAAAO,iBAAA,OAAyD;IAEnChB,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAQ,kBAAA,GAAAC,KAAA,KAAgC;IAC7BlB,EAAA,CAAAa,SAAA,GAC7B;IAD6Bb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAQ,kBAAA,GAAAE,QAAA,MAC7B;;;;;IAiBInB,EAAA,CAAAC,cAAA,WAA2D;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACnFH,EAAA,CAAAC,cAAA,WAAwD;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF3FH,EAAA,CAAAC,cAAA,cAAsG;IAEpGD,EADA,CAAAoB,UAAA,IAAAC,qCAAA,mBAA2D,IAAAC,qCAAA,mBACH;IAC1DtB,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFGH,EAAA,CAAAa,SAAA,EAAkD;IAAlDb,EAAA,CAAAuB,UAAA,UAAAC,OAAA,GAAAf,MAAA,CAAAgB,SAAA,CAAAC,GAAA,4BAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAkD;IAClD3B,EAAA,CAAAa,SAAA,EAA+C;IAA/Cb,EAAA,CAAAuB,UAAA,UAAAK,OAAA,GAAAnB,MAAA,CAAAgB,SAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,UAA+C;;;;;IAatD3B,EAAA,CAAAC,cAAA,WAA8D;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACzFH,EAAA,CAAAC,cAAA,WAA+D;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF9GH,EAAA,CAAAC,cAAA,cAA4G;IAE1GD,EADA,CAAAoB,UAAA,IAAAS,qCAAA,mBAA8D,IAAAC,qCAAA,mBACC;IACjE9B,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFGH,EAAA,CAAAa,SAAA,EAAqD;IAArDb,EAAA,CAAAuB,UAAA,UAAAC,OAAA,GAAAf,MAAA,CAAAgB,SAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAqD;IACrD3B,EAAA,CAAAa,SAAA,EAAsD;IAAtDb,EAAA,CAAAuB,UAAA,UAAAK,OAAA,GAAAnB,MAAA,CAAAgB,SAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAAsD;;;;;IAS/D3B,EAAA,CAAAI,SAAA,eAAqD;;;;;IAIvDJ,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAL,MAAA,CAAAsB,YAAA,MACF;;;AAkOV,OAAM,MAAOC,cAAc;EAYzBC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,mBAAwC;IAHxC,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAd7B,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAP,YAAY,GAAG,EAAE;IACjB,KAAAf,iBAAiB,GAAoC,UAAU;IAEvD,KAAAuB,eAAe,GAAG;MACxBC,QAAQ,EAAE;QAAEtB,KAAK,EAAE,mBAAmB;QAAEC,QAAQ,EAAE;MAAa,CAAE;MACjEsB,KAAK,EAAE;QAAEvB,KAAK,EAAE,oBAAoB;QAAEC,QAAQ,EAAE;MAAU,CAAE;MAC5DuB,MAAM,EAAE;QAAExB,KAAK,EAAE,oBAAoB;QAAEC,QAAQ,EAAE;MAAW;KAC7D;IAQC,IAAI,CAACM,SAAS,GAAG,IAAI,CAACS,EAAE,CAACS,KAAK,CAAC;MAC7BzB,KAAK,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAAC+C,QAAQ,EAAE/C,UAAU,CAACqB,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACtB,UAAU,CAAC+C,QAAQ,EAAE/C,UAAU,CAACgD,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEAC,eAAeA,CAACC,IAAqC;IACnD,IAAI,CAAC/B,iBAAiB,GAAG+B,IAAI;IAC7B,IAAI,CAAChB,YAAY,GAAG,EAAE;EACxB;EAEAd,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACsB,eAAe,CAAC,IAAI,CAACvB,iBAAiB,CAAC;EACrD;EAEAJ,mBAAmBA,CAAA;IACjB,MAAMoC,WAAW,GAAG,IAAI,CAAC/B,kBAAkB,EAAE;IAC7C,IAAI,CAACQ,SAAS,CAACwB,UAAU,CAAC;MACxB/B,KAAK,EAAE8B,WAAW,CAAC9B,KAAK;MACxBC,QAAQ,EAAE6B,WAAW,CAAC7B;KACvB,CAAC;EACJ;EAEA+B,QAAQA,CAAA;IACN,IAAI,IAAI,CAACzB,SAAS,CAAC0B,KAAK,EAAE;MACxB,IAAI,CAACb,OAAO,GAAG,IAAI;MACnB,IAAI,CAACP,YAAY,GAAG,EAAE;MAEtB,IAAI,CAACI,WAAW,CAACiB,KAAK,CAAC,IAAI,CAAC3B,SAAS,CAAC4B,KAAK,CAAC,CAACC,SAAS,CAAC;QACrDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAClB,OAAO,GAAG,KAAK;UACpB,IAAI,CAACD,mBAAmB,CAACoB,OAAO,CAC9B,mBAAmB,EACnB,iBAAiBD,QAAQ,CAACE,IAAI,CAACC,QAAQ,GAAG,CAC3C;UAED;UACA,IAAIH,QAAQ,CAACE,IAAI,CAACE,IAAI,KAAK,OAAO,EAAE;YAClC,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;WACjC,MAAM,IAAIL,QAAQ,CAACE,IAAI,CAACE,IAAI,KAAK,QAAQ,EAAE;YAC1C,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;WAC5C,MAAM;YACL,IAAI,CAACzB,MAAM,CAACyB,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;;QAEnC,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACxB,OAAO,GAAG,KAAK;UACpB,IAAI,CAACP,YAAY,GAAG+B,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,4DAA4D;UACxG,IAAI,CAAC1B,mBAAmB,CAACyB,KAAK,CAC5B,cAAc,EACd,8CAA8C,CAC/C;QACH;OACD,CAAC;;EAEN;;;uBAzEW9B,cAAc,EAAAhC,EAAA,CAAAgE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlE,EAAA,CAAAgE,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAApE,EAAA,CAAAgE,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAtE,EAAA,CAAAgE,iBAAA,CAAAO,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAdxC,cAAc;MAAAyC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3E,EAAA,CAAA4E,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7TjBlF,EAJN,CAAAC,cAAA,aAA4B,aACH,aAEH,YACU;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,iCAA0B;UAC/BF,EAD+B,CAAAG,YAAA,EAAI,EAC7B;UAIJH,EADF,CAAAC,cAAA,aAAiC,SAC3B;UAAAD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEhBH,EADF,CAAAC,cAAA,cAAgC,iBAM7B;UADCD,EAAA,CAAAK,UAAA,mBAAA+E,iDAAA;YAAA,OAASD,GAAA,CAAArC,eAAA,CAAgB,UAAU,CAAC;UAAA,EAAC;UAErC9C,EAAA,CAAAI,SAAA,YAA2B;UAC3BJ,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAChBF,EADgB,CAAAG,YAAA,EAAO,EACd;UACTH,EAAA,CAAAC,cAAA,iBAKC;UADCD,EAAA,CAAAK,UAAA,mBAAAgF,iDAAA;YAAA,OAASF,GAAA,CAAArC,eAAA,CAAgB,OAAO,CAAC;UAAA,EAAC;UAElC9C,EAAA,CAAAI,SAAA,YAAiC;UACjCJ,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,aAAK;UACbF,EADa,CAAAG,YAAA,EAAO,EACX;UACTH,EAAA,CAAAC,cAAA,iBAKC;UADCD,EAAA,CAAAK,UAAA,mBAAAiF,iDAAA;YAAA,OAASH,GAAA,CAAArC,eAAA,CAAgB,QAAQ,CAAC;UAAA,EAAC;UAEnC9C,EAAA,CAAAI,SAAA,YAA4B;UAC5BJ,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAGlBF,EAHkB,CAAAG,YAAA,EAAO,EACZ,EACL,EACF;UAGNH,EAAA,CAAAoB,UAAA,KAAAmE,8BAAA,mBAAwD;UAYxDvF,EAAA,CAAAC,cAAA,gBAAwE;UAA1CD,EAAA,CAAAK,UAAA,sBAAAmF,kDAAA;YAAA,OAAYL,GAAA,CAAAjC,QAAA,EAAU;UAAA,EAAC;UACnDlD,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAI,SAAA,iBAMC;UACDJ,EAAA,CAAAoB,UAAA,KAAAqE,8BAAA,kBAAsG;UAIxGzF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAI,SAAA,iBAMC;UACDJ,EAAA,CAAAoB,UAAA,KAAAsE,8BAAA,kBAA4G;UAI9G1F,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,kBAIC;UACCD,EAAA,CAAAoB,UAAA,KAAAuE,+BAAA,mBAA8C;UAC9C3F,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAoB,UAAA,KAAAwE,8BAAA,kBAAgD;UAGlD5F,EAAA,CAAAG,YAAA,EAAO;UAMLH,EADF,CAAAC,cAAA,eAAuB,SAClB;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAC,cAAA,aAA+B;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAGtEF,EAHsE,CAAAG,YAAA,EAAI,EAAI,EACpE,EACF,EACF;;;;;;;UA1FIH,EAAA,CAAAa,SAAA,IAAiD;UAAjDb,EAAA,CAAA6F,WAAA,WAAAV,GAAA,CAAAnE,iBAAA,gBAAiD;UASjDhB,EAAA,CAAAa,SAAA,GAA8C;UAA9Cb,EAAA,CAAA6F,WAAA,WAAAV,GAAA,CAAAnE,iBAAA,aAA8C;UAS9ChB,EAAA,CAAAa,SAAA,GAA+C;UAA/Cb,EAAA,CAAA6F,WAAA,WAAAV,GAAA,CAAAnE,iBAAA,cAA+C;UAUtBhB,EAAA,CAAAa,SAAA,GAAuB;UAAvBb,EAAA,CAAAuB,UAAA,SAAA4D,GAAA,CAAAnE,iBAAA,CAAuB;UAYhDhB,EAAA,CAAAa,SAAA,EAAuB;UAAvBb,EAAA,CAAAuB,UAAA,cAAA4D,GAAA,CAAA1D,SAAA,CAAuB;UAOvBzB,EAAA,CAAAa,SAAA,GAAkF;UAAlFb,EAAA,CAAA6F,WAAA,YAAAC,OAAA,GAAAX,GAAA,CAAA1D,SAAA,CAAAC,GAAA,4BAAAoE,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAX,GAAA,CAAA1D,SAAA,CAAAC,GAAA,4BAAAoE,OAAA,CAAAE,OAAA,EAAkF;UAE9EhG,EAAA,CAAAa,SAAA,EAAwE;UAAxEb,EAAA,CAAAuB,UAAA,WAAA0E,OAAA,GAAAd,GAAA,CAAA1D,SAAA,CAAAC,GAAA,4BAAAuE,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAd,GAAA,CAAA1D,SAAA,CAAAC,GAAA,4BAAAuE,OAAA,CAAAD,OAAA,EAAwE;UAY5EhG,EAAA,CAAAa,SAAA,GAAwF;UAAxFb,EAAA,CAAA6F,WAAA,YAAAK,OAAA,GAAAf,GAAA,CAAA1D,SAAA,CAAAC,GAAA,+BAAAwE,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAf,GAAA,CAAA1D,SAAA,CAAAC,GAAA,+BAAAwE,OAAA,CAAAF,OAAA,EAAwF;UAEpFhG,EAAA,CAAAa,SAAA,EAA8E;UAA9Eb,EAAA,CAAAuB,UAAA,WAAA4E,OAAA,GAAAhB,GAAA,CAAA1D,SAAA,CAAAC,GAAA,+BAAAyE,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAhB,GAAA,CAAA1D,SAAA,CAAAC,GAAA,+BAAAyE,OAAA,CAAAH,OAAA,EAA8E;UASpFhG,EAAA,CAAAa,SAAA,EAAyC;UAAzCb,EAAA,CAAAuB,UAAA,aAAA4D,GAAA,CAAA1D,SAAA,CAAAsE,OAAA,IAAAZ,GAAA,CAAA7C,OAAA,CAAyC;UAElCtC,EAAA,CAAAa,SAAA,EAAa;UAAbb,EAAA,CAAAuB,UAAA,SAAA4D,GAAA,CAAA7C,OAAA,CAAa;UACpBtC,EAAA,CAAAa,SAAA,EACF;UADEb,EAAA,CAAAc,kBAAA,MAAAqE,GAAA,CAAA7C,OAAA,oCACF;UAEMtC,EAAA,CAAAa,SAAA,EAAkB;UAAlBb,EAAA,CAAAuB,UAAA,SAAA4D,GAAA,CAAApD,YAAA,CAAkB;;;qBA/FtBnC,YAAY,EAAAwG,EAAA,CAAAC,IAAA,EAAAD,EAAA,CAAAE,aAAA,EAAExG,mBAAmB,EAAAmE,EAAA,CAAAsC,aAAA,EAAAtC,EAAA,CAAAuC,oBAAA,EAAAvC,EAAA,CAAAwC,eAAA,EAAAxC,EAAA,CAAAyC,oBAAA,EAAAzC,EAAA,CAAA0C,kBAAA,EAAA1C,EAAA,CAAA2C,eAAA,EAAE7G,YAAY,EAAAsE,EAAA,CAAAwC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}