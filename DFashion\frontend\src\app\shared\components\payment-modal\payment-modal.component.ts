import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PaymentService } from '../../../core/services/payment.service';

export interface PaymentMethod {
  id: string;
  name: string;
  description: string;
  icon: string;
  enabled: boolean;
}

export interface PaymentModalData {
  amount: number;
  orderData: any;
  userDetails: any;
}

@Component({
  selector: 'app-payment-modal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './payment-modal.component.html',
  styleUrls: ['./payment-modal.component.scss']
})
export class PaymentModalComponent implements OnInit {
  @Input() isVisible = false;
  @Input() paymentData: PaymentModalData | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() paymentSelected = new EventEmitter<string>();
  @Output() paymentCompleted = new EventEmitter<any>();

  paymentMethods: PaymentMethod[] = [];
  selectedPaymentMethod: string = '';
  isProcessing = false;
  showConfirmation = false;
  paymentStatus: string = 'idle';

  constructor(private paymentService: PaymentService) {}

  ngOnInit() {
    this.paymentMethods = this.paymentService.getPaymentMethods();
    
    // Subscribe to payment status
    this.paymentService.paymentStatus$.subscribe(status => {
      this.paymentStatus = status;
      this.handlePaymentStatusChange(status);
    });
  }

  selectPaymentMethod(methodId: string) {
    this.selectedPaymentMethod = methodId;
    this.paymentSelected.emit(methodId);
  }

  async proceedWithPayment() {
    if (!this.selectedPaymentMethod || !this.paymentData) {
      return;
    }

    this.isProcessing = true;

    try {
      if (this.selectedPaymentMethod === 'cod') {
        // Handle Cash on Delivery
        this.handleCODPayment();
      } else {
        // Handle online payment methods
        await this.paymentService.initiatePayment(
          this.paymentData.orderData,
          this.paymentData.userDetails
        );
      }
    } catch (error) {
      console.error('Payment failed:', error);
      this.isProcessing = false;
      this.showPaymentMessage('Payment failed. Please try again.', 'error');
    }
  }

  private handleCODPayment() {
    // Simulate COD order processing
    setTimeout(() => {
      this.isProcessing = false;
      this.showPaymentMessage('Order placed successfully! You can pay when the order is delivered.', 'success');
      
      // Emit payment completion for COD
      this.paymentCompleted.emit({
        method: 'cod',
        orderId: this.generateOrderId(),
        status: 'success'
      });
    }, 2000);
  }

  private handlePaymentStatusChange(status: string) {
    this.isProcessing = status === 'processing';
    
    switch (status) {
      case 'success':
        this.showPaymentMessage('Payment successful! Your order has been confirmed.', 'success');
        this.paymentCompleted.emit({
          method: this.selectedPaymentMethod,
          status: 'success'
        });
        break;
      case 'failed':
        this.showPaymentMessage('Payment failed. Please try again or choose a different payment method.', 'error');
        break;
      case 'cancelled':
        this.showPaymentMessage('Payment was cancelled.', 'warning');
        break;
    }
  }

  private showPaymentMessage(message: string, type: 'success' | 'error' | 'warning') {
    // This could be replaced with a toast notification service
    alert(`${type.toUpperCase()}: ${message}`);
    
    if (type === 'success') {
      setTimeout(() => {
        this.closeModal();
      }, 2000);
    }
  }

  private generateOrderId(): string {
    return 'ORD' + Date.now().toString();
  }

  closeModal() {
    this.isVisible = false;
    this.selectedPaymentMethod = '';
    this.isProcessing = false;
    this.showConfirmation = false;
    this.paymentService.resetPaymentStatus();
    this.close.emit();
  }

  getPaymentMethodIcon(method: PaymentMethod): string {
    const iconMap: { [key: string]: string } = {
      'razorpay': 'fas fa-credit-card',
      'upi': 'fas fa-mobile-alt',
      'netbanking': 'fas fa-university',
      'wallet': 'fas fa-wallet',
      'cod': 'fas fa-money-bill-wave'
    };
    
    return iconMap[method.id] || method.icon;
  }

  getPaymentMethodColor(methodId: string): string {
    const colorMap: { [key: string]: string } = {
      'razorpay': '#667eea',
      'upi': '#00d4aa',
      'netbanking': '#ff6b6b',
      'wallet': '#4ecdc4',
      'cod': '#45b7d1'
    };
    
    return colorMap[methodId] || '#667eea';
  }

  formatAmount(amount: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  }
}
