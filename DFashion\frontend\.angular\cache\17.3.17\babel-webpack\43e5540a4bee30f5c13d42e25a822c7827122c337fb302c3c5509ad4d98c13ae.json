{"ast": null, "code": "import { TemplatePortal, CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport { CdkStepLabel, CdkStepHeader, CdkStep, STEPPER_GLOBAL_OPTIONS, CdkStepper, CdkStepperNext, CdkStepperPrevious, CdkStepperModule } from '@angular/cdk/stepper';\nimport { NgTemplateOutlet, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Injectable, Optional, SkipSelf, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, forwardRef, Inject, ContentChild, QueryList, EventEmitter, inject, ViewChildren, ContentChildren, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/material/core';\nimport { MatRipple, ErrorStateMatcher, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport { MatIcon, MatIconModule } from '@angular/material/icon';\nimport * as i2 from '@angular/cdk/a11y';\nimport { Subject, Subscription } from 'rxjs';\nimport * as i2$1 from '@angular/cdk/bidi';\nimport { switchMap, map, startWith, takeUntil, distinctUntilChanged } from 'rxjs/operators';\nimport { trigger, state, style, transition, group, animate, query, animateChild } from '@angular/animations';\nimport { Platform } from '@angular/cdk/platform';\nfunction MatStepHeader_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.iconOverrides[ctx_r0.state])(\"ngTemplateOutletContext\", ctx_r0._getIconContext());\n  }\n}\nfunction MatStepHeader_Conditional_4_Case_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0._getDefaultTextForState(ctx_r0.state));\n  }\n}\nfunction MatStepHeader_Conditional_4_Case_1_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0._intl.completedLabel);\n  }\n}\nfunction MatStepHeader_Conditional_4_Case_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0._intl.editableLabel);\n  }\n}\nfunction MatStepHeader_Conditional_4_Case_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatStepHeader_Conditional_4_Case_1_Conditional_0_Template, 2, 1, \"span\", 8)(1, MatStepHeader_Conditional_4_Case_1_Conditional_1_Template, 2, 1);\n    i0.ɵɵelementStart(2, \"mat-icon\", 7);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(0, ctx_r0.state === \"done\" ? 0 : ctx_r0.state === \"edit\" ? 1 : -1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0._getDefaultTextForState(ctx_r0.state));\n  }\n}\nfunction MatStepHeader_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatStepHeader_Conditional_4_Case_0_Template, 2, 1)(1, MatStepHeader_Conditional_4_Case_1_Template, 4, 2);\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, (tmp_1_0 = ctx_r0.state) === \"number\" ? 0 : 1);\n  }\n}\nfunction MatStepHeader_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelementContainer(1, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.template);\n  }\n}\nfunction MatStepHeader_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.label);\n  }\n}\nfunction MatStepHeader_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0._intl.optionalLabel);\n  }\n}\nfunction MatStepHeader_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n  }\n}\nconst _c0 = [\"*\"];\nfunction MatStep_ng_template_0_ng_template_1_Template(rf, ctx) {}\nfunction MatStep_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n    i0.ɵɵtemplate(1, MatStep_ng_template_0_ng_template_1_Template, 0, 0, \"ng-template\", 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"cdkPortalOutlet\", ctx_r0._portal);\n  }\n}\nconst _c1 = (a0, a1) => ({\n  step: a0,\n  i: a1\n});\nconst _c2 = a0 => ({\n  \"animationDuration\": a0\n});\nconst _c3 = (a0, a1) => ({\n  \"value\": a0,\n  \"params\": a1\n});\nfunction MatStepper_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction MatStepper_Case_1_For_3_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 6);\n  }\n}\nfunction MatStepper_Case_1_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 5);\n    i0.ɵɵtemplate(1, MatStepper_Case_1_For_3_Conditional_1_Template, 1, 0, \"div\", 6);\n  }\n  if (rf & 2) {\n    const step_r1 = ctx.$implicit;\n    const i_r2 = ctx.$index;\n    const ɵi_8_r3 = ctx.$index;\n    const ɵ$count_8_r4 = ctx.$count;\n    i0.ɵɵnextContext(2);\n    const stepTemplate_r5 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", stepTemplate_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(3, _c1, step_r1, i_r2));\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, !(ɵi_8_r3 === ɵ$count_8_r4 - 1) ? 1 : -1);\n  }\n}\nfunction MatStepper_Case_1_For_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵlistener(\"@horizontalStepTransition.done\", function MatStepper_Case_1_For_6_Template_div_animation_horizontalStepTransition_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6._animationDone.next($event));\n    });\n    i0.ɵɵelementContainer(1, 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r8 = ctx.$implicit;\n    const i_r9 = ctx.$index;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"mat-horizontal-stepper-content-inactive\", ctx_r6.selectedIndex !== i_r9);\n    i0.ɵɵproperty(\"@horizontalStepTransition\", i0.ɵɵpureFunction2(8, _c3, ctx_r6._getAnimationDirection(i_r9), i0.ɵɵpureFunction1(6, _c2, ctx_r6._getAnimationDuration())))(\"id\", ctx_r6._getStepContentId(i_r9));\n    i0.ɵɵattribute(\"aria-labelledby\", ctx_r6._getStepLabelId(i_r9));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", step_r8.content);\n  }\n}\nfunction MatStepper_Case_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n    i0.ɵɵrepeaterCreate(2, MatStepper_Case_1_For_3_Template, 2, 6, null, null, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 3);\n    i0.ɵɵrepeaterCreate(5, MatStepper_Case_1_For_6_Template, 2, 11, \"div\", 4, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵrepeater(ctx_r6.steps);\n    i0.ɵɵadvance(3);\n    i0.ɵɵrepeater(ctx_r6.steps);\n  }\n}\nfunction MatStepper_Case_2_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelementContainer(1, 5);\n    i0.ɵɵelementStart(2, \"div\", 10)(3, \"div\", 11);\n    i0.ɵɵlistener(\"@verticalStepTransition.done\", function MatStepper_Case_2_For_1_Template_div_animation_verticalStepTransition_done_3_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6._animationDone.next($event));\n    });\n    i0.ɵɵelementStart(4, \"div\", 12);\n    i0.ɵɵelementContainer(5, 8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const step_r11 = ctx.$implicit;\n    const i_r12 = ctx.$index;\n    const ɵi_22_r13 = ctx.$index;\n    const ɵ$count_22_r14 = ctx.$count;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    const stepTemplate_r5 = i0.ɵɵreference(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", stepTemplate_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(10, _c1, step_r11, i_r12));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"mat-stepper-vertical-line\", !(ɵi_22_r13 === ɵ$count_22_r14 - 1));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"mat-vertical-stepper-content-inactive\", ctx_r6.selectedIndex !== i_r12);\n    i0.ɵɵproperty(\"@verticalStepTransition\", i0.ɵɵpureFunction2(15, _c3, ctx_r6._getAnimationDirection(i_r12), i0.ɵɵpureFunction1(13, _c2, ctx_r6._getAnimationDuration())))(\"id\", ctx_r6._getStepContentId(i_r12));\n    i0.ɵɵattribute(\"aria-labelledby\", ctx_r6._getStepLabelId(i_r12));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", step_r11.content);\n  }\n}\nfunction MatStepper_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, MatStepper_Case_2_For_1_Template, 6, 18, \"div\", 9, i0.ɵɵrepeaterTrackByIdentity);\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r6.steps);\n  }\n}\nfunction MatStepper_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-step-header\", 13);\n    i0.ɵɵlistener(\"click\", function MatStepper_ng_template_3_Template_mat_step_header_click_0_listener() {\n      const step_r16 = i0.ɵɵrestoreView(_r15).step;\n      return i0.ɵɵresetView(step_r16.select());\n    })(\"keydown\", function MatStepper_ng_template_3_Template_mat_step_header_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6._onKeydown($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r16 = ctx.step;\n    const i_r17 = ctx.i;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-horizontal-stepper-header\", ctx_r6.orientation === \"horizontal\")(\"mat-vertical-stepper-header\", ctx_r6.orientation === \"vertical\");\n    i0.ɵɵproperty(\"tabIndex\", ctx_r6._getFocusIndex() === i_r17 ? 0 : -1)(\"id\", ctx_r6._getStepLabelId(i_r17))(\"index\", i_r17)(\"state\", ctx_r6._getIndicatorType(i_r17, step_r16.state))(\"label\", step_r16.stepLabel || step_r16.label)(\"selected\", ctx_r6.selectedIndex === i_r17)(\"active\", ctx_r6._stepIsNavigable(i_r17, step_r16))(\"optional\", step_r16.optional)(\"errorMessage\", step_r16.errorMessage)(\"iconOverrides\", ctx_r6._iconOverrides)(\"disableRipple\", ctx_r6.disableRipple || !ctx_r6._stepIsNavigable(i_r17, step_r16))(\"color\", step_r16.color || ctx_r6.color);\n    i0.ɵɵattribute(\"aria-posinset\", i_r17 + 1)(\"aria-setsize\", ctx_r6.steps.length)(\"aria-controls\", ctx_r6._getStepContentId(i_r17))(\"aria-selected\", ctx_r6.selectedIndex == i_r17)(\"aria-label\", step_r16.ariaLabel || null)(\"aria-labelledby\", !step_r16.ariaLabel && step_r16.ariaLabelledby ? step_r16.ariaLabelledby : null)(\"aria-disabled\", ctx_r6._stepIsNavigable(i_r17, step_r16) ? null : true);\n  }\n}\nlet MatStepLabel = /*#__PURE__*/(() => {\n  class MatStepLabel extends CdkStepLabel {\n    static {\n      this.ɵfac = /* @__PURE__ */(() => {\n        let ɵMatStepLabel_BaseFactory;\n        return function MatStepLabel_Factory(t) {\n          return (ɵMatStepLabel_BaseFactory || (ɵMatStepLabel_BaseFactory = i0.ɵɵgetInheritedFactory(MatStepLabel)))(t || MatStepLabel);\n        };\n      })();\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatStepLabel,\n        selectors: [[\"\", \"matStepLabel\", \"\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return MatStepLabel;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Stepper data that is required for internationalization. */\nlet MatStepperIntl = /*#__PURE__*/(() => {\n  class MatStepperIntl {\n    constructor() {\n      /**\n       * Stream that emits whenever the labels here are changed. Use this to notify\n       * components if the labels have changed after initialization.\n       */\n      this.changes = new Subject();\n      /** Label that is rendered below optional steps. */\n      this.optionalLabel = 'Optional';\n      /** Label that is used to indicate step as completed to screen readers. */\n      this.completedLabel = 'Completed';\n      /** Label that is used to indicate step as editable to screen readers. */\n      this.editableLabel = 'Editable';\n    }\n    static {\n      this.ɵfac = function MatStepperIntl_Factory(t) {\n        return new (t || MatStepperIntl)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: MatStepperIntl,\n        factory: MatStepperIntl.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return MatStepperIntl;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** @docs-private */\nfunction MAT_STEPPER_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatStepperIntl();\n}\n/** @docs-private */\nconst MAT_STEPPER_INTL_PROVIDER = {\n  provide: MatStepperIntl,\n  deps: [[/*#__PURE__*/new Optional(), /*#__PURE__*/new SkipSelf(), MatStepperIntl]],\n  useFactory: MAT_STEPPER_INTL_PROVIDER_FACTORY\n};\nlet MatStepHeader = /*#__PURE__*/(() => {\n  class MatStepHeader extends CdkStepHeader {\n    constructor(_intl, _focusMonitor, _elementRef, changeDetectorRef) {\n      super(_elementRef);\n      this._intl = _intl;\n      this._focusMonitor = _focusMonitor;\n      this._intlSubscription = _intl.changes.subscribe(() => changeDetectorRef.markForCheck());\n    }\n    ngAfterViewInit() {\n      this._focusMonitor.monitor(this._elementRef, true);\n    }\n    ngOnDestroy() {\n      this._intlSubscription.unsubscribe();\n      this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    /** Focuses the step header. */\n    focus(origin, options) {\n      if (origin) {\n        this._focusMonitor.focusVia(this._elementRef, origin, options);\n      } else {\n        this._elementRef.nativeElement.focus(options);\n      }\n    }\n    /** Returns string label of given step if it is a text label. */\n    _stringLabel() {\n      return this.label instanceof MatStepLabel ? null : this.label;\n    }\n    /** Returns MatStepLabel if the label of given step is a template label. */\n    _templateLabel() {\n      return this.label instanceof MatStepLabel ? this.label : null;\n    }\n    /** Returns the host HTML element. */\n    _getHostElement() {\n      return this._elementRef.nativeElement;\n    }\n    /** Template context variables that are exposed to the `matStepperIcon` instances. */\n    _getIconContext() {\n      return {\n        index: this.index,\n        active: this.active,\n        optional: this.optional\n      };\n    }\n    _getDefaultTextForState(state) {\n      if (state == 'number') {\n        return `${this.index + 1}`;\n      }\n      if (state == 'edit') {\n        return 'create';\n      }\n      if (state == 'error') {\n        return 'warning';\n      }\n      return state;\n    }\n    static {\n      this.ɵfac = function MatStepHeader_Factory(t) {\n        return new (t || MatStepHeader)(i0.ɵɵdirectiveInject(MatStepperIntl), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatStepHeader,\n        selectors: [[\"mat-step-header\"]],\n        hostAttrs: [\"role\", \"tab\", 1, \"mat-step-header\"],\n        hostVars: 2,\n        hostBindings: function MatStepHeader_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassMap(\"mat-\" + (ctx.color || \"primary\"));\n          }\n        },\n        inputs: {\n          state: \"state\",\n          label: \"label\",\n          errorMessage: \"errorMessage\",\n          iconOverrides: \"iconOverrides\",\n          index: \"index\",\n          selected: \"selected\",\n          active: \"active\",\n          optional: \"optional\",\n          disableRipple: \"disableRipple\",\n          color: \"color\"\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 10,\n        vars: 17,\n        consts: [[\"matRipple\", \"\", 1, \"mat-step-header-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mat-step-icon-content\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"mat-step-label\"], [1, \"mat-step-text-label\"], [1, \"mat-step-optional\"], [1, \"mat-step-sub-label-error\"], [\"aria-hidden\", \"true\"], [1, \"cdk-visually-hidden\"], [3, \"ngTemplateOutlet\"]],\n        template: function MatStepHeader_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"div\", 0);\n            i0.ɵɵelementStart(1, \"div\")(2, \"div\", 1);\n            i0.ɵɵtemplate(3, MatStepHeader_Conditional_3_Template, 1, 2, \"ng-container\", 2)(4, MatStepHeader_Conditional_4_Template, 2, 1);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 3);\n            i0.ɵɵtemplate(6, MatStepHeader_Conditional_6_Template, 2, 1, \"div\", 4)(7, MatStepHeader_Conditional_7_Template, 2, 1)(8, MatStepHeader_Conditional_8_Template, 2, 1, \"div\", 5)(9, MatStepHeader_Conditional_9_Template, 2, 1, \"div\", 6);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            let tmp_8_0;\n            i0.ɵɵproperty(\"matRippleTrigger\", ctx._getHostElement())(\"matRippleDisabled\", ctx.disableRipple);\n            i0.ɵɵadvance();\n            i0.ɵɵclassMapInterpolate1(\"mat-step-icon-state-\", ctx.state, \" mat-step-icon\");\n            i0.ɵɵclassProp(\"mat-step-icon-selected\", ctx.selected);\n            i0.ɵɵadvance(2);\n            i0.ɵɵconditional(3, ctx.iconOverrides && ctx.iconOverrides[ctx.state] ? 3 : 4);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"mat-step-label-active\", ctx.active)(\"mat-step-label-selected\", ctx.selected)(\"mat-step-label-error\", ctx.state == \"error\");\n            i0.ɵɵadvance();\n            i0.ɵɵconditional(6, (tmp_8_0 = ctx._templateLabel()) ? 6 : ctx._stringLabel() ? 7 : -1, tmp_8_0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵconditional(8, ctx.optional && ctx.state != \"error\" ? 8 : -1);\n            i0.ɵɵadvance();\n            i0.ɵɵconditional(9, ctx.state === \"error\" ? 9 : -1);\n          }\n        },\n        dependencies: [MatRipple, NgTemplateOutlet, MatIcon],\n        styles: [\".mat-step-header{overflow:hidden;outline:none;cursor:pointer;position:relative;box-sizing:content-box;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-step-header:focus .mat-focus-indicator::before{content:\\\"\\\"}.mat-step-header:hover[aria-disabled=true]{cursor:default}.mat-step-header:hover:not([aria-disabled]),.mat-step-header:hover[aria-disabled=false]{background-color:var(--mat-stepper-header-hover-state-layer-color);border-radius:var(--mat-stepper-header-hover-state-layer-shape)}.mat-step-header.cdk-keyboard-focused,.mat-step-header.cdk-program-focused{background-color:var(--mat-stepper-header-focus-state-layer-color);border-radius:var(--mat-stepper-header-focus-state-layer-shape)}@media(hover: none){.mat-step-header:hover{background:none}}.cdk-high-contrast-active .mat-step-header{outline:solid 1px}.cdk-high-contrast-active .mat-step-header[aria-selected=true] .mat-step-label{text-decoration:underline}.cdk-high-contrast-active .mat-step-header[aria-disabled=true]{outline-color:GrayText}.cdk-high-contrast-active .mat-step-header[aria-disabled=true] .mat-step-label,.cdk-high-contrast-active .mat-step-header[aria-disabled=true] .mat-step-icon,.cdk-high-contrast-active .mat-step-header[aria-disabled=true] .mat-step-optional{color:GrayText}.mat-step-optional{font-size:12px;color:var(--mat-stepper-header-optional-label-text-color)}.mat-step-sub-label-error{font-size:12px;font-weight:normal}.mat-step-icon{border-radius:50%;height:24px;width:24px;flex-shrink:0;position:relative;color:var(--mat-stepper-header-icon-foreground-color);background-color:var(--mat-stepper-header-icon-background-color)}.mat-step-icon-content{position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);display:flex}.mat-step-icon .mat-icon{font-size:16px;height:16px;width:16px}.mat-step-icon-state-error{background-color:var(--mat-stepper-header-error-state-icon-background-color);color:var(--mat-stepper-header-error-state-icon-foreground-color)}.mat-step-icon-state-error .mat-icon{font-size:24px;height:24px;width:24px}.mat-step-label{display:inline-block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;min-width:50px;vertical-align:middle;font-family:var(--mat-stepper-header-label-text-font);font-size:var(--mat-stepper-header-label-text-size);font-weight:var(--mat-stepper-header-label-text-weight);color:var(--mat-stepper-header-label-text-color)}.mat-step-label.mat-step-label-active{color:var(--mat-stepper-header-selected-state-label-text-color)}.mat-step-label.mat-step-label-error{color:var(--mat-stepper-header-error-state-label-text-color);font-size:var(--mat-stepper-header-error-state-label-text-size)}.mat-step-label.mat-step-label-selected{font-size:var(--mat-stepper-header-selected-state-label-text-size);font-weight:var(--mat-stepper-header-selected-state-label-text-weight)}.mat-step-text-label{text-overflow:ellipsis;overflow:hidden}.mat-step-header .mat-step-header-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-step-icon-selected{background-color:var(--mat-stepper-header-selected-state-icon-background-color);color:var(--mat-stepper-header-selected-state-icon-foreground-color)}.mat-step-icon-state-done{background-color:var(--mat-stepper-header-done-state-icon-background-color);color:var(--mat-stepper-header-done-state-icon-foreground-color)}.mat-step-icon-state-edit{background-color:var(--mat-stepper-header-edit-state-icon-background-color);color:var(--mat-stepper-header-edit-state-icon-foreground-color)}\"],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatStepHeader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst DEFAULT_HORIZONTAL_ANIMATION_DURATION = '500ms';\nconst DEFAULT_VERTICAL_ANIMATION_DURATION = '225ms';\n/**\n * Animations used by the Material steppers.\n * @docs-private\n */\nconst matStepperAnimations = {\n  /** Animation that transitions the step along the X axis in a horizontal stepper. */\n  horizontalStepTransition: /*#__PURE__*/trigger('horizontalStepTransition', [/*#__PURE__*/state('previous', /*#__PURE__*/style({\n    transform: 'translate3d(-100%, 0, 0)',\n    visibility: 'hidden'\n  })),\n  /*#__PURE__*/\n  // Transition to `inherit`, rather than `visible`,\n  // because visibility on a child element the one from the parent,\n  // making this element focusable inside of a `hidden` element.\n  state('current', /*#__PURE__*/style({\n    transform: 'none',\n    visibility: 'inherit'\n  })), /*#__PURE__*/state('next', /*#__PURE__*/style({\n    transform: 'translate3d(100%, 0, 0)',\n    visibility: 'hidden'\n  })), /*#__PURE__*/transition('* => *', /*#__PURE__*/group([/*#__PURE__*/animate('{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)'), /*#__PURE__*/query('@*', /*#__PURE__*/animateChild(), {\n    optional: true\n  })]), {\n    params: {\n      'animationDuration': DEFAULT_HORIZONTAL_ANIMATION_DURATION\n    }\n  })]),\n  /** Animation that transitions the step along the Y axis in a vertical stepper. */\n  verticalStepTransition: /*#__PURE__*/trigger('verticalStepTransition', [/*#__PURE__*/state('previous', /*#__PURE__*/style({\n    height: '0px',\n    visibility: 'hidden'\n  })), /*#__PURE__*/state('next', /*#__PURE__*/style({\n    height: '0px',\n    visibility: 'hidden'\n  })),\n  /*#__PURE__*/\n  // Transition to `inherit`, rather than `visible`,\n  // because visibility on a child element the one from the parent,\n  // making this element focusable inside of a `hidden` element.\n  state('current', /*#__PURE__*/style({\n    height: '*',\n    visibility: 'inherit'\n  })), /*#__PURE__*/transition('* <=> current', /*#__PURE__*/group([/*#__PURE__*/animate('{{animationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)'), /*#__PURE__*/query('@*', /*#__PURE__*/animateChild(), {\n    optional: true\n  })]), {\n    params: {\n      'animationDuration': DEFAULT_VERTICAL_ANIMATION_DURATION\n    }\n  })])\n};\n\n/**\n * Template to be used to override the icons inside the step header.\n */\nlet MatStepperIcon = /*#__PURE__*/(() => {\n  class MatStepperIcon {\n    constructor(templateRef) {\n      this.templateRef = templateRef;\n    }\n    static {\n      this.ɵfac = function MatStepperIcon_Factory(t) {\n        return new (t || MatStepperIcon)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatStepperIcon,\n        selectors: [[\"ng-template\", \"matStepperIcon\", \"\"]],\n        inputs: {\n          name: [i0.ɵɵInputFlags.None, \"matStepperIcon\", \"name\"]\n        },\n        standalone: true\n      });\n    }\n  }\n  return MatStepperIcon;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Content for a `mat-step` that will be rendered lazily.\n */\nlet MatStepContent = /*#__PURE__*/(() => {\n  class MatStepContent {\n    constructor(_template) {\n      this._template = _template;\n    }\n    static {\n      this.ɵfac = function MatStepContent_Factory(t) {\n        return new (t || MatStepContent)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatStepContent,\n        selectors: [[\"ng-template\", \"matStepContent\", \"\"]],\n        standalone: true\n      });\n    }\n  }\n  return MatStepContent;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatStep = /*#__PURE__*/(() => {\n  class MatStep extends CdkStep {\n    constructor(stepper, _errorStateMatcher, _viewContainerRef, stepperOptions) {\n      super(stepper, stepperOptions);\n      this._errorStateMatcher = _errorStateMatcher;\n      this._viewContainerRef = _viewContainerRef;\n      this._isSelected = Subscription.EMPTY;\n      /** Content for step label given by `<ng-template matStepLabel>`. */\n      // We need an initializer here to avoid a TS error.\n      this.stepLabel = undefined;\n    }\n    ngAfterContentInit() {\n      this._isSelected = this._stepper.steps.changes.pipe(switchMap(() => {\n        return this._stepper.selectionChange.pipe(map(event => event.selectedStep === this), startWith(this._stepper.selected === this));\n      })).subscribe(isSelected => {\n        if (isSelected && this._lazyContent && !this._portal) {\n          this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n        }\n      });\n    }\n    ngOnDestroy() {\n      this._isSelected.unsubscribe();\n    }\n    /** Custom error state matcher that additionally checks for validity of interacted form. */\n    isErrorState(control, form) {\n      const originalErrorState = this._errorStateMatcher.isErrorState(control, form);\n      // Custom error state checks for the validity of form that is not submitted or touched\n      // since user can trigger a form change by calling for another step without directly\n      // interacting with the current form.\n      const customErrorState = !!(control && control.invalid && this.interacted);\n      return originalErrorState || customErrorState;\n    }\n    static {\n      this.ɵfac = function MatStep_Factory(t) {\n        return new (t || MatStep)(i0.ɵɵdirectiveInject(forwardRef(() => MatStepper)), i0.ɵɵdirectiveInject(i1.ErrorStateMatcher, 4), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(STEPPER_GLOBAL_OPTIONS, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatStep,\n        selectors: [[\"mat-step\"]],\n        contentQueries: function MatStep_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, MatStepLabel, 5);\n            i0.ɵɵcontentQuery(dirIndex, MatStepContent, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.stepLabel = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lazyContent = _t.first);\n          }\n        },\n        hostAttrs: [\"hidden\", \"\"],\n        inputs: {\n          color: \"color\"\n        },\n        exportAs: [\"matStep\"],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: ErrorStateMatcher,\n          useExisting: MatStep\n        }, {\n          provide: CdkStep,\n          useExisting: MatStep\n        }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c0,\n        decls: 1,\n        vars: 0,\n        consts: [[3, \"cdkPortalOutlet\"]],\n        template: function MatStep_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵtemplate(0, MatStep_ng_template_0_Template, 2, 1, \"ng-template\");\n          }\n        },\n        dependencies: [CdkPortalOutlet],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return MatStep;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatStepper = /*#__PURE__*/(() => {\n  class MatStepper extends CdkStepper {\n    /** Duration for the animation. Will be normalized to milliseconds if no units are set. */\n    get animationDuration() {\n      return this._animationDuration;\n    }\n    set animationDuration(value) {\n      this._animationDuration = /^\\d+$/.test(value) ? value + 'ms' : value;\n    }\n    constructor(dir, changeDetectorRef, elementRef) {\n      super(dir, changeDetectorRef, elementRef);\n      /** The list of step headers of the steps in the stepper. */\n      // We need an initializer here to avoid a TS error.\n      this._stepHeader = undefined;\n      /** Full list of steps inside the stepper, including inside nested steppers. */\n      // We need an initializer here to avoid a TS error.\n      this._steps = undefined;\n      /** Steps that belong to the current stepper, excluding ones from nested steppers. */\n      this.steps = new QueryList();\n      /** Event emitted when the current step is done transitioning in. */\n      this.animationDone = new EventEmitter();\n      /**\n       * Whether the label should display in bottom or end position.\n       * Only applies in the `horizontal` orientation.\n       */\n      this.labelPosition = 'end';\n      /**\n       * Position of the stepper's header.\n       * Only applies in the `horizontal` orientation.\n       */\n      this.headerPosition = 'top';\n      /** Consumer-specified template-refs to be used to override the header icons. */\n      this._iconOverrides = {};\n      /** Stream of animation `done` events when the body expands/collapses. */\n      this._animationDone = new Subject();\n      this._animationDuration = '';\n      /** Whether the stepper is rendering on the server. */\n      this._isServer = !inject(Platform).isBrowser;\n      const nodeName = elementRef.nativeElement.nodeName.toLowerCase();\n      this.orientation = nodeName === 'mat-vertical-stepper' ? 'vertical' : 'horizontal';\n    }\n    ngAfterContentInit() {\n      super.ngAfterContentInit();\n      this._icons.forEach(({\n        name,\n        templateRef\n      }) => this._iconOverrides[name] = templateRef);\n      // Mark the component for change detection whenever the content children query changes\n      this.steps.changes.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        this._stateChanged();\n      });\n      this._animationDone.pipe(\n      // This needs a `distinctUntilChanged` in order to avoid emitting the same event twice due\n      // to a bug in animations where the `.done` callback gets invoked twice on some browsers.\n      // See https://github.com/angular/angular/issues/24084\n      distinctUntilChanged((x, y) => x.fromState === y.fromState && x.toState === y.toState), takeUntil(this._destroyed)).subscribe(event => {\n        if (event.toState === 'current') {\n          this.animationDone.emit();\n        }\n      });\n    }\n    _stepIsNavigable(index, step) {\n      return step.completed || this.selectedIndex === index || !this.linear;\n    }\n    _getAnimationDuration() {\n      if (this.animationDuration) {\n        return this.animationDuration;\n      }\n      return this.orientation === 'horizontal' ? DEFAULT_HORIZONTAL_ANIMATION_DURATION : DEFAULT_VERTICAL_ANIMATION_DURATION;\n    }\n    static {\n      this.ɵfac = function MatStepper_Factory(t) {\n        return new (t || MatStepper)(i0.ɵɵdirectiveInject(i2$1.Directionality, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: MatStepper,\n        selectors: [[\"mat-stepper\"], [\"mat-vertical-stepper\"], [\"mat-horizontal-stepper\"], [\"\", \"matStepper\", \"\"]],\n        contentQueries: function MatStepper_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, MatStep, 5);\n            i0.ɵɵcontentQuery(dirIndex, MatStepperIcon, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._steps = _t);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._icons = _t);\n          }\n        },\n        viewQuery: function MatStepper_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(MatStepHeader, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._stepHeader = _t);\n          }\n        },\n        hostAttrs: [\"role\", \"tablist\"],\n        hostVars: 11,\n        hostBindings: function MatStepper_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"aria-orientation\", ctx.orientation);\n            i0.ɵɵclassProp(\"mat-stepper-horizontal\", ctx.orientation === \"horizontal\")(\"mat-stepper-vertical\", ctx.orientation === \"vertical\")(\"mat-stepper-label-position-end\", ctx.orientation === \"horizontal\" && ctx.labelPosition == \"end\")(\"mat-stepper-label-position-bottom\", ctx.orientation === \"horizontal\" && ctx.labelPosition == \"bottom\")(\"mat-stepper-header-position-bottom\", ctx.headerPosition === \"bottom\");\n          }\n        },\n        inputs: {\n          disableRipple: \"disableRipple\",\n          color: \"color\",\n          labelPosition: \"labelPosition\",\n          headerPosition: \"headerPosition\",\n          animationDuration: \"animationDuration\"\n        },\n        outputs: {\n          animationDone: \"animationDone\"\n        },\n        exportAs: [\"matStepper\", \"matVerticalStepper\", \"matHorizontalStepper\"],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: CdkStepper,\n          useExisting: MatStepper\n        }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c0,\n        decls: 5,\n        vars: 2,\n        consts: [[\"stepTemplate\", \"\"], [1, \"mat-horizontal-stepper-wrapper\"], [1, \"mat-horizontal-stepper-header-container\"], [1, \"mat-horizontal-content-container\"], [\"role\", \"tabpanel\", 1, \"mat-horizontal-stepper-content\", 3, \"id\", \"mat-horizontal-stepper-content-inactive\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"mat-stepper-horizontal-line\"], [\"role\", \"tabpanel\", 1, \"mat-horizontal-stepper-content\", 3, \"id\"], [3, \"ngTemplateOutlet\"], [1, \"mat-step\"], [1, \"mat-vertical-content-container\"], [\"role\", \"tabpanel\", 1, \"mat-vertical-stepper-content\", 3, \"id\"], [1, \"mat-vertical-content\"], [3, \"click\", \"keydown\", \"tabIndex\", \"id\", \"index\", \"state\", \"label\", \"selected\", \"active\", \"optional\", \"errorMessage\", \"iconOverrides\", \"disableRipple\", \"color\"]],\n        template: function MatStepper_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵtemplate(0, MatStepper_Conditional_0_Template, 1, 0)(1, MatStepper_Case_1_Template, 7, 0)(2, MatStepper_Case_2_Template, 2, 0)(3, MatStepper_ng_template_3_Template, 1, 23, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            let tmp_2_0;\n            i0.ɵɵconditional(0, ctx._isServer ? 0 : -1);\n            i0.ɵɵadvance();\n            i0.ɵɵconditional(1, (tmp_2_0 = ctx.orientation) === \"horizontal\" ? 1 : tmp_2_0 === \"vertical\" ? 2 : -1);\n          }\n        },\n        dependencies: [NgTemplateOutlet, MatStepHeader],\n        styles: [\".mat-stepper-vertical,.mat-stepper-horizontal{display:block;font-family:var(--mat-stepper-container-text-font);background:var(--mat-stepper-container-color)}.mat-horizontal-stepper-header-container{white-space:nowrap;display:flex;align-items:center}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header-container{align-items:flex-start}.mat-stepper-header-position-bottom .mat-horizontal-stepper-header-container{order:1}.mat-stepper-horizontal-line{border-top-width:1px;border-top-style:solid;flex:auto;height:0;margin:0 -16px;min-width:32px;border-top-color:var(--mat-stepper-line-color)}.mat-stepper-label-position-bottom .mat-stepper-horizontal-line{margin:0;min-width:0;position:relative;top:calc(calc((var(--mat-stepper-header-height) - 24px) / 2) + 12px)}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::before,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::before,.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::after,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::after{border-top-width:1px;border-top-style:solid;content:\\\"\\\";display:inline-block;height:0;position:absolute;width:calc(50% - 20px)}.mat-horizontal-stepper-header{display:flex;height:72px;overflow:hidden;align-items:center;padding:0 24px;height:var(--mat-stepper-header-height)}.mat-horizontal-stepper-header .mat-step-icon{margin-right:8px;flex:none}[dir=rtl] .mat-horizontal-stepper-header .mat-step-icon{margin-right:0;margin-left:8px}.mat-horizontal-stepper-header::before,.mat-horizontal-stepper-header::after{border-top-color:var(--mat-stepper-line-color)}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header{padding:calc((var(--mat-stepper-header-height) - 24px) / 2) 24px}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header::before,.mat-stepper-label-position-bottom .mat-horizontal-stepper-header::after{top:calc(calc((var(--mat-stepper-header-height) - 24px) / 2) + 12px)}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header{box-sizing:border-box;flex-direction:column;height:auto}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::after,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::after{right:0}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:first-child)::before,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:not(:last-child)::before{left:0}[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:last-child::before,[dir=rtl] .mat-stepper-label-position-bottom .mat-horizontal-stepper-header:first-child::after{display:none}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header .mat-step-icon{margin-right:0;margin-left:0}.mat-stepper-label-position-bottom .mat-horizontal-stepper-header .mat-step-label{padding:16px 0 0 0;text-align:center;width:100%}.mat-vertical-stepper-header{display:flex;align-items:center;height:24px;padding:calc((var(--mat-stepper-header-height) - 24px) / 2) 24px}.mat-vertical-stepper-header .mat-step-icon{margin-right:12px}[dir=rtl] .mat-vertical-stepper-header .mat-step-icon{margin-right:0;margin-left:12px}.mat-horizontal-stepper-wrapper{display:flex;flex-direction:column}.mat-horizontal-stepper-content{outline:0}.mat-horizontal-stepper-content.mat-horizontal-stepper-content-inactive{height:0;overflow:hidden}.mat-horizontal-stepper-content:not(.mat-horizontal-stepper-content-inactive){visibility:inherit !important}.mat-horizontal-content-container{overflow:hidden;padding:0 24px 24px 24px}.cdk-high-contrast-active .mat-horizontal-content-container{outline:solid 1px}.mat-stepper-header-position-bottom .mat-horizontal-content-container{padding:24px 24px 0 24px}.mat-vertical-content-container{margin-left:36px;border:0;position:relative}.cdk-high-contrast-active .mat-vertical-content-container{outline:solid 1px}[dir=rtl] .mat-vertical-content-container{margin-left:0;margin-right:36px}.mat-stepper-vertical-line::before{content:\\\"\\\";position:absolute;left:0;border-left-width:1px;border-left-style:solid;border-left-color:var(--mat-stepper-line-color);top:calc(8px - calc((var(--mat-stepper-header-height) - 24px) / 2));bottom:calc(8px - calc((var(--mat-stepper-header-height) - 24px) / 2))}[dir=rtl] .mat-stepper-vertical-line::before{left:auto;right:0}.mat-vertical-stepper-content{overflow:hidden;outline:0}.mat-vertical-stepper-content:not(.mat-vertical-stepper-content-inactive){visibility:inherit !important}.mat-vertical-content{padding:0 24px 24px 24px}.mat-step:last-child .mat-vertical-content-container{border:none}\"],\n        encapsulation: 2,\n        data: {\n          animation: [matStepperAnimations.horizontalStepTransition, matStepperAnimations.verticalStepTransition]\n        },\n        changeDetection: 0\n      });\n    }\n  }\n  return MatStepper;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Button that moves to the next step in a stepper workflow. */\nlet MatStepperNext = /*#__PURE__*/(() => {\n  class MatStepperNext extends CdkStepperNext {\n    static {\n      this.ɵfac = /* @__PURE__ */(() => {\n        let ɵMatStepperNext_BaseFactory;\n        return function MatStepperNext_Factory(t) {\n          return (ɵMatStepperNext_BaseFactory || (ɵMatStepperNext_BaseFactory = i0.ɵɵgetInheritedFactory(MatStepperNext)))(t || MatStepperNext);\n        };\n      })();\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatStepperNext,\n        selectors: [[\"button\", \"matStepperNext\", \"\"]],\n        hostAttrs: [1, \"mat-stepper-next\"],\n        hostVars: 1,\n        hostBindings: function MatStepperNext_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵhostProperty(\"type\", ctx.type);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return MatStepperNext;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Button that moves to the previous step in a stepper workflow. */\nlet MatStepperPrevious = /*#__PURE__*/(() => {\n  class MatStepperPrevious extends CdkStepperPrevious {\n    static {\n      this.ɵfac = /* @__PURE__ */(() => {\n        let ɵMatStepperPrevious_BaseFactory;\n        return function MatStepperPrevious_Factory(t) {\n          return (ɵMatStepperPrevious_BaseFactory || (ɵMatStepperPrevious_BaseFactory = i0.ɵɵgetInheritedFactory(MatStepperPrevious)))(t || MatStepperPrevious);\n        };\n      })();\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: MatStepperPrevious,\n        selectors: [[\"button\", \"matStepperPrevious\", \"\"]],\n        hostAttrs: [1, \"mat-stepper-previous\"],\n        hostVars: 1,\n        hostBindings: function MatStepperPrevious_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵhostProperty(\"type\", ctx.type);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature]\n      });\n    }\n  }\n  return MatStepperPrevious;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatStepperModule = /*#__PURE__*/(() => {\n  class MatStepperModule {\n    static {\n      this.ɵfac = function MatStepperModule_Factory(t) {\n        return new (t || MatStepperModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: MatStepperModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        providers: [MAT_STEPPER_INTL_PROVIDER, ErrorStateMatcher],\n        imports: [MatCommonModule, CommonModule, PortalModule, CdkStepperModule, MatIconModule, MatRippleModule, MatStepper, MatStepHeader, MatCommonModule]\n      });\n    }\n  }\n  return MatStepperModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_STEPPER_INTL_PROVIDER, MAT_STEPPER_INTL_PROVIDER_FACTORY, MatStep, MatStepContent, MatStepHeader, MatStepLabel, MatStepper, MatStepperIcon, MatStepperIntl, MatStepperModule, MatStepperNext, MatStepperPrevious, matStepperAnimations };\n//# sourceMappingURL=stepper.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}