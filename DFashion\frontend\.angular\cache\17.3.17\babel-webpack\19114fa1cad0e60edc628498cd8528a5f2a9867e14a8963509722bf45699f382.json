{"ast": null, "code": "import { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { Subject } from 'rxjs';\nimport { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport { FormControl } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/order.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/table\";\nimport * as i10 from \"@angular/material/paginator\";\nimport * as i11 from \"@angular/material/sort\";\nimport * as i12 from \"@angular/material/input\";\nimport * as i13 from \"@angular/material/form-field\";\nimport * as i14 from \"@angular/material/select\";\nimport * as i15 from \"@angular/material/core\";\nimport * as i16 from \"@angular/material/tooltip\";\nconst _c0 = () => [5, 10, 25, 50];\nfunction OrderManagementComponent_mat_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r1.label, \" \");\n  }\n}\nfunction OrderManagementComponent_mat_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r2.label, \" \");\n  }\n}\nfunction OrderManagementComponent_th_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Order #\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OrderManagementComponent_td_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 25);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 26);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const order_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(order_r3.orderNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.formatDate(order_r3.orderDate));\n  }\n}\nfunction OrderManagementComponent_th_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 27);\n    i0.ɵɵtext(1, \"Customer\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OrderManagementComponent_td_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 28)(2, \"div\", 29);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 30);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const order_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(order_r5.customer == null ? null : order_r5.customer.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(order_r5.customer == null ? null : order_r5.customer.email);\n  }\n}\nfunction OrderManagementComponent_th_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 27);\n    i0.ɵɵtext(1, \"Items\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OrderManagementComponent_td_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 31)(2, \"span\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const order_r6 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", (order_r6.items == null ? null : order_r6.items.length) || 0, \" item(s)\");\n  }\n}\nfunction OrderManagementComponent_th_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 23);\n    i0.ɵɵtext(1, \"Amount\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OrderManagementComponent_td_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 33)(2, \"span\", 34);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 35);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const order_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", order_r7.totalAmount, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 2, order_r7.paymentMethod));\n  }\n}\nfunction OrderManagementComponent_th_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 27);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OrderManagementComponent_td_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 36)(2, \"span\", 37);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 38);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const order_r8 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r3.getStatusColor(order_r8.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 6, order_r8.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r3.getPaymentStatusColor(order_r8.paymentStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 8, order_r8.paymentStatus), \" \");\n  }\n}\nfunction OrderManagementComponent_th_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 27);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction OrderManagementComponent_td_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 24)(1, \"div\", 39)(2, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function OrderManagementComponent_td_44_Template_button_click_2_listener() {\n      const order_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.viewOrder(order_r10));\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function OrderManagementComponent_td_44_Template_button_click_5_listener() {\n      const order_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.updateOrderStatus(order_r10));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function OrderManagementComponent_td_44_Template_button_click_8_listener() {\n      const order_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.printInvoice(order_r10));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"print\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction OrderManagementComponent_tr_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 43);\n  }\n}\nfunction OrderManagementComponent_tr_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 44);\n  }\n}\nfunction OrderManagementComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"shopping_cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No orders found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try adjusting your search criteria.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let OrderManagementComponent = /*#__PURE__*/(() => {\n  class OrderManagementComponent {\n    constructor(orderService, dialog, snackBar) {\n      this.orderService = orderService;\n      this.dialog = dialog;\n      this.snackBar = snackBar;\n      this.destroy$ = new Subject();\n      this.displayedColumns = ['orderNumber', 'customer', 'items', 'amount', 'status', 'actions'];\n      this.dataSource = new MatTableDataSource([]);\n      this.isLoading = false;\n      this.totalOrders = 0;\n      // Filters\n      this.searchControl = new FormControl('');\n      this.statusFilter = new FormControl('');\n      this.paymentStatusFilter = new FormControl('');\n      this.statuses = [{\n        value: '',\n        label: 'All Statuses'\n      }, {\n        value: 'pending',\n        label: 'Pending'\n      }, {\n        value: 'confirmed',\n        label: 'Confirmed'\n      }, {\n        value: 'shipped',\n        label: 'Shipped'\n      }, {\n        value: 'delivered',\n        label: 'Delivered'\n      }, {\n        value: 'cancelled',\n        label: 'Cancelled'\n      }];\n      this.paymentStatuses = [{\n        value: '',\n        label: 'All Payment Statuses'\n      }, {\n        value: 'pending',\n        label: 'Pending'\n      }, {\n        value: 'paid',\n        label: 'Paid'\n      }, {\n        value: 'failed',\n        label: 'Failed'\n      }, {\n        value: 'refunded',\n        label: 'Refunded'\n      }];\n    }\n    ngOnInit() {\n      this.setupFilters();\n      this.loadOrders();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    setupFilters() {\n      this.searchControl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(() => {\n        this.loadOrders();\n      });\n      [this.statusFilter, this.paymentStatusFilter].forEach(control => {\n        control.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(() => {\n          this.loadOrders();\n        });\n      });\n    }\n    loadOrders() {\n      this.isLoading = true;\n      // Load orders from API\n      this.dataSource.data = [];\n      this.totalOrders = 0;\n      this.isLoading = false;\n    }\n    onPageChange() {\n      this.loadOrders();\n    }\n    viewOrder(order) {\n      this.snackBar.open('Order details view - Coming soon', 'Close', {\n        duration: 3000\n      });\n    }\n    updateOrderStatus(order) {\n      this.snackBar.open('Order status update - Coming soon', 'Close', {\n        duration: 3000\n      });\n    }\n    printInvoice(order) {\n      this.snackBar.open('Invoice printing - Coming soon', 'Close', {\n        duration: 3000\n      });\n    }\n    formatDate(dateString) {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleDateString('en-IN', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    }\n    getStatusColor(status) {\n      const statusColors = {\n        'pending': '#ff9800',\n        'confirmed': '#2196f3',\n        'shipped': '#9c27b0',\n        'delivered': '#4caf50',\n        'cancelled': '#f44336'\n      };\n      return statusColors[status] || '#666666';\n    }\n    getPaymentStatusColor(status) {\n      const statusColors = {\n        'pending': '#ff9800',\n        'paid': '#4caf50',\n        'failed': '#f44336',\n        'refunded': '#9e9e9e'\n      };\n      return statusColors[status] || '#666666';\n    }\n    static {\n      this.ɵfac = function OrderManagementComponent_Factory(t) {\n        return new (t || OrderManagementComponent)(i0.ɵɵdirectiveInject(i1.OrderService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: OrderManagementComponent,\n        selectors: [[\"app-order-management\"]],\n        viewQuery: function OrderManagementComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(MatPaginator, 5);\n            i0.ɵɵviewQuery(MatSort, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n          }\n        },\n        decls: 49,\n        vars: 13,\n        consts: [[1, \"order-management\"], [1, \"filters-section\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"placeholder\", \"Search by order number or customer\", 3, \"formControl\"], [\"matSuffix\", \"\"], [3, \"formControl\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", 3, \"dataSource\"], [\"matColumnDef\", \"orderNumber\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"customer\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"items\"], [\"matColumnDef\", \"amount\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"showFirstLastButtons\", \"\", 3, \"page\", \"length\", \"pageSize\", \"pageSizeOptions\"], [3, \"value\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [\"mat-cell\", \"\"], [1, \"order-number\"], [1, \"order-date\"], [\"mat-header-cell\", \"\"], [1, \"customer-info\"], [1, \"customer-name\"], [1, \"customer-email\"], [1, \"items-info\"], [1, \"items-count\"], [1, \"amount-cell\"], [1, \"total-amount\"], [1, \"payment-method\"], [1, \"status-cell\"], [1, \"status-chip\"], [1, \"payment-status\"], [1, \"actions-cell\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"View order details\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Update status\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Print invoice\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"no-data\"]],\n        template: function OrderManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\")(2, \"mat-card-header\")(3, \"mat-card-title\");\n            i0.ɵɵtext(4, \"Order Management\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n            i0.ɵɵtext(6, \"Track and manage customer orders\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"div\", 1)(9, \"mat-form-field\", 2)(10, \"mat-label\");\n            i0.ɵɵtext(11, \"Search orders\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(12, \"input\", 3);\n            i0.ɵɵelementStart(13, \"mat-icon\", 4);\n            i0.ɵɵtext(14, \"search\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"mat-form-field\", 2)(16, \"mat-label\");\n            i0.ɵɵtext(17, \"Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"mat-select\", 5);\n            i0.ɵɵtemplate(19, OrderManagementComponent_mat_option_19_Template, 2, 2, \"mat-option\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"mat-form-field\", 2)(21, \"mat-label\");\n            i0.ɵɵtext(22, \"Payment Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"mat-select\", 5);\n            i0.ɵɵtemplate(24, OrderManagementComponent_mat_option_24_Template, 2, 2, \"mat-option\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(25, \"div\", 7)(26, \"table\", 8);\n            i0.ɵɵelementContainerStart(27, 9);\n            i0.ɵɵtemplate(28, OrderManagementComponent_th_28_Template, 2, 0, \"th\", 10)(29, OrderManagementComponent_td_29_Template, 5, 2, \"td\", 11);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(30, 12);\n            i0.ɵɵtemplate(31, OrderManagementComponent_th_31_Template, 2, 0, \"th\", 13)(32, OrderManagementComponent_td_32_Template, 6, 2, \"td\", 11);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(33, 14);\n            i0.ɵɵtemplate(34, OrderManagementComponent_th_34_Template, 2, 0, \"th\", 13)(35, OrderManagementComponent_td_35_Template, 4, 1, \"td\", 11);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(36, 15);\n            i0.ɵɵtemplate(37, OrderManagementComponent_th_37_Template, 2, 0, \"th\", 10)(38, OrderManagementComponent_td_38_Template, 7, 4, \"td\", 11);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(39, 16);\n            i0.ɵɵtemplate(40, OrderManagementComponent_th_40_Template, 2, 0, \"th\", 13)(41, OrderManagementComponent_td_41_Template, 8, 10, \"td\", 11);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵelementContainerStart(42, 17);\n            i0.ɵɵtemplate(43, OrderManagementComponent_th_43_Template, 2, 0, \"th\", 13)(44, OrderManagementComponent_td_44_Template, 11, 0, \"td\", 11);\n            i0.ɵɵelementContainerEnd();\n            i0.ɵɵtemplate(45, OrderManagementComponent_tr_45_Template, 1, 0, \"tr\", 18)(46, OrderManagementComponent_tr_46_Template, 1, 0, \"tr\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(47, OrderManagementComponent_div_47_Template, 7, 0, \"div\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"mat-paginator\", 21);\n            i0.ɵɵlistener(\"page\", function OrderManagementComponent_Template_mat_paginator_page_48_listener() {\n              return ctx.onPageChange();\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"formControl\", ctx.searchControl);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"formControl\", ctx.statusFilter);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.statuses);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"formControl\", ctx.paymentStatusFilter);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.paymentStatuses);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n            i0.ɵɵadvance(19);\n            i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.dataSource.data.length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"length\", ctx.totalOrders)(\"pageSize\", 10)(\"pageSizeOptions\", i0.ɵɵpureFunction0(12, _c0));\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.FormControlDirective, i6.MatIcon, i7.MatIconButton, i8.MatCard, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle, i9.MatTable, i9.MatHeaderCellDef, i9.MatHeaderRowDef, i9.MatColumnDef, i9.MatCellDef, i9.MatRowDef, i9.MatHeaderCell, i9.MatCell, i9.MatHeaderRow, i9.MatRow, i10.MatPaginator, i11.MatSort, i11.MatSortHeader, i12.MatInput, i13.MatFormField, i13.MatLabel, i13.MatSuffix, i14.MatSelect, i15.MatOption, i16.MatTooltip, i4.TitleCasePipe],\n        styles: [\".order-management[_ngcontent-%COMP%]{padding:1rem}.filters-section[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-bottom:1rem;flex-wrap:wrap;align-items:center}.filters-section[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{min-width:200px}.table-container[_ngcontent-%COMP%]{overflow-x:auto;margin-bottom:1rem}.order-number[_ngcontent-%COMP%]{font-weight:600;color:#1976d2;margin-bottom:.25rem}.order-date[_ngcontent-%COMP%]{font-size:.875rem;color:#666}.customer-info[_ngcontent-%COMP%]   .customer-name[_ngcontent-%COMP%]{font-weight:500;margin-bottom:.25rem}.customer-info[_ngcontent-%COMP%]   .customer-email[_ngcontent-%COMP%], .items-info[_ngcontent-%COMP%]   .items-count[_ngcontent-%COMP%]{font-size:.875rem;color:#666}.amount-cell[_ngcontent-%COMP%]   .total-amount[_ngcontent-%COMP%]{font-weight:600;color:#2e7d32;display:block;margin-bottom:.25rem}.amount-cell[_ngcontent-%COMP%]   .payment-method[_ngcontent-%COMP%]{font-size:.75rem;color:#666;text-transform:uppercase}.status-cell[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]{display:inline-block;padding:.25rem .5rem;border-radius:12px;font-size:.75rem;font-weight:500;color:#fff;margin-bottom:.25rem}.status-cell[_ngcontent-%COMP%]   .payment-status[_ngcontent-%COMP%]{display:block;font-size:.75rem;font-weight:500}.actions-cell[_ngcontent-%COMP%]{display:flex;gap:.25rem}.no-data[_ngcontent-%COMP%]{text-align:center;padding:3rem;color:#666}.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:4rem;width:4rem;height:4rem;margin-bottom:1rem;opacity:.5}.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:1rem 0 .5rem;font-weight:400}.no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.875rem}@media (max-width: 768px){.filters-section[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.filters-section[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{min-width:auto;width:100%}.customer-info[_ngcontent-%COMP%], .amount-cell[_ngcontent-%COMP%], .status-cell[_ngcontent-%COMP%]{font-size:.875rem}}\"]\n      });\n    }\n  }\n  return OrderManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}