{"ast": null, "code": "export const environment = {\n  production: false,\n  apiUrl: 'http://localhost:5000/api',\n  socketUrl: 'http://localhost:5000',\n  frontendUrl: 'http://localhost:4200',\n  razorpayKeyId: 'rzp_test_MiheZxloav4xYh' // Your Razorpay test key\n};", "map": {"version": 3, "names": ["environment", "production", "apiUrl", "socketUrl", "frontendUrl", "razorpayKeyId"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\environments\\environment.ts"], "sourcesContent": ["export const environment = {\n  production: false,\n  apiUrl: 'http://localhost:5000/api',\n  socketUrl: 'http://localhost:5000',\n  frontendUrl: 'http://localhost:4200',\n  razorpayKeyId: 'rzp_test_MiheZxloav4xYh' // Your Razorpay test key\n};\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,2BAA2B;EACnCC,SAAS,EAAE,uBAAuB;EAClCC,WAAW,EAAE,uBAAuB;EACpCC,aAAa,EAAE,yBAAyB,CAAC;CAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}