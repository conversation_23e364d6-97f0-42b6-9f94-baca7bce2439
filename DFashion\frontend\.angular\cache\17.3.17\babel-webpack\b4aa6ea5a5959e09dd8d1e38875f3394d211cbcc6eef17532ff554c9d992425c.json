{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { timer } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../core/services/story.service\";\nimport * as i3 from \"../../../core/services/cart.service\";\nimport * as i4 from \"../../../core/services/wishlist.service\";\nimport * as i5 from \"../../../core/services/media.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction StoryViewerComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r3 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getProgressWidth(i_r3), \"%\");\n    i0.ɵɵclassProp(\"active\", i_r3 === ctx_r1.currentStoryIndex)(\"completed\", i_r3 < ctx_r1.currentStoryIndex);\n  }\n}\nfunction StoryViewerComponent_div_0_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.pauseStory());\n    });\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StoryViewerComponent_div_0_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.resumeStory());\n    });\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StoryViewerComponent_div_0_img_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 37);\n    i0.ɵɵlistener(\"load\", function StoryViewerComponent_div_0_img_17_Template_img_load_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onMediaLoaded());\n    })(\"error\", function StoryViewerComponent_div_0_img_17_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleImageError($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.url) || \"\", i0.ɵɵsanitizeUrl)(\"alt\", (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.alt) || (ctx_r1.currentStory == null ? null : ctx_r1.currentStory.caption) || \"\");\n  }\n}\nfunction StoryViewerComponent_div_0_video_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"video\", 38, 0);\n    i0.ɵɵlistener(\"loadeddata\", function StoryViewerComponent_div_0_video_18_Template_video_loadeddata_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onMediaLoaded());\n    })(\"ended\", function StoryViewerComponent_div_0_video_18_Template_video_ended_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    })(\"error\", function StoryViewerComponent_div_0_video_18_Template_video_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleVideoError($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.url) || \"\", i0.ɵɵsanitizeUrl)(\"poster\", (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.thumbnailUrl) || \"\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction StoryViewerComponent_div_0_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_div_19_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleStoryVideo());\n    });\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"playing\", ctx_r1.isStoryVideoPlaying);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.isStoryVideoPlaying ? \"fas fa-pause\" : \"fas fa-play\");\n  }\n}\nfunction StoryViewerComponent_div_0_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.currentStory.caption);\n  }\n}\nfunction StoryViewerComponent_div_0_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_div_21_div_1_Template_div_click_0_listener() {\n      const productTag_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.showProductDetails(productTag_r10));\n    });\n    i0.ɵɵelement(1, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const productTag_r10 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"top\", productTag_r10.position.y, \"%\")(\"left\", productTag_r10.position.x, \"%\");\n  }\n}\nfunction StoryViewerComponent_div_0_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtemplate(1, StoryViewerComponent_div_0_div_21_div_1_Template, 2, 4, \"div\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentStory.products);\n  }\n}\nfunction StoryViewerComponent_div_0_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.sendMessage());\n    });\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StoryViewerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onStoryClick($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 5);\n    i0.ɵɵtemplate(2, StoryViewerComponent_div_0_div_2_Template, 2, 6, \"div\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 7)(4, \"div\", 8);\n    i0.ɵɵelement(5, \"img\", 9);\n    i0.ɵɵelementStart(6, \"div\", 10)(7, \"span\", 11);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 12);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 13);\n    i0.ɵɵtemplate(12, StoryViewerComponent_div_0_button_12_Template, 2, 0, \"button\", 14)(13, StoryViewerComponent_div_0_button_13_Template, 2, 0, \"button\", 14);\n    i0.ɵɵelementStart(14, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStory());\n    });\n    i0.ɵɵelement(15, \"i\", 16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 17);\n    i0.ɵɵtemplate(17, StoryViewerComponent_div_0_img_17_Template, 1, 2, \"img\", 18)(18, StoryViewerComponent_div_0_video_18_Template, 2, 2, \"video\", 19)(19, StoryViewerComponent_div_0_div_19_Template, 3, 4, \"div\", 20)(20, StoryViewerComponent_div_0_div_20_Template, 3, 1, \"div\", 21)(21, StoryViewerComponent_div_0_div_21_Template, 2, 1, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_div_click_22_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_div_click_23_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 25)(25, \"div\", 26)(26, \"input\", 27);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StoryViewerComponent_div_0_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.messageText, $event) || (ctx_r1.messageText = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function StoryViewerComponent_div_0_Template_input_keyup_enter_26_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.sendMessage());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, StoryViewerComponent_div_0_button_27_Template, 2, 0, \"button\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 29)(29, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.likeStory());\n    });\n    i0.ɵɵelement(30, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_0_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareStory());\n    });\n    i0.ɵɵelement(32, \"i\", 32);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.userStories);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.user.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentStory.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.currentStory.createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isPaused);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isPaused);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.type) === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.type) === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.currentMediaItem == null ? null : ctx_r1.currentMediaItem.type) === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.products.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.messageText);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.messageText.trim());\n  }\n}\nfunction StoryViewerComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 49);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_1_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 50);\n    i0.ɵɵelement(3, \"img\", 51);\n    i0.ɵɵelementStart(4, \"div\", 52)(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 53);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 54);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 55)(13, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addToWishlist(ctx_r1.selectedProduct.product._id));\n    });\n    i0.ɵɵelement(14, \"i\", 31);\n    i0.ɵɵtext(15, \" Wishlist \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_1_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addToCart(ctx_r1.selectedProduct.product._id));\n    });\n    i0.ɵɵelement(17, \"i\", 58);\n    i0.ɵɵtext(18, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function StoryViewerComponent_div_1_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.buyNow(ctx_r1.selectedProduct.product._id));\n    });\n    i0.ɵɵtext(20, \" Buy Now \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.selectedProduct.product.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.selectedProduct.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(9, 5, ctx_r1.selectedProduct.product.price), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.product.brand);\n  }\n}\nfunction StoryViewerComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵelement(1, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class StoryViewerComponent {\n  constructor(route, router, storyService, cartService, wishlistService, mediaService) {\n    this.route = route;\n    this.router = router;\n    this.storyService = storyService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.mediaService = mediaService;\n    this.userStories = [];\n    this.currentStoryIndex = 0;\n    this.currentStory = null;\n    this.isLoading = true;\n    this.isPaused = false;\n    this.progress = 0;\n    this.storyDuration = 5000; // 5 seconds for images\n    this.messageText = '';\n    this.selectedProduct = null;\n    // Media handling\n    this.currentMediaItem = null;\n    this.isStoryVideoPlaying = false;\n  }\n  ngOnInit() {\n    this.route.params.subscribe(params => {\n      const userId = params['userId'];\n      const storyIndex = parseInt(params['storyIndex']) || 0;\n      if (userId) {\n        this.loadUserStories(userId, storyIndex);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.stopProgress();\n    if (this.storyTimeout) {\n      clearTimeout(this.storyTimeout);\n    }\n  }\n  handleKeyboardEvent(event) {\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n      case ' ':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStory();\n        break;\n    }\n  }\n  loadUserStories(userId, startIndex = 0) {\n    this.isLoading = true;\n    this.storyService.getUserStories(userId).subscribe({\n      next: response => {\n        this.userStories = response.stories;\n        // Enhance stories with video content if needed\n        this.userStories = this.enhanceStoriesWithMedia(this.userStories);\n        this.currentStoryIndex = Math.min(startIndex, this.userStories.length - 1);\n        this.currentStory = this.userStories[this.currentStoryIndex];\n        this.updateCurrentMedia();\n        this.isLoading = false;\n        this.startStoryProgress();\n      },\n      error: error => {\n        console.error('Failed to load stories:', error);\n        this.isLoading = false;\n        this.closeStory();\n      }\n    });\n  }\n  enhanceStoriesWithMedia(stories) {\n    return stories.map((story, index) => {\n      // If story doesn't have media or has broken media, add sample video\n      if (!story.media || !story.media.url) {\n        const sampleVideo = this.mediaService.getRandomSampleVideo();\n        story.media = {\n          type: 'video',\n          url: sampleVideo.url,\n          thumbnail: sampleVideo.thumbnail,\n          duration: sampleVideo.duration\n        };\n      }\n      return story;\n    });\n  }\n  updateCurrentMedia() {\n    if (this.currentStory?.media) {\n      this.currentMediaItem = {\n        id: this.currentStory._id,\n        type: this.currentStory.media.type,\n        url: this.mediaService.getSafeImageUrl(this.currentStory.media.url, 'story'),\n        thumbnailUrl: this.currentStory.media.thumbnail,\n        alt: this.currentStory.caption,\n        duration: this.currentStory.media.duration\n      };\n    }\n  }\n  startStoryProgress() {\n    this.stopProgress();\n    this.progress = 0;\n    if (this.currentStory?.media.type === 'video') {\n      // For videos, let the video control the progress\n      return;\n    }\n    const intervalMs = 50; // Update every 50ms\n    const increment = intervalMs / this.storyDuration * 100;\n    this.progressSubscription = timer(0, intervalMs).subscribe(() => {\n      if (!this.isPaused) {\n        this.progress += increment;\n        if (this.progress >= 100) {\n          this.nextStory();\n        }\n      }\n    });\n  }\n  stopProgress() {\n    if (this.progressSubscription) {\n      this.progressSubscription.unsubscribe();\n      this.progressSubscription = undefined;\n    }\n  }\n  getProgressWidth(index) {\n    if (index < this.currentStoryIndex) {\n      return 100;\n    } else if (index === this.currentStoryIndex) {\n      return this.progress;\n    } else {\n      return 0;\n    }\n  }\n  nextStory() {\n    if (this.currentStoryIndex < this.userStories.length - 1) {\n      this.currentStoryIndex++;\n      this.currentStory = this.userStories[this.currentStoryIndex];\n      this.updateCurrentMedia();\n      this.startStoryProgress();\n    } else {\n      // Move to next user's stories or close\n      this.closeStory();\n    }\n  }\n  previousStory() {\n    if (this.currentStoryIndex > 0) {\n      this.currentStoryIndex--;\n      this.currentStory = this.userStories[this.currentStoryIndex];\n      this.updateCurrentMedia();\n      this.startStoryProgress();\n    }\n  }\n  pauseStory() {\n    this.isPaused = true;\n    const videoElement = document.querySelector('video');\n    if (videoElement) {\n      videoElement.pause();\n    }\n  }\n  resumeStory() {\n    this.isPaused = false;\n    const videoElement = document.querySelector('video');\n    if (videoElement) {\n      videoElement.play();\n    }\n  }\n  onStoryClick(event) {\n    const target = event.target;\n    // Don't handle clicks on interactive elements\n    if (target.closest('.story-header') || target.closest('.story-footer') || target.closest('.product-tag') || target.closest('.nav-area')) {\n      return;\n    }\n    // Pause/resume on tap\n    if (this.isPaused) {\n      this.resumeStory();\n    } else {\n      this.pauseStory();\n    }\n  }\n  onMediaLoaded() {\n    // Media is loaded, story can start\n    if (this.currentMediaItem?.type === 'video') {\n      this.isStoryVideoPlaying = true;\n    }\n  }\n  handleImageError(event) {\n    this.mediaService.handleImageError(event, 'story');\n  }\n  handleVideoError(event) {\n    console.error('Story video error:', event);\n    // Could fallback to thumbnail or different content\n  }\n  toggleStoryVideo() {\n    const videoElement = document.querySelector('video');\n    if (videoElement) {\n      if (videoElement.paused) {\n        videoElement.play();\n        this.isStoryVideoPlaying = true;\n        this.resumeStory();\n      } else {\n        videoElement.pause();\n        this.isStoryVideoPlaying = false;\n        this.pauseStory();\n      }\n    }\n  }\n  showProductDetails(productTag) {\n    this.selectedProduct = productTag;\n    this.pauseStory();\n  }\n  closeProductModal() {\n    this.selectedProduct = null;\n    this.resumeStory();\n  }\n  addToWishlist(productId) {\n    if (this.selectedProduct) {\n      this.wishlistService.addToWishlist(productId).subscribe({\n        next: () => {\n          this.showNotification('Added to wishlist ❤️');\n          this.closeProductModal();\n        },\n        error: error => {\n          console.error('Wishlist error:', error);\n          // Fallback to offline mode\n          this.wishlistService.addToWishlistOffline(this.selectedProduct.product);\n          this.showNotification('Added to wishlist ❤️');\n          this.closeProductModal();\n        }\n      });\n    }\n  }\n  addToCart(productId) {\n    if (this.selectedProduct) {\n      this.cartService.addToCart(productId, 1, this.selectedProduct.size, this.selectedProduct.color).subscribe({\n        next: () => {\n          this.showNotification('Added to cart 🛒');\n          this.closeProductModal();\n        },\n        error: error => {\n          console.error('Cart error:', error);\n          this.showNotification('Added to cart 🛒');\n          this.closeProductModal();\n        }\n      });\n    }\n  }\n  buyNow(productId) {\n    if (this.selectedProduct) {\n      this.cartService.addToCart(productId, 1, this.selectedProduct.size, this.selectedProduct.color).subscribe({\n        next: () => {\n          this.showNotification('Redirecting to checkout...');\n          this.closeProductModal();\n          this.router.navigate(['/shop/checkout']);\n        },\n        error: error => {\n          console.error('Buy now error:', error);\n          this.showNotification('Redirecting to product page...');\n          this.closeProductModal();\n          this.router.navigate(['/product', productId]);\n        }\n      });\n    }\n  }\n  showNotification(message) {\n    // Create a simple notification\n    const notification = document.createElement('div');\n    notification.textContent = message;\n    notification.style.cssText = `\n      position: fixed;\n      top: 80px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: white;\n      padding: 12px 20px;\n      border-radius: 20px;\n      z-index: 30000;\n      font-size: 14px;\n      backdrop-filter: blur(10px);\n      animation: slideDown 0.3s ease;\n    `;\n    document.body.appendChild(notification);\n    setTimeout(() => {\n      notification.remove();\n    }, 2000);\n  }\n  sendMessage() {\n    if (this.messageText.trim()) {\n      console.log('Send message:', this.messageText);\n      this.messageText = '';\n    }\n  }\n  likeStory() {\n    console.log('Like story');\n  }\n  shareStory() {\n    console.log('Share story');\n  }\n  closeStory() {\n    this.router.navigate(['/']);\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diff = now.getTime() - new Date(date).getTime();\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    if (hours < 1) return 'now';\n    if (hours < 24) return `${hours}h`;\n    const days = Math.floor(hours / 24);\n    return `${days}d`;\n  }\n  static {\n    this.ɵfac = function StoryViewerComponent_Factory(t) {\n      return new (t || StoryViewerComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.StoryService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.MediaService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoryViewerComponent,\n      selectors: [[\"app-story-viewer\"]],\n      hostBindings: function StoryViewerComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function StoryViewerComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeyboardEvent($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 3,\n      consts: [[\"videoElement\", \"\"], [\"class\", \"story-viewer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"product-modal\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"story-viewer\", 3, \"click\"], [1, \"progress-container\"], [\"class\", \"progress-bar\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"username\"], [1, \"time-ago\"], [1, \"story-actions\"], [\"class\", \"action-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"action-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story-content\"], [\"class\", \"story-media\", 3, \"src\", \"alt\", \"load\", \"error\", 4, \"ngIf\"], [\"class\", \"story-media\", \"autoplay\", \"\", \"muted\", \"\", \"playsinline\", \"\", 3, \"src\", \"poster\", \"loadeddata\", \"ended\", \"error\", 4, \"ngIf\"], [\"class\", \"video-story-controls\", 4, \"ngIf\"], [\"class\", \"story-caption\", 4, \"ngIf\"], [\"class\", \"product-tags\", 4, \"ngIf\"], [1, \"nav-area\", \"nav-left\", 3, \"click\"], [1, \"nav-area\", \"nav-right\", 3, \"click\"], [1, \"story-footer\"], [1, \"story-input\"], [\"type\", \"text\", \"placeholder\", \"Send message\", 1, \"message-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"class\", \"send-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"story-reactions\"], [1, \"reaction-btn\", 3, \"click\"], [1, \"far\", \"fa-heart\"], [1, \"far\", \"fa-share\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"fas\", \"fa-pause\"], [1, \"fas\", \"fa-play\"], [1, \"story-media\", 3, \"load\", \"error\", \"src\", \"alt\"], [\"autoplay\", \"\", \"muted\", \"\", \"playsinline\", \"\", 1, \"story-media\", 3, \"loadeddata\", \"ended\", \"error\", \"src\", \"poster\"], [1, \"video-story-controls\"], [1, \"video-play-btn\", 3, \"click\"], [1, \"story-caption\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"top\", \"left\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"tag-dot\"], [1, \"send-btn\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"product-modal\", 3, \"click\"], [1, \"product-modal-content\", 3, \"click\"], [1, \"product-header\"], [1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"product-price\"], [1, \"product-brand\"], [1, \"product-actions\"], [1, \"btn-wishlist\", 3, \"click\"], [1, \"btn-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"btn-buy-now\", 3, \"click\"], [1, \"loading-container\"], [1, \"spinner\"]],\n      template: function StoryViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, StoryViewerComponent_div_0_Template, 33, 14, \"div\", 1)(1, StoryViewerComponent_div_1_Template, 21, 7, \"div\", 2)(2, StoryViewerComponent_div_2_Template, 2, 0, \"div\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStory);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedProduct);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, i6.DecimalPipe, FormsModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel],\n      styles: [\".story-viewer[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 10000;\\n  display: flex;\\n  flex-direction: column;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n  padding: 8px 16px;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 10;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 1px;\\n  overflow: hidden;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: white;\\n  transition: width 0.1s linear;\\n  border-radius: 1px;\\n}\\n\\n.progress-fill.completed[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n}\\n\\n.story-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 16px;\\n  position: absolute;\\n  top: 20px;\\n  left: 0;\\n  right: 0;\\n  z-index: 10;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 2px solid white;\\n  object-fit: cover;\\n}\\n\\n.user-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  color: white;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n\\n.time-ago[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n}\\n\\n.story-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: rgba(0, 0, 0, 0.5);\\n  border: none;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: background 0.2s;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.7);\\n}\\n\\n.story-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.story-media[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  max-height: 100%;\\n  object-fit: contain;\\n}\\n\\n\\n\\n.video-story-controls[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100px;\\n  right: 20px;\\n  z-index: 1000;\\n}\\n\\n.video-play-btn[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.9);\\n  border: none;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  font-size: 16px;\\n  color: #333;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.video-play-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n\\n.video-play-btn.playing[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n}\\n\\n.story-caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100px;\\n  left: 16px;\\n  right: 16px;\\n  background: rgba(0, 0, 0, 0.5);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.story-caption[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n\\n.product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  pointer-events: none;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: all;\\n  cursor: pointer;\\n}\\n\\n.tag-dot[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  background: white;\\n  border-radius: 50%;\\n  border: 2px solid #007bff;\\n  position: relative;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.tag-dot[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 8px;\\n  height: 8px;\\n  background: #007bff;\\n  border-radius: 50%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);\\n  }\\n}\\n.nav-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 30%;\\n  z-index: 5;\\n  cursor: pointer;\\n}\\n\\n.nav-left[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n\\n.nav-right[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n\\n.story-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 16px;\\n  background: rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.story-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.message-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 20px;\\n  padding: 12px 16px;\\n  color: white;\\n  font-size: 14px;\\n}\\n\\n.message-input[_ngcontent-%COMP%]::placeholder {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.message-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: rgba(255, 255, 255, 0.4);\\n}\\n\\n.send-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: #007bff;\\n  border: none;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.story-reactions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.reaction-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: none;\\n  color: white;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 18px;\\n}\\n\\n.product-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.8);\\n  z-index: 20000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px;\\n}\\n\\n.product-modal-content[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  max-width: 400px;\\n  width: 100%;\\n  animation: _ngcontent-%COMP%_modalSlideUp 0.3s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_modalSlideUp {\\n  from {\\n    transform: translateY(50px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n.product-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 20px;\\n}\\n\\n.product-image[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n}\\n\\n.product-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n}\\n\\n.product-price[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: #e91e63;\\n  margin: 0 0 4px 0;\\n}\\n\\n.product-brand[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n.product-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.btn-wishlist[_ngcontent-%COMP%], .btn-cart[_ngcontent-%COMP%], .btn-buy-now[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px;\\n  border: none;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  font-size: 12px;\\n}\\n\\n.btn-wishlist[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #666;\\n  border: 1px solid #ddd;\\n}\\n\\n.btn-cart[_ngcontent-%COMP%] {\\n  background: #2196f3;\\n  color: white;\\n}\\n\\n.btn-buy-now[_ngcontent-%COMP%] {\\n  background: #ff9800;\\n  color: white;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 10000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  border-top: 3px solid white;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .story-viewer[_ngcontent-%COMP%] {\\n    padding: 0;\\n  }\\n  .story-container[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n    height: 100vh;\\n    border-radius: 0;\\n  }\\n  .progress-bars[_ngcontent-%COMP%] {\\n    padding: 1rem 1rem 0.5rem;\\n  }\\n  .progress-bar[_ngcontent-%COMP%] {\\n    height: 2px;\\n  }\\n  .story-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem 1rem;\\n  }\\n  .user-avatar[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .user-info[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .header-actions[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n  .header-btn[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n    font-size: 1rem;\\n  }\\n  .story-content[_ngcontent-%COMP%] {\\n    height: calc(100vh - 140px);\\n  }\\n  .story-footer[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .message-input[_ngcontent-%COMP%] {\\n    padding: 0.75rem 1rem;\\n    font-size: 0.9rem;\\n  }\\n  .footer-actions[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n  .footer-btn[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n    font-size: 1.2rem;\\n  }\\n  .nav-area[_ngcontent-%COMP%] {\\n    width: 40%;\\n  }\\n  .product-tag[_ngcontent-%COMP%] {\\n    transform: scale(0.9);\\n  }\\n  .product-info[_ngcontent-%COMP%] {\\n    width: 280px;\\n    padding: 1rem;\\n  }\\n  .product-image[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n  }\\n  .product-details[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .product-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .product-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .product-btn[_ngcontent-%COMP%] {\\n    padding: 0.6rem 1rem;\\n    font-size: 0.8rem;\\n  }\\n  .video-story-controls[_ngcontent-%COMP%] {\\n    bottom: 120px;\\n    right: 15px;\\n  }\\n  .video-play-btn[_ngcontent-%COMP%] {\\n    width: 45px;\\n    height: 45px;\\n    font-size: 14px;\\n  }\\n  \\n\\n  .product-modal[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    max-width: 90vw;\\n    max-height: 80vh;\\n  }\\n  .modal-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .modal-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .modal-body[_ngcontent-%COMP%] {\\n    padding: 1rem 0;\\n  }\\n  .modal-footer[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n  }\\n  .modal-footer[_ngcontent-%COMP%]   .product-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n\\n@media (hover: none) and (pointer: coarse) {\\n  .header-btn[_ngcontent-%COMP%], .footer-btn[_ngcontent-%COMP%], .product-btn[_ngcontent-%COMP%], .video-play-btn[_ngcontent-%COMP%] {\\n    transform: none;\\n    transition: background-color 0.2s ease;\\n  }\\n  .header-btn[_ngcontent-%COMP%]:active, .footer-btn[_ngcontent-%COMP%]:active, .product-btn[_ngcontent-%COMP%]:active, .video-play-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n  }\\n  .product-tag[_ngcontent-%COMP%]:active {\\n    transform: scale(0.85);\\n  }\\n  \\n\\n  .video-story-controls[_ngcontent-%COMP%] {\\n    opacity: 0.8;\\n  }\\n  .video-play-btn[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.95);\\n  }\\n}\\n\\n\\n@media (max-width: 768px) and (orientation: landscape) {\\n  .story-container[_ngcontent-%COMP%] {\\n    max-width: 100vh;\\n    margin: 0 auto;\\n  }\\n  .story-content[_ngcontent-%COMP%] {\\n    height: calc(100vh - 100px);\\n  }\\n  .story-header[_ngcontent-%COMP%], .story-footer[_ngcontent-%COMP%] {\\n    padding: 0.5rem 1rem;\\n  }\\n  .progress-bars[_ngcontent-%COMP%] {\\n    padding: 0.5rem 1rem 0.25rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvc3Rvcnkvc3Rvcnktdmlld2VyL3N0b3J5LXZpZXdlci5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0k7RUFDRSxlQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLHlCQUFBO1VBQUEsaUJBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSxRQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFdBQUE7QUFBTjs7QUFHSTtFQUNFLE9BQUE7RUFDQSxXQUFBO0VBQ0Esb0NBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FBQU47O0FBR0k7RUFDRSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSw2QkFBQTtFQUNBLGtCQUFBO0FBQU47O0FBR0k7RUFDRSxzQkFBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSxhQUFBO0VBQ0Esa0JBQUE7RUFDQSxTQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxXQUFBO0FBQU47O0FBR0k7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0FBQU47O0FBR0k7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsdUJBQUE7RUFDQSxpQkFBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0FBQU47O0FBR0k7RUFDRSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0FBQU47O0FBR0k7RUFDRSwrQkFBQTtFQUNBLGVBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSxRQUFBO0FBQU47O0FBR0k7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsOEJBQUE7RUFDQSxZQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLDJCQUFBO0FBQU47O0FBR0k7RUFDRSw4QkFBQTtBQUFOOztBQUdJO0VBQ0UsT0FBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQUFOOztBQUdJO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7QUFBTjs7QUFHSSx5QkFBQTtBQUNBO0VBQ0Usa0JBQUE7RUFDQSxhQUFBO0VBQ0EsV0FBQTtFQUNBLGFBQUE7QUFBTjs7QUFHSTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxvQ0FBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLGVBQUE7RUFDQSxXQUFBO0VBQ0EsbUNBQUE7VUFBQSwyQkFBQTtBQUFOOztBQUdJO0VBQ0UsaUJBQUE7RUFDQSxxQkFBQTtBQUFOOztBQUdJO0VBQ0UsOEJBQUE7RUFDQSxZQUFBO0FBQU47O0FBR0k7RUFDRSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxVQUFBO0VBQ0EsV0FBQTtFQUNBLDhCQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQ0FBQTtVQUFBLDJCQUFBO0FBQU47O0FBR0k7RUFDRSxTQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0FBQU47O0FBR0k7RUFDRSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxvQkFBQTtBQUFOOztBQUdJO0VBQ0Usa0JBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7QUFBTjs7QUFHSTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSw0QkFBQTtBQUFOOztBQUdJO0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxnQ0FBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtBQUFOOztBQUdJO0VBQ0U7SUFBSywwQ0FBQTtFQUNUO0VBQUk7SUFBTSwyQ0FBQTtFQUdWO0VBRkk7SUFBTyx3Q0FBQTtFQUtYO0FBQ0Y7QUFISTtFQUNFLGtCQUFBO0VBQ0EsTUFBQTtFQUNBLFNBQUE7RUFDQSxVQUFBO0VBQ0EsVUFBQTtFQUNBLGVBQUE7QUFLTjs7QUFGSTtFQUNFLE9BQUE7QUFLTjs7QUFGSTtFQUNFLFFBQUE7QUFLTjs7QUFGSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQ0FBQTtVQUFBLDJCQUFBO0FBS047O0FBRkk7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtBQUtOOztBQUZJO0VBQ0UsT0FBQTtFQUNBLG9DQUFBO0VBQ0EsMENBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7QUFLTjs7QUFGSTtFQUNFLCtCQUFBO0FBS047O0FBRkk7RUFDRSxhQUFBO0VBQ0Esc0NBQUE7QUFLTjs7QUFGSTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0FBS047O0FBRkk7RUFDRSxhQUFBO0VBQ0EsUUFBQTtBQUtOOztBQUZJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLG9DQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7RUFDQSxlQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxlQUFBO0FBS047O0FBRkk7RUFDRSxlQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsY0FBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsYUFBQTtBQUtOOztBQUZJO0VBQ0UsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7RUFDQSxpQ0FBQTtBQUtOOztBQUZJO0VBQ0U7SUFDRSwyQkFBQTtJQUNBLFVBQUE7RUFLTjtFQUhJO0lBQ0Usd0JBQUE7SUFDQSxVQUFBO0VBS047QUFDRjtBQUZJO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtBQUlOOztBQURJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0FBSU47O0FBREk7RUFDRSxpQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtBQUlOOztBQURJO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGlCQUFBO0FBSU47O0FBREk7RUFDRSxXQUFBO0VBQ0EsU0FBQTtFQUNBLGVBQUE7QUFJTjs7QUFESTtFQUNFLGFBQUE7RUFDQSxRQUFBO0FBSU47O0FBREk7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsUUFBQTtFQUNBLGVBQUE7QUFJTjs7QUFESTtFQUNFLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLHNCQUFBO0FBSU47O0FBREk7RUFDRSxtQkFBQTtFQUNBLFlBQUE7QUFJTjs7QUFESTtFQUNFLG1CQUFBO0VBQ0EsWUFBQTtBQUlOOztBQURJO0VBQ0UsZUFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtBQUlOOztBQURJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSwwQ0FBQTtFQUNBLDJCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQ0FBQTtBQUlOOztBQURJO0VBQ0U7SUFBSyx1QkFBQTtFQUtUO0VBSkk7SUFBTyx5QkFBQTtFQU9YO0FBQ0Y7QUFMSSw2QkFBQTtBQUNBO0VBQ0U7SUFDRSxVQUFBO0VBT047RUFKSTtJQUNFLGdCQUFBO0lBQ0EsYUFBQTtJQUNBLGdCQUFBO0VBTU47RUFISTtJQUNFLHlCQUFBO0VBS047RUFGSTtJQUNFLFdBQUE7RUFJTjtFQURJO0lBQ0UscUJBQUE7RUFHTjtFQUFJO0lBQ0UsV0FBQTtJQUNBLFlBQUE7RUFFTjtFQUNJO0lBQ0UsaUJBQUE7RUFDTjtFQUVJO0lBQ0UsaUJBQUE7RUFBTjtFQUdJO0lBQ0UsU0FBQTtFQUROO0VBSUk7SUFDRSxXQUFBO0lBQ0EsWUFBQTtJQUNBLGVBQUE7RUFGTjtFQUtJO0lBQ0UsMkJBQUE7RUFITjtFQU1JO0lBQ0UsYUFBQTtFQUpOO0VBT0k7SUFDRSxxQkFBQTtJQUNBLGlCQUFBO0VBTE47RUFRSTtJQUNFLFNBQUE7RUFOTjtFQVNJO0lBQ0UsV0FBQTtJQUNBLFlBQUE7SUFDQSxpQkFBQTtFQVBOO0VBVUk7SUFDRSxVQUFBO0VBUk47RUFXSTtJQUNFLHFCQUFBO0VBVE47RUFZSTtJQUNFLFlBQUE7SUFDQSxhQUFBO0VBVk47RUFhSTtJQUNFLFdBQUE7SUFDQSxZQUFBO0VBWE47RUFjSTtJQUNFLGlCQUFBO0VBWk47RUFlSTtJQUNFLGlCQUFBO0VBYk47RUFnQkk7SUFDRSxzQkFBQTtJQUNBLFdBQUE7RUFkTjtFQWlCSTtJQUNFLG9CQUFBO0lBQ0EsaUJBQUE7RUFmTjtFQWtCSTtJQUNFLGFBQUE7SUFDQSxXQUFBO0VBaEJOO0VBbUJJO0lBQ0UsV0FBQTtJQUNBLFlBQUE7SUFDQSxlQUFBO0VBakJOO0VBb0JJLHlCQUFBO0VBQ0E7SUFDRSxhQUFBO0lBQ0EsZUFBQTtJQUNBLGdCQUFBO0VBbEJOO0VBcUJJO0lBQ0UsYUFBQTtFQW5CTjtFQXNCSTtJQUNFLGlCQUFBO0VBcEJOO0VBdUJJO0lBQ0UsZUFBQTtFQXJCTjtFQXdCSTtJQUNFLHNCQUFBO0lBQ0EsWUFBQTtFQXRCTjtFQXlCSTtJQUNFLFdBQUE7SUFDQSx1QkFBQTtFQXZCTjtBQUNGO0FBMEJJLDJDQUFBO0FBQ0E7RUFDRTs7OztJQUlFLGVBQUE7SUFDQSxzQ0FBQTtFQXhCTjtFQTJCSTs7OztJQUlFLHNCQUFBO0VBekJOO0VBNEJJO0lBQ0Usc0JBQUE7RUExQk47RUE2QkksZ0RBQUE7RUFDQTtJQUNFLFlBQUE7RUEzQk47RUE4Qkk7SUFDRSxxQ0FBQTtFQTVCTjtBQUNGO0FBK0JJLGlDQUFBO0FBQ0E7RUFDRTtJQUNFLGdCQUFBO0lBQ0EsY0FBQTtFQTdCTjtFQWdDSTtJQUNFLDJCQUFBO0VBOUJOO0VBaUNJOztJQUVFLG9CQUFBO0VBL0JOO0VBa0NJO0lBQ0UsNEJBQUE7RUFoQ047QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5zdG9yeS12aWV3ZXIge1xuICAgICAgcG9zaXRpb246IGZpeGVkO1xuICAgICAgdG9wOiAwO1xuICAgICAgbGVmdDogMDtcbiAgICAgIHdpZHRoOiAxMDB2dztcbiAgICAgIGhlaWdodDogMTAwdmg7XG4gICAgICBiYWNrZ3JvdW5kOiAjMDAwO1xuICAgICAgei1pbmRleDogMTAwMDA7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgIHVzZXItc2VsZWN0OiBub25lO1xuICAgIH1cblxuICAgIC5wcm9ncmVzcy1jb250YWluZXIge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGdhcDogMnB4O1xuICAgICAgcGFkZGluZzogOHB4IDE2cHg7XG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICB0b3A6IDA7XG4gICAgICBsZWZ0OiAwO1xuICAgICAgcmlnaHQ6IDA7XG4gICAgICB6LWluZGV4OiAxMDtcbiAgICB9XG5cbiAgICAucHJvZ3Jlc3MtYmFyIHtcbiAgICAgIGZsZXg6IDE7XG4gICAgICBoZWlnaHQ6IDJweDtcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDFweDtcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgfVxuXG4gICAgLnByb2dyZXNzLWZpbGwge1xuICAgICAgaGVpZ2h0OiAxMDAlO1xuICAgICAgYmFja2dyb3VuZDogd2hpdGU7XG4gICAgICB0cmFuc2l0aW9uOiB3aWR0aCAwLjFzIGxpbmVhcjtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDFweDtcbiAgICB9XG5cbiAgICAucHJvZ3Jlc3MtZmlsbC5jb21wbGV0ZWQge1xuICAgICAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAuc3RvcnktaGVhZGVyIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgICAgcGFkZGluZzogMTZweDtcbiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgIHRvcDogMjBweDtcbiAgICAgIGxlZnQ6IDA7XG4gICAgICByaWdodDogMDtcbiAgICAgIHotaW5kZXg6IDEwO1xuICAgIH1cblxuICAgIC51c2VyLWluZm8ge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDEycHg7XG4gICAgfVxuXG4gICAgLnVzZXItYXZhdGFyIHtcbiAgICAgIHdpZHRoOiA0MHB4O1xuICAgICAgaGVpZ2h0OiA0MHB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgICAgYm9yZGVyOiAycHggc29saWQgd2hpdGU7XG4gICAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgICB9XG5cbiAgICAudXNlci1kZXRhaWxzIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIH1cblxuICAgIC51c2VybmFtZSB7XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgIH1cblxuICAgIC50aW1lLWFnbyB7XG4gICAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpO1xuICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgIH1cblxuICAgIC5zdG9yeS1hY3Rpb25zIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBnYXA6IDhweDtcbiAgICB9XG5cbiAgICAuYWN0aW9uLWJ0biB7XG4gICAgICB3aWR0aDogNDBweDtcbiAgICAgIGhlaWdodDogNDBweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC41KTtcbiAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kIDAuMnM7XG4gICAgfVxuXG4gICAgLmFjdGlvbi1idG46aG92ZXIge1xuICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjcpO1xuICAgIH1cblxuICAgIC5zdG9yeS1jb250ZW50IHtcbiAgICAgIGZsZXg6IDE7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICB9XG5cbiAgICAuc3RvcnktbWVkaWEge1xuICAgICAgbWF4LXdpZHRoOiAxMDAlO1xuICAgICAgbWF4LWhlaWdodDogMTAwJTtcbiAgICAgIG9iamVjdC1maXQ6IGNvbnRhaW47XG4gICAgfVxuXG4gICAgLyogVmlkZW8gU3RvcnkgQ29udHJvbHMgKi9cbiAgICAudmlkZW8tc3RvcnktY29udHJvbHMge1xuICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgYm90dG9tOiAxMDBweDtcbiAgICAgIHJpZ2h0OiAyMHB4O1xuICAgICAgei1pbmRleDogMTAwMDtcbiAgICB9XG5cbiAgICAudmlkZW8tcGxheS1idG4ge1xuICAgICAgd2lkdGg6IDUwcHg7XG4gICAgICBoZWlnaHQ6IDUwcHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSk7XG4gICAgICBib3JkZXI6IG5vbmU7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgIGNvbG9yOiAjMzMzO1xuICAgICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xuICAgIH1cblxuICAgIC52aWRlby1wbGF5LWJ0bjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcbiAgICB9XG5cbiAgICAudmlkZW8tcGxheS1idG4ucGxheWluZyB7XG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuNyk7XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgfVxuXG4gICAgLnN0b3J5LWNhcHRpb24ge1xuICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgYm90dG9tOiAxMDBweDtcbiAgICAgIGxlZnQ6IDE2cHg7XG4gICAgICByaWdodDogMTZweDtcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC41KTtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICAgIHBhZGRpbmc6IDEycHggMTZweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDIwcHg7XG4gICAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCk7XG4gICAgfVxuXG4gICAgLnN0b3J5LWNhcHRpb24gcCB7XG4gICAgICBtYXJnaW46IDA7XG4gICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICBsaW5lLWhlaWdodDogMS40O1xuICAgIH1cblxuICAgIC5wcm9kdWN0LXRhZ3Mge1xuICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgdG9wOiAwO1xuICAgICAgbGVmdDogMDtcbiAgICAgIHJpZ2h0OiAwO1xuICAgICAgYm90dG9tOiAwO1xuICAgICAgcG9pbnRlci1ldmVudHM6IG5vbmU7XG4gICAgfVxuXG4gICAgLnByb2R1Y3QtdGFnIHtcbiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgIHBvaW50ZXItZXZlbnRzOiBhbGw7XG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgfVxuXG4gICAgLnRhZy1kb3Qge1xuICAgICAgd2lkdGg6IDI0cHg7XG4gICAgICBoZWlnaHQ6IDI0cHg7XG4gICAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICAgIGJvcmRlcjogMnB4IHNvbGlkICMwMDdiZmY7XG4gICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgICBhbmltYXRpb246IHB1bHNlIDJzIGluZmluaXRlO1xuICAgIH1cblxuICAgIC50YWctZG90OjphZnRlciB7XG4gICAgICBjb250ZW50OiAnJztcbiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgIHRvcDogNTAlO1xuICAgICAgbGVmdDogNTAlO1xuICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSk7XG4gICAgICB3aWR0aDogOHB4O1xuICAgICAgaGVpZ2h0OiA4cHg7XG4gICAgICBiYWNrZ3JvdW5kOiAjMDA3YmZmO1xuICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgIH1cblxuICAgIEBrZXlmcmFtZXMgcHVsc2Uge1xuICAgICAgMCUgeyBib3gtc2hhZG93OiAwIDAgMCAwIHJnYmEoMCwgMTIzLCAyNTUsIDAuNyk7IH1cbiAgICAgIDcwJSB7IGJveC1zaGFkb3c6IDAgMCAwIDEwcHggcmdiYSgwLCAxMjMsIDI1NSwgMCk7IH1cbiAgICAgIDEwMCUgeyBib3gtc2hhZG93OiAwIDAgMCAwIHJnYmEoMCwgMTIzLCAyNTUsIDApOyB9XG4gICAgfVxuXG4gICAgLm5hdi1hcmVhIHtcbiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgIHRvcDogMDtcbiAgICAgIGJvdHRvbTogMDtcbiAgICAgIHdpZHRoOiAzMCU7XG4gICAgICB6LWluZGV4OiA1O1xuICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgIH1cblxuICAgIC5uYXYtbGVmdCB7XG4gICAgICBsZWZ0OiAwO1xuICAgIH1cblxuICAgIC5uYXYtcmlnaHQge1xuICAgICAgcmlnaHQ6IDA7XG4gICAgfVxuXG4gICAgLnN0b3J5LWZvb3RlciB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogMTZweDtcbiAgICAgIHBhZGRpbmc6IDE2cHg7XG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMyk7XG4gICAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCk7XG4gICAgfVxuXG4gICAgLnN0b3J5LWlucHV0IHtcbiAgICAgIGZsZXg6IDE7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogOHB4O1xuICAgIH1cblxuICAgIC5tZXNzYWdlLWlucHV0IHtcbiAgICAgIGZsZXg6IDE7XG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XG4gICAgICBib3JkZXItcmFkaXVzOiAyMHB4O1xuICAgICAgcGFkZGluZzogMTJweCAxNnB4O1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgIH1cblxuICAgIC5tZXNzYWdlLWlucHV0OjpwbGFjZWhvbGRlciB7XG4gICAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpO1xuICAgIH1cblxuICAgIC5tZXNzYWdlLWlucHV0OmZvY3VzIHtcbiAgICAgIG91dGxpbmU6IG5vbmU7XG4gICAgICBib3JkZXItY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC40KTtcbiAgICB9XG5cbiAgICAuc2VuZC1idG4ge1xuICAgICAgd2lkdGg6IDQwcHg7XG4gICAgICBoZWlnaHQ6IDQwcHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICBiYWNrZ3JvdW5kOiAjMDA3YmZmO1xuICAgICAgYm9yZGVyOiBub25lO1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICB9XG5cbiAgICAuc3RvcnktcmVhY3Rpb25zIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBnYXA6IDhweDtcbiAgICB9XG5cbiAgICAucmVhY3Rpb24tYnRuIHtcbiAgICAgIHdpZHRoOiA0MHB4O1xuICAgICAgaGVpZ2h0OiA0MHB4O1xuICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuICAgICAgYm9yZGVyOiBub25lO1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgIGZvbnQtc2l6ZTogMThweDtcbiAgICB9XG5cbiAgICAucHJvZHVjdC1tb2RhbCB7XG4gICAgICBwb3NpdGlvbjogZml4ZWQ7XG4gICAgICB0b3A6IDA7XG4gICAgICBsZWZ0OiAwO1xuICAgICAgd2lkdGg6IDEwMHZ3O1xuICAgICAgaGVpZ2h0OiAxMDB2aDtcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC44KTtcbiAgICAgIHotaW5kZXg6IDIwMDAwO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgIHBhZGRpbmc6IDIwcHg7XG4gICAgfVxuXG4gICAgLnByb2R1Y3QtbW9kYWwtY29udGVudCB7XG4gICAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDE2cHg7XG4gICAgICBwYWRkaW5nOiAyNHB4O1xuICAgICAgbWF4LXdpZHRoOiA0MDBweDtcbiAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgYW5pbWF0aW9uOiBtb2RhbFNsaWRlVXAgMC4zcyBlYXNlO1xuICAgIH1cblxuICAgIEBrZXlmcmFtZXMgbW9kYWxTbGlkZVVwIHtcbiAgICAgIGZyb20ge1xuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoNTBweCk7XG4gICAgICAgIG9wYWNpdHk6IDA7XG4gICAgICB9XG4gICAgICB0byB7XG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcbiAgICAgICAgb3BhY2l0eTogMTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAucHJvZHVjdC1oZWFkZXIge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGdhcDogMTZweDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7XG4gICAgfVxuXG4gICAgLnByb2R1Y3QtaW1hZ2Uge1xuICAgICAgd2lkdGg6IDgwcHg7XG4gICAgICBoZWlnaHQ6IDgwcHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgICB9XG5cbiAgICAucHJvZHVjdC1pbmZvIGgzIHtcbiAgICAgIG1hcmdpbjogMCAwIDhweCAwO1xuICAgICAgZm9udC1zaXplOiAxOHB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICB9XG5cbiAgICAucHJvZHVjdC1wcmljZSB7XG4gICAgICBmb250LXNpemU6IDIwcHg7XG4gICAgICBmb250LXdlaWdodDogNzAwO1xuICAgICAgY29sb3I6ICNlOTFlNjM7XG4gICAgICBtYXJnaW46IDAgMCA0cHggMDtcbiAgICB9XG5cbiAgICAucHJvZHVjdC1icmFuZCB7XG4gICAgICBjb2xvcjogIzY2NjtcbiAgICAgIG1hcmdpbjogMDtcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICB9XG5cbiAgICAucHJvZHVjdC1hY3Rpb25zIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBnYXA6IDhweDtcbiAgICB9XG5cbiAgICAuYnRuLXdpc2hsaXN0LCAuYnRuLWNhcnQsIC5idG4tYnV5LW5vdyB7XG4gICAgICBmbGV4OiAxO1xuICAgICAgcGFkZGluZzogMTJweDtcbiAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgZ2FwOiA2cHg7XG4gICAgICBmb250LXNpemU6IDEycHg7XG4gICAgfVxuXG4gICAgLmJ0bi13aXNobGlzdCB7XG4gICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xuICAgICAgY29sb3I6ICM2NjY7XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZGRkO1xuICAgIH1cblxuICAgIC5idG4tY2FydCB7XG4gICAgICBiYWNrZ3JvdW5kOiAjMjE5NmYzO1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgIH1cblxuICAgIC5idG4tYnV5LW5vdyB7XG4gICAgICBiYWNrZ3JvdW5kOiAjZmY5ODAwO1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgIH1cblxuICAgIC5sb2FkaW5nLWNvbnRhaW5lciB7XG4gICAgICBwb3NpdGlvbjogZml4ZWQ7XG4gICAgICB0b3A6IDA7XG4gICAgICBsZWZ0OiAwO1xuICAgICAgd2lkdGg6IDEwMHZ3O1xuICAgICAgaGVpZ2h0OiAxMDB2aDtcbiAgICAgIGJhY2tncm91bmQ6ICMwMDA7XG4gICAgICB6LWluZGV4OiAxMDAwMDtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgfVxuXG4gICAgLnNwaW5uZXIge1xuICAgICAgd2lkdGg6IDQwcHg7XG4gICAgICBoZWlnaHQ6IDQwcHg7XG4gICAgICBib3JkZXI6IDNweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyk7XG4gICAgICBib3JkZXItdG9wOiAzcHggc29saWQgd2hpdGU7XG4gICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xuICAgIH1cblxuICAgIEBrZXlmcmFtZXMgc3BpbiB7XG4gICAgICAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOyB9XG4gICAgICAxMDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfVxuICAgIH1cblxuICAgIC8qIE1vYmlsZSBSZXNwb25zaXZlIFN0eWxlcyAqL1xuICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgICAgLnN0b3J5LXZpZXdlciB7XG4gICAgICAgIHBhZGRpbmc6IDA7XG4gICAgICB9XG5cbiAgICAgIC5zdG9yeS1jb250YWluZXIge1xuICAgICAgICBtYXgtd2lkdGg6IDEwMHZ3O1xuICAgICAgICBoZWlnaHQ6IDEwMHZoO1xuICAgICAgICBib3JkZXItcmFkaXVzOiAwO1xuICAgICAgfVxuXG4gICAgICAucHJvZ3Jlc3MtYmFycyB7XG4gICAgICAgIHBhZGRpbmc6IDFyZW0gMXJlbSAwLjVyZW07XG4gICAgICB9XG5cbiAgICAgIC5wcm9ncmVzcy1iYXIge1xuICAgICAgICBoZWlnaHQ6IDJweDtcbiAgICAgIH1cblxuICAgICAgLnN0b3J5LWhlYWRlciB7XG4gICAgICAgIHBhZGRpbmc6IDAuNzVyZW0gMXJlbTtcbiAgICAgIH1cblxuICAgICAgLnVzZXItYXZhdGFyIHtcbiAgICAgICAgd2lkdGg6IDM1cHg7XG4gICAgICAgIGhlaWdodDogMzVweDtcbiAgICAgIH1cblxuICAgICAgLnVzZXItaW5mbyBoMyB7XG4gICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xuICAgICAgfVxuXG4gICAgICAudXNlci1pbmZvIHNwYW4ge1xuICAgICAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgICAgIH1cblxuICAgICAgLmhlYWRlci1hY3Rpb25zIHtcbiAgICAgICAgZ2FwOiAxcmVtO1xuICAgICAgfVxuXG4gICAgICAuaGVhZGVyLWJ0biB7XG4gICAgICAgIHdpZHRoOiAzNXB4O1xuICAgICAgICBoZWlnaHQ6IDM1cHg7XG4gICAgICAgIGZvbnQtc2l6ZTogMXJlbTtcbiAgICAgIH1cblxuICAgICAgLnN0b3J5LWNvbnRlbnQge1xuICAgICAgICBoZWlnaHQ6IGNhbGMoMTAwdmggLSAxNDBweCk7XG4gICAgICB9XG5cbiAgICAgIC5zdG9yeS1mb290ZXIge1xuICAgICAgICBwYWRkaW5nOiAxcmVtO1xuICAgICAgfVxuXG4gICAgICAubWVzc2FnZS1pbnB1dCB7XG4gICAgICAgIHBhZGRpbmc6IDAuNzVyZW0gMXJlbTtcbiAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XG4gICAgICB9XG5cbiAgICAgIC5mb290ZXItYWN0aW9ucyB7XG4gICAgICAgIGdhcDogMXJlbTtcbiAgICAgIH1cblxuICAgICAgLmZvb3Rlci1idG4ge1xuICAgICAgICB3aWR0aDogMzVweDtcbiAgICAgICAgaGVpZ2h0OiAzNXB4O1xuICAgICAgICBmb250LXNpemU6IDEuMnJlbTtcbiAgICAgIH1cblxuICAgICAgLm5hdi1hcmVhIHtcbiAgICAgICAgd2lkdGg6IDQwJTtcbiAgICAgIH1cblxuICAgICAgLnByb2R1Y3QtdGFnIHtcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgwLjkpO1xuICAgICAgfVxuXG4gICAgICAucHJvZHVjdC1pbmZvIHtcbiAgICAgICAgd2lkdGg6IDI4MHB4O1xuICAgICAgICBwYWRkaW5nOiAxcmVtO1xuICAgICAgfVxuXG4gICAgICAucHJvZHVjdC1pbWFnZSB7XG4gICAgICAgIHdpZHRoOiA2MHB4O1xuICAgICAgICBoZWlnaHQ6IDYwcHg7XG4gICAgICB9XG5cbiAgICAgIC5wcm9kdWN0LWRldGFpbHMgaDUge1xuICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcbiAgICAgIH1cblxuICAgICAgLnByb2R1Y3QtZGV0YWlscyBwIHtcbiAgICAgICAgZm9udC1zaXplOiAwLjhyZW07XG4gICAgICB9XG5cbiAgICAgIC5wcm9kdWN0LWFjdGlvbnMge1xuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICBnYXA6IDAuNXJlbTtcbiAgICAgIH1cblxuICAgICAgLnByb2R1Y3QtYnRuIHtcbiAgICAgICAgcGFkZGluZzogMC42cmVtIDFyZW07XG4gICAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xuICAgICAgfVxuXG4gICAgICAudmlkZW8tc3RvcnktY29udHJvbHMge1xuICAgICAgICBib3R0b206IDEyMHB4O1xuICAgICAgICByaWdodDogMTVweDtcbiAgICAgIH1cblxuICAgICAgLnZpZGVvLXBsYXktYnRuIHtcbiAgICAgICAgd2lkdGg6IDQ1cHg7XG4gICAgICAgIGhlaWdodDogNDVweDtcbiAgICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgfVxuXG4gICAgICAvKiBQcm9kdWN0IE1vZGFsIE1vYmlsZSAqL1xuICAgICAgLnByb2R1Y3QtbW9kYWwge1xuICAgICAgICBwYWRkaW5nOiAxcmVtO1xuICAgICAgICBtYXgtd2lkdGg6IDkwdnc7XG4gICAgICAgIG1heC1oZWlnaHQ6IDgwdmg7XG4gICAgICB9XG5cbiAgICAgIC5tb2RhbC1jb250ZW50IHtcbiAgICAgICAgcGFkZGluZzogMXJlbTtcbiAgICAgIH1cblxuICAgICAgLm1vZGFsLWhlYWRlciBoNCB7XG4gICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xuICAgICAgfVxuXG4gICAgICAubW9kYWwtYm9keSB7XG4gICAgICAgIHBhZGRpbmc6IDFyZW0gMDtcbiAgICAgIH1cblxuICAgICAgLm1vZGFsLWZvb3RlciB7XG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgIGdhcDogMC43NXJlbTtcbiAgICAgIH1cblxuICAgICAgLm1vZGFsLWZvb3RlciAucHJvZHVjdC1idG4ge1xuICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLyogVG91Y2gtZnJpZW5kbHkgaW50ZXJhY3Rpb25zIGZvciBtb2JpbGUgKi9cbiAgICBAbWVkaWEgKGhvdmVyOiBub25lKSBhbmQgKHBvaW50ZXI6IGNvYXJzZSkge1xuICAgICAgLmhlYWRlci1idG4sXG4gICAgICAuZm9vdGVyLWJ0bixcbiAgICAgIC5wcm9kdWN0LWJ0bixcbiAgICAgIC52aWRlby1wbGF5LWJ0biB7XG4gICAgICAgIHRyYW5zZm9ybTogbm9uZTtcbiAgICAgICAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjJzIGVhc2U7XG4gICAgICB9XG5cbiAgICAgIC5oZWFkZXItYnRuOmFjdGl2ZSxcbiAgICAgIC5mb290ZXItYnRuOmFjdGl2ZSxcbiAgICAgIC5wcm9kdWN0LWJ0bjphY3RpdmUsXG4gICAgICAudmlkZW8tcGxheS1idG46YWN0aXZlIHtcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgwLjk1KTtcbiAgICAgIH1cblxuICAgICAgLnByb2R1Y3QtdGFnOmFjdGl2ZSB7XG4gICAgICAgIHRyYW5zZm9ybTogc2NhbGUoMC44NSk7XG4gICAgICB9XG5cbiAgICAgIC8qIEFsd2F5cyBzaG93IHZpZGVvIGNvbnRyb2xzIG9uIHRvdWNoIGRldmljZXMgKi9cbiAgICAgIC52aWRlby1zdG9yeS1jb250cm9scyB7XG4gICAgICAgIG9wYWNpdHk6IDAuODtcbiAgICAgIH1cblxuICAgICAgLnZpZGVvLXBsYXktYnRuIHtcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjk1KTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvKiBMYW5kc2NhcGUgbW9iaWxlIG9yaWVudGF0aW9uICovXG4gICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSBhbmQgKG9yaWVudGF0aW9uOiBsYW5kc2NhcGUpIHtcbiAgICAgIC5zdG9yeS1jb250YWluZXIge1xuICAgICAgICBtYXgtd2lkdGg6IDEwMHZoO1xuICAgICAgICBtYXJnaW46IDAgYXV0bztcbiAgICAgIH1cblxuICAgICAgLnN0b3J5LWNvbnRlbnQge1xuICAgICAgICBoZWlnaHQ6IGNhbGMoMTAwdmggLSAxMDBweCk7XG4gICAgICB9XG5cbiAgICAgIC5zdG9yeS1oZWFkZXIsXG4gICAgICAuc3RvcnktZm9vdGVyIHtcbiAgICAgICAgcGFkZGluZzogMC41cmVtIDFyZW07XG4gICAgICB9XG5cbiAgICAgIC5wcm9ncmVzcy1iYXJzIHtcbiAgICAgICAgcGFkZGluZzogMC41cmVtIDFyZW0gMC4yNXJlbTtcbiAgICAgIH1cbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "timer", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "ctx_r1", "getProgressWidth", "i_r3", "ɵɵclassProp", "currentStoryIndex", "ɵɵlistener", "StoryViewerComponent_div_0_button_12_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵresetView", "pauseStory", "StoryViewerComponent_div_0_button_13_Template_button_click_0_listener", "_r5", "resumeStory", "StoryViewerComponent_div_0_img_17_Template_img_load_0_listener", "_r6", "onMediaLoaded", "StoryViewerComponent_div_0_img_17_Template_img_error_0_listener", "$event", "handleImageError", "ɵɵproperty", "currentMediaItem", "url", "ɵɵsanitizeUrl", "alt", "currentStory", "caption", "StoryViewerComponent_div_0_video_18_Template_video_loadeddata_0_listener", "_r7", "StoryViewerComponent_div_0_video_18_Template_video_ended_0_listener", "nextStory", "StoryViewerComponent_div_0_video_18_Template_video_error_0_listener", "handleVideoError", "thumbnailUrl", "StoryViewerComponent_div_0_div_19_Template_button_click_1_listener", "_r8", "toggleStoryVideo", "isStoryVideoPlaying", "ɵɵclassMap", "ɵɵtext", "ɵɵtextInterpolate", "StoryViewerComponent_div_0_div_21_div_1_Template_div_click_0_listener", "productTag_r10", "_r9", "$implicit", "showProductDetails", "position", "y", "x", "ɵɵtemplate", "StoryViewerComponent_div_0_div_21_div_1_Template", "products", "StoryViewerComponent_div_0_button_27_Template_button_click_0_listener", "_r11", "sendMessage", "StoryViewerComponent_div_0_Template_div_click_0_listener", "_r1", "onStoryClick", "StoryViewerComponent_div_0_div_2_Template", "StoryViewerComponent_div_0_button_12_Template", "StoryViewerComponent_div_0_button_13_Template", "StoryViewerComponent_div_0_Template_button_click_14_listener", "closeStory", "StoryViewerComponent_div_0_img_17_Template", "StoryViewerComponent_div_0_video_18_Template", "StoryViewerComponent_div_0_div_19_Template", "StoryViewerComponent_div_0_div_20_Template", "StoryViewerComponent_div_0_div_21_Template", "StoryViewerComponent_div_0_Template_div_click_22_listener", "previousStory", "StoryViewerComponent_div_0_Template_div_click_23_listener", "ɵɵtwoWayListener", "StoryViewerComponent_div_0_Template_input_ngModelChange_26_listener", "ɵɵtwoWayBindingSet", "messageText", "StoryViewerComponent_div_0_Template_input_keyup_enter_26_listener", "StoryViewerComponent_div_0_button_27_Template", "StoryViewerComponent_div_0_Template_button_click_29_listener", "likeStory", "StoryViewerComponent_div_0_Template_button_click_31_listener", "shareStory", "userStories", "user", "avatar", "fullName", "username", "getTimeAgo", "createdAt", "isPaused", "type", "length", "ɵɵtwoWayProperty", "trim", "StoryViewerComponent_div_1_Template_div_click_0_listener", "_r12", "closeProductModal", "StoryViewerComponent_div_1_Template_div_click_1_listener", "stopPropagation", "StoryViewerComponent_div_1_Template_button_click_13_listener", "addToWishlist", "selectedProduct", "product", "_id", "StoryViewerComponent_div_1_Template_button_click_16_listener", "addToCart", "StoryViewerComponent_div_1_Template_button_click_19_listener", "buyNow", "images", "name", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "price", "brand", "StoryViewerComponent", "constructor", "route", "router", "storyService", "cartService", "wishlistService", "mediaService", "isLoading", "progress", "storyDuration", "ngOnInit", "params", "subscribe", "userId", "storyIndex", "parseInt", "loadUserStories", "ngOnDestroy", "stopProgress", "storyTimeout", "clearTimeout", "handleKeyboardEvent", "event", "key", "startIndex", "getUserStories", "next", "response", "stories", "enhanceStoriesWithMedia", "Math", "min", "updateCurrentMedia", "startStoryProgress", "error", "console", "map", "story", "index", "media", "sampleVideo", "getRandomSampleVideo", "thumbnail", "duration", "id", "getSafeImageUrl", "intervalMs", "increment", "progressSubscription", "unsubscribe", "undefined", "videoElement", "document", "querySelector", "pause", "play", "target", "closest", "paused", "productTag", "productId", "showNotification", "addToWishlistOffline", "size", "color", "navigate", "message", "notification", "createElement", "textContent", "style", "cssText", "body", "append<PERSON><PERSON><PERSON>", "setTimeout", "remove", "log", "date", "now", "Date", "diff", "getTime", "hours", "floor", "days", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "StoryService", "i3", "CartService", "i4", "WishlistService", "i5", "MediaService", "selectors", "hostBindings", "StoryViewerComponent_HostBindings", "rf", "ctx", "StoryViewerComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "StoryViewerComponent_div_0_Template", "StoryViewerComponent_div_1_Template", "StoryViewerComponent_div_2_Template", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i7", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\story\\story-viewer\\story-viewer.component.ts"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>roy, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Subscription, interval, timer } from 'rxjs';\n\nimport { Story } from '../../../core/models/story.model';\nimport { StoryService } from '../../../core/services/story.service';\nimport { CartService } from '../../../core/services/cart.service';\nimport { WishlistService } from '../../../core/services/wishlist.service';\nimport { MediaService, MediaItem } from '../../../core/services/media.service';\n\n@Component({\n  selector: 'app-story-viewer',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"story-viewer\" *ngIf=\"currentStory\" (click)=\"onStoryClick($event)\">\n      <!-- Progress Bars -->\n      <div class=\"progress-container\">\n        <div \n          *ngFor=\"let story of userStories; let i = index\" \n          class=\"progress-bar\"\n        >\n          <div \n            class=\"progress-fill\"\n            [style.width.%]=\"getProgressWidth(i)\"\n            [class.active]=\"i === currentStoryIndex\"\n            [class.completed]=\"i < currentStoryIndex\"\n          ></div>\n        </div>\n      </div>\n\n      <!-- Story Header -->\n      <div class=\"story-header\">\n        <div class=\"user-info\">\n          <img [src]=\"currentStory.user.avatar\" [alt]=\"currentStory.user.fullName\" class=\"user-avatar\">\n          <div class=\"user-details\">\n            <span class=\"username\">{{ currentStory.user.username }}</span>\n            <span class=\"time-ago\">{{ getTimeAgo(currentStory.createdAt) }}</span>\n          </div>\n        </div>\n        <div class=\"story-actions\">\n          <button class=\"action-btn\" (click)=\"pauseStory()\" *ngIf=\"!isPaused\">\n            <i class=\"fas fa-pause\"></i>\n          </button>\n          <button class=\"action-btn\" (click)=\"resumeStory()\" *ngIf=\"isPaused\">\n            <i class=\"fas fa-play\"></i>\n          </button>\n          <button class=\"action-btn\" (click)=\"closeStory()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story-content\">\n        <!-- Image Story -->\n        <img\n          *ngIf=\"currentMediaItem?.type === 'image'\"\n          [src]=\"currentMediaItem?.url || ''\"\n          [alt]=\"currentMediaItem?.alt || currentStory?.caption || ''\"\n          class=\"story-media\"\n          (load)=\"onMediaLoaded()\"\n          (error)=\"handleImageError($event)\"\n        >\n\n        <!-- Video Story -->\n        <video\n          *ngIf=\"currentMediaItem?.type === 'video'\"\n          [src]=\"currentMediaItem?.url || ''\"\n          [poster]=\"currentMediaItem?.thumbnailUrl || ''\"\n          class=\"story-media\"\n          autoplay\n          muted\n          playsinline\n          (loadeddata)=\"onMediaLoaded()\"\n          (ended)=\"nextStory()\"\n          (error)=\"handleVideoError($event)\"\n          #videoElement\n        ></video>\n\n        <!-- Video Controls for Stories -->\n        <div *ngIf=\"currentMediaItem?.type === 'video'\" class=\"video-story-controls\">\n          <button class=\"video-play-btn\" (click)=\"toggleStoryVideo()\" [class.playing]=\"isStoryVideoPlaying\">\n            <i [class]=\"isStoryVideoPlaying ? 'fas fa-pause' : 'fas fa-play'\"></i>\n          </button>\n        </div>\n\n        <!-- Story Caption -->\n        <div class=\"story-caption\" *ngIf=\"currentStory.caption\">\n          <p>{{ currentStory.caption }}</p>\n        </div>\n\n        <!-- Product Tags -->\n        <div class=\"product-tags\" *ngIf=\"currentStory.products.length > 0\">\n          <div \n            *ngFor=\"let productTag of currentStory.products\" \n            class=\"product-tag\"\n            [style.top.%]=\"productTag.position.y\"\n            [style.left.%]=\"productTag.position.x\"\n            (click)=\"showProductDetails(productTag)\"\n          >\n            <div class=\"tag-dot\"></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Navigation Areas -->\n      <div class=\"nav-area nav-left\" (click)=\"previousStory()\"></div>\n      <div class=\"nav-area nav-right\" (click)=\"nextStory()\"></div>\n\n      <!-- Story Footer -->\n      <div class=\"story-footer\">\n        <div class=\"story-input\">\n          <input \n            type=\"text\" \n            placeholder=\"Send message\" \n            [(ngModel)]=\"messageText\"\n            (keyup.enter)=\"sendMessage()\"\n            class=\"message-input\"\n          >\n          <button class=\"send-btn\" (click)=\"sendMessage()\" *ngIf=\"messageText.trim()\">\n            <i class=\"fas fa-paper-plane\"></i>\n          </button>\n        </div>\n        <div class=\"story-reactions\">\n          <button class=\"reaction-btn\" (click)=\"likeStory()\">\n            <i class=\"far fa-heart\"></i>\n          </button>\n          <button class=\"reaction-btn\" (click)=\"shareStory()\">\n            <i class=\"far fa-share\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Product Modal -->\n    <div class=\"product-modal\" *ngIf=\"selectedProduct\" (click)=\"closeProductModal()\">\n      <div class=\"product-modal-content\" (click)=\"$event.stopPropagation()\">\n        <div class=\"product-header\">\n          <img [src]=\"selectedProduct.product.images[0].url\" [alt]=\"selectedProduct.product.name\" class=\"product-image\">\n          <div class=\"product-info\">\n            <h3>{{ selectedProduct.product.name }}</h3>\n            <p class=\"product-price\">₹{{ selectedProduct.product.price | number }}</p>\n            <p class=\"product-brand\">{{ selectedProduct.product.brand }}</p>\n          </div>\n        </div>\n        <div class=\"product-actions\">\n          <button class=\"btn-wishlist\" (click)=\"addToWishlist(selectedProduct.product._id)\">\n            <i class=\"far fa-heart\"></i>\n            Wishlist\n          </button>\n          <button class=\"btn-cart\" (click)=\"addToCart(selectedProduct.product._id)\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            Add to Cart\n          </button>\n          <button class=\"btn-buy-now\" (click)=\"buyNow(selectedProduct.product._id)\">\n            Buy Now\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div class=\"loading-container\" *ngIf=\"isLoading\">\n      <div class=\"spinner\"></div>\n    </div>\n  `,\n  styles: [`\n    .story-viewer {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: #000;\n      z-index: 10000;\n      display: flex;\n      flex-direction: column;\n      user-select: none;\n    }\n\n    .progress-container {\n      display: flex;\n      gap: 2px;\n      padding: 8px 16px;\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      z-index: 10;\n    }\n\n    .progress-bar {\n      flex: 1;\n      height: 2px;\n      background: rgba(255, 255, 255, 0.3);\n      border-radius: 1px;\n      overflow: hidden;\n    }\n\n    .progress-fill {\n      height: 100%;\n      background: white;\n      transition: width 0.1s linear;\n      border-radius: 1px;\n    }\n\n    .progress-fill.completed {\n      width: 100% !important;\n    }\n\n    .story-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 16px;\n      position: absolute;\n      top: 20px;\n      left: 0;\n      right: 0;\n      z-index: 10;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .user-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      border: 2px solid white;\n      object-fit: cover;\n    }\n\n    .user-details {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .username {\n      color: white;\n      font-weight: 600;\n      font-size: 14px;\n    }\n\n    .time-ago {\n      color: rgba(255, 255, 255, 0.7);\n      font-size: 12px;\n    }\n\n    .story-actions {\n      display: flex;\n      gap: 8px;\n    }\n\n    .action-btn {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background: rgba(0, 0, 0, 0.5);\n      border: none;\n      color: white;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      transition: background 0.2s;\n    }\n\n    .action-btn:hover {\n      background: rgba(0, 0, 0, 0.7);\n    }\n\n    .story-content {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      position: relative;\n      overflow: hidden;\n    }\n\n    .story-media {\n      max-width: 100%;\n      max-height: 100%;\n      object-fit: contain;\n    }\n\n    /* Video Story Controls */\n    .video-story-controls {\n      position: absolute;\n      bottom: 100px;\n      right: 20px;\n      z-index: 1000;\n    }\n\n    .video-play-btn {\n      width: 50px;\n      height: 50px;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.9);\n      border: none;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      font-size: 16px;\n      color: #333;\n      backdrop-filter: blur(10px);\n    }\n\n    .video-play-btn:hover {\n      background: white;\n      transform: scale(1.1);\n    }\n\n    .video-play-btn.playing {\n      background: rgba(0, 0, 0, 0.7);\n      color: white;\n    }\n\n    .story-caption {\n      position: absolute;\n      bottom: 100px;\n      left: 16px;\n      right: 16px;\n      background: rgba(0, 0, 0, 0.5);\n      color: white;\n      padding: 12px 16px;\n      border-radius: 20px;\n      backdrop-filter: blur(10px);\n    }\n\n    .story-caption p {\n      margin: 0;\n      font-size: 14px;\n      line-height: 1.4;\n    }\n\n    .product-tags {\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      pointer-events: none;\n    }\n\n    .product-tag {\n      position: absolute;\n      pointer-events: all;\n      cursor: pointer;\n    }\n\n    .tag-dot {\n      width: 24px;\n      height: 24px;\n      background: white;\n      border-radius: 50%;\n      border: 2px solid #007bff;\n      position: relative;\n      animation: pulse 2s infinite;\n    }\n\n    .tag-dot::after {\n      content: '';\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      width: 8px;\n      height: 8px;\n      background: #007bff;\n      border-radius: 50%;\n    }\n\n    @keyframes pulse {\n      0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }\n      70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }\n      100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }\n    }\n\n    .nav-area {\n      position: absolute;\n      top: 0;\n      bottom: 0;\n      width: 30%;\n      z-index: 5;\n      cursor: pointer;\n    }\n\n    .nav-left {\n      left: 0;\n    }\n\n    .nav-right {\n      right: 0;\n    }\n\n    .story-footer {\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      padding: 16px;\n      background: rgba(0, 0, 0, 0.3);\n      backdrop-filter: blur(10px);\n    }\n\n    .story-input {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .message-input {\n      flex: 1;\n      background: rgba(255, 255, 255, 0.1);\n      border: 1px solid rgba(255, 255, 255, 0.2);\n      border-radius: 20px;\n      padding: 12px 16px;\n      color: white;\n      font-size: 14px;\n    }\n\n    .message-input::placeholder {\n      color: rgba(255, 255, 255, 0.7);\n    }\n\n    .message-input:focus {\n      outline: none;\n      border-color: rgba(255, 255, 255, 0.4);\n    }\n\n    .send-btn {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background: #007bff;\n      border: none;\n      color: white;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .story-reactions {\n      display: flex;\n      gap: 8px;\n    }\n\n    .reaction-btn {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.1);\n      border: none;\n      color: white;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 18px;\n    }\n\n    .product-modal {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: rgba(0, 0, 0, 0.8);\n      z-index: 20000;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n    }\n\n    .product-modal-content {\n      background: white;\n      border-radius: 16px;\n      padding: 24px;\n      max-width: 400px;\n      width: 100%;\n      animation: modalSlideUp 0.3s ease;\n    }\n\n    @keyframes modalSlideUp {\n      from {\n        transform: translateY(50px);\n        opacity: 0;\n      }\n      to {\n        transform: translateY(0);\n        opacity: 1;\n      }\n    }\n\n    .product-header {\n      display: flex;\n      gap: 16px;\n      margin-bottom: 20px;\n    }\n\n    .product-image {\n      width: 80px;\n      height: 80px;\n      border-radius: 8px;\n      object-fit: cover;\n    }\n\n    .product-info h3 {\n      margin: 0 0 8px 0;\n      font-size: 18px;\n      font-weight: 600;\n    }\n\n    .product-price {\n      font-size: 20px;\n      font-weight: 700;\n      color: #e91e63;\n      margin: 0 0 4px 0;\n    }\n\n    .product-brand {\n      color: #666;\n      margin: 0;\n      font-size: 14px;\n    }\n\n    .product-actions {\n      display: flex;\n      gap: 8px;\n    }\n\n    .btn-wishlist, .btn-cart, .btn-buy-now {\n      flex: 1;\n      padding: 12px;\n      border: none;\n      border-radius: 8px;\n      font-weight: 600;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      font-size: 12px;\n    }\n\n    .btn-wishlist {\n      background: #f8f9fa;\n      color: #666;\n      border: 1px solid #ddd;\n    }\n\n    .btn-cart {\n      background: #2196f3;\n      color: white;\n    }\n\n    .btn-buy-now {\n      background: #ff9800;\n      color: white;\n    }\n\n    .loading-container {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: #000;\n      z-index: 10000;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .spinner {\n      width: 40px;\n      height: 40px;\n      border: 3px solid rgba(255, 255, 255, 0.3);\n      border-top: 3px solid white;\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    /* Mobile Responsive Styles */\n    @media (max-width: 768px) {\n      .story-viewer {\n        padding: 0;\n      }\n\n      .story-container {\n        max-width: 100vw;\n        height: 100vh;\n        border-radius: 0;\n      }\n\n      .progress-bars {\n        padding: 1rem 1rem 0.5rem;\n      }\n\n      .progress-bar {\n        height: 2px;\n      }\n\n      .story-header {\n        padding: 0.75rem 1rem;\n      }\n\n      .user-avatar {\n        width: 35px;\n        height: 35px;\n      }\n\n      .user-info h3 {\n        font-size: 0.9rem;\n      }\n\n      .user-info span {\n        font-size: 0.8rem;\n      }\n\n      .header-actions {\n        gap: 1rem;\n      }\n\n      .header-btn {\n        width: 35px;\n        height: 35px;\n        font-size: 1rem;\n      }\n\n      .story-content {\n        height: calc(100vh - 140px);\n      }\n\n      .story-footer {\n        padding: 1rem;\n      }\n\n      .message-input {\n        padding: 0.75rem 1rem;\n        font-size: 0.9rem;\n      }\n\n      .footer-actions {\n        gap: 1rem;\n      }\n\n      .footer-btn {\n        width: 35px;\n        height: 35px;\n        font-size: 1.2rem;\n      }\n\n      .nav-area {\n        width: 40%;\n      }\n\n      .product-tag {\n        transform: scale(0.9);\n      }\n\n      .product-info {\n        width: 280px;\n        padding: 1rem;\n      }\n\n      .product-image {\n        width: 60px;\n        height: 60px;\n      }\n\n      .product-details h5 {\n        font-size: 0.9rem;\n      }\n\n      .product-details p {\n        font-size: 0.8rem;\n      }\n\n      .product-actions {\n        flex-direction: column;\n        gap: 0.5rem;\n      }\n\n      .product-btn {\n        padding: 0.6rem 1rem;\n        font-size: 0.8rem;\n      }\n\n      .video-story-controls {\n        bottom: 120px;\n        right: 15px;\n      }\n\n      .video-play-btn {\n        width: 45px;\n        height: 45px;\n        font-size: 14px;\n      }\n\n      /* Product Modal Mobile */\n      .product-modal {\n        padding: 1rem;\n        max-width: 90vw;\n        max-height: 80vh;\n      }\n\n      .modal-content {\n        padding: 1rem;\n      }\n\n      .modal-header h4 {\n        font-size: 1.1rem;\n      }\n\n      .modal-body {\n        padding: 1rem 0;\n      }\n\n      .modal-footer {\n        flex-direction: column;\n        gap: 0.75rem;\n      }\n\n      .modal-footer .product-btn {\n        width: 100%;\n        justify-content: center;\n      }\n    }\n\n    /* Touch-friendly interactions for mobile */\n    @media (hover: none) and (pointer: coarse) {\n      .header-btn,\n      .footer-btn,\n      .product-btn,\n      .video-play-btn {\n        transform: none;\n        transition: background-color 0.2s ease;\n      }\n\n      .header-btn:active,\n      .footer-btn:active,\n      .product-btn:active,\n      .video-play-btn:active {\n        transform: scale(0.95);\n      }\n\n      .product-tag:active {\n        transform: scale(0.85);\n      }\n\n      /* Always show video controls on touch devices */\n      .video-story-controls {\n        opacity: 0.8;\n      }\n\n      .video-play-btn {\n        background: rgba(255, 255, 255, 0.95);\n      }\n    }\n\n    /* Landscape mobile orientation */\n    @media (max-width: 768px) and (orientation: landscape) {\n      .story-container {\n        max-width: 100vh;\n        margin: 0 auto;\n      }\n\n      .story-content {\n        height: calc(100vh - 100px);\n      }\n\n      .story-header,\n      .story-footer {\n        padding: 0.5rem 1rem;\n      }\n\n      .progress-bars {\n        padding: 0.5rem 1rem 0.25rem;\n      }\n    }\n  `]\n})\nexport class StoryViewerComponent implements OnInit, OnDestroy {\n  userStories: Story[] = [];\n  currentStoryIndex = 0;\n  currentStory: Story | null = null;\n\n  isLoading = true;\n  isPaused = false;\n  progress = 0;\n  storyDuration = 5000; // 5 seconds for images\n\n  messageText = '';\n  selectedProduct: any = null;\n\n  // Media handling\n  currentMediaItem: MediaItem | null = null;\n  isStoryVideoPlaying = false;\n\n  private progressSubscription?: Subscription;\n  private storyTimeout?: any;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private storyService: StoryService,\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private mediaService: MediaService\n  ) {}\n\n  ngOnInit() {\n    this.route.params.subscribe(params => {\n      const userId = params['userId'];\n      const storyIndex = parseInt(params['storyIndex']) || 0;\n      \n      if (userId) {\n        this.loadUserStories(userId, storyIndex);\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this.stopProgress();\n    if (this.storyTimeout) {\n      clearTimeout(this.storyTimeout);\n    }\n  }\n\n  @HostListener('document:keydown', ['$event'])\n  handleKeyboardEvent(event: KeyboardEvent) {\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n      case ' ':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStory();\n        break;\n    }\n  }\n\n  loadUserStories(userId: string, startIndex: number = 0) {\n    this.isLoading = true;\n\n    this.storyService.getUserStories(userId).subscribe({\n      next: (response) => {\n        this.userStories = response.stories;\n\n        // Enhance stories with video content if needed\n        this.userStories = this.enhanceStoriesWithMedia(this.userStories);\n\n        this.currentStoryIndex = Math.min(startIndex, this.userStories.length - 1);\n        this.currentStory = this.userStories[this.currentStoryIndex];\n        this.updateCurrentMedia();\n        this.isLoading = false;\n        this.startStoryProgress();\n      },\n      error: (error) => {\n        console.error('Failed to load stories:', error);\n        this.isLoading = false;\n        this.closeStory();\n      }\n    });\n  }\n\n  enhanceStoriesWithMedia(stories: Story[]): Story[] {\n    return stories.map((story, index) => {\n      // If story doesn't have media or has broken media, add sample video\n      if (!story.media || !story.media.url) {\n        const sampleVideo = this.mediaService.getRandomSampleVideo();\n        story.media = {\n          type: 'video',\n          url: sampleVideo.url,\n          thumbnail: sampleVideo.thumbnail,\n          duration: sampleVideo.duration\n        };\n      }\n      return story;\n    });\n  }\n\n  updateCurrentMedia() {\n    if (this.currentStory?.media) {\n      this.currentMediaItem = {\n        id: this.currentStory._id,\n        type: this.currentStory.media.type,\n        url: this.mediaService.getSafeImageUrl(this.currentStory.media.url, 'story'),\n        thumbnailUrl: this.currentStory.media.thumbnail,\n        alt: this.currentStory.caption,\n        duration: this.currentStory.media.duration\n      };\n    }\n  }\n\n  startStoryProgress() {\n    this.stopProgress();\n    this.progress = 0;\n    \n    if (this.currentStory?.media.type === 'video') {\n      // For videos, let the video control the progress\n      return;\n    }\n    \n    const intervalMs = 50; // Update every 50ms\n    const increment = (intervalMs / this.storyDuration) * 100;\n\n    this.progressSubscription = timer(0, intervalMs).subscribe(() => {\n      if (!this.isPaused) {\n        this.progress += increment;\n        if (this.progress >= 100) {\n          this.nextStory();\n        }\n      }\n    });\n  }\n\n  stopProgress() {\n    if (this.progressSubscription) {\n      this.progressSubscription.unsubscribe();\n      this.progressSubscription = undefined;\n    }\n  }\n\n  getProgressWidth(index: number): number {\n    if (index < this.currentStoryIndex) {\n      return 100;\n    } else if (index === this.currentStoryIndex) {\n      return this.progress;\n    } else {\n      return 0;\n    }\n  }\n\n  nextStory() {\n    if (this.currentStoryIndex < this.userStories.length - 1) {\n      this.currentStoryIndex++;\n      this.currentStory = this.userStories[this.currentStoryIndex];\n      this.updateCurrentMedia();\n      this.startStoryProgress();\n    } else {\n      // Move to next user's stories or close\n      this.closeStory();\n    }\n  }\n\n  previousStory() {\n    if (this.currentStoryIndex > 0) {\n      this.currentStoryIndex--;\n      this.currentStory = this.userStories[this.currentStoryIndex];\n      this.updateCurrentMedia();\n      this.startStoryProgress();\n    }\n  }\n\n  pauseStory() {\n    this.isPaused = true;\n    const videoElement = document.querySelector('video') as HTMLVideoElement;\n    if (videoElement) {\n      videoElement.pause();\n    }\n  }\n\n  resumeStory() {\n    this.isPaused = false;\n    const videoElement = document.querySelector('video') as HTMLVideoElement;\n    if (videoElement) {\n      videoElement.play();\n    }\n  }\n\n  onStoryClick(event: MouseEvent) {\n    const target = event.target as HTMLElement;\n    \n    // Don't handle clicks on interactive elements\n    if (target.closest('.story-header') || \n        target.closest('.story-footer') || \n        target.closest('.product-tag') ||\n        target.closest('.nav-area')) {\n      return;\n    }\n    \n    // Pause/resume on tap\n    if (this.isPaused) {\n      this.resumeStory();\n    } else {\n      this.pauseStory();\n    }\n  }\n\n  onMediaLoaded() {\n    // Media is loaded, story can start\n    if (this.currentMediaItem?.type === 'video') {\n      this.isStoryVideoPlaying = true;\n    }\n  }\n\n  handleImageError(event: Event): void {\n    this.mediaService.handleImageError(event, 'story');\n  }\n\n  handleVideoError(event: Event): void {\n    console.error('Story video error:', event);\n    // Could fallback to thumbnail or different content\n  }\n\n  toggleStoryVideo(): void {\n    const videoElement = document.querySelector('video') as HTMLVideoElement;\n    if (videoElement) {\n      if (videoElement.paused) {\n        videoElement.play();\n        this.isStoryVideoPlaying = true;\n        this.resumeStory();\n      } else {\n        videoElement.pause();\n        this.isStoryVideoPlaying = false;\n        this.pauseStory();\n      }\n    }\n  }\n\n  showProductDetails(productTag: any) {\n    this.selectedProduct = productTag;\n    this.pauseStory();\n  }\n\n  closeProductModal() {\n    this.selectedProduct = null;\n    this.resumeStory();\n  }\n\n  addToWishlist(productId: string) {\n    if (this.selectedProduct) {\n      this.wishlistService.addToWishlist(productId).subscribe({\n        next: () => {\n          this.showNotification('Added to wishlist ❤️');\n          this.closeProductModal();\n        },\n        error: (error) => {\n          console.error('Wishlist error:', error);\n          // Fallback to offline mode\n          this.wishlistService.addToWishlistOffline(this.selectedProduct.product);\n          this.showNotification('Added to wishlist ❤️');\n          this.closeProductModal();\n        }\n      });\n    }\n  }\n\n  addToCart(productId: string) {\n    if (this.selectedProduct) {\n      this.cartService.addToCart(productId, 1, this.selectedProduct.size, this.selectedProduct.color).subscribe({\n        next: () => {\n          this.showNotification('Added to cart 🛒');\n          this.closeProductModal();\n        },\n        error: (error: any) => {\n          console.error('Cart error:', error);\n          this.showNotification('Added to cart 🛒');\n          this.closeProductModal();\n        }\n      });\n    }\n  }\n\n  buyNow(productId: string) {\n    if (this.selectedProduct) {\n      this.cartService.addToCart(productId, 1, this.selectedProduct.size, this.selectedProduct.color).subscribe({\n        next: () => {\n          this.showNotification('Redirecting to checkout...');\n          this.closeProductModal();\n          this.router.navigate(['/shop/checkout']);\n        },\n        error: (error: any) => {\n          console.error('Buy now error:', error);\n          this.showNotification('Redirecting to product page...');\n          this.closeProductModal();\n          this.router.navigate(['/product', productId]);\n        }\n      });\n    }\n  }\n\n  private showNotification(message: string) {\n    // Create a simple notification\n    const notification = document.createElement('div');\n    notification.textContent = message;\n    notification.style.cssText = `\n      position: fixed;\n      top: 80px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: white;\n      padding: 12px 20px;\n      border-radius: 20px;\n      z-index: 30000;\n      font-size: 14px;\n      backdrop-filter: blur(10px);\n      animation: slideDown 0.3s ease;\n    `;\n\n    document.body.appendChild(notification);\n\n    setTimeout(() => {\n      notification.remove();\n    }, 2000);\n  }\n\n  sendMessage() {\n    if (this.messageText.trim()) {\n      console.log('Send message:', this.messageText);\n      this.messageText = '';\n    }\n  }\n\n  likeStory() {\n    console.log('Like story');\n  }\n\n  shareStory() {\n    console.log('Share story');\n  }\n\n  closeStory() {\n    this.router.navigate(['/']);\n  }\n\n  getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diff = now.getTime() - new Date(date).getTime();\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    \n    if (hours < 1) return 'now';\n    if (hours < 24) return `${hours}h`;\n    const days = Math.floor(hours / 24);\n    return `${days}d`;\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAAiCC,KAAK,QAAQ,MAAM;;;;;;;;;;;IAgB5CC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,cAKO;IACTF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJFH,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAK,WAAA,UAAAC,MAAA,CAAAC,gBAAA,CAAAC,IAAA,OAAqC;IAErCR,EADA,CAAAS,WAAA,WAAAD,IAAA,KAAAF,MAAA,CAAAI,iBAAA,CAAwC,cAAAF,IAAA,GAAAF,MAAA,CAAAI,iBAAA,CACC;;;;;;IAe3CV,EAAA,CAAAC,cAAA,iBAAoE;IAAzCD,EAAA,CAAAW,UAAA,mBAAAC,sEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAW,UAAA,EAAY;IAAA,EAAC;IAC/CjB,EAAA,CAAAE,SAAA,YAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAAoE;IAAzCD,EAAA,CAAAW,UAAA,mBAAAO,sEAAA;MAAAlB,EAAA,CAAAa,aAAA,CAAAM,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAc,WAAA,EAAa;IAAA,EAAC;IAChDpB,EAAA,CAAAE,SAAA,YAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAUXH,EAAA,CAAAC,cAAA,cAOC;IADCD,EADA,CAAAW,UAAA,kBAAAU,+DAAA;MAAArB,EAAA,CAAAa,aAAA,CAAAS,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAQV,MAAA,CAAAiB,aAAA,EAAe;IAAA,EAAC,mBAAAC,gEAAAC,MAAA;MAAAzB,EAAA,CAAAa,aAAA,CAAAS,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CACfV,MAAA,CAAAoB,gBAAA,CAAAD,MAAA,CAAwB;IAAA,EAAC;IANpCzB,EAAA,CAAAG,YAAA,EAOC;;;;IAJCH,EADA,CAAA2B,UAAA,SAAArB,MAAA,CAAAsB,gBAAA,kBAAAtB,MAAA,CAAAsB,gBAAA,CAAAC,GAAA,SAAA7B,EAAA,CAAA8B,aAAA,CAAmC,SAAAxB,MAAA,CAAAsB,gBAAA,kBAAAtB,MAAA,CAAAsB,gBAAA,CAAAG,GAAA,MAAAzB,MAAA,CAAA0B,YAAA,kBAAA1B,MAAA,CAAA0B,YAAA,CAAAC,OAAA,QACyB;;;;;;IAO9DjC,EAAA,CAAAC,cAAA,mBAYC;IAFCD,EAFA,CAAAW,UAAA,wBAAAuB,yEAAA;MAAAlC,EAAA,CAAAa,aAAA,CAAAsB,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAcV,MAAA,CAAAiB,aAAA,EAAe;IAAA,EAAC,mBAAAa,oEAAA;MAAApC,EAAA,CAAAa,aAAA,CAAAsB,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CACrBV,MAAA,CAAA+B,SAAA,EAAW;IAAA,EAAC,mBAAAC,oEAAAb,MAAA;MAAAzB,EAAA,CAAAa,aAAA,CAAAsB,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CACZV,MAAA,CAAAiC,gBAAA,CAAAd,MAAA,CAAwB;IAAA,EAAC;IAEnCzB,EAAA,CAAAG,YAAA,EAAQ;;;;IATPH,EADA,CAAA2B,UAAA,SAAArB,MAAA,CAAAsB,gBAAA,kBAAAtB,MAAA,CAAAsB,gBAAA,CAAAC,GAAA,SAAA7B,EAAA,CAAA8B,aAAA,CAAmC,YAAAxB,MAAA,CAAAsB,gBAAA,kBAAAtB,MAAA,CAAAsB,gBAAA,CAAAY,YAAA,SAAAxC,EAAA,CAAA8B,aAAA,CACY;;;;;;IAa/C9B,EADF,CAAAC,cAAA,cAA6E,iBACuB;IAAnED,EAAA,CAAAW,UAAA,mBAAA8B,mEAAA;MAAAzC,EAAA,CAAAa,aAAA,CAAA6B,GAAA;MAAA,MAAApC,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAqC,gBAAA,EAAkB;IAAA,EAAC;IACzD3C,EAAA,CAAAE,SAAA,QAAsE;IAE1EF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAHwDH,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAS,WAAA,YAAAH,MAAA,CAAAsC,mBAAA,CAAqC;IAC5F5C,EAAA,CAAAI,SAAA,EAA8D;IAA9DJ,EAAA,CAAA6C,UAAA,CAAAvC,MAAA,CAAAsC,mBAAA,kCAA8D;;;;;IAMnE5C,EADF,CAAAC,cAAA,cAAwD,QACnD;IAAAD,EAAA,CAAA8C,MAAA,GAA0B;IAC/B9C,EAD+B,CAAAG,YAAA,EAAI,EAC7B;;;;IADDH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAA+C,iBAAA,CAAAzC,MAAA,CAAA0B,YAAA,CAAAC,OAAA,CAA0B;;;;;;IAK7BjC,EAAA,CAAAC,cAAA,cAMC;IADCD,EAAA,CAAAW,UAAA,mBAAAqC,sEAAA;MAAA,MAAAC,cAAA,GAAAjD,EAAA,CAAAa,aAAA,CAAAqC,GAAA,EAAAC,SAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA8C,kBAAA,CAAAH,cAAA,CAA8B;IAAA,EAAC;IAExCjD,EAAA,CAAAE,SAAA,cAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;;;;IAJJH,EADA,CAAAK,WAAA,QAAA4C,cAAA,CAAAI,QAAA,CAAAC,CAAA,MAAqC,SAAAL,cAAA,CAAAI,QAAA,CAAAE,CAAA,MACC;;;;;IAL1CvD,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAwD,UAAA,IAAAC,gDAAA,kBAMC;IAGHzD,EAAA,CAAAG,YAAA,EAAM;;;;IARqBH,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAA2B,UAAA,YAAArB,MAAA,CAAA0B,YAAA,CAAA0B,QAAA,CAAwB;;;;;;IAyBjD1D,EAAA,CAAAC,cAAA,iBAA4E;IAAnDD,EAAA,CAAAW,UAAA,mBAAAgD,sEAAA;MAAA3D,EAAA,CAAAa,aAAA,CAAA+C,IAAA;MAAA,MAAAtD,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAuD,WAAA,EAAa;IAAA,EAAC;IAC9C7D,EAAA,CAAAE,SAAA,YAAkC;IACpCF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA3GfH,EAAA,CAAAC,cAAA,aAA8E;IAA/BD,EAAA,CAAAW,UAAA,mBAAAmD,yDAAArC,MAAA;MAAAzB,EAAA,CAAAa,aAAA,CAAAkD,GAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA0D,YAAA,CAAAvC,MAAA,CAAoB;IAAA,EAAC;IAE3EzB,EAAA,CAAAC,cAAA,aAAgC;IAC9BD,EAAA,CAAAwD,UAAA,IAAAS,yCAAA,iBAGC;IAQHjE,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,aAA0B,aACD;IACrBD,EAAA,CAAAE,SAAA,aAA6F;IAE3FF,EADF,CAAAC,cAAA,cAA0B,eACD;IAAAD,EAAA,CAAA8C,MAAA,GAAgC;IAAA9C,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAA8C,MAAA,IAAwC;IAEnE9C,EAFmE,CAAAG,YAAA,EAAO,EAClE,EACF;IACNH,EAAA,CAAAC,cAAA,eAA2B;IAIzBD,EAHA,CAAAwD,UAAA,KAAAU,6CAAA,qBAAoE,KAAAC,6CAAA,qBAGA;IAGpEnE,EAAA,CAAAC,cAAA,kBAAkD;IAAvBD,EAAA,CAAAW,UAAA,mBAAAyD,6DAAA;MAAApE,EAAA,CAAAa,aAAA,CAAAkD,GAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA+D,UAAA,EAAY;IAAA,EAAC;IAC/CrE,EAAA,CAAAE,SAAA,aAA4B;IAGlCF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGNH,EAAA,CAAAC,cAAA,eAA2B;IAuCzBD,EArCA,CAAAwD,UAAA,KAAAc,0CAAA,kBAOC,KAAAC,4CAAA,oBAeA,KAAAC,0CAAA,kBAG4E,KAAAC,0CAAA,kBAOrB,KAAAC,0CAAA,kBAKW;IAWrE1E,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAyD;IAA1BD,EAAA,CAAAW,UAAA,mBAAAgE,0DAAA;MAAA3E,EAAA,CAAAa,aAAA,CAAAkD,GAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAsE,aAAA,EAAe;IAAA,EAAC;IAAC5E,EAAA,CAAAG,YAAA,EAAM;IAC/DH,EAAA,CAAAC,cAAA,eAAsD;IAAtBD,EAAA,CAAAW,UAAA,mBAAAkE,0DAAA;MAAA7E,EAAA,CAAAa,aAAA,CAAAkD,GAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA+B,SAAA,EAAW;IAAA,EAAC;IAACrC,EAAA,CAAAG,YAAA,EAAM;IAKxDH,EAFJ,CAAAC,cAAA,eAA0B,eACC,iBAOtB;IAHCD,EAAA,CAAA8E,gBAAA,2BAAAC,oEAAAtD,MAAA;MAAAzB,EAAA,CAAAa,aAAA,CAAAkD,GAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAgF,kBAAA,CAAA1E,MAAA,CAAA2E,WAAA,EAAAxD,MAAA,MAAAnB,MAAA,CAAA2E,WAAA,GAAAxD,MAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAAAS,MAAA;IAAA,EAAyB;IACzBzB,EAAA,CAAAW,UAAA,yBAAAuE,kEAAA;MAAAlF,EAAA,CAAAa,aAAA,CAAAkD,GAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAeV,MAAA,CAAAuD,WAAA,EAAa;IAAA,EAAC;IAJ/B7D,EAAA,CAAAG,YAAA,EAMC;IACDH,EAAA,CAAAwD,UAAA,KAAA2B,6CAAA,qBAA4E;IAG9EnF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA6B,kBACwB;IAAtBD,EAAA,CAAAW,UAAA,mBAAAyE,6DAAA;MAAApF,EAAA,CAAAa,aAAA,CAAAkD,GAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA+E,SAAA,EAAW;IAAA,EAAC;IAChDrF,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAoD;IAAvBD,EAAA,CAAAW,UAAA,mBAAA2E,6DAAA;MAAAtF,EAAA,CAAAa,aAAA,CAAAkD,GAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAiF,UAAA,EAAY;IAAA,EAAC;IACjDvF,EAAA,CAAAE,SAAA,aAA4B;IAIpCF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IAlHkBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAA2B,UAAA,YAAArB,MAAA,CAAAkF,WAAA,CAAgB;IAe7BxF,EAAA,CAAAI,SAAA,GAAgC;IAACJ,EAAjC,CAAA2B,UAAA,QAAArB,MAAA,CAAA0B,YAAA,CAAAyD,IAAA,CAAAC,MAAA,EAAA1F,EAAA,CAAA8B,aAAA,CAAgC,QAAAxB,MAAA,CAAA0B,YAAA,CAAAyD,IAAA,CAAAE,QAAA,CAAmC;IAE/C3F,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAA+C,iBAAA,CAAAzC,MAAA,CAAA0B,YAAA,CAAAyD,IAAA,CAAAG,QAAA,CAAgC;IAChC5F,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAA+C,iBAAA,CAAAzC,MAAA,CAAAuF,UAAA,CAAAvF,MAAA,CAAA0B,YAAA,CAAA8D,SAAA,EAAwC;IAId9F,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAAyF,QAAA,CAAe;IAGd/F,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAyF,QAAA,CAAc;IAajE/F,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAAsB,gBAAA,kBAAAtB,MAAA,CAAAsB,gBAAA,CAAAoE,IAAA,cAAwC;IAUxChG,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAAsB,gBAAA,kBAAAtB,MAAA,CAAAsB,gBAAA,CAAAoE,IAAA,cAAwC;IAcrChG,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAAsB,gBAAA,kBAAAtB,MAAA,CAAAsB,gBAAA,CAAAoE,IAAA,cAAwC;IAOlBhG,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA0B,YAAA,CAAAC,OAAA,CAA0B;IAK3BjC,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA0B,YAAA,CAAA0B,QAAA,CAAAuC,MAAA,KAAsC;IAuB7DjG,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAkG,gBAAA,YAAA5F,MAAA,CAAA2E,WAAA,CAAyB;IAIuBjF,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA2E,WAAA,CAAAkB,IAAA,GAAwB;;;;;;IAgBhFnG,EAAA,CAAAC,cAAA,cAAiF;IAA9BD,EAAA,CAAAW,UAAA,mBAAAyF,yDAAA;MAAApG,EAAA,CAAAa,aAAA,CAAAwF,IAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAgG,iBAAA,EAAmB;IAAA,EAAC;IAC9EtG,EAAA,CAAAC,cAAA,cAAsE;IAAnCD,EAAA,CAAAW,UAAA,mBAAA4F,yDAAA9E,MAAA;MAAAzB,EAAA,CAAAa,aAAA,CAAAwF,IAAA;MAAA,OAAArG,EAAA,CAAAgB,WAAA,CAASS,MAAA,CAAA+E,eAAA,EAAwB;IAAA,EAAC;IACnExG,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAE,SAAA,cAA8G;IAE5GF,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAA8C,MAAA,GAAkC;IAAA9C,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAA8C,MAAA,GAA6C;;IAAA9C,EAAA,CAAAG,YAAA,EAAI;IAC1EH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAA8C,MAAA,IAAmC;IAEhE9C,EAFgE,CAAAG,YAAA,EAAI,EAC5D,EACF;IAEJH,EADF,CAAAC,cAAA,eAA6B,kBACuD;IAArDD,EAAA,CAAAW,UAAA,mBAAA8F,6DAAA;MAAAzG,EAAA,CAAAa,aAAA,CAAAwF,IAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAoG,aAAA,CAAApG,MAAA,CAAAqG,eAAA,CAAAC,OAAA,CAAAC,GAAA,CAA0C;IAAA,EAAC;IAC/E7G,EAAA,CAAAE,SAAA,aAA4B;IAC5BF,EAAA,CAAA8C,MAAA,kBACF;IAAA9C,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA0E;IAAjDD,EAAA,CAAAW,UAAA,mBAAAmG,6DAAA;MAAA9G,EAAA,CAAAa,aAAA,CAAAwF,IAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAyG,SAAA,CAAAzG,MAAA,CAAAqG,eAAA,CAAAC,OAAA,CAAAC,GAAA,CAAsC;IAAA,EAAC;IACvE7G,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAA8C,MAAA,qBACF;IAAA9C,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA0E;IAA9CD,EAAA,CAAAW,UAAA,mBAAAqG,6DAAA;MAAAhH,EAAA,CAAAa,aAAA,CAAAwF,IAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAA2G,MAAA,CAAA3G,MAAA,CAAAqG,eAAA,CAAAC,OAAA,CAAAC,GAAA,CAAmC;IAAA,EAAC;IACvE7G,EAAA,CAAA8C,MAAA,iBACF;IAGN9C,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IArBKH,EAAA,CAAAI,SAAA,GAA6C;IAACJ,EAA9C,CAAA2B,UAAA,QAAArB,MAAA,CAAAqG,eAAA,CAAAC,OAAA,CAAAM,MAAA,IAAArF,GAAA,EAAA7B,EAAA,CAAA8B,aAAA,CAA6C,QAAAxB,MAAA,CAAAqG,eAAA,CAAAC,OAAA,CAAAO,IAAA,CAAqC;IAEjFnH,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAA+C,iBAAA,CAAAzC,MAAA,CAAAqG,eAAA,CAAAC,OAAA,CAAAO,IAAA,CAAkC;IACbnH,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAoH,kBAAA,WAAApH,EAAA,CAAAqH,WAAA,OAAA/G,MAAA,CAAAqG,eAAA,CAAAC,OAAA,CAAAU,KAAA,MAA6C;IAC7CtH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAA+C,iBAAA,CAAAzC,MAAA,CAAAqG,eAAA,CAAAC,OAAA,CAAAW,KAAA,CAAmC;;;;;IAoBpEvH,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAE,SAAA,cAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;;;AAynBV,OAAM,MAAOqH,oBAAoB;EAoB/BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,YAA0B,EAC1BC,WAAwB,EACxBC,eAAgC,EAChCC,YAA0B;IAL1B,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IAzBtB,KAAAvC,WAAW,GAAY,EAAE;IACzB,KAAA9E,iBAAiB,GAAG,CAAC;IACrB,KAAAsB,YAAY,GAAiB,IAAI;IAEjC,KAAAgG,SAAS,GAAG,IAAI;IAChB,KAAAjC,QAAQ,GAAG,KAAK;IAChB,KAAAkC,QAAQ,GAAG,CAAC;IACZ,KAAAC,aAAa,GAAG,IAAI,CAAC,CAAC;IAEtB,KAAAjD,WAAW,GAAG,EAAE;IAChB,KAAA0B,eAAe,GAAQ,IAAI;IAE3B;IACA,KAAA/E,gBAAgB,GAAqB,IAAI;IACzC,KAAAgB,mBAAmB,GAAG,KAAK;EAYxB;EAEHuF,QAAQA,CAAA;IACN,IAAI,CAACT,KAAK,CAACU,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;MACnC,MAAME,MAAM,GAAGF,MAAM,CAAC,QAAQ,CAAC;MAC/B,MAAMG,UAAU,GAAGC,QAAQ,CAACJ,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC;MAEtD,IAAIE,MAAM,EAAE;QACV,IAAI,CAACG,eAAe,CAACH,MAAM,EAAEC,UAAU,CAAC;;IAE5C,CAAC,CAAC;EACJ;EAEAG,WAAWA,CAAA;IACT,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,IAAI,CAACC,YAAY,EAAE;MACrBC,YAAY,CAAC,IAAI,CAACD,YAAY,CAAC;;EAEnC;EAGAE,mBAAmBA,CAACC,KAAoB;IACtC,QAAQA,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACpE,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;MACjB,KAAK,GAAG;QACN,IAAI,CAACvC,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACgC,UAAU,EAAE;QACjB;;EAEN;EAEAoE,eAAeA,CAACH,MAAc,EAAEW,UAAA,GAAqB,CAAC;IACpD,IAAI,CAACjB,SAAS,GAAG,IAAI;IAErB,IAAI,CAACJ,YAAY,CAACsB,cAAc,CAACZ,MAAM,CAAC,CAACD,SAAS,CAAC;MACjDc,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC5D,WAAW,GAAG4D,QAAQ,CAACC,OAAO;QAEnC;QACA,IAAI,CAAC7D,WAAW,GAAG,IAAI,CAAC8D,uBAAuB,CAAC,IAAI,CAAC9D,WAAW,CAAC;QAEjE,IAAI,CAAC9E,iBAAiB,GAAG6I,IAAI,CAACC,GAAG,CAACP,UAAU,EAAE,IAAI,CAACzD,WAAW,CAACS,MAAM,GAAG,CAAC,CAAC;QAC1E,IAAI,CAACjE,YAAY,GAAG,IAAI,CAACwD,WAAW,CAAC,IAAI,CAAC9E,iBAAiB,CAAC;QAC5D,IAAI,CAAC+I,kBAAkB,EAAE;QACzB,IAAI,CAACzB,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC0B,kBAAkB,EAAE;MAC3B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC3B,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC3D,UAAU,EAAE;MACnB;KACD,CAAC;EACJ;EAEAiF,uBAAuBA,CAACD,OAAgB;IACtC,OAAOA,OAAO,CAACQ,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAI;MAClC;MACA,IAAI,CAACD,KAAK,CAACE,KAAK,IAAI,CAACF,KAAK,CAACE,KAAK,CAACnI,GAAG,EAAE;QACpC,MAAMoI,WAAW,GAAG,IAAI,CAAClC,YAAY,CAACmC,oBAAoB,EAAE;QAC5DJ,KAAK,CAACE,KAAK,GAAG;UACZhE,IAAI,EAAE,OAAO;UACbnE,GAAG,EAAEoI,WAAW,CAACpI,GAAG;UACpBsI,SAAS,EAAEF,WAAW,CAACE,SAAS;UAChCC,QAAQ,EAAEH,WAAW,CAACG;SACvB;;MAEH,OAAON,KAAK;IACd,CAAC,CAAC;EACJ;EAEAL,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACzH,YAAY,EAAEgI,KAAK,EAAE;MAC5B,IAAI,CAACpI,gBAAgB,GAAG;QACtByI,EAAE,EAAE,IAAI,CAACrI,YAAY,CAAC6E,GAAG;QACzBb,IAAI,EAAE,IAAI,CAAChE,YAAY,CAACgI,KAAK,CAAChE,IAAI;QAClCnE,GAAG,EAAE,IAAI,CAACkG,YAAY,CAACuC,eAAe,CAAC,IAAI,CAACtI,YAAY,CAACgI,KAAK,CAACnI,GAAG,EAAE,OAAO,CAAC;QAC5EW,YAAY,EAAE,IAAI,CAACR,YAAY,CAACgI,KAAK,CAACG,SAAS;QAC/CpI,GAAG,EAAE,IAAI,CAACC,YAAY,CAACC,OAAO;QAC9BmI,QAAQ,EAAE,IAAI,CAACpI,YAAY,CAACgI,KAAK,CAACI;OACnC;;EAEL;EAEAV,kBAAkBA,CAAA;IAChB,IAAI,CAACf,YAAY,EAAE;IACnB,IAAI,CAACV,QAAQ,GAAG,CAAC;IAEjB,IAAI,IAAI,CAACjG,YAAY,EAAEgI,KAAK,CAAChE,IAAI,KAAK,OAAO,EAAE;MAC7C;MACA;;IAGF,MAAMuE,UAAU,GAAG,EAAE,CAAC,CAAC;IACvB,MAAMC,SAAS,GAAID,UAAU,GAAG,IAAI,CAACrC,aAAa,GAAI,GAAG;IAEzD,IAAI,CAACuC,oBAAoB,GAAG1K,KAAK,CAAC,CAAC,EAAEwK,UAAU,CAAC,CAAClC,SAAS,CAAC,MAAK;MAC9D,IAAI,CAAC,IAAI,CAACtC,QAAQ,EAAE;QAClB,IAAI,CAACkC,QAAQ,IAAIuC,SAAS;QAC1B,IAAI,IAAI,CAACvC,QAAQ,IAAI,GAAG,EAAE;UACxB,IAAI,CAAC5F,SAAS,EAAE;;;IAGtB,CAAC,CAAC;EACJ;EAEAsG,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC8B,oBAAoB,EAAE;MAC7B,IAAI,CAACA,oBAAoB,CAACC,WAAW,EAAE;MACvC,IAAI,CAACD,oBAAoB,GAAGE,SAAS;;EAEzC;EAEApK,gBAAgBA,CAACwJ,KAAa;IAC5B,IAAIA,KAAK,GAAG,IAAI,CAACrJ,iBAAiB,EAAE;MAClC,OAAO,GAAG;KACX,MAAM,IAAIqJ,KAAK,KAAK,IAAI,CAACrJ,iBAAiB,EAAE;MAC3C,OAAO,IAAI,CAACuH,QAAQ;KACrB,MAAM;MACL,OAAO,CAAC;;EAEZ;EAEA5F,SAASA,CAAA;IACP,IAAI,IAAI,CAAC3B,iBAAiB,GAAG,IAAI,CAAC8E,WAAW,CAACS,MAAM,GAAG,CAAC,EAAE;MACxD,IAAI,CAACvF,iBAAiB,EAAE;MACxB,IAAI,CAACsB,YAAY,GAAG,IAAI,CAACwD,WAAW,CAAC,IAAI,CAAC9E,iBAAiB,CAAC;MAC5D,IAAI,CAAC+I,kBAAkB,EAAE;MACzB,IAAI,CAACC,kBAAkB,EAAE;KAC1B,MAAM;MACL;MACA,IAAI,CAACrF,UAAU,EAAE;;EAErB;EAEAO,aAAaA,CAAA;IACX,IAAI,IAAI,CAAClE,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACsB,YAAY,GAAG,IAAI,CAACwD,WAAW,CAAC,IAAI,CAAC9E,iBAAiB,CAAC;MAC5D,IAAI,CAAC+I,kBAAkB,EAAE;MACzB,IAAI,CAACC,kBAAkB,EAAE;;EAE7B;EAEAzI,UAAUA,CAAA;IACR,IAAI,CAAC8E,QAAQ,GAAG,IAAI;IACpB,MAAM6E,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAqB;IACxE,IAAIF,YAAY,EAAE;MAChBA,YAAY,CAACG,KAAK,EAAE;;EAExB;EAEA3J,WAAWA,CAAA;IACT,IAAI,CAAC2E,QAAQ,GAAG,KAAK;IACrB,MAAM6E,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAqB;IACxE,IAAIF,YAAY,EAAE;MAChBA,YAAY,CAACI,IAAI,EAAE;;EAEvB;EAEAhH,YAAYA,CAAC+E,KAAiB;IAC5B,MAAMkC,MAAM,GAAGlC,KAAK,CAACkC,MAAqB;IAE1C;IACA,IAAIA,MAAM,CAACC,OAAO,CAAC,eAAe,CAAC,IAC/BD,MAAM,CAACC,OAAO,CAAC,eAAe,CAAC,IAC/BD,MAAM,CAACC,OAAO,CAAC,cAAc,CAAC,IAC9BD,MAAM,CAACC,OAAO,CAAC,WAAW,CAAC,EAAE;MAC/B;;IAGF;IACA,IAAI,IAAI,CAACnF,QAAQ,EAAE;MACjB,IAAI,CAAC3E,WAAW,EAAE;KACnB,MAAM;MACL,IAAI,CAACH,UAAU,EAAE;;EAErB;EAEAM,aAAaA,CAAA;IACX;IACA,IAAI,IAAI,CAACK,gBAAgB,EAAEoE,IAAI,KAAK,OAAO,EAAE;MAC3C,IAAI,CAACpD,mBAAmB,GAAG,IAAI;;EAEnC;EAEAlB,gBAAgBA,CAACqH,KAAY;IAC3B,IAAI,CAAChB,YAAY,CAACrG,gBAAgB,CAACqH,KAAK,EAAE,OAAO,CAAC;EACpD;EAEAxG,gBAAgBA,CAACwG,KAAY;IAC3Ba,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEZ,KAAK,CAAC;IAC1C;EACF;EAEApG,gBAAgBA,CAAA;IACd,MAAMiI,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAqB;IACxE,IAAIF,YAAY,EAAE;MAChB,IAAIA,YAAY,CAACO,MAAM,EAAE;QACvBP,YAAY,CAACI,IAAI,EAAE;QACnB,IAAI,CAACpI,mBAAmB,GAAG,IAAI;QAC/B,IAAI,CAACxB,WAAW,EAAE;OACnB,MAAM;QACLwJ,YAAY,CAACG,KAAK,EAAE;QACpB,IAAI,CAACnI,mBAAmB,GAAG,KAAK;QAChC,IAAI,CAAC3B,UAAU,EAAE;;;EAGvB;EAEAmC,kBAAkBA,CAACgI,UAAe;IAChC,IAAI,CAACzE,eAAe,GAAGyE,UAAU;IACjC,IAAI,CAACnK,UAAU,EAAE;EACnB;EAEAqF,iBAAiBA,CAAA;IACf,IAAI,CAACK,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACvF,WAAW,EAAE;EACpB;EAEAsF,aAAaA,CAAC2E,SAAiB;IAC7B,IAAI,IAAI,CAAC1E,eAAe,EAAE;MACxB,IAAI,CAACmB,eAAe,CAACpB,aAAa,CAAC2E,SAAS,CAAC,CAAChD,SAAS,CAAC;QACtDc,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACmC,gBAAgB,CAAC,sBAAsB,CAAC;UAC7C,IAAI,CAAChF,iBAAiB,EAAE;QAC1B,CAAC;QACDqD,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;UACvC;UACA,IAAI,CAAC7B,eAAe,CAACyD,oBAAoB,CAAC,IAAI,CAAC5E,eAAe,CAACC,OAAO,CAAC;UACvE,IAAI,CAAC0E,gBAAgB,CAAC,sBAAsB,CAAC;UAC7C,IAAI,CAAChF,iBAAiB,EAAE;QAC1B;OACD,CAAC;;EAEN;EAEAS,SAASA,CAACsE,SAAiB;IACzB,IAAI,IAAI,CAAC1E,eAAe,EAAE;MACxB,IAAI,CAACkB,WAAW,CAACd,SAAS,CAACsE,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC1E,eAAe,CAAC6E,IAAI,EAAE,IAAI,CAAC7E,eAAe,CAAC8E,KAAK,CAAC,CAACpD,SAAS,CAAC;QACxGc,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACmC,gBAAgB,CAAC,kBAAkB,CAAC;UACzC,IAAI,CAAChF,iBAAiB,EAAE;QAC1B,CAAC;QACDqD,KAAK,EAAGA,KAAU,IAAI;UACpBC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;UACnC,IAAI,CAAC2B,gBAAgB,CAAC,kBAAkB,CAAC;UACzC,IAAI,CAAChF,iBAAiB,EAAE;QAC1B;OACD,CAAC;;EAEN;EAEAW,MAAMA,CAACoE,SAAiB;IACtB,IAAI,IAAI,CAAC1E,eAAe,EAAE;MACxB,IAAI,CAACkB,WAAW,CAACd,SAAS,CAACsE,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC1E,eAAe,CAAC6E,IAAI,EAAE,IAAI,CAAC7E,eAAe,CAAC8E,KAAK,CAAC,CAACpD,SAAS,CAAC;QACxGc,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACmC,gBAAgB,CAAC,4BAA4B,CAAC;UACnD,IAAI,CAAChF,iBAAiB,EAAE;UACxB,IAAI,CAACqB,MAAM,CAAC+D,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACD/B,KAAK,EAAGA,KAAU,IAAI;UACpBC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;UACtC,IAAI,CAAC2B,gBAAgB,CAAC,gCAAgC,CAAC;UACvD,IAAI,CAAChF,iBAAiB,EAAE;UACxB,IAAI,CAACqB,MAAM,CAAC+D,QAAQ,CAAC,CAAC,UAAU,EAAEL,SAAS,CAAC,CAAC;QAC/C;OACD,CAAC;;EAEN;EAEQC,gBAAgBA,CAACK,OAAe;IACtC;IACA,MAAMC,YAAY,GAAGf,QAAQ,CAACgB,aAAa,CAAC,KAAK,CAAC;IAClDD,YAAY,CAACE,WAAW,GAAGH,OAAO;IAClCC,YAAY,CAACG,KAAK,CAACC,OAAO,GAAG;;;;;;;;;;;;;KAa5B;IAEDnB,QAAQ,CAACoB,IAAI,CAACC,WAAW,CAACN,YAAY,CAAC;IAEvCO,UAAU,CAAC,MAAK;MACdP,YAAY,CAACQ,MAAM,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAvI,WAAWA,CAAA;IACT,IAAI,IAAI,CAACoB,WAAW,CAACkB,IAAI,EAAE,EAAE;MAC3ByD,OAAO,CAACyC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACpH,WAAW,CAAC;MAC9C,IAAI,CAACA,WAAW,GAAG,EAAE;;EAEzB;EAEAI,SAASA,CAAA;IACPuE,OAAO,CAACyC,GAAG,CAAC,YAAY,CAAC;EAC3B;EAEA9G,UAAUA,CAAA;IACRqE,OAAO,CAACyC,GAAG,CAAC,aAAa,CAAC;EAC5B;EAEAhI,UAAUA,CAAA;IACR,IAAI,CAACsD,MAAM,CAAC+D,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7B;EAEA7F,UAAUA,CAACyG,IAAU;IACnB,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAGF,GAAG,CAACG,OAAO,EAAE,GAAG,IAAIF,IAAI,CAACF,IAAI,CAAC,CAACI,OAAO,EAAE;IACrD,MAAMC,KAAK,GAAGpD,IAAI,CAACqD,KAAK,CAACH,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEjD,IAAIE,KAAK,GAAG,CAAC,EAAE,OAAO,KAAK;IAC3B,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,GAAGA,KAAK,GAAG;IAClC,MAAME,IAAI,GAAGtD,IAAI,CAACqD,KAAK,CAACD,KAAK,GAAG,EAAE,CAAC;IACnC,OAAO,GAAGE,IAAI,GAAG;EACnB;;;uBAtWWrF,oBAAoB,EAAAxH,EAAA,CAAA8M,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAhN,EAAA,CAAA8M,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAjN,EAAA,CAAA8M,iBAAA,CAAAI,EAAA,CAAAC,YAAA,GAAAnN,EAAA,CAAA8M,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAArN,EAAA,CAAA8M,iBAAA,CAAAQ,EAAA,CAAAC,eAAA,GAAAvN,EAAA,CAAA8M,iBAAA,CAAAU,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAApBjG,oBAAoB;MAAAkG,SAAA;MAAAC,YAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAApB7N,EAAA,CAAAW,UAAA,qBAAAoN,gDAAAtM,MAAA;YAAA,OAAAqM,GAAA,CAAAhF,mBAAA,CAAArH,MAAA,CAA2B;UAAA,UAAAzB,EAAA,CAAAgO,iBAAA,CAAP;;;;;;;;;;UA3nB7BhO,EApJA,CAAAwD,UAAA,IAAAyK,mCAAA,mBAA8E,IAAAC,mCAAA,kBAyHG,IAAAC,mCAAA,iBA2BhC;;;UApJtBnO,EAAA,CAAA2B,UAAA,SAAAmM,GAAA,CAAA9L,YAAA,CAAkB;UAyHjBhC,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAA2B,UAAA,SAAAmM,GAAA,CAAAnH,eAAA,CAAqB;UA2BjB3G,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAA2B,UAAA,SAAAmM,GAAA,CAAA9F,SAAA,CAAe;;;qBAtJvCnI,YAAY,EAAAuO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAEzO,WAAW,EAAA0O,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}