import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { CategoryOption } from '../../../core/services/advanced-search.service';

@Component({
  selector: 'app-category-selection',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="category-selection-container">
      <div class="category-selection-header">
        <h2>Choose Category for "{{ originalQuery }}"</h2>
        <p>We found multiple categories that match your search. Please select one:</p>
      </div>
      
      <div class="category-grid">
        <div 
          *ngFor="let category of categories" 
          class="category-card"
          (click)="selectCategory(category)"
          [attr.aria-label]="'Select ' + category.name"
          tabindex="0"
          (keydown.enter)="selectCategory(category)"
          (keydown.space)="selectCategory(category)">
          
          <div class="category-image">
            <img [src]="category.image" [alt]="category.name" loading="lazy">
            <div class="category-overlay">
              <div class="product-count">{{ category.productCount }} Products</div>
            </div>
          </div>
          
          <div class="category-info">
            <h3>{{ category.name }}</h3>
            <p>{{ category.description }}</p>
            <div class="category-meta">
              <span class="target-gender">{{ formatTargetGender(category.targetGender) }}</span>
              <span class="avg-price" *ngIf="category.avgPrice">
                Avg: ₹{{ category.avgPrice | number:'1.0-0' }}
              </span>
            </div>
          </div>
          
          <div class="category-action">
            <button class="select-btn" type="button">
              <i class="fas fa-arrow-right"></i>
              Browse
            </button>
          </div>
        </div>
      </div>
      
      <div class="alternative-actions">
        <button 
          class="alt-action-btn search-all-btn" 
          (click)="searchAllCategories()"
          type="button">
          <i class="fas fa-search"></i>
          Search All Categories
        </button>
        
        <button 
          class="alt-action-btn refine-search-btn" 
          (click)="refineSearch()"
          type="button">
          <i class="fas fa-edit"></i>
          Refine Search
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./category-selection.component.scss']
})
export class CategorySelectionComponent implements OnInit {
  @Input() categories: CategoryOption[] = [];
  @Input() originalQuery: string = '';
  
  @Output() categorySelected = new EventEmitter<CategoryOption>();
  @Output() searchAllRequested = new EventEmitter<string>();
  @Output() refineSearchRequested = new EventEmitter<string>();

  constructor(private router: Router) {}

  ngOnInit() {
    // Track category selection page view
    this.trackPageView();
  }

  selectCategory(category: CategoryOption): void {
    // Track category selection
    this.trackCategorySelection(category);
    
    // Emit category selection event
    this.categorySelected.emit(category);
    
    // Navigate to search results with category filter
    this.router.navigate(['/search'], {
      queryParams: {
        q: this.originalQuery,
        category: category.category,
        subcategory: category.subcategory,
        targetGender: category.targetGender
      }
    });
  }

  searchAllCategories(): void {
    // Track search all action
    this.trackSearchAllAction();
    
    // Emit search all event
    this.searchAllRequested.emit(this.originalQuery);
    
    // Navigate to search results without category filter
    this.router.navigate(['/search'], {
      queryParams: { q: this.originalQuery }
    });
  }

  refineSearch(): void {
    // Track refine search action
    this.trackRefineSearchAction();
    
    // Emit refine search event
    this.refineSearchRequested.emit(this.originalQuery);
    
    // Navigate back to search page with query pre-filled
    this.router.navigate(['/search'], {
      queryParams: { q: this.originalQuery, refine: 'true' }
    });
  }

  formatTargetGender(gender: string): string {
    switch (gender.toLowerCase()) {
      case 'men':
        return 'Men\'s Fashion';
      case 'women':
        return 'Women\'s Fashion';
      case 'unisex':
        return 'Unisex';
      case 'boys':
        return 'Boys\' Fashion';
      case 'girls':
        return 'Girls\' Fashion';
      default:
        return 'Fashion';
    }
  }

  private trackPageView(): void {
    // Analytics tracking for category selection page
    if (typeof (window as any).gtag !== 'undefined') {
      (window as any).gtag('event', 'page_view', {
        page_title: 'Category Selection',
        page_location: window.location.href,
        custom_parameters: {
          original_query: this.originalQuery,
          categories_shown: this.categories.length
        }
      });
    }
  }

  private trackCategorySelection(category: CategoryOption): void {
    // Analytics tracking for category selection
    if (typeof (window as any).gtag !== 'undefined') {
      (window as any).gtag('event', 'select_content', {
        content_type: 'category',
        content_id: category.id,
        custom_parameters: {
          category_name: category.name,
          target_gender: category.targetGender,
          product_count: category.productCount,
          original_query: this.originalQuery
        }
      });
    }
  }

  private trackSearchAllAction(): void {
    // Analytics tracking for search all action
    if (typeof (window as any).gtag !== 'undefined') {
      (window as any).gtag('event', 'search', {
        search_term: this.originalQuery,
        custom_parameters: {
          search_type: 'all_categories',
          from_category_selection: true
        }
      });
    }
  }

  private trackRefineSearchAction(): void {
    // Analytics tracking for refine search action
    if (typeof (window as any).gtag !== 'undefined') {
      (window as any).gtag('event', 'search', {
        search_term: this.originalQuery,
        custom_parameters: {
          search_type: 'refine',
          from_category_selection: true
        }
      });
    }
  }
}
