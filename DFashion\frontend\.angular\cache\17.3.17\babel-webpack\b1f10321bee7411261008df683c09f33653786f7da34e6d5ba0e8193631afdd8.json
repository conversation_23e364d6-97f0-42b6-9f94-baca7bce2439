{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/auth.service\";\nimport * as i2 from \"../../../core/services/cart.service\";\nimport * as i3 from \"../../../core/services/wishlist-new.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nfunction HeaderComponent_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.wishlistItemCount);\n  }\n}\nfunction HeaderComponent_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.cartItemCount);\n  }\n}\nfunction HeaderComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"span\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFormattedCartTotal());\n  }\n}\nfunction HeaderComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"i\", 14);\n    i0.ɵɵelementStart(2, \"span\", 30);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.getTotalItemCount());\n  }\n}\nfunction HeaderComponent_div_35_a_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 46);\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵtext(2, \" Vendor Dashboard \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HeaderComponent_div_35_a_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 48);\n    i0.ɵɵelement(1, \"i\", 49);\n    i0.ɵɵtext(2, \" Admin Panel \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HeaderComponent_div_35_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 40);\n  }\n}\nfunction HeaderComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_div_35_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleUserMenu());\n    });\n    i0.ɵɵelement(1, \"img\", 32);\n    i0.ɵɵelementStart(2, \"span\", 33);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 34);\n    i0.ɵɵelementStart(5, \"div\", 35)(6, \"a\", 36);\n    i0.ɵɵelement(7, \"i\", 37);\n    i0.ɵɵtext(8, \" Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"a\", 38);\n    i0.ɵɵelement(10, \"i\", 39);\n    i0.ɵɵtext(11, \" Settings \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"div\", 40);\n    i0.ɵɵtemplate(13, HeaderComponent_div_35_a_13_Template, 3, 0, \"a\", 41)(14, HeaderComponent_div_35_a_14_Template, 3, 0, \"a\", 42)(15, HeaderComponent_div_35_div_15_Template, 1, 0, \"div\", 43);\n    i0.ɵɵelementStart(16, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_div_35_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.logout());\n    });\n    i0.ɵɵelement(17, \"i\", 45);\n    i0.ɵɵtext(18, \" Logout \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.currentUser.avatar, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.currentUser.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.currentUser.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"show\", ctx_r0.showUserMenu);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentUser.role === \"vendor\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentUser.role === \"admin\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentUser.role !== \"customer\");\n  }\n}\nfunction HeaderComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"a\", 51);\n    i0.ɵɵtext(2, \"Login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 52);\n    i0.ɵɵtext(4, \"Sign Up\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class HeaderComponent {\n  constructor(authService, cartService, wishlistService, router) {\n    this.authService = authService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.currentUser = null;\n    this.searchQuery = '';\n    this.showUserMenu = false;\n    this.cartItemCount = 0;\n    this.wishlistItemCount = 0;\n    this.totalItemCount = 0;\n    this.cartTotalAmount = 0;\n    this.showCartTotalPrice = false;\n  }\n  ngOnInit() {\n    // Subscribe to user changes and refresh counts on login\n    this.authService.currentUser$.subscribe(user => {\n      const wasLoggedOut = !this.currentUser;\n      this.currentUser = user;\n      // If user just logged in, refresh cart and wishlist\n      if (user && wasLoggedOut) {\n        console.log('🔄 User logged in, refreshing cart and wishlist...');\n        setTimeout(() => {\n          this.cartService.refreshCartOnLogin();\n          this.wishlistService.refreshWishlistOnLogin();\n        }, 100);\n      }\n    });\n    // Subscribe to cart count\n    this.cartService.cartItemCount$.subscribe(count => {\n      this.cartItemCount = count;\n      this.updateTotalCount();\n      console.log('🛒 Header cart count updated:', count);\n    });\n    // Subscribe to wishlist count\n    this.wishlistService.wishlistItemCount$.subscribe(count => {\n      this.wishlistItemCount = count;\n      this.updateTotalCount();\n      console.log('💝 Header wishlist count updated:', count);\n    });\n    // Subscribe to cart total amount\n    this.cartService.cartTotalAmount$.subscribe(amount => {\n      this.cartTotalAmount = amount;\n      console.log('💰 Header cart total amount updated:', amount);\n    });\n    // Subscribe to cart price display flag\n    this.cartService.showCartTotalPrice$.subscribe(showPrice => {\n      this.showCartTotalPrice = showPrice;\n      console.log('💲 Header show cart total price updated:', showPrice);\n    });\n    // Load cart and wishlist on init\n    this.cartService.loadCart();\n    this.wishlistService.loadWishlist();\n    // Close dropdown when clicking outside\n    document.addEventListener('click', event => {\n      const target = event.target;\n      if (!target.closest('.user-menu')) {\n        this.showUserMenu = false;\n      }\n    });\n  }\n  toggleUserMenu() {\n    this.showUserMenu = !this.showUserMenu;\n  }\n  openSearch() {\n    this.router.navigate(['/search']);\n  }\n  // Update total count\n  updateTotalCount() {\n    this.totalItemCount = (this.cartItemCount || 0) + (this.wishlistItemCount || 0);\n  }\n  // Get total count for display\n  getTotalItemCount() {\n    return this.totalItemCount;\n  }\n  // Get formatted cart total amount\n  getFormattedCartTotal() {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(this.cartTotalAmount || 0);\n  }\n  // Check if cart total price should be displayed\n  shouldShowCartTotalPrice() {\n    return this.currentUser !== null && this.showCartTotalPrice;\n  }\n  onSearch() {\n    if (this.searchQuery.trim()) {\n      this.router.navigate(['/search'], {\n        queryParams: {\n          q: this.searchQuery\n        }\n      });\n      this.searchQuery = ''; // Clear search after navigation\n    } else {\n      this.router.navigate(['/search']);\n    }\n  }\n  logout() {\n    this.authService.logout();\n    this.showUserMenu = false;\n  }\n  static {\n    this.ɵfac = function HeaderComponent_Factory(t) {\n      return new (t || HeaderComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.CartService), i0.ɵɵdirectiveInject(i3.WishlistNewService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HeaderComponent,\n      selectors: [[\"app-header\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 37,\n      vars: 7,\n      consts: [[1, \"header\"], [1, \"container\"], [1, \"header-content\"], [1, \"logo\"], [\"routerLink\", \"/home\"], [1, \"gradient-text\"], [1, \"search-bar\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"type\", \"text\", \"placeholder\", \"Search for fashion, brands, and more...\", \"readonly\", \"\", 3, \"ngModelChange\", \"keyup.enter\", \"click\", \"ngModel\"], [1, \"nav-menu\"], [\"routerLink\", \"/home\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [1, \"fas\", \"fa-home\"], [\"routerLink\", \"/shop\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [1, \"fas\", \"fa-compass\"], [1, \"fas\", \"fa-shopping-bag\"], [\"routerLink\", \"/wishlist\", \"routerLinkActive\", \"active\", 1, \"nav-item\", \"wishlist-item\"], [1, \"fas\", \"fa-heart\"], [\"class\", \"wishlist-badge\", 4, \"ngIf\"], [\"routerLink\", \"/cart\", \"routerLinkActive\", \"active\", 1, \"nav-item\", \"cart-item\"], [1, \"fas\", \"fa-shopping-cart\"], [\"class\", \"cart-badge\", 4, \"ngIf\"], [\"class\", \"cart-total-display\", 4, \"ngIf\"], [\"class\", \"total-count-display\", 4, \"ngIf\"], [\"class\", \"user-menu\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"auth-buttons\", 4, \"ngIf\"], [1, \"wishlist-badge\"], [1, \"cart-badge\"], [1, \"cart-total-display\"], [1, \"cart-total-text\"], [1, \"total-count-display\"], [1, \"total-count-text\"], [1, \"user-menu\", 3, \"click\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"username\"], [1, \"fas\", \"fa-chevron-down\"], [1, \"dropdown-menu\"], [\"routerLink\", \"/profile\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-user\"], [\"routerLink\", \"/settings\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-cog\"], [1, \"dropdown-divider\"], [\"routerLink\", \"/vendor/dashboard\", \"class\", \"dropdown-item\", 4, \"ngIf\"], [\"routerLink\", \"/admin\", \"class\", \"dropdown-item\", 4, \"ngIf\"], [\"class\", \"dropdown-divider\", 4, \"ngIf\"], [1, \"dropdown-item\", \"logout\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"], [\"routerLink\", \"/vendor/dashboard\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-store\"], [\"routerLink\", \"/admin\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-shield-alt\"], [1, \"auth-buttons\"], [\"routerLink\", \"/auth/login\", 1, \"btn\", \"btn-outline\"], [\"routerLink\", \"/auth/register\", 1, \"btn\", \"btn-primary\"]],\n      template: function HeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"header\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4)(5, \"h1\", 5);\n          i0.ɵɵtext(6, \"DFashion\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_div_click_7_listener() {\n            return ctx.openSearch();\n          });\n          i0.ɵɵelement(8, \"i\", 7);\n          i0.ɵɵelementStart(9, \"input\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HeaderComponent_Template_input_ngModelChange_9_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function HeaderComponent_Template_input_keyup_enter_9_listener() {\n            return ctx.onSearch();\n          })(\"click\", function HeaderComponent_Template_input_click_9_listener() {\n            return ctx.openSearch();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"nav\", 9)(11, \"a\", 10);\n          i0.ɵɵelement(12, \"i\", 11);\n          i0.ɵɵelementStart(13, \"span\");\n          i0.ɵɵtext(14, \"Home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"a\", 12);\n          i0.ɵɵelement(16, \"i\", 13);\n          i0.ɵɵelementStart(17, \"span\");\n          i0.ɵɵtext(18, \"Explore\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"a\", 12);\n          i0.ɵɵelement(20, \"i\", 14);\n          i0.ɵɵelementStart(21, \"span\");\n          i0.ɵɵtext(22, \"Shop\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"a\", 15);\n          i0.ɵɵelement(24, \"i\", 16);\n          i0.ɵɵelementStart(25, \"span\");\n          i0.ɵɵtext(26, \"Wishlist\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, HeaderComponent_span_27_Template, 2, 1, \"span\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"a\", 18);\n          i0.ɵɵelement(29, \"i\", 19);\n          i0.ɵɵelementStart(30, \"span\");\n          i0.ɵɵtext(31, \"Cart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, HeaderComponent_span_32_Template, 2, 1, \"span\", 20)(33, HeaderComponent_div_33_Template, 3, 1, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(34, HeaderComponent_div_34_Template, 4, 1, \"div\", 22)(35, HeaderComponent_div_35_Template, 19, 8, \"div\", 23)(36, HeaderComponent_div_36_Template, 5, 0, \"div\", 24);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngIf\", ctx.wishlistItemCount > 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.cartItemCount > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.shouldShowCartTotalPrice());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser && ctx.getTotalItemCount() > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.currentUser);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, RouterModule, i4.RouterLink, i4.RouterLinkActive, FormsModule, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel],\n      styles: [\".header[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-bottom: 1px solid #dbdbdb;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 1000;\\n  height: 60px;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  height: 60px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n\\n.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  margin: 0;\\n}\\n\\n.search-bar[_ngcontent-%COMP%] {\\n  position: relative;\\n  flex: 1;\\n  max-width: 400px;\\n  margin: 0 40px;\\n}\\n\\n.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 8px 16px 8px 40px;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  background: #fafafa;\\n  font-size: 14px;\\n  outline: none;\\n  transition: all 0.2s;\\n}\\n\\n.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  background: #fff;\\n  border-color: var(--primary-color);\\n}\\n\\n.search-bar[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 12px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: #8e8e8e;\\n}\\n\\n.nav-menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 24px;\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-decoration: none;\\n  color: #262626;\\n  font-size: 12px;\\n  transition: color 0.2s;\\n  padding: 8px;\\n  border-radius: 4px;\\n}\\n\\n.nav-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin-bottom: 4px;\\n}\\n\\n.nav-item.active[_ngcontent-%COMP%], .nav-item[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n}\\n\\n.cart-item[_ngcontent-%COMP%], .wishlist-item[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.cart-badge[_ngcontent-%COMP%], .wishlist-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  right: -2px;\\n  background: #ef4444;\\n  color: white;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  min-width: 16px;\\n  text-align: center;\\n  line-height: 1.2;\\n}\\n\\n.cart-total-display[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  right: 0;\\n  background: #28a745;\\n  color: white;\\n  font-size: 9px;\\n  font-weight: 600;\\n  padding: 2px 4px;\\n  border-radius: 4px;\\n  white-space: nowrap;\\n  margin-top: 2px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.cart-total-text[_ngcontent-%COMP%] {\\n  font-size: 9px;\\n}\\n\\n.total-count-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  padding: 4px 8px;\\n  background: linear-gradient(135deg, #4834d4, #686de0);\\n  color: white;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  margin-left: 8px;\\n}\\n\\n.total-count-display[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n.total-count-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  min-width: 16px;\\n  text-align: center;\\n}\\n\\n.auth-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  text-decoration: none;\\n  font-size: 14px;\\n  font-weight: 500;\\n  transition: all 0.2s;\\n  border: 1px solid transparent;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  border-color: var(--primary-color);\\n  background: transparent;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-dark);\\n}\\n\\n.user-menu[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 8px;\\n  transition: background 0.2s;\\n}\\n\\n.user-menu[_ngcontent-%COMP%]:hover {\\n  background: #f1f5f9;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 14px;\\n}\\n\\n.user-menu[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #64748b;\\n  transition: transform 0.2s;\\n}\\n\\n.user-menu.active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n.dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  right: 0;\\n  background: #fff;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  min-width: 200px;\\n  opacity: 0;\\n  visibility: hidden;\\n  transform: translateY(-10px);\\n  transition: all 0.2s;\\n  z-index: 1000;\\n}\\n\\n.dropdown-menu.show[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n  transform: translateY(0);\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px 16px;\\n  text-decoration: none;\\n  color: #262626;\\n  font-size: 14px;\\n  transition: background 0.2s;\\n  border: none;\\n  background: none;\\n  width: 100%;\\n  text-align: left;\\n  cursor: pointer;\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%]:hover {\\n  background: #f8fafc;\\n}\\n\\n.dropdown-item.logout[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n}\\n\\n.dropdown-item.logout[_ngcontent-%COMP%]:hover {\\n  background: #fef2f2;\\n}\\n\\n.dropdown-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: #e2e8f0;\\n  margin: 8px 0;\\n}\\n\\n@media (max-width: 768px) {\\n  .search-bar[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .nav-menu[_ngcontent-%COMP%] {\\n    gap: 16px;\\n  }\\n  .nav-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .username[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "wishlistItemCount", "cartItemCount", "getFormattedCartTotal", "ɵɵelement", "getTotalItemCount", "ɵɵlistener", "HeaderComponent_div_35_Template_div_click_0_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "toggleUserMenu", "ɵɵtemplate", "HeaderComponent_div_35_a_13_Template", "HeaderComponent_div_35_a_14_Template", "HeaderComponent_div_35_div_15_Template", "HeaderComponent_div_35_Template_button_click_16_listener", "logout", "ɵɵproperty", "currentUser", "avatar", "ɵɵsanitizeUrl", "fullName", "username", "ɵɵclassProp", "showUserMenu", "role", "HeaderComponent", "constructor", "authService", "cartService", "wishlistService", "router", "searchQuery", "totalItemCount", "cartTotalAmount", "showCartTotalPrice", "ngOnInit", "currentUser$", "subscribe", "user", "wasLoggedOut", "console", "log", "setTimeout", "refreshCartOnLogin", "refreshWishlistOnLogin", "cartItemCount$", "count", "updateTotalCount", "wishlistItemCount$", "cartTotalAmount$", "amount", "showCartTotalPrice$", "showPrice", "loadCart", "loadWishlist", "document", "addEventListener", "event", "target", "closest", "openSearch", "navigate", "Intl", "NumberFormat", "style", "currency", "format", "shouldShowCartTotalPrice", "onSearch", "trim", "queryParams", "q", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "CartService", "i3", "WishlistNewService", "i4", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HeaderComponent_Template", "rf", "ctx", "HeaderComponent_Template_div_click_7_listener", "ɵɵtwoWayListener", "HeaderComponent_Template_input_ngModelChange_9_listener", "$event", "ɵɵtwoWayBindingSet", "HeaderComponent_Template_input_keyup_enter_9_listener", "HeaderComponent_Template_input_click_9_listener", "HeaderComponent_span_27_Template", "HeaderComponent_span_32_Template", "HeaderComponent_div_33_Template", "HeaderComponent_div_34_Template", "HeaderComponent_div_35_Template", "HeaderComponent_div_36_Template", "ɵɵtwoWayProperty", "i5", "NgIf", "RouterLink", "RouterLinkActive", "i6", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\shared\\components\\header\\header.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Router } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\n\nimport { AuthService } from '../../../core/services/auth.service';\nimport { CartService } from '../../../core/services/cart.service';\nimport { WishlistNewService } from '../../../core/services/wishlist-new.service';\nimport { User } from '../../../core/models/user.model';\n\n@Component({\n  selector: 'app-header',\n  standalone: true,\n  imports: [CommonModule, RouterModule, FormsModule],\n  template: `\n    <header class=\"header\">\n      <div class=\"container\">\n        <div class=\"header-content\">\n          <!-- Logo -->\n          <div class=\"logo\">\n            <a routerLink=\"/home\">\n              <h1 class=\"gradient-text\">DFashion</h1>\n            </a>\n          </div>\n\n          <!-- Search Bar -->\n          <div class=\"search-bar\" (click)=\"openSearch()\">\n            <i class=\"fas fa-search\"></i>\n            <input\n              type=\"text\"\n              placeholder=\"Search for fashion, brands, and more...\"\n              [(ngModel)]=\"searchQuery\"\n              (keyup.enter)=\"onSearch()\"\n              (click)=\"openSearch()\"\n              readonly\n            >\n          </div>\n\n          <!-- Navigation -->\n          <nav class=\"nav-menu\">\n            <a routerLink=\"/home\" routerLinkActive=\"active\" class=\"nav-item\">\n              <i class=\"fas fa-home\"></i>\n              <span>Home</span>\n            </a>\n            <a routerLink=\"/shop\" routerLinkActive=\"active\" class=\"nav-item\">\n              <i class=\"fas fa-compass\"></i>\n              <span>Explore</span>\n            </a>\n            <a routerLink=\"/shop\" routerLinkActive=\"active\" class=\"nav-item\">\n              <i class=\"fas fa-shopping-bag\"></i>\n              <span>Shop</span>\n            </a>\n            <a routerLink=\"/wishlist\" routerLinkActive=\"active\" class=\"nav-item wishlist-item\">\n              <i class=\"fas fa-heart\"></i>\n              <span>Wishlist</span>\n              <span class=\"wishlist-badge\" *ngIf=\"wishlistItemCount > 0\">{{ wishlistItemCount }}</span>\n            </a>\n            <a routerLink=\"/cart\" routerLinkActive=\"active\" class=\"nav-item cart-item\">\n              <i class=\"fas fa-shopping-cart\"></i>\n              <span>Cart</span>\n              <span class=\"cart-badge\" *ngIf=\"cartItemCount > 0\">{{ cartItemCount }}</span>\n              <!-- Cart total price display when cart has 4+ items -->\n              <div class=\"cart-total-display\" *ngIf=\"shouldShowCartTotalPrice()\">\n                <span class=\"cart-total-text\">{{ getFormattedCartTotal() }}</span>\n              </div>\n            </a>\n\n            <!-- Combined total count display -->\n            <div class=\"total-count-display\" *ngIf=\"currentUser && getTotalItemCount() > 0\">\n              <i class=\"fas fa-shopping-bag\"></i>\n              <span class=\"total-count-text\">{{ getTotalItemCount() }}</span>\n            </div>\n\n            <!-- User Menu for logged in users -->\n            <div *ngIf=\"currentUser\" class=\"user-menu\" (click)=\"toggleUserMenu()\">\n              <img [src]=\"currentUser.avatar\" [alt]=\"currentUser.fullName\" class=\"user-avatar\">\n              <span class=\"username\">{{ currentUser.username }}</span>\n              <i class=\"fas fa-chevron-down\"></i>\n              \n              <!-- Dropdown Menu -->\n              <div class=\"dropdown-menu\" [class.show]=\"showUserMenu\">\n                <a routerLink=\"/profile\" class=\"dropdown-item\">\n                  <i class=\"fas fa-user\"></i>\n                  Profile\n                </a>\n                <a routerLink=\"/settings\" class=\"dropdown-item\">\n                  <i class=\"fas fa-cog\"></i>\n                  Settings\n                </a>\n                <div class=\"dropdown-divider\"></div>\n                <a *ngIf=\"currentUser.role === 'vendor'\" routerLink=\"/vendor/dashboard\" class=\"dropdown-item\">\n                  <i class=\"fas fa-store\"></i>\n                  Vendor Dashboard\n                </a>\n                <a *ngIf=\"currentUser.role === 'admin'\" routerLink=\"/admin\" class=\"dropdown-item\">\n                  <i class=\"fas fa-shield-alt\"></i>\n                  Admin Panel\n                </a>\n                <div class=\"dropdown-divider\" *ngIf=\"currentUser.role !== 'customer'\"></div>\n                <button (click)=\"logout()\" class=\"dropdown-item logout\">\n                  <i class=\"fas fa-sign-out-alt\"></i>\n                  Logout\n                </button>\n              </div>\n            </div>\n\n            <!-- Login/Register for guest users -->\n            <div *ngIf=\"!currentUser\" class=\"auth-buttons\">\n              <a routerLink=\"/auth/login\" class=\"btn btn-outline\">Login</a>\n              <a routerLink=\"/auth/register\" class=\"btn btn-primary\">Sign Up</a>\n            </div>\n          </nav>\n        </div>\n      </div>\n    </header>\n  `,\n  styles: [`\n    .header {\n      background: #fff;\n      border-bottom: 1px solid #dbdbdb;\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      z-index: 1000;\n      height: 60px;\n    }\n\n    .header-content {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      height: 60px;\n    }\n\n    .logo a {\n      text-decoration: none;\n    }\n\n    .logo h1 {\n      font-size: 24px;\n      font-weight: 700;\n      margin: 0;\n    }\n\n    .search-bar {\n      position: relative;\n      flex: 1;\n      max-width: 400px;\n      margin: 0 40px;\n    }\n\n    .search-bar input {\n      width: 100%;\n      padding: 8px 16px 8px 40px;\n      border: 1px solid #dbdbdb;\n      border-radius: 8px;\n      background: #fafafa;\n      font-size: 14px;\n      outline: none;\n      transition: all 0.2s;\n    }\n\n    .search-bar input:focus {\n      background: #fff;\n      border-color: var(--primary-color);\n    }\n\n    .search-bar i {\n      position: absolute;\n      left: 12px;\n      top: 50%;\n      transform: translateY(-50%);\n      color: #8e8e8e;\n    }\n\n    .nav-menu {\n      display: flex;\n      align-items: center;\n      gap: 24px;\n    }\n\n    .nav-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      text-decoration: none;\n      color: #262626;\n      font-size: 12px;\n      transition: color 0.2s;\n      padding: 8px;\n      border-radius: 4px;\n    }\n\n    .nav-item i {\n      font-size: 20px;\n      margin-bottom: 4px;\n    }\n\n    .nav-item.active,\n    .nav-item:hover {\n      color: var(--primary-color);\n    }\n\n    .cart-item,\n    .wishlist-item {\n      position: relative;\n    }\n\n    .cart-badge,\n    .wishlist-badge {\n      position: absolute;\n      top: -2px;\n      right: -2px;\n      background: #ef4444;\n      color: white;\n      font-size: 10px;\n      font-weight: 600;\n      padding: 2px 6px;\n      border-radius: 10px;\n      min-width: 16px;\n      text-align: center;\n      line-height: 1.2;\n    }\n\n    .cart-total-display {\n      position: absolute;\n      top: 100%;\n      right: 0;\n      background: #28a745;\n      color: white;\n      font-size: 9px;\n      font-weight: 600;\n      padding: 2px 4px;\n      border-radius: 4px;\n      white-space: nowrap;\n      margin-top: 2px;\n      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n    }\n\n    .cart-total-text {\n      font-size: 9px;\n    }\n\n    .total-count-display {\n      display: flex;\n      align-items: center;\n      gap: 4px;\n      padding: 4px 8px;\n      background: linear-gradient(135deg, #4834d4, #686de0);\n      color: white;\n      border-radius: 12px;\n      font-size: 12px;\n      font-weight: 600;\n      margin-left: 8px;\n    }\n\n    .total-count-display i {\n      font-size: 12px;\n    }\n\n    .total-count-text {\n      font-size: 12px;\n      min-width: 16px;\n      text-align: center;\n    }\n\n    .auth-buttons {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .btn {\n      padding: 8px 16px;\n      border-radius: 6px;\n      text-decoration: none;\n      font-size: 14px;\n      font-weight: 500;\n      transition: all 0.2s;\n      border: 1px solid transparent;\n    }\n\n    .btn-outline {\n      color: var(--primary-color);\n      border-color: var(--primary-color);\n      background: transparent;\n    }\n\n    .btn-outline:hover {\n      background: var(--primary-color);\n      color: white;\n    }\n\n    .btn-primary {\n      background: var(--primary-color);\n      color: white;\n    }\n\n    .btn-primary:hover {\n      background: var(--primary-dark);\n    }\n\n    .user-menu {\n      position: relative;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      cursor: pointer;\n      padding: 8px 12px;\n      border-radius: 8px;\n      transition: background 0.2s;\n    }\n\n    .user-menu:hover {\n      background: #f1f5f9;\n    }\n\n    .user-avatar {\n      width: 32px;\n      height: 32px;\n      border-radius: 50%;\n      object-fit: cover;\n    }\n\n    .username {\n      font-weight: 500;\n      font-size: 14px;\n    }\n\n    .user-menu i {\n      font-size: 12px;\n      color: #64748b;\n      transition: transform 0.2s;\n    }\n\n    .user-menu.active i {\n      transform: rotate(180deg);\n    }\n\n    .dropdown-menu {\n      position: absolute;\n      top: 100%;\n      right: 0;\n      background: #fff;\n      border: 1px solid #e2e8f0;\n      border-radius: 8px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      min-width: 200px;\n      opacity: 0;\n      visibility: hidden;\n      transform: translateY(-10px);\n      transition: all 0.2s;\n      z-index: 1000;\n    }\n\n    .dropdown-menu.show {\n      opacity: 1;\n      visibility: visible;\n      transform: translateY(0);\n    }\n\n    .dropdown-item {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      padding: 12px 16px;\n      text-decoration: none;\n      color: #262626;\n      font-size: 14px;\n      transition: background 0.2s;\n      border: none;\n      background: none;\n      width: 100%;\n      text-align: left;\n      cursor: pointer;\n    }\n\n    .dropdown-item:hover {\n      background: #f8fafc;\n    }\n\n    .dropdown-item.logout {\n      color: #ef4444;\n    }\n\n    .dropdown-item.logout:hover {\n      background: #fef2f2;\n    }\n\n    .dropdown-divider {\n      height: 1px;\n      background: #e2e8f0;\n      margin: 8px 0;\n    }\n\n    @media (max-width: 768px) {\n      .search-bar {\n        display: none;\n      }\n      \n      .nav-menu {\n        gap: 16px;\n      }\n      \n      .nav-item span {\n        display: none;\n      }\n\n      .username {\n        display: none;\n      }\n    }\n  `]\n})\nexport class HeaderComponent implements OnInit {\n  currentUser: User | null = null;\n  searchQuery = '';\n  showUserMenu = false;\n  cartItemCount = 0;\n  wishlistItemCount = 0;\n  totalItemCount = 0;\n  cartTotalAmount = 0;\n  showCartTotalPrice = false;\n\n  constructor(\n    private authService: AuthService,\n    private cartService: CartService,\n    private wishlistService: WishlistNewService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    // Subscribe to user changes and refresh counts on login\n    this.authService.currentUser$.subscribe(user => {\n      const wasLoggedOut = !this.currentUser;\n      this.currentUser = user;\n\n      // If user just logged in, refresh cart and wishlist\n      if (user && wasLoggedOut) {\n        console.log('🔄 User logged in, refreshing cart and wishlist...');\n        setTimeout(() => {\n          this.cartService.refreshCartOnLogin();\n          this.wishlistService.refreshWishlistOnLogin();\n        }, 100);\n      }\n    });\n\n    // Subscribe to cart count\n    this.cartService.cartItemCount$.subscribe((count: number) => {\n      this.cartItemCount = count;\n      this.updateTotalCount();\n      console.log('🛒 Header cart count updated:', count);\n    });\n\n    // Subscribe to wishlist count\n    this.wishlistService.wishlistItemCount$.subscribe((count: number) => {\n      this.wishlistItemCount = count;\n      this.updateTotalCount();\n      console.log('💝 Header wishlist count updated:', count);\n    });\n\n    // Subscribe to cart total amount\n    this.cartService.cartTotalAmount$.subscribe((amount: number) => {\n      this.cartTotalAmount = amount;\n      console.log('💰 Header cart total amount updated:', amount);\n    });\n\n    // Subscribe to cart price display flag\n    this.cartService.showCartTotalPrice$.subscribe((showPrice: boolean) => {\n      this.showCartTotalPrice = showPrice;\n      console.log('💲 Header show cart total price updated:', showPrice);\n    });\n\n    // Load cart and wishlist on init\n    this.cartService.loadCart();\n    this.wishlistService.loadWishlist();\n\n    // Close dropdown when clicking outside\n    document.addEventListener('click', (event) => {\n      const target = event.target as HTMLElement;\n      if (!target.closest('.user-menu')) {\n        this.showUserMenu = false;\n      }\n    });\n  }\n\n  toggleUserMenu() {\n    this.showUserMenu = !this.showUserMenu;\n  }\n\n  openSearch() {\n    this.router.navigate(['/search']);\n  }\n\n  // Update total count\n  private updateTotalCount() {\n    this.totalItemCount = (this.cartItemCount || 0) + (this.wishlistItemCount || 0);\n  }\n\n  // Get total count for display\n  getTotalItemCount(): number {\n    return this.totalItemCount;\n  }\n\n  // Get formatted cart total amount\n  getFormattedCartTotal(): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(this.cartTotalAmount || 0);\n  }\n\n  // Check if cart total price should be displayed\n  shouldShowCartTotalPrice(): boolean {\n    return this.currentUser !== null && this.showCartTotalPrice;\n  }\n\n  onSearch() {\n    if (this.searchQuery.trim()) {\n      this.router.navigate(['/search'], {\n        queryParams: { q: this.searchQuery }\n      });\n      this.searchQuery = ''; // Clear search after navigation\n    } else {\n      this.router.navigate(['/search']);\n    }\n  }\n\n  logout() {\n    this.authService.logout();\n    this.showUserMenu = false;\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;IAoD9BC,EAAA,CAAAC,cAAA,eAA2D;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA9BH,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,iBAAA,CAAuB;;;;;IAKlFP,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAE,aAAA,CAAmB;;;;;IAGpER,EADF,CAAAC,cAAA,cAAmE,eACnC;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;;;;IAD0BH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAG,qBAAA,GAA6B;;;;;IAK/DT,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAU,SAAA,YAAmC;IACnCV,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;;;;IAD2BH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAK,iBAAA,GAAyB;;;;;IAoBtDX,EAAA,CAAAC,cAAA,YAA8F;IAC5FD,EAAA,CAAAU,SAAA,YAA4B;IAC5BV,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IACJH,EAAA,CAAAC,cAAA,YAAkF;IAChFD,EAAA,CAAAU,SAAA,YAAiC;IACjCV,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IACJH,EAAA,CAAAU,SAAA,cAA4E;;;;;;IAxBhFV,EAAA,CAAAC,cAAA,cAAsE;IAA3BD,EAAA,CAAAY,UAAA,mBAAAC,qDAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASX,MAAA,CAAAY,cAAA,EAAgB;IAAA,EAAC;IACnElB,EAAA,CAAAU,SAAA,cAAiF;IACjFV,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxDH,EAAA,CAAAU,SAAA,YAAmC;IAIjCV,EADF,CAAAC,cAAA,cAAuD,YACN;IAC7CD,EAAA,CAAAU,SAAA,YAA2B;IAC3BV,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,YAAgD;IAC9CD,EAAA,CAAAU,SAAA,aAA0B;IAC1BV,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAU,SAAA,eAAoC;IASpCV,EARA,CAAAmB,UAAA,KAAAC,oCAAA,gBAA8F,KAAAC,oCAAA,gBAIZ,KAAAC,sCAAA,kBAIZ;IACtEtB,EAAA,CAAAC,cAAA,kBAAwD;IAAhDD,EAAA,CAAAY,UAAA,mBAAAW,yDAAA;MAAAvB,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASX,MAAA,CAAAkB,MAAA,EAAQ;IAAA,EAAC;IACxBxB,EAAA,CAAAU,SAAA,aAAmC;IACnCV,EAAA,CAAAE,MAAA,gBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IA7BCH,EAAA,CAAAI,SAAA,EAA0B;IAACJ,EAA3B,CAAAyB,UAAA,QAAAnB,MAAA,CAAAoB,WAAA,CAAAC,MAAA,EAAA3B,EAAA,CAAA4B,aAAA,CAA0B,QAAAtB,MAAA,CAAAoB,WAAA,CAAAG,QAAA,CAA6B;IACrC7B,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAoB,WAAA,CAAAI,QAAA,CAA0B;IAItB9B,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAA+B,WAAA,SAAAzB,MAAA,CAAA0B,YAAA,CAA2B;IAUhDhC,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAoB,WAAA,CAAAO,IAAA,cAAmC;IAInCjC,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAoB,WAAA,CAAAO,IAAA,aAAkC;IAIPjC,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAoB,WAAA,CAAAO,IAAA,gBAAqC;;;;;IAUtEjC,EADF,CAAAC,cAAA,cAA+C,YACO;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC7DH,EAAA,CAAAC,cAAA,YAAuD;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAChEF,EADgE,CAAAG,YAAA,EAAI,EAC9D;;;AAiTlB,OAAM,MAAO+B,eAAe;EAU1BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,eAAmC,EACnCC,MAAc;IAHd,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAbhB,KAAAb,WAAW,GAAgB,IAAI;IAC/B,KAAAc,WAAW,GAAG,EAAE;IAChB,KAAAR,YAAY,GAAG,KAAK;IACpB,KAAAxB,aAAa,GAAG,CAAC;IACjB,KAAAD,iBAAiB,GAAG,CAAC;IACrB,KAAAkC,cAAc,GAAG,CAAC;IAClB,KAAAC,eAAe,GAAG,CAAC;IACnB,KAAAC,kBAAkB,GAAG,KAAK;EAOvB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACR,WAAW,CAACS,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,MAAMC,YAAY,GAAG,CAAC,IAAI,CAACtB,WAAW;MACtC,IAAI,CAACA,WAAW,GAAGqB,IAAI;MAEvB;MACA,IAAIA,IAAI,IAAIC,YAAY,EAAE;QACxBC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjEC,UAAU,CAAC,MAAK;UACd,IAAI,CAACd,WAAW,CAACe,kBAAkB,EAAE;UACrC,IAAI,CAACd,eAAe,CAACe,sBAAsB,EAAE;QAC/C,CAAC,EAAE,GAAG,CAAC;;IAEX,CAAC,CAAC;IAEF;IACA,IAAI,CAAChB,WAAW,CAACiB,cAAc,CAACR,SAAS,CAAES,KAAa,IAAI;MAC1D,IAAI,CAAC/C,aAAa,GAAG+C,KAAK;MAC1B,IAAI,CAACC,gBAAgB,EAAE;MACvBP,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEK,KAAK,CAAC;IACrD,CAAC,CAAC;IAEF;IACA,IAAI,CAACjB,eAAe,CAACmB,kBAAkB,CAACX,SAAS,CAAES,KAAa,IAAI;MAClE,IAAI,CAAChD,iBAAiB,GAAGgD,KAAK;MAC9B,IAAI,CAACC,gBAAgB,EAAE;MACvBP,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEK,KAAK,CAAC;IACzD,CAAC,CAAC;IAEF;IACA,IAAI,CAAClB,WAAW,CAACqB,gBAAgB,CAACZ,SAAS,CAAEa,MAAc,IAAI;MAC7D,IAAI,CAACjB,eAAe,GAAGiB,MAAM;MAC7BV,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAES,MAAM,CAAC;IAC7D,CAAC,CAAC;IAEF;IACA,IAAI,CAACtB,WAAW,CAACuB,mBAAmB,CAACd,SAAS,CAAEe,SAAkB,IAAI;MACpE,IAAI,CAAClB,kBAAkB,GAAGkB,SAAS;MACnCZ,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEW,SAAS,CAAC;IACpE,CAAC,CAAC;IAEF;IACA,IAAI,CAACxB,WAAW,CAACyB,QAAQ,EAAE;IAC3B,IAAI,CAACxB,eAAe,CAACyB,YAAY,EAAE;IAEnC;IACAC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAI;MAC3C,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAqB;MAC1C,IAAI,CAACA,MAAM,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;QACjC,IAAI,CAACpC,YAAY,GAAG,KAAK;;IAE7B,CAAC,CAAC;EACJ;EAEAd,cAAcA,CAAA;IACZ,IAAI,CAACc,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAqC,UAAUA,CAAA;IACR,IAAI,CAAC9B,MAAM,CAAC+B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAEA;EACQd,gBAAgBA,CAAA;IACtB,IAAI,CAACf,cAAc,GAAG,CAAC,IAAI,CAACjC,aAAa,IAAI,CAAC,KAAK,IAAI,CAACD,iBAAiB,IAAI,CAAC,CAAC;EACjF;EAEA;EACAI,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC8B,cAAc;EAC5B;EAEA;EACAhC,qBAAqBA,CAAA;IACnB,OAAO,IAAI8D,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAAC,IAAI,CAACjC,eAAe,IAAI,CAAC,CAAC;EACtC;EAEA;EACAkC,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAAClD,WAAW,KAAK,IAAI,IAAI,IAAI,CAACiB,kBAAkB;EAC7D;EAEAkC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACrC,WAAW,CAACsC,IAAI,EAAE,EAAE;MAC3B,IAAI,CAACvC,MAAM,CAAC+B,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;QAChCS,WAAW,EAAE;UAAEC,CAAC,EAAE,IAAI,CAACxC;QAAW;OACnC,CAAC;MACF,IAAI,CAACA,WAAW,GAAG,EAAE,CAAC,CAAC;KACxB,MAAM;MACL,IAAI,CAACD,MAAM,CAAC+B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;;EAErC;EAEA9C,MAAMA,CAAA;IACJ,IAAI,CAACY,WAAW,CAACZ,MAAM,EAAE;IACzB,IAAI,CAACQ,YAAY,GAAG,KAAK;EAC3B;;;uBArHWE,eAAe,EAAAlC,EAAA,CAAAiF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnF,EAAA,CAAAiF,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAArF,EAAA,CAAAiF,iBAAA,CAAAK,EAAA,CAAAC,kBAAA,GAAAvF,EAAA,CAAAiF,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfvD,eAAe;MAAAwD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA5F,EAAA,CAAA6F,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1YdnG,EANV,CAAAC,cAAA,gBAAuB,aACE,aACO,aAER,WACM,YACM;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACrC,EACA;UAGNH,EAAA,CAAAC,cAAA,aAA+C;UAAvBD,EAAA,CAAAY,UAAA,mBAAAyF,8CAAA;YAAA,OAASD,GAAA,CAAA/B,UAAA,EAAY;UAAA,EAAC;UAC5CrE,EAAA,CAAAU,SAAA,WAA6B;UAC7BV,EAAA,CAAAC,cAAA,eAOC;UAJCD,EAAA,CAAAsG,gBAAA,2BAAAC,wDAAAC,MAAA;YAAAxG,EAAA,CAAAyG,kBAAA,CAAAL,GAAA,CAAA5D,WAAA,EAAAgE,MAAA,MAAAJ,GAAA,CAAA5D,WAAA,GAAAgE,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAEzBxG,EADA,CAAAY,UAAA,yBAAA8F,sDAAA;YAAA,OAAeN,GAAA,CAAAvB,QAAA,EAAU;UAAA,EAAC,mBAAA8B,gDAAA;YAAA,OACjBP,GAAA,CAAA/B,UAAA,EAAY;UAAA,EAAC;UAG1BrE,EARE,CAAAG,YAAA,EAOC,EACG;UAIJH,EADF,CAAAC,cAAA,cAAsB,aAC6C;UAC/DD,EAAA,CAAAU,SAAA,aAA2B;UAC3BV,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,YAAI;UACZF,EADY,CAAAG,YAAA,EAAO,EACf;UACJH,EAAA,CAAAC,cAAA,aAAiE;UAC/DD,EAAA,CAAAU,SAAA,aAA8B;UAC9BV,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,eAAO;UACfF,EADe,CAAAG,YAAA,EAAO,EAClB;UACJH,EAAA,CAAAC,cAAA,aAAiE;UAC/DD,EAAA,CAAAU,SAAA,aAAmC;UACnCV,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,YAAI;UACZF,EADY,CAAAG,YAAA,EAAO,EACf;UACJH,EAAA,CAAAC,cAAA,aAAmF;UACjFD,EAAA,CAAAU,SAAA,aAA4B;UAC5BV,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrBH,EAAA,CAAAmB,UAAA,KAAAyF,gCAAA,mBAA2D;UAC7D5G,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,aAA2E;UACzED,EAAA,CAAAU,SAAA,aAAoC;UACpCV,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGjBH,EAFA,CAAAmB,UAAA,KAAA0F,gCAAA,mBAAmD,KAAAC,+BAAA,kBAEgB;UAGrE9G,EAAA,CAAAG,YAAA,EAAI;UA0CJH,EAvCA,CAAAmB,UAAA,KAAA4F,+BAAA,kBAAgF,KAAAC,+BAAA,mBAMV,KAAAC,+BAAA,kBAiCvB;UAOvDjH,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACC;;;UAnFCH,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAkH,gBAAA,YAAAd,GAAA,CAAA5D,WAAA,CAAyB;UAwBKxC,EAAA,CAAAI,SAAA,IAA2B;UAA3BJ,EAAA,CAAAyB,UAAA,SAAA2E,GAAA,CAAA7F,iBAAA,KAA2B;UAK/BP,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAyB,UAAA,SAAA2E,GAAA,CAAA5F,aAAA,KAAuB;UAEhBR,EAAA,CAAAI,SAAA,EAAgC;UAAhCJ,EAAA,CAAAyB,UAAA,SAAA2E,GAAA,CAAAxB,wBAAA,GAAgC;UAMjC5E,EAAA,CAAAI,SAAA,EAA4C;UAA5CJ,EAAA,CAAAyB,UAAA,SAAA2E,GAAA,CAAA1E,WAAA,IAAA0E,GAAA,CAAAzF,iBAAA,OAA4C;UAMxEX,EAAA,CAAAI,SAAA,EAAiB;UAAjBJ,EAAA,CAAAyB,UAAA,SAAA2E,GAAA,CAAA1E,WAAA,CAAiB;UAiCjB1B,EAAA,CAAAI,SAAA,EAAkB;UAAlBJ,EAAA,CAAAyB,UAAA,UAAA2E,GAAA,CAAA1E,WAAA,CAAkB;;;qBA9FxB7B,YAAY,EAAAsH,EAAA,CAAAC,IAAA,EAAEtH,YAAY,EAAA0F,EAAA,CAAA6B,UAAA,EAAA7B,EAAA,CAAA8B,gBAAA,EAAEvH,WAAW,EAAAwH,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}