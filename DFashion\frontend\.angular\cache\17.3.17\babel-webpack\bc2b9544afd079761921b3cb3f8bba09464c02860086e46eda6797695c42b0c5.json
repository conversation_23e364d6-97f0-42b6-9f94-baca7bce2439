{"ast": null, "code": "// DEFLATE is a complex format; to read this code, you should probably check the RFC first:\n// https://tools.ietf.org/html/rfc1951\n// You may also wish to take a look at the guide I made about this program:\n// https://gist.github.com/101arrowz/253f31eb5abc3d9275ab943003ffecad\n// Some of the following code is similar to that of UZIP.js:\n// https://github.com/photopea/UZIP.js\n// However, the vast majority of the codebase has diverged from UZIP.js to increase performance and reduce bundle size.\n// Sometimes 0 will appear where -1 would be more appropriate. This is because using a uint\n// is better for memory in most engines (I *think*).\nvar ch2 = {};\nvar wk = function (c, id, msg, transfer, cb) {\n  var w = new Worker(ch2[id] || (ch2[id] = URL.createObjectURL(new Blob([c + ';addEventListener(\"error\",function(e){e=e.error;postMessage({$e$:[e.message,e.code,e.stack]})})'], {\n    type: 'text/javascript'\n  }))));\n  w.onmessage = function (e) {\n    var d = e.data,\n      ed = d.$e$;\n    if (ed) {\n      var err = new Error(ed[0]);\n      err['code'] = ed[1];\n      err.stack = ed[2];\n      cb(err, null);\n    } else cb(null, d);\n  };\n  w.postMessage(msg, transfer);\n  return w;\n};\n\n// aliases for shorter compressed code (most minifers don't do this)\nvar u8 = Uint8Array,\n  u16 = Uint16Array,\n  i32 = Int32Array;\n// fixed length extra bits\nvar fleb = new u8([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, /* unused */0, 0, /* impossible */0]);\n// fixed distance extra bits\nvar fdeb = new u8([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, /* unused */0, 0]);\n// code length index map\nvar clim = new u8([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]);\n// get base, reverse index map from extra bits\nvar freb = function (eb, start) {\n  var b = new u16(31);\n  for (var i = 0; i < 31; ++i) {\n    b[i] = start += 1 << eb[i - 1];\n  }\n  // numbers here are at max 18 bits\n  var r = new i32(b[30]);\n  for (var i = 1; i < 30; ++i) {\n    for (var j = b[i]; j < b[i + 1]; ++j) {\n      r[j] = j - b[i] << 5 | i;\n    }\n  }\n  return {\n    b: b,\n    r: r\n  };\n};\nvar _a = freb(fleb, 2),\n  fl = _a.b,\n  revfl = _a.r;\n// we can ignore the fact that the other numbers are wrong; they never happen anyway\nfl[28] = 258, revfl[258] = 28;\nvar _b = freb(fdeb, 0),\n  fd = _b.b,\n  revfd = _b.r;\n// map of value to reverse (assuming 16 bits)\nvar rev = new u16(32768);\nfor (var i = 0; i < 32768; ++i) {\n  // reverse table algorithm from SO\n  var x = (i & 0xAAAA) >> 1 | (i & 0x5555) << 1;\n  x = (x & 0xCCCC) >> 2 | (x & 0x3333) << 2;\n  x = (x & 0xF0F0) >> 4 | (x & 0x0F0F) << 4;\n  rev[i] = ((x & 0xFF00) >> 8 | (x & 0x00FF) << 8) >> 1;\n}\n// create huffman tree from u8 \"map\": index -> code length for code index\n// mb (max bits) must be at most 15\n// TODO: optimize/split up?\nvar hMap = function (cd, mb, r) {\n  var s = cd.length;\n  // index\n  var i = 0;\n  // u16 \"map\": index -> # of codes with bit length = index\n  var l = new u16(mb);\n  // length of cd must be 288 (total # of codes)\n  for (; i < s; ++i) {\n    if (cd[i]) ++l[cd[i] - 1];\n  }\n  // u16 \"map\": index -> minimum code for bit length = index\n  var le = new u16(mb);\n  for (i = 1; i < mb; ++i) {\n    le[i] = le[i - 1] + l[i - 1] << 1;\n  }\n  var co;\n  if (r) {\n    // u16 \"map\": index -> number of actual bits, symbol for code\n    co = new u16(1 << mb);\n    // bits to remove for reverser\n    var rvb = 15 - mb;\n    for (i = 0; i < s; ++i) {\n      // ignore 0 lengths\n      if (cd[i]) {\n        // num encoding both symbol and bits read\n        var sv = i << 4 | cd[i];\n        // free bits\n        var r_1 = mb - cd[i];\n        // start value\n        var v = le[cd[i] - 1]++ << r_1;\n        // m is end value\n        for (var m = v | (1 << r_1) - 1; v <= m; ++v) {\n          // every 16 bit value starting with the code yields the same result\n          co[rev[v] >> rvb] = sv;\n        }\n      }\n    }\n  } else {\n    co = new u16(s);\n    for (i = 0; i < s; ++i) {\n      if (cd[i]) {\n        co[i] = rev[le[cd[i] - 1]++] >> 15 - cd[i];\n      }\n    }\n  }\n  return co;\n};\n// fixed length tree\nvar flt = new u8(288);\nfor (var i = 0; i < 144; ++i) flt[i] = 8;\nfor (var i = 144; i < 256; ++i) flt[i] = 9;\nfor (var i = 256; i < 280; ++i) flt[i] = 7;\nfor (var i = 280; i < 288; ++i) flt[i] = 8;\n// fixed distance tree\nvar fdt = new u8(32);\nfor (var i = 0; i < 32; ++i) fdt[i] = 5;\n// fixed length map\nvar flm = /*#__PURE__*/hMap(flt, 9, 0),\n  flrm = /*#__PURE__*/hMap(flt, 9, 1);\n// fixed distance map\nvar fdm = /*#__PURE__*/hMap(fdt, 5, 0),\n  fdrm = /*#__PURE__*/hMap(fdt, 5, 1);\n// find max of array\nvar max = function (a) {\n  var m = a[0];\n  for (var i = 1; i < a.length; ++i) {\n    if (a[i] > m) m = a[i];\n  }\n  return m;\n};\n// read d, starting at bit p and mask with m\nvar bits = function (d, p, m) {\n  var o = p / 8 | 0;\n  return (d[o] | d[o + 1] << 8) >> (p & 7) & m;\n};\n// read d, starting at bit p continuing for at least 16 bits\nvar bits16 = function (d, p) {\n  var o = p / 8 | 0;\n  return (d[o] | d[o + 1] << 8 | d[o + 2] << 16) >> (p & 7);\n};\n// get end of byte\nvar shft = function (p) {\n  return (p + 7) / 8 | 0;\n};\n// typed array slice - allows garbage collector to free original reference,\n// while being more compatible than .slice\nvar slc = function (v, s, e) {\n  if (s == null || s < 0) s = 0;\n  if (e == null || e > v.length) e = v.length;\n  // can't use .constructor in case user-supplied\n  return new u8(v.subarray(s, e));\n};\n/**\n * Codes for errors generated within this library\n */\nexport var FlateErrorCode = {\n  UnexpectedEOF: 0,\n  InvalidBlockType: 1,\n  InvalidLengthLiteral: 2,\n  InvalidDistance: 3,\n  StreamFinished: 4,\n  NoStreamHandler: 5,\n  InvalidHeader: 6,\n  NoCallback: 7,\n  InvalidUTF8: 8,\n  ExtraFieldTooLong: 9,\n  InvalidDate: 10,\n  FilenameTooLong: 11,\n  StreamFinishing: 12,\n  InvalidZipData: 13,\n  UnknownCompressionMethod: 14\n};\n// error codes\nvar ec = ['unexpected EOF', 'invalid block type', 'invalid length/literal', 'invalid distance', 'stream finished', 'no stream handler',, 'no callback', 'invalid UTF-8 data', 'extra field too long', 'date not in range 1980-2099', 'filename too long', 'stream finishing', 'invalid zip data'\n// determined by unknown compression method\n];\n;\nvar err = function (ind, msg, nt) {\n  var e = new Error(msg || ec[ind]);\n  e.code = ind;\n  if (Error.captureStackTrace) Error.captureStackTrace(e, err);\n  if (!nt) throw e;\n  return e;\n};\n// expands raw DEFLATE data\nvar inflt = function (dat, st, buf, dict) {\n  // source length       dict length\n  var sl = dat.length,\n    dl = dict ? dict.length : 0;\n  if (!sl || st.f && !st.l) return buf || new u8(0);\n  var noBuf = !buf;\n  // have to estimate size\n  var resize = noBuf || st.i != 2;\n  // no state\n  var noSt = st.i;\n  // Assumes roughly 33% compression ratio average\n  if (noBuf) buf = new u8(sl * 3);\n  // ensure buffer can fit at least l elements\n  var cbuf = function (l) {\n    var bl = buf.length;\n    // need to increase size to fit\n    if (l > bl) {\n      // Double or set to necessary, whichever is greater\n      var nbuf = new u8(Math.max(bl * 2, l));\n      nbuf.set(buf);\n      buf = nbuf;\n    }\n  };\n  //  last chunk         bitpos           bytes\n  var final = st.f || 0,\n    pos = st.p || 0,\n    bt = st.b || 0,\n    lm = st.l,\n    dm = st.d,\n    lbt = st.m,\n    dbt = st.n;\n  // total bits\n  var tbts = sl * 8;\n  do {\n    if (!lm) {\n      // BFINAL - this is only 1 when last chunk is next\n      final = bits(dat, pos, 1);\n      // type: 0 = no compression, 1 = fixed huffman, 2 = dynamic huffman\n      var type = bits(dat, pos + 1, 3);\n      pos += 3;\n      if (!type) {\n        // go to end of byte boundary\n        var s = shft(pos) + 4,\n          l = dat[s - 4] | dat[s - 3] << 8,\n          t = s + l;\n        if (t > sl) {\n          if (noSt) err(0);\n          break;\n        }\n        // ensure size\n        if (resize) cbuf(bt + l);\n        // Copy over uncompressed data\n        buf.set(dat.subarray(s, t), bt);\n        // Get new bitpos, update byte count\n        st.b = bt += l, st.p = pos = t * 8, st.f = final;\n        continue;\n      } else if (type == 1) lm = flrm, dm = fdrm, lbt = 9, dbt = 5;else if (type == 2) {\n        //  literal                            lengths\n        var hLit = bits(dat, pos, 31) + 257,\n          hcLen = bits(dat, pos + 10, 15) + 4;\n        var tl = hLit + bits(dat, pos + 5, 31) + 1;\n        pos += 14;\n        // length+distance tree\n        var ldt = new u8(tl);\n        // code length tree\n        var clt = new u8(19);\n        for (var i = 0; i < hcLen; ++i) {\n          // use index map to get real code\n          clt[clim[i]] = bits(dat, pos + i * 3, 7);\n        }\n        pos += hcLen * 3;\n        // code lengths bits\n        var clb = max(clt),\n          clbmsk = (1 << clb) - 1;\n        // code lengths map\n        var clm = hMap(clt, clb, 1);\n        for (var i = 0; i < tl;) {\n          var r = clm[bits(dat, pos, clbmsk)];\n          // bits read\n          pos += r & 15;\n          // symbol\n          var s = r >> 4;\n          // code length to copy\n          if (s < 16) {\n            ldt[i++] = s;\n          } else {\n            //  copy   count\n            var c = 0,\n              n = 0;\n            if (s == 16) n = 3 + bits(dat, pos, 3), pos += 2, c = ldt[i - 1];else if (s == 17) n = 3 + bits(dat, pos, 7), pos += 3;else if (s == 18) n = 11 + bits(dat, pos, 127), pos += 7;\n            while (n--) ldt[i++] = c;\n          }\n        }\n        //    length tree                 distance tree\n        var lt = ldt.subarray(0, hLit),\n          dt = ldt.subarray(hLit);\n        // max length bits\n        lbt = max(lt);\n        // max dist bits\n        dbt = max(dt);\n        lm = hMap(lt, lbt, 1);\n        dm = hMap(dt, dbt, 1);\n      } else err(1);\n      if (pos > tbts) {\n        if (noSt) err(0);\n        break;\n      }\n    }\n    // Make sure the buffer can hold this + the largest possible addition\n    // Maximum chunk size (practically, theoretically infinite) is 2^17\n    if (resize) cbuf(bt + 131072);\n    var lms = (1 << lbt) - 1,\n      dms = (1 << dbt) - 1;\n    var lpos = pos;\n    for (;; lpos = pos) {\n      // bits read, code\n      var c = lm[bits16(dat, pos) & lms],\n        sym = c >> 4;\n      pos += c & 15;\n      if (pos > tbts) {\n        if (noSt) err(0);\n        break;\n      }\n      if (!c) err(2);\n      if (sym < 256) buf[bt++] = sym;else if (sym == 256) {\n        lpos = pos, lm = null;\n        break;\n      } else {\n        var add = sym - 254;\n        // no extra bits needed if less\n        if (sym > 264) {\n          // index\n          var i = sym - 257,\n            b = fleb[i];\n          add = bits(dat, pos, (1 << b) - 1) + fl[i];\n          pos += b;\n        }\n        // dist\n        var d = dm[bits16(dat, pos) & dms],\n          dsym = d >> 4;\n        if (!d) err(3);\n        pos += d & 15;\n        var dt = fd[dsym];\n        if (dsym > 3) {\n          var b = fdeb[dsym];\n          dt += bits16(dat, pos) & (1 << b) - 1, pos += b;\n        }\n        if (pos > tbts) {\n          if (noSt) err(0);\n          break;\n        }\n        if (resize) cbuf(bt + 131072);\n        var end = bt + add;\n        if (bt < dt) {\n          var shift = dl - dt,\n            dend = Math.min(dt, end);\n          if (shift + bt < 0) err(3);\n          for (; bt < dend; ++bt) buf[bt] = dict[shift + bt];\n        }\n        for (; bt < end; ++bt) buf[bt] = buf[bt - dt];\n      }\n    }\n    st.l = lm, st.p = lpos, st.b = bt, st.f = final;\n    if (lm) final = 1, st.m = lbt, st.d = dm, st.n = dbt;\n  } while (!final);\n  // don't reallocate for streams or user buffers\n  return bt != buf.length && noBuf ? slc(buf, 0, bt) : buf.subarray(0, bt);\n};\n// starting at p, write the minimum number of bits that can hold v to d\nvar wbits = function (d, p, v) {\n  v <<= p & 7;\n  var o = p / 8 | 0;\n  d[o] |= v;\n  d[o + 1] |= v >> 8;\n};\n// starting at p, write the minimum number of bits (>8) that can hold v to d\nvar wbits16 = function (d, p, v) {\n  v <<= p & 7;\n  var o = p / 8 | 0;\n  d[o] |= v;\n  d[o + 1] |= v >> 8;\n  d[o + 2] |= v >> 16;\n};\n// creates code lengths from a frequency table\nvar hTree = function (d, mb) {\n  // Need extra info to make a tree\n  var t = [];\n  for (var i = 0; i < d.length; ++i) {\n    if (d[i]) t.push({\n      s: i,\n      f: d[i]\n    });\n  }\n  var s = t.length;\n  var t2 = t.slice();\n  if (!s) return {\n    t: et,\n    l: 0\n  };\n  if (s == 1) {\n    var v = new u8(t[0].s + 1);\n    v[t[0].s] = 1;\n    return {\n      t: v,\n      l: 1\n    };\n  }\n  t.sort(function (a, b) {\n    return a.f - b.f;\n  });\n  // after i2 reaches last ind, will be stopped\n  // freq must be greater than largest possible number of symbols\n  t.push({\n    s: -1,\n    f: 25001\n  });\n  var l = t[0],\n    r = t[1],\n    i0 = 0,\n    i1 = 1,\n    i2 = 2;\n  t[0] = {\n    s: -1,\n    f: l.f + r.f,\n    l: l,\n    r: r\n  };\n  // efficient algorithm from UZIP.js\n  // i0 is lookbehind, i2 is lookahead - after processing two low-freq\n  // symbols that combined have high freq, will start processing i2 (high-freq,\n  // non-composite) symbols instead\n  // see https://reddit.com/r/photopea/comments/ikekht/uzipjs_questions/\n  while (i1 != s - 1) {\n    l = t[t[i0].f < t[i2].f ? i0++ : i2++];\n    r = t[i0 != i1 && t[i0].f < t[i2].f ? i0++ : i2++];\n    t[i1++] = {\n      s: -1,\n      f: l.f + r.f,\n      l: l,\n      r: r\n    };\n  }\n  var maxSym = t2[0].s;\n  for (var i = 1; i < s; ++i) {\n    if (t2[i].s > maxSym) maxSym = t2[i].s;\n  }\n  // code lengths\n  var tr = new u16(maxSym + 1);\n  // max bits in tree\n  var mbt = ln(t[i1 - 1], tr, 0);\n  if (mbt > mb) {\n    // more algorithms from UZIP.js\n    // TODO: find out how this code works (debt)\n    //  ind    debt\n    var i = 0,\n      dt = 0;\n    //    left            cost\n    var lft = mbt - mb,\n      cst = 1 << lft;\n    t2.sort(function (a, b) {\n      return tr[b.s] - tr[a.s] || a.f - b.f;\n    });\n    for (; i < s; ++i) {\n      var i2_1 = t2[i].s;\n      if (tr[i2_1] > mb) {\n        dt += cst - (1 << mbt - tr[i2_1]);\n        tr[i2_1] = mb;\n      } else break;\n    }\n    dt >>= lft;\n    while (dt > 0) {\n      var i2_2 = t2[i].s;\n      if (tr[i2_2] < mb) dt -= 1 << mb - tr[i2_2]++ - 1;else ++i;\n    }\n    for (; i >= 0 && dt; --i) {\n      var i2_3 = t2[i].s;\n      if (tr[i2_3] == mb) {\n        --tr[i2_3];\n        ++dt;\n      }\n    }\n    mbt = mb;\n  }\n  return {\n    t: new u8(tr),\n    l: mbt\n  };\n};\n// get the max length and assign length codes\nvar ln = function (n, l, d) {\n  return n.s == -1 ? Math.max(ln(n.l, l, d + 1), ln(n.r, l, d + 1)) : l[n.s] = d;\n};\n// length codes generation\nvar lc = function (c) {\n  var s = c.length;\n  // Note that the semicolon was intentional\n  while (s && !c[--s]);\n  var cl = new u16(++s);\n  //  ind      num         streak\n  var cli = 0,\n    cln = c[0],\n    cls = 1;\n  var w = function (v) {\n    cl[cli++] = v;\n  };\n  for (var i = 1; i <= s; ++i) {\n    if (c[i] == cln && i != s) ++cls;else {\n      if (!cln && cls > 2) {\n        for (; cls > 138; cls -= 138) w(32754);\n        if (cls > 2) {\n          w(cls > 10 ? cls - 11 << 5 | 28690 : cls - 3 << 5 | 12305);\n          cls = 0;\n        }\n      } else if (cls > 3) {\n        w(cln), --cls;\n        for (; cls > 6; cls -= 6) w(8304);\n        if (cls > 2) w(cls - 3 << 5 | 8208), cls = 0;\n      }\n      while (cls--) w(cln);\n      cls = 1;\n      cln = c[i];\n    }\n  }\n  return {\n    c: cl.subarray(0, cli),\n    n: s\n  };\n};\n// calculate the length of output from tree, code lengths\nvar clen = function (cf, cl) {\n  var l = 0;\n  for (var i = 0; i < cl.length; ++i) l += cf[i] * cl[i];\n  return l;\n};\n// writes a fixed block\n// returns the new bit pos\nvar wfblk = function (out, pos, dat) {\n  // no need to write 00 as type: TypedArray defaults to 0\n  var s = dat.length;\n  var o = shft(pos + 2);\n  out[o] = s & 255;\n  out[o + 1] = s >> 8;\n  out[o + 2] = out[o] ^ 255;\n  out[o + 3] = out[o + 1] ^ 255;\n  for (var i = 0; i < s; ++i) out[o + i + 4] = dat[i];\n  return (o + 4 + s) * 8;\n};\n// writes a block\nvar wblk = function (dat, out, final, syms, lf, df, eb, li, bs, bl, p) {\n  wbits(out, p++, final);\n  ++lf[256];\n  var _a = hTree(lf, 15),\n    dlt = _a.t,\n    mlb = _a.l;\n  var _b = hTree(df, 15),\n    ddt = _b.t,\n    mdb = _b.l;\n  var _c = lc(dlt),\n    lclt = _c.c,\n    nlc = _c.n;\n  var _d = lc(ddt),\n    lcdt = _d.c,\n    ndc = _d.n;\n  var lcfreq = new u16(19);\n  for (var i = 0; i < lclt.length; ++i) ++lcfreq[lclt[i] & 31];\n  for (var i = 0; i < lcdt.length; ++i) ++lcfreq[lcdt[i] & 31];\n  var _e = hTree(lcfreq, 7),\n    lct = _e.t,\n    mlcb = _e.l;\n  var nlcc = 19;\n  for (; nlcc > 4 && !lct[clim[nlcc - 1]]; --nlcc);\n  var flen = bl + 5 << 3;\n  var ftlen = clen(lf, flt) + clen(df, fdt) + eb;\n  var dtlen = clen(lf, dlt) + clen(df, ddt) + eb + 14 + 3 * nlcc + clen(lcfreq, lct) + 2 * lcfreq[16] + 3 * lcfreq[17] + 7 * lcfreq[18];\n  if (bs >= 0 && flen <= ftlen && flen <= dtlen) return wfblk(out, p, dat.subarray(bs, bs + bl));\n  var lm, ll, dm, dl;\n  wbits(out, p, 1 + (dtlen < ftlen)), p += 2;\n  if (dtlen < ftlen) {\n    lm = hMap(dlt, mlb, 0), ll = dlt, dm = hMap(ddt, mdb, 0), dl = ddt;\n    var llm = hMap(lct, mlcb, 0);\n    wbits(out, p, nlc - 257);\n    wbits(out, p + 5, ndc - 1);\n    wbits(out, p + 10, nlcc - 4);\n    p += 14;\n    for (var i = 0; i < nlcc; ++i) wbits(out, p + 3 * i, lct[clim[i]]);\n    p += 3 * nlcc;\n    var lcts = [lclt, lcdt];\n    for (var it = 0; it < 2; ++it) {\n      var clct = lcts[it];\n      for (var i = 0; i < clct.length; ++i) {\n        var len = clct[i] & 31;\n        wbits(out, p, llm[len]), p += lct[len];\n        if (len > 15) wbits(out, p, clct[i] >> 5 & 127), p += clct[i] >> 12;\n      }\n    }\n  } else {\n    lm = flm, ll = flt, dm = fdm, dl = fdt;\n  }\n  for (var i = 0; i < li; ++i) {\n    var sym = syms[i];\n    if (sym > 255) {\n      var len = sym >> 18 & 31;\n      wbits16(out, p, lm[len + 257]), p += ll[len + 257];\n      if (len > 7) wbits(out, p, sym >> 23 & 31), p += fleb[len];\n      var dst = sym & 31;\n      wbits16(out, p, dm[dst]), p += dl[dst];\n      if (dst > 3) wbits16(out, p, sym >> 5 & 8191), p += fdeb[dst];\n    } else {\n      wbits16(out, p, lm[sym]), p += ll[sym];\n    }\n  }\n  wbits16(out, p, lm[256]);\n  return p + ll[256];\n};\n// deflate options (nice << 13) | chain\nvar deo = /*#__PURE__*/new i32([65540, 131080, 131088, 131104, 262176, 1048704, 1048832, 2114560, 2117632]);\n// empty\nvar et = /*#__PURE__*/new u8(0);\n// compresses data into a raw DEFLATE buffer\nvar dflt = function (dat, lvl, plvl, pre, post, st) {\n  var s = st.z || dat.length;\n  var o = new u8(pre + s + 5 * (1 + Math.ceil(s / 7000)) + post);\n  // writing to this writes to the output buffer\n  var w = o.subarray(pre, o.length - post);\n  var lst = st.l;\n  var pos = (st.r || 0) & 7;\n  if (lvl) {\n    if (pos) w[0] = st.r >> 3;\n    var opt = deo[lvl - 1];\n    var n = opt >> 13,\n      c = opt & 8191;\n    var msk_1 = (1 << plvl) - 1;\n    //    prev 2-byte val map    curr 2-byte val map\n    var prev = st.p || new u16(32768),\n      head = st.h || new u16(msk_1 + 1);\n    var bs1_1 = Math.ceil(plvl / 3),\n      bs2_1 = 2 * bs1_1;\n    var hsh = function (i) {\n      return (dat[i] ^ dat[i + 1] << bs1_1 ^ dat[i + 2] << bs2_1) & msk_1;\n    };\n    // 24576 is an arbitrary number of maximum symbols per block\n    // 424 buffer for last block\n    var syms = new i32(25000);\n    // length/literal freq   distance freq\n    var lf = new u16(288),\n      df = new u16(32);\n    //  l/lcnt  exbits  index          l/lind  waitdx          blkpos\n    var lc_1 = 0,\n      eb = 0,\n      i = st.i || 0,\n      li = 0,\n      wi = st.w || 0,\n      bs = 0;\n    for (; i + 2 < s; ++i) {\n      // hash value\n      var hv = hsh(i);\n      // index mod 32768    previous index mod\n      var imod = i & 32767,\n        pimod = head[hv];\n      prev[imod] = pimod;\n      head[hv] = imod;\n      // We always should modify head and prev, but only add symbols if\n      // this data is not yet processed (\"wait\" for wait index)\n      if (wi <= i) {\n        // bytes remaining\n        var rem = s - i;\n        if ((lc_1 > 7000 || li > 24576) && (rem > 423 || !lst)) {\n          pos = wblk(dat, w, 0, syms, lf, df, eb, li, bs, i - bs, pos);\n          li = lc_1 = eb = 0, bs = i;\n          for (var j = 0; j < 286; ++j) lf[j] = 0;\n          for (var j = 0; j < 30; ++j) df[j] = 0;\n        }\n        //  len    dist   chain\n        var l = 2,\n          d = 0,\n          ch_1 = c,\n          dif = imod - pimod & 32767;\n        if (rem > 2 && hv == hsh(i - dif)) {\n          var maxn = Math.min(n, rem) - 1;\n          var maxd = Math.min(32767, i);\n          // max possible length\n          // not capped at dif because decompressors implement \"rolling\" index population\n          var ml = Math.min(258, rem);\n          while (dif <= maxd && --ch_1 && imod != pimod) {\n            if (dat[i + l] == dat[i + l - dif]) {\n              var nl = 0;\n              for (; nl < ml && dat[i + nl] == dat[i + nl - dif]; ++nl);\n              if (nl > l) {\n                l = nl, d = dif;\n                // break out early when we reach \"nice\" (we are satisfied enough)\n                if (nl > maxn) break;\n                // now, find the rarest 2-byte sequence within this\n                // length of literals and search for that instead.\n                // Much faster than just using the start\n                var mmd = Math.min(dif, nl - 2);\n                var md = 0;\n                for (var j = 0; j < mmd; ++j) {\n                  var ti = i - dif + j & 32767;\n                  var pti = prev[ti];\n                  var cd = ti - pti & 32767;\n                  if (cd > md) md = cd, pimod = ti;\n                }\n              }\n            }\n            // check the previous match\n            imod = pimod, pimod = prev[imod];\n            dif += imod - pimod & 32767;\n          }\n        }\n        // d will be nonzero only when a match was found\n        if (d) {\n          // store both dist and len data in one int32\n          // Make sure this is recognized as a len/dist with 28th bit (2^28)\n          syms[li++] = 268435456 | revfl[l] << 18 | revfd[d];\n          var lin = revfl[l] & 31,\n            din = revfd[d] & 31;\n          eb += fleb[lin] + fdeb[din];\n          ++lf[257 + lin];\n          ++df[din];\n          wi = i + l;\n          ++lc_1;\n        } else {\n          syms[li++] = dat[i];\n          ++lf[dat[i]];\n        }\n      }\n    }\n    for (i = Math.max(i, wi); i < s; ++i) {\n      syms[li++] = dat[i];\n      ++lf[dat[i]];\n    }\n    pos = wblk(dat, w, lst, syms, lf, df, eb, li, bs, i - bs, pos);\n    if (!lst) {\n      st.r = pos & 7 | w[pos / 8 | 0] << 3;\n      // shft(pos) now 1 less if pos & 7 != 0\n      pos -= 7;\n      st.h = head, st.p = prev, st.i = i, st.w = wi;\n    }\n  } else {\n    for (var i = st.w || 0; i < s + lst; i += 65535) {\n      // end\n      var e = i + 65535;\n      if (e >= s) {\n        // write final block\n        w[pos / 8 | 0] = lst;\n        e = s;\n      }\n      pos = wfblk(w, pos + 1, dat.subarray(i, e));\n    }\n    st.i = s;\n  }\n  return slc(o, 0, pre + shft(pos) + post);\n};\n// CRC32 table\nvar crct = /*#__PURE__*/function () {\n  var t = new Int32Array(256);\n  for (var i = 0; i < 256; ++i) {\n    var c = i,\n      k = 9;\n    while (--k) c = (c & 1 && -306674912) ^ c >>> 1;\n    t[i] = c;\n  }\n  return t;\n}();\n// CRC32\nvar crc = function () {\n  var c = -1;\n  return {\n    p: function (d) {\n      // closures have awful performance\n      var cr = c;\n      for (var i = 0; i < d.length; ++i) cr = crct[cr & 255 ^ d[i]] ^ cr >>> 8;\n      c = cr;\n    },\n    d: function () {\n      return ~c;\n    }\n  };\n};\n// Adler32\nvar adler = function () {\n  var a = 1,\n    b = 0;\n  return {\n    p: function (d) {\n      // closures have awful performance\n      var n = a,\n        m = b;\n      var l = d.length | 0;\n      for (var i = 0; i != l;) {\n        var e = Math.min(i + 2655, l);\n        for (; i < e; ++i) m += n += d[i];\n        n = (n & 65535) + 15 * (n >> 16), m = (m & 65535) + 15 * (m >> 16);\n      }\n      a = n, b = m;\n    },\n    d: function () {\n      a %= 65521, b %= 65521;\n      return (a & 255) << 24 | (a & 0xFF00) << 8 | (b & 255) << 8 | b >> 8;\n    }\n  };\n};\n;\n// deflate with opts\nvar dopt = function (dat, opt, pre, post, st) {\n  if (!st) {\n    st = {\n      l: 1\n    };\n    if (opt.dictionary) {\n      var dict = opt.dictionary.subarray(-32768);\n      var newDat = new u8(dict.length + dat.length);\n      newDat.set(dict);\n      newDat.set(dat, dict.length);\n      dat = newDat;\n      st.w = dict.length;\n    }\n  }\n  return dflt(dat, opt.level == null ? 6 : opt.level, opt.mem == null ? st.l ? Math.ceil(Math.max(8, Math.min(13, Math.log(dat.length))) * 1.5) : 20 : 12 + opt.mem, pre, post, st);\n};\n// Walmart object spread\nvar mrg = function (a, b) {\n  var o = {};\n  for (var k in a) o[k] = a[k];\n  for (var k in b) o[k] = b[k];\n  return o;\n};\n// worker clone\n// This is possibly the craziest part of the entire codebase, despite how simple it may seem.\n// The only parameter to this function is a closure that returns an array of variables outside of the function scope.\n// We're going to try to figure out the variable names used in the closure as strings because that is crucial for workerization.\n// We will return an object mapping of true variable name to value (basically, the current scope as a JS object).\n// The reason we can't just use the original variable names is minifiers mangling the toplevel scope.\n// This took me three weeks to figure out how to do.\nvar wcln = function (fn, fnStr, td) {\n  var dt = fn();\n  var st = fn.toString();\n  var ks = st.slice(st.indexOf('[') + 1, st.lastIndexOf(']')).replace(/\\s+/g, '').split(',');\n  for (var i = 0; i < dt.length; ++i) {\n    var v = dt[i],\n      k = ks[i];\n    if (typeof v == 'function') {\n      fnStr += ';' + k + '=';\n      var st_1 = v.toString();\n      if (v.prototype) {\n        // for global objects\n        if (st_1.indexOf('[native code]') != -1) {\n          var spInd = st_1.indexOf(' ', 8) + 1;\n          fnStr += st_1.slice(spInd, st_1.indexOf('(', spInd));\n        } else {\n          fnStr += st_1;\n          for (var t in v.prototype) fnStr += ';' + k + '.prototype.' + t + '=' + v.prototype[t].toString();\n        }\n      } else fnStr += st_1;\n    } else td[k] = v;\n  }\n  return fnStr;\n};\nvar ch = [];\n// clone bufs\nvar cbfs = function (v) {\n  var tl = [];\n  for (var k in v) {\n    if (v[k].buffer) {\n      tl.push((v[k] = new v[k].constructor(v[k])).buffer);\n    }\n  }\n  return tl;\n};\n// use a worker to execute code\nvar wrkr = function (fns, init, id, cb) {\n  if (!ch[id]) {\n    var fnStr = '',\n      td_1 = {},\n      m = fns.length - 1;\n    for (var i = 0; i < m; ++i) fnStr = wcln(fns[i], fnStr, td_1);\n    ch[id] = {\n      c: wcln(fns[m], fnStr, td_1),\n      e: td_1\n    };\n  }\n  var td = mrg({}, ch[id].e);\n  return wk(ch[id].c + ';onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage=' + init.toString() + '}', id, td, cbfs(td), cb);\n};\n// base async inflate fn\nvar bInflt = function () {\n  return [u8, u16, i32, fleb, fdeb, clim, fl, fd, flrm, fdrm, rev, ec, hMap, max, bits, bits16, shft, slc, err, inflt, inflateSync, pbf, gopt];\n};\nvar bDflt = function () {\n  return [u8, u16, i32, fleb, fdeb, clim, revfl, revfd, flm, flt, fdm, fdt, rev, deo, et, hMap, wbits, wbits16, hTree, ln, lc, clen, wfblk, wblk, shft, slc, dflt, dopt, deflateSync, pbf];\n};\n// gzip extra\nvar gze = function () {\n  return [gzh, gzhl, wbytes, crc, crct];\n};\n// gunzip extra\nvar guze = function () {\n  return [gzs, gzl];\n};\n// zlib extra\nvar zle = function () {\n  return [zlh, wbytes, adler];\n};\n// unzlib extra\nvar zule = function () {\n  return [zls];\n};\n// post buf\nvar pbf = function (msg) {\n  return postMessage(msg, [msg.buffer]);\n};\n// get opts\nvar gopt = function (o) {\n  return o && {\n    out: o.size && new u8(o.size),\n    dictionary: o.dictionary\n  };\n};\n// async helper\nvar cbify = function (dat, opts, fns, init, id, cb) {\n  var w = wrkr(fns, init, id, function (err, dat) {\n    w.terminate();\n    cb(err, dat);\n  });\n  w.postMessage([dat, opts], opts.consume ? [dat.buffer] : []);\n  return function () {\n    w.terminate();\n  };\n};\n// auto stream\nvar astrm = function (strm) {\n  strm.ondata = function (dat, final) {\n    return postMessage([dat, final], [dat.buffer]);\n  };\n  return function (ev) {\n    if (ev.data.length) {\n      strm.push(ev.data[0], ev.data[1]);\n      postMessage([ev.data[0].length]);\n    } else strm.flush();\n  };\n};\n// async stream attach\nvar astrmify = function (fns, strm, opts, init, id, flush, ext) {\n  var t;\n  var w = wrkr(fns, init, id, function (err, dat) {\n    if (err) w.terminate(), strm.ondata.call(strm, err);else if (!Array.isArray(dat)) ext(dat);else if (dat.length == 1) {\n      strm.queuedSize -= dat[0];\n      if (strm.ondrain) strm.ondrain(dat[0]);\n    } else {\n      if (dat[1]) w.terminate();\n      strm.ondata.call(strm, err, dat[0], dat[1]);\n    }\n  });\n  w.postMessage(opts);\n  strm.queuedSize = 0;\n  strm.push = function (d, f) {\n    if (!strm.ondata) err(5);\n    if (t) strm.ondata(err(4, 0, 1), null, !!f);\n    strm.queuedSize += d.length;\n    w.postMessage([d, t = f], [d.buffer]);\n  };\n  strm.terminate = function () {\n    w.terminate();\n  };\n  if (flush) {\n    strm.flush = function () {\n      w.postMessage([]);\n    };\n  }\n};\n// read 2 bytes\nvar b2 = function (d, b) {\n  return d[b] | d[b + 1] << 8;\n};\n// read 4 bytes\nvar b4 = function (d, b) {\n  return (d[b] | d[b + 1] << 8 | d[b + 2] << 16 | d[b + 3] << 24) >>> 0;\n};\nvar b8 = function (d, b) {\n  return b4(d, b) + b4(d, b + 4) * 4294967296;\n};\n// write bytes\nvar wbytes = function (d, b, v) {\n  for (; v; ++b) d[b] = v, v >>>= 8;\n};\n// gzip header\nvar gzh = function (c, o) {\n  var fn = o.filename;\n  c[0] = 31, c[1] = 139, c[2] = 8, c[8] = o.level < 2 ? 4 : o.level == 9 ? 2 : 0, c[9] = 3; // assume Unix\n  if (o.mtime != 0) wbytes(c, 4, Math.floor(new Date(o.mtime || Date.now()) / 1000));\n  if (fn) {\n    c[3] = 8;\n    for (var i = 0; i <= fn.length; ++i) c[i + 10] = fn.charCodeAt(i);\n  }\n};\n// gzip footer: -8 to -4 = CRC, -4 to -0 is length\n// gzip start\nvar gzs = function (d) {\n  if (d[0] != 31 || d[1] != 139 || d[2] != 8) err(6, 'invalid gzip data');\n  var flg = d[3];\n  var st = 10;\n  if (flg & 4) st += (d[10] | d[11] << 8) + 2;\n  for (var zs = (flg >> 3 & 1) + (flg >> 4 & 1); zs > 0; zs -= !d[st++]);\n  return st + (flg & 2);\n};\n// gzip length\nvar gzl = function (d) {\n  var l = d.length;\n  return (d[l - 4] | d[l - 3] << 8 | d[l - 2] << 16 | d[l - 1] << 24) >>> 0;\n};\n// gzip header length\nvar gzhl = function (o) {\n  return 10 + (o.filename ? o.filename.length + 1 : 0);\n};\n// zlib header\nvar zlh = function (c, o) {\n  var lv = o.level,\n    fl = lv == 0 ? 0 : lv < 6 ? 1 : lv == 9 ? 3 : 2;\n  c[0] = 120, c[1] = fl << 6 | (o.dictionary && 32);\n  c[1] |= 31 - (c[0] << 8 | c[1]) % 31;\n  if (o.dictionary) {\n    var h = adler();\n    h.p(o.dictionary);\n    wbytes(c, 2, h.d());\n  }\n};\n// zlib start\nvar zls = function (d, dict) {\n  if ((d[0] & 15) != 8 || d[0] >> 4 > 7 || (d[0] << 8 | d[1]) % 31) err(6, 'invalid zlib data');\n  if ((d[1] >> 5 & 1) == +!dict) err(6, 'invalid zlib data: ' + (d[1] & 32 ? 'need' : 'unexpected') + ' dictionary');\n  return (d[1] >> 3 & 4) + 2;\n};\nfunction StrmOpt(opts, cb) {\n  if (typeof opts == 'function') cb = opts, opts = {};\n  this.ondata = cb;\n  return opts;\n}\n/**\n * Streaming DEFLATE compression\n */\nvar Deflate = /*#__PURE__*/function () {\n  function Deflate(opts, cb) {\n    if (typeof opts == 'function') cb = opts, opts = {};\n    this.ondata = cb;\n    this.o = opts || {};\n    this.s = {\n      l: 0,\n      i: 32768,\n      w: 32768,\n      z: 32768\n    };\n    // Buffer length must always be 0 mod 32768 for index calculations to be correct when modifying head and prev\n    // 98304 = 32768 (lookback) + 65536 (common chunk size)\n    this.b = new u8(98304);\n    if (this.o.dictionary) {\n      var dict = this.o.dictionary.subarray(-32768);\n      this.b.set(dict, 32768 - dict.length);\n      this.s.i = 32768 - dict.length;\n    }\n  }\n  Deflate.prototype.p = function (c, f) {\n    this.ondata(dopt(c, this.o, 0, 0, this.s), f);\n  };\n  /**\n   * Pushes a chunk to be deflated\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Deflate.prototype.push = function (chunk, final) {\n    if (!this.ondata) err(5);\n    if (this.s.l) err(4);\n    var endLen = chunk.length + this.s.z;\n    if (endLen > this.b.length) {\n      if (endLen > 2 * this.b.length - 32768) {\n        var newBuf = new u8(endLen & -32768);\n        newBuf.set(this.b.subarray(0, this.s.z));\n        this.b = newBuf;\n      }\n      var split = this.b.length - this.s.z;\n      this.b.set(chunk.subarray(0, split), this.s.z);\n      this.s.z = this.b.length;\n      this.p(this.b, false);\n      this.b.set(this.b.subarray(-32768));\n      this.b.set(chunk.subarray(split), 32768);\n      this.s.z = chunk.length - split + 32768;\n      this.s.i = 32766, this.s.w = 32768;\n    } else {\n      this.b.set(chunk, this.s.z);\n      this.s.z += chunk.length;\n    }\n    this.s.l = final & 1;\n    if (this.s.z > this.s.w + 8191 || final) {\n      this.p(this.b, final || false);\n      this.s.w = this.s.i, this.s.i -= 2;\n    }\n  };\n  /**\n   * Flushes buffered uncompressed data. Useful to immediately retrieve the\n   * deflated output for small inputs.\n   */\n  Deflate.prototype.flush = function () {\n    if (!this.ondata) err(5);\n    if (this.s.l) err(4);\n    this.p(this.b, false);\n    this.s.w = this.s.i, this.s.i -= 2;\n  };\n  return Deflate;\n}();\nexport { Deflate };\n/**\n * Asynchronous streaming DEFLATE compression\n */\nvar AsyncDeflate = /*#__PURE__*/function () {\n  function AsyncDeflate(opts, cb) {\n    astrmify([bDflt, function () {\n      return [astrm, Deflate];\n    }], this, StrmOpt.call(this, opts, cb), function (ev) {\n      var strm = new Deflate(ev.data);\n      onmessage = astrm(strm);\n    }, 6, 1);\n  }\n  return AsyncDeflate;\n}();\nexport { AsyncDeflate };\nexport function deflate(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') err(7);\n  return cbify(data, opts, [bDflt], function (ev) {\n    return pbf(deflateSync(ev.data[0], ev.data[1]));\n  }, 0, cb);\n}\n/**\n * Compresses data with DEFLATE without any wrapper\n * @param data The data to compress\n * @param opts The compression options\n * @returns The deflated version of the data\n */\nexport function deflateSync(data, opts) {\n  return dopt(data, opts || {}, 0, 0);\n}\n/**\n * Streaming DEFLATE decompression\n */\nvar Inflate = /*#__PURE__*/function () {\n  function Inflate(opts, cb) {\n    // no StrmOpt here to avoid adding to workerizer\n    if (typeof opts == 'function') cb = opts, opts = {};\n    this.ondata = cb;\n    var dict = opts && opts.dictionary && opts.dictionary.subarray(-32768);\n    this.s = {\n      i: 0,\n      b: dict ? dict.length : 0\n    };\n    this.o = new u8(32768);\n    this.p = new u8(0);\n    if (dict) this.o.set(dict);\n  }\n  Inflate.prototype.e = function (c) {\n    if (!this.ondata) err(5);\n    if (this.d) err(4);\n    if (!this.p.length) this.p = c;else if (c.length) {\n      var n = new u8(this.p.length + c.length);\n      n.set(this.p), n.set(c, this.p.length), this.p = n;\n    }\n  };\n  Inflate.prototype.c = function (final) {\n    this.s.i = +(this.d = final || false);\n    var bts = this.s.b;\n    var dt = inflt(this.p, this.s, this.o);\n    this.ondata(slc(dt, bts, this.s.b), this.d);\n    this.o = slc(dt, this.s.b - 32768), this.s.b = this.o.length;\n    this.p = slc(this.p, this.s.p / 8 | 0), this.s.p &= 7;\n  };\n  /**\n   * Pushes a chunk to be inflated\n   * @param chunk The chunk to push\n   * @param final Whether this is the final chunk\n   */\n  Inflate.prototype.push = function (chunk, final) {\n    this.e(chunk), this.c(final);\n  };\n  return Inflate;\n}();\nexport { Inflate };\n/**\n * Asynchronous streaming DEFLATE decompression\n */\nvar AsyncInflate = /*#__PURE__*/function () {\n  function AsyncInflate(opts, cb) {\n    astrmify([bInflt, function () {\n      return [astrm, Inflate];\n    }], this, StrmOpt.call(this, opts, cb), function (ev) {\n      var strm = new Inflate(ev.data);\n      onmessage = astrm(strm);\n    }, 7, 0);\n  }\n  return AsyncInflate;\n}();\nexport { AsyncInflate };\nexport function inflate(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') err(7);\n  return cbify(data, opts, [bInflt], function (ev) {\n    return pbf(inflateSync(ev.data[0], gopt(ev.data[1])));\n  }, 1, cb);\n}\n/**\n * Expands DEFLATE data with no wrapper\n * @param data The data to decompress\n * @param opts The decompression options\n * @returns The decompressed version of the data\n */\nexport function inflateSync(data, opts) {\n  return inflt(data, {\n    i: 2\n  }, opts && opts.out, opts && opts.dictionary);\n}\n// before you yell at me for not just using extends, my reason is that TS inheritance is hard to workerize.\n/**\n * Streaming GZIP compression\n */\nvar Gzip = /*#__PURE__*/function () {\n  function Gzip(opts, cb) {\n    this.c = crc();\n    this.l = 0;\n    this.v = 1;\n    Deflate.call(this, opts, cb);\n  }\n  /**\n   * Pushes a chunk to be GZIPped\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Gzip.prototype.push = function (chunk, final) {\n    this.c.p(chunk);\n    this.l += chunk.length;\n    Deflate.prototype.push.call(this, chunk, final);\n  };\n  Gzip.prototype.p = function (c, f) {\n    var raw = dopt(c, this.o, this.v && gzhl(this.o), f && 8, this.s);\n    if (this.v) gzh(raw, this.o), this.v = 0;\n    if (f) wbytes(raw, raw.length - 8, this.c.d()), wbytes(raw, raw.length - 4, this.l);\n    this.ondata(raw, f);\n  };\n  /**\n   * Flushes buffered uncompressed data. Useful to immediately retrieve the\n   * GZIPped output for small inputs.\n   */\n  Gzip.prototype.flush = function () {\n    Deflate.prototype.flush.call(this);\n  };\n  return Gzip;\n}();\nexport { Gzip };\n/**\n * Asynchronous streaming GZIP compression\n */\nvar AsyncGzip = /*#__PURE__*/function () {\n  function AsyncGzip(opts, cb) {\n    astrmify([bDflt, gze, function () {\n      return [astrm, Deflate, Gzip];\n    }], this, StrmOpt.call(this, opts, cb), function (ev) {\n      var strm = new Gzip(ev.data);\n      onmessage = astrm(strm);\n    }, 8, 1);\n  }\n  return AsyncGzip;\n}();\nexport { AsyncGzip };\nexport function gzip(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') err(7);\n  return cbify(data, opts, [bDflt, gze, function () {\n    return [gzipSync];\n  }], function (ev) {\n    return pbf(gzipSync(ev.data[0], ev.data[1]));\n  }, 2, cb);\n}\n/**\n * Compresses data with GZIP\n * @param data The data to compress\n * @param opts The compression options\n * @returns The gzipped version of the data\n */\nexport function gzipSync(data, opts) {\n  if (!opts) opts = {};\n  var c = crc(),\n    l = data.length;\n  c.p(data);\n  var d = dopt(data, opts, gzhl(opts), 8),\n    s = d.length;\n  return gzh(d, opts), wbytes(d, s - 8, c.d()), wbytes(d, s - 4, l), d;\n}\n/**\n * Streaming single or multi-member GZIP decompression\n */\nvar Gunzip = /*#__PURE__*/function () {\n  function Gunzip(opts, cb) {\n    this.v = 1;\n    this.r = 0;\n    Inflate.call(this, opts, cb);\n  }\n  /**\n   * Pushes a chunk to be GUNZIPped\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Gunzip.prototype.push = function (chunk, final) {\n    Inflate.prototype.e.call(this, chunk);\n    this.r += chunk.length;\n    if (this.v) {\n      var p = this.p.subarray(this.v - 1);\n      var s = p.length > 3 ? gzs(p) : 4;\n      if (s > p.length) {\n        if (!final) return;\n      } else if (this.v > 1 && this.onmember) {\n        this.onmember(this.r - p.length);\n      }\n      this.p = p.subarray(s), this.v = 0;\n    }\n    // necessary to prevent TS from using the closure value\n    // This allows for workerization to function correctly\n    Inflate.prototype.c.call(this, final);\n    // process concatenated GZIP\n    if (this.s.f && !this.s.l && !final) {\n      this.v = shft(this.s.p) + 9;\n      this.s = {\n        i: 0\n      };\n      this.o = new u8(0);\n      this.push(new u8(0), final);\n    }\n  };\n  return Gunzip;\n}();\nexport { Gunzip };\n/**\n * Asynchronous streaming single or multi-member GZIP decompression\n */\nvar AsyncGunzip = /*#__PURE__*/function () {\n  function AsyncGunzip(opts, cb) {\n    var _this = this;\n    astrmify([bInflt, guze, function () {\n      return [astrm, Inflate, Gunzip];\n    }], this, StrmOpt.call(this, opts, cb), function (ev) {\n      var strm = new Gunzip(ev.data);\n      strm.onmember = function (offset) {\n        return postMessage(offset);\n      };\n      onmessage = astrm(strm);\n    }, 9, 0, function (offset) {\n      return _this.onmember && _this.onmember(offset);\n    });\n  }\n  return AsyncGunzip;\n}();\nexport { AsyncGunzip };\nexport function gunzip(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') err(7);\n  return cbify(data, opts, [bInflt, guze, function () {\n    return [gunzipSync];\n  }], function (ev) {\n    return pbf(gunzipSync(ev.data[0], ev.data[1]));\n  }, 3, cb);\n}\n/**\n * Expands GZIP data\n * @param data The data to decompress\n * @param opts The decompression options\n * @returns The decompressed version of the data\n */\nexport function gunzipSync(data, opts) {\n  var st = gzs(data);\n  if (st + 8 > data.length) err(6, 'invalid gzip data');\n  return inflt(data.subarray(st, -8), {\n    i: 2\n  }, opts && opts.out || new u8(gzl(data)), opts && opts.dictionary);\n}\n/**\n * Streaming Zlib compression\n */\nvar Zlib = /*#__PURE__*/function () {\n  function Zlib(opts, cb) {\n    this.c = adler();\n    this.v = 1;\n    Deflate.call(this, opts, cb);\n  }\n  /**\n   * Pushes a chunk to be zlibbed\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Zlib.prototype.push = function (chunk, final) {\n    this.c.p(chunk);\n    Deflate.prototype.push.call(this, chunk, final);\n  };\n  Zlib.prototype.p = function (c, f) {\n    var raw = dopt(c, this.o, this.v && (this.o.dictionary ? 6 : 2), f && 4, this.s);\n    if (this.v) zlh(raw, this.o), this.v = 0;\n    if (f) wbytes(raw, raw.length - 4, this.c.d());\n    this.ondata(raw, f);\n  };\n  /**\n   * Flushes buffered uncompressed data. Useful to immediately retrieve the\n   * zlibbed output for small inputs.\n   */\n  Zlib.prototype.flush = function () {\n    Deflate.prototype.flush.call(this);\n  };\n  return Zlib;\n}();\nexport { Zlib };\n/**\n * Asynchronous streaming Zlib compression\n */\nvar AsyncZlib = /*#__PURE__*/function () {\n  function AsyncZlib(opts, cb) {\n    astrmify([bDflt, zle, function () {\n      return [astrm, Deflate, Zlib];\n    }], this, StrmOpt.call(this, opts, cb), function (ev) {\n      var strm = new Zlib(ev.data);\n      onmessage = astrm(strm);\n    }, 10, 1);\n  }\n  return AsyncZlib;\n}();\nexport { AsyncZlib };\nexport function zlib(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') err(7);\n  return cbify(data, opts, [bDflt, zle, function () {\n    return [zlibSync];\n  }], function (ev) {\n    return pbf(zlibSync(ev.data[0], ev.data[1]));\n  }, 4, cb);\n}\n/**\n * Compress data with Zlib\n * @param data The data to compress\n * @param opts The compression options\n * @returns The zlib-compressed version of the data\n */\nexport function zlibSync(data, opts) {\n  if (!opts) opts = {};\n  var a = adler();\n  a.p(data);\n  var d = dopt(data, opts, opts.dictionary ? 6 : 2, 4);\n  return zlh(d, opts), wbytes(d, d.length - 4, a.d()), d;\n}\n/**\n * Streaming Zlib decompression\n */\nvar Unzlib = /*#__PURE__*/function () {\n  function Unzlib(opts, cb) {\n    Inflate.call(this, opts, cb);\n    this.v = opts && opts.dictionary ? 2 : 1;\n  }\n  /**\n   * Pushes a chunk to be unzlibbed\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Unzlib.prototype.push = function (chunk, final) {\n    Inflate.prototype.e.call(this, chunk);\n    if (this.v) {\n      if (this.p.length < 6 && !final) return;\n      this.p = this.p.subarray(zls(this.p, this.v - 1)), this.v = 0;\n    }\n    if (final) {\n      if (this.p.length < 4) err(6, 'invalid zlib data');\n      this.p = this.p.subarray(0, -4);\n    }\n    // necessary to prevent TS from using the closure value\n    // This allows for workerization to function correctly\n    Inflate.prototype.c.call(this, final);\n  };\n  return Unzlib;\n}();\nexport { Unzlib };\n/**\n * Asynchronous streaming Zlib decompression\n */\nvar AsyncUnzlib = /*#__PURE__*/function () {\n  function AsyncUnzlib(opts, cb) {\n    astrmify([bInflt, zule, function () {\n      return [astrm, Inflate, Unzlib];\n    }], this, StrmOpt.call(this, opts, cb), function (ev) {\n      var strm = new Unzlib(ev.data);\n      onmessage = astrm(strm);\n    }, 11, 0);\n  }\n  return AsyncUnzlib;\n}();\nexport { AsyncUnzlib };\nexport function unzlib(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') err(7);\n  return cbify(data, opts, [bInflt, zule, function () {\n    return [unzlibSync];\n  }], function (ev) {\n    return pbf(unzlibSync(ev.data[0], gopt(ev.data[1])));\n  }, 5, cb);\n}\n/**\n * Expands Zlib data\n * @param data The data to decompress\n * @param opts The decompression options\n * @returns The decompressed version of the data\n */\nexport function unzlibSync(data, opts) {\n  return inflt(data.subarray(zls(data, opts && opts.dictionary), -4), {\n    i: 2\n  }, opts && opts.out, opts && opts.dictionary);\n}\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzip as compress, AsyncGzip as AsyncCompress };\nexport { gzipSync as compressSync, Gzip as Compress };\n/**\n * Streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar Decompress = /*#__PURE__*/function () {\n  function Decompress(opts, cb) {\n    this.o = StrmOpt.call(this, opts, cb) || {};\n    this.G = Gunzip;\n    this.I = Inflate;\n    this.Z = Unzlib;\n  }\n  // init substream\n  // overriden by AsyncDecompress\n  Decompress.prototype.i = function () {\n    var _this = this;\n    this.s.ondata = function (dat, final) {\n      _this.ondata(dat, final);\n    };\n  };\n  /**\n   * Pushes a chunk to be decompressed\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Decompress.prototype.push = function (chunk, final) {\n    if (!this.ondata) err(5);\n    if (!this.s) {\n      if (this.p && this.p.length) {\n        var n = new u8(this.p.length + chunk.length);\n        n.set(this.p), n.set(chunk, this.p.length);\n      } else this.p = chunk;\n      if (this.p.length > 2) {\n        this.s = this.p[0] == 31 && this.p[1] == 139 && this.p[2] == 8 ? new this.G(this.o) : (this.p[0] & 15) != 8 || this.p[0] >> 4 > 7 || (this.p[0] << 8 | this.p[1]) % 31 ? new this.I(this.o) : new this.Z(this.o);\n        this.i();\n        this.s.push(this.p, final);\n        this.p = null;\n      }\n    } else this.s.push(chunk, final);\n  };\n  return Decompress;\n}();\nexport { Decompress };\n/**\n * Asynchronous streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar AsyncDecompress = /*#__PURE__*/function () {\n  function AsyncDecompress(opts, cb) {\n    Decompress.call(this, opts, cb);\n    this.queuedSize = 0;\n    this.G = AsyncGunzip;\n    this.I = AsyncInflate;\n    this.Z = AsyncUnzlib;\n  }\n  AsyncDecompress.prototype.i = function () {\n    var _this = this;\n    this.s.ondata = function (err, dat, final) {\n      _this.ondata(err, dat, final);\n    };\n    this.s.ondrain = function (size) {\n      _this.queuedSize -= size;\n      if (_this.ondrain) _this.ondrain(size);\n    };\n  };\n  /**\n   * Pushes a chunk to be decompressed\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  AsyncDecompress.prototype.push = function (chunk, final) {\n    this.queuedSize += chunk.length;\n    Decompress.prototype.push.call(this, chunk, final);\n  };\n  return AsyncDecompress;\n}();\nexport { AsyncDecompress };\nexport function decompress(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') err(7);\n  return data[0] == 31 && data[1] == 139 && data[2] == 8 ? gunzip(data, opts, cb) : (data[0] & 15) != 8 || data[0] >> 4 > 7 || (data[0] << 8 | data[1]) % 31 ? inflate(data, opts, cb) : unzlib(data, opts, cb);\n}\n/**\n * Expands compressed GZIP, Zlib, or raw DEFLATE data, automatically detecting the format\n * @param data The data to decompress\n * @param opts The decompression options\n * @returns The decompressed version of the data\n */\nexport function decompressSync(data, opts) {\n  return data[0] == 31 && data[1] == 139 && data[2] == 8 ? gunzipSync(data, opts) : (data[0] & 15) != 8 || data[0] >> 4 > 7 || (data[0] << 8 | data[1]) % 31 ? inflateSync(data, opts) : unzlibSync(data, opts);\n}\n// flatten a directory structure\nvar fltn = function (d, p, t, o) {\n  for (var k in d) {\n    var val = d[k],\n      n = p + k,\n      op = o;\n    if (Array.isArray(val)) op = mrg(o, val[1]), val = val[0];\n    if (val instanceof u8) t[n] = [val, op];else {\n      t[n += '/'] = [new u8(0), op];\n      fltn(val, n, t, o);\n    }\n  }\n};\n// text encoder\nvar te = typeof TextEncoder != 'undefined' && /*#__PURE__*/new TextEncoder();\n// text decoder\nvar td = typeof TextDecoder != 'undefined' && /*#__PURE__*/new TextDecoder();\n// text decoder stream\nvar tds = 0;\ntry {\n  td.decode(et, {\n    stream: true\n  });\n  tds = 1;\n} catch (e) {}\n// decode UTF8\nvar dutf8 = function (d) {\n  for (var r = '', i = 0;;) {\n    var c = d[i++];\n    var eb = (c > 127) + (c > 223) + (c > 239);\n    if (i + eb > d.length) return {\n      s: r,\n      r: slc(d, i - 1)\n    };\n    if (!eb) r += String.fromCharCode(c);else if (eb == 3) {\n      c = ((c & 15) << 18 | (d[i++] & 63) << 12 | (d[i++] & 63) << 6 | d[i++] & 63) - 65536, r += String.fromCharCode(55296 | c >> 10, 56320 | c & 1023);\n    } else if (eb & 1) r += String.fromCharCode((c & 31) << 6 | d[i++] & 63);else r += String.fromCharCode((c & 15) << 12 | (d[i++] & 63) << 6 | d[i++] & 63);\n  }\n};\n/**\n * Streaming UTF-8 decoding\n */\nvar DecodeUTF8 = /*#__PURE__*/function () {\n  /**\n   * Creates a UTF-8 decoding stream\n   * @param cb The callback to call whenever data is decoded\n   */\n  function DecodeUTF8(cb) {\n    this.ondata = cb;\n    if (tds) this.t = new TextDecoder();else this.p = et;\n  }\n  /**\n   * Pushes a chunk to be decoded from UTF-8 binary\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  DecodeUTF8.prototype.push = function (chunk, final) {\n    if (!this.ondata) err(5);\n    final = !!final;\n    if (this.t) {\n      this.ondata(this.t.decode(chunk, {\n        stream: true\n      }), final);\n      if (final) {\n        if (this.t.decode().length) err(8);\n        this.t = null;\n      }\n      return;\n    }\n    if (!this.p) err(4);\n    var dat = new u8(this.p.length + chunk.length);\n    dat.set(this.p);\n    dat.set(chunk, this.p.length);\n    var _a = dutf8(dat),\n      s = _a.s,\n      r = _a.r;\n    if (final) {\n      if (r.length) err(8);\n      this.p = null;\n    } else this.p = r;\n    this.ondata(s, final);\n  };\n  return DecodeUTF8;\n}();\nexport { DecodeUTF8 };\n/**\n * Streaming UTF-8 encoding\n */\nvar EncodeUTF8 = /*#__PURE__*/function () {\n  /**\n   * Creates a UTF-8 decoding stream\n   * @param cb The callback to call whenever data is encoded\n   */\n  function EncodeUTF8(cb) {\n    this.ondata = cb;\n  }\n  /**\n   * Pushes a chunk to be encoded to UTF-8\n   * @param chunk The string data to push\n   * @param final Whether this is the last chunk\n   */\n  EncodeUTF8.prototype.push = function (chunk, final) {\n    if (!this.ondata) err(5);\n    if (this.d) err(4);\n    this.ondata(strToU8(chunk), this.d = final || false);\n  };\n  return EncodeUTF8;\n}();\nexport { EncodeUTF8 };\n/**\n * Converts a string into a Uint8Array for use with compression/decompression methods\n * @param str The string to encode\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless decoding a binary string.\n * @returns The string encoded in UTF-8/Latin-1 binary\n */\nexport function strToU8(str, latin1) {\n  if (latin1) {\n    var ar_1 = new u8(str.length);\n    for (var i = 0; i < str.length; ++i) ar_1[i] = str.charCodeAt(i);\n    return ar_1;\n  }\n  if (te) return te.encode(str);\n  var l = str.length;\n  var ar = new u8(str.length + (str.length >> 1));\n  var ai = 0;\n  var w = function (v) {\n    ar[ai++] = v;\n  };\n  for (var i = 0; i < l; ++i) {\n    if (ai + 5 > ar.length) {\n      var n = new u8(ai + 8 + (l - i << 1));\n      n.set(ar);\n      ar = n;\n    }\n    var c = str.charCodeAt(i);\n    if (c < 128 || latin1) w(c);else if (c < 2048) w(192 | c >> 6), w(128 | c & 63);else if (c > 55295 && c < 57344) c = 65536 + (c & 1023 << 10) | str.charCodeAt(++i) & 1023, w(240 | c >> 18), w(128 | c >> 12 & 63), w(128 | c >> 6 & 63), w(128 | c & 63);else w(224 | c >> 12), w(128 | c >> 6 & 63), w(128 | c & 63);\n  }\n  return slc(ar, 0, ai);\n}\n/**\n * Converts a Uint8Array to a string\n * @param dat The data to decode to string\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless encoding to binary string.\n * @returns The original UTF-8/Latin-1 string\n */\nexport function strFromU8(dat, latin1) {\n  if (latin1) {\n    var r = '';\n    for (var i = 0; i < dat.length; i += 16384) r += String.fromCharCode.apply(null, dat.subarray(i, i + 16384));\n    return r;\n  } else if (td) {\n    return td.decode(dat);\n  } else {\n    var _a = dutf8(dat),\n      s = _a.s,\n      r = _a.r;\n    if (r.length) err(8);\n    return s;\n  }\n}\n;\n// deflate bit flag\nvar dbf = function (l) {\n  return l == 1 ? 3 : l < 6 ? 2 : l == 9 ? 1 : 0;\n};\n// skip local zip header\nvar slzh = function (d, b) {\n  return b + 30 + b2(d, b + 26) + b2(d, b + 28);\n};\n// read zip header\nvar zh = function (d, b, z) {\n  var fnl = b2(d, b + 28),\n    fn = strFromU8(d.subarray(b + 46, b + 46 + fnl), !(b2(d, b + 8) & 2048)),\n    es = b + 46 + fnl,\n    bs = b4(d, b + 20);\n  var _a = z && bs == 4294967295 ? z64e(d, es) : [bs, b4(d, b + 24), b4(d, b + 42)],\n    sc = _a[0],\n    su = _a[1],\n    off = _a[2];\n  return [b2(d, b + 10), sc, su, fn, es + b2(d, b + 30) + b2(d, b + 32), off];\n};\n// read zip64 extra field\nvar z64e = function (d, b) {\n  for (; b2(d, b) != 1; b += 4 + b2(d, b + 2));\n  return [b8(d, b + 12), b8(d, b + 4), b8(d, b + 20)];\n};\n// extra field length\nvar exfl = function (ex) {\n  var le = 0;\n  if (ex) {\n    for (var k in ex) {\n      var l = ex[k].length;\n      if (l > 65535) err(9);\n      le += l + 4;\n    }\n  }\n  return le;\n};\n// write zip header\nvar wzh = function (d, b, f, fn, u, c, ce, co) {\n  var fl = fn.length,\n    ex = f.extra,\n    col = co && co.length;\n  var exl = exfl(ex);\n  wbytes(d, b, ce != null ? 0x2014B50 : 0x4034B50), b += 4;\n  if (ce != null) d[b++] = 20, d[b++] = f.os;\n  d[b] = 20, b += 2; // spec compliance? what's that?\n  d[b++] = f.flag << 1 | (c < 0 && 8), d[b++] = u && 8;\n  d[b++] = f.compression & 255, d[b++] = f.compression >> 8;\n  var dt = new Date(f.mtime == null ? Date.now() : f.mtime),\n    y = dt.getFullYear() - 1980;\n  if (y < 0 || y > 119) err(10);\n  wbytes(d, b, y << 25 | dt.getMonth() + 1 << 21 | dt.getDate() << 16 | dt.getHours() << 11 | dt.getMinutes() << 5 | dt.getSeconds() >> 1), b += 4;\n  if (c != -1) {\n    wbytes(d, b, f.crc);\n    wbytes(d, b + 4, c < 0 ? -c - 2 : c);\n    wbytes(d, b + 8, f.size);\n  }\n  wbytes(d, b + 12, fl);\n  wbytes(d, b + 14, exl), b += 16;\n  if (ce != null) {\n    wbytes(d, b, col);\n    wbytes(d, b + 6, f.attrs);\n    wbytes(d, b + 10, ce), b += 14;\n  }\n  d.set(fn, b);\n  b += fl;\n  if (exl) {\n    for (var k in ex) {\n      var exf = ex[k],\n        l = exf.length;\n      wbytes(d, b, +k);\n      wbytes(d, b + 2, l);\n      d.set(exf, b + 4), b += 4 + l;\n    }\n  }\n  if (col) d.set(co, b), b += col;\n  return b;\n};\n// write zip footer (end of central directory)\nvar wzf = function (o, b, c, d, e) {\n  wbytes(o, b, 0x6054B50); // skip disk\n  wbytes(o, b + 8, c);\n  wbytes(o, b + 10, c);\n  wbytes(o, b + 12, d);\n  wbytes(o, b + 16, e);\n};\n/**\n * A pass-through stream to keep data uncompressed in a ZIP archive.\n */\nvar ZipPassThrough = /*#__PURE__*/function () {\n  /**\n   * Creates a pass-through stream that can be added to ZIP archives\n   * @param filename The filename to associate with this data stream\n   */\n  function ZipPassThrough(filename) {\n    this.filename = filename;\n    this.c = crc();\n    this.size = 0;\n    this.compression = 0;\n  }\n  /**\n   * Processes a chunk and pushes to the output stream. You can override this\n   * method in a subclass for custom behavior, but by default this passes\n   * the data through. You must call this.ondata(err, chunk, final) at some\n   * point in this method.\n   * @param chunk The chunk to process\n   * @param final Whether this is the last chunk\n   */\n  ZipPassThrough.prototype.process = function (chunk, final) {\n    this.ondata(null, chunk, final);\n  };\n  /**\n   * Pushes a chunk to be added. If you are subclassing this with a custom\n   * compression algorithm, note that you must push data from the source\n   * file only, pre-compression.\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  ZipPassThrough.prototype.push = function (chunk, final) {\n    if (!this.ondata) err(5);\n    this.c.p(chunk);\n    this.size += chunk.length;\n    if (final) this.crc = this.c.d();\n    this.process(chunk, final || false);\n  };\n  return ZipPassThrough;\n}();\nexport { ZipPassThrough };\n// I don't extend because TypeScript extension adds 1kB of runtime bloat\n/**\n * Streaming DEFLATE compression for ZIP archives. Prefer using AsyncZipDeflate\n * for better performance\n */\nvar ZipDeflate = /*#__PURE__*/function () {\n  /**\n   * Creates a DEFLATE stream that can be added to ZIP archives\n   * @param filename The filename to associate with this data stream\n   * @param opts The compression options\n   */\n  function ZipDeflate(filename, opts) {\n    var _this = this;\n    if (!opts) opts = {};\n    ZipPassThrough.call(this, filename);\n    this.d = new Deflate(opts, function (dat, final) {\n      _this.ondata(null, dat, final);\n    });\n    this.compression = 8;\n    this.flag = dbf(opts.level);\n  }\n  ZipDeflate.prototype.process = function (chunk, final) {\n    try {\n      this.d.push(chunk, final);\n    } catch (e) {\n      this.ondata(e, null, final);\n    }\n  };\n  /**\n   * Pushes a chunk to be deflated\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  ZipDeflate.prototype.push = function (chunk, final) {\n    ZipPassThrough.prototype.push.call(this, chunk, final);\n  };\n  return ZipDeflate;\n}();\nexport { ZipDeflate };\n/**\n * Asynchronous streaming DEFLATE compression for ZIP archives\n */\nvar AsyncZipDeflate = /*#__PURE__*/function () {\n  /**\n   * Creates an asynchronous DEFLATE stream that can be added to ZIP archives\n   * @param filename The filename to associate with this data stream\n   * @param opts The compression options\n   */\n  function AsyncZipDeflate(filename, opts) {\n    var _this = this;\n    if (!opts) opts = {};\n    ZipPassThrough.call(this, filename);\n    this.d = new AsyncDeflate(opts, function (err, dat, final) {\n      _this.ondata(err, dat, final);\n    });\n    this.compression = 8;\n    this.flag = dbf(opts.level);\n    this.terminate = this.d.terminate;\n  }\n  AsyncZipDeflate.prototype.process = function (chunk, final) {\n    this.d.push(chunk, final);\n  };\n  /**\n   * Pushes a chunk to be deflated\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  AsyncZipDeflate.prototype.push = function (chunk, final) {\n    ZipPassThrough.prototype.push.call(this, chunk, final);\n  };\n  return AsyncZipDeflate;\n}();\nexport { AsyncZipDeflate };\n// TODO: Better tree shaking\n/**\n * A zippable archive to which files can incrementally be added\n */\nvar Zip = /*#__PURE__*/function () {\n  /**\n   * Creates an empty ZIP archive to which files can be added\n   * @param cb The callback to call whenever data for the generated ZIP archive\n   *           is available\n   */\n  function Zip(cb) {\n    this.ondata = cb;\n    this.u = [];\n    this.d = 1;\n  }\n  /**\n   * Adds a file to the ZIP archive\n   * @param file The file stream to add\n   */\n  Zip.prototype.add = function (file) {\n    var _this = this;\n    if (!this.ondata) err(5);\n    // finishing or finished\n    if (this.d & 2) this.ondata(err(4 + (this.d & 1) * 8, 0, 1), null, false);else {\n      var f = strToU8(file.filename),\n        fl_1 = f.length;\n      var com = file.comment,\n        o = com && strToU8(com);\n      var u = fl_1 != file.filename.length || o && com.length != o.length;\n      var hl_1 = fl_1 + exfl(file.extra) + 30;\n      if (fl_1 > 65535) this.ondata(err(11, 0, 1), null, false);\n      var header = new u8(hl_1);\n      wzh(header, 0, file, f, u, -1);\n      var chks_1 = [header];\n      var pAll_1 = function () {\n        for (var _i = 0, chks_2 = chks_1; _i < chks_2.length; _i++) {\n          var chk = chks_2[_i];\n          _this.ondata(null, chk, false);\n        }\n        chks_1 = [];\n      };\n      var tr_1 = this.d;\n      this.d = 0;\n      var ind_1 = this.u.length;\n      var uf_1 = mrg(file, {\n        f: f,\n        u: u,\n        o: o,\n        t: function () {\n          if (file.terminate) file.terminate();\n        },\n        r: function () {\n          pAll_1();\n          if (tr_1) {\n            var nxt = _this.u[ind_1 + 1];\n            if (nxt) nxt.r();else _this.d = 1;\n          }\n          tr_1 = 1;\n        }\n      });\n      var cl_1 = 0;\n      file.ondata = function (err, dat, final) {\n        if (err) {\n          _this.ondata(err, dat, final);\n          _this.terminate();\n        } else {\n          cl_1 += dat.length;\n          chks_1.push(dat);\n          if (final) {\n            var dd = new u8(16);\n            wbytes(dd, 0, 0x8074B50);\n            wbytes(dd, 4, file.crc);\n            wbytes(dd, 8, cl_1);\n            wbytes(dd, 12, file.size);\n            chks_1.push(dd);\n            uf_1.c = cl_1, uf_1.b = hl_1 + cl_1 + 16, uf_1.crc = file.crc, uf_1.size = file.size;\n            if (tr_1) uf_1.r();\n            tr_1 = 1;\n          } else if (tr_1) pAll_1();\n        }\n      };\n      this.u.push(uf_1);\n    }\n  };\n  /**\n   * Ends the process of adding files and prepares to emit the final chunks.\n   * This *must* be called after adding all desired files for the resulting\n   * ZIP file to work properly.\n   */\n  Zip.prototype.end = function () {\n    var _this = this;\n    if (this.d & 2) {\n      this.ondata(err(4 + (this.d & 1) * 8, 0, 1), null, true);\n      return;\n    }\n    if (this.d) this.e();else this.u.push({\n      r: function () {\n        if (!(_this.d & 1)) return;\n        _this.u.splice(-1, 1);\n        _this.e();\n      },\n      t: function () {}\n    });\n    this.d = 3;\n  };\n  Zip.prototype.e = function () {\n    var bt = 0,\n      l = 0,\n      tl = 0;\n    for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n      var f = _a[_i];\n      tl += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0);\n    }\n    var out = new u8(tl + 22);\n    for (var _b = 0, _c = this.u; _b < _c.length; _b++) {\n      var f = _c[_b];\n      wzh(out, bt, f, f.f, f.u, -f.c - 2, l, f.o);\n      bt += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0), l += f.b;\n    }\n    wzf(out, bt, this.u.length, tl, l);\n    this.ondata(null, out, true);\n    this.d = 2;\n  };\n  /**\n   * A method to terminate any internal workers used by the stream. Subsequent\n   * calls to add() will fail.\n   */\n  Zip.prototype.terminate = function () {\n    for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n      var f = _a[_i];\n      f.t();\n    }\n    this.d = 2;\n  };\n  return Zip;\n}();\nexport { Zip };\nexport function zip(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') err(7);\n  var r = {};\n  fltn(data, '', r, opts);\n  var k = Object.keys(r);\n  var lft = k.length,\n    o = 0,\n    tot = 0;\n  var slft = lft,\n    files = new Array(lft);\n  var term = [];\n  var tAll = function () {\n    for (var i = 0; i < term.length; ++i) term[i]();\n  };\n  var cbd = function (a, b) {\n    mt(function () {\n      cb(a, b);\n    });\n  };\n  mt(function () {\n    cbd = cb;\n  });\n  var cbf = function () {\n    var out = new u8(tot + 22),\n      oe = o,\n      cdl = tot - o;\n    tot = 0;\n    for (var i = 0; i < slft; ++i) {\n      var f = files[i];\n      try {\n        var l = f.c.length;\n        wzh(out, tot, f, f.f, f.u, l);\n        var badd = 30 + f.f.length + exfl(f.extra);\n        var loc = tot + badd;\n        out.set(f.c, loc);\n        wzh(out, o, f, f.f, f.u, l, tot, f.m), o += 16 + badd + (f.m ? f.m.length : 0), tot = loc + l;\n      } catch (e) {\n        return cbd(e, null);\n      }\n    }\n    wzf(out, o, files.length, cdl, oe);\n    cbd(null, out);\n  };\n  if (!lft) cbf();\n  var _loop_1 = function (i) {\n    var fn = k[i];\n    var _a = r[fn],\n      file = _a[0],\n      p = _a[1];\n    var c = crc(),\n      size = file.length;\n    c.p(file);\n    var f = strToU8(fn),\n      s = f.length;\n    var com = p.comment,\n      m = com && strToU8(com),\n      ms = m && m.length;\n    var exl = exfl(p.extra);\n    var compression = p.level == 0 ? 0 : 8;\n    var cbl = function (e, d) {\n      if (e) {\n        tAll();\n        cbd(e, null);\n      } else {\n        var l = d.length;\n        files[i] = mrg(p, {\n          size: size,\n          crc: c.d(),\n          c: d,\n          f: f,\n          m: m,\n          u: s != fn.length || m && com.length != ms,\n          compression: compression\n        });\n        o += 30 + s + exl + l;\n        tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n        if (! --lft) cbf();\n      }\n    };\n    if (s > 65535) cbl(err(11, 0, 1), null);\n    if (!compression) cbl(null, file);else if (size < 160000) {\n      try {\n        cbl(null, deflateSync(file, p));\n      } catch (e) {\n        cbl(e, null);\n      }\n    } else term.push(deflate(file, p, cbl));\n  };\n  // Cannot use lft because it can decrease\n  for (var i = 0; i < slft; ++i) {\n    _loop_1(i);\n  }\n  return tAll;\n}\n/**\n * Synchronously creates a ZIP file. Prefer using `zip` for better performance\n * with more than one file.\n * @param data The directory structure for the ZIP archive\n * @param opts The main options, merged with per-file options\n * @returns The generated ZIP archive\n */\nexport function zipSync(data, opts) {\n  if (!opts) opts = {};\n  var r = {};\n  var files = [];\n  fltn(data, '', r, opts);\n  var o = 0;\n  var tot = 0;\n  for (var fn in r) {\n    var _a = r[fn],\n      file = _a[0],\n      p = _a[1];\n    var compression = p.level == 0 ? 0 : 8;\n    var f = strToU8(fn),\n      s = f.length;\n    var com = p.comment,\n      m = com && strToU8(com),\n      ms = m && m.length;\n    var exl = exfl(p.extra);\n    if (s > 65535) err(11);\n    var d = compression ? deflateSync(file, p) : file,\n      l = d.length;\n    var c = crc();\n    c.p(file);\n    files.push(mrg(p, {\n      size: file.length,\n      crc: c.d(),\n      c: d,\n      f: f,\n      m: m,\n      u: s != fn.length || m && com.length != ms,\n      o: o,\n      compression: compression\n    }));\n    o += 30 + s + exl + l;\n    tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n  }\n  var out = new u8(tot + 22),\n    oe = o,\n    cdl = tot - o;\n  for (var i = 0; i < files.length; ++i) {\n    var f = files[i];\n    wzh(out, f.o, f, f.f, f.u, f.c.length);\n    var badd = 30 + f.f.length + exfl(f.extra);\n    out.set(f.c, f.o + badd);\n    wzh(out, o, f, f.f, f.u, f.c.length, f.o, f.m), o += 16 + badd + (f.m ? f.m.length : 0);\n  }\n  wzf(out, o, files.length, cdl, oe);\n  return out;\n}\n/**\n * Streaming pass-through decompression for ZIP archives\n */\nvar UnzipPassThrough = /*#__PURE__*/function () {\n  function UnzipPassThrough() {}\n  UnzipPassThrough.prototype.push = function (data, final) {\n    this.ondata(null, data, final);\n  };\n  UnzipPassThrough.compression = 0;\n  return UnzipPassThrough;\n}();\nexport { UnzipPassThrough };\n/**\n * Streaming DEFLATE decompression for ZIP archives. Prefer AsyncZipInflate for\n * better performance.\n */\nvar UnzipInflate = /*#__PURE__*/function () {\n  /**\n   * Creates a DEFLATE decompression that can be used in ZIP archives\n   */\n  function UnzipInflate() {\n    var _this = this;\n    this.i = new Inflate(function (dat, final) {\n      _this.ondata(null, dat, final);\n    });\n  }\n  UnzipInflate.prototype.push = function (data, final) {\n    try {\n      this.i.push(data, final);\n    } catch (e) {\n      this.ondata(e, null, final);\n    }\n  };\n  UnzipInflate.compression = 8;\n  return UnzipInflate;\n}();\nexport { UnzipInflate };\n/**\n * Asynchronous streaming DEFLATE decompression for ZIP archives\n */\nvar AsyncUnzipInflate = /*#__PURE__*/function () {\n  /**\n   * Creates a DEFLATE decompression that can be used in ZIP archives\n   */\n  function AsyncUnzipInflate(_, sz) {\n    var _this = this;\n    if (sz < 320000) {\n      this.i = new Inflate(function (dat, final) {\n        _this.ondata(null, dat, final);\n      });\n    } else {\n      this.i = new AsyncInflate(function (err, dat, final) {\n        _this.ondata(err, dat, final);\n      });\n      this.terminate = this.i.terminate;\n    }\n  }\n  AsyncUnzipInflate.prototype.push = function (data, final) {\n    if (this.i.terminate) data = slc(data, 0);\n    this.i.push(data, final);\n  };\n  AsyncUnzipInflate.compression = 8;\n  return AsyncUnzipInflate;\n}();\nexport { AsyncUnzipInflate };\n/**\n * A ZIP archive decompression stream that emits files as they are discovered\n */\nvar Unzip = /*#__PURE__*/function () {\n  /**\n   * Creates a ZIP decompression stream\n   * @param cb The callback to call whenever a file in the ZIP archive is found\n   */\n  function Unzip(cb) {\n    this.onfile = cb;\n    this.k = [];\n    this.o = {\n      0: UnzipPassThrough\n    };\n    this.p = et;\n  }\n  /**\n   * Pushes a chunk to be unzipped\n   * @param chunk The chunk to push\n   * @param final Whether this is the last chunk\n   */\n  Unzip.prototype.push = function (chunk, final) {\n    var _this = this;\n    if (!this.onfile) err(5);\n    if (!this.p) err(4);\n    if (this.c > 0) {\n      var len = Math.min(this.c, chunk.length);\n      var toAdd = chunk.subarray(0, len);\n      this.c -= len;\n      if (this.d) this.d.push(toAdd, !this.c);else this.k[0].push(toAdd);\n      chunk = chunk.subarray(len);\n      if (chunk.length) return this.push(chunk, final);\n    } else {\n      var f = 0,\n        i = 0,\n        is = void 0,\n        buf = void 0;\n      if (!this.p.length) buf = chunk;else if (!chunk.length) buf = this.p;else {\n        buf = new u8(this.p.length + chunk.length);\n        buf.set(this.p), buf.set(chunk, this.p.length);\n      }\n      var l = buf.length,\n        oc = this.c,\n        add = oc && this.d;\n      var _loop_2 = function () {\n        var _a;\n        var sig = b4(buf, i);\n        if (sig == 0x4034B50) {\n          f = 1, is = i;\n          this_1.d = null;\n          this_1.c = 0;\n          var bf = b2(buf, i + 6),\n            cmp_1 = b2(buf, i + 8),\n            u = bf & 2048,\n            dd = bf & 8,\n            fnl = b2(buf, i + 26),\n            es = b2(buf, i + 28);\n          if (l > i + 30 + fnl + es) {\n            var chks_3 = [];\n            this_1.k.unshift(chks_3);\n            f = 2;\n            var sc_1 = b4(buf, i + 18),\n              su_1 = b4(buf, i + 22);\n            var fn_1 = strFromU8(buf.subarray(i + 30, i += 30 + fnl), !u);\n            if (sc_1 == 4294967295) {\n              _a = dd ? [-2] : z64e(buf, i), sc_1 = _a[0], su_1 = _a[1];\n            } else if (dd) sc_1 = -1;\n            i += es;\n            this_1.c = sc_1;\n            var d_1;\n            var file_1 = {\n              name: fn_1,\n              compression: cmp_1,\n              start: function () {\n                if (!file_1.ondata) err(5);\n                if (!sc_1) file_1.ondata(null, et, true);else {\n                  var ctr = _this.o[cmp_1];\n                  if (!ctr) file_1.ondata(err(14, 'unknown compression type ' + cmp_1, 1), null, false);\n                  d_1 = sc_1 < 0 ? new ctr(fn_1) : new ctr(fn_1, sc_1, su_1);\n                  d_1.ondata = function (err, dat, final) {\n                    file_1.ondata(err, dat, final);\n                  };\n                  for (var _i = 0, chks_4 = chks_3; _i < chks_4.length; _i++) {\n                    var dat = chks_4[_i];\n                    d_1.push(dat, false);\n                  }\n                  if (_this.k[0] == chks_3 && _this.c) _this.d = d_1;else d_1.push(et, true);\n                }\n              },\n              terminate: function () {\n                if (d_1 && d_1.terminate) d_1.terminate();\n              }\n            };\n            if (sc_1 >= 0) file_1.size = sc_1, file_1.originalSize = su_1;\n            this_1.onfile(file_1);\n          }\n          return \"break\";\n        } else if (oc) {\n          if (sig == 0x8074B50) {\n            is = i += 12 + (oc == -2 && 8), f = 3, this_1.c = 0;\n            return \"break\";\n          } else if (sig == 0x2014B50) {\n            is = i -= 4, f = 3, this_1.c = 0;\n            return \"break\";\n          }\n        }\n      };\n      var this_1 = this;\n      for (; i < l - 4; ++i) {\n        var state_1 = _loop_2();\n        if (state_1 === \"break\") break;\n      }\n      this.p = et;\n      if (oc < 0) {\n        var dat = f ? buf.subarray(0, is - 12 - (oc == -2 && 8) - (b4(buf, is - 16) == 0x8074B50 && 4)) : buf.subarray(0, i);\n        if (add) add.push(dat, !!f);else this.k[+(f == 2)].push(dat);\n      }\n      if (f & 2) return this.push(buf.subarray(i), final);\n      this.p = buf.subarray(i);\n    }\n    if (final) {\n      if (this.c) err(13);\n      this.p = null;\n    }\n  };\n  /**\n   * Registers a decoder with the stream, allowing for files compressed with\n   * the compression type provided to be expanded correctly\n   * @param decoder The decoder constructor\n   */\n  Unzip.prototype.register = function (decoder) {\n    this.o[decoder.compression] = decoder;\n  };\n  return Unzip;\n}();\nexport { Unzip };\nvar mt = typeof queueMicrotask == 'function' ? queueMicrotask : typeof setTimeout == 'function' ? setTimeout : function (fn) {\n  fn();\n};\nexport function unzip(data, opts, cb) {\n  if (!cb) cb = opts, opts = {};\n  if (typeof cb != 'function') err(7);\n  var term = [];\n  var tAll = function () {\n    for (var i = 0; i < term.length; ++i) term[i]();\n  };\n  var files = {};\n  var cbd = function (a, b) {\n    mt(function () {\n      cb(a, b);\n    });\n  };\n  mt(function () {\n    cbd = cb;\n  });\n  var e = data.length - 22;\n  for (; b4(data, e) != 0x6054B50; --e) {\n    if (!e || data.length - e > 65558) {\n      cbd(err(13, 0, 1), null);\n      return tAll;\n    }\n  }\n  ;\n  var lft = b2(data, e + 8);\n  if (lft) {\n    var c = lft;\n    var o = b4(data, e + 16);\n    var z = o == 4294967295 || c == 65535;\n    if (z) {\n      var ze = b4(data, e - 12);\n      z = b4(data, ze) == 0x6064B50;\n      if (z) {\n        c = lft = b4(data, ze + 32);\n        o = b4(data, ze + 48);\n      }\n    }\n    var fltr = opts && opts.filter;\n    var _loop_3 = function (i) {\n      var _a = zh(data, o, z),\n        c_1 = _a[0],\n        sc = _a[1],\n        su = _a[2],\n        fn = _a[3],\n        no = _a[4],\n        off = _a[5],\n        b = slzh(data, off);\n      o = no;\n      var cbl = function (e, d) {\n        if (e) {\n          tAll();\n          cbd(e, null);\n        } else {\n          if (d) files[fn] = d;\n          if (! --lft) cbd(null, files);\n        }\n      };\n      if (!fltr || fltr({\n        name: fn,\n        size: sc,\n        originalSize: su,\n        compression: c_1\n      })) {\n        if (!c_1) cbl(null, slc(data, b, b + sc));else if (c_1 == 8) {\n          var infl = data.subarray(b, b + sc);\n          // Synchronously decompress under 512KB, or barely-compressed data\n          if (su < 524288 || sc > 0.8 * su) {\n            try {\n              cbl(null, inflateSync(infl, {\n                out: new u8(su)\n              }));\n            } catch (e) {\n              cbl(e, null);\n            }\n          } else term.push(inflate(infl, {\n            size: su\n          }, cbl));\n        } else cbl(err(14, 'unknown compression type ' + c_1, 1), null);\n      } else cbl(null, null);\n    };\n    for (var i = 0; i < c; ++i) {\n      _loop_3(i);\n    }\n  } else cbd(null, {});\n  return tAll;\n}\n/**\n * Synchronously decompresses a ZIP archive. Prefer using `unzip` for better\n * performance with more than one file.\n * @param data The raw compressed ZIP file\n * @param opts The ZIP extraction options\n * @returns The decompressed files\n */\nexport function unzipSync(data, opts) {\n  var files = {};\n  var e = data.length - 22;\n  for (; b4(data, e) != 0x6054B50; --e) {\n    if (!e || data.length - e > 65558) err(13);\n  }\n  ;\n  var c = b2(data, e + 8);\n  if (!c) return {};\n  var o = b4(data, e + 16);\n  var z = o == 4294967295 || c == 65535;\n  if (z) {\n    var ze = b4(data, e - 12);\n    z = b4(data, ze) == 0x6064B50;\n    if (z) {\n      c = b4(data, ze + 32);\n      o = b4(data, ze + 48);\n    }\n  }\n  var fltr = opts && opts.filter;\n  for (var i = 0; i < c; ++i) {\n    var _a = zh(data, o, z),\n      c_2 = _a[0],\n      sc = _a[1],\n      su = _a[2],\n      fn = _a[3],\n      no = _a[4],\n      off = _a[5],\n      b = slzh(data, off);\n    o = no;\n    if (!fltr || fltr({\n      name: fn,\n      size: sc,\n      originalSize: su,\n      compression: c_2\n    })) {\n      if (!c_2) files[fn] = slc(data, b, b + sc);else if (c_2 == 8) files[fn] = inflateSync(data.subarray(b, b + sc), {\n        out: new u8(su)\n      });else err(14, 'unknown compression type ' + c_2);\n    }\n  }\n  return files;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}