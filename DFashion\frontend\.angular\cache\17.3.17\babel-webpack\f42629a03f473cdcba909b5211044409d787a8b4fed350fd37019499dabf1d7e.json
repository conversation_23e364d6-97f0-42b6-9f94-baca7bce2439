{"ast": null, "code": "import _asyncToGenerator from \"E:/Fahion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement, i as forceUpdate } from './index-a1a47f01.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { c as createNotchController } from './notch-controller-6bd3e0f9.js';\nimport { i as isOptionSelected, c as compareOptions } from './compare-with-utils-a96ff2ea.js';\nimport { k as inheritAttributes, f as focusVisibleElement, h as findItemLabel, d as renderHiddenInput, e as getAriaLabel } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { c as popoverController, b as actionSheetController, a as alertController, s as safeCall } from './overlays-b874c3c3.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { h as hostContext, c as createColorClasses, g as getClassMap } from './theme-01f3f29c.js';\nimport { w as watchForOptions } from './watch-options-c2911ace.js';\nimport { w as chevronExpand, q as caretDownSharp } from './index-f7dc70ba.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nimport './index-a5d50daf.js';\nimport './hardware-back-button-6107a37c.js';\nimport './framework-delegate-ed4ba327.js';\nconst selectIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:0.6;--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(:not(.legacy-select)){width:100%;min-height:44px}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.legacy-select){-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.in-item:not(.legacy-select)){-ms-flex:1 1 0px;flex:1 1 0}:host(.in-item.legacy-select){position:static;max-width:45%}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]:not(.legacy-select)),:host([slot=end]:not(.legacy-select)){width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}:host(.legacy-select) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-select) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-select) label{left:0}:host-context([dir=rtl]):host(.legacy-select) label,:host-context([dir=rtl]).legacy-select label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-select:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-select) label::-moz-focus-inner{border:0}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.legacy-select){--padding-top:10px;--padding-end:8px;--padding-bottom:10px;--padding-start:16px}.select-icon{width:1.125rem;height:1.125rem;color:var(--ion-color-step-650, #595959)}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 1.125rem - 4px)}:host(.select-disabled){opacity:0.3}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonSelectIosStyle0 = selectIosCss;\nconst selectMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:0.6;--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(:not(.legacy-select)){width:100%;min-height:44px}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.legacy-select){-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.in-item:not(.legacy-select)){-ms-flex:1 1 0px;flex:1 1 0}:host(.in-item.legacy-select){position:static;max-width:45%}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]:not(.legacy-select)),:host([slot=end]:not(.legacy-select)){width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}:host(.legacy-select) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-select) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-select) label{left:0}:host-context([dir=rtl]):host(.legacy-select) label,:host-context([dir=rtl]).legacy-select label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-select:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-select) label::-moz-focus-inner{border:0}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.select-fill-solid){--background:var(--ion-color-step-50, #f2f2f2);--border-color:var(--ion-color-step-500, gray);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-solid) .select-wrapper{border-bottom:var(--border-width) var(--border-style) var(--border-color)}:host(.has-focus.select-fill-solid.ion-valid),:host(.select-fill-solid.ion-touched.ion-invalid){--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-bottom{border-top:none}@media (any-hover: hover){:host(.select-fill-solid:hover){--background:var(--ion-color-step-100, #e6e6e6);--border-color:var(--ion-color-step-750, #404040)}}:host(.select-fill-solid.select-expanded),:host(.select-fill-solid.ion-focused){--background:var(--ion-color-step-150, #d9d9d9);--border-color:var(--ion-color-step-750, #404040)}:host(.select-fill-solid) .select-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}:host-context([dir=rtl]):host(.select-fill-solid) .select-wrapper,:host-context([dir=rtl]).select-fill-solid .select-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}@supports selector(:dir(rtl)){:host(.select-fill-solid:dir(rtl)) .select-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}}:host(.label-floating.select-fill-solid) .label-text-wrapper{max-width:calc(100% / 0.75)}:host(.select-fill-outline){--border-color:var(--ion-color-step-300, #b3b3b3);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-outline.select-shape-round){--border-radius:28px;--padding-start:32px;--padding-end:32px}:host(.has-focus.select-fill-outline.ion-valid),:host(.select-fill-outline.ion-touched.ion-invalid){--border-color:var(--highlight-color)}@media (any-hover: hover){:host(.select-fill-outline:hover){--border-color:var(--ion-color-step-750, #404040)}}:host(.select-fill-outline.select-expanded),:host(.select-fill-outline.ion-focused){--border-width:2px;--border-color:var(--highlight-color)}:host(.select-fill-outline) .select-bottom{border-top:none}:host(.select-fill-outline) .select-wrapper{border-bottom:none}:host(.select-ltr.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-fill-outline.select-label-placement-floating) .label-text-wrapper{position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .label-text-wrapper{position:relative;z-index:1}:host(.label-floating.select-fill-outline) .label-text-wrapper{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}:host(.select-fill-outline.select-label-placement-stacked) select,:host(.select-fill-outline.select-label-placement-floating) select{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}:host(.select-fill-outline) .select-outline-container{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-end{pointer-events:none}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-notch,:host(.select-fill-outline) .select-outline-end{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.select-fill-outline) .select-outline-notch{max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .notch-spacer{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none}:host(.select-fill-outline) .select-outline-start{-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color)}:host(.select-ltr.select-fill-outline) .select-outline-start{border-radius:var(--border-radius) 0px 0px var(--border-radius)}:host(.select-rtl.select-fill-outline) .select-outline-start{border-radius:0px var(--border-radius) var(--border-radius) 0px}:host(.select-fill-outline) .select-outline-start{width:calc(var(--padding-start) - 4px)}:host(.select-fill-outline) .select-outline-end{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color)}:host(.select-ltr.select-fill-outline) .select-outline-end{border-radius:0px var(--border-radius) var(--border-radius) 0px}:host(.select-rtl.select-fill-outline) .select-outline-end{border-radius:var(--border-radius) 0px 0px var(--border-radius)}:host(.select-fill-outline) .select-outline-end{-ms-flex-positive:1;flex-grow:1}:host(.label-floating.select-fill-outline) .select-outline-notch{border-top:none}:host{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))))}:host(.legacy-select){--padding-top:10px;--padding-end:0;--padding-bottom:10px;--padding-start:16px}.select-icon{width:0.8125rem;-webkit-transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);color:var(--ion-color-step-500, gray)}:host(.select-label-placement-floating.select-expanded) .label-text-wrapper,:host(.select-label-placement-floating.ion-focused) .label-text-wrapper,:host(.select-label-placement-stacked.select-expanded) .label-text-wrapper,:host(.select-label-placement-stacked.ion-focused) .label-text-wrapper{color:var(--highlight-color)}:host(.has-focus.select-label-placement-floating.ion-valid) .label-text-wrapper,:host(.select-label-placement-floating.ion-touched.ion-invalid) .label-text-wrapper,:host(.has-focus.select-label-placement-stacked.ion-valid) .label-text-wrapper,:host(.select-label-placement-stacked.ion-touched.ion-invalid) .label-text-wrapper{color:var(--highlight-color)}.select-highlight{bottom:-1px;position:absolute;width:100%;height:2px;-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}@supports (inset-inline-start: 0){.select-highlight{inset-inline-start:0}}@supports not (inset-inline-start: 0){.select-highlight{left:0}:host-context([dir=rtl]) .select-highlight{left:unset;right:unset;right:0}[dir=rtl] .select-highlight{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.select-highlight:dir(rtl){left:unset;right:unset;right:0}}}:host(.select-expanded) .select-highlight,:host(.ion-focused) .select-highlight{-webkit-transform:scale(1);transform:scale(1)}:host(.in-item) .select-highlight{bottom:0}@supports (inset-inline-start: 0){:host(.in-item) .select-highlight{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.in-item) .select-highlight{left:0}:host-context([dir=rtl]):host(.in-item) .select-highlight,:host-context([dir=rtl]).in-item .select-highlight{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.in-item:dir(rtl)) .select-highlight{left:unset;right:unset;right:0}}}:host(.select-expanded:not(.legacy-select):not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.select-expanded) .select-wrapper .select-icon,:host(.has-focus.ion-valid) .select-wrapper .select-icon,:host(.ion-touched.ion-invalid) .select-wrapper .select-icon,:host(.ion-focused) .select-wrapper .select-icon{color:var(--highlight-color)}:host-context(.item-label-stacked) .select-icon,:host-context(.item-label-floating:not(.item-fill-outline)) .select-icon,:host-context(.item-label-floating.item-fill-outline){-webkit-transform:translate3d(0,  -9px,  0);transform:translate3d(0,  -9px,  0)}:host-context(.item-has-focus):host(:not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host-context(.item-has-focus.item-label-stacked):host(:not(.has-expanded-icon)) .select-icon,:host-context(.item-has-focus.item-label-floating:not(.item-fill-outline)):host(:not(.has-expanded-icon)) .select-icon{-webkit-transform:translate3d(0,  -9px,  0) rotate(180deg);transform:translate3d(0,  -9px,  0) rotate(180deg)}:host(.select-shape-round){--border-radius:16px}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 0.8125rem - 4px)}:host(.select-disabled){opacity:0.38}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonSelectMdStyle0 = selectMdCss;\nconst Select = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionCancel = createEvent(this, \"ionCancel\", 7);\n    this.ionDismiss = createEvent(this, \"ionDismiss\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.inputId = `ion-sel-${selectIds++}`;\n    this.inheritedAttributes = {};\n    // This flag ensures we log the deprecation warning at most once.\n    this.hasLoggedDeprecationWarning = false;\n    this.onClick = ev => {\n      const target = ev.target;\n      const closestSlot = target.closest('[slot=\"start\"], [slot=\"end\"]');\n      if (target === this.el || closestSlot === null) {\n        this.setFocus();\n        this.open(ev);\n      } else {\n        /**\n         * Prevent clicks to the start/end slots from opening the select.\n         * We ensure the target isn't this element in case the select is slotted\n         * in, for example, an item. This would prevent the select from ever\n         * being opened since the element itself has slot=\"start\"/\"end\".\n         *\n         * Clicking a slotted element also causes a click\n         * on the <label> element (since it wraps the slots).\n         * Clicking <label> dispatches another click event on\n         * the native form control that then bubbles up to this\n         * listener. This additional event targets the host\n         * element, so the select overlay is opened.\n         *\n         * When the slotted elements are clicked (and therefore\n         * the ancestor <label> element) we want to prevent the label\n         * from dispatching another click event.\n         *\n         * Do not call stopPropagation() because this will cause\n         * click handlers on the slotted elements to never fire in React.\n         * When developers do onClick in React a native \"click\" listener\n         * is added on the root element, not the slotted element. When that\n         * native click listener fires, React then dispatches the synthetic\n         * click event on the slotted element. However, if stopPropagation\n         * is called then the native click event will never bubble up\n         * to the root element.\n         */\n        ev.preventDefault();\n      }\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.isExpanded = false;\n    this.cancelText = 'Cancel';\n    this.color = undefined;\n    this.compareWith = undefined;\n    this.disabled = false;\n    this.fill = undefined;\n    this.interface = 'alert';\n    this.interfaceOptions = {};\n    this.justify = 'space-between';\n    this.label = undefined;\n    this.labelPlacement = 'start';\n    this.legacy = undefined;\n    this.multiple = false;\n    this.name = this.inputId;\n    this.okText = 'OK';\n    this.placeholder = undefined;\n    this.selectedText = undefined;\n    this.toggleIcon = undefined;\n    this.expandedIcon = undefined;\n    this.shape = undefined;\n    this.value = undefined;\n  }\n  styleChanged() {\n    this.emitStyle();\n  }\n  setValue(value) {\n    this.value = value;\n    this.ionChange.emit({\n      value\n    });\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n  }\n  connectedCallback() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const {\n        el\n      } = _this;\n      _this.legacyFormController = createLegacyFormController(el);\n      _this.notchController = createNotchController(el, () => _this.notchSpacerEl, () => _this.labelSlot);\n      _this.updateOverlayOptions();\n      _this.emitStyle();\n      _this.mutationO = watchForOptions(_this.el, 'ion-select-option', /*#__PURE__*/_asyncToGenerator(function* () {\n        _this.updateOverlayOptions();\n        /**\n         * We need to re-render the component\n         * because one of the new ion-select-option\n         * elements may match the value. In this case,\n         * the rendered selected text should be updated.\n         */\n        forceUpdate(_this);\n      }));\n    })();\n  }\n  disconnectedCallback() {\n    if (this.mutationO) {\n      this.mutationO.disconnect();\n      this.mutationO = undefined;\n    }\n    if (this.notchController) {\n      this.notchController.destroy();\n      this.notchController = undefined;\n    }\n  }\n  /**\n   * Open the select overlay. The overlay is either an alert, action sheet, or popover,\n   * depending on the `interface` property on the `ion-select`.\n   *\n   * @param event The user interface event that called the open.\n   */\n  open(event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.disabled || _this2.isExpanded) {\n        return undefined;\n      }\n      _this2.isExpanded = true;\n      const overlay = _this2.overlay = yield _this2.createOverlay(event);\n      overlay.onDidDismiss().then(() => {\n        _this2.overlay = undefined;\n        _this2.isExpanded = false;\n        _this2.ionDismiss.emit();\n        _this2.setFocus();\n      });\n      yield overlay.present();\n      // focus selected option for popovers\n      if (_this2.interface === 'popover') {\n        const indexOfSelected = _this2.childOpts.map(o => o.value).indexOf(_this2.value);\n        if (indexOfSelected > -1) {\n          const selectedItem = overlay.querySelector(`.select-interface-option:nth-child(${indexOfSelected + 1})`);\n          if (selectedItem) {\n            focusVisibleElement(selectedItem);\n            /**\n             * Browsers such as Firefox do not\n             * correctly delegate focus when manually\n             * focusing an element with delegatesFocus.\n             * We work around this by manually focusing\n             * the interactive element.\n             * ion-radio and ion-checkbox are the only\n             * elements that ion-select-popover uses, so\n             * we only need to worry about those two components\n             * when focusing.\n             */\n            const interactiveEl = selectedItem.querySelector('ion-radio, ion-checkbox');\n            if (interactiveEl) {\n              interactiveEl.focus();\n            }\n          }\n        } else {\n          /**\n           * If no value is set then focus the first enabled option.\n           */\n          const firstEnabledOption = overlay.querySelector('ion-radio:not(.radio-disabled), ion-checkbox:not(.checkbox-disabled)');\n          if (firstEnabledOption) {\n            focusVisibleElement(firstEnabledOption.closest('ion-item'));\n            /**\n             * Focus the option for the same reason as we do above.\n             */\n            firstEnabledOption.focus();\n          }\n        }\n      }\n      return overlay;\n    })();\n  }\n  createOverlay(ev) {\n    let selectInterface = this.interface;\n    if (selectInterface === 'action-sheet' && this.multiple) {\n      console.warn(`Select interface cannot be \"${selectInterface}\" with a multi-value select. Using the \"alert\" interface instead.`);\n      selectInterface = 'alert';\n    }\n    if (selectInterface === 'popover' && !ev) {\n      console.warn(`Select interface cannot be a \"${selectInterface}\" without passing an event. Using the \"alert\" interface instead.`);\n      selectInterface = 'alert';\n    }\n    if (selectInterface === 'action-sheet') {\n      return this.openActionSheet();\n    }\n    if (selectInterface === 'popover') {\n      return this.openPopover(ev);\n    }\n    return this.openAlert();\n  }\n  updateOverlayOptions() {\n    const overlay = this.overlay;\n    if (!overlay) {\n      return;\n    }\n    const childOpts = this.childOpts;\n    const value = this.value;\n    switch (this.interface) {\n      case 'action-sheet':\n        overlay.buttons = this.createActionSheetButtons(childOpts, value);\n        break;\n      case 'popover':\n        const popover = overlay.querySelector('ion-select-popover');\n        if (popover) {\n          popover.options = this.createPopoverOptions(childOpts, value);\n        }\n        break;\n      case 'alert':\n        const inputType = this.multiple ? 'checkbox' : 'radio';\n        overlay.inputs = this.createAlertInputs(childOpts, inputType, value);\n        break;\n    }\n  }\n  createActionSheetButtons(data, selectValue) {\n    const actionSheetButtons = data.map(option => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList).filter(cls => cls !== 'hydrated').join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        role: isOptionSelected(selectValue, value, this.compareWith) ? 'selected' : '',\n        text: option.textContent,\n        cssClass: optClass,\n        handler: () => {\n          this.setValue(value);\n        }\n      };\n    });\n    // Add \"cancel\" button\n    actionSheetButtons.push({\n      text: this.cancelText,\n      role: 'cancel',\n      handler: () => {\n        this.ionCancel.emit();\n      }\n    });\n    return actionSheetButtons;\n  }\n  createAlertInputs(data, inputType, selectValue) {\n    const alertInputs = data.map(option => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList).filter(cls => cls !== 'hydrated').join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        type: inputType,\n        cssClass: optClass,\n        label: option.textContent || '',\n        value,\n        checked: isOptionSelected(selectValue, value, this.compareWith),\n        disabled: option.disabled\n      };\n    });\n    return alertInputs;\n  }\n  createPopoverOptions(data, selectValue) {\n    const popoverOptions = data.map(option => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList).filter(cls => cls !== 'hydrated').join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        text: option.textContent || '',\n        cssClass: optClass,\n        value,\n        checked: isOptionSelected(selectValue, value, this.compareWith),\n        disabled: option.disabled,\n        handler: selected => {\n          this.setValue(selected);\n          if (!this.multiple) {\n            this.close();\n          }\n        }\n      };\n    });\n    return popoverOptions;\n  }\n  openPopover(ev) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        fill,\n        labelPlacement\n      } = _this3;\n      const interfaceOptions = _this3.interfaceOptions;\n      const mode = getIonMode(_this3);\n      const showBackdrop = mode === 'md' ? false : true;\n      const multiple = _this3.multiple;\n      const value = _this3.value;\n      let event = ev;\n      let size = 'auto';\n      if (_this3.legacyFormController.hasLegacyControl()) {\n        const item = _this3.el.closest('ion-item');\n        // If the select is inside of an item containing a floating\n        // or stacked label then the popover should take up the\n        // full width of the item when it presents\n        if (item && (item.classList.contains('item-label-floating') || item.classList.contains('item-label-stacked'))) {\n          event = Object.assign(Object.assign({}, ev), {\n            detail: {\n              ionShadowTarget: item\n            }\n          });\n          size = 'cover';\n        }\n      } else {\n        const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n        /**\n         * The popover should take up the full width\n         * when using a fill in MD mode or if the\n         * label is floating/stacked.\n         */\n        if (hasFloatingOrStackedLabel || mode === 'md' && fill !== undefined) {\n          size = 'cover';\n          /**\n           * Otherwise the popover\n           * should be positioned relative\n           * to the native element.\n           */\n        } else {\n          event = Object.assign(Object.assign({}, ev), {\n            detail: {\n              ionShadowTarget: _this3.nativeWrapperEl\n            }\n          });\n        }\n      }\n      const popoverOpts = Object.assign(Object.assign({\n        mode,\n        event,\n        alignment: 'center',\n        size,\n        showBackdrop\n      }, interfaceOptions), {\n        component: 'ion-select-popover',\n        cssClass: ['select-popover', interfaceOptions.cssClass],\n        componentProps: {\n          header: interfaceOptions.header,\n          subHeader: interfaceOptions.subHeader,\n          message: interfaceOptions.message,\n          multiple,\n          value,\n          options: _this3.createPopoverOptions(_this3.childOpts, value)\n        }\n      });\n      return popoverController.create(popoverOpts);\n    })();\n  }\n  openActionSheet() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const mode = getIonMode(_this4);\n      const interfaceOptions = _this4.interfaceOptions;\n      const actionSheetOpts = Object.assign(Object.assign({\n        mode\n      }, interfaceOptions), {\n        buttons: _this4.createActionSheetButtons(_this4.childOpts, _this4.value),\n        cssClass: ['select-action-sheet', interfaceOptions.cssClass]\n      });\n      return actionSheetController.create(actionSheetOpts);\n    })();\n  }\n  openAlert() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      /**\n       * TODO FW-3194\n       * Remove legacyFormController logic.\n       * Remove label and labelText vars\n       * Pass `this.labelText` instead of `labelText`\n       * when setting the header.\n       */\n      let label;\n      let labelText;\n      if (_this5.legacyFormController.hasLegacyControl()) {\n        label = _this5.getLabel();\n        labelText = label ? label.textContent : null;\n      } else {\n        labelText = _this5.labelText;\n      }\n      const interfaceOptions = _this5.interfaceOptions;\n      const inputType = _this5.multiple ? 'checkbox' : 'radio';\n      const mode = getIonMode(_this5);\n      const alertOpts = Object.assign(Object.assign({\n        mode\n      }, interfaceOptions), {\n        header: interfaceOptions.header ? interfaceOptions.header : labelText,\n        inputs: _this5.createAlertInputs(_this5.childOpts, inputType, _this5.value),\n        buttons: [{\n          text: _this5.cancelText,\n          role: 'cancel',\n          handler: () => {\n            _this5.ionCancel.emit();\n          }\n        }, {\n          text: _this5.okText,\n          handler: selectedValues => {\n            _this5.setValue(selectedValues);\n          }\n        }],\n        cssClass: ['select-alert', interfaceOptions.cssClass, _this5.multiple ? 'multiple-select-alert' : 'single-select-alert']\n      });\n      return alertController.create(alertOpts);\n    })();\n  }\n  /**\n   * Close the select interface.\n   */\n  close() {\n    if (!this.overlay) {\n      return Promise.resolve(false);\n    }\n    return this.overlay.dismiss();\n  }\n  // TODO FW-3194 Remove this\n  getLabel() {\n    return findItemLabel(this.el);\n  }\n  hasValue() {\n    return this.getText() !== '';\n  }\n  get childOpts() {\n    return Array.from(this.el.querySelectorAll('ion-select-option'));\n  }\n  /**\n   * Returns any plaintext associated with\n   * the label (either prop or slot).\n   * Note: This will not return any custom\n   * HTML. Use the `hasLabel` getter if you\n   * want to know if any slotted label content\n   * was passed.\n   */\n  get labelText() {\n    const {\n      label\n    } = this;\n    if (label !== undefined) {\n      return label;\n    }\n    const {\n      labelSlot\n    } = this;\n    if (labelSlot !== null) {\n      return labelSlot.textContent;\n    }\n    return;\n  }\n  getText() {\n    const selectedText = this.selectedText;\n    if (selectedText != null && selectedText !== '') {\n      return selectedText;\n    }\n    return generateText(this.childOpts, this.value, this.compareWith);\n  }\n  setFocus() {\n    if (this.focusEl) {\n      this.focusEl.focus();\n    }\n  }\n  emitStyle() {\n    const {\n      disabled\n    } = this;\n    const style = {\n      'interactive-disabled': disabled\n    };\n    if (this.legacyFormController.hasLegacyControl()) {\n      style['interactive'] = true;\n      style['select'] = true;\n      style['select-disabled'] = disabled;\n      style['has-placeholder'] = this.placeholder !== undefined;\n      style['has-value'] = this.hasValue();\n      style['has-focus'] = this.isExpanded;\n      // TODO(FW-3194): remove this\n      style['legacy'] = !!this.legacy;\n    }\n    this.ionStyle.emit(style);\n  }\n  renderLabel() {\n    const {\n      label\n    } = this;\n    return h(\"div\", {\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !this.hasLabel\n      },\n      part: \"label\"\n    }, label === undefined ? h(\"slot\", {\n      name: \"label\"\n    }) : h(\"div\", {\n      class: \"label-text\"\n    }, label));\n  }\n  componentDidRender() {\n    var _a;\n    (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n  }\n  /**\n   * Gets any content passed into the `label` slot,\n   * not the <slot> definition.\n   */\n  get labelSlot() {\n    return this.el.querySelector('[slot=\"label\"]');\n  }\n  /**\n   * Returns `true` if label content is provided\n   * either by a prop or a content. If you want\n   * to get the plaintext value of the label use\n   * the `labelText` getter instead.\n   */\n  get hasLabel() {\n    return this.label !== undefined || this.labelSlot !== null;\n  }\n  /**\n   * Renders the border container\n   * when fill=\"outline\".\n   */\n  renderLabelContainer() {\n    const mode = getIonMode(this);\n    const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n    if (hasOutlineFill) {\n      /**\n       * The outline fill has a special outline\n       * that appears around the select and the label.\n       * Certain stacked and floating label placements cause the\n       * label to translate up and create a \"cut out\"\n       * inside of that border by using the notch-spacer element.\n       */\n      return [h(\"div\", {\n        class: \"select-outline-container\"\n      }, h(\"div\", {\n        class: \"select-outline-start\"\n      }), h(\"div\", {\n        class: {\n          'select-outline-notch': true,\n          'select-outline-notch-hidden': !this.hasLabel\n        }\n      }, h(\"div\", {\n        class: \"notch-spacer\",\n        \"aria-hidden\": \"true\",\n        ref: el => this.notchSpacerEl = el\n      }, this.label)), h(\"div\", {\n        class: \"select-outline-end\"\n      })), this.renderLabel()];\n    }\n    /**\n     * If not using the outline style,\n     * we can render just the label.\n     */\n    return this.renderLabel();\n  }\n  renderSelect() {\n    const {\n      disabled,\n      el,\n      isExpanded,\n      expandedIcon,\n      labelPlacement,\n      justify,\n      placeholder,\n      fill,\n      shape,\n      name,\n      value\n    } = this;\n    const mode = getIonMode(this);\n    const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n    const justifyEnabled = !hasFloatingOrStackedLabel;\n    const rtl = isRTL(el) ? 'rtl' : 'ltr';\n    const inItem = hostContext('ion-item', this.el);\n    const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n    const hasValue = this.hasValue();\n    const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n    renderHiddenInput(true, el, name, parseValue(value), disabled);\n    /**\n     * If the label is stacked, it should always sit above the select.\n     * For floating labels, the label should move above the select if\n     * the select has a value, is open, or has anything in either\n     * the start or end slot.\n     *\n     * If there is content in the start slot, the label would overlap\n     * it if not forced to float. This is also applied to the end slot\n     * because with the default or solid fills, the select is not\n     * vertically centered in the container, but the label is. This\n     * causes the slots and label to appear vertically offset from each\n     * other when the label isn't floating above the input. This doesn't\n     * apply to the outline fill, but this was not accounted for to keep\n     * things consistent.\n     *\n     * TODO(FW-5592): Remove hasStartEndSlots condition\n     */\n    const labelShouldFloat = labelPlacement === 'stacked' || labelPlacement === 'floating' && (hasValue || isExpanded || hasStartEndSlots);\n    return h(Host, {\n      onClick: this.onClick,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'in-item': inItem,\n        'in-item-color': hostContext('ion-item.ion-color', el),\n        'select-disabled': disabled,\n        'select-expanded': isExpanded,\n        'has-expanded-icon': expandedIcon !== undefined,\n        'has-value': hasValue,\n        'label-floating': labelShouldFloat,\n        'has-placeholder': placeholder !== undefined,\n        'ion-focusable': true,\n        [`select-${rtl}`]: true,\n        [`select-fill-${fill}`]: fill !== undefined,\n        [`select-justify-${justify}`]: justifyEnabled,\n        [`select-shape-${shape}`]: shape !== undefined,\n        [`select-label-placement-${labelPlacement}`]: true\n      })\n    }, h(\"label\", {\n      class: \"select-wrapper\",\n      id: \"select-label\"\n    }, this.renderLabelContainer(), h(\"div\", {\n      class: \"select-wrapper-inner\"\n    }, h(\"slot\", {\n      name: \"start\"\n    }), h(\"div\", {\n      class: \"native-wrapper\",\n      ref: el => this.nativeWrapperEl = el,\n      part: \"container\"\n    }, this.renderSelectText(), this.renderListbox()), h(\"slot\", {\n      name: \"end\"\n    }), !hasFloatingOrStackedLabel && this.renderSelectIcon()), hasFloatingOrStackedLabel && this.renderSelectIcon(), shouldRenderHighlight && h(\"div\", {\n      class: \"select-highlight\"\n    })));\n  }\n  // TODO FW-3194 - Remove this\n  renderLegacySelect() {\n    if (!this.hasLoggedDeprecationWarning) {\n      printIonWarning(`ion-select now requires providing a label with either the \"label\" property or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the \"label\" property or the \"aria-label\" attribute.\n\nExample: <ion-select label=\"Favorite Color\">...</ion-select>\nExample with aria-label: <ion-select aria-label=\"Favorite Color\">...</ion-select>\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n      if (this.legacy) {\n        printIonWarning(`ion-select is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\n    Developers can dismiss this warning by removing their usage of the \"legacy\" property and using the new select syntax.`, this.el);\n      }\n      this.hasLoggedDeprecationWarning = true;\n    }\n    const {\n      disabled,\n      el,\n      inputId,\n      isExpanded,\n      expandedIcon,\n      name,\n      placeholder,\n      value\n    } = this;\n    const mode = getIonMode(this);\n    const {\n      labelText,\n      labelId\n    } = getAriaLabel(el, inputId);\n    renderHiddenInput(true, el, name, parseValue(value), disabled);\n    const displayValue = this.getText();\n    let selectText = displayValue;\n    if (selectText === '' && placeholder !== undefined) {\n      selectText = placeholder;\n    }\n    // If there is a label then we need to concatenate it with the\n    // current value (or placeholder) and a comma so it separates\n    // nicely when the screen reader announces it, otherwise just\n    // announce the value / placeholder\n    const displayLabel = labelText !== undefined ? selectText !== '' ? `${selectText}, ${labelText}` : labelText : selectText;\n    return h(Host, {\n      onClick: this.onClick,\n      role: \"button\",\n      \"aria-haspopup\": \"listbox\",\n      \"aria-disabled\": disabled ? 'true' : null,\n      \"aria-label\": displayLabel,\n      class: {\n        [mode]: true,\n        'in-item': hostContext('ion-item', el),\n        'in-item-color': hostContext('ion-item.ion-color', el),\n        'select-disabled': disabled,\n        'select-expanded': isExpanded,\n        'has-expanded-icon': expandedIcon !== undefined,\n        'legacy-select': true\n      }\n    }, this.renderSelectText(), this.renderSelectIcon(), h(\"label\", {\n      id: labelId\n    }, displayLabel), this.renderListbox());\n  }\n  /**\n   * Renders either the placeholder\n   * or the selected values based on\n   * the state of the select.\n   */\n  renderSelectText() {\n    const {\n      placeholder\n    } = this;\n    const displayValue = this.getText();\n    let addPlaceholderClass = false;\n    let selectText = displayValue;\n    if (selectText === '' && placeholder !== undefined) {\n      selectText = placeholder;\n      addPlaceholderClass = true;\n    }\n    const selectTextClasses = {\n      'select-text': true,\n      'select-placeholder': addPlaceholderClass\n    };\n    const textPart = addPlaceholderClass ? 'placeholder' : 'text';\n    return h(\"div\", {\n      \"aria-hidden\": \"true\",\n      class: selectTextClasses,\n      part: textPart\n    }, selectText);\n  }\n  /**\n   * Renders the chevron icon\n   * next to the select text.\n   */\n  renderSelectIcon() {\n    const mode = getIonMode(this);\n    const {\n      isExpanded,\n      toggleIcon,\n      expandedIcon\n    } = this;\n    let icon;\n    if (isExpanded && expandedIcon !== undefined) {\n      icon = expandedIcon;\n    } else {\n      const defaultIcon = mode === 'ios' ? chevronExpand : caretDownSharp;\n      icon = toggleIcon !== null && toggleIcon !== void 0 ? toggleIcon : defaultIcon;\n    }\n    return h(\"ion-icon\", {\n      class: \"select-icon\",\n      part: \"icon\",\n      \"aria-hidden\": \"true\",\n      icon: icon\n    });\n  }\n  get ariaLabel() {\n    var _a, _b;\n    const {\n      placeholder,\n      el,\n      inputId,\n      inheritedAttributes\n    } = this;\n    const displayValue = this.getText();\n    const {\n      labelText\n    } = getAriaLabel(el, inputId);\n    const definedLabel = (_b = (_a = this.labelText) !== null && _a !== void 0 ? _a : inheritedAttributes['aria-label']) !== null && _b !== void 0 ? _b : labelText;\n    /**\n     * If developer has specified a placeholder\n     * and there is nothing selected, the selectText\n     * should have the placeholder value.\n     */\n    let renderedLabel = displayValue;\n    if (renderedLabel === '' && placeholder !== undefined) {\n      renderedLabel = placeholder;\n    }\n    /**\n     * If there is a developer-defined label,\n     * then we need to concatenate the developer label\n     * string with the current current value.\n     * The label for the control should be read\n     * before the values of the control.\n     */\n    if (definedLabel !== undefined) {\n      renderedLabel = renderedLabel === '' ? definedLabel : `${definedLabel}, ${renderedLabel}`;\n    }\n    return renderedLabel;\n  }\n  renderListbox() {\n    const {\n      disabled,\n      inputId,\n      isExpanded\n    } = this;\n    return h(\"button\", {\n      disabled: disabled,\n      id: inputId,\n      \"aria-label\": this.ariaLabel,\n      \"aria-haspopup\": \"dialog\",\n      \"aria-expanded\": `${isExpanded}`,\n      onFocus: this.onFocus,\n      onBlur: this.onBlur,\n      ref: focusEl => this.focusEl = focusEl\n    });\n  }\n  render() {\n    const {\n      legacyFormController\n    } = this;\n    return legacyFormController.hasLegacyControl() ? this.renderLegacySelect() : this.renderSelect();\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"disabled\": [\"styleChanged\"],\n      \"isExpanded\": [\"styleChanged\"],\n      \"placeholder\": [\"styleChanged\"],\n      \"value\": [\"styleChanged\"]\n    };\n  }\n};\nconst getOptionValue = el => {\n  const value = el.value;\n  return value === undefined ? el.textContent || '' : value;\n};\nconst parseValue = value => {\n  if (value == null) {\n    return undefined;\n  }\n  if (Array.isArray(value)) {\n    return value.join(',');\n  }\n  return value.toString();\n};\nconst generateText = (opts, value, compareWith) => {\n  if (value === undefined) {\n    return '';\n  }\n  if (Array.isArray(value)) {\n    return value.map(v => textForValue(opts, v, compareWith)).filter(opt => opt !== null).join(', ');\n  } else {\n    return textForValue(opts, value, compareWith) || '';\n  }\n};\nconst textForValue = (opts, value, compareWith) => {\n  const selectOpt = opts.find(opt => {\n    return compareOptions(value, getOptionValue(opt), compareWith);\n  });\n  return selectOpt ? selectOpt.textContent : null;\n};\nlet selectIds = 0;\nconst OPTION_CLASS = 'select-interface-option';\nSelect.style = {\n  ios: IonSelectIosStyle0,\n  md: IonSelectMdStyle0\n};\nconst selectOptionCss = \":host{display:none}\";\nconst IonSelectOptionStyle0 = selectOptionCss;\nconst SelectOption = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.inputId = `ion-selopt-${selectOptionIds++}`;\n    this.disabled = false;\n    this.value = undefined;\n  }\n  render() {\n    return h(Host, {\n      key: 'abf6e85d60e815f59077910abec922826bf46eb2',\n      role: \"option\",\n      id: this.inputId,\n      class: getIonMode(this)\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nlet selectOptionIds = 0;\nSelectOption.style = IonSelectOptionStyle0;\nconst selectPopoverIosCss = \".sc-ion-select-popover-ios-h ion-list.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-ios,ion-label.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-ios-h{overflow-y:auto}\";\nconst IonSelectPopoverIosStyle0 = selectPopoverIosCss;\nconst selectPopoverMdCss = \".sc-ion-select-popover-md-h ion-list.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-md,ion-label.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-md-h{overflow-y:auto}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(container){opacity:0}ion-item.sc-ion-select-popover-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-popover-md{--background:rgba(var(--ion-color-primary-rgb, 56, 128, 255), 0.08);--background-focused:var(--ion-color-primary, #3880ff);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #3880ff);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-popover-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #3880ff)}\";\nconst IonSelectPopoverMdStyle0 = selectPopoverMdCss;\nconst SelectPopover = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.header = undefined;\n    this.subHeader = undefined;\n    this.message = undefined;\n    this.multiple = undefined;\n    this.options = [];\n  }\n  findOptionFromEvent(ev) {\n    const {\n      options\n    } = this;\n    return options.find(o => o.value === ev.target.value);\n  }\n  /**\n   * When an option is selected we need to get the value(s)\n   * of the selected option(s) and return it in the option\n   * handler\n   */\n  callOptionHandler(ev) {\n    const option = this.findOptionFromEvent(ev);\n    const values = this.getValues(ev);\n    if (option === null || option === void 0 ? void 0 : option.handler) {\n      safeCall(option.handler, values);\n    }\n  }\n  /**\n   * Dismisses the host popover that the `ion-select-popover`\n   * is rendered within.\n   */\n  dismissParentPopover() {\n    const popover = this.el.closest('ion-popover');\n    if (popover) {\n      popover.dismiss();\n    }\n  }\n  setChecked(ev) {\n    const {\n      multiple\n    } = this;\n    const option = this.findOptionFromEvent(ev);\n    // this is a popover with checkboxes (multiple value select)\n    // we need to set the checked value for this option\n    if (multiple && option) {\n      option.checked = ev.detail.checked;\n    }\n  }\n  getValues(ev) {\n    const {\n      multiple,\n      options\n    } = this;\n    if (multiple) {\n      // this is a popover with checkboxes (multiple value select)\n      // return an array of all the checked values\n      return options.filter(o => o.checked).map(o => o.value);\n    }\n    // this is a popover with radio buttons (single value select)\n    // return the value that was clicked, otherwise undefined\n    const option = this.findOptionFromEvent(ev);\n    return option ? option.value : undefined;\n  }\n  renderOptions(options) {\n    const {\n      multiple\n    } = this;\n    switch (multiple) {\n      case true:\n        return this.renderCheckboxOptions(options);\n      default:\n        return this.renderRadioOptions(options);\n    }\n  }\n  renderCheckboxOptions(options) {\n    return options.map(option => h(\"ion-item\", {\n      class: Object.assign({\n        // TODO FW-4784\n        'item-checkbox-checked': option.checked\n      }, getClassMap(option.cssClass))\n    }, h(\"ion-checkbox\", {\n      value: option.value,\n      disabled: option.disabled,\n      checked: option.checked,\n      justify: \"start\",\n      labelPlacement: \"end\",\n      onIonChange: ev => {\n        this.setChecked(ev);\n        this.callOptionHandler(ev);\n        // TODO FW-4784\n        forceUpdate(this);\n      }\n    }, option.text)));\n  }\n  renderRadioOptions(options) {\n    const checked = options.filter(o => o.checked).map(o => o.value)[0];\n    return h(\"ion-radio-group\", {\n      value: checked,\n      onIonChange: ev => this.callOptionHandler(ev)\n    }, options.map(option => h(\"ion-item\", {\n      class: Object.assign({\n        // TODO FW-4784\n        'item-radio-checked': option.value === checked\n      }, getClassMap(option.cssClass))\n    }, h(\"ion-radio\", {\n      value: option.value,\n      disabled: option.disabled,\n      onClick: () => this.dismissParentPopover(),\n      onKeyUp: ev => {\n        if (ev.key === ' ') {\n          /**\n           * Selecting a radio option with keyboard navigation,\n           * either through the Enter or Space keys, should\n           * dismiss the popover.\n           */\n          this.dismissParentPopover();\n        }\n      }\n    }, option.text))));\n  }\n  render() {\n    const {\n      header,\n      message,\n      options,\n      subHeader\n    } = this;\n    const hasSubHeaderOrMessage = subHeader !== undefined || message !== undefined;\n    return h(Host, {\n      key: 'ddf45e058c75aae175f8589e3539ff152a5b47ad',\n      class: getIonMode(this)\n    }, h(\"ion-list\", {\n      key: '52dbf712bf6cbdcb9d2e6223b99c67ecc90977ff'\n    }, header !== undefined && h(\"ion-list-header\", {\n      key: '692fc85c97591f09a2a9b0bccc8f71e97681cc09'\n    }, header), hasSubHeaderOrMessage && h(\"ion-item\", {\n      key: 'ecab23444eaadc3ed21e7053d50890db1012475f'\n    }, h(\"ion-label\", {\n      key: '639f08137d7066fd79316f63e850ddcc6a3b54a7',\n      class: \"ion-text-wrap\"\n    }, subHeader !== undefined && h(\"h3\", {\n      key: 'dc501101ac9d68b1d0ce80679b339a2b132d1ae9'\n    }, subHeader), message !== undefined && h(\"p\", {\n      key: '5ead8c1a2e90d29fe0f05e04a9fa65c7e9e62ca5'\n    }, message))), this.renderOptions(options)));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nSelectPopover.style = {\n  ios: IonSelectPopoverIosStyle0,\n  md: IonSelectPopoverMdStyle0\n};\nexport { Select as ion_select, SelectOption as ion_select_option, SelectPopover as ion_select_popover };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "H", "Host", "f", "getElement", "i", "forceUpdate", "c", "createLegacyFormController", "createNotchController", "isOptionSelected", "compareOptions", "k", "inheritAttributes", "focusVisibleElement", "findItemLabel", "renderHiddenInput", "e", "getAriaLabel", "p", "printIonWarning", "popoverController", "b", "actionSheetController", "a", "alertController", "s", "safeCall", "isRTL", "hostContext", "createColorClasses", "g", "getClassMap", "w", "watchForOptions", "chevronExpand", "q", "caretDownSharp", "getIonMode", "selectIosCss", "IonSelectIosStyle0", "selectMdCss", "IonSelectMdStyle0", "Select", "constructor", "hostRef", "ionChange", "ionCancel", "ion<PERSON><PERSON><PERSON>", "ionFocus", "ionBlur", "ionStyle", "inputId", "selectIds", "inheritedAttributes", "hasLoggedDeprecationWarning", "onClick", "ev", "target", "closestSlot", "closest", "el", "setFocus", "open", "preventDefault", "onFocus", "emit", "onBlur", "isExpanded", "cancelText", "color", "undefined", "compareWith", "disabled", "fill", "interface", "interfaceOptions", "justify", "label", "labelPlacement", "legacy", "multiple", "name", "okText", "placeholder", "selectedText", "toggleIcon", "expandedIcon", "shape", "value", "styleChanged", "emitStyle", "setValue", "componentWillLoad", "connectedCallback", "_this", "_asyncToGenerator", "legacyFormController", "notchController", "notchSpacerEl", "labelSlot", "updateOverlayOptions", "mutationO", "disconnectedCallback", "disconnect", "destroy", "event", "_this2", "overlay", "createOverlay", "onDid<PERSON><PERSON><PERSON>", "then", "present", "indexOfSelected", "childOpts", "map", "o", "indexOf", "selectedItem", "querySelector", "interactiveEl", "focus", "firstEnabledOption", "selectInterface", "console", "warn", "openActionSheet", "openPopover", "openAlert", "buttons", "createActionSheetButtons", "popover", "options", "createPopoverOptions", "inputType", "inputs", "createAlertInputs", "data", "selectValue", "actionSheetButtons", "option", "getOptionValue", "copyClasses", "Array", "from", "classList", "filter", "cls", "join", "optClass", "OPTION_CLASS", "role", "text", "textContent", "cssClass", "handler", "push", "alertInputs", "type", "checked", "popoverOptions", "selected", "close", "_this3", "mode", "showBackdrop", "size", "hasLegacyControl", "item", "contains", "Object", "assign", "detail", "ionShadowTarget", "hasFloatingOrStackedLabel", "nativeWrapperEl", "popoverOpts", "alignment", "component", "componentProps", "header", "subHeader", "message", "create", "_this4", "actionSheetOpts", "_this5", "labelText", "get<PERSON><PERSON><PERSON>", "alertOpts", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "dismiss", "hasValue", "getText", "querySelectorAll", "generateText", "focusEl", "style", "renderLabel", "class", "<PERSON><PERSON><PERSON><PERSON>", "part", "componentDidRender", "_a", "calculateNotchWidth", "renderLabelContainer", "hasOutlineFill", "ref", "renderSelect", "justifyEnabled", "rtl", "inItem", "should<PERSON>ender<PERSON>ighlight", "hasStartEndSlots", "parseValue", "labelShouldFloat", "id", "renderSelectText", "renderListbox", "renderSelectIcon", "renderLegacySelect", "labelId", "displayValue", "selectText", "displayLabel", "addPlaceholderClass", "selectTextClasses", "textPart", "icon", "defaultIcon", "aria<PERSON><PERSON><PERSON>", "_b", "defined<PERSON>abel", "<PERSON><PERSON><PERSON><PERSON>", "render", "watchers", "isArray", "toString", "opts", "v", "textForValue", "opt", "selectOpt", "find", "ios", "md", "selectOptionCss", "IonSelectOptionStyle0", "SelectOption", "selectOptionIds", "key", "selectPopoverIosCss", "IonSelectPopoverIosStyle0", "selectPopoverMdCss", "IonSelectPopoverMdStyle0", "SelectPopover", "findOptionFromEvent", "callOptionHandler", "values", "getV<PERSON>ues", "dismissParentPopover", "setChecked", "renderOptions", "renderCheckboxOptions", "renderRadioOptions", "onIonChange", "onKeyUp", "hasSubHeaderOrMessage", "ion_select", "ion_select_option", "ion_select_popover"], "sources": ["E:/Fahion/DFashion/frontend/node_modules/@ionic/core/dist/esm/ion-select_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement, i as forceUpdate } from './index-a1a47f01.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { c as createNotchController } from './notch-controller-6bd3e0f9.js';\nimport { i as isOptionSelected, c as compareOptions } from './compare-with-utils-a96ff2ea.js';\nimport { k as inheritAttributes, f as focusVisibleElement, h as findItemLabel, d as renderHiddenInput, e as getAriaLabel } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { c as popoverController, b as actionSheetController, a as alertController, s as safeCall } from './overlays-b874c3c3.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { h as hostContext, c as createColorClasses, g as getClassMap } from './theme-01f3f29c.js';\nimport { w as watchForOptions } from './watch-options-c2911ace.js';\nimport { w as chevronExpand, q as caretDownSharp } from './index-f7dc70ba.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nimport './index-a5d50daf.js';\nimport './hardware-back-button-6107a37c.js';\nimport './framework-delegate-ed4ba327.js';\n\nconst selectIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:0.6;--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(:not(.legacy-select)){width:100%;min-height:44px}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.legacy-select){-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.in-item:not(.legacy-select)){-ms-flex:1 1 0px;flex:1 1 0}:host(.in-item.legacy-select){position:static;max-width:45%}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]:not(.legacy-select)),:host([slot=end]:not(.legacy-select)){width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}:host(.legacy-select) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-select) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-select) label{left:0}:host-context([dir=rtl]):host(.legacy-select) label,:host-context([dir=rtl]).legacy-select label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-select:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-select) label::-moz-focus-inner{border:0}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.legacy-select){--padding-top:10px;--padding-end:8px;--padding-bottom:10px;--padding-start:16px}.select-icon{width:1.125rem;height:1.125rem;color:var(--ion-color-step-650, #595959)}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 1.125rem - 4px)}:host(.select-disabled){opacity:0.3}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonSelectIosStyle0 = selectIosCss;\n\nconst selectMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:0.6;--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(:not(.legacy-select)){width:100%;min-height:44px}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.legacy-select){-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.in-item:not(.legacy-select)){-ms-flex:1 1 0px;flex:1 1 0}:host(.in-item.legacy-select){position:static;max-width:45%}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]:not(.legacy-select)),:host([slot=end]:not(.legacy-select)){width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}:host(.legacy-select) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-select) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-select) label{left:0}:host-context([dir=rtl]):host(.legacy-select) label,:host-context([dir=rtl]).legacy-select label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-select:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-select) label::-moz-focus-inner{border:0}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.select-fill-solid){--background:var(--ion-color-step-50, #f2f2f2);--border-color:var(--ion-color-step-500, gray);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-solid) .select-wrapper{border-bottom:var(--border-width) var(--border-style) var(--border-color)}:host(.has-focus.select-fill-solid.ion-valid),:host(.select-fill-solid.ion-touched.ion-invalid){--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-bottom{border-top:none}@media (any-hover: hover){:host(.select-fill-solid:hover){--background:var(--ion-color-step-100, #e6e6e6);--border-color:var(--ion-color-step-750, #404040)}}:host(.select-fill-solid.select-expanded),:host(.select-fill-solid.ion-focused){--background:var(--ion-color-step-150, #d9d9d9);--border-color:var(--ion-color-step-750, #404040)}:host(.select-fill-solid) .select-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}:host-context([dir=rtl]):host(.select-fill-solid) .select-wrapper,:host-context([dir=rtl]).select-fill-solid .select-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}@supports selector(:dir(rtl)){:host(.select-fill-solid:dir(rtl)) .select-wrapper{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}}:host(.label-floating.select-fill-solid) .label-text-wrapper{max-width:calc(100% / 0.75)}:host(.select-fill-outline){--border-color:var(--ion-color-step-300, #b3b3b3);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-outline.select-shape-round){--border-radius:28px;--padding-start:32px;--padding-end:32px}:host(.has-focus.select-fill-outline.ion-valid),:host(.select-fill-outline.ion-touched.ion-invalid){--border-color:var(--highlight-color)}@media (any-hover: hover){:host(.select-fill-outline:hover){--border-color:var(--ion-color-step-750, #404040)}}:host(.select-fill-outline.select-expanded),:host(.select-fill-outline.ion-focused){--border-width:2px;--border-color:var(--highlight-color)}:host(.select-fill-outline) .select-bottom{border-top:none}:host(.select-fill-outline) .select-wrapper{border-bottom:none}:host(.select-ltr.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-fill-outline.select-label-placement-floating) .label-text-wrapper{position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .label-text-wrapper{position:relative;z-index:1}:host(.label-floating.select-fill-outline) .label-text-wrapper{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}:host(.select-fill-outline.select-label-placement-stacked) select,:host(.select-fill-outline.select-label-placement-floating) select{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}:host(.select-fill-outline) .select-outline-container{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-end{pointer-events:none}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-notch,:host(.select-fill-outline) .select-outline-end{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.select-fill-outline) .select-outline-notch{max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .notch-spacer{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none}:host(.select-fill-outline) .select-outline-start{-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color)}:host(.select-ltr.select-fill-outline) .select-outline-start{border-radius:var(--border-radius) 0px 0px var(--border-radius)}:host(.select-rtl.select-fill-outline) .select-outline-start{border-radius:0px var(--border-radius) var(--border-radius) 0px}:host(.select-fill-outline) .select-outline-start{width:calc(var(--padding-start) - 4px)}:host(.select-fill-outline) .select-outline-end{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color)}:host(.select-ltr.select-fill-outline) .select-outline-end{border-radius:0px var(--border-radius) var(--border-radius) 0px}:host(.select-rtl.select-fill-outline) .select-outline-end{border-radius:var(--border-radius) 0px 0px var(--border-radius)}:host(.select-fill-outline) .select-outline-end{-ms-flex-positive:1;flex-grow:1}:host(.label-floating.select-fill-outline) .select-outline-notch{border-top:none}:host{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))))}:host(.legacy-select){--padding-top:10px;--padding-end:0;--padding-bottom:10px;--padding-start:16px}.select-icon{width:0.8125rem;-webkit-transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);color:var(--ion-color-step-500, gray)}:host(.select-label-placement-floating.select-expanded) .label-text-wrapper,:host(.select-label-placement-floating.ion-focused) .label-text-wrapper,:host(.select-label-placement-stacked.select-expanded) .label-text-wrapper,:host(.select-label-placement-stacked.ion-focused) .label-text-wrapper{color:var(--highlight-color)}:host(.has-focus.select-label-placement-floating.ion-valid) .label-text-wrapper,:host(.select-label-placement-floating.ion-touched.ion-invalid) .label-text-wrapper,:host(.has-focus.select-label-placement-stacked.ion-valid) .label-text-wrapper,:host(.select-label-placement-stacked.ion-touched.ion-invalid) .label-text-wrapper{color:var(--highlight-color)}.select-highlight{bottom:-1px;position:absolute;width:100%;height:2px;-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}@supports (inset-inline-start: 0){.select-highlight{inset-inline-start:0}}@supports not (inset-inline-start: 0){.select-highlight{left:0}:host-context([dir=rtl]) .select-highlight{left:unset;right:unset;right:0}[dir=rtl] .select-highlight{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.select-highlight:dir(rtl){left:unset;right:unset;right:0}}}:host(.select-expanded) .select-highlight,:host(.ion-focused) .select-highlight{-webkit-transform:scale(1);transform:scale(1)}:host(.in-item) .select-highlight{bottom:0}@supports (inset-inline-start: 0){:host(.in-item) .select-highlight{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.in-item) .select-highlight{left:0}:host-context([dir=rtl]):host(.in-item) .select-highlight,:host-context([dir=rtl]).in-item .select-highlight{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.in-item:dir(rtl)) .select-highlight{left:unset;right:unset;right:0}}}:host(.select-expanded:not(.legacy-select):not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.select-expanded) .select-wrapper .select-icon,:host(.has-focus.ion-valid) .select-wrapper .select-icon,:host(.ion-touched.ion-invalid) .select-wrapper .select-icon,:host(.ion-focused) .select-wrapper .select-icon{color:var(--highlight-color)}:host-context(.item-label-stacked) .select-icon,:host-context(.item-label-floating:not(.item-fill-outline)) .select-icon,:host-context(.item-label-floating.item-fill-outline){-webkit-transform:translate3d(0,  -9px,  0);transform:translate3d(0,  -9px,  0)}:host-context(.item-has-focus):host(:not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host-context(.item-has-focus.item-label-stacked):host(:not(.has-expanded-icon)) .select-icon,:host-context(.item-has-focus.item-label-floating:not(.item-fill-outline)):host(:not(.has-expanded-icon)) .select-icon{-webkit-transform:translate3d(0,  -9px,  0) rotate(180deg);transform:translate3d(0,  -9px,  0) rotate(180deg)}:host(.select-shape-round){--border-radius:16px}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 0.8125rem - 4px)}:host(.select-disabled){opacity:0.38}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonSelectMdStyle0 = selectMdCss;\n\nconst Select = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionCancel = createEvent(this, \"ionCancel\", 7);\n        this.ionDismiss = createEvent(this, \"ionDismiss\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.inputId = `ion-sel-${selectIds++}`;\n        this.inheritedAttributes = {};\n        // This flag ensures we log the deprecation warning at most once.\n        this.hasLoggedDeprecationWarning = false;\n        this.onClick = (ev) => {\n            const target = ev.target;\n            const closestSlot = target.closest('[slot=\"start\"], [slot=\"end\"]');\n            if (target === this.el || closestSlot === null) {\n                this.setFocus();\n                this.open(ev);\n            }\n            else {\n                /**\n                 * Prevent clicks to the start/end slots from opening the select.\n                 * We ensure the target isn't this element in case the select is slotted\n                 * in, for example, an item. This would prevent the select from ever\n                 * being opened since the element itself has slot=\"start\"/\"end\".\n                 *\n                 * Clicking a slotted element also causes a click\n                 * on the <label> element (since it wraps the slots).\n                 * Clicking <label> dispatches another click event on\n                 * the native form control that then bubbles up to this\n                 * listener. This additional event targets the host\n                 * element, so the select overlay is opened.\n                 *\n                 * When the slotted elements are clicked (and therefore\n                 * the ancestor <label> element) we want to prevent the label\n                 * from dispatching another click event.\n                 *\n                 * Do not call stopPropagation() because this will cause\n                 * click handlers on the slotted elements to never fire in React.\n                 * When developers do onClick in React a native \"click\" listener\n                 * is added on the root element, not the slotted element. When that\n                 * native click listener fires, React then dispatches the synthetic\n                 * click event on the slotted element. However, if stopPropagation\n                 * is called then the native click event will never bubble up\n                 * to the root element.\n                 */\n                ev.preventDefault();\n            }\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.isExpanded = false;\n        this.cancelText = 'Cancel';\n        this.color = undefined;\n        this.compareWith = undefined;\n        this.disabled = false;\n        this.fill = undefined;\n        this.interface = 'alert';\n        this.interfaceOptions = {};\n        this.justify = 'space-between';\n        this.label = undefined;\n        this.labelPlacement = 'start';\n        this.legacy = undefined;\n        this.multiple = false;\n        this.name = this.inputId;\n        this.okText = 'OK';\n        this.placeholder = undefined;\n        this.selectedText = undefined;\n        this.toggleIcon = undefined;\n        this.expandedIcon = undefined;\n        this.shape = undefined;\n        this.value = undefined;\n    }\n    styleChanged() {\n        this.emitStyle();\n    }\n    setValue(value) {\n        this.value = value;\n        this.ionChange.emit({ value });\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n    }\n    async connectedCallback() {\n        const { el } = this;\n        this.legacyFormController = createLegacyFormController(el);\n        this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n        this.updateOverlayOptions();\n        this.emitStyle();\n        this.mutationO = watchForOptions(this.el, 'ion-select-option', async () => {\n            this.updateOverlayOptions();\n            /**\n             * We need to re-render the component\n             * because one of the new ion-select-option\n             * elements may match the value. In this case,\n             * the rendered selected text should be updated.\n             */\n            forceUpdate(this);\n        });\n    }\n    disconnectedCallback() {\n        if (this.mutationO) {\n            this.mutationO.disconnect();\n            this.mutationO = undefined;\n        }\n        if (this.notchController) {\n            this.notchController.destroy();\n            this.notchController = undefined;\n        }\n    }\n    /**\n     * Open the select overlay. The overlay is either an alert, action sheet, or popover,\n     * depending on the `interface` property on the `ion-select`.\n     *\n     * @param event The user interface event that called the open.\n     */\n    async open(event) {\n        if (this.disabled || this.isExpanded) {\n            return undefined;\n        }\n        this.isExpanded = true;\n        const overlay = (this.overlay = await this.createOverlay(event));\n        overlay.onDidDismiss().then(() => {\n            this.overlay = undefined;\n            this.isExpanded = false;\n            this.ionDismiss.emit();\n            this.setFocus();\n        });\n        await overlay.present();\n        // focus selected option for popovers\n        if (this.interface === 'popover') {\n            const indexOfSelected = this.childOpts.map((o) => o.value).indexOf(this.value);\n            if (indexOfSelected > -1) {\n                const selectedItem = overlay.querySelector(`.select-interface-option:nth-child(${indexOfSelected + 1})`);\n                if (selectedItem) {\n                    focusVisibleElement(selectedItem);\n                    /**\n                     * Browsers such as Firefox do not\n                     * correctly delegate focus when manually\n                     * focusing an element with delegatesFocus.\n                     * We work around this by manually focusing\n                     * the interactive element.\n                     * ion-radio and ion-checkbox are the only\n                     * elements that ion-select-popover uses, so\n                     * we only need to worry about those two components\n                     * when focusing.\n                     */\n                    const interactiveEl = selectedItem.querySelector('ion-radio, ion-checkbox');\n                    if (interactiveEl) {\n                        interactiveEl.focus();\n                    }\n                }\n            }\n            else {\n                /**\n                 * If no value is set then focus the first enabled option.\n                 */\n                const firstEnabledOption = overlay.querySelector('ion-radio:not(.radio-disabled), ion-checkbox:not(.checkbox-disabled)');\n                if (firstEnabledOption) {\n                    focusVisibleElement(firstEnabledOption.closest('ion-item'));\n                    /**\n                     * Focus the option for the same reason as we do above.\n                     */\n                    firstEnabledOption.focus();\n                }\n            }\n        }\n        return overlay;\n    }\n    createOverlay(ev) {\n        let selectInterface = this.interface;\n        if (selectInterface === 'action-sheet' && this.multiple) {\n            console.warn(`Select interface cannot be \"${selectInterface}\" with a multi-value select. Using the \"alert\" interface instead.`);\n            selectInterface = 'alert';\n        }\n        if (selectInterface === 'popover' && !ev) {\n            console.warn(`Select interface cannot be a \"${selectInterface}\" without passing an event. Using the \"alert\" interface instead.`);\n            selectInterface = 'alert';\n        }\n        if (selectInterface === 'action-sheet') {\n            return this.openActionSheet();\n        }\n        if (selectInterface === 'popover') {\n            return this.openPopover(ev);\n        }\n        return this.openAlert();\n    }\n    updateOverlayOptions() {\n        const overlay = this.overlay;\n        if (!overlay) {\n            return;\n        }\n        const childOpts = this.childOpts;\n        const value = this.value;\n        switch (this.interface) {\n            case 'action-sheet':\n                overlay.buttons = this.createActionSheetButtons(childOpts, value);\n                break;\n            case 'popover':\n                const popover = overlay.querySelector('ion-select-popover');\n                if (popover) {\n                    popover.options = this.createPopoverOptions(childOpts, value);\n                }\n                break;\n            case 'alert':\n                const inputType = this.multiple ? 'checkbox' : 'radio';\n                overlay.inputs = this.createAlertInputs(childOpts, inputType, value);\n                break;\n        }\n    }\n    createActionSheetButtons(data, selectValue) {\n        const actionSheetButtons = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                role: isOptionSelected(selectValue, value, this.compareWith) ? 'selected' : '',\n                text: option.textContent,\n                cssClass: optClass,\n                handler: () => {\n                    this.setValue(value);\n                },\n            };\n        });\n        // Add \"cancel\" button\n        actionSheetButtons.push({\n            text: this.cancelText,\n            role: 'cancel',\n            handler: () => {\n                this.ionCancel.emit();\n            },\n        });\n        return actionSheetButtons;\n    }\n    createAlertInputs(data, inputType, selectValue) {\n        const alertInputs = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                type: inputType,\n                cssClass: optClass,\n                label: option.textContent || '',\n                value,\n                checked: isOptionSelected(selectValue, value, this.compareWith),\n                disabled: option.disabled,\n            };\n        });\n        return alertInputs;\n    }\n    createPopoverOptions(data, selectValue) {\n        const popoverOptions = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                text: option.textContent || '',\n                cssClass: optClass,\n                value,\n                checked: isOptionSelected(selectValue, value, this.compareWith),\n                disabled: option.disabled,\n                handler: (selected) => {\n                    this.setValue(selected);\n                    if (!this.multiple) {\n                        this.close();\n                    }\n                },\n            };\n        });\n        return popoverOptions;\n    }\n    async openPopover(ev) {\n        const { fill, labelPlacement } = this;\n        const interfaceOptions = this.interfaceOptions;\n        const mode = getIonMode(this);\n        const showBackdrop = mode === 'md' ? false : true;\n        const multiple = this.multiple;\n        const value = this.value;\n        let event = ev;\n        let size = 'auto';\n        if (this.legacyFormController.hasLegacyControl()) {\n            const item = this.el.closest('ion-item');\n            // If the select is inside of an item containing a floating\n            // or stacked label then the popover should take up the\n            // full width of the item when it presents\n            if (item && (item.classList.contains('item-label-floating') || item.classList.contains('item-label-stacked'))) {\n                event = Object.assign(Object.assign({}, ev), { detail: {\n                        ionShadowTarget: item,\n                    } });\n                size = 'cover';\n            }\n        }\n        else {\n            const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n            /**\n             * The popover should take up the full width\n             * when using a fill in MD mode or if the\n             * label is floating/stacked.\n             */\n            if (hasFloatingOrStackedLabel || (mode === 'md' && fill !== undefined)) {\n                size = 'cover';\n                /**\n                 * Otherwise the popover\n                 * should be positioned relative\n                 * to the native element.\n                 */\n            }\n            else {\n                event = Object.assign(Object.assign({}, ev), { detail: {\n                        ionShadowTarget: this.nativeWrapperEl,\n                    } });\n            }\n        }\n        const popoverOpts = Object.assign(Object.assign({ mode,\n            event, alignment: 'center', size,\n            showBackdrop }, interfaceOptions), { component: 'ion-select-popover', cssClass: ['select-popover', interfaceOptions.cssClass], componentProps: {\n                header: interfaceOptions.header,\n                subHeader: interfaceOptions.subHeader,\n                message: interfaceOptions.message,\n                multiple,\n                value,\n                options: this.createPopoverOptions(this.childOpts, value),\n            } });\n        return popoverController.create(popoverOpts);\n    }\n    async openActionSheet() {\n        const mode = getIonMode(this);\n        const interfaceOptions = this.interfaceOptions;\n        const actionSheetOpts = Object.assign(Object.assign({ mode }, interfaceOptions), { buttons: this.createActionSheetButtons(this.childOpts, this.value), cssClass: ['select-action-sheet', interfaceOptions.cssClass] });\n        return actionSheetController.create(actionSheetOpts);\n    }\n    async openAlert() {\n        /**\n         * TODO FW-3194\n         * Remove legacyFormController logic.\n         * Remove label and labelText vars\n         * Pass `this.labelText` instead of `labelText`\n         * when setting the header.\n         */\n        let label;\n        let labelText;\n        if (this.legacyFormController.hasLegacyControl()) {\n            label = this.getLabel();\n            labelText = label ? label.textContent : null;\n        }\n        else {\n            labelText = this.labelText;\n        }\n        const interfaceOptions = this.interfaceOptions;\n        const inputType = this.multiple ? 'checkbox' : 'radio';\n        const mode = getIonMode(this);\n        const alertOpts = Object.assign(Object.assign({ mode }, interfaceOptions), { header: interfaceOptions.header ? interfaceOptions.header : labelText, inputs: this.createAlertInputs(this.childOpts, inputType, this.value), buttons: [\n                {\n                    text: this.cancelText,\n                    role: 'cancel',\n                    handler: () => {\n                        this.ionCancel.emit();\n                    },\n                },\n                {\n                    text: this.okText,\n                    handler: (selectedValues) => {\n                        this.setValue(selectedValues);\n                    },\n                },\n            ], cssClass: [\n                'select-alert',\n                interfaceOptions.cssClass,\n                this.multiple ? 'multiple-select-alert' : 'single-select-alert',\n            ] });\n        return alertController.create(alertOpts);\n    }\n    /**\n     * Close the select interface.\n     */\n    close() {\n        if (!this.overlay) {\n            return Promise.resolve(false);\n        }\n        return this.overlay.dismiss();\n    }\n    // TODO FW-3194 Remove this\n    getLabel() {\n        return findItemLabel(this.el);\n    }\n    hasValue() {\n        return this.getText() !== '';\n    }\n    get childOpts() {\n        return Array.from(this.el.querySelectorAll('ion-select-option'));\n    }\n    /**\n     * Returns any plaintext associated with\n     * the label (either prop or slot).\n     * Note: This will not return any custom\n     * HTML. Use the `hasLabel` getter if you\n     * want to know if any slotted label content\n     * was passed.\n     */\n    get labelText() {\n        const { label } = this;\n        if (label !== undefined) {\n            return label;\n        }\n        const { labelSlot } = this;\n        if (labelSlot !== null) {\n            return labelSlot.textContent;\n        }\n        return;\n    }\n    getText() {\n        const selectedText = this.selectedText;\n        if (selectedText != null && selectedText !== '') {\n            return selectedText;\n        }\n        return generateText(this.childOpts, this.value, this.compareWith);\n    }\n    setFocus() {\n        if (this.focusEl) {\n            this.focusEl.focus();\n        }\n    }\n    emitStyle() {\n        const { disabled } = this;\n        const style = {\n            'interactive-disabled': disabled,\n        };\n        if (this.legacyFormController.hasLegacyControl()) {\n            style['interactive'] = true;\n            style['select'] = true;\n            style['select-disabled'] = disabled;\n            style['has-placeholder'] = this.placeholder !== undefined;\n            style['has-value'] = this.hasValue();\n            style['has-focus'] = this.isExpanded;\n            // TODO(FW-3194): remove this\n            style['legacy'] = !!this.legacy;\n        }\n        this.ionStyle.emit(style);\n    }\n    renderLabel() {\n        const { label } = this;\n        return (h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !this.hasLabel,\n            }, part: \"label\" }, label === undefined ? h(\"slot\", { name: \"label\" }) : h(\"div\", { class: \"label-text\" }, label)));\n    }\n    componentDidRender() {\n        var _a;\n        (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n    }\n    /**\n     * Gets any content passed into the `label` slot,\n     * not the <slot> definition.\n     */\n    get labelSlot() {\n        return this.el.querySelector('[slot=\"label\"]');\n    }\n    /**\n     * Returns `true` if label content is provided\n     * either by a prop or a content. If you want\n     * to get the plaintext value of the label use\n     * the `labelText` getter instead.\n     */\n    get hasLabel() {\n        return this.label !== undefined || this.labelSlot !== null;\n    }\n    /**\n     * Renders the border container\n     * when fill=\"outline\".\n     */\n    renderLabelContainer() {\n        const mode = getIonMode(this);\n        const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n        if (hasOutlineFill) {\n            /**\n             * The outline fill has a special outline\n             * that appears around the select and the label.\n             * Certain stacked and floating label placements cause the\n             * label to translate up and create a \"cut out\"\n             * inside of that border by using the notch-spacer element.\n             */\n            return [\n                h(\"div\", { class: \"select-outline-container\" }, h(\"div\", { class: \"select-outline-start\" }), h(\"div\", { class: {\n                        'select-outline-notch': true,\n                        'select-outline-notch-hidden': !this.hasLabel,\n                    } }, h(\"div\", { class: \"notch-spacer\", \"aria-hidden\": \"true\", ref: (el) => (this.notchSpacerEl = el) }, this.label)), h(\"div\", { class: \"select-outline-end\" })),\n                this.renderLabel(),\n            ];\n        }\n        /**\n         * If not using the outline style,\n         * we can render just the label.\n         */\n        return this.renderLabel();\n    }\n    renderSelect() {\n        const { disabled, el, isExpanded, expandedIcon, labelPlacement, justify, placeholder, fill, shape, name, value } = this;\n        const mode = getIonMode(this);\n        const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n        const justifyEnabled = !hasFloatingOrStackedLabel;\n        const rtl = isRTL(el) ? 'rtl' : 'ltr';\n        const inItem = hostContext('ion-item', this.el);\n        const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n        const hasValue = this.hasValue();\n        const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n        renderHiddenInput(true, el, name, parseValue(value), disabled);\n        /**\n         * If the label is stacked, it should always sit above the select.\n         * For floating labels, the label should move above the select if\n         * the select has a value, is open, or has anything in either\n         * the start or end slot.\n         *\n         * If there is content in the start slot, the label would overlap\n         * it if not forced to float. This is also applied to the end slot\n         * because with the default or solid fills, the select is not\n         * vertically centered in the container, but the label is. This\n         * causes the slots and label to appear vertically offset from each\n         * other when the label isn't floating above the input. This doesn't\n         * apply to the outline fill, but this was not accounted for to keep\n         * things consistent.\n         *\n         * TODO(FW-5592): Remove hasStartEndSlots condition\n         */\n        const labelShouldFloat = labelPlacement === 'stacked' || (labelPlacement === 'floating' && (hasValue || isExpanded || hasStartEndSlots));\n        return (h(Host, { onClick: this.onClick, class: createColorClasses(this.color, {\n                [mode]: true,\n                'in-item': inItem,\n                'in-item-color': hostContext('ion-item.ion-color', el),\n                'select-disabled': disabled,\n                'select-expanded': isExpanded,\n                'has-expanded-icon': expandedIcon !== undefined,\n                'has-value': hasValue,\n                'label-floating': labelShouldFloat,\n                'has-placeholder': placeholder !== undefined,\n                'ion-focusable': true,\n                [`select-${rtl}`]: true,\n                [`select-fill-${fill}`]: fill !== undefined,\n                [`select-justify-${justify}`]: justifyEnabled,\n                [`select-shape-${shape}`]: shape !== undefined,\n                [`select-label-placement-${labelPlacement}`]: true,\n            }) }, h(\"label\", { class: \"select-wrapper\", id: \"select-label\" }, this.renderLabelContainer(), h(\"div\", { class: \"select-wrapper-inner\" }, h(\"slot\", { name: \"start\" }), h(\"div\", { class: \"native-wrapper\", ref: (el) => (this.nativeWrapperEl = el), part: \"container\" }, this.renderSelectText(), this.renderListbox()), h(\"slot\", { name: \"end\" }), !hasFloatingOrStackedLabel && this.renderSelectIcon()), hasFloatingOrStackedLabel && this.renderSelectIcon(), shouldRenderHighlight && h(\"div\", { class: \"select-highlight\" }))));\n    }\n    // TODO FW-3194 - Remove this\n    renderLegacySelect() {\n        if (!this.hasLoggedDeprecationWarning) {\n            printIonWarning(`ion-select now requires providing a label with either the \"label\" property or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the \"label\" property or the \"aria-label\" attribute.\n\nExample: <ion-select label=\"Favorite Color\">...</ion-select>\nExample with aria-label: <ion-select aria-label=\"Favorite Color\">...</ion-select>\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n            if (this.legacy) {\n                printIonWarning(`ion-select is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\n    Developers can dismiss this warning by removing their usage of the \"legacy\" property and using the new select syntax.`, this.el);\n            }\n            this.hasLoggedDeprecationWarning = true;\n        }\n        const { disabled, el, inputId, isExpanded, expandedIcon, name, placeholder, value } = this;\n        const mode = getIonMode(this);\n        const { labelText, labelId } = getAriaLabel(el, inputId);\n        renderHiddenInput(true, el, name, parseValue(value), disabled);\n        const displayValue = this.getText();\n        let selectText = displayValue;\n        if (selectText === '' && placeholder !== undefined) {\n            selectText = placeholder;\n        }\n        // If there is a label then we need to concatenate it with the\n        // current value (or placeholder) and a comma so it separates\n        // nicely when the screen reader announces it, otherwise just\n        // announce the value / placeholder\n        const displayLabel = labelText !== undefined ? (selectText !== '' ? `${selectText}, ${labelText}` : labelText) : selectText;\n        return (h(Host, { onClick: this.onClick, role: \"button\", \"aria-haspopup\": \"listbox\", \"aria-disabled\": disabled ? 'true' : null, \"aria-label\": displayLabel, class: {\n                [mode]: true,\n                'in-item': hostContext('ion-item', el),\n                'in-item-color': hostContext('ion-item.ion-color', el),\n                'select-disabled': disabled,\n                'select-expanded': isExpanded,\n                'has-expanded-icon': expandedIcon !== undefined,\n                'legacy-select': true,\n            } }, this.renderSelectText(), this.renderSelectIcon(), h(\"label\", { id: labelId }, displayLabel), this.renderListbox()));\n    }\n    /**\n     * Renders either the placeholder\n     * or the selected values based on\n     * the state of the select.\n     */\n    renderSelectText() {\n        const { placeholder } = this;\n        const displayValue = this.getText();\n        let addPlaceholderClass = false;\n        let selectText = displayValue;\n        if (selectText === '' && placeholder !== undefined) {\n            selectText = placeholder;\n            addPlaceholderClass = true;\n        }\n        const selectTextClasses = {\n            'select-text': true,\n            'select-placeholder': addPlaceholderClass,\n        };\n        const textPart = addPlaceholderClass ? 'placeholder' : 'text';\n        return (h(\"div\", { \"aria-hidden\": \"true\", class: selectTextClasses, part: textPart }, selectText));\n    }\n    /**\n     * Renders the chevron icon\n     * next to the select text.\n     */\n    renderSelectIcon() {\n        const mode = getIonMode(this);\n        const { isExpanded, toggleIcon, expandedIcon } = this;\n        let icon;\n        if (isExpanded && expandedIcon !== undefined) {\n            icon = expandedIcon;\n        }\n        else {\n            const defaultIcon = mode === 'ios' ? chevronExpand : caretDownSharp;\n            icon = toggleIcon !== null && toggleIcon !== void 0 ? toggleIcon : defaultIcon;\n        }\n        return h(\"ion-icon\", { class: \"select-icon\", part: \"icon\", \"aria-hidden\": \"true\", icon: icon });\n    }\n    get ariaLabel() {\n        var _a, _b;\n        const { placeholder, el, inputId, inheritedAttributes } = this;\n        const displayValue = this.getText();\n        const { labelText } = getAriaLabel(el, inputId);\n        const definedLabel = (_b = (_a = this.labelText) !== null && _a !== void 0 ? _a : inheritedAttributes['aria-label']) !== null && _b !== void 0 ? _b : labelText;\n        /**\n         * If developer has specified a placeholder\n         * and there is nothing selected, the selectText\n         * should have the placeholder value.\n         */\n        let renderedLabel = displayValue;\n        if (renderedLabel === '' && placeholder !== undefined) {\n            renderedLabel = placeholder;\n        }\n        /**\n         * If there is a developer-defined label,\n         * then we need to concatenate the developer label\n         * string with the current current value.\n         * The label for the control should be read\n         * before the values of the control.\n         */\n        if (definedLabel !== undefined) {\n            renderedLabel = renderedLabel === '' ? definedLabel : `${definedLabel}, ${renderedLabel}`;\n        }\n        return renderedLabel;\n    }\n    renderListbox() {\n        const { disabled, inputId, isExpanded } = this;\n        return (h(\"button\", { disabled: disabled, id: inputId, \"aria-label\": this.ariaLabel, \"aria-haspopup\": \"dialog\", \"aria-expanded\": `${isExpanded}`, onFocus: this.onFocus, onBlur: this.onBlur, ref: (focusEl) => (this.focusEl = focusEl) }));\n    }\n    render() {\n        const { legacyFormController } = this;\n        return legacyFormController.hasLegacyControl() ? this.renderLegacySelect() : this.renderSelect();\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"styleChanged\"],\n        \"isExpanded\": [\"styleChanged\"],\n        \"placeholder\": [\"styleChanged\"],\n        \"value\": [\"styleChanged\"]\n    }; }\n};\nconst getOptionValue = (el) => {\n    const value = el.value;\n    return value === undefined ? el.textContent || '' : value;\n};\nconst parseValue = (value) => {\n    if (value == null) {\n        return undefined;\n    }\n    if (Array.isArray(value)) {\n        return value.join(',');\n    }\n    return value.toString();\n};\nconst generateText = (opts, value, compareWith) => {\n    if (value === undefined) {\n        return '';\n    }\n    if (Array.isArray(value)) {\n        return value\n            .map((v) => textForValue(opts, v, compareWith))\n            .filter((opt) => opt !== null)\n            .join(', ');\n    }\n    else {\n        return textForValue(opts, value, compareWith) || '';\n    }\n};\nconst textForValue = (opts, value, compareWith) => {\n    const selectOpt = opts.find((opt) => {\n        return compareOptions(value, getOptionValue(opt), compareWith);\n    });\n    return selectOpt ? selectOpt.textContent : null;\n};\nlet selectIds = 0;\nconst OPTION_CLASS = 'select-interface-option';\nSelect.style = {\n    ios: IonSelectIosStyle0,\n    md: IonSelectMdStyle0\n};\n\nconst selectOptionCss = \":host{display:none}\";\nconst IonSelectOptionStyle0 = selectOptionCss;\n\nconst SelectOption = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.inputId = `ion-selopt-${selectOptionIds++}`;\n        this.disabled = false;\n        this.value = undefined;\n    }\n    render() {\n        return h(Host, { key: 'abf6e85d60e815f59077910abec922826bf46eb2', role: \"option\", id: this.inputId, class: getIonMode(this) });\n    }\n    get el() { return getElement(this); }\n};\nlet selectOptionIds = 0;\nSelectOption.style = IonSelectOptionStyle0;\n\nconst selectPopoverIosCss = \".sc-ion-select-popover-ios-h ion-list.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-ios,ion-label.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-ios-h{overflow-y:auto}\";\nconst IonSelectPopoverIosStyle0 = selectPopoverIosCss;\n\nconst selectPopoverMdCss = \".sc-ion-select-popover-md-h ion-list.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-md,ion-label.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-md-h{overflow-y:auto}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(container){opacity:0}ion-item.sc-ion-select-popover-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-popover-md{--background:rgba(var(--ion-color-primary-rgb, 56, 128, 255), 0.08);--background-focused:var(--ion-color-primary, #3880ff);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #3880ff);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-popover-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #3880ff)}\";\nconst IonSelectPopoverMdStyle0 = selectPopoverMdCss;\n\nconst SelectPopover = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.header = undefined;\n        this.subHeader = undefined;\n        this.message = undefined;\n        this.multiple = undefined;\n        this.options = [];\n    }\n    findOptionFromEvent(ev) {\n        const { options } = this;\n        return options.find((o) => o.value === ev.target.value);\n    }\n    /**\n     * When an option is selected we need to get the value(s)\n     * of the selected option(s) and return it in the option\n     * handler\n     */\n    callOptionHandler(ev) {\n        const option = this.findOptionFromEvent(ev);\n        const values = this.getValues(ev);\n        if (option === null || option === void 0 ? void 0 : option.handler) {\n            safeCall(option.handler, values);\n        }\n    }\n    /**\n     * Dismisses the host popover that the `ion-select-popover`\n     * is rendered within.\n     */\n    dismissParentPopover() {\n        const popover = this.el.closest('ion-popover');\n        if (popover) {\n            popover.dismiss();\n        }\n    }\n    setChecked(ev) {\n        const { multiple } = this;\n        const option = this.findOptionFromEvent(ev);\n        // this is a popover with checkboxes (multiple value select)\n        // we need to set the checked value for this option\n        if (multiple && option) {\n            option.checked = ev.detail.checked;\n        }\n    }\n    getValues(ev) {\n        const { multiple, options } = this;\n        if (multiple) {\n            // this is a popover with checkboxes (multiple value select)\n            // return an array of all the checked values\n            return options.filter((o) => o.checked).map((o) => o.value);\n        }\n        // this is a popover with radio buttons (single value select)\n        // return the value that was clicked, otherwise undefined\n        const option = this.findOptionFromEvent(ev);\n        return option ? option.value : undefined;\n    }\n    renderOptions(options) {\n        const { multiple } = this;\n        switch (multiple) {\n            case true:\n                return this.renderCheckboxOptions(options);\n            default:\n                return this.renderRadioOptions(options);\n        }\n    }\n    renderCheckboxOptions(options) {\n        return options.map((option) => (h(\"ion-item\", { class: Object.assign({\n                // TODO FW-4784\n                'item-checkbox-checked': option.checked\n            }, getClassMap(option.cssClass)) }, h(\"ion-checkbox\", { value: option.value, disabled: option.disabled, checked: option.checked, justify: \"start\", labelPlacement: \"end\", onIonChange: (ev) => {\n                this.setChecked(ev);\n                this.callOptionHandler(ev);\n                // TODO FW-4784\n                forceUpdate(this);\n            } }, option.text))));\n    }\n    renderRadioOptions(options) {\n        const checked = options.filter((o) => o.checked).map((o) => o.value)[0];\n        return (h(\"ion-radio-group\", { value: checked, onIonChange: (ev) => this.callOptionHandler(ev) }, options.map((option) => (h(\"ion-item\", { class: Object.assign({\n                // TODO FW-4784\n                'item-radio-checked': option.value === checked\n            }, getClassMap(option.cssClass)) }, h(\"ion-radio\", { value: option.value, disabled: option.disabled, onClick: () => this.dismissParentPopover(), onKeyUp: (ev) => {\n                if (ev.key === ' ') {\n                    /**\n                     * Selecting a radio option with keyboard navigation,\n                     * either through the Enter or Space keys, should\n                     * dismiss the popover.\n                     */\n                    this.dismissParentPopover();\n                }\n            } }, option.text))))));\n    }\n    render() {\n        const { header, message, options, subHeader } = this;\n        const hasSubHeaderOrMessage = subHeader !== undefined || message !== undefined;\n        return (h(Host, { key: 'ddf45e058c75aae175f8589e3539ff152a5b47ad', class: getIonMode(this) }, h(\"ion-list\", { key: '52dbf712bf6cbdcb9d2e6223b99c67ecc90977ff' }, header !== undefined && h(\"ion-list-header\", { key: '692fc85c97591f09a2a9b0bccc8f71e97681cc09' }, header), hasSubHeaderOrMessage && (h(\"ion-item\", { key: 'ecab23444eaadc3ed21e7053d50890db1012475f' }, h(\"ion-label\", { key: '639f08137d7066fd79316f63e850ddcc6a3b54a7', class: \"ion-text-wrap\" }, subHeader !== undefined && h(\"h3\", { key: 'dc501101ac9d68b1d0ce80679b339a2b132d1ae9' }, subHeader), message !== undefined && h(\"p\", { key: '5ead8c1a2e90d29fe0f05e04a9fa65c7e9e62ca5' }, message)))), this.renderOptions(options))));\n    }\n    get el() { return getElement(this); }\n};\nSelectPopover.style = {\n    ios: IonSelectPopoverIosStyle0,\n    md: IonSelectPopoverMdStyle0\n};\n\nexport { Select as ion_select, SelectOption as ion_select_option, SelectPopover as ion_select_popover };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,WAAW,QAAQ,qBAAqB;AAC9H,SAASC,CAAC,IAAIC,0BAA0B,QAAQ,+BAA+B;AAC/E,SAASD,CAAC,IAAIE,qBAAqB,QAAQ,gCAAgC;AAC3E,SAASJ,CAAC,IAAIK,gBAAgB,EAAEH,CAAC,IAAII,cAAc,QAAQ,kCAAkC;AAC7F,SAASC,CAAC,IAAIC,iBAAiB,EAAEV,CAAC,IAAIW,mBAAmB,EAAEd,CAAC,IAAIe,aAAa,EAAEjB,CAAC,IAAIkB,iBAAiB,EAAEC,CAAC,IAAIC,YAAY,QAAQ,uBAAuB;AACvJ,SAASC,CAAC,IAAIC,eAAe,QAAQ,qBAAqB;AAC1D,SAASb,CAAC,IAAIc,iBAAiB,EAAEC,CAAC,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,QAAQ,QAAQ,wBAAwB;AAChI,SAAStB,CAAC,IAAIuB,KAAK,QAAQ,mBAAmB;AAC9C,SAAS5B,CAAC,IAAI6B,WAAW,EAAEtB,CAAC,IAAIuB,kBAAkB,EAAEC,CAAC,IAAIC,WAAW,QAAQ,qBAAqB;AACjG,SAASC,CAAC,IAAIC,eAAe,QAAQ,6BAA6B;AAClE,SAASD,CAAC,IAAIE,aAAa,EAAEC,CAAC,IAAIC,cAAc,QAAQ,qBAAqB;AAC7E,SAASf,CAAC,IAAIgB,UAAU,QAAQ,4BAA4B;AAC5D,OAAO,qBAAqB;AAC5B,OAAO,oCAAoC;AAC3C,OAAO,kCAAkC;AAEzC,MAAMC,YAAY,GAAG,q7RAAq7R;AAC18R,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,WAAW,GAAG,29jBAA29jB;AAC/+jB,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,MAAM,GAAG,MAAM;EACjBC,WAAWA,CAACC,OAAO,EAAE;IACjBhD,gBAAgB,CAAC,IAAI,EAAEgD,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAG/C,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACgD,SAAS,GAAGhD,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACiD,UAAU,GAAGjD,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IACpD,IAAI,CAACkD,QAAQ,GAAGlD,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACmD,OAAO,GAAGnD,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACoD,QAAQ,GAAGpD,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACqD,OAAO,GAAG,WAAWC,SAAS,EAAE,EAAE;IACvC,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B;IACA,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACnB,MAAMC,MAAM,GAAGD,EAAE,CAACC,MAAM;MACxB,MAAMC,WAAW,GAAGD,MAAM,CAACE,OAAO,CAAC,8BAA8B,CAAC;MAClE,IAAIF,MAAM,KAAK,IAAI,CAACG,EAAE,IAAIF,WAAW,KAAK,IAAI,EAAE;QAC5C,IAAI,CAACG,QAAQ,CAAC,CAAC;QACf,IAAI,CAACC,IAAI,CAACN,EAAE,CAAC;MACjB,CAAC,MACI;QACD;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgBA,EAAE,CAACO,cAAc,CAAC,CAAC;MACvB;IACJ,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAAChB,QAAQ,CAACiB,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAACjB,OAAO,CAACgB,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACE,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,UAAU,GAAG,QAAQ;IAC1B,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,WAAW,GAAGD,SAAS;IAC5B,IAAI,CAACE,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAGH,SAAS;IACrB,IAAI,CAACI,SAAS,GAAG,OAAO;IACxB,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,OAAO,GAAG,eAAe;IAC9B,IAAI,CAACC,KAAK,GAAGP,SAAS;IACtB,IAAI,CAACQ,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,MAAM,GAAGT,SAAS;IACvB,IAAI,CAACU,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAG,IAAI,CAAC9B,OAAO;IACxB,IAAI,CAAC+B,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAGb,SAAS;IAC5B,IAAI,CAACc,YAAY,GAAGd,SAAS;IAC7B,IAAI,CAACe,UAAU,GAAGf,SAAS;IAC3B,IAAI,CAACgB,YAAY,GAAGhB,SAAS;IAC7B,IAAI,CAACiB,KAAK,GAAGjB,SAAS;IACtB,IAAI,CAACkB,KAAK,GAAGlB,SAAS;EAC1B;EACAmB,YAAYA,CAAA,EAAG;IACX,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAC,QAAQA,CAACH,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC3C,SAAS,CAACoB,IAAI,CAAC;MAAEuB;IAAM,CAAC,CAAC;EAClC;EACAI,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACvC,mBAAmB,GAAGzC,iBAAiB,CAAC,IAAI,CAACgD,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC;EACzE;EACMiC,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,MAAM;QAAEnC;MAAG,CAAC,GAAGkC,KAAI;MACnBA,KAAI,CAACE,oBAAoB,GAAGzF,0BAA0B,CAACqD,EAAE,CAAC;MAC1DkC,KAAI,CAACG,eAAe,GAAGzF,qBAAqB,CAACoD,EAAE,EAAE,MAAMkC,KAAI,CAACI,aAAa,EAAE,MAAMJ,KAAI,CAACK,SAAS,CAAC;MAChGL,KAAI,CAACM,oBAAoB,CAAC,CAAC;MAC3BN,KAAI,CAACJ,SAAS,CAAC,CAAC;MAChBI,KAAI,CAACO,SAAS,GAAGpE,eAAe,CAAC6D,KAAI,CAAClC,EAAE,EAAE,mBAAmB,eAAAmC,iBAAA,CAAE,aAAY;QACvED,KAAI,CAACM,oBAAoB,CAAC,CAAC;QAC3B;AACZ;AACA;AACA;AACA;AACA;QACY/F,WAAW,CAACyF,KAAI,CAAC;MACrB,CAAC,EAAC;IAAC;EACP;EACAQ,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACD,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACE,UAAU,CAAC,CAAC;MAC3B,IAAI,CAACF,SAAS,GAAG/B,SAAS;IAC9B;IACA,IAAI,IAAI,CAAC2B,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACO,OAAO,CAAC,CAAC;MAC9B,IAAI,CAACP,eAAe,GAAG3B,SAAS;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACUR,IAAIA,CAAC2C,KAAK,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAX,iBAAA;MACd,IAAIW,MAAI,CAAClC,QAAQ,IAAIkC,MAAI,CAACvC,UAAU,EAAE;QAClC,OAAOG,SAAS;MACpB;MACAoC,MAAI,CAACvC,UAAU,GAAG,IAAI;MACtB,MAAMwC,OAAO,GAAID,MAAI,CAACC,OAAO,SAASD,MAAI,CAACE,aAAa,CAACH,KAAK,CAAE;MAChEE,OAAO,CAACE,YAAY,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAC9BJ,MAAI,CAACC,OAAO,GAAGrC,SAAS;QACxBoC,MAAI,CAACvC,UAAU,GAAG,KAAK;QACvBuC,MAAI,CAAC3D,UAAU,CAACkB,IAAI,CAAC,CAAC;QACtByC,MAAI,CAAC7C,QAAQ,CAAC,CAAC;MACnB,CAAC,CAAC;MACF,MAAM8C,OAAO,CAACI,OAAO,CAAC,CAAC;MACvB;MACA,IAAIL,MAAI,CAAChC,SAAS,KAAK,SAAS,EAAE;QAC9B,MAAMsC,eAAe,GAAGN,MAAI,CAACO,SAAS,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC3B,KAAK,CAAC,CAAC4B,OAAO,CAACV,MAAI,CAAClB,KAAK,CAAC;QAC9E,IAAIwB,eAAe,GAAG,CAAC,CAAC,EAAE;UACtB,MAAMK,YAAY,GAAGV,OAAO,CAACW,aAAa,CAAC,sCAAsCN,eAAe,GAAG,CAAC,GAAG,CAAC;UACxG,IAAIK,YAAY,EAAE;YACdxG,mBAAmB,CAACwG,YAAY,CAAC;YACjC;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;YACoB,MAAME,aAAa,GAAGF,YAAY,CAACC,aAAa,CAAC,yBAAyB,CAAC;YAC3E,IAAIC,aAAa,EAAE;cACfA,aAAa,CAACC,KAAK,CAAC,CAAC;YACzB;UACJ;QACJ,CAAC,MACI;UACD;AAChB;AACA;UACgB,MAAMC,kBAAkB,GAAGd,OAAO,CAACW,aAAa,CAAC,sEAAsE,CAAC;UACxH,IAAIG,kBAAkB,EAAE;YACpB5G,mBAAmB,CAAC4G,kBAAkB,CAAC9D,OAAO,CAAC,UAAU,CAAC,CAAC;YAC3D;AACpB;AACA;YACoB8D,kBAAkB,CAACD,KAAK,CAAC,CAAC;UAC9B;QACJ;MACJ;MACA,OAAOb,OAAO;IAAC;EACnB;EACAC,aAAaA,CAACpD,EAAE,EAAE;IACd,IAAIkE,eAAe,GAAG,IAAI,CAAChD,SAAS;IACpC,IAAIgD,eAAe,KAAK,cAAc,IAAI,IAAI,CAAC1C,QAAQ,EAAE;MACrD2C,OAAO,CAACC,IAAI,CAAC,+BAA+BF,eAAe,mEAAmE,CAAC;MAC/HA,eAAe,GAAG,OAAO;IAC7B;IACA,IAAIA,eAAe,KAAK,SAAS,IAAI,CAAClE,EAAE,EAAE;MACtCmE,OAAO,CAACC,IAAI,CAAC,iCAAiCF,eAAe,kEAAkE,CAAC;MAChIA,eAAe,GAAG,OAAO;IAC7B;IACA,IAAIA,eAAe,KAAK,cAAc,EAAE;MACpC,OAAO,IAAI,CAACG,eAAe,CAAC,CAAC;IACjC;IACA,IAAIH,eAAe,KAAK,SAAS,EAAE;MAC/B,OAAO,IAAI,CAACI,WAAW,CAACtE,EAAE,CAAC;IAC/B;IACA,OAAO,IAAI,CAACuE,SAAS,CAAC,CAAC;EAC3B;EACA3B,oBAAoBA,CAAA,EAAG;IACnB,MAAMO,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAI,CAACA,OAAO,EAAE;MACV;IACJ;IACA,MAAMM,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMzB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,QAAQ,IAAI,CAACd,SAAS;MAClB,KAAK,cAAc;QACfiC,OAAO,CAACqB,OAAO,GAAG,IAAI,CAACC,wBAAwB,CAAChB,SAAS,EAAEzB,KAAK,CAAC;QACjE;MACJ,KAAK,SAAS;QACV,MAAM0C,OAAO,GAAGvB,OAAO,CAACW,aAAa,CAAC,oBAAoB,CAAC;QAC3D,IAAIY,OAAO,EAAE;UACTA,OAAO,CAACC,OAAO,GAAG,IAAI,CAACC,oBAAoB,CAACnB,SAAS,EAAEzB,KAAK,CAAC;QACjE;QACA;MACJ,KAAK,OAAO;QACR,MAAM6C,SAAS,GAAG,IAAI,CAACrD,QAAQ,GAAG,UAAU,GAAG,OAAO;QACtD2B,OAAO,CAAC2B,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAACtB,SAAS,EAAEoB,SAAS,EAAE7C,KAAK,CAAC;QACpE;IACR;EACJ;EACAyC,wBAAwBA,CAACO,IAAI,EAAEC,WAAW,EAAE;IACxC,MAAMC,kBAAkB,GAAGF,IAAI,CAACtB,GAAG,CAAEyB,MAAM,IAAK;MAC5C,MAAMnD,KAAK,GAAGoD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC3CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACd,MAAMC,QAAQ,GAAG,GAAGC,YAAY,IAAIR,WAAW,EAAE;MACjD,OAAO;QACHS,IAAI,EAAE7I,gBAAgB,CAACgI,WAAW,EAAEjD,KAAK,EAAE,IAAI,CAACjB,WAAW,CAAC,GAAG,UAAU,GAAG,EAAE;QAC9EgF,IAAI,EAAEZ,MAAM,CAACa,WAAW;QACxBC,QAAQ,EAAEL,QAAQ;QAClBM,OAAO,EAAEA,CAAA,KAAM;UACX,IAAI,CAAC/D,QAAQ,CAACH,KAAK,CAAC;QACxB;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAkD,kBAAkB,CAACiB,IAAI,CAAC;MACpBJ,IAAI,EAAE,IAAI,CAACnF,UAAU;MACrBkF,IAAI,EAAE,QAAQ;MACdI,OAAO,EAAEA,CAAA,KAAM;QACX,IAAI,CAAC5G,SAAS,CAACmB,IAAI,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;IACF,OAAOyE,kBAAkB;EAC7B;EACAH,iBAAiBA,CAACC,IAAI,EAAEH,SAAS,EAAEI,WAAW,EAAE;IAC5C,MAAMmB,WAAW,GAAGpB,IAAI,CAACtB,GAAG,CAAEyB,MAAM,IAAK;MACrC,MAAMnD,KAAK,GAAGoD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC3CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACd,MAAMC,QAAQ,GAAG,GAAGC,YAAY,IAAIR,WAAW,EAAE;MACjD,OAAO;QACHgB,IAAI,EAAExB,SAAS;QACfoB,QAAQ,EAAEL,QAAQ;QAClBvE,KAAK,EAAE8D,MAAM,CAACa,WAAW,IAAI,EAAE;QAC/BhE,KAAK;QACLsE,OAAO,EAAErJ,gBAAgB,CAACgI,WAAW,EAAEjD,KAAK,EAAE,IAAI,CAACjB,WAAW,CAAC;QAC/DC,QAAQ,EAAEmE,MAAM,CAACnE;MACrB,CAAC;IACL,CAAC,CAAC;IACF,OAAOoF,WAAW;EACtB;EACAxB,oBAAoBA,CAACI,IAAI,EAAEC,WAAW,EAAE;IACpC,MAAMsB,cAAc,GAAGvB,IAAI,CAACtB,GAAG,CAAEyB,MAAM,IAAK;MACxC,MAAMnD,KAAK,GAAGoD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC3CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACd,MAAMC,QAAQ,GAAG,GAAGC,YAAY,IAAIR,WAAW,EAAE;MACjD,OAAO;QACHU,IAAI,EAAEZ,MAAM,CAACa,WAAW,IAAI,EAAE;QAC9BC,QAAQ,EAAEL,QAAQ;QAClB5D,KAAK;QACLsE,OAAO,EAAErJ,gBAAgB,CAACgI,WAAW,EAAEjD,KAAK,EAAE,IAAI,CAACjB,WAAW,CAAC;QAC/DC,QAAQ,EAAEmE,MAAM,CAACnE,QAAQ;QACzBkF,OAAO,EAAGM,QAAQ,IAAK;UACnB,IAAI,CAACrE,QAAQ,CAACqE,QAAQ,CAAC;UACvB,IAAI,CAAC,IAAI,CAAChF,QAAQ,EAAE;YAChB,IAAI,CAACiF,KAAK,CAAC,CAAC;UAChB;QACJ;MACJ,CAAC;IACL,CAAC,CAAC;IACF,OAAOF,cAAc;EACzB;EACMjC,WAAWA,CAACtE,EAAE,EAAE;IAAA,IAAA0G,MAAA;IAAA,OAAAnE,iBAAA;MAClB,MAAM;QAAEtB,IAAI;QAAEK;MAAe,CAAC,GAAGoF,MAAI;MACrC,MAAMvF,gBAAgB,GAAGuF,MAAI,CAACvF,gBAAgB;MAC9C,MAAMwF,IAAI,GAAG9H,UAAU,CAAC6H,MAAI,CAAC;MAC7B,MAAME,YAAY,GAAGD,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI;MACjD,MAAMnF,QAAQ,GAAGkF,MAAI,CAAClF,QAAQ;MAC9B,MAAMQ,KAAK,GAAG0E,MAAI,CAAC1E,KAAK;MACxB,IAAIiB,KAAK,GAAGjD,EAAE;MACd,IAAI6G,IAAI,GAAG,MAAM;MACjB,IAAIH,MAAI,CAAClE,oBAAoB,CAACsE,gBAAgB,CAAC,CAAC,EAAE;QAC9C,MAAMC,IAAI,GAAGL,MAAI,CAACtG,EAAE,CAACD,OAAO,CAAC,UAAU,CAAC;QACxC;QACA;QACA;QACA,IAAI4G,IAAI,KAAKA,IAAI,CAACvB,SAAS,CAACwB,QAAQ,CAAC,qBAAqB,CAAC,IAAID,IAAI,CAACvB,SAAS,CAACwB,QAAQ,CAAC,oBAAoB,CAAC,CAAC,EAAE;UAC3G/D,KAAK,GAAGgE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElH,EAAE,CAAC,EAAE;YAAEmH,MAAM,EAAE;cAC/CC,eAAe,EAAEL;YACrB;UAAE,CAAC,CAAC;UACRF,IAAI,GAAG,OAAO;QAClB;MACJ,CAAC,MACI;QACD,MAAMQ,yBAAyB,GAAG/F,cAAc,KAAK,UAAU,IAAIA,cAAc,KAAK,SAAS;QAC/F;AACZ;AACA;AACA;AACA;QACY,IAAI+F,yBAAyB,IAAKV,IAAI,KAAK,IAAI,IAAI1F,IAAI,KAAKH,SAAU,EAAE;UACpE+F,IAAI,GAAG,OAAO;UACd;AAChB;AACA;AACA;AACA;QACY,CAAC,MACI;UACD5D,KAAK,GAAGgE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElH,EAAE,CAAC,EAAE;YAAEmH,MAAM,EAAE;cAC/CC,eAAe,EAAEV,MAAI,CAACY;YAC1B;UAAE,CAAC,CAAC;QACZ;MACJ;MACA,MAAMC,WAAW,GAAGN,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEP,IAAI;QAClD1D,KAAK;QAAEuE,SAAS,EAAE,QAAQ;QAAEX,IAAI;QAChCD;MAAa,CAAC,EAAEzF,gBAAgB,CAAC,EAAE;QAAEsG,SAAS,EAAE,oBAAoB;QAAExB,QAAQ,EAAE,CAAC,gBAAgB,EAAE9E,gBAAgB,CAAC8E,QAAQ,CAAC;QAAEyB,cAAc,EAAE;UAC3IC,MAAM,EAAExG,gBAAgB,CAACwG,MAAM;UAC/BC,SAAS,EAAEzG,gBAAgB,CAACyG,SAAS;UACrCC,OAAO,EAAE1G,gBAAgB,CAAC0G,OAAO;UACjCrG,QAAQ;UACRQ,KAAK;UACL2C,OAAO,EAAE+B,MAAI,CAAC9B,oBAAoB,CAAC8B,MAAI,CAACjD,SAAS,EAAEzB,KAAK;QAC5D;MAAE,CAAC,CAAC;MACR,OAAOpE,iBAAiB,CAACkK,MAAM,CAACP,WAAW,CAAC;IAAC;EACjD;EACMlD,eAAeA,CAAA,EAAG;IAAA,IAAA0D,MAAA;IAAA,OAAAxF,iBAAA;MACpB,MAAMoE,IAAI,GAAG9H,UAAU,CAACkJ,MAAI,CAAC;MAC7B,MAAM5G,gBAAgB,GAAG4G,MAAI,CAAC5G,gBAAgB;MAC9C,MAAM6G,eAAe,GAAGf,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEP;MAAK,CAAC,EAAExF,gBAAgB,CAAC,EAAE;QAAEqD,OAAO,EAAEuD,MAAI,CAACtD,wBAAwB,CAACsD,MAAI,CAACtE,SAAS,EAAEsE,MAAI,CAAC/F,KAAK,CAAC;QAAEiE,QAAQ,EAAE,CAAC,qBAAqB,EAAE9E,gBAAgB,CAAC8E,QAAQ;MAAE,CAAC,CAAC;MACtN,OAAOnI,qBAAqB,CAACgK,MAAM,CAACE,eAAe,CAAC;IAAC;EACzD;EACMzD,SAASA,CAAA,EAAG;IAAA,IAAA0D,MAAA;IAAA,OAAA1F,iBAAA;MACd;AACR;AACA;AACA;AACA;AACA;AACA;MACQ,IAAIlB,KAAK;MACT,IAAI6G,SAAS;MACb,IAAID,MAAI,CAACzF,oBAAoB,CAACsE,gBAAgB,CAAC,CAAC,EAAE;QAC9CzF,KAAK,GAAG4G,MAAI,CAACE,QAAQ,CAAC,CAAC;QACvBD,SAAS,GAAG7G,KAAK,GAAGA,KAAK,CAAC2E,WAAW,GAAG,IAAI;MAChD,CAAC,MACI;QACDkC,SAAS,GAAGD,MAAI,CAACC,SAAS;MAC9B;MACA,MAAM/G,gBAAgB,GAAG8G,MAAI,CAAC9G,gBAAgB;MAC9C,MAAM0D,SAAS,GAAGoD,MAAI,CAACzG,QAAQ,GAAG,UAAU,GAAG,OAAO;MACtD,MAAMmF,IAAI,GAAG9H,UAAU,CAACoJ,MAAI,CAAC;MAC7B,MAAMG,SAAS,GAAGnB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEP;MAAK,CAAC,EAAExF,gBAAgB,CAAC,EAAE;QAAEwG,MAAM,EAAExG,gBAAgB,CAACwG,MAAM,GAAGxG,gBAAgB,CAACwG,MAAM,GAAGO,SAAS;QAAEpD,MAAM,EAAEmD,MAAI,CAAClD,iBAAiB,CAACkD,MAAI,CAACxE,SAAS,EAAEoB,SAAS,EAAEoD,MAAI,CAACjG,KAAK,CAAC;QAAEwC,OAAO,EAAE,CAC5N;UACIuB,IAAI,EAAEkC,MAAI,CAACrH,UAAU;UACrBkF,IAAI,EAAE,QAAQ;UACdI,OAAO,EAAEA,CAAA,KAAM;YACX+B,MAAI,CAAC3I,SAAS,CAACmB,IAAI,CAAC,CAAC;UACzB;QACJ,CAAC,EACD;UACIsF,IAAI,EAAEkC,MAAI,CAACvG,MAAM;UACjBwE,OAAO,EAAGmC,cAAc,IAAK;YACzBJ,MAAI,CAAC9F,QAAQ,CAACkG,cAAc,CAAC;UACjC;QACJ,CAAC,CACJ;QAAEpC,QAAQ,EAAE,CACT,cAAc,EACd9E,gBAAgB,CAAC8E,QAAQ,EACzBgC,MAAI,CAACzG,QAAQ,GAAG,uBAAuB,GAAG,qBAAqB;MACjE,CAAC,CAAC;MACR,OAAOxD,eAAe,CAAC8J,MAAM,CAACM,SAAS,CAAC;IAAC;EAC7C;EACA;AACJ;AACA;EACI3B,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACtD,OAAO,EAAE;MACf,OAAOmF,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,OAAO,IAAI,CAACpF,OAAO,CAACqF,OAAO,CAAC,CAAC;EACjC;EACA;EACAL,QAAQA,CAAA,EAAG;IACP,OAAO7K,aAAa,CAAC,IAAI,CAAC8C,EAAE,CAAC;EACjC;EACAqI,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC,KAAK,EAAE;EAChC;EACA,IAAIjF,SAASA,CAAA,EAAG;IACZ,OAAO6B,KAAK,CAACC,IAAI,CAAC,IAAI,CAACnF,EAAE,CAACuI,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;EACpE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIT,SAASA,CAAA,EAAG;IACZ,MAAM;MAAE7G;IAAM,CAAC,GAAG,IAAI;IACtB,IAAIA,KAAK,KAAKP,SAAS,EAAE;MACrB,OAAOO,KAAK;IAChB;IACA,MAAM;MAAEsB;IAAU,CAAC,GAAG,IAAI;IAC1B,IAAIA,SAAS,KAAK,IAAI,EAAE;MACpB,OAAOA,SAAS,CAACqD,WAAW;IAChC;IACA;EACJ;EACA0C,OAAOA,CAAA,EAAG;IACN,MAAM9G,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,IAAIA,YAAY,IAAI,IAAI,IAAIA,YAAY,KAAK,EAAE,EAAE;MAC7C,OAAOA,YAAY;IACvB;IACA,OAAOgH,YAAY,CAAC,IAAI,CAACnF,SAAS,EAAE,IAAI,CAACzB,KAAK,EAAE,IAAI,CAACjB,WAAW,CAAC;EACrE;EACAV,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACwI,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAAC7E,KAAK,CAAC,CAAC;IACxB;EACJ;EACA9B,SAASA,CAAA,EAAG;IACR,MAAM;MAAElB;IAAS,CAAC,GAAG,IAAI;IACzB,MAAM8H,KAAK,GAAG;MACV,sBAAsB,EAAE9H;IAC5B,CAAC;IACD,IAAI,IAAI,CAACwB,oBAAoB,CAACsE,gBAAgB,CAAC,CAAC,EAAE;MAC9CgC,KAAK,CAAC,aAAa,CAAC,GAAG,IAAI;MAC3BA,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI;MACtBA,KAAK,CAAC,iBAAiB,CAAC,GAAG9H,QAAQ;MACnC8H,KAAK,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAACnH,WAAW,KAAKb,SAAS;MACzDgI,KAAK,CAAC,WAAW,CAAC,GAAG,IAAI,CAACL,QAAQ,CAAC,CAAC;MACpCK,KAAK,CAAC,WAAW,CAAC,GAAG,IAAI,CAACnI,UAAU;MACpC;MACAmI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAACvH,MAAM;IACnC;IACA,IAAI,CAAC7B,QAAQ,CAACe,IAAI,CAACqI,KAAK,CAAC;EAC7B;EACAC,WAAWA,CAAA,EAAG;IACV,MAAM;MAAE1H;IAAM,CAAC,GAAG,IAAI;IACtB,OAAQ9E,CAAC,CAAC,KAAK,EAAE;MAAEyM,KAAK,EAAE;QAClB,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAAC,IAAI,CAACC;MACvC,CAAC;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAAE7H,KAAK,KAAKP,SAAS,GAAGvE,CAAC,CAAC,MAAM,EAAE;MAAEkF,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAGlF,CAAC,CAAC,KAAK,EAAE;MAAEyM,KAAK,EAAE;IAAa,CAAC,EAAE3H,KAAK,CAAC,CAAC;EAC1H;EACA8H,kBAAkBA,CAAA,EAAG;IACjB,IAAIC,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAAC3G,eAAe,MAAM,IAAI,IAAI2G,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,mBAAmB,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;EACI,IAAI1G,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACvC,EAAE,CAAC0D,aAAa,CAAC,gBAAgB,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAImF,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC5H,KAAK,KAAKP,SAAS,IAAI,IAAI,CAAC6B,SAAS,KAAK,IAAI;EAC9D;EACA;AACJ;AACA;AACA;EACI2G,oBAAoBA,CAAA,EAAG;IACnB,MAAM3C,IAAI,GAAG9H,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM0K,cAAc,GAAG5C,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC1F,IAAI,KAAK,SAAS;IAC/D,IAAIsI,cAAc,EAAE;MAChB;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,OAAO,CACHhN,CAAC,CAAC,KAAK,EAAE;QAAEyM,KAAK,EAAE;MAA2B,CAAC,EAAEzM,CAAC,CAAC,KAAK,EAAE;QAAEyM,KAAK,EAAE;MAAuB,CAAC,CAAC,EAAEzM,CAAC,CAAC,KAAK,EAAE;QAAEyM,KAAK,EAAE;UACvG,sBAAsB,EAAE,IAAI;UAC5B,6BAA6B,EAAE,CAAC,IAAI,CAACC;QACzC;MAAE,CAAC,EAAE1M,CAAC,CAAC,KAAK,EAAE;QAAEyM,KAAK,EAAE,cAAc;QAAE,aAAa,EAAE,MAAM;QAAEQ,GAAG,EAAGpJ,EAAE,IAAM,IAAI,CAACsC,aAAa,GAAGtC;MAAI,CAAC,EAAE,IAAI,CAACiB,KAAK,CAAC,CAAC,EAAE9E,CAAC,CAAC,KAAK,EAAE;QAAEyM,KAAK,EAAE;MAAqB,CAAC,CAAC,CAAC,EACpK,IAAI,CAACD,WAAW,CAAC,CAAC,CACrB;IACL;IACA;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACA,WAAW,CAAC,CAAC;EAC7B;EACAU,YAAYA,CAAA,EAAG;IACX,MAAM;MAAEzI,QAAQ;MAAEZ,EAAE;MAAEO,UAAU;MAAEmB,YAAY;MAAER,cAAc;MAAEF,OAAO;MAAEO,WAAW;MAAEV,IAAI;MAAEc,KAAK;MAAEN,IAAI;MAAEO;IAAM,CAAC,GAAG,IAAI;IACvH,MAAM2E,IAAI,GAAG9H,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMwI,yBAAyB,GAAG/F,cAAc,KAAK,UAAU,IAAIA,cAAc,KAAK,SAAS;IAC/F,MAAMoI,cAAc,GAAG,CAACrC,yBAAyB;IACjD,MAAMsC,GAAG,GAAGxL,KAAK,CAACiC,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK;IACrC,MAAMwJ,MAAM,GAAGxL,WAAW,CAAC,UAAU,EAAE,IAAI,CAACgC,EAAE,CAAC;IAC/C,MAAMyJ,qBAAqB,GAAGlD,IAAI,KAAK,IAAI,IAAI1F,IAAI,KAAK,SAAS,IAAI,CAAC2I,MAAM;IAC5E,MAAMnB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;IAChC,MAAMqB,gBAAgB,GAAG1J,EAAE,CAAC0D,aAAa,CAAC,8BAA8B,CAAC,KAAK,IAAI;IAClFvG,iBAAiB,CAAC,IAAI,EAAE6C,EAAE,EAAEqB,IAAI,EAAEsI,UAAU,CAAC/H,KAAK,CAAC,EAAEhB,QAAQ,CAAC;IAC9D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMgJ,gBAAgB,GAAG1I,cAAc,KAAK,SAAS,IAAKA,cAAc,KAAK,UAAU,KAAKmH,QAAQ,IAAI9H,UAAU,IAAImJ,gBAAgB,CAAE;IACxI,OAAQvN,CAAC,CAACE,IAAI,EAAE;MAAEsD,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEiJ,KAAK,EAAE3K,kBAAkB,CAAC,IAAI,CAACwC,KAAK,EAAE;QACvE,CAAC8F,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEiD,MAAM;QACjB,eAAe,EAAExL,WAAW,CAAC,oBAAoB,EAAEgC,EAAE,CAAC;QACtD,iBAAiB,EAAEY,QAAQ;QAC3B,iBAAiB,EAAEL,UAAU;QAC7B,mBAAmB,EAAEmB,YAAY,KAAKhB,SAAS;QAC/C,WAAW,EAAE2H,QAAQ;QACrB,gBAAgB,EAAEuB,gBAAgB;QAClC,iBAAiB,EAAErI,WAAW,KAAKb,SAAS;QAC5C,eAAe,EAAE,IAAI;QACrB,CAAC,UAAU6I,GAAG,EAAE,GAAG,IAAI;QACvB,CAAC,eAAe1I,IAAI,EAAE,GAAGA,IAAI,KAAKH,SAAS;QAC3C,CAAC,kBAAkBM,OAAO,EAAE,GAAGsI,cAAc;QAC7C,CAAC,gBAAgB3H,KAAK,EAAE,GAAGA,KAAK,KAAKjB,SAAS;QAC9C,CAAC,0BAA0BQ,cAAc,EAAE,GAAG;MAClD,CAAC;IAAE,CAAC,EAAE/E,CAAC,CAAC,OAAO,EAAE;MAAEyM,KAAK,EAAE,gBAAgB;MAAEiB,EAAE,EAAE;IAAe,CAAC,EAAE,IAAI,CAACX,oBAAoB,CAAC,CAAC,EAAE/M,CAAC,CAAC,KAAK,EAAE;MAAEyM,KAAK,EAAE;IAAuB,CAAC,EAAEzM,CAAC,CAAC,MAAM,EAAE;MAAEkF,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAElF,CAAC,CAAC,KAAK,EAAE;MAAEyM,KAAK,EAAE,gBAAgB;MAAEQ,GAAG,EAAGpJ,EAAE,IAAM,IAAI,CAACkH,eAAe,GAAGlH,EAAG;MAAE8I,IAAI,EAAE;IAAY,CAAC,EAAE,IAAI,CAACgB,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,EAAE5N,CAAC,CAAC,MAAM,EAAE;MAAEkF,IAAI,EAAE;IAAM,CAAC,CAAC,EAAE,CAAC4F,yBAAyB,IAAI,IAAI,CAAC+C,gBAAgB,CAAC,CAAC,CAAC,EAAE/C,yBAAyB,IAAI,IAAI,CAAC+C,gBAAgB,CAAC,CAAC,EAAEP,qBAAqB,IAAItN,CAAC,CAAC,KAAK,EAAE;MAAEyM,KAAK,EAAE;IAAmB,CAAC,CAAC,CAAC,CAAC;EAChhB;EACA;EACAqB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACvK,2BAA2B,EAAE;MACnCnC,eAAe,CAAC;AAC5B;AACA;AACA;AACA;AACA,gNAAgN,EAAE,IAAI,CAACyC,EAAE,CAAC;MAC9M,IAAI,IAAI,CAACmB,MAAM,EAAE;QACb5D,eAAe,CAAC;AAChC,0HAA0H,EAAE,IAAI,CAACyC,EAAE,CAAC;MACxH;MACA,IAAI,CAACN,2BAA2B,GAAG,IAAI;IAC3C;IACA,MAAM;MAAEkB,QAAQ;MAAEZ,EAAE;MAAET,OAAO;MAAEgB,UAAU;MAAEmB,YAAY;MAAEL,IAAI;MAAEE,WAAW;MAAEK;IAAM,CAAC,GAAG,IAAI;IAC1F,MAAM2E,IAAI,GAAG9H,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAEqJ,SAAS;MAAEoC;IAAQ,CAAC,GAAG7M,YAAY,CAAC2C,EAAE,EAAET,OAAO,CAAC;IACxDpC,iBAAiB,CAAC,IAAI,EAAE6C,EAAE,EAAEqB,IAAI,EAAEsI,UAAU,CAAC/H,KAAK,CAAC,EAAEhB,QAAQ,CAAC;IAC9D,MAAMuJ,YAAY,GAAG,IAAI,CAAC7B,OAAO,CAAC,CAAC;IACnC,IAAI8B,UAAU,GAAGD,YAAY;IAC7B,IAAIC,UAAU,KAAK,EAAE,IAAI7I,WAAW,KAAKb,SAAS,EAAE;MAChD0J,UAAU,GAAG7I,WAAW;IAC5B;IACA;IACA;IACA;IACA;IACA,MAAM8I,YAAY,GAAGvC,SAAS,KAAKpH,SAAS,GAAI0J,UAAU,KAAK,EAAE,GAAG,GAAGA,UAAU,KAAKtC,SAAS,EAAE,GAAGA,SAAS,GAAIsC,UAAU;IAC3H,OAAQjO,CAAC,CAACE,IAAI,EAAE;MAAEsD,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE+F,IAAI,EAAE,QAAQ;MAAE,eAAe,EAAE,SAAS;MAAE,eAAe,EAAE9E,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE,YAAY,EAAEyJ,YAAY;MAAEzB,KAAK,EAAE;QAC3J,CAACrC,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEvI,WAAW,CAAC,UAAU,EAAEgC,EAAE,CAAC;QACtC,eAAe,EAAEhC,WAAW,CAAC,oBAAoB,EAAEgC,EAAE,CAAC;QACtD,iBAAiB,EAAEY,QAAQ;QAC3B,iBAAiB,EAAEL,UAAU;QAC7B,mBAAmB,EAAEmB,YAAY,KAAKhB,SAAS;QAC/C,eAAe,EAAE;MACrB;IAAE,CAAC,EAAE,IAAI,CAACoJ,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACE,gBAAgB,CAAC,CAAC,EAAE7N,CAAC,CAAC,OAAO,EAAE;MAAE0N,EAAE,EAAEK;IAAQ,CAAC,EAAEG,YAAY,CAAC,EAAE,IAAI,CAACN,aAAa,CAAC,CAAC,CAAC;EAC/H;EACA;AACJ;AACA;AACA;AACA;EACID,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAEvI;IAAY,CAAC,GAAG,IAAI;IAC5B,MAAM4I,YAAY,GAAG,IAAI,CAAC7B,OAAO,CAAC,CAAC;IACnC,IAAIgC,mBAAmB,GAAG,KAAK;IAC/B,IAAIF,UAAU,GAAGD,YAAY;IAC7B,IAAIC,UAAU,KAAK,EAAE,IAAI7I,WAAW,KAAKb,SAAS,EAAE;MAChD0J,UAAU,GAAG7I,WAAW;MACxB+I,mBAAmB,GAAG,IAAI;IAC9B;IACA,MAAMC,iBAAiB,GAAG;MACtB,aAAa,EAAE,IAAI;MACnB,oBAAoB,EAAED;IAC1B,CAAC;IACD,MAAME,QAAQ,GAAGF,mBAAmB,GAAG,aAAa,GAAG,MAAM;IAC7D,OAAQnO,CAAC,CAAC,KAAK,EAAE;MAAE,aAAa,EAAE,MAAM;MAAEyM,KAAK,EAAE2B,iBAAiB;MAAEzB,IAAI,EAAE0B;IAAS,CAAC,EAAEJ,UAAU,CAAC;EACrG;EACA;AACJ;AACA;AACA;EACIJ,gBAAgBA,CAAA,EAAG;IACf,MAAMzD,IAAI,GAAG9H,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAE8B,UAAU;MAAEkB,UAAU;MAAEC;IAAa,CAAC,GAAG,IAAI;IACrD,IAAI+I,IAAI;IACR,IAAIlK,UAAU,IAAImB,YAAY,KAAKhB,SAAS,EAAE;MAC1C+J,IAAI,GAAG/I,YAAY;IACvB,CAAC,MACI;MACD,MAAMgJ,WAAW,GAAGnE,IAAI,KAAK,KAAK,GAAGjI,aAAa,GAAGE,cAAc;MACnEiM,IAAI,GAAGhJ,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGiJ,WAAW;IAClF;IACA,OAAOvO,CAAC,CAAC,UAAU,EAAE;MAAEyM,KAAK,EAAE,aAAa;MAAEE,IAAI,EAAE,MAAM;MAAE,aAAa,EAAE,MAAM;MAAE2B,IAAI,EAAEA;IAAK,CAAC,CAAC;EACnG;EACA,IAAIE,SAASA,CAAA,EAAG;IACZ,IAAI3B,EAAE,EAAE4B,EAAE;IACV,MAAM;MAAErJ,WAAW;MAAEvB,EAAE;MAAET,OAAO;MAAEE;IAAoB,CAAC,GAAG,IAAI;IAC9D,MAAM0K,YAAY,GAAG,IAAI,CAAC7B,OAAO,CAAC,CAAC;IACnC,MAAM;MAAER;IAAU,CAAC,GAAGzK,YAAY,CAAC2C,EAAE,EAAET,OAAO,CAAC;IAC/C,MAAMsL,YAAY,GAAG,CAACD,EAAE,GAAG,CAAC5B,EAAE,GAAG,IAAI,CAAClB,SAAS,MAAM,IAAI,IAAIkB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGvJ,mBAAmB,CAAC,YAAY,CAAC,MAAM,IAAI,IAAImL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG9C,SAAS;IAC/J;AACR;AACA;AACA;AACA;IACQ,IAAIgD,aAAa,GAAGX,YAAY;IAChC,IAAIW,aAAa,KAAK,EAAE,IAAIvJ,WAAW,KAAKb,SAAS,EAAE;MACnDoK,aAAa,GAAGvJ,WAAW;IAC/B;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIsJ,YAAY,KAAKnK,SAAS,EAAE;MAC5BoK,aAAa,GAAGA,aAAa,KAAK,EAAE,GAAGD,YAAY,GAAG,GAAGA,YAAY,KAAKC,aAAa,EAAE;IAC7F;IACA,OAAOA,aAAa;EACxB;EACAf,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAEnJ,QAAQ;MAAErB,OAAO;MAAEgB;IAAW,CAAC,GAAG,IAAI;IAC9C,OAAQpE,CAAC,CAAC,QAAQ,EAAE;MAAEyE,QAAQ,EAAEA,QAAQ;MAAEiJ,EAAE,EAAEtK,OAAO;MAAE,YAAY,EAAE,IAAI,CAACoL,SAAS;MAAE,eAAe,EAAE,QAAQ;MAAE,eAAe,EAAE,GAAGpK,UAAU,EAAE;MAAEH,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAE8I,GAAG,EAAGX,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,CAAC;EAC/O;EACAsC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE3I;IAAqB,CAAC,GAAG,IAAI;IACrC,OAAOA,oBAAoB,CAACsE,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACuD,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACZ,YAAY,CAAC,CAAC;EACpG;EACA,IAAIrJ,EAAEA,CAAA,EAAG;IAAE,OAAOzD,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWyO,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,cAAc,CAAC;MAC5B,YAAY,EAAE,CAAC,cAAc,CAAC;MAC9B,aAAa,EAAE,CAAC,cAAc,CAAC;MAC/B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,MAAMhG,cAAc,GAAIhF,EAAE,IAAK;EAC3B,MAAM4B,KAAK,GAAG5B,EAAE,CAAC4B,KAAK;EACtB,OAAOA,KAAK,KAAKlB,SAAS,GAAGV,EAAE,CAAC4F,WAAW,IAAI,EAAE,GAAGhE,KAAK;AAC7D,CAAC;AACD,MAAM+H,UAAU,GAAI/H,KAAK,IAAK;EAC1B,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,OAAOlB,SAAS;EACpB;EACA,IAAIwE,KAAK,CAAC+F,OAAO,CAACrJ,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CAAC2D,IAAI,CAAC,GAAG,CAAC;EAC1B;EACA,OAAO3D,KAAK,CAACsJ,QAAQ,CAAC,CAAC;AAC3B,CAAC;AACD,MAAM1C,YAAY,GAAGA,CAAC2C,IAAI,EAAEvJ,KAAK,EAAEjB,WAAW,KAAK;EAC/C,IAAIiB,KAAK,KAAKlB,SAAS,EAAE;IACrB,OAAO,EAAE;EACb;EACA,IAAIwE,KAAK,CAAC+F,OAAO,CAACrJ,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CACP0B,GAAG,CAAE8H,CAAC,IAAKC,YAAY,CAACF,IAAI,EAAEC,CAAC,EAAEzK,WAAW,CAAC,CAAC,CAC9C0E,MAAM,CAAEiG,GAAG,IAAKA,GAAG,KAAK,IAAI,CAAC,CAC7B/F,IAAI,CAAC,IAAI,CAAC;EACnB,CAAC,MACI;IACD,OAAO8F,YAAY,CAACF,IAAI,EAAEvJ,KAAK,EAAEjB,WAAW,CAAC,IAAI,EAAE;EACvD;AACJ,CAAC;AACD,MAAM0K,YAAY,GAAGA,CAACF,IAAI,EAAEvJ,KAAK,EAAEjB,WAAW,KAAK;EAC/C,MAAM4K,SAAS,GAAGJ,IAAI,CAACK,IAAI,CAAEF,GAAG,IAAK;IACjC,OAAOxO,cAAc,CAAC8E,KAAK,EAAEoD,cAAc,CAACsG,GAAG,CAAC,EAAE3K,WAAW,CAAC;EAClE,CAAC,CAAC;EACF,OAAO4K,SAAS,GAAGA,SAAS,CAAC3F,WAAW,GAAG,IAAI;AACnD,CAAC;AACD,IAAIpG,SAAS,GAAG,CAAC;AACjB,MAAMiG,YAAY,GAAG,yBAAyB;AAC9C3G,MAAM,CAAC4J,KAAK,GAAG;EACX+C,GAAG,EAAE9M,kBAAkB;EACvB+M,EAAE,EAAE7M;AACR,CAAC;AAED,MAAM8M,eAAe,GAAG,qBAAqB;AAC7C,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,YAAY,GAAG,MAAM;EACvB9M,WAAWA,CAACC,OAAO,EAAE;IACjBhD,gBAAgB,CAAC,IAAI,EAAEgD,OAAO,CAAC;IAC/B,IAAI,CAACO,OAAO,GAAG,cAAcuM,eAAe,EAAE,EAAE;IAChD,IAAI,CAAClL,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACgB,KAAK,GAAGlB,SAAS;EAC1B;EACAqK,MAAMA,CAAA,EAAG;IACL,OAAO5O,CAAC,CAACE,IAAI,EAAE;MAAE0P,GAAG,EAAE,0CAA0C;MAAErG,IAAI,EAAE,QAAQ;MAAEmE,EAAE,EAAE,IAAI,CAACtK,OAAO;MAAEqJ,KAAK,EAAEnK,UAAU,CAAC,IAAI;IAAE,CAAC,CAAC;EAClI;EACA,IAAIuB,EAAEA,CAAA,EAAG;IAAE,OAAOzD,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD,IAAIuP,eAAe,GAAG,CAAC;AACvBD,YAAY,CAACnD,KAAK,GAAGkD,qBAAqB;AAE1C,MAAMI,mBAAmB,GAAG,iTAAiT;AAC7U,MAAMC,yBAAyB,GAAGD,mBAAmB;AAErD,MAAME,kBAAkB,GAAG,qhCAAqhC;AAChjC,MAAMC,wBAAwB,GAAGD,kBAAkB;AAEnD,MAAME,aAAa,GAAG,MAAM;EACxBrN,WAAWA,CAACC,OAAO,EAAE;IACjBhD,gBAAgB,CAAC,IAAI,EAAEgD,OAAO,CAAC;IAC/B,IAAI,CAACuI,MAAM,GAAG7G,SAAS;IACvB,IAAI,CAAC8G,SAAS,GAAG9G,SAAS;IAC1B,IAAI,CAAC+G,OAAO,GAAG/G,SAAS;IACxB,IAAI,CAACU,QAAQ,GAAGV,SAAS;IACzB,IAAI,CAAC6D,OAAO,GAAG,EAAE;EACrB;EACA8H,mBAAmBA,CAACzM,EAAE,EAAE;IACpB,MAAM;MAAE2E;IAAQ,CAAC,GAAG,IAAI;IACxB,OAAOA,OAAO,CAACiH,IAAI,CAAEjI,CAAC,IAAKA,CAAC,CAAC3B,KAAK,KAAKhC,EAAE,CAACC,MAAM,CAAC+B,KAAK,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;EACI0K,iBAAiBA,CAAC1M,EAAE,EAAE;IAClB,MAAMmF,MAAM,GAAG,IAAI,CAACsH,mBAAmB,CAACzM,EAAE,CAAC;IAC3C,MAAM2M,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC5M,EAAE,CAAC;IACjC,IAAImF,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACe,OAAO,EAAE;MAChEhI,QAAQ,CAACiH,MAAM,CAACe,OAAO,EAAEyG,MAAM,CAAC;IACpC;EACJ;EACA;AACJ;AACA;AACA;EACIE,oBAAoBA,CAAA,EAAG;IACnB,MAAMnI,OAAO,GAAG,IAAI,CAACtE,EAAE,CAACD,OAAO,CAAC,aAAa,CAAC;IAC9C,IAAIuE,OAAO,EAAE;MACTA,OAAO,CAAC8D,OAAO,CAAC,CAAC;IACrB;EACJ;EACAsE,UAAUA,CAAC9M,EAAE,EAAE;IACX,MAAM;MAAEwB;IAAS,CAAC,GAAG,IAAI;IACzB,MAAM2D,MAAM,GAAG,IAAI,CAACsH,mBAAmB,CAACzM,EAAE,CAAC;IAC3C;IACA;IACA,IAAIwB,QAAQ,IAAI2D,MAAM,EAAE;MACpBA,MAAM,CAACmB,OAAO,GAAGtG,EAAE,CAACmH,MAAM,CAACb,OAAO;IACtC;EACJ;EACAsG,SAASA,CAAC5M,EAAE,EAAE;IACV,MAAM;MAAEwB,QAAQ;MAAEmD;IAAQ,CAAC,GAAG,IAAI;IAClC,IAAInD,QAAQ,EAAE;MACV;MACA;MACA,OAAOmD,OAAO,CAACc,MAAM,CAAE9B,CAAC,IAAKA,CAAC,CAAC2C,OAAO,CAAC,CAAC5C,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC3B,KAAK,CAAC;IAC/D;IACA;IACA;IACA,MAAMmD,MAAM,GAAG,IAAI,CAACsH,mBAAmB,CAACzM,EAAE,CAAC;IAC3C,OAAOmF,MAAM,GAAGA,MAAM,CAACnD,KAAK,GAAGlB,SAAS;EAC5C;EACAiM,aAAaA,CAACpI,OAAO,EAAE;IACnB,MAAM;MAAEnD;IAAS,CAAC,GAAG,IAAI;IACzB,QAAQA,QAAQ;MACZ,KAAK,IAAI;QACL,OAAO,IAAI,CAACwL,qBAAqB,CAACrI,OAAO,CAAC;MAC9C;QACI,OAAO,IAAI,CAACsI,kBAAkB,CAACtI,OAAO,CAAC;IAC/C;EACJ;EACAqI,qBAAqBA,CAACrI,OAAO,EAAE;IAC3B,OAAOA,OAAO,CAACjB,GAAG,CAAEyB,MAAM,IAAM5I,CAAC,CAAC,UAAU,EAAE;MAAEyM,KAAK,EAAE/B,MAAM,CAACC,MAAM,CAAC;QAC7D;QACA,uBAAuB,EAAE/B,MAAM,CAACmB;MACpC,CAAC,EAAE/H,WAAW,CAAC4G,MAAM,CAACc,QAAQ,CAAC;IAAE,CAAC,EAAE1J,CAAC,CAAC,cAAc,EAAE;MAAEyF,KAAK,EAAEmD,MAAM,CAACnD,KAAK;MAAEhB,QAAQ,EAAEmE,MAAM,CAACnE,QAAQ;MAAEsF,OAAO,EAAEnB,MAAM,CAACmB,OAAO;MAAElF,OAAO,EAAE,OAAO;MAAEE,cAAc,EAAE,KAAK;MAAE4L,WAAW,EAAGlN,EAAE,IAAK;QAC3L,IAAI,CAAC8M,UAAU,CAAC9M,EAAE,CAAC;QACnB,IAAI,CAAC0M,iBAAiB,CAAC1M,EAAE,CAAC;QAC1B;QACAnD,WAAW,CAAC,IAAI,CAAC;MACrB;IAAE,CAAC,EAAEsI,MAAM,CAACY,IAAI,CAAC,CAAE,CAAC;EAC5B;EACAkH,kBAAkBA,CAACtI,OAAO,EAAE;IACxB,MAAM2B,OAAO,GAAG3B,OAAO,CAACc,MAAM,CAAE9B,CAAC,IAAKA,CAAC,CAAC2C,OAAO,CAAC,CAAC5C,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC3B,KAAK,CAAC,CAAC,CAAC,CAAC;IACvE,OAAQzF,CAAC,CAAC,iBAAiB,EAAE;MAAEyF,KAAK,EAAEsE,OAAO;MAAE4G,WAAW,EAAGlN,EAAE,IAAK,IAAI,CAAC0M,iBAAiB,CAAC1M,EAAE;IAAE,CAAC,EAAE2E,OAAO,CAACjB,GAAG,CAAEyB,MAAM,IAAM5I,CAAC,CAAC,UAAU,EAAE;MAAEyM,KAAK,EAAE/B,MAAM,CAACC,MAAM,CAAC;QACxJ;QACA,oBAAoB,EAAE/B,MAAM,CAACnD,KAAK,KAAKsE;MAC3C,CAAC,EAAE/H,WAAW,CAAC4G,MAAM,CAACc,QAAQ,CAAC;IAAE,CAAC,EAAE1J,CAAC,CAAC,WAAW,EAAE;MAAEyF,KAAK,EAAEmD,MAAM,CAACnD,KAAK;MAAEhB,QAAQ,EAAEmE,MAAM,CAACnE,QAAQ;MAAEjB,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC8M,oBAAoB,CAAC,CAAC;MAAEM,OAAO,EAAGnN,EAAE,IAAK;QAC9J,IAAIA,EAAE,CAACmM,GAAG,KAAK,GAAG,EAAE;UAChB;AACpB;AACA;AACA;AACA;UACoB,IAAI,CAACU,oBAAoB,CAAC,CAAC;QAC/B;MACJ;IAAE,CAAC,EAAE1H,MAAM,CAACY,IAAI,CAAC,CAAE,CAAC,CAAC;EAC7B;EACAoF,MAAMA,CAAA,EAAG;IACL,MAAM;MAAExD,MAAM;MAAEE,OAAO;MAAElD,OAAO;MAAEiD;IAAU,CAAC,GAAG,IAAI;IACpD,MAAMwF,qBAAqB,GAAGxF,SAAS,KAAK9G,SAAS,IAAI+G,OAAO,KAAK/G,SAAS;IAC9E,OAAQvE,CAAC,CAACE,IAAI,EAAE;MAAE0P,GAAG,EAAE,0CAA0C;MAAEnD,KAAK,EAAEnK,UAAU,CAAC,IAAI;IAAE,CAAC,EAAEtC,CAAC,CAAC,UAAU,EAAE;MAAE4P,GAAG,EAAE;IAA2C,CAAC,EAAExE,MAAM,KAAK7G,SAAS,IAAIvE,CAAC,CAAC,iBAAiB,EAAE;MAAE4P,GAAG,EAAE;IAA2C,CAAC,EAAExE,MAAM,CAAC,EAAEyF,qBAAqB,IAAK7Q,CAAC,CAAC,UAAU,EAAE;MAAE4P,GAAG,EAAE;IAA2C,CAAC,EAAE5P,CAAC,CAAC,WAAW,EAAE;MAAE4P,GAAG,EAAE,0CAA0C;MAAEnD,KAAK,EAAE;IAAgB,CAAC,EAAEpB,SAAS,KAAK9G,SAAS,IAAIvE,CAAC,CAAC,IAAI,EAAE;MAAE4P,GAAG,EAAE;IAA2C,CAAC,EAAEvE,SAAS,CAAC,EAAEC,OAAO,KAAK/G,SAAS,IAAIvE,CAAC,CAAC,GAAG,EAAE;MAAE4P,GAAG,EAAE;IAA2C,CAAC,EAAEtE,OAAO,CAAC,CAAC,CAAE,EAAE,IAAI,CAACkF,aAAa,CAACpI,OAAO,CAAC,CAAC,CAAC;EAC5qB;EACA,IAAIvE,EAAEA,CAAA,EAAG;IAAE,OAAOzD,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD6P,aAAa,CAAC1D,KAAK,GAAG;EAClB+C,GAAG,EAAEQ,yBAAyB;EAC9BP,EAAE,EAAES;AACR,CAAC;AAED,SAASrN,MAAM,IAAImO,UAAU,EAAEpB,YAAY,IAAIqB,iBAAiB,EAAEd,aAAa,IAAIe,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}