{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst watchForOptions = (containerEl, tagName, onChange) => {\n  if (typeof MutationObserver === 'undefined') {\n    return;\n  }\n  const mutation = new MutationObserver(mutationList => {\n    onChange(getSelectedOption(mutationList, tagName));\n  });\n  mutation.observe(containerEl, {\n    childList: true,\n    subtree: true\n  });\n  return mutation;\n};\nconst getSelectedOption = (mutationList, tagName) => {\n  let newOption;\n  mutationList.forEach(mut => {\n    // eslint-disable-next-line @typescript-eslint/prefer-for-of\n    for (let i = 0; i < mut.addedNodes.length; i++) {\n      newOption = findCheckedOption(mut.addedNodes[i], tagName) || newOption;\n    }\n  });\n  return newOption;\n};\n/**\n * The \"value\" key is only set on some components such as ion-select-option.\n * As a result, we create a default union type of HTMLElement and the \"value\" key.\n * However, implementers are required to provide the appropriate component type\n * such as HTMLIonSelectOptionElement.\n */\nconst findCheckedOption = (node, tagName) => {\n  /**\n   * https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\n   * The above check ensures \"node\" is an Element (nodeType 1).\n   */\n  if (node.nodeType !== 1) {\n    return undefined;\n  }\n  // HTMLElement inherits from Element, so we cast \"el\" as T.\n  const el = node;\n  const options = el.tagName === tagName.toUpperCase() ? [el] : Array.from(el.querySelectorAll(tagName));\n  return options.find(o => o.value === el.value);\n};\nexport { watchForOptions as w };", "map": {"version": 3, "names": ["watchForOptions", "containerEl", "tagName", "onChange", "MutationObserver", "mutation", "mutationList", "getSelectedOption", "observe", "childList", "subtree", "newOption", "for<PERSON>ach", "mut", "i", "addedNodes", "length", "findCheckedOption", "node", "nodeType", "undefined", "el", "options", "toUpperCase", "Array", "from", "querySelectorAll", "find", "o", "value", "w"], "sources": ["E:/Fahion/DFashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/watch-options-c2911ace.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst watchForOptions = (containerEl, tagName, onChange) => {\n    if (typeof MutationObserver === 'undefined') {\n        return;\n    }\n    const mutation = new MutationObserver((mutationList) => {\n        onChange(getSelectedOption(mutationList, tagName));\n    });\n    mutation.observe(containerEl, {\n        childList: true,\n        subtree: true,\n    });\n    return mutation;\n};\nconst getSelectedOption = (mutationList, tagName) => {\n    let newOption;\n    mutationList.forEach((mut) => {\n        // eslint-disable-next-line @typescript-eslint/prefer-for-of\n        for (let i = 0; i < mut.addedNodes.length; i++) {\n            newOption = findCheckedOption(mut.addedNodes[i], tagName) || newOption;\n        }\n    });\n    return newOption;\n};\n/**\n * The \"value\" key is only set on some components such as ion-select-option.\n * As a result, we create a default union type of HTMLElement and the \"value\" key.\n * However, implementers are required to provide the appropriate component type\n * such as HTMLIonSelectOptionElement.\n */\nconst findCheckedOption = (node, tagName) => {\n    /**\n     * https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\n     * The above check ensures \"node\" is an Element (nodeType 1).\n     */\n    if (node.nodeType !== 1) {\n        return undefined;\n    }\n    // HTMLElement inherits from Element, so we cast \"el\" as T.\n    const el = node;\n    const options = el.tagName === tagName.toUpperCase() ? [el] : Array.from(el.querySelectorAll(tagName));\n    return options.find((o) => o.value === el.value);\n};\n\nexport { watchForOptions as w };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,eAAe,GAAGA,CAACC,WAAW,EAAEC,OAAO,EAAEC,QAAQ,KAAK;EACxD,IAAI,OAAOC,gBAAgB,KAAK,WAAW,EAAE;IACzC;EACJ;EACA,MAAMC,QAAQ,GAAG,IAAID,gBAAgB,CAAEE,YAAY,IAAK;IACpDH,QAAQ,CAACI,iBAAiB,CAACD,YAAY,EAAEJ,OAAO,CAAC,CAAC;EACtD,CAAC,CAAC;EACFG,QAAQ,CAACG,OAAO,CAACP,WAAW,EAAE;IAC1BQ,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE;EACb,CAAC,CAAC;EACF,OAAOL,QAAQ;AACnB,CAAC;AACD,MAAME,iBAAiB,GAAGA,CAACD,YAAY,EAAEJ,OAAO,KAAK;EACjD,IAAIS,SAAS;EACbL,YAAY,CAACM,OAAO,CAAEC,GAAG,IAAK;IAC1B;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAACE,UAAU,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC5CH,SAAS,GAAGM,iBAAiB,CAACJ,GAAG,CAACE,UAAU,CAACD,CAAC,CAAC,EAAEZ,OAAO,CAAC,IAAIS,SAAS;IAC1E;EACJ,CAAC,CAAC;EACF,OAAOA,SAAS;AACpB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,iBAAiB,GAAGA,CAACC,IAAI,EAAEhB,OAAO,KAAK;EACzC;AACJ;AACA;AACA;EACI,IAAIgB,IAAI,CAACC,QAAQ,KAAK,CAAC,EAAE;IACrB,OAAOC,SAAS;EACpB;EACA;EACA,MAAMC,EAAE,GAAGH,IAAI;EACf,MAAMI,OAAO,GAAGD,EAAE,CAACnB,OAAO,KAAKA,OAAO,CAACqB,WAAW,CAAC,CAAC,GAAG,CAACF,EAAE,CAAC,GAAGG,KAAK,CAACC,IAAI,CAACJ,EAAE,CAACK,gBAAgB,CAACxB,OAAO,CAAC,CAAC;EACtG,OAAOoB,OAAO,CAACK,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,KAAK,KAAKR,EAAE,CAACQ,KAAK,CAAC;AACpD,CAAC;AAED,SAAS7B,eAAe,IAAI8B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}