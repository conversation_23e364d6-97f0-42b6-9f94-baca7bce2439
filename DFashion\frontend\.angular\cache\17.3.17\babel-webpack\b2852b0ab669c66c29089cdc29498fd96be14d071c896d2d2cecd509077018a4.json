{"ast": null, "code": "export const profileRoutes = [{\n  path: '',\n  loadComponent: () => import('./pages/profile/profile.component').then(m => m.ProfileComponent)\n}];", "map": {"version": 3, "names": ["profileRoutes", "path", "loadComponent", "then", "m", "ProfileComponent"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\profile\\profile.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const profileRoutes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./pages/profile/profile.component').then(m => m.ProfileComponent)\n  }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,aAAa,GAAW,CACnC;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB;CAC9F,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}