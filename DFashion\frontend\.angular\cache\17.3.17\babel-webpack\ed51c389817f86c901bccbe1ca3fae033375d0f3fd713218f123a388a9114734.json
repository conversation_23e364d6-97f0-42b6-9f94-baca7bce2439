{"ast": null, "code": "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n  let obj = uri;\n  // default to window.location\n  loc = loc || typeof location !== \"undefined\" && location;\n  if (null == uri) uri = loc.protocol + \"//\" + loc.host;\n  // relative path support\n  if (typeof uri === \"string\") {\n    if (\"/\" === uri.charAt(0)) {\n      if (\"/\" === uri.charAt(1)) {\n        uri = loc.protocol + uri;\n      } else {\n        uri = loc.host + uri;\n      }\n    }\n    if (!/^(https?|wss?):\\/\\//.test(uri)) {\n      if (\"undefined\" !== typeof loc) {\n        uri = loc.protocol + \"//\" + uri;\n      } else {\n        uri = \"https://\" + uri;\n      }\n    }\n    // parse\n    obj = parse(uri);\n  }\n  // make sure we treat `localhost:80` and `localhost` equally\n  if (!obj.port) {\n    if (/^(http|ws)$/.test(obj.protocol)) {\n      obj.port = \"80\";\n    } else if (/^(http|ws)s$/.test(obj.protocol)) {\n      obj.port = \"443\";\n    }\n  }\n  obj.path = obj.path || \"/\";\n  const ipv6 = obj.host.indexOf(\":\") !== -1;\n  const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n  // define unique id\n  obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n  // define href\n  obj.href = obj.protocol + \"://\" + host + (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n  return obj;\n}", "map": {"version": 3, "names": ["parse", "url", "uri", "path", "loc", "obj", "location", "protocol", "host", "char<PERSON>t", "test", "port", "ipv6", "indexOf", "id", "href"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/socket.io-client/build/esm/url.js"], "sourcesContent": ["import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,kBAAkB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,GAAGA,CAACC,GAAG,EAAEC,IAAI,GAAG,EAAE,EAAEC,GAAG,EAAE;EACrC,IAAIC,GAAG,GAAGH,GAAG;EACb;EACAE,GAAG,GAAGA,GAAG,IAAK,OAAOE,QAAQ,KAAK,WAAW,IAAIA,QAAS;EAC1D,IAAI,IAAI,IAAIJ,GAAG,EACXA,GAAG,GAAGE,GAAG,CAACG,QAAQ,GAAG,IAAI,GAAGH,GAAG,CAACI,IAAI;EACxC;EACA,IAAI,OAAON,GAAG,KAAK,QAAQ,EAAE;IACzB,IAAI,GAAG,KAAKA,GAAG,CAACO,MAAM,CAAC,CAAC,CAAC,EAAE;MACvB,IAAI,GAAG,KAAKP,GAAG,CAACO,MAAM,CAAC,CAAC,CAAC,EAAE;QACvBP,GAAG,GAAGE,GAAG,CAACG,QAAQ,GAAGL,GAAG;MAC5B,CAAC,MACI;QACDA,GAAG,GAAGE,GAAG,CAACI,IAAI,GAAGN,GAAG;MACxB;IACJ;IACA,IAAI,CAAC,qBAAqB,CAACQ,IAAI,CAACR,GAAG,CAAC,EAAE;MAClC,IAAI,WAAW,KAAK,OAAOE,GAAG,EAAE;QAC5BF,GAAG,GAAGE,GAAG,CAACG,QAAQ,GAAG,IAAI,GAAGL,GAAG;MACnC,CAAC,MACI;QACDA,GAAG,GAAG,UAAU,GAAGA,GAAG;MAC1B;IACJ;IACA;IACAG,GAAG,GAAGL,KAAK,CAACE,GAAG,CAAC;EACpB;EACA;EACA,IAAI,CAACG,GAAG,CAACM,IAAI,EAAE;IACX,IAAI,aAAa,CAACD,IAAI,CAACL,GAAG,CAACE,QAAQ,CAAC,EAAE;MAClCF,GAAG,CAACM,IAAI,GAAG,IAAI;IACnB,CAAC,MACI,IAAI,cAAc,CAACD,IAAI,CAACL,GAAG,CAACE,QAAQ,CAAC,EAAE;MACxCF,GAAG,CAACM,IAAI,GAAG,KAAK;IACpB;EACJ;EACAN,GAAG,CAACF,IAAI,GAAGE,GAAG,CAACF,IAAI,IAAI,GAAG;EAC1B,MAAMS,IAAI,GAAGP,GAAG,CAACG,IAAI,CAACK,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;EACzC,MAAML,IAAI,GAAGI,IAAI,GAAG,GAAG,GAAGP,GAAG,CAACG,IAAI,GAAG,GAAG,GAAGH,GAAG,CAACG,IAAI;EACnD;EACAH,GAAG,CAACS,EAAE,GAAGT,GAAG,CAACE,QAAQ,GAAG,KAAK,GAAGC,IAAI,GAAG,GAAG,GAAGH,GAAG,CAACM,IAAI,GAAGR,IAAI;EAC5D;EACAE,GAAG,CAACU,IAAI,GACJV,GAAG,CAACE,QAAQ,GACR,KAAK,GACLC,IAAI,IACHJ,GAAG,IAAIA,GAAG,CAACO,IAAI,KAAKN,GAAG,CAACM,IAAI,GAAG,EAAE,GAAG,GAAG,GAAGN,GAAG,CAACM,IAAI,CAAC;EAC5D,OAAON,GAAG;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}