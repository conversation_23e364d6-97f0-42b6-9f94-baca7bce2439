{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/admin-auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/toolbar\";\nimport * as i6 from \"@angular/material/divider\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/input\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/menu\";\nimport * as i12 from \"@angular/material/badge\";\nimport * as i13 from \"@angular/material/tooltip\";\nfunction HeaderComponent_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_button_27_Template_button_click_0_listener() {\n      const notification_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onNotificationClick(notification_r3));\n    });\n    i0.ɵɵelementStart(1, \"div\", 30)(2, \"div\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 32);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 33);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notification_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"unread\", !notification_r3.read);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(notification_r3.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r3.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.formatNotificationTime(notification_r3.time));\n  }\n}\nfunction HeaderComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 36);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 37);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.currentUser.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.currentUser.email);\n  }\n}\nexport class HeaderComponent {\n  constructor(adminAuthService) {\n    this.adminAuthService = adminAuthService;\n    this.sidenavToggle = new EventEmitter();\n    this.currentUser = null;\n    this.pageTitle = 'Dashboard';\n    this.searchQuery = '';\n    this.notificationCount = 3;\n    this.notifications = [];\n  }\n  ngOnInit() {\n    // Subscribe to current user\n    this.adminAuthService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    // Update notification count\n    this.updateNotificationCount();\n  }\n  toggleSidenav() {\n    this.sidenavToggle.emit();\n  }\n  onSearch() {\n    if (this.searchQuery.trim()) {\n      console.log('Searching for:', this.searchQuery);\n      // Implement search functionality\n    }\n  }\n  onNotificationClick(notification) {\n    // Mark as read\n    notification.read = true;\n    this.updateNotificationCount();\n    // Handle notification click (navigate to relevant page)\n    console.log('Notification clicked:', notification);\n  }\n  markAllAsRead() {\n    this.notifications.forEach(notification => {\n      notification.read = true;\n    });\n    this.updateNotificationCount();\n  }\n  updateNotificationCount() {\n    this.notificationCount = this.notifications.filter(n => !n.read).length;\n  }\n  logout() {\n    this.adminAuthService.logout();\n  }\n  formatNotificationTime(dateString) {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n      return 'just now';\n    } else if (diffInSeconds < 3600) {\n      const minutes = Math.floor(diffInSeconds / 60);\n      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n    } else if (diffInSeconds < 86400) {\n      const hours = Math.floor(diffInSeconds / 3600);\n      return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n    } else {\n      const days = Math.floor(diffInSeconds / 86400);\n      return `${days} day${days > 1 ? 's' : ''} ago`;\n    }\n  }\n  static {\n    this.ɵfac = function HeaderComponent_Factory(t) {\n      return new (t || HeaderComponent)(i0.ɵɵdirectiveInject(i1.AdminAuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HeaderComponent,\n      selectors: [[\"app-header\"]],\n      outputs: {\n        sidenavToggle: \"sidenavToggle\"\n      },\n      decls: 81,\n      vars: 10,\n      consts: [[\"notificationMenu\", \"matMenu\"], [\"quickActionsMenu\", \"matMenu\"], [\"userMenu\", \"matMenu\"], [1, \"admin-header\"], [\"mat-icon-button\", \"\", 1, \"menu-toggle\", 3, \"click\"], [1, \"page-title\"], [1, \"header-actions\"], [1, \"search-container\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"matSuffix\", \"\"], [\"mat-icon-button\", \"\", 1, \"notification-button\", 3, \"matMenuTriggerFor\"], [\"matBadgeColor\", \"warn\", 3, \"matBadge\"], [1, \"notification-menu\"], [1, \"notification-header\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"notification-list\"], [\"mat-menu-item\", \"\", \"class\", \"notification-item\", 3, \"unread\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/admin/notifications\", 1, \"view-all-button\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Quick Actions\", 3, \"matMenuTriggerFor\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/admin/products/new\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/admin/users/new\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/admin/orders\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/admin/analytics\"], [\"mat-icon-button\", \"\", 1, \"user-profile-button\", 3, \"matMenuTriggerFor\"], [\"class\", \"user-menu-header\", 4, \"ngIf\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/admin/profile\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/admin/settings\"], [\"mat-menu-item\", \"\", 3, \"click\"], [\"mat-menu-item\", \"\", 1, \"notification-item\", 3, \"click\"], [1, \"notification-content\"], [1, \"notification-title\"], [1, \"notification-message\"], [1, \"notification-time\"], [1, \"user-menu-header\"], [1, \"user-info\"], [1, \"user-name\"], [1, \"user-email\"]],\n      template: function HeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"mat-toolbar\", 3)(1, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_button_click_1_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleSidenav());\n          });\n          i0.ɵɵelementStart(2, \"mat-icon\");\n          i0.ɵɵtext(3, \"menu\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 5)(5, \"h1\");\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"mat-form-field\", 8)(10, \"mat-label\");\n          i0.ɵɵtext(11, \"Search...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"input\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HeaderComponent_Template_input_ngModelChange_12_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function HeaderComponent_Template_input_keyup_enter_12_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"mat-icon\", 10);\n          i0.ɵɵtext(14, \"search\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"button\", 11)(16, \"mat-icon\", 12);\n          i0.ɵɵtext(17, \"notifications\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"mat-menu\", 13, 0)(20, \"div\", 14)(21, \"h3\");\n          i0.ɵɵtext(22, \"Notifications\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_button_click_23_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.markAllAsRead());\n          });\n          i0.ɵɵtext(24, \"Mark all as read\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(25, \"mat-divider\");\n          i0.ɵɵelementStart(26, \"div\", 16);\n          i0.ɵɵtemplate(27, HeaderComponent_button_27_Template, 8, 5, \"button\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(28, \"mat-divider\");\n          i0.ɵɵelementStart(29, \"button\", 18)(30, \"mat-icon\");\n          i0.ɵɵtext(31, \"list\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(32, \" View All Notifications \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"button\", 19)(34, \"mat-icon\");\n          i0.ɵɵtext(35, \"add\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"mat-menu\", null, 1)(38, \"button\", 20)(39, \"mat-icon\");\n          i0.ɵɵtext(40, \"add_box\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"span\");\n          i0.ɵɵtext(42, \"Add Product\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"button\", 21)(44, \"mat-icon\");\n          i0.ɵɵtext(45, \"person_add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"span\");\n          i0.ɵɵtext(47, \"Add User\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"button\", 22)(49, \"mat-icon\");\n          i0.ɵɵtext(50, \"shopping_cart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"span\");\n          i0.ɵɵtext(52, \"View Orders\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"button\", 23)(54, \"mat-icon\");\n          i0.ɵɵtext(55, \"analytics\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"span\");\n          i0.ɵɵtext(57, \"View Analytics\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"button\", 24)(59, \"mat-icon\");\n          i0.ɵɵtext(60, \"account_circle\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"mat-menu\", null, 2);\n          i0.ɵɵtemplate(63, HeaderComponent_div_63_Template, 6, 2, \"div\", 25);\n          i0.ɵɵelement(64, \"mat-divider\");\n          i0.ɵɵelementStart(65, \"button\", 26)(66, \"mat-icon\");\n          i0.ɵɵtext(67, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"span\");\n          i0.ɵɵtext(69, \"My Profile\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(70, \"button\", 27)(71, \"mat-icon\");\n          i0.ɵɵtext(72, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"span\");\n          i0.ɵɵtext(74, \"Settings\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(75, \"mat-divider\");\n          i0.ɵɵelementStart(76, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function HeaderComponent_Template_button_click_76_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.logout());\n          });\n          i0.ɵɵelementStart(77, \"mat-icon\");\n          i0.ɵɵtext(78, \"logout\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"span\");\n          i0.ɵɵtext(80, \"Logout\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const notificationMenu_r5 = i0.ɵɵreference(19);\n          const quickActionsMenu_r6 = i0.ɵɵreference(37);\n          const userMenu_r7 = i0.ɵɵreference(62);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"mobile-only\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.pageTitle);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", notificationMenu_r5);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matBadge\", ctx.notificationCount);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.notifications);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", quickActionsMenu_r6);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r7);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.RouterLink, i5.MatToolbar, i6.MatDivider, i7.MatIcon, i8.MatButton, i8.MatIconButton, i9.MatInput, i10.MatFormField, i10.MatLabel, i10.MatSuffix, i11.MatMenu, i11.MatMenuItem, i11.MatMenuTrigger, i12.MatBadge, i13.MatTooltip],\n      styles: [\".admin-header[_ngcontent-%COMP%] {\\n  background: white;\\n  color: #333;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  padding: 0 1rem;\\n  height: 64px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  position: sticky;\\n  top: 0;\\n  z-index: 100;\\n}\\n\\n.menu-toggle[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n}\\n.menu-toggle.mobile-only[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.page-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.5rem;\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 300px;\\n}\\n.search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-form-field-wrapper {\\n  padding-bottom: 0;\\n}\\n.search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-form-field-infix {\\n  padding: 0.5rem 0;\\n}\\n.search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-form-field-outline {\\n  top: 0;\\n}\\n.search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-form-field-outline-start, .search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-form-field-outline-end {\\n  border-radius: 20px;\\n}\\n.search-container[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]     .mat-form-field-outline-gap {\\n  border-radius: 0;\\n}\\n\\n.notification-button[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.notification-button[_ngcontent-%COMP%]     .mat-badge-content {\\n  font-size: 0.625rem;\\n  font-weight: 600;\\n}\\n\\n.notification-menu[_ngcontent-%COMP%] {\\n  width: 350px;\\n  max-height: 400px;\\n}\\n.notification-menu[_ngcontent-%COMP%]   .notification-header[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.notification-menu[_ngcontent-%COMP%]   .notification-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.125rem;\\n  font-weight: 500;\\n}\\n.notification-menu[_ngcontent-%COMP%]   .notification-list[_ngcontent-%COMP%] {\\n  max-height: 250px;\\n  overflow-y: auto;\\n}\\n.notification-menu[_ngcontent-%COMP%]   .notification-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 1rem;\\n  text-align: left;\\n  border: none;\\n  background: none;\\n  cursor: pointer;\\n  transition: background 0.2s ease;\\n}\\n.notification-menu[_ngcontent-%COMP%]   .notification-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]:hover {\\n  background: #f5f5f5;\\n}\\n.notification-menu[_ngcontent-%COMP%]   .notification-list[_ngcontent-%COMP%]   .notification-item.unread[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n}\\n.notification-menu[_ngcontent-%COMP%]   .notification-list[_ngcontent-%COMP%]   .notification-item.unread[_ngcontent-%COMP%]:hover {\\n  background: #bbdefb;\\n}\\n.notification-menu[_ngcontent-%COMP%]   .notification-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%]   .notification-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 0.25rem;\\n  color: #333;\\n}\\n.notification-menu[_ngcontent-%COMP%]   .notification-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%]   .notification-message[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #666;\\n  margin-bottom: 0.25rem;\\n  line-height: 1.3;\\n}\\n.notification-menu[_ngcontent-%COMP%]   .notification-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%]   .notification-time[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #999;\\n}\\n.notification-menu[_ngcontent-%COMP%]   .view-all-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  justify-content: center;\\n  font-weight: 500;\\n}\\n\\n.user-profile-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.75rem;\\n  width: 1.75rem;\\n  height: 1.75rem;\\n}\\n\\n.user-menu-header[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n.user-menu-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 0.25rem;\\n}\\n.user-menu-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-email[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #666;\\n}\\n\\n@media (max-width: 768px) {\\n  .admin-header[_ngcontent-%COMP%] {\\n    padding: 0 0.5rem;\\n  }\\n  .menu-toggle.mobile-only[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n  .page-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n  .search-container[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .header-actions[_ngcontent-%COMP%] {\\n    gap: 0.25rem;\\n  }\\n  .notification-menu[_ngcontent-%COMP%] {\\n    width: 300px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .page-title[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .notification-menu[_ngcontent-%COMP%] {\\n    width: 280px;\\n  }\\n}\\n.notification-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.notification-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n}\\n.notification-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 2px;\\n}\\n.notification-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵlistener", "HeaderComponent_button_27_Template_button_click_0_listener", "notification_r3", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "onNotificationClick", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "read", "ɵɵadvance", "ɵɵtextInterpolate", "title", "message", "formatNotificationTime", "time", "currentUser", "fullName", "email", "HeaderComponent", "constructor", "adminAuthService", "sidenavToggle", "pageTitle", "searchQuery", "notificationCount", "notifications", "ngOnInit", "currentUser$", "subscribe", "user", "updateNotificationCount", "toggle<PERSON><PERSON><PERSON>", "emit", "onSearch", "trim", "console", "log", "notification", "markAllAsRead", "for<PERSON>ach", "filter", "n", "length", "logout", "dateString", "date", "Date", "now", "diffInSeconds", "Math", "floor", "getTime", "minutes", "hours", "days", "ɵɵdirectiveInject", "i1", "AdminAuthService", "selectors", "outputs", "decls", "vars", "consts", "template", "HeaderComponent_Template", "rf", "ctx", "HeaderComponent_Template_button_click_1_listener", "_r1", "ɵɵtwoWayListener", "HeaderComponent_Template_input_ngModelChange_12_listener", "$event", "ɵɵtwoWayBindingSet", "HeaderComponent_Template_input_keyup_enter_12_listener", "HeaderComponent_Template_button_click_23_listener", "ɵɵelement", "ɵɵtemplate", "HeaderComponent_button_27_Template", "HeaderComponent_div_63_Template", "HeaderComponent_Template_button_click_76_listener", "ɵɵtwoWayProperty", "ɵɵproperty", "notificationMenu_r5", "quickActionsMenu_r6", "userMenu_r7"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\admin\\layout\\header.component.ts"], "sourcesContent": ["import { Component, OnInit, Output, EventEmitter } from '@angular/core';\nimport { AdminAuthService } from '../services/admin-auth.service';\n\n@Component({\n  selector: 'app-header',\n  template: `\n    <mat-toolbar class=\"admin-header\">\n      <!-- Mobile Menu Toggle -->\n      <button \n        mat-icon-button \n        class=\"menu-toggle\"\n        (click)=\"toggleSidenav()\"\n        [class.mobile-only]=\"true\">\n        <mat-icon>menu</mat-icon>\n      </button>\n\n      <!-- Page Title -->\n      <div class=\"page-title\">\n        <h1>{{ pageTitle }}</h1>\n      </div>\n\n      <!-- Header Actions -->\n      <div class=\"header-actions\">\n        <!-- Search -->\n        <div class=\"search-container\">\n          <mat-form-field appearance=\"outline\" class=\"search-field\">\n            <mat-label>Search...</mat-label>\n            <input matInput [(ngModel)]=\"searchQuery\" (keyup.enter)=\"onSearch()\">\n            <mat-icon matSuffix>search</mat-icon>\n          </mat-form-field>\n        </div>\n\n        <!-- Notifications -->\n        <button \n          mat-icon-button \n          [matMenuTriggerFor]=\"notificationMenu\"\n          class=\"notification-button\">\n          <mat-icon [matBadge]=\"notificationCount\" matBadgeColor=\"warn\">notifications</mat-icon>\n        </button>\n\n        <mat-menu #notificationMenu=\"matMenu\" class=\"notification-menu\">\n          <div class=\"notification-header\">\n            <h3>Notifications</h3>\n            <button mat-button color=\"primary\" (click)=\"markAllAsRead()\">Mark all as read</button>\n          </div>\n          <mat-divider></mat-divider>\n          \n          <div class=\"notification-list\">\n            <button \n              *ngFor=\"let notification of notifications\" \n              mat-menu-item \n              class=\"notification-item\"\n              [class.unread]=\"!notification.read\"\n              (click)=\"onNotificationClick(notification)\">\n              <div class=\"notification-content\">\n                <div class=\"notification-title\">{{ notification.title }}</div>\n                <div class=\"notification-message\">{{ notification.message }}</div>\n                <div class=\"notification-time\">{{ formatNotificationTime(notification.time) }}</div>\n              </div>\n            </button>\n          </div>\n\n          <mat-divider></mat-divider>\n          <button mat-menu-item routerLink=\"/admin/notifications\" class=\"view-all-button\">\n            <mat-icon>list</mat-icon>\n            View All Notifications\n          </button>\n        </mat-menu>\n\n        <!-- Quick Actions -->\n        <button \n          mat-icon-button \n          [matMenuTriggerFor]=\"quickActionsMenu\"\n          matTooltip=\"Quick Actions\">\n          <mat-icon>add</mat-icon>\n        </button>\n\n        <mat-menu #quickActionsMenu=\"matMenu\">\n          <button mat-menu-item routerLink=\"/admin/products/new\">\n            <mat-icon>add_box</mat-icon>\n            <span>Add Product</span>\n          </button>\n          <button mat-menu-item routerLink=\"/admin/users/new\">\n            <mat-icon>person_add</mat-icon>\n            <span>Add User</span>\n          </button>\n          <button mat-menu-item routerLink=\"/admin/orders\">\n            <mat-icon>shopping_cart</mat-icon>\n            <span>View Orders</span>\n          </button>\n          <button mat-menu-item routerLink=\"/admin/analytics\">\n            <mat-icon>analytics</mat-icon>\n            <span>View Analytics</span>\n          </button>\n        </mat-menu>\n\n        <!-- User Profile -->\n        <button \n          mat-icon-button \n          [matMenuTriggerFor]=\"userMenu\"\n          class=\"user-profile-button\">\n          <mat-icon>account_circle</mat-icon>\n        </button>\n\n        <mat-menu #userMenu=\"matMenu\">\n          <div class=\"user-menu-header\" *ngIf=\"currentUser\">\n            <div class=\"user-info\">\n              <div class=\"user-name\">{{ currentUser.fullName }}</div>\n              <div class=\"user-email\">{{ currentUser.email }}</div>\n            </div>\n          </div>\n          <mat-divider></mat-divider>\n          \n          <button mat-menu-item routerLink=\"/admin/profile\">\n            <mat-icon>person</mat-icon>\n            <span>My Profile</span>\n          </button>\n          <button mat-menu-item routerLink=\"/admin/settings\">\n            <mat-icon>settings</mat-icon>\n            <span>Settings</span>\n          </button>\n          <mat-divider></mat-divider>\n          <button mat-menu-item (click)=\"logout()\">\n            <mat-icon>logout</mat-icon>\n            <span>Logout</span>\n          </button>\n        </mat-menu>\n      </div>\n    </mat-toolbar>\n  `,\n  styleUrls: ['./header.component.scss']\n})\nexport class HeaderComponent implements OnInit {\n  @Output() sidenavToggle = new EventEmitter<void>();\n\n  currentUser: any = null;\n  pageTitle: string = 'Dashboard';\n  searchQuery: string = '';\n  notificationCount: number = 3;\n\n  notifications: any[] = [];\n\n  constructor(\n    private adminAuthService: AdminAuthService\n  ) {}\n\n  ngOnInit(): void {\n    // Subscribe to current user\n    this.adminAuthService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n\n    // Update notification count\n    this.updateNotificationCount();\n  }\n\n  toggleSidenav(): void {\n    this.sidenavToggle.emit();\n  }\n\n  onSearch(): void {\n    if (this.searchQuery.trim()) {\n      console.log('Searching for:', this.searchQuery);\n      // Implement search functionality\n    }\n  }\n\n  onNotificationClick(notification: any): void {\n    // Mark as read\n    notification.read = true;\n    this.updateNotificationCount();\n    \n    // Handle notification click (navigate to relevant page)\n    console.log('Notification clicked:', notification);\n  }\n\n  markAllAsRead(): void {\n    this.notifications.forEach(notification => {\n      notification.read = true;\n    });\n    this.updateNotificationCount();\n  }\n\n  private updateNotificationCount(): void {\n    this.notificationCount = this.notifications.filter(n => !n.read).length;\n  }\n\n  logout(): void {\n    this.adminAuthService.logout();\n  }\n\n  formatNotificationTime(dateString: string): string {\n    if (!dateString) return '';\n\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n\n    if (diffInSeconds < 60) {\n      return 'just now';\n    } else if (diffInSeconds < 3600) {\n      const minutes = Math.floor(diffInSeconds / 60);\n      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n    } else if (diffInSeconds < 86400) {\n      const hours = Math.floor(diffInSeconds / 3600);\n      return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n    } else {\n      const days = Math.floor(diffInSeconds / 86400);\n      return `${days} day${days > 1 ? 's' : ''} ago`;\n    }\n  }\n}\n"], "mappings": "AAAA,SAAoCA,YAAY,QAAQ,eAAe;;;;;;;;;;;;;;;;;;IAgD3DC,EAAA,CAAAC,cAAA,iBAK8C;IAA5CD,EAAA,CAAAE,UAAA,mBAAAC,2DAAA;MAAA,MAAAC,eAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,mBAAA,CAAAP,eAAA,CAAiC;IAAA,EAAC;IAEzCJ,EADF,CAAAC,cAAA,cAAkC,cACA;IAAAD,EAAA,CAAAY,MAAA,GAAwB;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IAC9Db,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAY,MAAA,GAA0B;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IAClEb,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAY,MAAA,GAA+C;IAElFZ,EAFkF,CAAAa,YAAA,EAAM,EAChF,EACC;;;;;IAPPb,EAAA,CAAAc,WAAA,YAAAV,eAAA,CAAAW,IAAA,CAAmC;IAGDf,EAAA,CAAAgB,SAAA,GAAwB;IAAxBhB,EAAA,CAAAiB,iBAAA,CAAAb,eAAA,CAAAc,KAAA,CAAwB;IACtBlB,EAAA,CAAAgB,SAAA,GAA0B;IAA1BhB,EAAA,CAAAiB,iBAAA,CAAAb,eAAA,CAAAe,OAAA,CAA0B;IAC7BnB,EAAA,CAAAgB,SAAA,GAA+C;IAA/ChB,EAAA,CAAAiB,iBAAA,CAAAT,MAAA,CAAAY,sBAAA,CAAAhB,eAAA,CAAAiB,IAAA,EAA+C;;;;;IAkDhFrB,EAFJ,CAAAC,cAAA,cAAkD,cACzB,cACE;IAAAD,EAAA,CAAAY,MAAA,GAA0B;IAAAZ,EAAA,CAAAa,YAAA,EAAM;IACvDb,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAY,MAAA,GAAuB;IAEnDZ,EAFmD,CAAAa,YAAA,EAAM,EACjD,EACF;;;;IAHqBb,EAAA,CAAAgB,SAAA,GAA0B;IAA1BhB,EAAA,CAAAiB,iBAAA,CAAAT,MAAA,CAAAc,WAAA,CAAAC,QAAA,CAA0B;IACzBvB,EAAA,CAAAgB,SAAA,GAAuB;IAAvBhB,EAAA,CAAAiB,iBAAA,CAAAT,MAAA,CAAAc,WAAA,CAAAE,KAAA,CAAuB;;;AAwB7D,OAAM,MAAOC,eAAe;EAU1BC,YACUC,gBAAkC;IAAlC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAVhB,KAAAC,aAAa,GAAG,IAAI7B,YAAY,EAAQ;IAElD,KAAAuB,WAAW,GAAQ,IAAI;IACvB,KAAAO,SAAS,GAAW,WAAW;IAC/B,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,iBAAiB,GAAW,CAAC;IAE7B,KAAAC,aAAa,GAAU,EAAE;EAItB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACN,gBAAgB,CAACO,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAClD,IAAI,CAACd,WAAW,GAAGc,IAAI;IACzB,CAAC,CAAC;IAEF;IACA,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACV,aAAa,CAACW,IAAI,EAAE;EAC3B;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACV,WAAW,CAACW,IAAI,EAAE,EAAE;MAC3BC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACb,WAAW,CAAC;MAC/C;;EAEJ;EAEAnB,mBAAmBA,CAACiC,YAAiB;IACnC;IACAA,YAAY,CAAC7B,IAAI,GAAG,IAAI;IACxB,IAAI,CAACsB,uBAAuB,EAAE;IAE9B;IACAK,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,YAAY,CAAC;EACpD;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACb,aAAa,CAACc,OAAO,CAACF,YAAY,IAAG;MACxCA,YAAY,CAAC7B,IAAI,GAAG,IAAI;IAC1B,CAAC,CAAC;IACF,IAAI,CAACsB,uBAAuB,EAAE;EAChC;EAEQA,uBAAuBA,CAAA;IAC7B,IAAI,CAACN,iBAAiB,GAAG,IAAI,CAACC,aAAa,CAACe,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACjC,IAAI,CAAC,CAACkC,MAAM;EACzE;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACvB,gBAAgB,CAACuB,MAAM,EAAE;EAChC;EAEA9B,sBAAsBA,CAAC+B,UAAkB;IACvC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAE1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,CAACI,OAAO,EAAE,GAAGN,IAAI,CAACM,OAAO,EAAE,IAAI,IAAI,CAAC;IAEzE,IAAIH,aAAa,GAAG,EAAE,EAAE;MACtB,OAAO,UAAU;KAClB,MAAM,IAAIA,aAAa,GAAG,IAAI,EAAE;MAC/B,MAAMI,OAAO,GAAGH,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;MAC9C,OAAO,GAAGI,OAAO,UAAUA,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;KACxD,MAAM,IAAIJ,aAAa,GAAG,KAAK,EAAE;MAChC,MAAMK,KAAK,GAAGJ,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAC;MAC9C,OAAO,GAAGK,KAAK,QAAQA,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;KAClD,MAAM;MACL,MAAMC,IAAI,GAAGL,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAC;MAC9C,OAAO,GAAGM,IAAI,OAAOA,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM;;EAElD;;;uBA9EWpC,eAAe,EAAAzB,EAAA,CAAA8D,iBAAA,CAAAC,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAfvC,eAAe;MAAAwC,SAAA;MAAAC,OAAA;QAAAtC,aAAA;MAAA;MAAAuC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UA5HtBxE,EAFF,CAAAC,cAAA,qBAAkC,gBAMH;UAD3BD,EAAA,CAAAE,UAAA,mBAAAwE,iDAAA;YAAA1E,EAAA,CAAAK,aAAA,CAAAsE,GAAA;YAAA,OAAA3E,EAAA,CAAAU,WAAA,CAAS+D,GAAA,CAAAnC,aAAA,EAAe;UAAA,EAAC;UAEzBtC,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAY,MAAA,WAAI;UAChBZ,EADgB,CAAAa,YAAA,EAAW,EAClB;UAIPb,EADF,CAAAC,cAAA,aAAwB,SAClB;UAAAD,EAAA,CAAAY,MAAA,GAAe;UACrBZ,EADqB,CAAAa,YAAA,EAAK,EACpB;UAOAb,EAJN,CAAAC,cAAA,aAA4B,aAEI,wBAC8B,iBAC7C;UAAAD,EAAA,CAAAY,MAAA,iBAAS;UAAAZ,EAAA,CAAAa,YAAA,EAAY;UAChCb,EAAA,CAAAC,cAAA,gBAAqE;UAArDD,EAAA,CAAA4E,gBAAA,2BAAAC,yDAAAC,MAAA;YAAA9E,EAAA,CAAAK,aAAA,CAAAsE,GAAA;YAAA3E,EAAA,CAAA+E,kBAAA,CAAAN,GAAA,CAAA3C,WAAA,EAAAgD,MAAA,MAAAL,GAAA,CAAA3C,WAAA,GAAAgD,MAAA;YAAA,OAAA9E,EAAA,CAAAU,WAAA,CAAAoE,MAAA;UAAA,EAAyB;UAAC9E,EAAA,CAAAE,UAAA,yBAAA8E,uDAAA;YAAAhF,EAAA,CAAAK,aAAA,CAAAsE,GAAA;YAAA,OAAA3E,EAAA,CAAAU,WAAA,CAAe+D,GAAA,CAAAjC,QAAA,EAAU;UAAA,EAAC;UAApExC,EAAA,CAAAa,YAAA,EAAqE;UACrEb,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAY,MAAA,cAAM;UAE9BZ,EAF8B,CAAAa,YAAA,EAAW,EACtB,EACb;UAOJb,EAJF,CAAAC,cAAA,kBAG8B,oBACkC;UAAAD,EAAA,CAAAY,MAAA,qBAAa;UAC7EZ,EAD6E,CAAAa,YAAA,EAAW,EAC/E;UAILb,EAFJ,CAAAC,cAAA,uBAAgE,eAC7B,UAC3B;UAAAD,EAAA,CAAAY,MAAA,qBAAa;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACtBb,EAAA,CAAAC,cAAA,kBAA6D;UAA1BD,EAAA,CAAAE,UAAA,mBAAA+E,kDAAA;YAAAjF,EAAA,CAAAK,aAAA,CAAAsE,GAAA;YAAA,OAAA3E,EAAA,CAAAU,WAAA,CAAS+D,GAAA,CAAA5B,aAAA,EAAe;UAAA,EAAC;UAAC7C,EAAA,CAAAY,MAAA,wBAAgB;UAC/EZ,EAD+E,CAAAa,YAAA,EAAS,EAClF;UACNb,EAAA,CAAAkF,SAAA,mBAA2B;UAE3BlF,EAAA,CAAAC,cAAA,eAA+B;UAC7BD,EAAA,CAAAmF,UAAA,KAAAC,kCAAA,qBAK8C;UAOhDpF,EAAA,CAAAa,YAAA,EAAM;UAENb,EAAA,CAAAkF,SAAA,mBAA2B;UAEzBlF,EADF,CAAAC,cAAA,kBAAgF,gBACpE;UAAAD,EAAA,CAAAY,MAAA,YAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAW;UACzBb,EAAA,CAAAY,MAAA,gCACF;UACFZ,EADE,CAAAa,YAAA,EAAS,EACA;UAOTb,EAJF,CAAAC,cAAA,kBAG6B,gBACjB;UAAAD,EAAA,CAAAY,MAAA,WAAG;UACfZ,EADe,CAAAa,YAAA,EAAW,EACjB;UAILb,EAFJ,CAAAC,cAAA,yBAAsC,kBACmB,gBAC3C;UAAAD,EAAA,CAAAY,MAAA,eAAO;UAAAZ,EAAA,CAAAa,YAAA,EAAW;UAC5Bb,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAY,MAAA,mBAAW;UACnBZ,EADmB,CAAAa,YAAA,EAAO,EACjB;UAEPb,EADF,CAAAC,cAAA,kBAAoD,gBACxC;UAAAD,EAAA,CAAAY,MAAA,kBAAU;UAAAZ,EAAA,CAAAa,YAAA,EAAW;UAC/Bb,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAY,MAAA,gBAAQ;UAChBZ,EADgB,CAAAa,YAAA,EAAO,EACd;UAEPb,EADF,CAAAC,cAAA,kBAAiD,gBACrC;UAAAD,EAAA,CAAAY,MAAA,qBAAa;UAAAZ,EAAA,CAAAa,YAAA,EAAW;UAClCb,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAY,MAAA,mBAAW;UACnBZ,EADmB,CAAAa,YAAA,EAAO,EACjB;UAEPb,EADF,CAAAC,cAAA,kBAAoD,gBACxC;UAAAD,EAAA,CAAAY,MAAA,iBAAS;UAAAZ,EAAA,CAAAa,YAAA,EAAW;UAC9Bb,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAY,MAAA,sBAAc;UAExBZ,EAFwB,CAAAa,YAAA,EAAO,EACpB,EACA;UAOTb,EAJF,CAAAC,cAAA,kBAG8B,gBAClB;UAAAD,EAAA,CAAAY,MAAA,sBAAc;UAC1BZ,EAD0B,CAAAa,YAAA,EAAW,EAC5B;UAETb,EAAA,CAAAC,cAAA,yBAA8B;UAC5BD,EAAA,CAAAmF,UAAA,KAAAE,+BAAA,kBAAkD;UAMlDrF,EAAA,CAAAkF,SAAA,mBAA2B;UAGzBlF,EADF,CAAAC,cAAA,kBAAkD,gBACtC;UAAAD,EAAA,CAAAY,MAAA,cAAM;UAAAZ,EAAA,CAAAa,YAAA,EAAW;UAC3Bb,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAY,MAAA,kBAAU;UAClBZ,EADkB,CAAAa,YAAA,EAAO,EAChB;UAEPb,EADF,CAAAC,cAAA,kBAAmD,gBACvC;UAAAD,EAAA,CAAAY,MAAA,gBAAQ;UAAAZ,EAAA,CAAAa,YAAA,EAAW;UAC7Bb,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAY,MAAA,gBAAQ;UAChBZ,EADgB,CAAAa,YAAA,EAAO,EACd;UACTb,EAAA,CAAAkF,SAAA,mBAA2B;UAC3BlF,EAAA,CAAAC,cAAA,kBAAyC;UAAnBD,EAAA,CAAAE,UAAA,mBAAAoF,kDAAA;YAAAtF,EAAA,CAAAK,aAAA,CAAAsE,GAAA;YAAA,OAAA3E,EAAA,CAAAU,WAAA,CAAS+D,GAAA,CAAAvB,MAAA,EAAQ;UAAA,EAAC;UACtClD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAY,MAAA,cAAM;UAAAZ,EAAA,CAAAa,YAAA,EAAW;UAC3Bb,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAY,MAAA,cAAM;UAIpBZ,EAJoB,CAAAa,YAAA,EAAO,EACZ,EACA,EACP,EACM;;;;;;UApHVb,EAAA,CAAAgB,SAAA,EAA0B;UAA1BhB,EAAA,CAAAc,WAAA,qBAA0B;UAMtBd,EAAA,CAAAgB,SAAA,GAAe;UAAfhB,EAAA,CAAAiB,iBAAA,CAAAwD,GAAA,CAAA5C,SAAA,CAAe;UASC7B,EAAA,CAAAgB,SAAA,GAAyB;UAAzBhB,EAAA,CAAAuF,gBAAA,YAAAd,GAAA,CAAA3C,WAAA,CAAyB;UAQ3C9B,EAAA,CAAAgB,SAAA,GAAsC;UAAtChB,EAAA,CAAAwF,UAAA,sBAAAC,mBAAA,CAAsC;UAE5BzF,EAAA,CAAAgB,SAAA,EAA8B;UAA9BhB,EAAA,CAAAwF,UAAA,aAAAf,GAAA,CAAA1C,iBAAA,CAA8B;UAYX/B,EAAA,CAAAgB,SAAA,IAAgB;UAAhBhB,EAAA,CAAAwF,UAAA,YAAAf,GAAA,CAAAzC,aAAA,CAAgB;UAuB7ChC,EAAA,CAAAgB,SAAA,GAAsC;UAAtChB,EAAA,CAAAwF,UAAA,sBAAAE,mBAAA,CAAsC;UA2BtC1F,EAAA,CAAAgB,SAAA,IAA8B;UAA9BhB,EAAA,CAAAwF,UAAA,sBAAAG,WAAA,CAA8B;UAMC3F,EAAA,CAAAgB,SAAA,GAAiB;UAAjBhB,EAAA,CAAAwF,UAAA,SAAAf,GAAA,CAAAnD,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}