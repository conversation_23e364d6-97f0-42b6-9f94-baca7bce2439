{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, of, throwError } from 'rxjs';\nimport { tap, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./storage.service\";\nimport * as i3 from \"@ionic/angular\";\nexport let CartService = /*#__PURE__*/(() => {\n  class CartService {\n    constructor(http, storageService, toastController) {\n      this.http = http;\n      this.storageService = storageService;\n      this.toastController = toastController;\n      this.API_URL = 'http://localhost:5000/api';\n      this.cartItems = new BehaviorSubject([]);\n      this.cartSummary = new BehaviorSubject(null);\n      this.cartItemCount = new BehaviorSubject(0);\n      this.cartTotalAmount = new BehaviorSubject(0);\n      this.showCartTotalPrice = new BehaviorSubject(false);\n      this.totalItemCount = new BehaviorSubject(0); // Combined cart + wishlist count\n      this.isLoadingCart = false;\n      this.useLocalStorageOnly = false; // Temporary flag to disable API calls\n      this.cartItems$ = this.cartItems.asObservable();\n      this.cartSummary$ = this.cartSummary.asObservable();\n      this.cartItemCount$ = this.cartItemCount.asObservable();\n      this.cartTotalAmount$ = this.cartTotalAmount.asObservable();\n      this.showCartTotalPrice$ = this.showCartTotalPrice.asObservable();\n      this.totalItemCount$ = this.totalItemCount.asObservable(); // Observable for total count\n      // Initialize cart on service creation - but only load from storage for guest users\n      this.initializeCart();\n    }\n    initializeCart() {\n      const token = localStorage.getItem('token');\n      if (token) {\n        // User is authenticated, load from API\n        this.loadCart();\n      } else {\n        // Guest user, load from local storage only\n        console.log('🔄 Guest user detected, loading cart from local storage only...');\n        this.loadCartFromStorage();\n      }\n    }\n    // Get cart from API\n    getCart() {\n      const token = localStorage.getItem('token');\n      const options = token ? {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      } : {};\n      return this.http.get(`${this.API_URL}/cart`, options);\n    }\n    // Get cart count only (lightweight endpoint) - returns total quantities\n    getCartCount() {\n      const token = localStorage.getItem('token');\n      const options = token ? {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      } : {};\n      return this.http.get(`${this.API_URL}/cart/count`, options);\n    }\n    // Get total count for logged-in user (cart + wishlist)\n    getTotalCount() {\n      const token = localStorage.getItem('token');\n      const options = token ? {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      } : {};\n      return this.http.get(`${this.API_URL}/cart/total-count`, options);\n    }\n    // Debug cart data\n    debugCart() {\n      const token = localStorage.getItem('token');\n      const options = token ? {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      } : {};\n      return this.http.get(`${this.API_URL}/cart/debug`, options).pipe(catchError(error => {\n        console.log('🔍 Debug endpoint not available, skipping debug');\n        return of({\n          success: false,\n          message: 'Debug endpoint not available'\n        });\n      }));\n    }\n    // Recalculate cart totals\n    recalculateCart() {\n      const token = localStorage.getItem('token');\n      const options = token ? {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      } : {};\n      return this.http.post(`${this.API_URL}/cart/recalculate`, {}, options).pipe(catchError(error => {\n        console.log('🔧 Recalculate endpoint not available, skipping recalculation');\n        return of({\n          success: false,\n          message: 'Recalculate endpoint not available'\n        });\n      }));\n    }\n    // Load cart and update local state\n    loadCart() {\n      // Temporary: Use local storage only to avoid API errors\n      if (this.useLocalStorageOnly) {\n        console.log('🔄 Using local storage only (API disabled)...');\n        this.loadCartFromStorage();\n        return;\n      }\n      // Check if user is logged in\n      const token = localStorage.getItem('token');\n      if (token) {\n        // User is logged in - try API first, fallback to local storage\n        console.log('🔄 User authenticated, attempting to load cart from API...');\n        this.loadCartFromAPI();\n      } else {\n        // Guest user - load from local storage\n        console.log('🔄 Guest user, loading cart from local storage...');\n        this.loadCartFromStorage();\n      }\n    }\n    // Load cart from API for logged-in users\n    loadCartFromAPI() {\n      // Check if user is authenticated\n      const token = localStorage.getItem('token');\n      if (!token) {\n        console.log('❌ No authentication token, using local storage fallback');\n        this.loadCartFromStorage();\n        return;\n      }\n      // Prevent multiple simultaneous API calls\n      if (this.isLoadingCart) {\n        console.log('🔄 Cart already loading, skipping duplicate request');\n        return;\n      }\n      this.isLoadingCart = true;\n      this.getCart().subscribe({\n        next: response => {\n          this.isLoadingCart = false;\n          if (response.success && response.cart) {\n            const items = response.cart.items || [];\n            this.cartItems.next(items);\n            this.cartSummary.next(response.summary);\n            this.updateCartCount();\n            console.log('✅ Cart loaded from API:', items.length, 'items');\n            console.log('🛒 Cart items details:', items.map(item => ({\n              id: item._id,\n              name: item.product?.name,\n              quantity: item.quantity,\n              price: item.product?.price\n            })));\n          } else {\n            // No cart data from API, initialize empty cart\n            this.cartItems.next([]);\n            this.cartSummary.next(null);\n            this.updateCartCount();\n            console.log('❌ No cart data from API');\n          }\n        },\n        error: error => {\n          this.isLoadingCart = false;\n          console.error('❌ API cart error:', error);\n          if (error.status === 401) {\n            console.log('❌ Authentication failed, clearing token');\n            localStorage.removeItem('token');\n            this.cartItems.next([]);\n            this.cartSummary.next(null);\n            this.updateCartCount();\n          } else if (error.status === 500) {\n            console.log('❌ Server error, using local storage fallback');\n            this.loadCartFromStorage();\n          } else {\n            console.log('❌ API error, using local storage fallback');\n            this.loadCartFromStorage();\n          }\n        }\n      });\n    }\n    loadCartFromStorage() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          // Check if storage service is available\n          if (!_this.storageService) {\n            console.log('Storage service not available, using empty cart');\n            _this.cartItems.next([]);\n            _this.updateCartCount();\n            return;\n          }\n          // Wait a bit for storage to initialize\n          yield new Promise(resolve => setTimeout(resolve, 100));\n          const cart = yield _this.storageService.getCart();\n          _this.cartItems.next(cart || []);\n          _this.updateCartCount();\n        } catch (error) {\n          console.error('Error loading cart from storage:', error);\n          _this.cartItems.next([]);\n          _this.updateCartCount();\n        }\n      })();\n    }\n    saveCartToStorage() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          if (!_this2.storageService) {\n            console.log('Storage service not available, skipping cart save');\n            return;\n          }\n          yield _this2.storageService.setCart(_this2.cartItems.value);\n        } catch (error) {\n          console.error('Error saving cart to storage:', error);\n        }\n      })();\n    }\n    updateCartCount() {\n      const items = this.cartItems.value || [];\n      const count = items.reduce((total, item) => total + item.quantity, 0);\n      this.cartItemCount.next(count);\n      console.log('🛒 Cart count updated:', count);\n    }\n    // Method to refresh cart on user login\n    refreshCartOnLogin() {\n      console.log('🔄 Refreshing cart on login...');\n      this.loadCartFromAPI();\n    }\n    // Method to refresh total count (cart + wishlist) for logged-in user\n    refreshTotalCount() {\n      const token = localStorage.getItem('token');\n      if (token) {\n        // Skip debug for now and go directly to getting total count\n        console.log('🔄 Refreshing total count for logged-in user...');\n        this.getTotalCountAfterRecalculation();\n      } else {\n        // No token, set all counts to 0\n        this.resetAllCounts();\n      }\n    }\n    getTotalCountAfterRecalculation() {\n      // Only refresh if user is authenticated\n      const token = localStorage.getItem('token');\n      if (!token) {\n        console.log('🔒 No authentication token, skipping cart count refresh');\n        this.resetAllCounts();\n        return;\n      }\n      this.getTotalCount().subscribe({\n        next: response => {\n          if (response.success) {\n            const data = response.data;\n            // Update cart count with TOTAL QUANTITY (not just item count)\n            this.cartItemCount.next(data.cart.quantityTotal || 0);\n            this.cartTotalAmount.next(data.cart.totalAmount || 0);\n            this.showCartTotalPrice.next(data.showCartTotalPrice || false);\n            // Update TOTAL COUNT (cart + wishlist)\n            this.totalItemCount.next(data.totalCount || 0);\n            console.log('🔢 Total count refreshed for user:', response.username, {\n              cartQuantityTotal: data.cart.quantityTotal,\n              cartItemCount: data.cart.itemCount,\n              wishlistItems: data.wishlist.itemCount,\n              totalCount: data.totalCount,\n              cartTotal: data.cart.totalAmount,\n              showPrice: data.showCartTotalPrice\n            });\n          }\n        },\n        error: error => {\n          console.error('❌ Error refreshing total count:', error);\n          if (error.status === 401) {\n            console.log('❌ Authentication failed, clearing token');\n            localStorage.removeItem('token');\n            this.resetAllCounts();\n          } else if (error.status === 404) {\n            console.log('❌ Total count endpoint not found, using fallback');\n            // Fallback: Load cart directly to get counts\n            this.loadCartFromAPI();\n          } else {\n            // For other errors, reset counts to avoid showing stale data\n            this.resetAllCounts();\n          }\n        }\n      });\n    }\n    // Method to refresh only cart count (lightweight) - kept for backward compatibility\n    refreshCartCount() {\n      this.refreshTotalCount(); // Use the new total count method\n    }\n    // Reset all counts to 0\n    resetAllCounts() {\n      this.cartItemCount.next(0);\n      this.cartTotalAmount.next(0);\n      this.showCartTotalPrice.next(false);\n      this.totalItemCount.next(0);\n      console.log('🔄 All counts reset to 0');\n    }\n    // Method to clear cart on logout\n    clearCartOnLogout() {\n      console.log('🔄 Clearing cart on logout...');\n      this.cartItems.next([]);\n      this.cartSummary.next(null);\n      this.resetAllCounts();\n    }\n    // Temporary method to enable/disable API calls\n    setUseLocalStorageOnly(useLocalOnly) {\n      this.useLocalStorageOnly = useLocalOnly;\n      console.log('🔧 Cart API calls', useLocalOnly ? 'DISABLED' : 'ENABLED');\n      if (useLocalOnly) {\n        console.log('🔧 Cart will use local storage only');\n      }\n    }\n    // Add item to cart via API\n    addToCart(productId, quantity = 1, size, color) {\n      const payload = {\n        productId,\n        quantity,\n        size,\n        color\n      };\n      const token = localStorage.getItem('token');\n      const options = token ? {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      } : {};\n      return this.http.post(`${this.API_URL}/cart/add`, payload, options).pipe(tap(response => {\n        if (response.success) {\n          console.log('✅ Product added to cart successfully');\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        } else {\n          console.warn('⚠️ Add to cart API returned unsuccessful response');\n        }\n      }), catchError(error => {\n        console.error('❌ Error adding to cart:', error);\n        return throwError(() => error);\n      }));\n    }\n    // Legacy method for backward compatibility - works for guest users\n    addToCartLegacy(_x) {\n      var _this3 = this;\n      return _asyncToGenerator(function* (product, quantity = 1, size, color) {\n        try {\n          const productId = product._id || product.id;\n          // Try API first, but fallback to local storage for guest users\n          try {\n            const response = yield _this3.addToCart(productId, quantity, size, color).toPromise();\n            if (response?.success) {\n              yield _this3.showToast('Item added to cart', 'success');\n              _this3.loadCart(); // Refresh cart\n              return true;\n            }\n          } catch (apiError) {\n            console.log('API not available, using local storage');\n          }\n          // Fallback to local storage (for guest users)\n          const cartItem = {\n            _id: `${productId}_${size || 'default'}_${color || 'default'}`,\n            product: {\n              _id: productId,\n              name: product.name,\n              price: product.price,\n              originalPrice: product.originalPrice,\n              images: product.images || [],\n              brand: product.brand || '',\n              discount: product.discount\n            },\n            quantity,\n            size,\n            color,\n            addedAt: new Date()\n          };\n          const currentCart = _this3.cartItems.value;\n          const existingItemIndex = currentCart.findIndex(item => item.product._id === productId && (item.size || 'default') === (size || 'default') && (item.color || 'default') === (color || 'default'));\n          if (existingItemIndex >= 0) {\n            currentCart[existingItemIndex].quantity += quantity;\n          } else {\n            currentCart.push(cartItem);\n          }\n          _this3.cartItems.next(currentCart);\n          _this3.updateCartCount();\n          yield _this3.saveCartToStorage();\n          yield _this3.showToast('Item added to cart', 'success');\n          return true;\n        } catch (error) {\n          console.error('Error adding to cart:', error);\n          yield _this3.showToast('Failed to add item to cart', 'danger');\n          return false;\n        }\n      }).apply(this, arguments);\n    }\n    // Remove item from cart via API\n    removeFromCart(itemId) {\n      const token = localStorage.getItem('token');\n      const options = token ? {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      } : {};\n      return this.http.delete(`${this.API_URL}/cart/remove/${itemId}`, options).pipe(tap(response => {\n        if (response.success) {\n          console.log('✅ Product removed from cart successfully');\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        } else {\n          console.warn('⚠️ Remove from cart API returned unsuccessful response');\n        }\n      }), catchError(error => {\n        console.error('❌ Error removing from cart:', error);\n        return throwError(() => error);\n      }));\n    }\n    // Bulk remove items from cart\n    bulkRemoveFromCart(itemIds) {\n      const token = localStorage.getItem('token');\n      const options = token ? {\n        body: {\n          itemIds\n        },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      } : {\n        body: {\n          itemIds\n        }\n      };\n      return this.http.delete(`${this.API_URL}/cart/bulk-remove`, options).pipe(tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      }));\n    }\n    // Legacy method\n    removeFromCartLegacy(itemId) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const response = yield _this4.removeFromCart(itemId).toPromise();\n          if (response?.success) {\n            yield _this4.showToast('Item removed from cart', 'success');\n            _this4.loadCart(); // Refresh cart\n          }\n        } catch (error) {\n          console.error('Error removing from cart:', error);\n          yield _this4.showToast('Failed to remove item from cart', 'danger');\n        }\n      })();\n    }\n    // Update cart item quantity via API\n    updateCartItem(itemId, quantity) {\n      const token = localStorage.getItem('token');\n      const options = token ? {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      } : {};\n      return this.http.put(`${this.API_URL}/cart/update/${itemId}`, {\n        quantity\n      }, options).pipe(tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      }));\n    }\n    // Legacy method\n    updateQuantity(itemId, quantity) {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          if (quantity <= 0) {\n            yield _this5.removeFromCartLegacy(itemId);\n            return;\n          }\n          const response = yield _this5.updateCartItem(itemId, quantity).toPromise();\n          if (response?.success) {\n            _this5.loadCart(); // Refresh cart\n          }\n        } catch (error) {\n          console.error('Error updating quantity:', error);\n          yield _this5.showToast('Failed to update quantity', 'danger');\n        }\n      })();\n    }\n    // Clear cart via API\n    clearCartAPI() {\n      const token = localStorage.getItem('token');\n      const options = token ? {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      } : {};\n      return this.http.delete(`${this.API_URL}/cart/clear`, options);\n    }\n    clearCart() {\n      var _this6 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const response = yield _this6.clearCartAPI().toPromise();\n          if (response?.success) {\n            _this6.cartItems.next([]);\n            _this6.cartSummary.next(null);\n            _this6.updateCartCount();\n            yield _this6.showToast('Cart cleared', 'success');\n          }\n        } catch (error) {\n          console.error('Error clearing cart:', error);\n          yield _this6.showToast('Failed to clear cart', 'danger');\n        }\n      })();\n    }\n    getCartTotal() {\n      const summary = this.cartSummary.value;\n      if (summary) {\n        return summary.total;\n      }\n      // Fallback calculation\n      return this.cartItems.value.reduce((total, item) => {\n        const price = item.product.price;\n        return total + price * item.quantity;\n      }, 0);\n    }\n    // Get total count (cart + wishlist items) for the logged-in user\n    getTotalItemCount() {\n      return this.totalItemCount.value;\n    }\n    // Get cart item count only\n    getCartItemCount() {\n      return this.cartItemCount.value;\n    }\n    // Get cart total amount\n    getCartTotalAmount() {\n      return this.cartTotalAmount.value;\n    }\n    // Check if cart total price should be displayed (when cart has 4+ products)\n    shouldShowCartTotalPrice() {\n      return this.showCartTotalPrice.value;\n    }\n    isInCart(productId, size, color) {\n      return this.cartItems.value.some(item => item.product._id === productId && (item.size || 'default') === (size || 'default') && (item.color || 'default') === (color || 'default'));\n    }\n    getCartItem(productId, size, color) {\n      return this.cartItems.value.find(item => item.product._id === productId && (item.size || 'default') === (size || 'default') && (item.color || 'default') === (color || 'default'));\n    }\n    showToast(message, color) {\n      var _this7 = this;\n      return _asyncToGenerator(function* () {\n        const toast = yield _this7.toastController.create({\n          message: message,\n          duration: 2000,\n          color: color,\n          position: 'bottom'\n        });\n        toast.present();\n      })();\n    }\n    static {\n      this.ɵfac = function CartService_Factory(t) {\n        return new (t || CartService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.StorageService), i0.ɵɵinject(i3.ToastController));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: CartService,\n        factory: CartService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return CartService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}