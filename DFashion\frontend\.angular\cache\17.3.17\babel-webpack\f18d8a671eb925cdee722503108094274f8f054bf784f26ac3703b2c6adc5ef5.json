{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport { HeaderComponent } from './shared/components/header/header.component';\nimport { NotificationComponent } from './shared/components/notification/notification.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./core/services/auth.service\";\nimport * as i3 from \"@angular/common\";\nfunction AppComponent_app_header_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-header\");\n  }\n}\nexport class AppComponent {\n  constructor(router, authService) {\n    this.router = router;\n    this.authService = authService;\n    this.title = 'DFashion';\n    this.showHeader = true;\n  }\n  ngOnInit() {\n    // Hide header on auth pages\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      const navigationEnd = event;\n      this.showHeader = !navigationEnd.url.includes('/auth');\n    });\n    // Initialize auth state\n    this.authService.initializeAuth();\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"app-container\"], [4, \"ngIf\"], [1, \"main-content\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, AppComponent_app_header_1_Template, 1, 0, \"app-header\", 1);\n          i0.ɵɵelementStart(2, \"main\", 2);\n          i0.ɵɵelement(3, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(4, \"app-notification\");\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showHeader);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"with-header\", ctx.showHeader);\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf, RouterOutlet, HeaderComponent, NotificationComponent],\n      styles: [\".app-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  transition: all 0.3s ease;\\n}\\n\\n.main-content.with-header[_ngcontent-%COMP%] {\\n  margin-top: 60px;\\n}\\n\\n@media (max-width: 768px) {\\n  .main-content.with-header[_ngcontent-%COMP%] {\\n    margin-top: 56px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDSTtFQUNFLGlCQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0FBQU47O0FBR0k7RUFDRSxPQUFBO0VBQ0EseUJBQUE7QUFBTjs7QUFHSTtFQUNFLGdCQUFBO0FBQU47O0FBR0k7RUFDRTtJQUNFLGdCQUFBO0VBQU47QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5hcHAtY29udGFpbmVyIHtcbiAgICAgIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgfVxuXG4gICAgLm1haW4tY29udGVudCB7XG4gICAgICBmbGV4OiAxO1xuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbiAgICB9XG5cbiAgICAubWFpbi1jb250ZW50LndpdGgtaGVhZGVyIHtcbiAgICAgIG1hcmdpbi10b3A6IDYwcHg7XG4gICAgfVxuXG4gICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgICAubWFpbi1jb250ZW50LndpdGgtaGVhZGVyIHtcbiAgICAgICAgbWFyZ2luLXRvcDogNTZweDtcbiAgICAgIH1cbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterOutlet", "NavigationEnd", "filter", "HeaderComponent", "NotificationComponent", "i0", "ɵɵelement", "AppComponent", "constructor", "router", "authService", "title", "showHeader", "ngOnInit", "events", "pipe", "event", "subscribe", "navigationEnd", "url", "includes", "initializeAuth", "ɵɵdirectiveInject", "i1", "Router", "i2", "AuthService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtemplate", "AppComponent_app_header_1_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵclassProp", "i3", "NgIf", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet, Router, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\n\nimport { HeaderComponent } from './shared/components/header/header.component';\nimport { NotificationComponent } from './shared/components/notification/notification.component';\nimport { AuthService } from './core/services/auth.service';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [CommonModule, RouterOutlet, HeaderComponent, NotificationComponent],\n  template: `\n    <div class=\"app-container\">\n      <app-header *ngIf=\"showHeader\"></app-header>\n      <main class=\"main-content\" [class.with-header]=\"showHeader\">\n        <router-outlet></router-outlet>\n      </main>\n    </div>\n\n    <!-- Notifications -->\n    <app-notification></app-notification>\n  `,\n  styles: [`\n    .app-container {\n      min-height: 100vh;\n      display: flex;\n      flex-direction: column;\n    }\n\n    .main-content {\n      flex: 1;\n      transition: all 0.3s ease;\n    }\n\n    .main-content.with-header {\n      margin-top: 60px;\n    }\n\n    @media (max-width: 768px) {\n      .main-content.with-header {\n        margin-top: 56px;\n      }\n    }\n  `]\n})\nexport class AppComponent implements OnInit {\n  title = 'DFashion';\n  showHeader = true;\n\n  constructor(\n    private router: Router,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit() {\n    // Hide header on auth pages\n    this.router.events\n      .pipe(filter(event => event instanceof NavigationEnd))\n      .subscribe((event) => {\n        const navigationEnd = event as NavigationEnd;\n        this.showHeader = !navigationEnd.url.includes('/auth');\n      });\n\n    // Initialize auth state\n    this.authService.initializeAuth();\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,EAAUC,aAAa,QAAQ,iBAAiB;AACrE,SAASC,MAAM,QAAQ,gBAAgB;AAEvC,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,qBAAqB,QAAQ,yDAAyD;;;;;;;IASzFC,EAAA,CAAAC,SAAA,iBAA4C;;;AAgClD,OAAM,MAAOC,YAAY;EAIvBC,YACUC,MAAc,EACdC,WAAwB;IADxB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IALrB,KAAAC,KAAK,GAAG,UAAU;IAClB,KAAAC,UAAU,GAAG,IAAI;EAKd;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,MAAM,CAACK,MAAM,CACfC,IAAI,CAACb,MAAM,CAACc,KAAK,IAAIA,KAAK,YAAYf,aAAa,CAAC,CAAC,CACrDgB,SAAS,CAAED,KAAK,IAAI;MACnB,MAAME,aAAa,GAAGF,KAAsB;MAC5C,IAAI,CAACJ,UAAU,GAAG,CAACM,aAAa,CAACC,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC;IACxD,CAAC,CAAC;IAEJ;IACA,IAAI,CAACV,WAAW,CAACW,cAAc,EAAE;EACnC;;;uBApBWd,YAAY,EAAAF,EAAA,CAAAiB,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAnB,EAAA,CAAAiB,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAZnB,YAAY;MAAAoB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAxB,EAAA,CAAAyB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjCrB/B,EAAA,CAAAiC,cAAA,aAA2B;UACzBjC,EAAA,CAAAkC,UAAA,IAAAC,kCAAA,wBAA+B;UAC/BnC,EAAA,CAAAiC,cAAA,cAA4D;UAC1DjC,EAAA,CAAAC,SAAA,oBAA+B;UAEnCD,EADE,CAAAoC,YAAA,EAAO,EACH;UAGNpC,EAAA,CAAAC,SAAA,uBAAqC;;;UAPtBD,EAAA,CAAAqC,SAAA,EAAgB;UAAhBrC,EAAA,CAAAsC,UAAA,SAAAN,GAAA,CAAAzB,UAAA,CAAgB;UACFP,EAAA,CAAAqC,SAAA,EAAgC;UAAhCrC,EAAA,CAAAuC,WAAA,gBAAAP,GAAA,CAAAzB,UAAA,CAAgC;;;qBAJrDb,YAAY,EAAA8C,EAAA,CAAAC,IAAA,EAAE9C,YAAY,EAAEG,eAAe,EAAEC,qBAAqB;MAAA2C,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}