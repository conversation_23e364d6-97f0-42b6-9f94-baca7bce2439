{"ast": null, "code": "import _asyncToGenerator from \"E:/Fahion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ViewAddStoriesComponent } from '../../components/instagram-stories/view-add-stories.component';\nimport { PaymentModalComponent } from '../../../../shared/components/payment-modal/payment-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/product.service\";\nimport * as i3 from \"../../../../core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction HomeComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"div\", 5);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading amazing fashion...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HomeComponent_div_2_article_5_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(post_r3.location);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_12_button_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelement(1, \"img\", 70);\n    i0.ɵɵelementStart(2, \"div\", 71)(3, \"span\", 72);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 73);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r5.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r5.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.formatPrice(product_r5.price));\n  }\n}\nfunction HomeComponent_div_2_article_5_div_12_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_12_button_1_Template_button_click_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r5.showProductDetails(product_r5));\n    });\n    i0.ɵɵelement(1, \"div\", 67);\n    i0.ɵɵtemplate(2, HomeComponent_div_2_article_5_div_12_button_1_div_2_Template, 7, 4, \"div\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", (product_r5.position == null ? null : product_r5.position.x) || 50, \"%\")(\"top\", (product_r5.position == null ? null : product_r5.position.y) || 50, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r5.showPreview);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtemplate(1, HomeComponent_div_2_article_5_div_12_button_1_Template, 3, 5, \"button\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", post_r3.products);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"span\", 75);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r5.formatLikesCount(post_r3.likes), \" likes\");\n  }\n}\nfunction HomeComponent_div_2_article_5_div_29_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelement(1, \"img\", 87);\n    i0.ɵɵelementStart(2, \"div\", 88)(3, \"span\", 72);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 73);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r8 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r8.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r8.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.formatPrice(product_r8.price));\n  }\n}\nfunction HomeComponent_div_2_article_5_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"div\", 77);\n    i0.ɵɵtemplate(2, HomeComponent_div_2_article_5_div_29_div_2_Template, 7, 4, \"div\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 79)(4, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_29_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const post_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.buyNow(post_r3.products[0]));\n    });\n    i0.ɵɵelement(5, \"i\", 81);\n    i0.ɵɵtext(6, \" Buy Now \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_29_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const post_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.addToWishlist(post_r3.products[0]));\n    });\n    i0.ɵɵelement(8, \"i\", 83);\n    i0.ɵɵtext(9, \" Wishlist \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_29_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const post_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.addToCart(post_r3.products[0]));\n    });\n    i0.ɵɵelement(11, \"i\", 85);\n    i0.ɵɵtext(12, \" Add to Cart \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const post_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", post_r3.products.slice(0, 2));\n  }\n}\nfunction HomeComponent_div_2_article_5_div_30_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"span\", 94);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 95);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const comment_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r10.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r10.text);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_30_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const post_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.toggleComments(post_r3));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 91);\n    i0.ɵɵtemplate(4, HomeComponent_div_2_article_5_div_30_div_4_Template, 5, 2, \"div\", 92);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" View all \", post_r3.comments.length, \" comments \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", post_r3.comments.slice(0, 2));\n  }\n}\nfunction HomeComponent_div_2_article_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"article\", 34)(1, \"header\", 35)(2, \"div\", 36);\n    i0.ɵɵelement(3, \"img\", 37);\n    i0.ɵɵelementStart(4, \"div\", 38)(5, \"h3\", 39);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, HomeComponent_div_2_article_5_span_7_Template, 2, 1, \"span\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 41);\n    i0.ɵɵelement(9, \"i\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 43);\n    i0.ɵɵelement(11, \"img\", 44);\n    i0.ɵɵtemplate(12, HomeComponent_div_2_article_5_div_12_Template, 2, 1, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 46)(14, \"div\", 47)(15, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_15_listener() {\n      const post_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.toggleLike(post_r3));\n    });\n    i0.ɵɵelement(16, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_17_listener() {\n      const post_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.focusCommentInput(post_r3));\n    });\n    i0.ɵɵelement(18, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_19_listener() {\n      const post_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.sharePost(post_r3));\n    });\n    i0.ɵɵelement(20, \"i\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_21_listener() {\n      const post_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.toggleSave(post_r3));\n    });\n    i0.ɵɵelement(22, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, HomeComponent_div_2_article_5_div_23_Template, 3, 1, \"div\", 54);\n    i0.ɵɵelementStart(24, \"div\", 55)(25, \"span\", 39);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 56);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(29, HomeComponent_div_2_article_5_div_29_Template, 13, 1, \"div\", 57)(30, HomeComponent_div_2_article_5_div_30_Template, 5, 2, \"div\", 58);\n    i0.ɵɵelementStart(31, \"div\", 59);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 60)(34, \"input\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HomeComponent_div_2_article_5_Template_input_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r5.newComment, $event) || (ctx_r5.newComment = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function HomeComponent_div_2_article_5_Template_input_keyup_enter_34_listener() {\n      const post_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.addComment(post_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_35_listener() {\n      const post_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.addComment(post_r3));\n    });\n    i0.ɵɵtext(36, \"Post\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const post_r3 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", post_r3.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", post_r3.user.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(post_r3.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r3.location);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", post_r3.mediaUrl, i0.ɵɵsanitizeUrl)(\"alt\", post_r3.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r3.products && post_r3.products.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"liked\", post_r3.isLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(post_r3.isLiked ? \"fas fa-heart\" : \"far fa-heart\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"saved\", post_r3.isSaved);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(post_r3.isSaved ? \"fas fa-bookmark\" : \"far fa-bookmark\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r3.likes > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(post_r3.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(post_r3.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r3.products && post_r3.products.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r3.comments && post_r3.comments.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.getTimeAgo(post_r3.createdAt), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.newComment);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r5.newComment || !ctx_r5.newComment.trim());\n  }\n}\nfunction HomeComponent_div_2_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelement(1, \"img\", 97);\n    i0.ɵɵelementStart(2, \"div\", 98)(3, \"h4\", 99);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 100);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 101);\n    i0.ɵɵtext(8, \"Switch\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r5.currentUser.avatar, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r5.currentUser.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.currentUser.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.currentUser.fullName);\n  }\n}\nfunction HomeComponent_div_2_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_div_15_Template_div_click_0_listener() {\n      const item_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onProductClick(item_r12));\n    });\n    i0.ɵɵelement(1, \"img\", 103);\n    i0.ɵɵelementStart(2, \"div\", 104)(3, \"span\", 105);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 106);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", item_r12.images[0] == null ? null : item_r12.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", item_r12.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.formatPrice(item_r12.price));\n  }\n}\nfunction HomeComponent_div_2_div_23_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 115);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r14 = i0.ɵɵnextContext().$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r5.formatPrice(product_r14.originalPrice));\n  }\n}\nfunction HomeComponent_div_2_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_div_23_Template_div_click_0_listener() {\n      const product_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onProductClick(product_r14));\n    });\n    i0.ɵɵelement(1, \"img\", 108);\n    i0.ɵɵelementStart(2, \"div\", 109)(3, \"span\", 110);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 111);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 112)(8, \"span\", 113);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, HomeComponent_div_2_div_23_span_10_Template, 2, 1, \"span\", 114);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r14 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r14.images[0] == null ? null : product_r14.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r14.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r14.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r14.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.formatPrice(product_r14.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r14.originalPrice);\n  }\n}\nfunction HomeComponent_div_2_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 116);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_div_31_Template_div_click_0_listener() {\n      const product_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onProductClick(product_r16));\n    });\n    i0.ɵɵelement(1, \"img\", 117);\n    i0.ɵɵelementStart(2, \"div\", 118);\n    i0.ɵɵelement(3, \"i\", 119);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Hot\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 120)(7, \"span\", 121);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 122)(10, \"span\");\n    i0.ɵɵelement(11, \"i\", 123);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵelement(14, \"i\", 124);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"span\", 125);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r16 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r16.images[0] == null ? null : product_r16.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r16.name);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(product_r16.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.formatNumber((product_r16.analytics == null ? null : product_r16.analytics.views) || 0), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.formatNumber((product_r16.analytics == null ? null : product_r16.analytics.likes) || 0), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.formatPrice(product_r16.price));\n  }\n}\nfunction HomeComponent_div_2_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 126);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_div_39_Template_div_click_0_listener() {\n      const product_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onProductClick(product_r18));\n    });\n    i0.ɵɵelement(1, \"img\", 127);\n    i0.ɵɵelementStart(2, \"div\", 128);\n    i0.ɵɵtext(3, \"New\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 129)(5, \"span\", 130);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 131);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 132);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r18 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r18.images[0] == null ? null : product_r18.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r18.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r18.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r18.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.formatPrice(product_r18.price));\n  }\n}\nfunction HomeComponent_div_2_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 133);\n    i0.ɵɵelement(1, \"img\", 134);\n    i0.ɵɵelementStart(2, \"div\", 135)(3, \"span\", 136);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 137);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 138);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_div_47_Template_button_click_7_listener() {\n      const user_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.followUser(user_r20));\n    });\n    i0.ɵɵtext(8, \"Follow\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r20 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", user_r20.avatar, i0.ɵɵsanitizeUrl)(\"alt\", user_r20.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r20.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r5.formatNumber(user_r20.followers), \" followers\");\n  }\n}\nfunction HomeComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"section\", 8);\n    i0.ɵɵelement(3, \"app-view-add-stories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"section\", 9);\n    i0.ɵɵtemplate(5, HomeComponent_div_2_article_5_Template, 37, 23, \"article\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"aside\", 11);\n    i0.ɵɵtemplate(7, HomeComponent_div_2_div_7_Template, 9, 4, \"div\", 12);\n    i0.ɵɵelementStart(8, \"section\", 13)(9, \"div\", 14)(10, \"h3\");\n    i0.ɵɵtext(11, \"Summer Collection 2024\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.viewSummerCollection());\n    });\n    i0.ɵɵtext(13, \"See All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 16);\n    i0.ɵɵtemplate(15, HomeComponent_div_2_div_15_Template, 7, 4, \"div\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"section\", 13)(17, \"div\", 14)(18, \"h3\");\n    i0.ɵɵtext(19, \"Featured Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 18);\n    i0.ɵɵtext(21, \"See All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 19);\n    i0.ɵɵtemplate(23, HomeComponent_div_2_div_23_Template, 11, 6, \"div\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"section\", 13)(25, \"div\", 14)(26, \"h3\");\n    i0.ɵɵtext(27, \"Trending Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 21);\n    i0.ɵɵtext(29, \"See All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 22);\n    i0.ɵɵtemplate(31, HomeComponent_div_2_div_31_Template, 18, 6, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"section\", 13)(33, \"div\", 14)(34, \"h3\");\n    i0.ɵɵtext(35, \"New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"button\", 24);\n    i0.ɵɵtext(37, \"See All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 25);\n    i0.ɵɵtemplate(39, HomeComponent_div_2_div_39_Template, 11, 5, \"div\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"section\", 13)(41, \"div\", 14)(42, \"h3\");\n    i0.ɵɵtext(43, \"Suggested for You\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"button\", 27);\n    i0.ɵɵtext(45, \"See All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 28);\n    i0.ɵɵtemplate(47, HomeComponent_div_2_div_47_Template, 9, 4, \"div\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 30)(49, \"div\", 31)(50, \"a\", 32);\n    i0.ɵɵtext(51, \"About\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"a\", 32);\n    i0.ɵɵtext(53, \"Help\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"a\", 32);\n    i0.ɵɵtext(55, \"Press\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"a\", 32);\n    i0.ɵɵtext(57, \"API\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"a\", 32);\n    i0.ɵɵtext(59, \"Jobs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"a\", 32);\n    i0.ɵɵtext(61, \"Privacy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"a\", 32);\n    i0.ɵɵtext(63, \"Terms\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"div\", 33)(65, \"span\");\n    i0.ɵɵtext(66, \"\\u00A9 2024 DFashion\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.instagramPosts)(\"ngForTrackBy\", ctx_r5.trackByPostId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.currentUser);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.summerCollection.slice(0, 3));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.featuredProducts.slice(0, 4));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.trendingProducts.slice(0, 4));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.newArrivals.slice(0, 4));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.suggestedUsers.slice(0, 3));\n  }\n}\nexport class HomeComponent {\n  constructor(router, productService, authService) {\n    this.router = router;\n    this.productService = productService;\n    this.authService = authService;\n    this.featuredProducts = [];\n    this.trendingProducts = [];\n    this.newArrivals = [];\n    this.trendingPosts = [];\n    this.categories = [];\n    this.isLoading = true;\n    this.isAuthenticated = false;\n    // Instagram-style data\n    this.instagramPosts = [];\n    this.summerCollection = [];\n    this.suggestedUsers = [];\n    this.currentUser = null;\n    this.newComment = '';\n    // Payment Modal\n    this.showPaymentModal = false;\n    this.paymentModalData = null;\n  }\n  ngOnInit() {\n    this.loadHomeData();\n    this.checkAuthStatus();\n    this.loadInstagramData();\n  }\n  checkAuthStatus() {\n    this.authService.currentUser$.subscribe(user => {\n      this.isAuthenticated = !!user;\n      this.currentUser = user;\n    });\n  }\n  loadInstagramData() {\n    this.loadInstagramPosts();\n    this.loadSummerCollection();\n    this.loadSuggestedUsers();\n  }\n  loadInstagramPosts() {\n    // Mock Instagram-style posts with shoppable content\n    this.instagramPosts = [{\n      id: '1',\n      user: {\n        username: 'fashionista_maya',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600',\n      caption: 'Summer vibes with this amazing denim jacket! Perfect for those cool evenings ✨ #SummerStyle #DenimLove',\n      location: 'Los Angeles, CA',\n      likes: 1247,\n      isLiked: false,\n      isSaved: false,\n      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),\n      products: [{\n        id: '1',\n        name: 'Vintage Denim Jacket',\n        price: 89.99,\n        image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=200',\n        position: {\n          x: 30,\n          y: 40\n        }\n      }],\n      comments: [{\n        username: 'style_lover',\n        text: 'Love this look! 😍'\n      }, {\n        username: 'fashion_fan',\n        text: 'Where can I get this jacket?'\n      }]\n    }, {\n      id: '2',\n      user: {\n        username: 'style_guru_alex',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=600',\n      caption: 'Elegant evening look with this silk dress 💫 Perfect for date nights! #EveningWear #SilkDress',\n      location: 'New York, NY',\n      likes: 892,\n      isLiked: true,\n      isSaved: false,\n      createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000),\n      products: [{\n        id: '2',\n        name: 'Silk Slip Dress',\n        price: 159.99,\n        image: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=200',\n        position: {\n          x: 50,\n          y: 60\n        }\n      }],\n      comments: [{\n        username: 'elegant_style',\n        text: 'Absolutely gorgeous! 💕'\n      }]\n    }, {\n      id: '3',\n      user: {\n        username: 'trendy_sarah',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600',\n      caption: 'Cozy vibes in this amazing knit sweater! Perfect for autumn days 🍂 #CozyStyle #AutumnFashion',\n      location: 'San Francisco, CA',\n      likes: 567,\n      isLiked: false,\n      isSaved: true,\n      createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000),\n      products: [{\n        id: '3',\n        name: 'Cozy Knit Sweater',\n        price: 79.99,\n        image: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=200',\n        position: {\n          x: 40,\n          y: 50\n        }\n      }],\n      comments: []\n    }];\n  }\n  loadSummerCollection() {\n    this.summerCollection = [{\n      _id: 'summer1',\n      name: 'Floral Maxi Dress',\n      price: 129.99,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=300'\n      }]\n    }, {\n      _id: 'summer2',\n      name: 'Linen Beach Shirt',\n      price: 69.99,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300'\n      }]\n    }, {\n      _id: 'summer3',\n      name: 'Summer Sandals',\n      price: 89.99,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1543163521-1bf539c55dd2?w=300'\n      }]\n    }];\n  }\n  loadSuggestedUsers() {\n    this.suggestedUsers = [{\n      username: 'fashion_weekly',\n      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100',\n      followers: 125000\n    }, {\n      username: 'style_trends',\n      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',\n      followers: 89000\n    }, {\n      username: 'outfit_inspo',\n      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',\n      followers: 67000\n    }];\n  }\n  loadHomeData() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        // Load all data in parallel\n        const [featured, trending, arrivals] = yield Promise.all([_this.productService.getFeaturedProducts().toPromise(), _this.productService.getTrendingProducts().toPromise(), _this.productService.getNewArrivals().toPromise()]);\n        _this.featuredProducts = (featured?.products || featured?.data || featured || []).slice(0, 8);\n        _this.trendingProducts = (trending?.products || trending?.data || trending || []).slice(0, 8);\n        _this.newArrivals = (arrivals?.products || arrivals?.data || arrivals || []).slice(0, 8);\n        // If no data loaded, use fallback\n        if (_this.featuredProducts.length === 0) {\n          _this.featuredProducts = _this.getFallbackProducts();\n        }\n        if (_this.trendingProducts.length === 0) {\n          _this.trendingProducts = _this.getFallbackProducts();\n        }\n        if (_this.newArrivals.length === 0) {\n          _this.newArrivals = _this.getFallbackProducts();\n        }\n        // Load categories\n        _this.loadCategories();\n        // Load trending posts (mock data for now)\n        _this.loadTrendingPosts();\n      } catch (error) {\n        console.error('Error loading home data:', error);\n        _this.loadFallbackData();\n      } finally {\n        _this.isLoading = false;\n      }\n    })();\n  }\n  loadCategories() {\n    this.categories = [{\n      name: 'Women',\n      icon: 'fas fa-female',\n      color: 'linear-gradient(45deg, #f093fb, #f5576c)'\n    }, {\n      name: 'Men',\n      icon: 'fas fa-male',\n      color: 'linear-gradient(45deg, #667eea, #764ba2)'\n    }, {\n      name: 'Kids',\n      icon: 'fas fa-child',\n      color: 'linear-gradient(45deg, #4facfe, #00f2fe)'\n    }, {\n      name: 'Accessories',\n      icon: 'fas fa-gem',\n      color: 'linear-gradient(45deg, #43e97b, #38f9d7)'\n    }, {\n      name: 'Shoes',\n      icon: 'fas fa-shoe-prints',\n      color: 'linear-gradient(45deg, #fa709a, #fee140)'\n    }, {\n      name: 'Bags',\n      icon: 'fas fa-shopping-bag',\n      color: 'linear-gradient(45deg, #a8edea, #fed6e3)'\n    }];\n  }\n  loadTrendingPosts() {\n    this.trendingPosts = [{\n      title: 'Summer Fashion Trends 2024',\n      mediaUrl: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',\n      mediaType: 'image',\n      analytics: {\n        views: 15420,\n        likes: 892\n      }\n    }, {\n      title: 'Street Style Inspiration',\n      mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400',\n      mediaType: 'image',\n      analytics: {\n        views: 12890,\n        likes: 1205\n      }\n    }];\n  }\n  getFallbackProducts() {\n    return [{\n      _id: '1',\n      name: 'Vintage Denim Jacket',\n      brand: 'Urban Threads',\n      price: 89.99,\n      originalPrice: 129.99,\n      discount: 31,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400'\n      }],\n      rating: {\n        average: 4.2,\n        count: 156\n      },\n      analytics: {\n        views: 15420,\n        likes: 892\n      }\n    }, {\n      _id: '2',\n      name: 'Silk Slip Dress',\n      brand: 'Ethereal',\n      price: 159.99,\n      originalPrice: 199.99,\n      discount: 20,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400'\n      }],\n      rating: {\n        average: 4.5,\n        count: 89\n      },\n      analytics: {\n        views: 12890,\n        likes: 1205\n      }\n    }, {\n      _id: '3',\n      name: 'Cozy Knit Sweater',\n      brand: 'Cozy Co',\n      price: 79.99,\n      originalPrice: 99.99,\n      discount: 20,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400'\n      }],\n      rating: {\n        average: 4.3,\n        count: 203\n      },\n      analytics: {\n        views: 8750,\n        likes: 567\n      }\n    }, {\n      _id: '4',\n      name: 'High-Waisted Jeans',\n      brand: 'Urban Threads',\n      price: 119.99,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=400'\n      }],\n      rating: {\n        average: 4.6,\n        count: 342\n      },\n      analytics: {\n        views: 22100,\n        likes: 1456\n      }\n    }, {\n      _id: '5',\n      name: 'Floral Summer Dress',\n      brand: 'Ethereal',\n      price: 139.99,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400'\n      }],\n      rating: {\n        average: 4.4,\n        count: 128\n      },\n      analytics: {\n        views: 9870,\n        likes: 743\n      }\n    }, {\n      _id: '6',\n      name: 'Leather Ankle Boots',\n      brand: 'Urban Threads',\n      price: 199.99,\n      originalPrice: 249.99,\n      discount: 20,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400'\n      }],\n      rating: {\n        average: 4.7,\n        count: 89\n      },\n      analytics: {\n        views: 15600,\n        likes: 1123\n      }\n    }, {\n      _id: '7',\n      name: 'Oversized Blazer',\n      brand: 'Ethereal',\n      price: 189.99,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=400'\n      }],\n      rating: {\n        average: 4.1,\n        count: 76\n      },\n      analytics: {\n        views: 7890,\n        likes: 456\n      }\n    }, {\n      _id: '8',\n      name: 'Casual T-Shirt',\n      brand: 'Cozy Co',\n      price: 29.99,\n      originalPrice: 39.99,\n      discount: 25,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400'\n      }],\n      rating: {\n        average: 4.0,\n        count: 234\n      },\n      analytics: {\n        views: 12340,\n        likes: 678\n      }\n    }];\n  }\n  loadFallbackData() {\n    this.featuredProducts = this.getFallbackProducts();\n    this.trendingProducts = this.getFallbackProducts();\n    this.newArrivals = this.getFallbackProducts();\n  }\n  // Navigation methods\n  onProductClick(product) {\n    this.router.navigate(['/product', product._id || product.id]);\n  }\n  onCategoryClick(category) {\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        category: category.name.toLowerCase()\n      }\n    });\n  }\n  viewAllCategories() {\n    this.router.navigate(['/shop']);\n  }\n  // Instagram-style interaction methods\n  toggleLike(post) {\n    post.isLiked = !post.isLiked;\n    post.likes += post.isLiked ? 1 : -1;\n  }\n  toggleSave(post) {\n    post.isSaved = !post.isSaved;\n  }\n  sharePost(post) {\n    // Implement share functionality\n    console.log('Sharing post:', post.id);\n  }\n  focusCommentInput(post) {\n    // Focus on comment input for this post\n    console.log('Focus comment for post:', post.id);\n  }\n  addComment(post) {\n    if (this.newComment && this.newComment.trim()) {\n      if (!post.comments) {\n        post.comments = [];\n      }\n      post.comments.push({\n        username: this.currentUser?.username || 'user',\n        text: this.newComment.trim()\n      });\n      this.newComment = '';\n    }\n  }\n  showProductDetails(product) {\n    console.log('Show product details:', product);\n    // Toggle product preview\n    product.showPreview = !product.showPreview;\n  }\n  // E-commerce methods\n  buyNow(product) {\n    console.log('Buy now:', product);\n    if (!this.currentUser) {\n      this.router.navigate(['/auth/login'], {\n        queryParams: {\n          returnUrl: '/home'\n        }\n      });\n      return;\n    }\n    // Prepare payment modal data for single product purchase\n    this.paymentModalData = {\n      amount: product.price,\n      orderData: {\n        items: [{\n          id: product.id,\n          name: product.name,\n          price: product.price,\n          quantity: 1,\n          image: product.image\n        }],\n        subtotal: product.price,\n        tax: product.price * 0.18,\n        shipping: 0,\n        discount: 0,\n        total: product.price + product.price * 0.18\n      },\n      userDetails: {\n        name: this.currentUser.fullName || this.currentUser.username,\n        email: this.currentUser.email,\n        phone: this.currentUser.phone || ''\n      }\n    };\n    this.showPaymentModal = true;\n  }\n  addToWishlist(product) {\n    console.log('Add to wishlist:', product);\n    // Implement wishlist functionality\n  }\n  addToCart(product) {\n    console.log('Add to cart:', product);\n    // Implement cart functionality\n  }\n  followUser(user) {\n    console.log('Follow user:', user.username);\n    // Implement follow functionality\n  }\n  viewSummerCollection() {\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        collection: 'summer2024'\n      }\n    });\n  }\n  toggleComments(post) {\n    console.log('Toggle comments for post:', post.id);\n    // Implement comments toggle\n  }\n  // Utility methods\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatLikesCount(likes) {\n    return this.formatNumber(likes);\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;\n    return `${Math.floor(diffInSeconds / 604800)}w`;\n  }\n  trackByPostId(index, post) {\n    return post.id;\n  }\n  // Payment Modal handlers\n  onPaymentCompleted(paymentResult) {\n    this.showPaymentModal = false;\n    if (paymentResult.status === 'success') {\n      // Navigate to success page\n      this.router.navigate(['/payment-success'], {\n        queryParams: {\n          orderId: paymentResult.orderId || this.generateOrderId(),\n          method: paymentResult.method\n        }\n      });\n    } else {\n      // Show error message\n      alert('Payment failed. Please try again.');\n    }\n  }\n  onPaymentModalClose() {\n    this.showPaymentModal = false;\n  }\n  generateOrderId() {\n    return 'ORD' + Date.now().toString();\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 4,\n      consts: [[1, \"instagram-home-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"instagram-layout\", 4, \"ngIf\"], [3, \"close\", \"paymentCompleted\", \"isVisible\", \"paymentData\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"instagram-layout\"], [1, \"main-feed\"], [1, \"stories-section\"], [1, \"posts-feed\"], [\"class\", \"instagram-post\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"instagram-sidebar\"], [\"class\", \"profile-card\", 4, \"ngIf\"], [1, \"sidebar-section\"], [1, \"section-header\"], [1, \"see-all-btn\", 3, \"click\"], [1, \"collection-items\"], [\"class\", \"collection-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"routerLink\", \"/shop?filter=featured\", 1, \"see-all-btn\"], [1, \"featured-items\"], [\"class\", \"featured-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"routerLink\", \"/shop?filter=trending\", 1, \"see-all-btn\"], [1, \"trending-items\"], [\"class\", \"trending-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"routerLink\", \"/shop?filter=new\", 1, \"see-all-btn\"], [1, \"arrivals-items\"], [\"class\", \"arrival-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"see-all-btn\"], [1, \"suggested-users\"], [\"class\", \"suggested-user\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-footer\"], [1, \"footer-links\"], [\"href\", \"#\"], [1, \"copyright\"], [1, \"instagram-post\"], [1, \"post-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"username\"], [\"class\", \"location\", 4, \"ngIf\"], [1, \"more-options\"], [1, \"fas\", \"fa-ellipsis-h\"], [1, \"post-media\"], [1, \"post-image\", 3, \"src\", \"alt\"], [\"class\", \"product-tags-overlay\", 4, \"ngIf\"], [1, \"post-actions\"], [1, \"primary-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [1, \"far\", \"fa-comment\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [1, \"far\", \"fa-paper-plane\"], [1, \"action-btn\", \"save-btn\", 3, \"click\"], [\"class\", \"likes-section\", 4, \"ngIf\"], [1, \"post-caption\"], [1, \"caption-text\"], [\"class\", \"ecommerce-actions\", 4, \"ngIf\"], [\"class\", \"comments-preview\", 4, \"ngIf\"], [1, \"post-time\"], [1, \"add-comment-section\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 1, \"comment-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"post-comment-btn\", 3, \"click\", \"disabled\"], [1, \"location\"], [1, \"product-tags-overlay\"], [\"class\", \"product-tag\", 3, \"left\", \"top\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"tag-dot\"], [\"class\", \"product-preview\", 4, \"ngIf\"], [1, \"product-preview\"], [3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"likes-section\"], [1, \"likes-count\"], [1, \"ecommerce-actions\"], [1, \"product-showcase\"], [\"class\", \"featured-product\", 4, \"ngFor\", \"ngForOf\"], [1, \"shopping-buttons\"], [1, \"shop-btn\", \"buy-btn\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [1, \"shop-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"far\", \"fa-heart\"], [1, \"shop-btn\", \"cart-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"featured-product\"], [1, \"product-thumbnail\", 3, \"src\", \"alt\"], [1, \"product-details\"], [1, \"comments-preview\"], [1, \"view-comments-btn\", 3, \"click\"], [1, \"recent-comments\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [1, \"comment\"], [1, \"comment-username\"], [1, \"comment-text\"], [1, \"profile-card\"], [1, \"profile-avatar\", 3, \"src\", \"alt\"], [1, \"profile-info\"], [1, \"profile-username\"], [1, \"profile-name\"], [1, \"switch-btn\"], [1, \"collection-item\", 3, \"click\"], [1, \"collection-image\", 3, \"src\", \"alt\"], [1, \"collection-info\"], [1, \"collection-name\"], [1, \"collection-price\"], [1, \"featured-item\", 3, \"click\"], [1, \"featured-image\", 3, \"src\", \"alt\"], [1, \"featured-info\"], [1, \"featured-name\"], [1, \"featured-brand\"], [1, \"featured-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"original-price\"], [1, \"trending-item\", 3, \"click\"], [1, \"trending-image\", 3, \"src\", \"alt\"], [1, \"trending-badge\"], [1, \"fas\", \"fa-fire\"], [1, \"trending-info\"], [1, \"trending-name\"], [1, \"trending-stats\"], [1, \"fas\", \"fa-eye\"], [1, \"fas\", \"fa-heart\"], [1, \"trending-price\"], [1, \"arrival-item\", 3, \"click\"], [1, \"arrival-image\", 3, \"src\", \"alt\"], [1, \"new-badge\"], [1, \"arrival-info\"], [1, \"arrival-name\"], [1, \"arrival-brand\"], [1, \"arrival-price\"], [1, \"suggested-user\"], [1, \"suggested-avatar\", 3, \"src\", \"alt\"], [1, \"suggested-info\"], [1, \"suggested-username\"], [1, \"suggested-followers\"], [1, \"follow-btn\", 3, \"click\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, HomeComponent_div_1_Template, 4, 0, \"div\", 1)(2, HomeComponent_div_2_Template, 67, 8, \"div\", 2);\n          i0.ɵɵelementStart(3, \"app-payment-modal\", 3);\n          i0.ɵɵlistener(\"close\", function HomeComponent_Template_app_payment_modal_close_3_listener() {\n            return ctx.onPaymentModalClose();\n          })(\"paymentCompleted\", function HomeComponent_Template_app_payment_modal_paymentCompleted_3_listener($event) {\n            return ctx.onPaymentCompleted($event);\n          });\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"isVisible\", ctx.showPaymentModal)(\"paymentData\", ctx.paymentModalData);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, FormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, RouterModule, i1.RouterLink, ViewAddStoriesComponent, PaymentModalComponent],\n      styles: [\".instagram-home-container[_ngcontent-%COMP%] {\\n  min-height: calc(100vh - 80px);\\n  background: #fafafa;\\n  padding-top: 20px;\\n}\\n\\n.instagram-layout[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 320px;\\n  gap: 30px;\\n  max-width: 975px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n@media (max-width: 1024px) {\\n  .instagram-layout[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    max-width: 614px;\\n    gap: 0;\\n    padding: 0;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .instagram-layout[_ngcontent-%COMP%] {\\n    padding: 0;\\n    gap: 0;\\n  }\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 200px;\\n  gap: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 3px solid #e3e3e3;\\n  border-top: 3px solid #667eea;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 14px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.main-feed[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n  width: 100%;\\n  max-width: 614px;\\n}\\n\\n.stories-section[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  margin-bottom: 24px;\\n}\\n.stories-section[_ngcontent-%COMP%]     .stories-container {\\n  margin: 0;\\n  padding: 16px;\\n  background: white;\\n  border-radius: 0;\\n  box-shadow: none;\\n  border: none;\\n  -webkit-backdrop-filter: none;\\n          backdrop-filter: none;\\n}\\n.stories-section[_ngcontent-%COMP%]     .stories-header {\\n  display: none;\\n}\\n.stories-section[_ngcontent-%COMP%]     .stories-slider-wrapper {\\n  gap: 8px;\\n}\\n.stories-section[_ngcontent-%COMP%]     .nav-arrow {\\n  display: none;\\n}\\n.stories-section[_ngcontent-%COMP%]     .story-item {\\n  margin: 0 4px;\\n}\\n.stories-section[_ngcontent-%COMP%]     .story-avatar {\\n  width: 56px;\\n  height: 56px;\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%]     .story-avatar {\\n    width: 56px;\\n    height: 56px;\\n  }\\n}\\n.stories-section[_ngcontent-%COMP%]     .story-avatar-inner {\\n  width: 50px;\\n  height: 50px;\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%]     .story-avatar-inner {\\n    width: 50px;\\n    height: 50px;\\n  }\\n}\\n.stories-section[_ngcontent-%COMP%]     .story-username {\\n  font-size: 12px;\\n  color: #262626;\\n  text-shadow: none;\\n  max-width: 64px;\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%]     .story-username {\\n    max-width: 64px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%] {\\n    border-radius: 0;\\n    border-left: none;\\n    border-right: none;\\n    margin-bottom: 0;\\n  }\\n}\\n\\n.posts-feed[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.instagram-post[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  margin-bottom: 24px;\\n  max-width: 614px;\\n  width: 100%;\\n}\\n@media (max-width: 768px) {\\n  .instagram-post[_ngcontent-%COMP%] {\\n    border-radius: 0;\\n    border-left: none;\\n    border-right: none;\\n    margin-bottom: 0;\\n    border-bottom: none;\\n  }\\n}\\n\\n.post-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 16px;\\n}\\n.post-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.post-header[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.post-header[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 0;\\n  color: #262626;\\n}\\n.post-header[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   .location[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n}\\n.post-header[_ngcontent-%COMP%]   .more-options[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px;\\n  color: #262626;\\n}\\n.post-header[_ngcontent-%COMP%]   .more-options[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n\\n.post-media[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  aspect-ratio: 1;\\n  overflow: hidden;\\n}\\n.post-media[_ngcontent-%COMP%]   .post-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  display: block;\\n}\\n\\n.product-tags-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: all;\\n  cursor: pointer;\\n  z-index: 5;\\n}\\n.product-tag[_ngcontent-%COMP%]   .tag-dot[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background: white;\\n  border-radius: 50%;\\n  border: 2px solid #262626;\\n  position: relative;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.product-tag[_ngcontent-%COMP%]   .tag-dot[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 6px;\\n  height: 6px;\\n  background: #262626;\\n  border-radius: 50%;\\n}\\n.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 30px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(0, 0, 0, 0.8);\\n  border-radius: 8px;\\n  padding: 8px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 200px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 4px;\\n  object-fit: cover;\\n}\\n.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #f5f5f5;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n.post-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px 16px 8px;\\n}\\n.post-actions[_ngcontent-%COMP%]   .primary-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n.post-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px;\\n  font-size: 24px;\\n  color: #262626;\\n  transition: all 0.3s ease;\\n}\\n.post-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n.post-actions[_ngcontent-%COMP%]   .action-btn.liked[_ngcontent-%COMP%] {\\n  color: #ed4956;\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease;\\n}\\n.post-actions[_ngcontent-%COMP%]   .action-btn.saved[_ngcontent-%COMP%] {\\n  color: #262626;\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.2);\\n  }\\n}\\n.likes-section[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n}\\n.likes-section[_ngcontent-%COMP%]   .likes-count[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #262626;\\n}\\n\\n.post-caption[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n.post-caption[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #262626;\\n  margin-right: 8px;\\n}\\n.post-caption[_ngcontent-%COMP%]   .caption-text[_ngcontent-%COMP%] {\\n  color: #262626;\\n}\\n\\n.ecommerce-actions[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border-top: 1px solid #efefef;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 12px;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  flex: 1;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 6px;\\n  object-fit: cover;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: #262626;\\n  line-height: 1.2;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 8px 16px;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 4px;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.buy-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  color: white;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.buy-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.wishlist-btn[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  color: #262626;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n  border-color: #c6c6c6;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.cart-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n  color: white;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(240, 147, 251, 0.4);\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n}\\n\\n.comments-preview[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n}\\n.comments-preview[_ngcontent-%COMP%]   .view-comments-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #8e8e8e;\\n  font-size: 14px;\\n  cursor: pointer;\\n  margin-bottom: 4px;\\n}\\n.comments-preview[_ngcontent-%COMP%]   .view-comments-btn[_ngcontent-%COMP%]:hover {\\n  color: #262626;\\n}\\n.comments-preview[_ngcontent-%COMP%]   .recent-comments[_ngcontent-%COMP%]   .comment[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.4;\\n  margin-bottom: 2px;\\n}\\n.comments-preview[_ngcontent-%COMP%]   .recent-comments[_ngcontent-%COMP%]   .comment[_ngcontent-%COMP%]   .comment-username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #262626;\\n  margin-right: 8px;\\n}\\n.comments-preview[_ngcontent-%COMP%]   .recent-comments[_ngcontent-%COMP%]   .comment[_ngcontent-%COMP%]   .comment-text[_ngcontent-%COMP%] {\\n  color: #262626;\\n}\\n\\n.post-time[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n  font-size: 10px;\\n  color: #8e8e8e;\\n  text-transform: uppercase;\\n  letter-spacing: 0.2px;\\n}\\n\\n.add-comment-section[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  border-top: 1px solid #efefef;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.add-comment-section[_ngcontent-%COMP%]   .comment-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  outline: none;\\n  font-size: 14px;\\n  color: #262626;\\n}\\n.add-comment-section[_ngcontent-%COMP%]   .comment-input[_ngcontent-%COMP%]::placeholder {\\n  color: #8e8e8e;\\n}\\n.add-comment-section[_ngcontent-%COMP%]   .post-comment-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #0095f6;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n}\\n.add-comment-section[_ngcontent-%COMP%]   .post-comment-btn[_ngcontent-%COMP%]:disabled {\\n  color: #c7c7c7;\\n  cursor: not-allowed;\\n}\\n.add-comment-section[_ngcontent-%COMP%]   .post-comment-btn[_ngcontent-%COMP%]:not(:disabled):hover {\\n  color: #00376b;\\n}\\n\\n.instagram-sidebar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 84px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  padding-left: 0;\\n}\\n@media (max-width: 1024px) {\\n  .instagram-sidebar[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n.profile-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 16px 0 24px 0;\\n  margin-bottom: 8px;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-username[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #262626;\\n  margin: 0 0 2px 0;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #8e8e8e;\\n}\\n.profile-card[_ngcontent-%COMP%]   .switch-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #0095f6;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n}\\n.profile-card[_ngcontent-%COMP%]   .switch-btn[_ngcontent-%COMP%]:hover {\\n  color: #00376b;\\n}\\n\\n.sidebar-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #8e8e8e;\\n  margin: 0;\\n  text-transform: none;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #262626;\\n  font-size: 12px;\\n  font-weight: 400;\\n  cursor: pointer;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n\\n.collection-items[_ngcontent-%COMP%], .featured-items[_ngcontent-%COMP%], .trending-items[_ngcontent-%COMP%], .arrivals-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.collection-item[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 4px 0;\\n  border-radius: 3px;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n}\\n.collection-item[_ngcontent-%COMP%]:hover, .featured-item[_ngcontent-%COMP%]:hover, .trending-item[_ngcontent-%COMP%]:hover, .arrival-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.05);\\n}\\n.collection-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border-radius: 3px;\\n  object-fit: cover;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #262626;\\n  line-height: 1.2;\\n  margin-bottom: 2px;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  color: #8e8e8e;\\n  margin-bottom: 2px;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  text-decoration: line-through;\\n  font-size: 10px;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-bottom: 2px;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 2px;\\n  font-size: 10px;\\n  color: #8e8e8e;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 8px;\\n}\\n\\n.trending-badge[_ngcontent-%COMP%], .new-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 4px;\\n  right: 4px;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  font-size: 8px;\\n  font-weight: 600;\\n  color: white;\\n}\\n\\n.trending-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b, #ffa500);\\n  display: flex;\\n  align-items: center;\\n  gap: 2px;\\n}\\n.trending-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 6px;\\n}\\n\\n.new-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\\n}\\n\\n.trending-item[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.suggested-users[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.suggested-user[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 8px;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .suggested-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .suggested-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .suggested-info[_ngcontent-%COMP%]   .suggested-username[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #262626;\\n  line-height: 1.2;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .suggested-info[_ngcontent-%COMP%]   .suggested-followers[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n  background: #0095f6;\\n  border: none;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background-color 0.3s ease;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover {\\n  background: #00376b;\\n}\\n\\n.sidebar-footer[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n  padding-top: 16px;\\n  border-top: 1px solid #efefef;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #c7c7c7;\\n  text-decoration: none;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .copyright[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #c7c7c7;\\n}\\n\\n@media (max-width: 768px) {\\n  .instagram-home-container[_ngcontent-%COMP%] {\\n    padding-top: 0;\\n  }\\n  .instagram-layout[_ngcontent-%COMP%] {\\n    padding: 0;\\n    max-width: 100%;\\n  }\\n  .main-feed[_ngcontent-%COMP%] {\\n    gap: 0;\\n  }\\n  .instagram-post[_ngcontent-%COMP%] {\\n    margin-bottom: 0;\\n    border-radius: 0;\\n    border-left: none;\\n    border-right: none;\\n    max-width: 100%;\\n  }\\n  .instagram-post[_ngcontent-%COMP%]:last-child {\\n    border-bottom: 1px solid #dbdbdb;\\n  }\\n  .stories-section[_ngcontent-%COMP%] {\\n    border-radius: 0;\\n    border-left: none;\\n    border-right: none;\\n    margin-bottom: 0;\\n    border-bottom: 1px solid #dbdbdb;\\n  }\\n  .post-header[_ngcontent-%COMP%] {\\n    padding: 14px 16px;\\n  }\\n  .post-actions[_ngcontent-%COMP%] {\\n    padding: 6px 16px 4px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 12px;\\n  }\\n  .add-comment-section[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .likes-section[_ngcontent-%COMP%], .post-caption[_ngcontent-%COMP%], .comments-preview[_ngcontent-%COMP%], .post-time[_ngcontent-%COMP%] {\\n    padding-left: 16px;\\n    padding-right: 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 6px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%] {\\n    padding: 8px 16px;\\n    font-size: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaG9tZS9wYWdlcy9ob21lL2hvbWUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0E7RUFDRSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsaUJBQUE7QUFBRjs7QUFHQTtFQUNFLGFBQUE7RUFDQSxnQ0FBQTtFQUNBLFNBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0FBQUY7QUFFRTtFQVJGO0lBU0ksMEJBQUE7SUFDQSxnQkFBQTtJQUNBLE1BQUE7SUFDQSxVQUFBO0VBQ0Y7QUFDRjtBQUNFO0VBZkY7SUFnQkksVUFBQTtJQUNBLE1BQUE7RUFFRjtBQUNGOztBQUVBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGFBQUE7RUFDQSxTQUFBO0FBQ0Y7QUFDRTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EseUJBQUE7RUFDQSw2QkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0NBQUE7QUFDSjtBQUVFO0VBQ0UsY0FBQTtFQUNBLGVBQUE7QUFBSjs7QUFJQTtFQUNFO0lBQUssdUJBQUE7RUFBTDtFQUNBO0lBQU8seUJBQUE7RUFFUDtBQUNGO0FBQ0E7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0VBQ0EsV0FBQTtFQUNBLGdCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxpQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0FBQ0Y7QUFHSTtFQUNFLFNBQUE7RUFDQSxhQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLDZCQUFBO1VBQUEscUJBQUE7QUFETjtBQUlJO0VBQ0UsYUFBQTtBQUZOO0FBS0k7RUFDRSxRQUFBO0FBSE47QUFNSTtFQUNFLGFBQUE7QUFKTjtBQU9JO0VBQ0UsYUFBQTtBQUxOO0FBUUk7RUFDRSxXQUFBO0VBQ0EsWUFBQTtBQU5OO0FBUU07RUFKRjtJQUtJLFdBQUE7SUFDQSxZQUFBO0VBTE47QUFDRjtBQVFJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7QUFOTjtBQVFNO0VBSkY7SUFLSSxXQUFBO0lBQ0EsWUFBQTtFQUxOO0FBQ0Y7QUFRSTtFQUNFLGVBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSxlQUFBO0FBTk47QUFRTTtFQU5GO0lBT0ksZUFBQTtFQUxOO0FBQ0Y7QUFTRTtFQW5FRjtJQW9FSSxnQkFBQTtJQUNBLGlCQUFBO0lBQ0Esa0JBQUE7SUFDQSxnQkFBQTtFQU5GO0FBQ0Y7O0FBU0E7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0FBTkY7O0FBVUE7RUFDRSxpQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxXQUFBO0FBUEY7QUFTRTtFQVRGO0lBVUksZ0JBQUE7SUFDQSxpQkFBQTtJQUNBLGtCQUFBO0lBQ0EsZ0JBQUE7SUFDQSxtQkFBQTtFQU5GO0FBQ0Y7O0FBU0E7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSw4QkFBQTtFQUNBLGFBQUE7QUFORjtBQVFFO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtBQU5KO0FBU0U7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7QUFQSjtBQVdJO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsU0FBQTtFQUNBLGNBQUE7QUFUTjtBQVlJO0VBQ0UsZUFBQTtFQUNBLGNBQUE7QUFWTjtBQWNFO0VBQ0UsZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0FBWko7QUFjSTtFQUNFLGNBQUE7QUFaTjs7QUFpQkE7RUFDRSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7QUFkRjtBQWdCRTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSxjQUFBO0FBZEo7O0FBbUJBO0VBQ0Usa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0Esb0JBQUE7QUFoQkY7O0FBbUJBO0VBQ0Usa0JBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxVQUFBO0FBaEJGO0FBa0JFO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLDRCQUFBO0FBaEJKO0FBa0JJO0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxnQ0FBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtBQWhCTjtBQW9CRTtFQUNFLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLFNBQUE7RUFDQSwyQkFBQTtFQUNBLDhCQUFBO0VBQ0Esa0JBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtFQUNBLGdCQUFBO0VBQ0EsbUNBQUE7VUFBQSwyQkFBQTtBQWxCSjtBQW9CSTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtBQWxCTjtBQXFCSTtFQUNFLFlBQUE7QUFuQk47QUFxQk07RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0FBbkJSO0FBc0JNO0VBQ0UsY0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUFwQlI7O0FBMEJBO0VBQ0U7SUFBVyxtQkFBQTtFQXRCWDtFQXVCQTtJQUFNLHFCQUFBO0VBcEJOO0FBQ0Y7QUF1QkE7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLHNCQUFBO0FBckJGO0FBdUJFO0VBQ0UsYUFBQTtFQUNBLFNBQUE7QUFyQko7QUF3QkU7RUFDRSxnQkFBQTtFQUNBLFlBQUE7RUFDQSxlQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7QUF0Qko7QUF3Qkk7RUFDRSxjQUFBO0FBdEJOO0FBeUJJO0VBQ0UsY0FBQTtFQUNBLDhCQUFBO0FBdkJOO0FBMEJJO0VBQ0UsY0FBQTtBQXhCTjs7QUE2QkE7RUFDRTtJQUFXLG1CQUFBO0VBekJYO0VBMEJBO0lBQU0scUJBQUE7RUF2Qk47QUFDRjtBQTBCQTtFQUNFLG1CQUFBO0FBeEJGO0FBMEJFO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtBQXhCSjs7QUE0QkE7RUFDRSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtBQXpCRjtBQTJCRTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGlCQUFBO0FBekJKO0FBNEJFO0VBQ0UsY0FBQTtBQTFCSjs7QUErQkE7RUFDRSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EsNkJBQUE7QUE1QkY7QUE4QkU7RUFDRSxhQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0FBNUJKO0FBOEJJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtFQUNBLE9BQUE7QUE1Qk47QUE4Qk07RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7QUE1QlI7QUErQk07RUFDRSxPQUFBO0FBN0JSO0FBK0JRO0VBQ0UsY0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtBQTdCVjtBQWdDUTtFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FBOUJWO0FBb0NFO0VBQ0UsYUFBQTtFQUNBLFFBQUE7QUFsQ0o7QUFvQ0k7RUFDRSxPQUFBO0VBQ0EsaUJBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0VBQ0EseUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFFBQUE7QUFsQ047QUFvQ007RUFDRSxxREFBQTtFQUNBLFlBQUE7QUFsQ1I7QUFvQ1E7RUFDRSwyQkFBQTtFQUNBLCtDQUFBO0FBbENWO0FBc0NNO0VBQ0UsaUJBQUE7RUFDQSx5QkFBQTtFQUNBLGNBQUE7QUFwQ1I7QUFzQ1E7RUFDRSxtQkFBQTtFQUNBLHFCQUFBO0FBcENWO0FBd0NNO0VBQ0UscURBQUE7RUFDQSxZQUFBO0FBdENSO0FBd0NRO0VBQ0UsMkJBQUE7RUFDQSwrQ0FBQTtBQXRDVjtBQTBDTTtFQUNFLGVBQUE7QUF4Q1I7O0FBK0NBO0VBQ0UsbUJBQUE7QUE1Q0Y7QUE4Q0U7RUFDRSxnQkFBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7RUFDQSxrQkFBQTtBQTVDSjtBQThDSTtFQUNFLGNBQUE7QUE1Q047QUFpREk7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtBQS9DTjtBQWlETTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGlCQUFBO0FBL0NSO0FBa0RNO0VBQ0UsY0FBQTtBQWhEUjs7QUFzREE7RUFDRSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0VBQ0EseUJBQUE7RUFDQSxxQkFBQTtBQW5ERjs7QUFzREE7RUFDRSxrQkFBQTtFQUNBLDZCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtBQW5ERjtBQXFERTtFQUNFLE9BQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0FBbkRKO0FBcURJO0VBQ0UsY0FBQTtBQW5ETjtBQXVERTtFQUNFLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0FBckRKO0FBdURJO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0FBckROO0FBd0RJO0VBQ0UsY0FBQTtBQXRETjs7QUE0REE7RUFDRSxnQkFBQTtFQUNBLFNBQUE7RUFDQSx3QkFBQTtFQUFBLG1CQUFBO0VBQ0EsZUFBQTtBQXpERjtBQTJERTtFQU5GO0lBT0ksYUFBQTtFQXhERjtBQUNGOztBQTJEQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxzQkFBQTtFQUNBLGtCQUFBO0FBeERGO0FBMERFO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0FBeERKO0FBMkRFO0VBQ0UsT0FBQTtBQXpESjtBQTJESTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxpQkFBQTtBQXpETjtBQTRESTtFQUNFLGVBQUE7RUFDQSxjQUFBO0FBMUROO0FBOERFO0VBQ0UsZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7QUE1REo7QUE4REk7RUFDRSxjQUFBO0FBNUROOztBQWlFQTtFQUNFLG1CQUFBO0FBOURGO0FBZ0VFO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtBQTlESjtBQWdFSTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxTQUFBO0VBQ0Esb0JBQUE7QUE5RE47QUFpRUk7RUFDRSxnQkFBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtBQS9ETjtBQWlFTTtFQUNFLGNBQUE7QUEvRFI7O0FBc0VBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsUUFBQTtBQW5FRjs7QUFzRUE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLHNDQUFBO0FBbkVGO0FBcUVFO0VBQ0UsK0JBQUE7QUFuRUo7QUFzRUU7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7QUFwRUo7QUF1RUU7RUFDRSxPQUFBO0FBckVKO0FBdUVJO0VBQ0UsY0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0FBckVOO0FBd0VJO0VBQ0UsY0FBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUF0RU47QUF5RUk7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FBdkVOO0FBMEVJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtBQXhFTjtBQTBFTTtFQUNFLGNBQUE7QUF4RVI7QUEyRU07RUFDRSxjQUFBO0VBQ0EsNkJBQUE7RUFDQSxlQUFBO0FBekVSO0FBNkVJO0VBQ0UsYUFBQTtFQUNBLFFBQUE7RUFDQSxrQkFBQTtBQTNFTjtBQTZFTTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxlQUFBO0VBQ0EsY0FBQTtBQTNFUjtBQTZFUTtFQUNFLGNBQUE7QUEzRVY7O0FBbUZBO0VBQ0Usa0JBQUE7RUFDQSxRQUFBO0VBQ0EsVUFBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxZQUFBO0FBaEZGOztBQW1GQTtFQUNFLHFEQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtBQWhGRjtBQWtGRTtFQUNFLGNBQUE7QUFoRko7O0FBb0ZBO0VBQ0UscURBQUE7QUFqRkY7O0FBb0ZBO0VBQ0Usa0JBQUE7QUFqRkY7O0FBcUZBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsUUFBQTtBQWxGRjs7QUFxRkE7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsWUFBQTtBQWxGRjtBQW9GRTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtBQWxGSjtBQXFGRTtFQUNFLE9BQUE7QUFuRko7QUFxRkk7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBbkZOO0FBc0ZJO0VBQ0UsZUFBQTtFQUNBLGNBQUE7QUFwRk47QUF3RkU7RUFDRSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxzQ0FBQTtBQXRGSjtBQXdGSTtFQUNFLG1CQUFBO0FBdEZOOztBQTRGQTtFQUNFLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSw2QkFBQTtBQXpGRjtBQTJGRTtFQUNFLGFBQUE7RUFDQSxlQUFBO0VBQ0EsUUFBQTtFQUNBLG1CQUFBO0FBekZKO0FBMkZJO0VBQ0UsZUFBQTtFQUNBLGNBQUE7RUFDQSxxQkFBQTtBQXpGTjtBQTJGTTtFQUNFLDBCQUFBO0FBekZSO0FBOEZFO0VBQ0UsZUFBQTtFQUNBLGNBQUE7QUE1Rko7O0FBaUdBO0VBQ0U7SUFDRSxjQUFBO0VBOUZGO0VBaUdBO0lBQ0UsVUFBQTtJQUNBLGVBQUE7RUEvRkY7RUFrR0E7SUFDRSxNQUFBO0VBaEdGO0VBbUdBO0lBQ0UsZ0JBQUE7SUFDQSxnQkFBQTtJQUNBLGlCQUFBO0lBQ0Esa0JBQUE7SUFDQSxlQUFBO0VBakdGO0VBbUdFO0lBQ0UsZ0NBQUE7RUFqR0o7RUFxR0E7SUFDRSxnQkFBQTtJQUNBLGlCQUFBO0lBQ0Esa0JBQUE7SUFDQSxnQkFBQTtJQUNBLGdDQUFBO0VBbkdGO0VBc0dBO0lBQ0Usa0JBQUE7RUFwR0Y7RUF1R0E7SUFDRSxxQkFBQTtFQXJHRjtFQXdHQTtJQUNFLGtCQUFBO0VBdEdGO0VBeUdJO0lBQ0UsaUJBQUE7SUFDQSxlQUFBO0VBdkdOO0VBNEdBO0lBQ0Usa0JBQUE7RUExR0Y7RUE2R0E7SUFDRSxrQkFBQTtJQUNBLG1CQUFBO0VBM0dGO0FBQ0Y7QUE4R0E7RUFFSTtJQUNFLHNCQUFBO0lBQ0EsUUFBQTtFQTdHSjtFQWdITTtJQUNFLFdBQUE7SUFDQSxZQUFBO0VBOUdSO0VBbUhFO0lBQ0Usc0JBQUE7SUFDQSxRQUFBO0VBakhKO0VBbUhJO0lBQ0UsaUJBQUE7SUFDQSxlQUFBO0VBakhOO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIvLyBJbnN0YWdyYW0tc3R5bGUgSG9tZSBMYXlvdXRcbi5pbnN0YWdyYW0taG9tZS1jb250YWluZXIge1xuICBtaW4taGVpZ2h0OiBjYWxjKDEwMHZoIC0gODBweCk7XG4gIGJhY2tncm91bmQ6ICNmYWZhZmE7XG4gIHBhZGRpbmctdG9wOiAyMHB4O1xufVxuXG4uaW5zdGFncmFtLWxheW91dCB7XG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyIDMyMHB4O1xuICBnYXA6IDMwcHg7XG4gIG1heC13aWR0aDogOTc1cHg7XG4gIG1hcmdpbjogMCBhdXRvO1xuICBwYWRkaW5nOiAwIDIwcHg7XG5cbiAgQG1lZGlhIChtYXgtd2lkdGg6IDEwMjRweCkge1xuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICAgIG1heC13aWR0aDogNjE0cHg7XG4gICAgZ2FwOiAwO1xuICAgIHBhZGRpbmc6IDA7XG4gIH1cblxuICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICBwYWRkaW5nOiAwO1xuICAgIGdhcDogMDtcbiAgfVxufVxuXG4vLyBMb2FkaW5nIFN0YXRlXG4ubG9hZGluZy1jb250YWluZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgaGVpZ2h0OiAyMDBweDtcbiAgZ2FwOiAxNnB4O1xuXG4gIC5sb2FkaW5nLXNwaW5uZXIge1xuICAgIHdpZHRoOiA0MHB4O1xuICAgIGhlaWdodDogNDBweDtcbiAgICBib3JkZXI6IDNweCBzb2xpZCAjZTNlM2UzO1xuICAgIGJvcmRlci10b3A6IDNweCBzb2xpZCAjNjY3ZWVhO1xuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xuICB9XG5cbiAgcCB7XG4gICAgY29sb3I6ICM2Yzc1N2Q7XG4gICAgZm9udC1zaXplOiAxNHB4O1xuICB9XG59XG5cbkBrZXlmcmFtZXMgc3BpbiB7XG4gIDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7IH1cbiAgMTAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7IH1cbn1cblxuLy8gTWFpbiBGZWVkIFN0eWxlc1xuLm1haW4tZmVlZCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogMjRweDtcbiAgd2lkdGg6IDEwMCU7XG4gIG1heC13aWR0aDogNjE0cHg7XG59XG5cbi5zdG9yaWVzLXNlY3Rpb24ge1xuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgYm9yZGVyOiAxcHggc29saWQgI2RiZGJkYjtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBtYXJnaW4tYm90dG9tOiAyNHB4O1xuXG4gIC8vIE92ZXJyaWRlIHRoZSBzdG9yaWVzIGNvbXBvbmVudCBzdHlsZXMgdG8gbWFrZSBpdCBtb3JlIGNvbXBhY3RcbiAgOjpuZy1kZWVwIHtcbiAgICAuc3Rvcmllcy1jb250YWluZXIge1xuICAgICAgbWFyZ2luOiAwO1xuICAgICAgcGFkZGluZzogMTZweDtcbiAgICAgIGJhY2tncm91bmQ6IHdoaXRlO1xuICAgICAgYm9yZGVyLXJhZGl1czogMDtcbiAgICAgIGJveC1zaGFkb3c6IG5vbmU7XG4gICAgICBib3JkZXI6IG5vbmU7XG4gICAgICBiYWNrZHJvcC1maWx0ZXI6IG5vbmU7XG4gICAgfVxuXG4gICAgLnN0b3JpZXMtaGVhZGVyIHtcbiAgICAgIGRpc3BsYXk6IG5vbmU7IC8vIEhpZGUgdGhlIGhlYWRlciB0byBtYWtlIGl0IG1vcmUgSW5zdGFncmFtLWxpa2VcbiAgICB9XG5cbiAgICAuc3Rvcmllcy1zbGlkZXItd3JhcHBlciB7XG4gICAgICBnYXA6IDhweDtcbiAgICB9XG5cbiAgICAubmF2LWFycm93IHtcbiAgICAgIGRpc3BsYXk6IG5vbmU7IC8vIEhpZGUgYXJyb3dzIGZvciBjbGVhbmVyIGxvb2tcbiAgICB9XG5cbiAgICAuc3RvcnktaXRlbSB7XG4gICAgICBtYXJnaW46IDAgNHB4O1xuICAgIH1cblxuICAgIC5zdG9yeS1hdmF0YXIge1xuICAgICAgd2lkdGg6IDU2cHg7XG4gICAgICBoZWlnaHQ6IDU2cHg7XG5cbiAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgICAgICB3aWR0aDogNTZweDtcbiAgICAgICAgaGVpZ2h0OiA1NnB4O1xuICAgICAgfVxuICAgIH1cblxuICAgIC5zdG9yeS1hdmF0YXItaW5uZXIge1xuICAgICAgd2lkdGg6IDUwcHg7XG4gICAgICBoZWlnaHQ6IDUwcHg7XG5cbiAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgICAgICB3aWR0aDogNTBweDtcbiAgICAgICAgaGVpZ2h0OiA1MHB4O1xuICAgICAgfVxuICAgIH1cblxuICAgIC5zdG9yeS11c2VybmFtZSB7XG4gICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICBjb2xvcjogIzI2MjYyNjtcbiAgICAgIHRleHQtc2hhZG93OiBub25lO1xuICAgICAgbWF4LXdpZHRoOiA2NHB4O1xuXG4gICAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgICAgbWF4LXdpZHRoOiA2NHB4O1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgIGJvcmRlci1yYWRpdXM6IDA7XG4gICAgYm9yZGVyLWxlZnQ6IG5vbmU7XG4gICAgYm9yZGVyLXJpZ2h0OiBub25lO1xuICAgIG1hcmdpbi1ib3R0b206IDA7XG4gIH1cbn1cblxuLnBvc3RzLWZlZWQge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBnYXA6IDI0cHg7XG59XG5cbi8vIEluc3RhZ3JhbSBQb3N0IFN0eWxlc1xuLmluc3RhZ3JhbS1wb3N0IHtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG4gIGJvcmRlcjogMXB4IHNvbGlkICNkYmRiZGI7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgbWFyZ2luLWJvdHRvbTogMjRweDtcbiAgbWF4LXdpZHRoOiA2MTRweDtcbiAgd2lkdGg6IDEwMCU7XG5cbiAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gICAgYm9yZGVyLXJhZGl1czogMDtcbiAgICBib3JkZXItbGVmdDogbm9uZTtcbiAgICBib3JkZXItcmlnaHQ6IG5vbmU7XG4gICAgbWFyZ2luLWJvdHRvbTogMDtcbiAgICBib3JkZXItYm90dG9tOiBub25lO1xuICB9XG59XG5cbi5wb3N0LWhlYWRlciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgcGFkZGluZzogMTZweDtcblxuICAudXNlci1pbmZvIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgZ2FwOiAxMnB4O1xuICB9XG5cbiAgLnVzZXItYXZhdGFyIHtcbiAgICB3aWR0aDogMzJweDtcbiAgICBoZWlnaHQ6IDMycHg7XG4gICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgIG9iamVjdC1maXQ6IGNvdmVyO1xuICB9XG5cbiAgLnVzZXItZGV0YWlscyB7XG4gICAgLnVzZXJuYW1lIHtcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBtYXJnaW46IDA7XG4gICAgICBjb2xvcjogIzI2MjYyNjtcbiAgICB9XG5cbiAgICAubG9jYXRpb24ge1xuICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgY29sb3I6ICM4ZThlOGU7XG4gICAgfVxuICB9XG5cbiAgLm1vcmUtb3B0aW9ucyB7XG4gICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgY3Vyc29yOiBwb2ludGVyO1xuICAgIHBhZGRpbmc6IDhweDtcbiAgICBjb2xvcjogIzI2MjYyNjtcblxuICAgICY6aG92ZXIge1xuICAgICAgY29sb3I6ICM4ZThlOGU7XG4gICAgfVxuICB9XG59XG5cbi5wb3N0LW1lZGlhIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB3aWR0aDogMTAwJTtcbiAgYXNwZWN0LXJhdGlvOiAxO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuXG4gIC5wb3N0LWltYWdlIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBoZWlnaHQ6IDEwMCU7XG4gICAgb2JqZWN0LWZpdDogY292ZXI7XG4gICAgZGlzcGxheTogYmxvY2s7XG4gIH1cbn1cblxuLy8gUHJvZHVjdCBUYWdzIE92ZXJsYXlcbi5wcm9kdWN0LXRhZ3Mtb3ZlcmxheSB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xuICBwb2ludGVyLWV2ZW50czogbm9uZTtcbn1cblxuLnByb2R1Y3QtdGFnIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICBwb2ludGVyLWV2ZW50czogYWxsO1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIHotaW5kZXg6IDU7XG5cbiAgLnRhZy1kb3Qge1xuICAgIHdpZHRoOiAyMHB4O1xuICAgIGhlaWdodDogMjBweDtcbiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgYm9yZGVyOiAycHggc29saWQgIzI2MjYyNjtcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgYW5pbWF0aW9uOiBwdWxzZSAycyBpbmZpbml0ZTtcblxuICAgICY6OmFmdGVyIHtcbiAgICAgIGNvbnRlbnQ6ICcnO1xuICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgdG9wOiA1MCU7XG4gICAgICBsZWZ0OiA1MCU7XG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgtNTAlLCAtNTAlKTtcbiAgICAgIHdpZHRoOiA2cHg7XG4gICAgICBoZWlnaHQ6IDZweDtcbiAgICAgIGJhY2tncm91bmQ6ICMyNjI2MjY7XG4gICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgfVxuICB9XG5cbiAgLnByb2R1Y3QtcHJldmlldyB7XG4gICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgIGJvdHRvbTogMzBweDtcbiAgICBsZWZ0OiA1MCU7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01MCUpO1xuICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC44KTtcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgcGFkZGluZzogOHB4O1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBnYXA6IDhweDtcbiAgICBtaW4td2lkdGg6IDIwMHB4O1xuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcblxuICAgIGltZyB7XG4gICAgICB3aWR0aDogNDBweDtcbiAgICAgIGhlaWdodDogNDBweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICAgIG9iamVjdC1maXQ6IGNvdmVyO1xuICAgIH1cblxuICAgIC5wcm9kdWN0LWluZm8ge1xuICAgICAgY29sb3I6IHdoaXRlO1xuXG4gICAgICAucHJvZHVjdC1uYW1lIHtcbiAgICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgIH1cblxuICAgICAgLnByb2R1Y3QtcHJpY2Uge1xuICAgICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICBjb2xvcjogI2Y1ZjVmNTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuQGtleWZyYW1lcyBwdWxzZSB7XG4gIDAlLCAxMDAlIHsgdHJhbnNmb3JtOiBzY2FsZSgxKTsgfVxuICA1MCUgeyB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7IH1cbn1cblxuLy8gUG9zdCBBY3Rpb25zXG4ucG9zdC1hY3Rpb25zIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBwYWRkaW5nOiAxMnB4IDE2cHggOHB4O1xuXG4gIC5wcmltYXJ5LWFjdGlvbnMge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgZ2FwOiAxNnB4O1xuICB9XG5cbiAgLmFjdGlvbi1idG4ge1xuICAgIGJhY2tncm91bmQ6IG5vbmU7XG4gICAgYm9yZGVyOiBub25lO1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICBwYWRkaW5nOiA4cHg7XG4gICAgZm9udC1zaXplOiAyNHB4O1xuICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGNvbG9yOiAjOGU4ZThlO1xuICAgIH1cblxuICAgICYubGlrZWQge1xuICAgICAgY29sb3I6ICNlZDQ5NTY7XG4gICAgICBhbmltYXRpb246IGhlYXJ0QmVhdCAwLjZzIGVhc2U7XG4gICAgfVxuXG4gICAgJi5zYXZlZCB7XG4gICAgICBjb2xvcjogIzI2MjYyNjtcbiAgICB9XG4gIH1cbn1cblxuQGtleWZyYW1lcyBoZWFydEJlYXQge1xuICAwJSwgMTAwJSB7IHRyYW5zZm9ybTogc2NhbGUoMSk7IH1cbiAgNTAlIHsgdHJhbnNmb3JtOiBzY2FsZSgxLjIpOyB9XG59XG5cbi8vIFBvc3QgQ29udGVudCBTZWN0aW9uc1xuLmxpa2VzLXNlY3Rpb24ge1xuICBwYWRkaW5nOiAwIDE2cHggOHB4O1xuXG4gIC5saWtlcy1jb3VudCB7XG4gICAgZm9udC1zaXplOiAxNHB4O1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgY29sb3I6ICMyNjI2MjY7XG4gIH1cbn1cblxuLnBvc3QtY2FwdGlvbiB7XG4gIHBhZGRpbmc6IDAgMTZweCA4cHg7XG4gIGZvbnQtc2l6ZTogMTRweDtcbiAgbGluZS1oZWlnaHQ6IDEuNDtcblxuICAudXNlcm5hbWUge1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgY29sb3I6ICMyNjI2MjY7XG4gICAgbWFyZ2luLXJpZ2h0OiA4cHg7XG4gIH1cblxuICAuY2FwdGlvbi10ZXh0IHtcbiAgICBjb2xvcjogIzI2MjYyNjtcbiAgfVxufVxuXG4vLyBFLWNvbW1lcmNlIEFjdGlvbnNcbi5lY29tbWVyY2UtYWN0aW9ucyB7XG4gIHBhZGRpbmc6IDEycHggMTZweDtcbiAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlZmVmZWY7XG5cbiAgLnByb2R1Y3Qtc2hvd2Nhc2Uge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgZ2FwOiAxMnB4O1xuICAgIG1hcmdpbi1ib3R0b206IDEycHg7XG5cbiAgICAuZmVhdHVyZWQtcHJvZHVjdCB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogOHB4O1xuICAgICAgZmxleDogMTtcblxuICAgICAgLnByb2R1Y3QtdGh1bWJuYWlsIHtcbiAgICAgICAgd2lkdGg6IDQwcHg7XG4gICAgICAgIGhlaWdodDogNDBweDtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNnB4O1xuICAgICAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgICAgIH1cblxuICAgICAgLnByb2R1Y3QtZGV0YWlscyB7XG4gICAgICAgIGZsZXg6IDE7XG5cbiAgICAgICAgLnByb2R1Y3QtbmFtZSB7XG4gICAgICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICAgICAgY29sb3I6ICMyNjI2MjY7XG4gICAgICAgICAgbGluZS1oZWlnaHQ6IDEuMjtcbiAgICAgICAgfVxuXG4gICAgICAgIC5wcm9kdWN0LXByaWNlIHtcbiAgICAgICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICBjb2xvcjogIzY2N2VlYTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5zaG9wcGluZy1idXR0b25zIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGdhcDogOHB4O1xuXG4gICAgLnNob3AtYnRuIHtcbiAgICAgIGZsZXg6IDE7XG4gICAgICBwYWRkaW5nOiA4cHggMTZweDtcbiAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDZweDtcbiAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgIGdhcDogNHB4O1xuXG4gICAgICAmLmJ1eS1idG4ge1xuICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhLCAjNzY0YmEyKTtcbiAgICAgICAgY29sb3I6IHdoaXRlO1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC40KTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAmLndpc2hsaXN0LWJ0biB7XG4gICAgICAgIGJhY2tncm91bmQ6IHdoaXRlO1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZGJkYmRiO1xuICAgICAgICBjb2xvcjogIzI2MjYyNjtcblxuICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xuICAgICAgICAgIGJvcmRlci1jb2xvcjogI2M2YzZjNjtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAmLmNhcnQtYnRuIHtcbiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2YwOTNmYiwgI2Y1NTc2Yyk7XG4gICAgICAgIGNvbG9yOiB3aGl0ZTtcblxuICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XG4gICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDI0MCwgMTQ3LCAyNTEsIDAuNCk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaSB7XG4gICAgICAgIGZvbnQtc2l6ZTogMTBweDtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLy8gQ29tbWVudHMgU2VjdGlvblxuLmNvbW1lbnRzLXByZXZpZXcge1xuICBwYWRkaW5nOiAwIDE2cHggOHB4O1xuXG4gIC52aWV3LWNvbW1lbnRzLWJ0biB7XG4gICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgY29sb3I6ICM4ZThlOGU7XG4gICAgZm9udC1zaXplOiAxNHB4O1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICBtYXJnaW4tYm90dG9tOiA0cHg7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgIH1cbiAgfVxuXG4gIC5yZWNlbnQtY29tbWVudHMge1xuICAgIC5jb21tZW50IHtcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjQ7XG4gICAgICBtYXJnaW4tYm90dG9tOiAycHg7XG5cbiAgICAgIC5jb21tZW50LXVzZXJuYW1lIHtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgY29sb3I6ICMyNjI2MjY7XG4gICAgICAgIG1hcmdpbi1yaWdodDogOHB4O1xuICAgICAgfVxuXG4gICAgICAuY29tbWVudC10ZXh0IHtcbiAgICAgICAgY29sb3I6ICMyNjI2MjY7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi5wb3N0LXRpbWUge1xuICBwYWRkaW5nOiAwIDE2cHggOHB4O1xuICBmb250LXNpemU6IDEwcHg7XG4gIGNvbG9yOiAjOGU4ZThlO1xuICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuICBsZXR0ZXItc3BhY2luZzogMC4ycHg7XG59XG5cbi5hZGQtY29tbWVudC1zZWN0aW9uIHtcbiAgcGFkZGluZzogMTJweCAxNnB4O1xuICBib3JkZXItdG9wOiAxcHggc29saWQgI2VmZWZlZjtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiA4cHg7XG5cbiAgLmNvbW1lbnQtaW5wdXQge1xuICAgIGZsZXg6IDE7XG4gICAgYm9yZGVyOiBub25lO1xuICAgIG91dGxpbmU6IG5vbmU7XG4gICAgZm9udC1zaXplOiAxNHB4O1xuICAgIGNvbG9yOiAjMjYyNjI2O1xuXG4gICAgJjo6cGxhY2Vob2xkZXIge1xuICAgICAgY29sb3I6ICM4ZThlOGU7XG4gICAgfVxuICB9XG5cbiAgLnBvc3QtY29tbWVudC1idG4ge1xuICAgIGJhY2tncm91bmQ6IG5vbmU7XG4gICAgYm9yZGVyOiBub25lO1xuICAgIGNvbG9yOiAjMDA5NWY2O1xuICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICBmb250LXdlaWdodDogNjAwO1xuICAgIGN1cnNvcjogcG9pbnRlcjtcblxuICAgICY6ZGlzYWJsZWQge1xuICAgICAgY29sb3I6ICNjN2M3Yzc7XG4gICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xuICAgIH1cblxuICAgICY6bm90KDpkaXNhYmxlZCk6aG92ZXIge1xuICAgICAgY29sb3I6ICMwMDM3NmI7XG4gICAgfVxuICB9XG59XG5cbi8vIEluc3RhZ3JhbSBTaWRlYmFyXG4uaW5zdGFncmFtLXNpZGViYXIge1xuICBwb3NpdGlvbjogc3RpY2t5O1xuICB0b3A6IDg0cHg7IC8vIEFjY291bnQgZm9yIGhlYWRlciBoZWlnaHRcbiAgaGVpZ2h0OiBmaXQtY29udGVudDtcbiAgcGFkZGluZy1sZWZ0OiAwO1xuXG4gIEBtZWRpYSAobWF4LXdpZHRoOiAxMDI0cHgpIHtcbiAgICBkaXNwbGF5OiBub25lO1xuICB9XG59XG5cbi5wcm9maWxlLWNhcmQge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDEycHg7XG4gIHBhZGRpbmc6IDE2cHggMCAyNHB4IDA7XG4gIG1hcmdpbi1ib3R0b206IDhweDtcblxuICAucHJvZmlsZS1hdmF0YXIge1xuICAgIHdpZHRoOiA1NnB4O1xuICAgIGhlaWdodDogNTZweDtcbiAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgb2JqZWN0LWZpdDogY292ZXI7XG4gIH1cblxuICAucHJvZmlsZS1pbmZvIHtcbiAgICBmbGV4OiAxO1xuXG4gICAgLnByb2ZpbGUtdXNlcm5hbWUge1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgICAgbWFyZ2luOiAwIDAgMnB4IDA7XG4gICAgfVxuXG4gICAgLnByb2ZpbGUtbmFtZSB7XG4gICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICBjb2xvcjogIzhlOGU4ZTtcbiAgICB9XG4gIH1cblxuICAuc3dpdGNoLWJ0biB7XG4gICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgY29sb3I6ICMwMDk1ZjY7XG4gICAgZm9udC1zaXplOiAxMnB4O1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgY3Vyc29yOiBwb2ludGVyO1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBjb2xvcjogIzAwMzc2YjtcbiAgICB9XG4gIH1cbn1cblxuLnNpZGViYXItc2VjdGlvbiB7XG4gIG1hcmdpbi1ib3R0b206IDMycHg7XG5cbiAgLnNlY3Rpb24taGVhZGVyIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIG1hcmdpbi1ib3R0b206IDE2cHg7XG5cbiAgICBoMyB7XG4gICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgY29sb3I6ICM4ZThlOGU7XG4gICAgICBtYXJnaW46IDA7XG4gICAgICB0ZXh0LXRyYW5zZm9ybTogbm9uZTtcbiAgICB9XG5cbiAgICAuc2VlLWFsbC1idG4ge1xuICAgICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDQwMDtcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcblxuICAgICAgJjpob3ZlciB7XG4gICAgICAgIGNvbG9yOiAjOGU4ZThlO1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyBDb2xsZWN0aW9uIEl0ZW1zXG4uY29sbGVjdGlvbi1pdGVtcywgLmZlYXR1cmVkLWl0ZW1zLCAudHJlbmRpbmctaXRlbXMsIC5hcnJpdmFscy1pdGVtcyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogOHB4O1xufVxuXG4uY29sbGVjdGlvbi1pdGVtLCAuZmVhdHVyZWQtaXRlbSwgLnRyZW5kaW5nLWl0ZW0sIC5hcnJpdmFsLWl0ZW0ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDEycHg7XG4gIHBhZGRpbmc6IDRweCAwO1xuICBib3JkZXItcmFkaXVzOiAzcHg7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjJzIGVhc2U7XG5cbiAgJjpob3ZlciB7XG4gICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjA1KTtcbiAgfVxuXG4gIGltZyB7XG4gICAgd2lkdGg6IDQ0cHg7XG4gICAgaGVpZ2h0OiA0NHB4O1xuICAgIGJvcmRlci1yYWRpdXM6IDNweDtcbiAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgfVxuXG4gIC5jb2xsZWN0aW9uLWluZm8sIC5mZWF0dXJlZC1pbmZvLCAudHJlbmRpbmctaW5mbywgLmFycml2YWwtaW5mbyB7XG4gICAgZmxleDogMTtcblxuICAgIC5jb2xsZWN0aW9uLW5hbWUsIC5mZWF0dXJlZC1uYW1lLCAudHJlbmRpbmctbmFtZSwgLmFycml2YWwtbmFtZSB7XG4gICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgICBjb2xvcjogIzI2MjYyNjtcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjI7XG4gICAgICBtYXJnaW4tYm90dG9tOiAycHg7XG4gICAgfVxuXG4gICAgLmZlYXR1cmVkLWJyYW5kLCAuYXJyaXZhbC1icmFuZCB7XG4gICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgIGNvbG9yOiAjOGU4ZThlO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMnB4O1xuICAgIH1cblxuICAgIC5jb2xsZWN0aW9uLXByaWNlLCAuZmVhdHVyZWQtcHJpY2UsIC50cmVuZGluZy1wcmljZSwgLmFycml2YWwtcHJpY2Uge1xuICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgIGNvbG9yOiAjNjY3ZWVhO1xuICAgIH1cblxuICAgIC5mZWF0dXJlZC1wcmljZSB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogNnB4O1xuXG4gICAgICAuY3VycmVudC1wcmljZSB7XG4gICAgICAgIGNvbG9yOiAjNjY3ZWVhO1xuICAgICAgfVxuXG4gICAgICAub3JpZ2luYWwtcHJpY2Uge1xuICAgICAgICBjb2xvcjogIzhlOGU4ZTtcbiAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiBsaW5lLXRocm91Z2g7XG4gICAgICAgIGZvbnQtc2l6ZTogMTBweDtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAudHJlbmRpbmctc3RhdHMge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGdhcDogOHB4O1xuICAgICAgbWFyZ2luLWJvdHRvbTogMnB4O1xuXG4gICAgICBzcGFuIHtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgZ2FwOiAycHg7XG4gICAgICAgIGZvbnQtc2l6ZTogMTBweDtcbiAgICAgICAgY29sb3I6ICM4ZThlOGU7XG5cbiAgICAgICAgaSB7XG4gICAgICAgICAgZm9udC1zaXplOiA4cHg7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLy8gU3BlY2lhbCBiYWRnZXNcbi50cmVuZGluZy1iYWRnZSwgLm5ldy1iYWRnZSB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiA0cHg7XG4gIHJpZ2h0OiA0cHg7XG4gIHBhZGRpbmc6IDJweCA2cHg7XG4gIGJvcmRlci1yYWRpdXM6IDEwcHg7XG4gIGZvbnQtc2l6ZTogOHB4O1xuICBmb250LXdlaWdodDogNjAwO1xuICBjb2xvcjogd2hpdGU7XG59XG5cbi50cmVuZGluZy1iYWRnZSB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZjZiNmIsICNmZmE1MDApO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDJweDtcblxuICBpIHtcbiAgICBmb250LXNpemU6IDZweDtcbiAgfVxufVxuXG4ubmV3LWJhZGdlIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzQzZTk3YiwgIzM4ZjlkNyk7XG59XG5cbi50cmVuZGluZy1pdGVtLCAuYXJyaXZhbC1pdGVtIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xufVxuXG4vLyBTdWdnZXN0ZWQgVXNlcnNcbi5zdWdnZXN0ZWQtdXNlcnMge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBnYXA6IDhweDtcbn1cblxuLnN1Z2dlc3RlZC11c2VyIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiAxMnB4O1xuICBwYWRkaW5nOiA4cHg7XG5cbiAgLnN1Z2dlc3RlZC1hdmF0YXIge1xuICAgIHdpZHRoOiAzMnB4O1xuICAgIGhlaWdodDogMzJweDtcbiAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgb2JqZWN0LWZpdDogY292ZXI7XG4gIH1cblxuICAuc3VnZ2VzdGVkLWluZm8ge1xuICAgIGZsZXg6IDE7XG5cbiAgICAuc3VnZ2VzdGVkLXVzZXJuYW1lIHtcbiAgICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgIGNvbG9yOiAjMjYyNjI2O1xuICAgICAgbGluZS1oZWlnaHQ6IDEuMjtcbiAgICB9XG5cbiAgICAuc3VnZ2VzdGVkLWZvbGxvd2VycyB7XG4gICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICBjb2xvcjogIzhlOGU4ZTtcbiAgICB9XG4gIH1cblxuICAuZm9sbG93LWJ0biB7XG4gICAgYmFja2dyb3VuZDogIzAwOTVmNjtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgY29sb3I6IHdoaXRlO1xuICAgIHBhZGRpbmc6IDRweCAxMnB4O1xuICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICBmb250LXNpemU6IDEycHg7XG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjNzIGVhc2U7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6ICMwMDM3NmI7XG4gICAgfVxuICB9XG59XG5cbi8vIFNpZGViYXIgRm9vdGVyXG4uc2lkZWJhci1mb290ZXIge1xuICBtYXJnaW4tdG9wOiAzMnB4O1xuICBwYWRkaW5nLXRvcDogMTZweDtcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlZmVmZWY7XG5cbiAgLmZvb3Rlci1saW5rcyB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBmbGV4LXdyYXA6IHdyYXA7XG4gICAgZ2FwOiA4cHg7XG4gICAgbWFyZ2luLWJvdHRvbTogMTZweDtcblxuICAgIGEge1xuICAgICAgZm9udC1zaXplOiAxMXB4O1xuICAgICAgY29sb3I6ICNjN2M3Yzc7XG4gICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG5cbiAgICAgICY6aG92ZXIge1xuICAgICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAuY29weXJpZ2h0IHtcbiAgICBmb250LXNpemU6IDExcHg7XG4gICAgY29sb3I6ICNjN2M3Yzc7XG4gIH1cbn1cblxuLy8gTW9iaWxlIFJlc3BvbnNpdmVuZXNzXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLmluc3RhZ3JhbS1ob21lLWNvbnRhaW5lciB7XG4gICAgcGFkZGluZy10b3A6IDA7XG4gIH1cblxuICAuaW5zdGFncmFtLWxheW91dCB7XG4gICAgcGFkZGluZzogMDtcbiAgICBtYXgtd2lkdGg6IDEwMCU7XG4gIH1cblxuICAubWFpbi1mZWVkIHtcbiAgICBnYXA6IDA7XG4gIH1cblxuICAuaW5zdGFncmFtLXBvc3Qge1xuICAgIG1hcmdpbi1ib3R0b206IDA7XG4gICAgYm9yZGVyLXJhZGl1czogMDtcbiAgICBib3JkZXItbGVmdDogbm9uZTtcbiAgICBib3JkZXItcmlnaHQ6IG5vbmU7XG4gICAgbWF4LXdpZHRoOiAxMDAlO1xuXG4gICAgJjpsYXN0LWNoaWxkIHtcbiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZGJkYmRiO1xuICAgIH1cbiAgfVxuXG4gIC5zdG9yaWVzLXNlY3Rpb24ge1xuICAgIGJvcmRlci1yYWRpdXM6IDA7XG4gICAgYm9yZGVyLWxlZnQ6IG5vbmU7XG4gICAgYm9yZGVyLXJpZ2h0OiBub25lO1xuICAgIG1hcmdpbi1ib3R0b206IDA7XG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNkYmRiZGI7XG4gIH1cblxuICAucG9zdC1oZWFkZXIge1xuICAgIHBhZGRpbmc6IDE0cHggMTZweDtcbiAgfVxuXG4gIC5wb3N0LWFjdGlvbnMge1xuICAgIHBhZGRpbmc6IDZweCAxNnB4IDRweDtcbiAgfVxuXG4gIC5lY29tbWVyY2UtYWN0aW9ucyB7XG4gICAgcGFkZGluZzogMTJweCAxNnB4O1xuXG4gICAgLnNob3BwaW5nLWJ1dHRvbnMge1xuICAgICAgLnNob3AtYnRuIHtcbiAgICAgICAgcGFkZGluZzogOHB4IDEycHg7XG4gICAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAuYWRkLWNvbW1lbnQtc2VjdGlvbiB7XG4gICAgcGFkZGluZzogMTJweCAxNnB4O1xuICB9XG5cbiAgLmxpa2VzLXNlY3Rpb24sIC5wb3N0LWNhcHRpb24sIC5jb21tZW50cy1wcmV2aWV3LCAucG9zdC10aW1lIHtcbiAgICBwYWRkaW5nLWxlZnQ6IDE2cHg7XG4gICAgcGFkZGluZy1yaWdodDogMTZweDtcbiAgfVxufVxuXG5AbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHtcbiAgLmVjb21tZXJjZS1hY3Rpb25zIHtcbiAgICAucHJvZHVjdC1zaG93Y2FzZSB7XG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgZ2FwOiA4cHg7XG5cbiAgICAgIC5mZWF0dXJlZC1wcm9kdWN0IHtcbiAgICAgICAgLnByb2R1Y3QtdGh1bWJuYWlsIHtcbiAgICAgICAgICB3aWR0aDogMzJweDtcbiAgICAgICAgICBoZWlnaHQ6IDMycHg7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAuc2hvcHBpbmctYnV0dG9ucyB7XG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgZ2FwOiA2cHg7XG5cbiAgICAgIC5zaG9wLWJ0biB7XG4gICAgICAgIHBhZGRpbmc6IDhweCAxNnB4O1xuICAgICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "RouterModule", "ViewAddStoriesComponent", "PaymentModalComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "post_r3", "location", "ɵɵproperty", "product_r5", "image", "ɵɵsanitizeUrl", "name", "ctx_r5", "formatPrice", "price", "ɵɵlistener", "HomeComponent_div_2_article_5_div_12_button_1_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "showProductDetails", "ɵɵtemplate", "HomeComponent_div_2_article_5_div_12_button_1_div_2_Template", "ɵɵstyleProp", "position", "x", "y", "showPreview", "HomeComponent_div_2_article_5_div_12_button_1_Template", "products", "ɵɵtextInterpolate1", "formatLikesCount", "likes", "product_r8", "HomeComponent_div_2_article_5_div_29_div_2_Template", "HomeComponent_div_2_article_5_div_29_Template_button_click_4_listener", "_r7", "buyNow", "HomeComponent_div_2_article_5_div_29_Template_button_click_7_listener", "addToWishlist", "HomeComponent_div_2_article_5_div_29_Template_button_click_10_listener", "addToCart", "slice", "comment_r10", "username", "text", "HomeComponent_div_2_article_5_div_30_Template_button_click_1_listener", "_r9", "toggleComments", "HomeComponent_div_2_article_5_div_30_div_4_Template", "comments", "length", "HomeComponent_div_2_article_5_span_7_Template", "HomeComponent_div_2_article_5_div_12_Template", "HomeComponent_div_2_article_5_Template_button_click_15_listener", "_r2", "toggleLike", "HomeComponent_div_2_article_5_Template_button_click_17_listener", "focusCommentInput", "HomeComponent_div_2_article_5_Template_button_click_19_listener", "sharePost", "HomeComponent_div_2_article_5_Template_button_click_21_listener", "toggleSave", "HomeComponent_div_2_article_5_div_23_Template", "HomeComponent_div_2_article_5_div_29_Template", "HomeComponent_div_2_article_5_div_30_Template", "ɵɵtwoWayListener", "HomeComponent_div_2_article_5_Template_input_ngModelChange_34_listener", "$event", "ɵɵtwoWayBindingSet", "newComment", "HomeComponent_div_2_article_5_Template_input_keyup_enter_34_listener", "addComment", "HomeComponent_div_2_article_5_Template_button_click_35_listener", "user", "avatar", "mediaUrl", "caption", "ɵɵclassProp", "isLiked", "ɵɵclassMap", "isSaved", "getTimeAgo", "createdAt", "ɵɵtwoWayProperty", "trim", "currentUser", "fullName", "HomeComponent_div_2_div_15_Template_div_click_0_listener", "item_r12", "_r11", "onProductClick", "images", "url", "product_r14", "originalPrice", "HomeComponent_div_2_div_23_Template_div_click_0_listener", "_r13", "HomeComponent_div_2_div_23_span_10_Template", "brand", "HomeComponent_div_2_div_31_Template_div_click_0_listener", "product_r16", "_r15", "formatNumber", "analytics", "views", "HomeComponent_div_2_div_39_Template_div_click_0_listener", "product_r18", "_r17", "HomeComponent_div_2_div_47_Template_button_click_7_listener", "user_r20", "_r19", "followUser", "followers", "HomeComponent_div_2_article_5_Template", "HomeComponent_div_2_div_7_Template", "HomeComponent_div_2_Template_button_click_12_listener", "_r1", "viewSummerCollection", "HomeComponent_div_2_div_15_Template", "HomeComponent_div_2_div_23_Template", "HomeComponent_div_2_div_31_Template", "HomeComponent_div_2_div_39_Template", "HomeComponent_div_2_div_47_Template", "instagramPosts", "trackByPostId", "summerCollection", "featuredProducts", "trendingProducts", "newArrivals", "suggestedUsers", "HomeComponent", "constructor", "router", "productService", "authService", "trendingPosts", "categories", "isLoading", "isAuthenticated", "showPaymentModal", "paymentModalData", "ngOnInit", "loadHomeData", "checkAuthStatus", "loadInstagramData", "currentUser$", "subscribe", "loadInstagramPosts", "loadSummerCollection", "loadSuggestedUsers", "id", "Date", "now", "_id", "_this", "_asyncToGenerator", "featured", "trending", "arrivals", "Promise", "all", "getFeaturedProducts", "to<PERSON>romise", "getTrendingProducts", "getNewArrivals", "data", "getFallbackProducts", "loadCategories", "loadTrendingPosts", "error", "console", "loadFallbackData", "icon", "color", "title", "mediaType", "discount", "rating", "average", "count", "product", "navigate", "onCategoryClick", "category", "queryParams", "toLowerCase", "viewAllCategories", "post", "log", "push", "returnUrl", "amount", "orderData", "items", "quantity", "subtotal", "tax", "shipping", "total", "userDetails", "email", "phone", "collection", "Intl", "NumberFormat", "style", "currency", "format", "num", "toFixed", "toString", "date", "diffInSeconds", "Math", "floor", "getTime", "index", "onPaymentCompleted", "paymentResult", "status", "orderId", "generateOrderId", "method", "alert", "onPaymentModalClose", "ɵɵdirectiveInject", "i1", "Router", "i2", "ProductService", "i3", "AuthService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "HomeComponent_div_1_Template", "HomeComponent_div_2_Template", "HomeComponent_Template_app_payment_modal_close_3_listener", "HomeComponent_Template_app_payment_modal_paymentCompleted_3_listener", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "DefaultValueAccessor", "NgControlStatus", "NgModel", "RouterLink", "styles"], "sources": ["E:\\Fahion\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.ts", "E:\\Fahion\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router, RouterModule } from '@angular/router';\nimport { ProductService } from '../../../../core/services/product.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { ViewAddStoriesComponent } from '../../components/instagram-stories/view-add-stories.component';\nimport { PaymentModalComponent, PaymentModalData } from '../../../../shared/components/payment-modal/payment-modal.component';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, FormsModule, RouterModule, ViewAddStoriesComponent, PaymentModalComponent],\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit {\n  featuredProducts: any[] = [];\n  trendingProducts: any[] = [];\n  newArrivals: any[] = [];\n  trendingPosts: any[] = [];\n  categories: any[] = [];\n  isLoading = true;\n  isAuthenticated = false;\n\n  // Instagram-style data\n  instagramPosts: any[] = [];\n  summerCollection: any[] = [];\n  suggestedUsers: any[] = [];\n  currentUser: any = null;\n  newComment = '';\n\n  // Payment Modal\n  showPaymentModal = false;\n  paymentModalData: PaymentModalData | null = null;\n\n  constructor(\n    private router: Router,\n    private productService: ProductService,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit() {\n    this.loadHomeData();\n    this.checkAuthStatus();\n    this.loadInstagramData();\n  }\n\n  checkAuthStatus() {\n    this.authService.currentUser$.subscribe(user => {\n      this.isAuthenticated = !!user;\n      this.currentUser = user;\n    });\n  }\n\n  loadInstagramData() {\n    this.loadInstagramPosts();\n    this.loadSummerCollection();\n    this.loadSuggestedUsers();\n  }\n\n  loadInstagramPosts() {\n    // Mock Instagram-style posts with shoppable content\n    this.instagramPosts = [\n      {\n        id: '1',\n        user: {\n          username: 'fashionista_maya',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600',\n        caption: 'Summer vibes with this amazing denim jacket! Perfect for those cool evenings ✨ #SummerStyle #DenimLove',\n        location: 'Los Angeles, CA',\n        likes: 1247,\n        isLiked: false,\n        isSaved: false,\n        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago\n        products: [\n          {\n            id: '1',\n            name: 'Vintage Denim Jacket',\n            price: 89.99,\n            image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=200',\n            position: { x: 30, y: 40 }\n          }\n        ],\n        comments: [\n          { username: 'style_lover', text: 'Love this look! 😍' },\n          { username: 'fashion_fan', text: 'Where can I get this jacket?' }\n        ]\n      },\n      {\n        id: '2',\n        user: {\n          username: 'style_guru_alex',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=600',\n        caption: 'Elegant evening look with this silk dress 💫 Perfect for date nights! #EveningWear #SilkDress',\n        location: 'New York, NY',\n        likes: 892,\n        isLiked: true,\n        isSaved: false,\n        createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago\n        products: [\n          {\n            id: '2',\n            name: 'Silk Slip Dress',\n            price: 159.99,\n            image: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=200',\n            position: { x: 50, y: 60 }\n          }\n        ],\n        comments: [\n          { username: 'elegant_style', text: 'Absolutely gorgeous! 💕' }\n        ]\n      },\n      {\n        id: '3',\n        user: {\n          username: 'trendy_sarah',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600',\n        caption: 'Cozy vibes in this amazing knit sweater! Perfect for autumn days 🍂 #CozyStyle #AutumnFashion',\n        location: 'San Francisco, CA',\n        likes: 567,\n        isLiked: false,\n        isSaved: true,\n        createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago\n        products: [\n          {\n            id: '3',\n            name: 'Cozy Knit Sweater',\n            price: 79.99,\n            image: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=200',\n            position: { x: 40, y: 50 }\n          }\n        ],\n        comments: []\n      }\n    ];\n  }\n\n  loadSummerCollection() {\n    this.summerCollection = [\n      {\n        _id: 'summer1',\n        name: 'Floral Maxi Dress',\n        price: 129.99,\n        images: [{ url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=300' }]\n      },\n      {\n        _id: 'summer2',\n        name: 'Linen Beach Shirt',\n        price: 69.99,\n        images: [{ url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300' }]\n      },\n      {\n        _id: 'summer3',\n        name: 'Summer Sandals',\n        price: 89.99,\n        images: [{ url: 'https://images.unsplash.com/photo-1543163521-1bf539c55dd2?w=300' }]\n      }\n    ];\n  }\n\n  loadSuggestedUsers() {\n    this.suggestedUsers = [\n      {\n        username: 'fashion_weekly',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100',\n        followers: 125000\n      },\n      {\n        username: 'style_trends',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',\n        followers: 89000\n      },\n      {\n        username: 'outfit_inspo',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',\n        followers: 67000\n      }\n    ];\n  }\n\n  async loadHomeData() {\n    try {\n      this.isLoading = true;\n\n      // Load all data in parallel\n      const [featured, trending, arrivals] = await Promise.all([\n        this.productService.getFeaturedProducts().toPromise(),\n        this.productService.getTrendingProducts().toPromise(),\n        this.productService.getNewArrivals().toPromise()\n      ]);\n\n      this.featuredProducts = ((featured as any)?.products || (featured as any)?.data || featured || []).slice(0, 8);\n      this.trendingProducts = ((trending as any)?.products || (trending as any)?.data || trending || []).slice(0, 8);\n      this.newArrivals = ((arrivals as any)?.products || (arrivals as any)?.data || arrivals || []).slice(0, 8);\n\n      // If no data loaded, use fallback\n      if (this.featuredProducts.length === 0) {\n        this.featuredProducts = this.getFallbackProducts();\n      }\n      if (this.trendingProducts.length === 0) {\n        this.trendingProducts = this.getFallbackProducts();\n      }\n      if (this.newArrivals.length === 0) {\n        this.newArrivals = this.getFallbackProducts();\n      }\n\n      // Load categories\n      this.loadCategories();\n\n      // Load trending posts (mock data for now)\n      this.loadTrendingPosts();\n\n    } catch (error) {\n      console.error('Error loading home data:', error);\n      this.loadFallbackData();\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  loadCategories() {\n    this.categories = [\n      { name: 'Women', icon: 'fas fa-female', color: 'linear-gradient(45deg, #f093fb, #f5576c)' },\n      { name: 'Men', icon: 'fas fa-male', color: 'linear-gradient(45deg, #667eea, #764ba2)' },\n      { name: 'Kids', icon: 'fas fa-child', color: 'linear-gradient(45deg, #4facfe, #00f2fe)' },\n      { name: 'Accessories', icon: 'fas fa-gem', color: 'linear-gradient(45deg, #43e97b, #38f9d7)' },\n      { name: 'Shoes', icon: 'fas fa-shoe-prints', color: 'linear-gradient(45deg, #fa709a, #fee140)' },\n      { name: 'Bags', icon: 'fas fa-shopping-bag', color: 'linear-gradient(45deg, #a8edea, #fed6e3)' }\n    ];\n  }\n\n  loadTrendingPosts() {\n    this.trendingPosts = [\n      {\n        title: 'Summer Fashion Trends 2024',\n        mediaUrl: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',\n        mediaType: 'image',\n        analytics: { views: 15420, likes: 892 }\n      },\n      {\n        title: 'Street Style Inspiration',\n        mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400',\n        mediaType: 'image',\n        analytics: { views: 12890, likes: 1205 }\n      }\n    ];\n  }\n\n  getFallbackProducts() {\n    return [\n      {\n        _id: '1',\n        name: 'Vintage Denim Jacket',\n        brand: 'Urban Threads',\n        price: 89.99,\n        originalPrice: 129.99,\n        discount: 31,\n        images: [{ url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400' }],\n        rating: { average: 4.2, count: 156 },\n        analytics: { views: 15420, likes: 892 }\n      },\n      {\n        _id: '2',\n        name: 'Silk Slip Dress',\n        brand: 'Ethereal',\n        price: 159.99,\n        originalPrice: 199.99,\n        discount: 20,\n        images: [{ url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400' }],\n        rating: { average: 4.5, count: 89 },\n        analytics: { views: 12890, likes: 1205 }\n      },\n      {\n        _id: '3',\n        name: 'Cozy Knit Sweater',\n        brand: 'Cozy Co',\n        price: 79.99,\n        originalPrice: 99.99,\n        discount: 20,\n        images: [{ url: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400' }],\n        rating: { average: 4.3, count: 203 },\n        analytics: { views: 8750, likes: 567 }\n      },\n      {\n        _id: '4',\n        name: 'High-Waisted Jeans',\n        brand: 'Urban Threads',\n        price: 119.99,\n        images: [{ url: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=400' }],\n        rating: { average: 4.6, count: 342 },\n        analytics: { views: 22100, likes: 1456 }\n      },\n      {\n        _id: '5',\n        name: 'Floral Summer Dress',\n        brand: 'Ethereal',\n        price: 139.99,\n        images: [{ url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400' }],\n        rating: { average: 4.4, count: 128 },\n        analytics: { views: 9870, likes: 743 }\n      },\n      {\n        _id: '6',\n        name: 'Leather Ankle Boots',\n        brand: 'Urban Threads',\n        price: 199.99,\n        originalPrice: 249.99,\n        discount: 20,\n        images: [{ url: 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400' }],\n        rating: { average: 4.7, count: 89 },\n        analytics: { views: 15600, likes: 1123 }\n      },\n      {\n        _id: '7',\n        name: 'Oversized Blazer',\n        brand: 'Ethereal',\n        price: 189.99,\n        images: [{ url: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=400' }],\n        rating: { average: 4.1, count: 76 },\n        analytics: { views: 7890, likes: 456 }\n      },\n      {\n        _id: '8',\n        name: 'Casual T-Shirt',\n        brand: 'Cozy Co',\n        price: 29.99,\n        originalPrice: 39.99,\n        discount: 25,\n        images: [{ url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400' }],\n        rating: { average: 4.0, count: 234 },\n        analytics: { views: 12340, likes: 678 }\n      }\n    ];\n  }\n\n  loadFallbackData() {\n    this.featuredProducts = this.getFallbackProducts();\n    this.trendingProducts = this.getFallbackProducts();\n    this.newArrivals = this.getFallbackProducts();\n  }\n\n  // Navigation methods\n  onProductClick(product: any) {\n    this.router.navigate(['/product', product._id || product.id]);\n  }\n\n  onCategoryClick(category: any) {\n    this.router.navigate(['/shop'], {\n      queryParams: { category: category.name.toLowerCase() }\n    });\n  }\n\n  viewAllCategories() {\n    this.router.navigate(['/shop']);\n  }\n\n  // Instagram-style interaction methods\n  toggleLike(post: any) {\n    post.isLiked = !post.isLiked;\n    post.likes += post.isLiked ? 1 : -1;\n  }\n\n  toggleSave(post: any) {\n    post.isSaved = !post.isSaved;\n  }\n\n  sharePost(post: any) {\n    // Implement share functionality\n    console.log('Sharing post:', post.id);\n  }\n\n  focusCommentInput(post: any) {\n    // Focus on comment input for this post\n    console.log('Focus comment for post:', post.id);\n  }\n\n  addComment(post: any) {\n    if (this.newComment && this.newComment.trim()) {\n      if (!post.comments) {\n        post.comments = [];\n      }\n      post.comments.push({\n        username: this.currentUser?.username || 'user',\n        text: this.newComment.trim()\n      });\n      this.newComment = '';\n    }\n  }\n\n  showProductDetails(product: any) {\n    console.log('Show product details:', product);\n    // Toggle product preview\n    product.showPreview = !product.showPreview;\n  }\n\n  // E-commerce methods\n  buyNow(product: any) {\n    console.log('Buy now:', product);\n\n    if (!this.currentUser) {\n      this.router.navigate(['/auth/login'], { queryParams: { returnUrl: '/home' } });\n      return;\n    }\n\n    // Prepare payment modal data for single product purchase\n    this.paymentModalData = {\n      amount: product.price,\n      orderData: {\n        items: [{\n          id: product.id,\n          name: product.name,\n          price: product.price,\n          quantity: 1,\n          image: product.image\n        }],\n        subtotal: product.price,\n        tax: product.price * 0.18,\n        shipping: 0,\n        discount: 0,\n        total: product.price + (product.price * 0.18)\n      },\n      userDetails: {\n        name: this.currentUser.fullName || this.currentUser.username,\n        email: this.currentUser.email,\n        phone: this.currentUser.phone || ''\n      }\n    };\n\n    this.showPaymentModal = true;\n  }\n\n  addToWishlist(product: any) {\n    console.log('Add to wishlist:', product);\n    // Implement wishlist functionality\n  }\n\n  addToCart(product: any) {\n    console.log('Add to cart:', product);\n    // Implement cart functionality\n  }\n\n  followUser(user: any) {\n    console.log('Follow user:', user.username);\n    // Implement follow functionality\n  }\n\n  viewSummerCollection() {\n    this.router.navigate(['/shop'], { queryParams: { collection: 'summer2024' } });\n  }\n\n  toggleComments(post: any) {\n    console.log('Toggle comments for post:', post.id);\n    // Implement comments toggle\n  }\n\n  // Utility methods\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  formatLikesCount(likes: number): string {\n    return this.formatNumber(likes);\n  }\n\n  getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;\n    return `${Math.floor(diffInSeconds / 604800)}w`;\n  }\n\n  trackByPostId(index: number, post: any): string {\n    return post.id;\n  }\n\n  // Payment Modal handlers\n  onPaymentCompleted(paymentResult: any) {\n    this.showPaymentModal = false;\n\n    if (paymentResult.status === 'success') {\n      // Navigate to success page\n      this.router.navigate(['/payment-success'], {\n        queryParams: {\n          orderId: paymentResult.orderId || this.generateOrderId(),\n          method: paymentResult.method\n        }\n      });\n    } else {\n      // Show error message\n      alert('Payment failed. Please try again.');\n    }\n  }\n\n  onPaymentModalClose() {\n    this.showPaymentModal = false;\n  }\n\n  private generateOrderId(): string {\n    return 'ORD' + Date.now().toString();\n  }\n}\n", "<div class=\"instagram-home-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-spinner\"></div>\n    <p>Loading amazing fashion...</p>\n  </div>\n\n  <!-- Instagram-style Layout -->\n  <div *ngIf=\"!isLoading\" class=\"instagram-layout\">\n    <!-- Main Feed -->\n    <div class=\"main-feed\">\n      <!-- Instagram Stories Section -->\n      <section class=\"stories-section\">\n        <app-view-add-stories></app-view-add-stories>\n      </section> \n\n      <!-- Instagram-style Posts Feed -->\n      <section class=\"posts-feed\">\n        <article *ngFor=\"let post of instagramPosts; trackBy: trackByPostId\" class=\"instagram-post\">\n          <!-- Post Header -->\n          <header class=\"post-header\">\n            <div class=\"user-info\">\n              <img [src]=\"post.user.avatar\" [alt]=\"post.user.username\" class=\"user-avatar\">\n              <div class=\"user-details\">\n                <h3 class=\"username\">{{ post.user.username }}</h3>\n                <span class=\"location\" *ngIf=\"post.location\">{{ post.location }}</span>\n              </div>\n            </div>\n            <button class=\"more-options\">\n              <i class=\"fas fa-ellipsis-h\"></i>\n            </button>\n          </header>\n\n          <!-- Post Media -->\n          <div class=\"post-media\">\n            <img [src]=\"post.mediaUrl\" [alt]=\"post.caption\" class=\"post-image\">\n\n            <!-- Product Tags Overlay -->\n            <div class=\"product-tags-overlay\" *ngIf=\"post.products && post.products.length > 0\">\n              <button *ngFor=\"let product of post.products\"\n                      class=\"product-tag\"\n                      [style.left.%]=\"product.position?.x || 50\"\n                      [style.top.%]=\"product.position?.y || 50\"\n                      (click)=\"showProductDetails(product)\">\n                <div class=\"tag-dot\"></div>\n                <div class=\"product-preview\" *ngIf=\"product.showPreview\">\n                  <img [src]=\"product.image\" [alt]=\"product.name\">\n                  <div class=\"product-info\">\n                    <span class=\"product-name\">{{ product.name }}</span>\n                    <span class=\"product-price\">{{ formatPrice(product.price) }}</span>\n                  </div>\n                </div>\n              </button>\n            </div>\n          </div>\n\n          <!-- Post Actions -->\n          <div class=\"post-actions\">\n            <div class=\"primary-actions\">\n              <button class=\"action-btn like-btn\" [class.liked]=\"post.isLiked\" (click)=\"toggleLike(post)\">\n                <i [class]=\"post.isLiked ? 'fas fa-heart' : 'far fa-heart'\"></i>\n              </button>\n              <button class=\"action-btn comment-btn\" (click)=\"focusCommentInput(post)\">\n                <i class=\"far fa-comment\"></i>\n              </button>\n              <button class=\"action-btn share-btn\" (click)=\"sharePost(post)\">\n                <i class=\"far fa-paper-plane\"></i>\n              </button>\n            </div>\n            <button class=\"action-btn save-btn\" [class.saved]=\"post.isSaved\" (click)=\"toggleSave(post)\">\n              <i [class]=\"post.isSaved ? 'fas fa-bookmark' : 'far fa-bookmark'\"></i>\n            </button>\n          </div>\n\n          <!-- Likes Count -->\n          <div class=\"likes-section\" *ngIf=\"post.likes > 0\">\n            <span class=\"likes-count\">{{ formatLikesCount(post.likes) }} likes</span>\n          </div>\n\n          <!-- Post Caption -->\n          <div class=\"post-caption\">\n            <span class=\"username\">{{ post.user.username }}</span>\n            <span class=\"caption-text\">{{ post.caption }}</span>\n          </div>\n\n          <!-- E-commerce Actions -->\n          <div class=\"ecommerce-actions\" *ngIf=\"post.products && post.products.length > 0\">\n            <div class=\"product-showcase\">\n              <div *ngFor=\"let product of post.products.slice(0, 2)\" class=\"featured-product\">\n                <img [src]=\"product.image\" [alt]=\"product.name\" class=\"product-thumbnail\">\n                <div class=\"product-details\">\n                  <span class=\"product-name\">{{ product.name }}</span>\n                  <span class=\"product-price\">{{ formatPrice(product.price) }}</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"shopping-buttons\">\n              <button class=\"shop-btn buy-btn\" (click)=\"buyNow(post.products[0])\">\n                <i class=\"fas fa-bolt\"></i>\n                Buy Now\n              </button>\n              <button class=\"shop-btn wishlist-btn\" (click)=\"addToWishlist(post.products[0])\">\n                <i class=\"far fa-heart\"></i>\n                Wishlist\n              </button>\n              <button class=\"shop-btn cart-btn\" (click)=\"addToCart(post.products[0])\">\n                <i class=\"fas fa-shopping-cart\"></i>\n                Add to Cart\n              </button>\n            </div>\n          </div>\n\n          <!-- Comments Preview -->\n          <div class=\"comments-preview\" *ngIf=\"post.comments && post.comments.length > 0\">\n            <button class=\"view-comments-btn\" (click)=\"toggleComments(post)\">\n              View all {{ post.comments.length }} comments\n            </button>\n            <div class=\"recent-comments\">\n              <div *ngFor=\"let comment of post.comments.slice(0, 2)\" class=\"comment\">\n                <span class=\"comment-username\">{{ comment.username }}</span>\n                <span class=\"comment-text\">{{ comment.text }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- Post Time -->\n          <div class=\"post-time\">\n            {{ getTimeAgo(post.createdAt) }}\n          </div>\n\n          <!-- Add Comment -->\n          <div class=\"add-comment-section\">\n            <input type=\"text\" placeholder=\"Add a comment...\" class=\"comment-input\"\n                   [(ngModel)]=\"newComment\" (keyup.enter)=\"addComment(post)\">\n            <button class=\"post-comment-btn\" (click)=\"addComment(post)\"\n                    [disabled]=\"!newComment || !newComment.trim()\">Post</button>\n          </div>\n        </article>\n      </section>\n    </div>\n\n    <!-- Instagram-style Sidebar -->\n    <aside class=\"instagram-sidebar\">\n      <!-- User Profile Card -->\n      <div class=\"profile-card\" *ngIf=\"currentUser\">\n        <img [src]=\"currentUser.avatar\" [alt]=\"currentUser.username\" class=\"profile-avatar\">\n        <div class=\"profile-info\">\n          <h4 class=\"profile-username\">{{ currentUser.username }}</h4>\n          <span class=\"profile-name\">{{ currentUser.fullName }}</span>\n        </div>\n        <button class=\"switch-btn\">Switch</button>\n      </div>\n\n      <!-- Summer Collection 2024 -->\n      <section class=\"sidebar-section\">\n        <div class=\"section-header\">\n          <h3>Summer Collection 2024</h3>\n          <button class=\"see-all-btn\" (click)=\"viewSummerCollection()\">See All</button>\n        </div>\n        <div class=\"collection-items\">\n          <div *ngFor=\"let item of summerCollection.slice(0, 3)\" class=\"collection-item\" (click)=\"onProductClick(item)\">\n            <img [src]=\"item.images[0]?.url\" [alt]=\"item.name\" class=\"collection-image\">\n            <div class=\"collection-info\">\n              <span class=\"collection-name\">{{ item.name }}</span>\n              <span class=\"collection-price\">{{ formatPrice(item.price) }}</span>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Featured Products -->\n      <section class=\"sidebar-section\">\n        <div class=\"section-header\">\n          <h3>Featured Products</h3>\n          <button class=\"see-all-btn\" routerLink=\"/shop?filter=featured\">See All</button>\n        </div>\n        <div class=\"featured-items\">\n          <div *ngFor=\"let product of featuredProducts.slice(0, 4)\" class=\"featured-item\" (click)=\"onProductClick(product)\">\n            <img [src]=\"product.images[0]?.url\" [alt]=\"product.name\" class=\"featured-image\">\n            <div class=\"featured-info\">\n              <span class=\"featured-name\">{{ product.name }}</span>\n              <span class=\"featured-brand\">{{ product.brand }}</span>\n              <div class=\"featured-price\">\n                <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n                <span class=\"original-price\" *ngIf=\"product.originalPrice\">{{ formatPrice(product.originalPrice) }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Trending Now -->\n      <section class=\"sidebar-section\">\n        <div class=\"section-header\">\n          <h3>Trending Now</h3>\n          <button class=\"see-all-btn\" routerLink=\"/shop?filter=trending\">See All</button>\n        </div>\n        <div class=\"trending-items\">\n          <div *ngFor=\"let product of trendingProducts.slice(0, 4)\" class=\"trending-item\" (click)=\"onProductClick(product)\">\n            <img [src]=\"product.images[0]?.url\" [alt]=\"product.name\" class=\"trending-image\">\n            <div class=\"trending-badge\">\n              <i class=\"fas fa-fire\"></i>\n              <span>Hot</span>\n            </div>\n            <div class=\"trending-info\">\n              <span class=\"trending-name\">{{ product.name }}</span>\n              <div class=\"trending-stats\">\n                <span><i class=\"fas fa-eye\"></i> {{ formatNumber(product.analytics?.views || 0) }}</span>\n                <span><i class=\"fas fa-heart\"></i> {{ formatNumber(product.analytics?.likes || 0) }}</span>\n              </div>\n              <span class=\"trending-price\">{{ formatPrice(product.price) }}</span>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- New Arrivals -->\n      <section class=\"sidebar-section\">\n        <div class=\"section-header\">\n          <h3>New Arrivals</h3>\n          <button class=\"see-all-btn\" routerLink=\"/shop?filter=new\">See All</button>\n        </div>\n        <div class=\"arrivals-items\">\n          <div *ngFor=\"let product of newArrivals.slice(0, 4)\" class=\"arrival-item\" (click)=\"onProductClick(product)\">\n            <img [src]=\"product.images[0]?.url\" [alt]=\"product.name\" class=\"arrival-image\">\n            <div class=\"new-badge\">New</div>\n            <div class=\"arrival-info\">\n              <span class=\"arrival-name\">{{ product.name }}</span>\n              <span class=\"arrival-brand\">{{ product.brand }}</span>\n              <span class=\"arrival-price\">{{ formatPrice(product.price) }}</span>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Suggested for You -->\n      <section class=\"sidebar-section\">\n        <div class=\"section-header\">\n          <h3>Suggested for You</h3>\n          <button class=\"see-all-btn\">See All</button>\n        </div>\n        <div class=\"suggested-users\">\n          <div *ngFor=\"let user of suggestedUsers.slice(0, 3)\" class=\"suggested-user\">\n            <img [src]=\"user.avatar\" [alt]=\"user.username\" class=\"suggested-avatar\">\n            <div class=\"suggested-info\">\n              <span class=\"suggested-username\">{{ user.username }}</span>\n              <span class=\"suggested-followers\">{{ formatNumber(user.followers) }} followers</span>\n            </div>\n            <button class=\"follow-btn\" (click)=\"followUser(user)\">Follow</button>\n          </div>\n        </div>\n      </section>\n\n      <!-- Footer Links -->\n      <div class=\"sidebar-footer\">\n        <div class=\"footer-links\">\n          <a href=\"#\">About</a>\n          <a href=\"#\">Help</a>\n          <a href=\"#\">Press</a>\n          <a href=\"#\">API</a>\n          <a href=\"#\">Jobs</a>\n          <a href=\"#\">Privacy</a>\n          <a href=\"#\">Terms</a>\n        </div>\n        <div class=\"copyright\">\n          <span>© 2024 DFashion</span>\n        </div>\n      </div>\n    </aside>\n  </div>\n\n  <!-- Payment Modal -->\n  <app-payment-modal\n    [isVisible]=\"showPaymentModal\"\n    [paymentData]=\"paymentModalData\"\n    (close)=\"onPaymentModalClose()\"\n    (paymentCompleted)=\"onPaymentCompleted($event)\">\n  </app-payment-modal>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAAiBC,YAAY,QAAQ,iBAAiB;AAGtD,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAASC,qBAAqB,QAA0B,qEAAqE;;;;;;;;;ICL3HC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,aAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,iCAA0B;IAC/BH,EAD+B,CAAAI,YAAA,EAAI,EAC7B;;;;;IAoBQJ,EAAA,CAAAC,cAAA,eAA6C;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAA1BJ,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAC,QAAA,CAAmB;;;;;IAoBhER,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,cAAgD;IAE9CF,EADF,CAAAC,cAAA,cAA0B,eACG;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAEhEH,EAFgE,CAAAI,YAAA,EAAO,EAC/D,EACF;;;;;IALCJ,EAAA,CAAAK,SAAA,EAAqB;IAACL,EAAtB,CAAAS,UAAA,QAAAC,UAAA,CAAAC,KAAA,EAAAX,EAAA,CAAAY,aAAA,CAAqB,QAAAF,UAAA,CAAAG,IAAA,CAAqB;IAElBb,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAI,UAAA,CAAAG,IAAA,CAAkB;IACjBb,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAQ,MAAA,CAAAC,WAAA,CAAAL,UAAA,CAAAM,KAAA,EAAgC;;;;;;IAVlEhB,EAAA,CAAAC,cAAA,iBAI8C;IAAtCD,EAAA,CAAAiB,UAAA,mBAAAC,+EAAA;MAAA,MAAAR,UAAA,GAAAV,EAAA,CAAAmB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAAU,kBAAA,CAAAd,UAAA,CAA2B;IAAA,EAAC;IAC3CV,EAAA,CAAAE,SAAA,cAA2B;IAC3BF,EAAA,CAAAyB,UAAA,IAAAC,4DAAA,kBAAyD;IAO3D1B,EAAA,CAAAI,YAAA,EAAS;;;;IAVDJ,EADA,CAAA2B,WAAA,UAAAjB,UAAA,CAAAkB,QAAA,kBAAAlB,UAAA,CAAAkB,QAAA,CAAAC,CAAA,aAA0C,SAAAnB,UAAA,CAAAkB,QAAA,kBAAAlB,UAAA,CAAAkB,QAAA,CAAAE,CAAA,aACD;IAGjB9B,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAS,UAAA,SAAAC,UAAA,CAAAqB,WAAA,CAAyB;;;;;IAP3D/B,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAyB,UAAA,IAAAO,sDAAA,qBAI8C;IAUhDhC,EAAA,CAAAI,YAAA,EAAM;;;;IAdwBJ,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAS,UAAA,YAAAF,OAAA,CAAA0B,QAAA,CAAgB;;;;;IAqC9CjC,EADF,CAAAC,cAAA,cAAkD,eACtB;IAAAD,EAAA,CAAAG,MAAA,GAAwC;IACpEH,EADoE,CAAAI,YAAA,EAAO,EACrE;;;;;IADsBJ,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAkC,kBAAA,KAAApB,MAAA,CAAAqB,gBAAA,CAAA5B,OAAA,CAAA6B,KAAA,YAAwC;;;;;IAYhEpC,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,SAAA,cAA0E;IAExEF,EADF,CAAAC,cAAA,cAA6B,eACA;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAEhEH,EAFgE,CAAAI,YAAA,EAAO,EAC/D,EACF;;;;;IALCJ,EAAA,CAAAK,SAAA,EAAqB;IAACL,EAAtB,CAAAS,UAAA,QAAA4B,UAAA,CAAA1B,KAAA,EAAAX,EAAA,CAAAY,aAAA,CAAqB,QAAAyB,UAAA,CAAAxB,IAAA,CAAqB;IAElBb,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAA+B,UAAA,CAAAxB,IAAA,CAAkB;IACjBb,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAQ,MAAA,CAAAC,WAAA,CAAAsB,UAAA,CAAArB,KAAA,EAAgC;;;;;;IALlEhB,EADF,CAAAC,cAAA,cAAiF,cACjD;IAC5BD,EAAA,CAAAyB,UAAA,IAAAa,mDAAA,kBAAgF;IAOlFtC,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,cAA8B,iBACwC;IAAnCD,EAAA,CAAAiB,UAAA,mBAAAsB,sEAAA;MAAAvC,EAAA,CAAAmB,aAAA,CAAAqB,GAAA;MAAA,MAAAjC,OAAA,GAAAP,EAAA,CAAAsB,aAAA,GAAAD,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAA2B,MAAA,CAAAlC,OAAA,CAAA0B,QAAA,CAAqB,CAAC,EAAE;IAAA,EAAC;IACjEjC,EAAA,CAAAE,SAAA,YAA2B;IAC3BF,EAAA,CAAAG,MAAA,gBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,iBAAgF;IAA1CD,EAAA,CAAAiB,UAAA,mBAAAyB,sEAAA;MAAA1C,EAAA,CAAAmB,aAAA,CAAAqB,GAAA;MAAA,MAAAjC,OAAA,GAAAP,EAAA,CAAAsB,aAAA,GAAAD,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAA6B,aAAA,CAAApC,OAAA,CAAA0B,QAAA,CAA4B,CAAC,EAAE;IAAA,EAAC;IAC7EjC,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAwE;IAAtCD,EAAA,CAAAiB,UAAA,mBAAA2B,uEAAA;MAAA5C,EAAA,CAAAmB,aAAA,CAAAqB,GAAA;MAAA,MAAAjC,OAAA,GAAAP,EAAA,CAAAsB,aAAA,GAAAD,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAA+B,SAAA,CAAAtC,OAAA,CAAA0B,QAAA,CAAwB,CAAC,EAAE;IAAA,EAAC;IACrEjC,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAAG,MAAA,qBACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;IAtBuBJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAS,UAAA,YAAAF,OAAA,CAAA0B,QAAA,CAAAa,KAAA,OAA4B;;;;;IA+BnD9C,EADF,CAAAC,cAAA,cAAuE,eACtC;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC5DJ,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAC/CH,EAD+C,CAAAI,YAAA,EAAO,EAChD;;;;IAF2BJ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,iBAAA,CAAAyC,WAAA,CAAAC,QAAA,CAAsB;IAC1BhD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAyC,WAAA,CAAAE,IAAA,CAAkB;;;;;;IANjDjD,EADF,CAAAC,cAAA,cAAgF,iBACb;IAA/BD,EAAA,CAAAiB,UAAA,mBAAAiC,sEAAA;MAAAlD,EAAA,CAAAmB,aAAA,CAAAgC,GAAA;MAAA,MAAA5C,OAAA,GAAAP,EAAA,CAAAsB,aAAA,GAAAD,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAAsC,cAAA,CAAA7C,OAAA,CAAoB;IAAA,EAAC;IAC9DP,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAyB,UAAA,IAAA4B,mDAAA,kBAAuE;IAK3ErD,EADE,CAAAI,YAAA,EAAM,EACF;;;;IARFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAkC,kBAAA,eAAA3B,OAAA,CAAA+C,QAAA,CAAAC,MAAA,eACF;IAE2BvD,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAS,UAAA,YAAAF,OAAA,CAAA+C,QAAA,CAAAR,KAAA,OAA4B;;;;;;IAjGvD9C,EAHJ,CAAAC,cAAA,kBAA4F,iBAE9D,cACH;IACrBD,EAAA,CAAAE,SAAA,cAA6E;IAE3EF,EADF,CAAAC,cAAA,cAA0B,aACH;IAAAD,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClDJ,EAAA,CAAAyB,UAAA,IAAA+B,6CAAA,mBAA6C;IAEjDxD,EADE,CAAAI,YAAA,EAAM,EACF;IACNJ,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,YAAiC;IAErCF,EADE,CAAAI,YAAA,EAAS,EACF;IAGTJ,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAE,SAAA,eAAmE;IAGnEF,EAAA,CAAAyB,UAAA,KAAAgC,6CAAA,kBAAoF;IAgBtFzD,EAAA,CAAAI,YAAA,EAAM;IAKFJ,EAFJ,CAAAC,cAAA,eAA0B,eACK,kBACiE;IAA3BD,EAAA,CAAAiB,UAAA,mBAAAyC,gEAAA;MAAA,MAAAnD,OAAA,GAAAP,EAAA,CAAAmB,aAAA,CAAAwC,GAAA,EAAAtC,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAA8C,UAAA,CAAArD,OAAA,CAAgB;IAAA,EAAC;IACzFP,EAAA,CAAAE,SAAA,SAAgE;IAClEF,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAyE;IAAlCD,EAAA,CAAAiB,UAAA,mBAAA4C,gEAAA;MAAA,MAAAtD,OAAA,GAAAP,EAAA,CAAAmB,aAAA,CAAAwC,GAAA,EAAAtC,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAAgD,iBAAA,CAAAvD,OAAA,CAAuB;IAAA,EAAC;IACtEP,EAAA,CAAAE,SAAA,aAA8B;IAChCF,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA+D;IAA1BD,EAAA,CAAAiB,UAAA,mBAAA8C,gEAAA;MAAA,MAAAxD,OAAA,GAAAP,EAAA,CAAAmB,aAAA,CAAAwC,GAAA,EAAAtC,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAAkD,SAAA,CAAAzD,OAAA,CAAe;IAAA,EAAC;IAC5DP,EAAA,CAAAE,SAAA,aAAkC;IAEtCF,EADE,CAAAI,YAAA,EAAS,EACL;IACNJ,EAAA,CAAAC,cAAA,kBAA4F;IAA3BD,EAAA,CAAAiB,UAAA,mBAAAgD,gEAAA;MAAA,MAAA1D,OAAA,GAAAP,EAAA,CAAAmB,aAAA,CAAAwC,GAAA,EAAAtC,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAAoD,UAAA,CAAA3D,OAAA,CAAgB;IAAA,EAAC;IACzFP,EAAA,CAAAE,SAAA,SAAsE;IAE1EF,EADE,CAAAI,YAAA,EAAS,EACL;IAGNJ,EAAA,CAAAyB,UAAA,KAAA0C,6CAAA,kBAAkD;IAMhDnE,EADF,CAAAC,cAAA,eAA0B,gBACD;IAAAD,EAAA,CAAAG,MAAA,IAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACtDJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAG,MAAA,IAAkB;IAC/CH,EAD+C,CAAAI,YAAA,EAAO,EAChD;IA8BNJ,EA3BA,CAAAyB,UAAA,KAAA2C,6CAAA,mBAAiF,KAAAC,6CAAA,kBA2BD;IAahFrE,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAIJJ,EADF,CAAAC,cAAA,eAAiC,iBAEkC;IAA1DD,EAAA,CAAAsE,gBAAA,2BAAAC,uEAAAC,MAAA;MAAAxE,EAAA,CAAAmB,aAAA,CAAAwC,GAAA;MAAA,MAAA7C,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAyE,kBAAA,CAAA3D,MAAA,CAAA4D,UAAA,EAAAF,MAAA,MAAA1D,MAAA,CAAA4D,UAAA,GAAAF,MAAA;MAAA,OAAAxE,EAAA,CAAAuB,WAAA,CAAAiD,MAAA;IAAA,EAAwB;IAACxE,EAAA,CAAAiB,UAAA,yBAAA0D,qEAAA;MAAA,MAAApE,OAAA,GAAAP,EAAA,CAAAmB,aAAA,CAAAwC,GAAA,EAAAtC,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAeT,MAAA,CAAA8D,UAAA,CAAArE,OAAA,CAAgB;IAAA,EAAC;IADhEP,EAAA,CAAAI,YAAA,EACiE;IACjEJ,EAAA,CAAAC,cAAA,kBACuD;IADtBD,EAAA,CAAAiB,UAAA,mBAAA4D,gEAAA;MAAA,MAAAtE,OAAA,GAAAP,EAAA,CAAAmB,aAAA,CAAAwC,GAAA,EAAAtC,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAA8D,UAAA,CAAArE,OAAA,CAAgB;IAAA,EAAC;IACJP,EAAA,CAAAG,MAAA,YAAI;IAE/DH,EAF+D,CAAAI,YAAA,EAAS,EAChE,EACE;;;;;IAnHCJ,EAAA,CAAAK,SAAA,GAAwB;IAACL,EAAzB,CAAAS,UAAA,QAAAF,OAAA,CAAAuE,IAAA,CAAAC,MAAA,EAAA/E,EAAA,CAAAY,aAAA,CAAwB,QAAAL,OAAA,CAAAuE,IAAA,CAAA9B,QAAA,CAA2B;IAEjChD,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAuE,IAAA,CAAA9B,QAAA,CAAwB;IACrBhD,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAAS,UAAA,SAAAF,OAAA,CAAAC,QAAA,CAAmB;IAU1CR,EAAA,CAAAK,SAAA,GAAqB;IAACL,EAAtB,CAAAS,UAAA,QAAAF,OAAA,CAAAyE,QAAA,EAAAhF,EAAA,CAAAY,aAAA,CAAqB,QAAAL,OAAA,CAAA0E,OAAA,CAAqB;IAGZjF,EAAA,CAAAK,SAAA,EAA+C;IAA/CL,EAAA,CAAAS,UAAA,SAAAF,OAAA,CAAA0B,QAAA,IAAA1B,OAAA,CAAA0B,QAAA,CAAAsB,MAAA,KAA+C;IAqB5CvD,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAkF,WAAA,UAAA3E,OAAA,CAAA4E,OAAA,CAA4B;IAC3DnF,EAAA,CAAAK,SAAA,EAAwD;IAAxDL,EAAA,CAAAoF,UAAA,CAAA7E,OAAA,CAAA4E,OAAA,mCAAwD;IAS3BnF,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAkF,WAAA,UAAA3E,OAAA,CAAA8E,OAAA,CAA4B;IAC3DrF,EAAA,CAAAK,SAAA,EAA8D;IAA9DL,EAAA,CAAAoF,UAAA,CAAA7E,OAAA,CAAA8E,OAAA,yCAA8D;IAKzCrF,EAAA,CAAAK,SAAA,EAAoB;IAApBL,EAAA,CAAAS,UAAA,SAAAF,OAAA,CAAA6B,KAAA,KAAoB;IAMvBpC,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAuE,IAAA,CAAA9B,QAAA,CAAwB;IACpBhD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAA0E,OAAA,CAAkB;IAIfjF,EAAA,CAAAK,SAAA,EAA+C;IAA/CL,EAAA,CAAAS,UAAA,SAAAF,OAAA,CAAA0B,QAAA,IAAA1B,OAAA,CAAA0B,QAAA,CAAAsB,MAAA,KAA+C;IA2BhDvD,EAAA,CAAAK,SAAA,EAA+C;IAA/CL,EAAA,CAAAS,UAAA,SAAAF,OAAA,CAAA+C,QAAA,IAAA/C,OAAA,CAAA+C,QAAA,CAAAC,MAAA,KAA+C;IAc5EvD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAkC,kBAAA,MAAApB,MAAA,CAAAwE,UAAA,CAAA/E,OAAA,CAAAgF,SAAA,OACF;IAKSvF,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAwF,gBAAA,YAAA1E,MAAA,CAAA4D,UAAA,CAAwB;IAEvB1E,EAAA,CAAAK,SAAA,EAA8C;IAA9CL,EAAA,CAAAS,UAAA,cAAAK,MAAA,CAAA4D,UAAA,KAAA5D,MAAA,CAAA4D,UAAA,CAAAe,IAAA,GAA8C;;;;;IAS5DzF,EAAA,CAAAC,cAAA,cAA8C;IAC5CD,EAAA,CAAAE,SAAA,cAAoF;IAElFF,EADF,CAAAC,cAAA,cAA0B,aACK;IAAAD,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5DJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAG,MAAA,GAA0B;IACvDH,EADuD,CAAAI,YAAA,EAAO,EACxD;IACNJ,EAAA,CAAAC,cAAA,kBAA2B;IAAAD,EAAA,CAAAG,MAAA,aAAM;IACnCH,EADmC,CAAAI,YAAA,EAAS,EACtC;;;;IANCJ,EAAA,CAAAK,SAAA,EAA0B;IAACL,EAA3B,CAAAS,UAAA,QAAAK,MAAA,CAAA4E,WAAA,CAAAX,MAAA,EAAA/E,EAAA,CAAAY,aAAA,CAA0B,QAAAE,MAAA,CAAA4E,WAAA,CAAA1C,QAAA,CAA6B;IAE7BhD,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,iBAAA,CAAAQ,MAAA,CAAA4E,WAAA,CAAA1C,QAAA,CAA0B;IAC5BhD,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,iBAAA,CAAAQ,MAAA,CAAA4E,WAAA,CAAAC,QAAA,CAA0B;;;;;;IAYrD3F,EAAA,CAAAC,cAAA,eAA8G;IAA/BD,EAAA,CAAAiB,UAAA,mBAAA2E,yDAAA;MAAA,MAAAC,QAAA,GAAA7F,EAAA,CAAAmB,aAAA,CAAA2E,IAAA,EAAAzE,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAAiF,cAAA,CAAAF,QAAA,CAAoB;IAAA,EAAC;IAC3G7F,EAAA,CAAAE,SAAA,eAA4E;IAE1EF,EADF,CAAAC,cAAA,eAA6B,gBACG;IAAAD,EAAA,CAAAG,MAAA,GAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,GAA6B;IAEhEH,EAFgE,CAAAI,YAAA,EAAO,EAC/D,EACF;;;;;IALCJ,EAAA,CAAAK,SAAA,EAA2B;IAACL,EAA5B,CAAAS,UAAA,QAAAoF,QAAA,CAAAG,MAAA,qBAAAH,QAAA,CAAAG,MAAA,IAAAC,GAAA,EAAAjG,EAAA,CAAAY,aAAA,CAA2B,QAAAiF,QAAA,CAAAhF,IAAA,CAAkB;IAElBb,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,iBAAA,CAAAuF,QAAA,CAAAhF,IAAA,CAAe;IACdb,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAM,iBAAA,CAAAQ,MAAA,CAAAC,WAAA,CAAA8E,QAAA,CAAA7E,KAAA,EAA6B;;;;;IAoB1DhB,EAAA,CAAAC,cAAA,gBAA2D;IAAAD,EAAA,CAAAG,MAAA,GAAwC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAA/CJ,EAAA,CAAAK,SAAA,EAAwC;IAAxCL,EAAA,CAAAM,iBAAA,CAAAQ,MAAA,CAAAC,WAAA,CAAAmF,WAAA,CAAAC,aAAA,EAAwC;;;;;;IAPzGnG,EAAA,CAAAC,cAAA,eAAkH;IAAlCD,EAAA,CAAAiB,UAAA,mBAAAmF,yDAAA;MAAA,MAAAF,WAAA,GAAAlG,EAAA,CAAAmB,aAAA,CAAAkF,IAAA,EAAAhF,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAAiF,cAAA,CAAAG,WAAA,CAAuB;IAAA,EAAC;IAC/GlG,EAAA,CAAAE,SAAA,eAAgF;IAE9EF,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACrDJ,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAErDJ,EADF,CAAAC,cAAA,eAA4B,gBACE;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACnEJ,EAAA,CAAAyB,UAAA,KAAA6E,2CAAA,oBAA2D;IAGjEtG,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;;IATCJ,EAAA,CAAAK,SAAA,EAA8B;IAACL,EAA/B,CAAAS,UAAA,QAAAyF,WAAA,CAAAF,MAAA,qBAAAE,WAAA,CAAAF,MAAA,IAAAC,GAAA,EAAAjG,EAAA,CAAAY,aAAA,CAA8B,QAAAsF,WAAA,CAAArF,IAAA,CAAqB;IAE1Bb,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAA4F,WAAA,CAAArF,IAAA,CAAkB;IACjBb,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAA4F,WAAA,CAAAK,KAAA,CAAmB;IAElBvG,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAQ,MAAA,CAAAC,WAAA,CAAAmF,WAAA,CAAAlF,KAAA,EAAgC;IAC9BhB,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAS,UAAA,SAAAyF,WAAA,CAAAC,aAAA,CAA2B;;;;;;IAc/DnG,EAAA,CAAAC,cAAA,eAAkH;IAAlCD,EAAA,CAAAiB,UAAA,mBAAAuF,yDAAA;MAAA,MAAAC,WAAA,GAAAzG,EAAA,CAAAmB,aAAA,CAAAuF,IAAA,EAAArF,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAAiF,cAAA,CAAAU,WAAA,CAAuB;IAAA,EAAC;IAC/GzG,EAAA,CAAAE,SAAA,eAAgF;IAChFF,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,SAAA,aAA2B;IAC3BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,UAAG;IACXH,EADW,CAAAI,YAAA,EAAO,EACZ;IAEJJ,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEnDJ,EADF,CAAAC,cAAA,eAA4B,YACpB;IAAAD,EAAA,CAAAE,SAAA,cAA0B;IAACF,EAAA,CAAAG,MAAA,IAAiD;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzFJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,SAAA,cAA4B;IAACF,EAAA,CAAAG,MAAA,IAAiD;IACtFH,EADsF,CAAAI,YAAA,EAAO,EACvF;IACNJ,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAG,MAAA,IAAgC;IAEjEH,EAFiE,CAAAI,YAAA,EAAO,EAChE,EACF;;;;;IAbCJ,EAAA,CAAAK,SAAA,EAA8B;IAACL,EAA/B,CAAAS,UAAA,QAAAgG,WAAA,CAAAT,MAAA,qBAAAS,WAAA,CAAAT,MAAA,IAAAC,GAAA,EAAAjG,EAAA,CAAAY,aAAA,CAA8B,QAAA6F,WAAA,CAAA5F,IAAA,CAAqB;IAM1Bb,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAmG,WAAA,CAAA5F,IAAA,CAAkB;IAEXb,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAkC,kBAAA,MAAApB,MAAA,CAAA6F,YAAA,EAAAF,WAAA,CAAAG,SAAA,kBAAAH,WAAA,CAAAG,SAAA,CAAAC,KAAA,YAAiD;IAC/C7G,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAkC,kBAAA,MAAApB,MAAA,CAAA6F,YAAA,EAAAF,WAAA,CAAAG,SAAA,kBAAAH,WAAA,CAAAG,SAAA,CAAAxE,KAAA,YAAiD;IAEzDpC,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAQ,MAAA,CAAAC,WAAA,CAAA0F,WAAA,CAAAzF,KAAA,EAAgC;;;;;;IAajEhB,EAAA,CAAAC,cAAA,eAA4G;IAAlCD,EAAA,CAAAiB,UAAA,mBAAA6F,yDAAA;MAAA,MAAAC,WAAA,GAAA/G,EAAA,CAAAmB,aAAA,CAAA6F,IAAA,EAAA3F,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAAiF,cAAA,CAAAgB,WAAA,CAAuB;IAAA,EAAC;IACzG/G,EAAA,CAAAE,SAAA,eAA+E;IAC/EF,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAG,MAAA,UAAG;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAE9BJ,EADF,CAAAC,cAAA,eAA0B,gBACG;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACtDJ,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAG,MAAA,IAAgC;IAEhEH,EAFgE,CAAAI,YAAA,EAAO,EAC/D,EACF;;;;;IAPCJ,EAAA,CAAAK,SAAA,EAA8B;IAACL,EAA/B,CAAAS,UAAA,QAAAsG,WAAA,CAAAf,MAAA,qBAAAe,WAAA,CAAAf,MAAA,IAAAC,GAAA,EAAAjG,EAAA,CAAAY,aAAA,CAA8B,QAAAmG,WAAA,CAAAlG,IAAA,CAAqB;IAG3Bb,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAyG,WAAA,CAAAlG,IAAA,CAAkB;IACjBb,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAyG,WAAA,CAAAR,KAAA,CAAmB;IACnBvG,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAQ,MAAA,CAAAC,WAAA,CAAAgG,WAAA,CAAA/F,KAAA,EAAgC;;;;;;IAahEhB,EAAA,CAAAC,cAAA,eAA4E;IAC1ED,EAAA,CAAAE,SAAA,eAAwE;IAEtEF,EADF,CAAAC,cAAA,eAA4B,gBACO;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC3DJ,EAAA,CAAAC,cAAA,gBAAkC;IAAAD,EAAA,CAAAG,MAAA,GAA4C;IAChFH,EADgF,CAAAI,YAAA,EAAO,EACjF;IACNJ,EAAA,CAAAC,cAAA,kBAAsD;IAA3BD,EAAA,CAAAiB,UAAA,mBAAAgG,4DAAA;MAAA,MAAAC,QAAA,GAAAlH,EAAA,CAAAmB,aAAA,CAAAgG,IAAA,EAAA9F,SAAA;MAAA,MAAAP,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAAsG,UAAA,CAAAF,QAAA,CAAgB;IAAA,EAAC;IAAClH,EAAA,CAAAG,MAAA,aAAM;IAC9DH,EAD8D,CAAAI,YAAA,EAAS,EACjE;;;;;IANCJ,EAAA,CAAAK,SAAA,EAAmB;IAACL,EAApB,CAAAS,UAAA,QAAAyG,QAAA,CAAAnC,MAAA,EAAA/E,EAAA,CAAAY,aAAA,CAAmB,QAAAsG,QAAA,CAAAlE,QAAA,CAAsB;IAEXhD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAA4G,QAAA,CAAAlE,QAAA,CAAmB;IAClBhD,EAAA,CAAAK,SAAA,GAA4C;IAA5CL,EAAA,CAAAkC,kBAAA,KAAApB,MAAA,CAAA6F,YAAA,CAAAO,QAAA,CAAAG,SAAA,gBAA4C;;;;;;IA1OtFrH,EAJJ,CAAAC,cAAA,aAAiD,aAExB,iBAEY;IAC/BD,EAAA,CAAAE,SAAA,2BAA6C;IAC/CF,EAAA,CAAAI,YAAA,EAAU;IAGVJ,EAAA,CAAAC,cAAA,iBAA4B;IAC1BD,EAAA,CAAAyB,UAAA,IAAA6F,sCAAA,wBAA4F;IAyHhGtH,EADE,CAAAI,YAAA,EAAU,EACN;IAGNJ,EAAA,CAAAC,cAAA,gBAAiC;IAE/BD,EAAA,CAAAyB,UAAA,IAAA8F,kCAAA,kBAA8C;IAY1CvH,EAFJ,CAAAC,cAAA,kBAAiC,cACH,UACtB;IAAAD,EAAA,CAAAG,MAAA,8BAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/BJ,EAAA,CAAAC,cAAA,kBAA6D;IAAjCD,EAAA,CAAAiB,UAAA,mBAAAuG,sDAAA;MAAAxH,EAAA,CAAAmB,aAAA,CAAAsG,GAAA;MAAA,MAAA3G,MAAA,GAAAd,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAST,MAAA,CAAA4G,oBAAA,EAAsB;IAAA,EAAC;IAAC1H,EAAA,CAAAG,MAAA,eAAO;IACtEH,EADsE,CAAAI,YAAA,EAAS,EACzE;IACNJ,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAyB,UAAA,KAAAkG,mCAAA,kBAA8G;IAQlH3H,EADE,CAAAI,YAAA,EAAM,EACE;IAKNJ,EAFJ,CAAAC,cAAA,mBAAiC,eACH,UACtB;IAAAD,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,kBAA+D;IAAAD,EAAA,CAAAG,MAAA,eAAO;IACxEH,EADwE,CAAAI,YAAA,EAAS,EAC3E;IACNJ,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAyB,UAAA,KAAAmG,mCAAA,mBAAkH;IAYtH5H,EADE,CAAAI,YAAA,EAAM,EACE;IAKNJ,EAFJ,CAAAC,cAAA,mBAAiC,eACH,UACtB;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrBJ,EAAA,CAAAC,cAAA,kBAA+D;IAAAD,EAAA,CAAAG,MAAA,eAAO;IACxEH,EADwE,CAAAI,YAAA,EAAS,EAC3E;IACNJ,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAyB,UAAA,KAAAoG,mCAAA,mBAAkH;IAgBtH7H,EADE,CAAAI,YAAA,EAAM,EACE;IAKNJ,EAFJ,CAAAC,cAAA,mBAAiC,eACH,UACtB;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrBJ,EAAA,CAAAC,cAAA,kBAA0D;IAAAD,EAAA,CAAAG,MAAA,eAAO;IACnEH,EADmE,CAAAI,YAAA,EAAS,EACtE;IACNJ,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAyB,UAAA,KAAAqG,mCAAA,mBAA4G;IAUhH9H,EADE,CAAAI,YAAA,EAAM,EACE;IAKNJ,EAFJ,CAAAC,cAAA,mBAAiC,eACH,UACtB;IAAAD,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,kBAA4B;IAAAD,EAAA,CAAAG,MAAA,eAAO;IACrCH,EADqC,CAAAI,YAAA,EAAS,EACxC;IACNJ,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAyB,UAAA,KAAAsG,mCAAA,kBAA4E;IAShF/H,EADE,CAAAI,YAAA,EAAM,EACE;IAKNJ,EAFJ,CAAAC,cAAA,eAA4B,eACA,aACZ;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACrBJ,EAAA,CAAAC,cAAA,aAAY;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACpBJ,EAAA,CAAAC,cAAA,aAAY;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACrBJ,EAAA,CAAAC,cAAA,aAAY;IAAAD,EAAA,CAAAG,MAAA,WAAG;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACnBJ,EAAA,CAAAC,cAAA,aAAY;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACpBJ,EAAA,CAAAC,cAAA,aAAY;IAAAD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACvBJ,EAAA,CAAAC,cAAA,aAAY;IAAAD,EAAA,CAAAG,MAAA,aAAK;IACnBH,EADmB,CAAAI,YAAA,EAAI,EACjB;IAEJJ,EADF,CAAAC,cAAA,eAAuB,YACf;IAAAD,EAAA,CAAAG,MAAA,4BAAe;IAI7BH,EAJ6B,CAAAI,YAAA,EAAO,EACxB,EACF,EACA,EACJ;;;;IA3P0BJ,EAAA,CAAAK,SAAA,GAAmB;IAAAL,EAAnB,CAAAS,UAAA,YAAAK,MAAA,CAAAkH,cAAA,CAAmB,iBAAAlH,MAAA,CAAAmH,aAAA,CAAsB;IA8H1CjI,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAS,UAAA,SAAAK,MAAA,CAAA4E,WAAA,CAAiB;IAgBlB1F,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAS,UAAA,YAAAK,MAAA,CAAAoH,gBAAA,CAAApF,KAAA,OAA+B;IAiB5B9C,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAS,UAAA,YAAAK,MAAA,CAAAqH,gBAAA,CAAArF,KAAA,OAA+B;IAqB/B9C,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAS,UAAA,YAAAK,MAAA,CAAAsH,gBAAA,CAAAtF,KAAA,OAA+B;IAyB/B9C,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAS,UAAA,YAAAK,MAAA,CAAAuH,WAAA,CAAAvF,KAAA,OAA0B;IAmB7B9C,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAS,UAAA,YAAAK,MAAA,CAAAwH,cAAA,CAAAxF,KAAA,OAA6B;;;ADlO7D,OAAM,MAAOyF,aAAa;EAoBxBC,YACUC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB;IAFxB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IAtBrB,KAAAR,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAO,aAAa,GAAU,EAAE;IACzB,KAAAC,UAAU,GAAU,EAAE;IACtB,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAAC,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAf,cAAc,GAAU,EAAE;IAC1B,KAAAE,gBAAgB,GAAU,EAAE;IAC5B,KAAAI,cAAc,GAAU,EAAE;IAC1B,KAAA5C,WAAW,GAAQ,IAAI;IACvB,KAAAhB,UAAU,GAAG,EAAE;IAEf;IACA,KAAAsE,gBAAgB,GAAG,KAAK;IACxB,KAAAC,gBAAgB,GAA4B,IAAI;EAM7C;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAD,eAAeA,CAAA;IACb,IAAI,CAACT,WAAW,CAACW,YAAY,CAACC,SAAS,CAACzE,IAAI,IAAG;MAC7C,IAAI,CAACiE,eAAe,GAAG,CAAC,CAACjE,IAAI;MAC7B,IAAI,CAACY,WAAW,GAAGZ,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAuE,iBAAiBA,CAAA;IACf,IAAI,CAACG,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAF,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACxB,cAAc,GAAG,CACpB;MACE2B,EAAE,EAAE,GAAG;MACP7E,IAAI,EAAE;QACJ9B,QAAQ,EAAE,kBAAkB;QAC5B+B,MAAM,EAAE;OACT;MACDC,QAAQ,EAAE,oEAAoE;MAC9EC,OAAO,EAAE,wGAAwG;MACjHzE,QAAQ,EAAE,iBAAiB;MAC3B4B,KAAK,EAAE,IAAI;MACX+C,OAAO,EAAE,KAAK;MACdE,OAAO,EAAE,KAAK;MACdE,SAAS,EAAE,IAAIqE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MACpD5H,QAAQ,EAAE,CACR;QACE0H,EAAE,EAAE,GAAG;QACP9I,IAAI,EAAE,sBAAsB;QAC5BG,KAAK,EAAE,KAAK;QACZL,KAAK,EAAE,iEAAiE;QACxEiB,QAAQ,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE;OACzB,CACF;MACDwB,QAAQ,EAAE,CACR;QAAEN,QAAQ,EAAE,aAAa;QAAEC,IAAI,EAAE;MAAoB,CAAE,EACvD;QAAED,QAAQ,EAAE,aAAa;QAAEC,IAAI,EAAE;MAA8B,CAAE;KAEpE,EACD;MACE0G,EAAE,EAAE,GAAG;MACP7E,IAAI,EAAE;QACJ9B,QAAQ,EAAE,iBAAiB;QAC3B+B,MAAM,EAAE;OACT;MACDC,QAAQ,EAAE,oEAAoE;MAC9EC,OAAO,EAAE,+FAA+F;MACxGzE,QAAQ,EAAE,cAAc;MACxB4B,KAAK,EAAE,GAAG;MACV+C,OAAO,EAAE,IAAI;MACbE,OAAO,EAAE,KAAK;MACdE,SAAS,EAAE,IAAIqE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MACpD5H,QAAQ,EAAE,CACR;QACE0H,EAAE,EAAE,GAAG;QACP9I,IAAI,EAAE,iBAAiB;QACvBG,KAAK,EAAE,MAAM;QACbL,KAAK,EAAE,oEAAoE;QAC3EiB,QAAQ,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE;OACzB,CACF;MACDwB,QAAQ,EAAE,CACR;QAAEN,QAAQ,EAAE,eAAe;QAAEC,IAAI,EAAE;MAAyB,CAAE;KAEjE,EACD;MACE0G,EAAE,EAAE,GAAG;MACP7E,IAAI,EAAE;QACJ9B,QAAQ,EAAE,cAAc;QACxB+B,MAAM,EAAE;OACT;MACDC,QAAQ,EAAE,oEAAoE;MAC9EC,OAAO,EAAE,+FAA+F;MACxGzE,QAAQ,EAAE,mBAAmB;MAC7B4B,KAAK,EAAE,GAAG;MACV+C,OAAO,EAAE,KAAK;MACdE,OAAO,EAAE,IAAI;MACbE,SAAS,EAAE,IAAIqE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MACpD5H,QAAQ,EAAE,CACR;QACE0H,EAAE,EAAE,GAAG;QACP9I,IAAI,EAAE,mBAAmB;QACzBG,KAAK,EAAE,KAAK;QACZL,KAAK,EAAE,oEAAoE;QAC3EiB,QAAQ,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE;OACzB,CACF;MACDwB,QAAQ,EAAE;KACX,CACF;EACH;EAEAmG,oBAAoBA,CAAA;IAClB,IAAI,CAACvB,gBAAgB,GAAG,CACtB;MACE4B,GAAG,EAAE,SAAS;MACdjJ,IAAI,EAAE,mBAAmB;MACzBG,KAAK,EAAE,MAAM;MACbgF,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAoE,CAAE;KACvF,EACD;MACE6D,GAAG,EAAE,SAAS;MACdjJ,IAAI,EAAE,mBAAmB;MACzBG,KAAK,EAAE,KAAK;MACZgF,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAoE,CAAE;KACvF,EACD;MACE6D,GAAG,EAAE,SAAS;MACdjJ,IAAI,EAAE,gBAAgB;MACtBG,KAAK,EAAE,KAAK;MACZgF,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAiE,CAAE;KACpF,CACF;EACH;EAEAyD,kBAAkBA,CAAA;IAChB,IAAI,CAACpB,cAAc,GAAG,CACpB;MACEtF,QAAQ,EAAE,gBAAgB;MAC1B+B,MAAM,EAAE,oEAAoE;MAC5EsC,SAAS,EAAE;KACZ,EACD;MACErE,QAAQ,EAAE,cAAc;MACxB+B,MAAM,EAAE,oEAAoE;MAC5EsC,SAAS,EAAE;KACZ,EACD;MACErE,QAAQ,EAAE,cAAc;MACxB+B,MAAM,EAAE,oEAAoE;MAC5EsC,SAAS,EAAE;KACZ,CACF;EACH;EAEM8B,YAAYA,CAAA;IAAA,IAAAY,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAI;QACFD,KAAI,CAACjB,SAAS,GAAG,IAAI;QAErB;QACA,MAAM,CAACmB,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,CAAC,SAASC,OAAO,CAACC,GAAG,CAAC,CACvDN,KAAI,CAACrB,cAAc,CAAC4B,mBAAmB,EAAE,CAACC,SAAS,EAAE,EACrDR,KAAI,CAACrB,cAAc,CAAC8B,mBAAmB,EAAE,CAACD,SAAS,EAAE,EACrDR,KAAI,CAACrB,cAAc,CAAC+B,cAAc,EAAE,CAACF,SAAS,EAAE,CACjD,CAAC;QAEFR,KAAI,CAAC5B,gBAAgB,GAAG,CAAE8B,QAAgB,EAAEhI,QAAQ,IAAKgI,QAAgB,EAAES,IAAI,IAAIT,QAAQ,IAAI,EAAE,EAAEnH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9GiH,KAAI,CAAC3B,gBAAgB,GAAG,CAAE8B,QAAgB,EAAEjI,QAAQ,IAAKiI,QAAgB,EAAEQ,IAAI,IAAIR,QAAQ,IAAI,EAAE,EAAEpH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9GiH,KAAI,CAAC1B,WAAW,GAAG,CAAE8B,QAAgB,EAAElI,QAAQ,IAAKkI,QAAgB,EAAEO,IAAI,IAAIP,QAAQ,IAAI,EAAE,EAAErH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAEzG;QACA,IAAIiH,KAAI,CAAC5B,gBAAgB,CAAC5E,MAAM,KAAK,CAAC,EAAE;UACtCwG,KAAI,CAAC5B,gBAAgB,GAAG4B,KAAI,CAACY,mBAAmB,EAAE;;QAEpD,IAAIZ,KAAI,CAAC3B,gBAAgB,CAAC7E,MAAM,KAAK,CAAC,EAAE;UACtCwG,KAAI,CAAC3B,gBAAgB,GAAG2B,KAAI,CAACY,mBAAmB,EAAE;;QAEpD,IAAIZ,KAAI,CAAC1B,WAAW,CAAC9E,MAAM,KAAK,CAAC,EAAE;UACjCwG,KAAI,CAAC1B,WAAW,GAAG0B,KAAI,CAACY,mBAAmB,EAAE;;QAG/C;QACAZ,KAAI,CAACa,cAAc,EAAE;QAErB;QACAb,KAAI,CAACc,iBAAiB,EAAE;OAEzB,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDf,KAAI,CAACiB,gBAAgB,EAAE;OACxB,SAAS;QACRjB,KAAI,CAACjB,SAAS,GAAG,KAAK;;IACvB;EACH;EAEA8B,cAAcA,CAAA;IACZ,IAAI,CAAC/B,UAAU,GAAG,CAChB;MAAEhI,IAAI,EAAE,OAAO;MAAEoK,IAAI,EAAE,eAAe;MAAEC,KAAK,EAAE;IAA0C,CAAE,EAC3F;MAAErK,IAAI,EAAE,KAAK;MAAEoK,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE;IAA0C,CAAE,EACvF;MAAErK,IAAI,EAAE,MAAM;MAAEoK,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE;IAA0C,CAAE,EACzF;MAAErK,IAAI,EAAE,aAAa;MAAEoK,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAA0C,CAAE,EAC9F;MAAErK,IAAI,EAAE,OAAO;MAAEoK,IAAI,EAAE,oBAAoB;MAAEC,KAAK,EAAE;IAA0C,CAAE,EAChG;MAAErK,IAAI,EAAE,MAAM;MAAEoK,IAAI,EAAE,qBAAqB;MAAEC,KAAK,EAAE;IAA0C,CAAE,CACjG;EACH;EAEAL,iBAAiBA,CAAA;IACf,IAAI,CAACjC,aAAa,GAAG,CACnB;MACEuC,KAAK,EAAE,4BAA4B;MACnCnG,QAAQ,EAAE,oEAAoE;MAC9EoG,SAAS,EAAE,OAAO;MAClBxE,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEzE,KAAK,EAAE;MAAG;KACtC,EACD;MACE+I,KAAK,EAAE,0BAA0B;MACjCnG,QAAQ,EAAE,oEAAoE;MAC9EoG,SAAS,EAAE,OAAO;MAClBxE,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEzE,KAAK,EAAE;MAAI;KACvC,CACF;EACH;EAEAuI,mBAAmBA,CAAA;IACjB,OAAO,CACL;MACEb,GAAG,EAAE,GAAG;MACRjJ,IAAI,EAAE,sBAAsB;MAC5B0F,KAAK,EAAE,eAAe;MACtBvF,KAAK,EAAE,KAAK;MACZmF,aAAa,EAAE,MAAM;MACrBkF,QAAQ,EAAE,EAAE;MACZrF,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAiE,CAAE,CAAC;MACpFqF,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpC5E,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEzE,KAAK,EAAE;MAAG;KACtC,EACD;MACE0H,GAAG,EAAE,GAAG;MACRjJ,IAAI,EAAE,iBAAiB;MACvB0F,KAAK,EAAE,UAAU;MACjBvF,KAAK,EAAE,MAAM;MACbmF,aAAa,EAAE,MAAM;MACrBkF,QAAQ,EAAE,EAAE;MACZrF,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAoE,CAAE,CAAC;MACvFqF,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAE,CAAE;MACnC5E,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEzE,KAAK,EAAE;MAAI;KACvC,EACD;MACE0H,GAAG,EAAE,GAAG;MACRjJ,IAAI,EAAE,mBAAmB;MACzB0F,KAAK,EAAE,SAAS;MAChBvF,KAAK,EAAE,KAAK;MACZmF,aAAa,EAAE,KAAK;MACpBkF,QAAQ,EAAE,EAAE;MACZrF,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAoE,CAAE,CAAC;MACvFqF,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpC5E,SAAS,EAAE;QAAEC,KAAK,EAAE,IAAI;QAAEzE,KAAK,EAAE;MAAG;KACrC,EACD;MACE0H,GAAG,EAAE,GAAG;MACRjJ,IAAI,EAAE,oBAAoB;MAC1B0F,KAAK,EAAE,eAAe;MACtBvF,KAAK,EAAE,MAAM;MACbgF,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAoE,CAAE,CAAC;MACvFqF,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpC5E,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEzE,KAAK,EAAE;MAAI;KACvC,EACD;MACE0H,GAAG,EAAE,GAAG;MACRjJ,IAAI,EAAE,qBAAqB;MAC3B0F,KAAK,EAAE,UAAU;MACjBvF,KAAK,EAAE,MAAM;MACbgF,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAoE,CAAE,CAAC;MACvFqF,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpC5E,SAAS,EAAE;QAAEC,KAAK,EAAE,IAAI;QAAEzE,KAAK,EAAE;MAAG;KACrC,EACD;MACE0H,GAAG,EAAE,GAAG;MACRjJ,IAAI,EAAE,qBAAqB;MAC3B0F,KAAK,EAAE,eAAe;MACtBvF,KAAK,EAAE,MAAM;MACbmF,aAAa,EAAE,MAAM;MACrBkF,QAAQ,EAAE,EAAE;MACZrF,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAiE,CAAE,CAAC;MACpFqF,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAE,CAAE;MACnC5E,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEzE,KAAK,EAAE;MAAI;KACvC,EACD;MACE0H,GAAG,EAAE,GAAG;MACRjJ,IAAI,EAAE,kBAAkB;MACxB0F,KAAK,EAAE,UAAU;MACjBvF,KAAK,EAAE,MAAM;MACbgF,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAoE,CAAE,CAAC;MACvFqF,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAE,CAAE;MACnC5E,SAAS,EAAE;QAAEC,KAAK,EAAE,IAAI;QAAEzE,KAAK,EAAE;MAAG;KACrC,EACD;MACE0H,GAAG,EAAE,GAAG;MACRjJ,IAAI,EAAE,gBAAgB;MACtB0F,KAAK,EAAE,SAAS;MAChBvF,KAAK,EAAE,KAAK;MACZmF,aAAa,EAAE,KAAK;MACpBkF,QAAQ,EAAE,EAAE;MACZrF,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAoE,CAAE,CAAC;MACvFqF,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpC5E,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEzE,KAAK,EAAE;MAAG;KACtC,CACF;EACH;EAEA4I,gBAAgBA,CAAA;IACd,IAAI,CAAC7C,gBAAgB,GAAG,IAAI,CAACwC,mBAAmB,EAAE;IAClD,IAAI,CAACvC,gBAAgB,GAAG,IAAI,CAACuC,mBAAmB,EAAE;IAClD,IAAI,CAACtC,WAAW,GAAG,IAAI,CAACsC,mBAAmB,EAAE;EAC/C;EAEA;EACA5E,cAAcA,CAAC0F,OAAY;IACzB,IAAI,CAAChD,MAAM,CAACiD,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAAC3B,GAAG,IAAI2B,OAAO,CAAC9B,EAAE,CAAC,CAAC;EAC/D;EAEAgC,eAAeA,CAACC,QAAa;IAC3B,IAAI,CAACnD,MAAM,CAACiD,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAC9BG,WAAW,EAAE;QAAED,QAAQ,EAAEA,QAAQ,CAAC/K,IAAI,CAACiL,WAAW;MAAE;KACrD,CAAC;EACJ;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACtD,MAAM,CAACiD,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEA;EACA9H,UAAUA,CAACoI,IAAS;IAClBA,IAAI,CAAC7G,OAAO,GAAG,CAAC6G,IAAI,CAAC7G,OAAO;IAC5B6G,IAAI,CAAC5J,KAAK,IAAI4J,IAAI,CAAC7G,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;EACrC;EAEAjB,UAAUA,CAAC8H,IAAS;IAClBA,IAAI,CAAC3G,OAAO,GAAG,CAAC2G,IAAI,CAAC3G,OAAO;EAC9B;EAEArB,SAASA,CAACgI,IAAS;IACjB;IACAjB,OAAO,CAACkB,GAAG,CAAC,eAAe,EAAED,IAAI,CAACrC,EAAE,CAAC;EACvC;EAEA7F,iBAAiBA,CAACkI,IAAS;IACzB;IACAjB,OAAO,CAACkB,GAAG,CAAC,yBAAyB,EAAED,IAAI,CAACrC,EAAE,CAAC;EACjD;EAEA/E,UAAUA,CAACoH,IAAS;IAClB,IAAI,IAAI,CAACtH,UAAU,IAAI,IAAI,CAACA,UAAU,CAACe,IAAI,EAAE,EAAE;MAC7C,IAAI,CAACuG,IAAI,CAAC1I,QAAQ,EAAE;QAClB0I,IAAI,CAAC1I,QAAQ,GAAG,EAAE;;MAEpB0I,IAAI,CAAC1I,QAAQ,CAAC4I,IAAI,CAAC;QACjBlJ,QAAQ,EAAE,IAAI,CAAC0C,WAAW,EAAE1C,QAAQ,IAAI,MAAM;QAC9CC,IAAI,EAAE,IAAI,CAACyB,UAAU,CAACe,IAAI;OAC3B,CAAC;MACF,IAAI,CAACf,UAAU,GAAG,EAAE;;EAExB;EAEAlD,kBAAkBA,CAACiK,OAAY;IAC7BV,OAAO,CAACkB,GAAG,CAAC,uBAAuB,EAAER,OAAO,CAAC;IAC7C;IACAA,OAAO,CAAC1J,WAAW,GAAG,CAAC0J,OAAO,CAAC1J,WAAW;EAC5C;EAEA;EACAU,MAAMA,CAACgJ,OAAY;IACjBV,OAAO,CAACkB,GAAG,CAAC,UAAU,EAAER,OAAO,CAAC;IAEhC,IAAI,CAAC,IAAI,CAAC/F,WAAW,EAAE;MACrB,IAAI,CAAC+C,MAAM,CAACiD,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;QAAEG,WAAW,EAAE;UAAEM,SAAS,EAAE;QAAO;MAAE,CAAE,CAAC;MAC9E;;IAGF;IACA,IAAI,CAAClD,gBAAgB,GAAG;MACtBmD,MAAM,EAAEX,OAAO,CAACzK,KAAK;MACrBqL,SAAS,EAAE;QACTC,KAAK,EAAE,CAAC;UACN3C,EAAE,EAAE8B,OAAO,CAAC9B,EAAE;UACd9I,IAAI,EAAE4K,OAAO,CAAC5K,IAAI;UAClBG,KAAK,EAAEyK,OAAO,CAACzK,KAAK;UACpBuL,QAAQ,EAAE,CAAC;UACX5L,KAAK,EAAE8K,OAAO,CAAC9K;SAChB,CAAC;QACF6L,QAAQ,EAAEf,OAAO,CAACzK,KAAK;QACvByL,GAAG,EAAEhB,OAAO,CAACzK,KAAK,GAAG,IAAI;QACzB0L,QAAQ,EAAE,CAAC;QACXrB,QAAQ,EAAE,CAAC;QACXsB,KAAK,EAAElB,OAAO,CAACzK,KAAK,GAAIyK,OAAO,CAACzK,KAAK,GAAG;OACzC;MACD4L,WAAW,EAAE;QACX/L,IAAI,EAAE,IAAI,CAAC6E,WAAW,CAACC,QAAQ,IAAI,IAAI,CAACD,WAAW,CAAC1C,QAAQ;QAC5D6J,KAAK,EAAE,IAAI,CAACnH,WAAW,CAACmH,KAAK;QAC7BC,KAAK,EAAE,IAAI,CAACpH,WAAW,CAACoH,KAAK,IAAI;;KAEpC;IAED,IAAI,CAAC9D,gBAAgB,GAAG,IAAI;EAC9B;EAEArG,aAAaA,CAAC8I,OAAY;IACxBV,OAAO,CAACkB,GAAG,CAAC,kBAAkB,EAAER,OAAO,CAAC;IACxC;EACF;EAEA5I,SAASA,CAAC4I,OAAY;IACpBV,OAAO,CAACkB,GAAG,CAAC,cAAc,EAAER,OAAO,CAAC;IACpC;EACF;EAEArE,UAAUA,CAACtC,IAAS;IAClBiG,OAAO,CAACkB,GAAG,CAAC,cAAc,EAAEnH,IAAI,CAAC9B,QAAQ,CAAC;IAC1C;EACF;EAEA0E,oBAAoBA,CAAA;IAClB,IAAI,CAACe,MAAM,CAACiD,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAAEG,WAAW,EAAE;QAAEkB,UAAU,EAAE;MAAY;IAAE,CAAE,CAAC;EAChF;EAEA3J,cAAcA,CAAC4I,IAAS;IACtBjB,OAAO,CAACkB,GAAG,CAAC,2BAA2B,EAAED,IAAI,CAACrC,EAAE,CAAC;IACjD;EACF;EAEA;EACA5I,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAIgM,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAACpM,KAAK,CAAC;EAClB;EAEA2F,YAAYA,CAAC0G,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEApL,gBAAgBA,CAACC,KAAa;IAC5B,OAAO,IAAI,CAACuE,YAAY,CAACvE,KAAK,CAAC;EACjC;EAEAkD,UAAUA,CAACkI,IAAU;IACnB,MAAM3D,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAM6D,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAC9D,GAAG,CAAC+D,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE,IAAI,IAAI,CAAC;IAEzE,IAAIH,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC,GAAG;IACrE,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAC,GAAG;IACxE,IAAIA,aAAa,GAAG,MAAM,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAC,GAAG;IAC1E,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,MAAM,CAAC,GAAG;EACjD;EAEAxF,aAAaA,CAAC4F,KAAa,EAAE7B,IAAS;IACpC,OAAOA,IAAI,CAACrC,EAAE;EAChB;EAEA;EACAmE,kBAAkBA,CAACC,aAAkB;IACnC,IAAI,CAAC/E,gBAAgB,GAAG,KAAK;IAE7B,IAAI+E,aAAa,CAACC,MAAM,KAAK,SAAS,EAAE;MACtC;MACA,IAAI,CAACvF,MAAM,CAACiD,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE;QACzCG,WAAW,EAAE;UACXoC,OAAO,EAAEF,aAAa,CAACE,OAAO,IAAI,IAAI,CAACC,eAAe,EAAE;UACxDC,MAAM,EAAEJ,aAAa,CAACI;;OAEzB,CAAC;KACH,MAAM;MACL;MACAC,KAAK,CAAC,mCAAmC,CAAC;;EAE9C;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAACrF,gBAAgB,GAAG,KAAK;EAC/B;EAEQkF,eAAeA,CAAA;IACrB,OAAO,KAAK,GAAGtE,IAAI,CAACC,GAAG,EAAE,CAAC0D,QAAQ,EAAE;EACtC;;;uBA1fWhF,aAAa,EAAAvI,EAAA,CAAAsO,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAxO,EAAA,CAAAsO,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA1O,EAAA,CAAAsO,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAbrG,aAAa;MAAAsG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA/O,EAAA,CAAAgP,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChB1BtP,EAAA,CAAAC,cAAA,aAAsC;UAQpCD,EANA,CAAAyB,UAAA,IAAA+N,4BAAA,iBAAiD,IAAAC,4BAAA,kBAMA;UAwQjDzP,EAAA,CAAAC,cAAA,2BAIkD;UAAhDD,EADA,CAAAiB,UAAA,mBAAAyO,0DAAA;YAAA,OAASH,GAAA,CAAAlB,mBAAA,EAAqB;UAAA,EAAC,8BAAAsB,qEAAAnL,MAAA;YAAA,OACX+K,GAAA,CAAAzB,kBAAA,CAAAtJ,MAAA,CAA0B;UAAA,EAAC;UAEnDxE,EADE,CAAAI,YAAA,EAAoB,EAChB;;;UApREJ,EAAA,CAAAK,SAAA,EAAe;UAAfL,EAAA,CAAAS,UAAA,SAAA8O,GAAA,CAAAzG,SAAA,CAAe;UAMf9I,EAAA,CAAAK,SAAA,EAAgB;UAAhBL,EAAA,CAAAS,UAAA,UAAA8O,GAAA,CAAAzG,SAAA,CAAgB;UAyQpB9I,EAAA,CAAAK,SAAA,EAA8B;UAC9BL,EADA,CAAAS,UAAA,cAAA8O,GAAA,CAAAvG,gBAAA,CAA8B,gBAAAuG,GAAA,CAAAtG,gBAAA,CACE;;;qBDtQxBtJ,YAAY,EAAAiQ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAElQ,WAAW,EAAAmQ,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAErQ,YAAY,EAAA0O,EAAA,CAAA4B,UAAA,EAAErQ,uBAAuB,EAAEC,qBAAqB;MAAAqQ,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}