{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nexport { getAssetPath, setAssetPath, setNonce, setPlatformOptions } from '@stencil/core/internal/client';\nexport { c as createAnimation } from './animation.js';\nexport { a as LIFECYCLE_DID_ENTER, c as LIFECYCLE_DID_LEAVE, L as LIFECYCLE_WILL_ENTER, b as LIFECYCLE_WILL_LEAVE, d as LIFECYCLE_WILL_UNLOAD, g as getIonPageElement } from './index2.js';\nexport { iosTransitionAnimation } from './ios.transition.js';\nexport { mdTransitionAnimation } from './md.transition.js';\nexport { g as getTimeGivenProgression } from './cubic-bezier.js';\nexport { createGesture } from './index3.js';\nexport { g as getPlatforms, i as initialize, a as isPlatform } from './ionic-global.js';\nexport { c as componentOnReady } from './helpers.js';\nexport { I as IonicSafeString, g as getMode, s as setupConfig } from './config.js';\nexport { o as openURL } from './theme.js';\nexport { m as menuController } from './index4.js';\nexport { b as actionSheetController, a as alertController, l as loadingController, m as modalController, p as pickerController, c as popoverController, t as toastController } from './overlays.js';\nconst IonicSlides = opts => {\n  const {\n    swiper,\n    extendParams\n  } = opts;\n  const slidesParams = {\n    effect: undefined,\n    direction: 'horizontal',\n    initialSlide: 0,\n    loop: false,\n    parallax: false,\n    slidesPerView: 1,\n    spaceBetween: 0,\n    speed: 300,\n    slidesPerColumn: 1,\n    slidesPerColumnFill: 'column',\n    slidesPerGroup: 1,\n    centeredSlides: false,\n    slidesOffsetBefore: 0,\n    slidesOffsetAfter: 0,\n    touchEventsTarget: 'container',\n    freeMode: false,\n    freeModeMomentum: true,\n    freeModeMomentumRatio: 1,\n    freeModeMomentumBounce: true,\n    freeModeMomentumBounceRatio: 1,\n    freeModeMomentumVelocityRatio: 1,\n    freeModeSticky: false,\n    freeModeMinimumVelocity: 0.02,\n    autoHeight: false,\n    setWrapperSize: false,\n    zoom: {\n      maxRatio: 3,\n      minRatio: 1,\n      toggle: false\n    },\n    touchRatio: 1,\n    touchAngle: 45,\n    simulateTouch: true,\n    touchStartPreventDefault: false,\n    shortSwipes: true,\n    longSwipes: true,\n    longSwipesRatio: 0.5,\n    longSwipesMs: 300,\n    followFinger: true,\n    threshold: 0,\n    touchMoveStopPropagation: true,\n    touchReleaseOnEdges: false,\n    iOSEdgeSwipeDetection: false,\n    iOSEdgeSwipeThreshold: 20,\n    resistance: true,\n    resistanceRatio: 0.85,\n    watchSlidesProgress: false,\n    watchSlidesVisibility: false,\n    preventClicks: true,\n    preventClicksPropagation: true,\n    slideToClickedSlide: false,\n    loopAdditionalSlides: 0,\n    noSwiping: true,\n    runCallbacksOnInit: true,\n    coverflowEffect: {\n      rotate: 50,\n      stretch: 0,\n      depth: 100,\n      modifier: 1,\n      slideShadows: true\n    },\n    flipEffect: {\n      slideShadows: true,\n      limitRotation: true\n    },\n    cubeEffect: {\n      slideShadows: true,\n      shadow: true,\n      shadowOffset: 20,\n      shadowScale: 0.94\n    },\n    fadeEffect: {\n      crossFade: false\n    },\n    a11y: {\n      prevSlideMessage: 'Previous slide',\n      nextSlideMessage: 'Next slide',\n      firstSlideMessage: 'This is the first slide',\n      lastSlideMessage: 'This is the last slide'\n    }\n  };\n  if (swiper.pagination) {\n    slidesParams.pagination = {\n      type: 'bullets',\n      clickable: false,\n      hideOnClick: false\n    };\n  }\n  if (swiper.scrollbar) {\n    slidesParams.scrollbar = {\n      hide: true\n    };\n  }\n  extendParams(slidesParams);\n};\nexport { IonicSlides };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}