{"ast": null, "code": "export const shopRoutes = [{\n  path: '',\n  loadComponent: () => import('./pages/shop/shop.component').then(m => m.ShopComponent)\n}, {\n  path: 'product/:id',\n  loadComponent: () => import('./pages/product-detail/product-detail.component').then(m => m.ProductDetailComponent)\n}, {\n  path: 'category/:category',\n  loadComponent: () => import('./pages/category/category.component').then(m => m.CategoryComponent)\n}, {\n  path: 'cart',\n  loadComponent: () => import('./pages/cart/cart.component').then(m => m.CartComponent)\n}, {\n  path: 'checkout',\n  loadComponent: () => import('./pages/checkout/checkout.component').then(m => m.CheckoutComponent)\n}, {\n  path: 'wishlist',\n  loadComponent: () => import('./pages/wishlist/wishlist.component').then(m => m.WishlistComponent)\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}