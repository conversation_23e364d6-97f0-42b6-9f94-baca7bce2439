{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst transitionEndAsync = (el, expectedDuration = 0) => {\n  return new Promise(resolve => {\n    transitionEnd(el, expectedDuration, resolve);\n  });\n};\n/**\n * Allows developer to wait for a transition\n * to finish and fallback to a timer if the\n * transition is cancelled or otherwise\n * never finishes. Also see transitionEndAsync\n * which is an await-able version of this.\n */\nconst transitionEnd = (el, expectedDuration = 0, callback) => {\n  let unRegTrans;\n  let animationTimeout;\n  const opts = {\n    passive: true\n  };\n  const ANIMATION_FALLBACK_TIMEOUT = 500;\n  const unregister = () => {\n    if (unRegTrans) {\n      unRegTrans();\n    }\n  };\n  const onTransitionEnd = ev => {\n    if (ev === undefined || el === ev.target) {\n      unregister();\n      callback(ev);\n    }\n  };\n  if (el) {\n    el.addEventListener('webkitTransitionEnd', onTransitionEnd, opts);\n    el.addEventListener('transitionend', onTransitionEnd, opts);\n    animationTimeout = setTimeout(onTransitionEnd, expectedDuration + ANIMATION_FALLBACK_TIMEOUT);\n    unRegTrans = () => {\n      if (animationTimeout !== undefined) {\n        clearTimeout(animationTimeout);\n        animationTimeout = undefined;\n      }\n      el.removeEventListener('webkitTransitionEnd', onTransitionEnd, opts);\n      el.removeEventListener('transitionend', onTransitionEnd, opts);\n    };\n  }\n  return unregister;\n};\n/**\n * Waits for a component to be ready for\n * both custom element and non-custom element builds.\n * If non-custom element build, el.componentOnReady\n * will be used.\n * For custom element builds, we wait a frame\n * so that the inner contents of the component\n * have a chance to render.\n *\n * Use this utility rather than calling\n * el.componentOnReady yourself.\n */\nconst componentOnReady = (el, callback) => {\n  if (el.componentOnReady) {\n    // eslint-disable-next-line custom-rules/no-component-on-ready-method\n    el.componentOnReady().then(resolvedEl => callback(resolvedEl));\n  } else {\n    raf(() => callback(el));\n  }\n};\n/**\n * This functions checks if a Stencil component is using\n * the lazy loaded build of Stencil. Returns `true` if\n * the component is lazy loaded. Returns `false` otherwise.\n */\nconst hasLazyBuild = stencilEl => {\n  return stencilEl.componentOnReady !== undefined;\n};\n/**\n * Elements inside of web components sometimes need to inherit global attributes\n * set on the host. For example, the inner input in `ion-input` should inherit\n * the `title` attribute that developers set directly on `ion-input`. This\n * helper function should be called in componentWillLoad and assigned to a variable\n * that is later used in the render function.\n *\n * This does not need to be reactive as changing attributes on the host element\n * does not trigger a re-render.\n */\nconst inheritAttributes = (el, attributes = []) => {\n  const attributeObject = {};\n  attributes.forEach(attr => {\n    if (el.hasAttribute(attr)) {\n      const value = el.getAttribute(attr);\n      if (value !== null) {\n        attributeObject[attr] = el.getAttribute(attr);\n      }\n      el.removeAttribute(attr);\n    }\n  });\n  return attributeObject;\n};\n/**\n * List of available ARIA attributes + `role`.\n * Removed deprecated attributes.\n * https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes\n */\nconst ariaAttributes = ['role', 'aria-activedescendant', 'aria-atomic', 'aria-autocomplete', 'aria-braillelabel', 'aria-brailleroledescription', 'aria-busy', 'aria-checked', 'aria-colcount', 'aria-colindex', 'aria-colindextext', 'aria-colspan', 'aria-controls', 'aria-current', 'aria-describedby', 'aria-description', 'aria-details', 'aria-disabled', 'aria-errormessage', 'aria-expanded', 'aria-flowto', 'aria-haspopup', 'aria-hidden', 'aria-invalid', 'aria-keyshortcuts', 'aria-label', 'aria-labelledby', 'aria-level', 'aria-live', 'aria-multiline', 'aria-multiselectable', 'aria-orientation', 'aria-owns', 'aria-placeholder', 'aria-posinset', 'aria-pressed', 'aria-readonly', 'aria-relevant', 'aria-required', 'aria-roledescription', 'aria-rowcount', 'aria-rowindex', 'aria-rowindextext', 'aria-rowspan', 'aria-selected', 'aria-setsize', 'aria-sort', 'aria-valuemax', 'aria-valuemin', 'aria-valuenow', 'aria-valuetext'];\n/**\n * Returns an array of aria attributes that should be copied from\n * the shadow host element to a target within the light DOM.\n * @param el The element that the attributes should be copied from.\n * @param ignoreList The list of aria-attributes to ignore reflecting and removing from the host.\n * Use this in instances where we manually specify aria attributes on the `<Host>` element.\n */\nconst inheritAriaAttributes = (el, ignoreList) => {\n  let attributesToInherit = ariaAttributes;\n  if (ignoreList && ignoreList.length > 0) {\n    attributesToInherit = attributesToInherit.filter(attr => !ignoreList.includes(attr));\n  }\n  return inheritAttributes(el, attributesToInherit);\n};\nconst addEventListener = (el, eventName, callback, opts) => {\n  var _a;\n  if (typeof window !== 'undefined') {\n    const win = window;\n    const config = (_a = win === null || win === void 0 ? void 0 : win.Ionic) === null || _a === void 0 ? void 0 : _a.config;\n    if (config) {\n      const ael = config.get('_ael');\n      if (ael) {\n        return ael(el, eventName, callback, opts);\n      } else if (config._ael) {\n        return config._ael(el, eventName, callback, opts);\n      }\n    }\n  }\n  return el.addEventListener(eventName, callback, opts);\n};\nconst removeEventListener = (el, eventName, callback, opts) => {\n  var _a;\n  if (typeof window !== 'undefined') {\n    const win = window;\n    const config = (_a = win === null || win === void 0 ? void 0 : win.Ionic) === null || _a === void 0 ? void 0 : _a.config;\n    if (config) {\n      const rel = config.get('_rel');\n      if (rel) {\n        return rel(el, eventName, callback, opts);\n      } else if (config._rel) {\n        return config._rel(el, eventName, callback, opts);\n      }\n    }\n  }\n  return el.removeEventListener(eventName, callback, opts);\n};\n/**\n * Gets the root context of a shadow dom element\n * On newer browsers this will be the shadowRoot,\n * but for older browser this may just be the\n * element itself.\n *\n * Useful for whenever you need to explicitly\n * do \"myElement.shadowRoot!.querySelector(...)\".\n */\nconst getElementRoot = (el, fallback = el) => {\n  return el.shadowRoot || fallback;\n};\n/**\n * Patched version of requestAnimationFrame that avoids ngzone\n * Use only when you know ngzone should not run\n */\nconst raf = h => {\n  if (typeof __zone_symbol__requestAnimationFrame === 'function') {\n    return __zone_symbol__requestAnimationFrame(h);\n  }\n  if (typeof requestAnimationFrame === 'function') {\n    return requestAnimationFrame(h);\n  }\n  return setTimeout(h);\n};\nconst hasShadowDom = el => {\n  return !!el.shadowRoot && !!el.attachShadow;\n};\nconst findItemLabel = componentEl => {\n  const itemEl = componentEl.closest('ion-item');\n  if (itemEl) {\n    return itemEl.querySelector('ion-label');\n  }\n  return null;\n};\nconst focusVisibleElement = el => {\n  el.focus();\n  /**\n   * When programmatically focusing an element,\n   * the focus-visible utility will not run because\n   * it is expecting a keyboard event to have triggered this;\n   * however, there are times when we need to manually control\n   * this behavior so we call the `setFocus` method on ion-app\n   * which will let us explicitly set the elements to focus.\n   */\n  if (el.classList.contains('ion-focusable')) {\n    const app = el.closest('ion-app');\n    if (app) {\n      app.setFocus([el]);\n    }\n  }\n};\n/**\n * This method is used for Ionic's input components that use Shadow DOM. In\n * order to properly label the inputs to work with screen readers, we need\n * to get the text content of the label outside of the shadow root and pass\n * it to the input inside of the shadow root.\n *\n * Referencing label elements by id from outside of the component is\n * impossible due to the shadow boundary, read more here:\n * https://developer.salesforce.com/blogs/2020/01/accessibility-for-web-components.html\n *\n * @param componentEl The shadow element that needs the aria label\n * @param inputId The unique identifier for the input\n */\nconst getAriaLabel = (componentEl, inputId) => {\n  let labelText;\n  // If the user provides their own label via the aria-labelledby attr\n  // we should use that instead of looking for an ion-label\n  const labelledBy = componentEl.getAttribute('aria-labelledby');\n  // Grab the id off of the component in case they are using\n  // a custom label using the label element\n  const componentId = componentEl.id;\n  let labelId = labelledBy !== null && labelledBy.trim() !== '' ? labelledBy : inputId + '-lbl';\n  let label = labelledBy !== null && labelledBy.trim() !== '' ? document.getElementById(labelledBy) : findItemLabel(componentEl);\n  if (label) {\n    if (labelledBy === null) {\n      label.id = labelId;\n    }\n    labelText = label.textContent;\n    label.setAttribute('aria-hidden', 'true');\n    // if there is no label, check to see if the user has provided\n    // one by setting an id on the component and using the label element\n  } else if (componentId.trim() !== '') {\n    label = document.querySelector(`label[for=\"${componentId}\"]`);\n    if (label) {\n      if (label.id !== '') {\n        labelId = label.id;\n      } else {\n        label.id = labelId = `${componentId}-lbl`;\n      }\n      labelText = label.textContent;\n    }\n  }\n  return {\n    label,\n    labelId,\n    labelText\n  };\n};\n/**\n * This method is used to add a hidden input to a host element that contains\n * a Shadow DOM. It does not add the input inside of the Shadow root which\n * allows it to be picked up inside of forms. It should contain the same\n * values as the host element.\n *\n * @param always Add a hidden input even if the container does not use Shadow\n * @param container The element where the input will be added\n * @param name The name of the input\n * @param value The value of the input\n * @param disabled If true, the input is disabled\n */\nconst renderHiddenInput = (always, container, name, value, disabled) => {\n  if (always || hasShadowDom(container)) {\n    let input = container.querySelector('input.aux-input');\n    if (!input) {\n      input = container.ownerDocument.createElement('input');\n      input.type = 'hidden';\n      input.classList.add('aux-input');\n      container.appendChild(input);\n    }\n    input.disabled = disabled;\n    input.name = name;\n    input.value = value || '';\n  }\n};\nconst clamp = (min, n, max) => {\n  return Math.max(min, Math.min(n, max));\n};\nconst assert = (actual, reason) => {\n  if (!actual) {\n    const message = 'ASSERT: ' + reason;\n    console.error(message);\n    debugger; // eslint-disable-line\n    throw new Error(message);\n  }\n};\nconst now = ev => {\n  return ev.timeStamp || Date.now();\n};\nconst pointerCoord = ev => {\n  // get X coordinates for either a mouse click\n  // or a touch depending on the given event\n  if (ev) {\n    const changedTouches = ev.changedTouches;\n    if (changedTouches && changedTouches.length > 0) {\n      const touch = changedTouches[0];\n      return {\n        x: touch.clientX,\n        y: touch.clientY\n      };\n    }\n    if (ev.pageX !== undefined) {\n      return {\n        x: ev.pageX,\n        y: ev.pageY\n      };\n    }\n  }\n  return {\n    x: 0,\n    y: 0\n  };\n};\n/**\n * @hidden\n * Given a side, return if it should be on the end\n * based on the value of dir\n * @param side the side\n * @param isRTL whether the application dir is rtl\n */\nconst isEndSide = side => {\n  const isRTL = document.dir === 'rtl';\n  switch (side) {\n    case 'start':\n      return isRTL;\n    case 'end':\n      return !isRTL;\n    default:\n      throw new Error(`\"${side}\" is not a valid value for [side]. Use \"start\" or \"end\" instead.`);\n  }\n};\nconst debounceEvent = (event, wait) => {\n  const original = event._original || event;\n  return {\n    _original: event,\n    emit: debounce(original.emit.bind(original), wait)\n  };\n};\nconst debounce = (func, wait = 0) => {\n  let timer;\n  return (...args) => {\n    clearTimeout(timer);\n    timer = setTimeout(func, wait, ...args);\n  };\n};\n/**\n * Check whether the two string maps are shallow equal.\n *\n * undefined is treated as an empty map.\n *\n * @returns whether the keys are the same and the values are shallow equal.\n */\nconst shallowEqualStringMap = (map1, map2) => {\n  map1 !== null && map1 !== void 0 ? map1 : map1 = {};\n  map2 !== null && map2 !== void 0 ? map2 : map2 = {};\n  if (map1 === map2) {\n    return true;\n  }\n  const keys1 = Object.keys(map1);\n  if (keys1.length !== Object.keys(map2).length) {\n    return false;\n  }\n  for (const k1 of keys1) {\n    if (!(k1 in map2)) {\n      return false;\n    }\n    if (map1[k1] !== map2[k1]) {\n      return false;\n    }\n  }\n  return true;\n};\nexport { addEventListener as a, removeEventListener as b, componentOnReady as c, inheritAttributes as d, renderHiddenInput as e, focusVisibleElement as f, getElementRoot as g, hasShadowDom as h, inheritAriaAttributes as i, getAriaLabel as j, findItemLabel as k, hasLazyBuild as l, clamp as m, debounceEvent as n, isEndSide as o, assert as p, debounce as q, raf as r, shallowEqualStringMap as s, transitionEndAsync as t, now as u, pointerCoord as v };", "map": {"version": 3, "names": ["transitionEndAsync", "el", "expectedDuration", "Promise", "resolve", "transitionEnd", "callback", "unRegTrans", "animationTimeout", "opts", "passive", "ANIMATION_FALLBACK_TIMEOUT", "unregister", "onTransitionEnd", "ev", "undefined", "target", "addEventListener", "setTimeout", "clearTimeout", "removeEventListener", "componentOnReady", "then", "resolvedEl", "raf", "hasLazyBuild", "stencilEl", "inheritAttributes", "attributes", "attributeObject", "for<PERSON>ach", "attr", "hasAttribute", "value", "getAttribute", "removeAttribute", "ariaAttributes", "inheritAriaAttributes", "ignoreList", "attributesToInherit", "length", "filter", "includes", "eventName", "_a", "window", "win", "config", "<PERSON><PERSON>", "ael", "get", "_ael", "rel", "_rel", "getElementRoot", "fallback", "shadowRoot", "h", "__zone_symbol__requestAnimationFrame", "requestAnimationFrame", "hasShadowDom", "attachShadow", "findItemLabel", "componentEl", "itemEl", "closest", "querySelector", "focusVisibleElement", "focus", "classList", "contains", "app", "setFocus", "getAriaLabel", "inputId", "labelText", "labelledBy", "componentId", "id", "labelId", "trim", "label", "document", "getElementById", "textContent", "setAttribute", "renderHiddenInput", "always", "container", "name", "disabled", "input", "ownerDocument", "createElement", "type", "add", "append<PERSON><PERSON><PERSON>", "clamp", "min", "n", "max", "Math", "assert", "actual", "reason", "message", "console", "error", "Error", "now", "timeStamp", "Date", "pointerCoord", "changedTouches", "touch", "x", "clientX", "y", "clientY", "pageX", "pageY", "isEndSide", "side", "isRTL", "dir", "debounceEvent", "event", "wait", "original", "_original", "emit", "debounce", "bind", "func", "timer", "args", "shallowEqualStringMap", "map1", "map2", "keys1", "Object", "keys", "k1", "a", "b", "c", "d", "e", "f", "g", "i", "j", "k", "l", "m", "o", "p", "q", "r", "s", "t", "u", "v"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@ionic/core/components/helpers.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst transitionEndAsync = (el, expectedDuration = 0) => {\n    return new Promise((resolve) => {\n        transitionEnd(el, expectedDuration, resolve);\n    });\n};\n/**\n * Allows developer to wait for a transition\n * to finish and fallback to a timer if the\n * transition is cancelled or otherwise\n * never finishes. Also see transitionEndAsync\n * which is an await-able version of this.\n */\nconst transitionEnd = (el, expectedDuration = 0, callback) => {\n    let unRegTrans;\n    let animationTimeout;\n    const opts = { passive: true };\n    const ANIMATION_FALLBACK_TIMEOUT = 500;\n    const unregister = () => {\n        if (unRegTrans) {\n            unRegTrans();\n        }\n    };\n    const onTransitionEnd = (ev) => {\n        if (ev === undefined || el === ev.target) {\n            unregister();\n            callback(ev);\n        }\n    };\n    if (el) {\n        el.addEventListener('webkitTransitionEnd', onTransitionEnd, opts);\n        el.addEventListener('transitionend', onTransitionEnd, opts);\n        animationTimeout = setTimeout(onTransitionEnd, expectedDuration + ANIMATION_FALLBACK_TIMEOUT);\n        unRegTrans = () => {\n            if (animationTimeout !== undefined) {\n                clearTimeout(animationTimeout);\n                animationTimeout = undefined;\n            }\n            el.removeEventListener('webkitTransitionEnd', onTransitionEnd, opts);\n            el.removeEventListener('transitionend', onTransitionEnd, opts);\n        };\n    }\n    return unregister;\n};\n/**\n * Waits for a component to be ready for\n * both custom element and non-custom element builds.\n * If non-custom element build, el.componentOnReady\n * will be used.\n * For custom element builds, we wait a frame\n * so that the inner contents of the component\n * have a chance to render.\n *\n * Use this utility rather than calling\n * el.componentOnReady yourself.\n */\nconst componentOnReady = (el, callback) => {\n    if (el.componentOnReady) {\n        // eslint-disable-next-line custom-rules/no-component-on-ready-method\n        el.componentOnReady().then((resolvedEl) => callback(resolvedEl));\n    }\n    else {\n        raf(() => callback(el));\n    }\n};\n/**\n * This functions checks if a Stencil component is using\n * the lazy loaded build of Stencil. Returns `true` if\n * the component is lazy loaded. Returns `false` otherwise.\n */\nconst hasLazyBuild = (stencilEl) => {\n    return stencilEl.componentOnReady !== undefined;\n};\n/**\n * Elements inside of web components sometimes need to inherit global attributes\n * set on the host. For example, the inner input in `ion-input` should inherit\n * the `title` attribute that developers set directly on `ion-input`. This\n * helper function should be called in componentWillLoad and assigned to a variable\n * that is later used in the render function.\n *\n * This does not need to be reactive as changing attributes on the host element\n * does not trigger a re-render.\n */\nconst inheritAttributes = (el, attributes = []) => {\n    const attributeObject = {};\n    attributes.forEach((attr) => {\n        if (el.hasAttribute(attr)) {\n            const value = el.getAttribute(attr);\n            if (value !== null) {\n                attributeObject[attr] = el.getAttribute(attr);\n            }\n            el.removeAttribute(attr);\n        }\n    });\n    return attributeObject;\n};\n/**\n * List of available ARIA attributes + `role`.\n * Removed deprecated attributes.\n * https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes\n */\nconst ariaAttributes = [\n    'role',\n    'aria-activedescendant',\n    'aria-atomic',\n    'aria-autocomplete',\n    'aria-braillelabel',\n    'aria-brailleroledescription',\n    'aria-busy',\n    'aria-checked',\n    'aria-colcount',\n    'aria-colindex',\n    'aria-colindextext',\n    'aria-colspan',\n    'aria-controls',\n    'aria-current',\n    'aria-describedby',\n    'aria-description',\n    'aria-details',\n    'aria-disabled',\n    'aria-errormessage',\n    'aria-expanded',\n    'aria-flowto',\n    'aria-haspopup',\n    'aria-hidden',\n    'aria-invalid',\n    'aria-keyshortcuts',\n    'aria-label',\n    'aria-labelledby',\n    'aria-level',\n    'aria-live',\n    'aria-multiline',\n    'aria-multiselectable',\n    'aria-orientation',\n    'aria-owns',\n    'aria-placeholder',\n    'aria-posinset',\n    'aria-pressed',\n    'aria-readonly',\n    'aria-relevant',\n    'aria-required',\n    'aria-roledescription',\n    'aria-rowcount',\n    'aria-rowindex',\n    'aria-rowindextext',\n    'aria-rowspan',\n    'aria-selected',\n    'aria-setsize',\n    'aria-sort',\n    'aria-valuemax',\n    'aria-valuemin',\n    'aria-valuenow',\n    'aria-valuetext',\n];\n/**\n * Returns an array of aria attributes that should be copied from\n * the shadow host element to a target within the light DOM.\n * @param el The element that the attributes should be copied from.\n * @param ignoreList The list of aria-attributes to ignore reflecting and removing from the host.\n * Use this in instances where we manually specify aria attributes on the `<Host>` element.\n */\nconst inheritAriaAttributes = (el, ignoreList) => {\n    let attributesToInherit = ariaAttributes;\n    if (ignoreList && ignoreList.length > 0) {\n        attributesToInherit = attributesToInherit.filter((attr) => !ignoreList.includes(attr));\n    }\n    return inheritAttributes(el, attributesToInherit);\n};\nconst addEventListener = (el, eventName, callback, opts) => {\n    var _a;\n    if (typeof window !== 'undefined') {\n        const win = window;\n        const config = (_a = win === null || win === void 0 ? void 0 : win.Ionic) === null || _a === void 0 ? void 0 : _a.config;\n        if (config) {\n            const ael = config.get('_ael');\n            if (ael) {\n                return ael(el, eventName, callback, opts);\n            }\n            else if (config._ael) {\n                return config._ael(el, eventName, callback, opts);\n            }\n        }\n    }\n    return el.addEventListener(eventName, callback, opts);\n};\nconst removeEventListener = (el, eventName, callback, opts) => {\n    var _a;\n    if (typeof window !== 'undefined') {\n        const win = window;\n        const config = (_a = win === null || win === void 0 ? void 0 : win.Ionic) === null || _a === void 0 ? void 0 : _a.config;\n        if (config) {\n            const rel = config.get('_rel');\n            if (rel) {\n                return rel(el, eventName, callback, opts);\n            }\n            else if (config._rel) {\n                return config._rel(el, eventName, callback, opts);\n            }\n        }\n    }\n    return el.removeEventListener(eventName, callback, opts);\n};\n/**\n * Gets the root context of a shadow dom element\n * On newer browsers this will be the shadowRoot,\n * but for older browser this may just be the\n * element itself.\n *\n * Useful for whenever you need to explicitly\n * do \"myElement.shadowRoot!.querySelector(...)\".\n */\nconst getElementRoot = (el, fallback = el) => {\n    return el.shadowRoot || fallback;\n};\n/**\n * Patched version of requestAnimationFrame that avoids ngzone\n * Use only when you know ngzone should not run\n */\nconst raf = (h) => {\n    if (typeof __zone_symbol__requestAnimationFrame === 'function') {\n        return __zone_symbol__requestAnimationFrame(h);\n    }\n    if (typeof requestAnimationFrame === 'function') {\n        return requestAnimationFrame(h);\n    }\n    return setTimeout(h);\n};\nconst hasShadowDom = (el) => {\n    return !!el.shadowRoot && !!el.attachShadow;\n};\nconst findItemLabel = (componentEl) => {\n    const itemEl = componentEl.closest('ion-item');\n    if (itemEl) {\n        return itemEl.querySelector('ion-label');\n    }\n    return null;\n};\nconst focusVisibleElement = (el) => {\n    el.focus();\n    /**\n     * When programmatically focusing an element,\n     * the focus-visible utility will not run because\n     * it is expecting a keyboard event to have triggered this;\n     * however, there are times when we need to manually control\n     * this behavior so we call the `setFocus` method on ion-app\n     * which will let us explicitly set the elements to focus.\n     */\n    if (el.classList.contains('ion-focusable')) {\n        const app = el.closest('ion-app');\n        if (app) {\n            app.setFocus([el]);\n        }\n    }\n};\n/**\n * This method is used for Ionic's input components that use Shadow DOM. In\n * order to properly label the inputs to work with screen readers, we need\n * to get the text content of the label outside of the shadow root and pass\n * it to the input inside of the shadow root.\n *\n * Referencing label elements by id from outside of the component is\n * impossible due to the shadow boundary, read more here:\n * https://developer.salesforce.com/blogs/2020/01/accessibility-for-web-components.html\n *\n * @param componentEl The shadow element that needs the aria label\n * @param inputId The unique identifier for the input\n */\nconst getAriaLabel = (componentEl, inputId) => {\n    let labelText;\n    // If the user provides their own label via the aria-labelledby attr\n    // we should use that instead of looking for an ion-label\n    const labelledBy = componentEl.getAttribute('aria-labelledby');\n    // Grab the id off of the component in case they are using\n    // a custom label using the label element\n    const componentId = componentEl.id;\n    let labelId = labelledBy !== null && labelledBy.trim() !== '' ? labelledBy : inputId + '-lbl';\n    let label = labelledBy !== null && labelledBy.trim() !== '' ? document.getElementById(labelledBy) : findItemLabel(componentEl);\n    if (label) {\n        if (labelledBy === null) {\n            label.id = labelId;\n        }\n        labelText = label.textContent;\n        label.setAttribute('aria-hidden', 'true');\n        // if there is no label, check to see if the user has provided\n        // one by setting an id on the component and using the label element\n    }\n    else if (componentId.trim() !== '') {\n        label = document.querySelector(`label[for=\"${componentId}\"]`);\n        if (label) {\n            if (label.id !== '') {\n                labelId = label.id;\n            }\n            else {\n                label.id = labelId = `${componentId}-lbl`;\n            }\n            labelText = label.textContent;\n        }\n    }\n    return { label, labelId, labelText };\n};\n/**\n * This method is used to add a hidden input to a host element that contains\n * a Shadow DOM. It does not add the input inside of the Shadow root which\n * allows it to be picked up inside of forms. It should contain the same\n * values as the host element.\n *\n * @param always Add a hidden input even if the container does not use Shadow\n * @param container The element where the input will be added\n * @param name The name of the input\n * @param value The value of the input\n * @param disabled If true, the input is disabled\n */\nconst renderHiddenInput = (always, container, name, value, disabled) => {\n    if (always || hasShadowDom(container)) {\n        let input = container.querySelector('input.aux-input');\n        if (!input) {\n            input = container.ownerDocument.createElement('input');\n            input.type = 'hidden';\n            input.classList.add('aux-input');\n            container.appendChild(input);\n        }\n        input.disabled = disabled;\n        input.name = name;\n        input.value = value || '';\n    }\n};\nconst clamp = (min, n, max) => {\n    return Math.max(min, Math.min(n, max));\n};\nconst assert = (actual, reason) => {\n    if (!actual) {\n        const message = 'ASSERT: ' + reason;\n        console.error(message);\n        debugger; // eslint-disable-line\n        throw new Error(message);\n    }\n};\nconst now = (ev) => {\n    return ev.timeStamp || Date.now();\n};\nconst pointerCoord = (ev) => {\n    // get X coordinates for either a mouse click\n    // or a touch depending on the given event\n    if (ev) {\n        const changedTouches = ev.changedTouches;\n        if (changedTouches && changedTouches.length > 0) {\n            const touch = changedTouches[0];\n            return { x: touch.clientX, y: touch.clientY };\n        }\n        if (ev.pageX !== undefined) {\n            return { x: ev.pageX, y: ev.pageY };\n        }\n    }\n    return { x: 0, y: 0 };\n};\n/**\n * @hidden\n * Given a side, return if it should be on the end\n * based on the value of dir\n * @param side the side\n * @param isRTL whether the application dir is rtl\n */\nconst isEndSide = (side) => {\n    const isRTL = document.dir === 'rtl';\n    switch (side) {\n        case 'start':\n            return isRTL;\n        case 'end':\n            return !isRTL;\n        default:\n            throw new Error(`\"${side}\" is not a valid value for [side]. Use \"start\" or \"end\" instead.`);\n    }\n};\nconst debounceEvent = (event, wait) => {\n    const original = event._original || event;\n    return {\n        _original: event,\n        emit: debounce(original.emit.bind(original), wait),\n    };\n};\nconst debounce = (func, wait = 0) => {\n    let timer;\n    return (...args) => {\n        clearTimeout(timer);\n        timer = setTimeout(func, wait, ...args);\n    };\n};\n/**\n * Check whether the two string maps are shallow equal.\n *\n * undefined is treated as an empty map.\n *\n * @returns whether the keys are the same and the values are shallow equal.\n */\nconst shallowEqualStringMap = (map1, map2) => {\n    map1 !== null && map1 !== void 0 ? map1 : (map1 = {});\n    map2 !== null && map2 !== void 0 ? map2 : (map2 = {});\n    if (map1 === map2) {\n        return true;\n    }\n    const keys1 = Object.keys(map1);\n    if (keys1.length !== Object.keys(map2).length) {\n        return false;\n    }\n    for (const k1 of keys1) {\n        if (!(k1 in map2)) {\n            return false;\n        }\n        if (map1[k1] !== map2[k1]) {\n            return false;\n        }\n    }\n    return true;\n};\n\nexport { addEventListener as a, removeEventListener as b, componentOnReady as c, inheritAttributes as d, renderHiddenInput as e, focusVisibleElement as f, getElementRoot as g, hasShadowDom as h, inheritAriaAttributes as i, getAriaLabel as j, findItemLabel as k, hasLazyBuild as l, clamp as m, debounceEvent as n, isEndSide as o, assert as p, debounce as q, raf as r, shallowEqualStringMap as s, transitionEndAsync as t, now as u, pointerCoord as v };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,kBAAkB,GAAGA,CAACC,EAAE,EAAEC,gBAAgB,GAAG,CAAC,KAAK;EACrD,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;IAC5BC,aAAa,CAACJ,EAAE,EAAEC,gBAAgB,EAAEE,OAAO,CAAC;EAChD,CAAC,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAGA,CAACJ,EAAE,EAAEC,gBAAgB,GAAG,CAAC,EAAEI,QAAQ,KAAK;EAC1D,IAAIC,UAAU;EACd,IAAIC,gBAAgB;EACpB,MAAMC,IAAI,GAAG;IAAEC,OAAO,EAAE;EAAK,CAAC;EAC9B,MAAMC,0BAA0B,GAAG,GAAG;EACtC,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIL,UAAU,EAAE;MACZA,UAAU,CAAC,CAAC;IAChB;EACJ,CAAC;EACD,MAAMM,eAAe,GAAIC,EAAE,IAAK;IAC5B,IAAIA,EAAE,KAAKC,SAAS,IAAId,EAAE,KAAKa,EAAE,CAACE,MAAM,EAAE;MACtCJ,UAAU,CAAC,CAAC;MACZN,QAAQ,CAACQ,EAAE,CAAC;IAChB;EACJ,CAAC;EACD,IAAIb,EAAE,EAAE;IACJA,EAAE,CAACgB,gBAAgB,CAAC,qBAAqB,EAAEJ,eAAe,EAAEJ,IAAI,CAAC;IACjER,EAAE,CAACgB,gBAAgB,CAAC,eAAe,EAAEJ,eAAe,EAAEJ,IAAI,CAAC;IAC3DD,gBAAgB,GAAGU,UAAU,CAACL,eAAe,EAAEX,gBAAgB,GAAGS,0BAA0B,CAAC;IAC7FJ,UAAU,GAAGA,CAAA,KAAM;MACf,IAAIC,gBAAgB,KAAKO,SAAS,EAAE;QAChCI,YAAY,CAACX,gBAAgB,CAAC;QAC9BA,gBAAgB,GAAGO,SAAS;MAChC;MACAd,EAAE,CAACmB,mBAAmB,CAAC,qBAAqB,EAAEP,eAAe,EAAEJ,IAAI,CAAC;MACpER,EAAE,CAACmB,mBAAmB,CAAC,eAAe,EAAEP,eAAe,EAAEJ,IAAI,CAAC;IAClE,CAAC;EACL;EACA,OAAOG,UAAU;AACrB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,gBAAgB,GAAGA,CAACpB,EAAE,EAAEK,QAAQ,KAAK;EACvC,IAAIL,EAAE,CAACoB,gBAAgB,EAAE;IACrB;IACApB,EAAE,CAACoB,gBAAgB,CAAC,CAAC,CAACC,IAAI,CAAEC,UAAU,IAAKjB,QAAQ,CAACiB,UAAU,CAAC,CAAC;EACpE,CAAC,MACI;IACDC,GAAG,CAAC,MAAMlB,QAAQ,CAACL,EAAE,CAAC,CAAC;EAC3B;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMwB,YAAY,GAAIC,SAAS,IAAK;EAChC,OAAOA,SAAS,CAACL,gBAAgB,KAAKN,SAAS;AACnD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,iBAAiB,GAAGA,CAAC1B,EAAE,EAAE2B,UAAU,GAAG,EAAE,KAAK;EAC/C,MAAMC,eAAe,GAAG,CAAC,CAAC;EAC1BD,UAAU,CAACE,OAAO,CAAEC,IAAI,IAAK;IACzB,IAAI9B,EAAE,CAAC+B,YAAY,CAACD,IAAI,CAAC,EAAE;MACvB,MAAME,KAAK,GAAGhC,EAAE,CAACiC,YAAY,CAACH,IAAI,CAAC;MACnC,IAAIE,KAAK,KAAK,IAAI,EAAE;QAChBJ,eAAe,CAACE,IAAI,CAAC,GAAG9B,EAAE,CAACiC,YAAY,CAACH,IAAI,CAAC;MACjD;MACA9B,EAAE,CAACkC,eAAe,CAACJ,IAAI,CAAC;IAC5B;EACJ,CAAC,CAAC;EACF,OAAOF,eAAe;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMO,cAAc,GAAG,CACnB,MAAM,EACN,uBAAuB,EACvB,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,6BAA6B,EAC7B,WAAW,EACX,cAAc,EACd,eAAe,EACf,eAAe,EACf,mBAAmB,EACnB,cAAc,EACd,eAAe,EACf,cAAc,EACd,kBAAkB,EAClB,kBAAkB,EAClB,cAAc,EACd,eAAe,EACf,mBAAmB,EACnB,eAAe,EACf,aAAa,EACb,eAAe,EACf,aAAa,EACb,cAAc,EACd,mBAAmB,EACnB,YAAY,EACZ,iBAAiB,EACjB,YAAY,EACZ,WAAW,EACX,gBAAgB,EAChB,sBAAsB,EACtB,kBAAkB,EAClB,WAAW,EACX,kBAAkB,EAClB,eAAe,EACf,cAAc,EACd,eAAe,EACf,eAAe,EACf,eAAe,EACf,sBAAsB,EACtB,eAAe,EACf,eAAe,EACf,mBAAmB,EACnB,cAAc,EACd,eAAe,EACf,cAAc,EACd,WAAW,EACX,eAAe,EACf,eAAe,EACf,eAAe,EACf,gBAAgB,CACnB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAGA,CAACpC,EAAE,EAAEqC,UAAU,KAAK;EAC9C,IAAIC,mBAAmB,GAAGH,cAAc;EACxC,IAAIE,UAAU,IAAIA,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;IACrCD,mBAAmB,GAAGA,mBAAmB,CAACE,MAAM,CAAEV,IAAI,IAAK,CAACO,UAAU,CAACI,QAAQ,CAACX,IAAI,CAAC,CAAC;EAC1F;EACA,OAAOJ,iBAAiB,CAAC1B,EAAE,EAAEsC,mBAAmB,CAAC;AACrD,CAAC;AACD,MAAMtB,gBAAgB,GAAGA,CAAChB,EAAE,EAAE0C,SAAS,EAAErC,QAAQ,EAAEG,IAAI,KAAK;EACxD,IAAImC,EAAE;EACN,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAC/B,MAAMC,GAAG,GAAGD,MAAM;IAClB,MAAME,MAAM,GAAG,CAACH,EAAE,GAAGE,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACE,KAAK,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,MAAM;IACxH,IAAIA,MAAM,EAAE;MACR,MAAME,GAAG,GAAGF,MAAM,CAACG,GAAG,CAAC,MAAM,CAAC;MAC9B,IAAID,GAAG,EAAE;QACL,OAAOA,GAAG,CAAChD,EAAE,EAAE0C,SAAS,EAAErC,QAAQ,EAAEG,IAAI,CAAC;MAC7C,CAAC,MACI,IAAIsC,MAAM,CAACI,IAAI,EAAE;QAClB,OAAOJ,MAAM,CAACI,IAAI,CAAClD,EAAE,EAAE0C,SAAS,EAAErC,QAAQ,EAAEG,IAAI,CAAC;MACrD;IACJ;EACJ;EACA,OAAOR,EAAE,CAACgB,gBAAgB,CAAC0B,SAAS,EAAErC,QAAQ,EAAEG,IAAI,CAAC;AACzD,CAAC;AACD,MAAMW,mBAAmB,GAAGA,CAACnB,EAAE,EAAE0C,SAAS,EAAErC,QAAQ,EAAEG,IAAI,KAAK;EAC3D,IAAImC,EAAE;EACN,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAC/B,MAAMC,GAAG,GAAGD,MAAM;IAClB,MAAME,MAAM,GAAG,CAACH,EAAE,GAAGE,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACE,KAAK,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,MAAM;IACxH,IAAIA,MAAM,EAAE;MACR,MAAMK,GAAG,GAAGL,MAAM,CAACG,GAAG,CAAC,MAAM,CAAC;MAC9B,IAAIE,GAAG,EAAE;QACL,OAAOA,GAAG,CAACnD,EAAE,EAAE0C,SAAS,EAAErC,QAAQ,EAAEG,IAAI,CAAC;MAC7C,CAAC,MACI,IAAIsC,MAAM,CAACM,IAAI,EAAE;QAClB,OAAON,MAAM,CAACM,IAAI,CAACpD,EAAE,EAAE0C,SAAS,EAAErC,QAAQ,EAAEG,IAAI,CAAC;MACrD;IACJ;EACJ;EACA,OAAOR,EAAE,CAACmB,mBAAmB,CAACuB,SAAS,EAAErC,QAAQ,EAAEG,IAAI,CAAC;AAC5D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6C,cAAc,GAAGA,CAACrD,EAAE,EAAEsD,QAAQ,GAAGtD,EAAE,KAAK;EAC1C,OAAOA,EAAE,CAACuD,UAAU,IAAID,QAAQ;AACpC,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM/B,GAAG,GAAIiC,CAAC,IAAK;EACf,IAAI,OAAOC,oCAAoC,KAAK,UAAU,EAAE;IAC5D,OAAOA,oCAAoC,CAACD,CAAC,CAAC;EAClD;EACA,IAAI,OAAOE,qBAAqB,KAAK,UAAU,EAAE;IAC7C,OAAOA,qBAAqB,CAACF,CAAC,CAAC;EACnC;EACA,OAAOvC,UAAU,CAACuC,CAAC,CAAC;AACxB,CAAC;AACD,MAAMG,YAAY,GAAI3D,EAAE,IAAK;EACzB,OAAO,CAAC,CAACA,EAAE,CAACuD,UAAU,IAAI,CAAC,CAACvD,EAAE,CAAC4D,YAAY;AAC/C,CAAC;AACD,MAAMC,aAAa,GAAIC,WAAW,IAAK;EACnC,MAAMC,MAAM,GAAGD,WAAW,CAACE,OAAO,CAAC,UAAU,CAAC;EAC9C,IAAID,MAAM,EAAE;IACR,OAAOA,MAAM,CAACE,aAAa,CAAC,WAAW,CAAC;EAC5C;EACA,OAAO,IAAI;AACf,CAAC;AACD,MAAMC,mBAAmB,GAAIlE,EAAE,IAAK;EAChCA,EAAE,CAACmE,KAAK,CAAC,CAAC;EACV;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAInE,EAAE,CAACoE,SAAS,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;IACxC,MAAMC,GAAG,GAAGtE,EAAE,CAACgE,OAAO,CAAC,SAAS,CAAC;IACjC,IAAIM,GAAG,EAAE;MACLA,GAAG,CAACC,QAAQ,CAAC,CAACvE,EAAE,CAAC,CAAC;IACtB;EACJ;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwE,YAAY,GAAGA,CAACV,WAAW,EAAEW,OAAO,KAAK;EAC3C,IAAIC,SAAS;EACb;EACA;EACA,MAAMC,UAAU,GAAGb,WAAW,CAAC7B,YAAY,CAAC,iBAAiB,CAAC;EAC9D;EACA;EACA,MAAM2C,WAAW,GAAGd,WAAW,CAACe,EAAE;EAClC,IAAIC,OAAO,GAAGH,UAAU,KAAK,IAAI,IAAIA,UAAU,CAACI,IAAI,CAAC,CAAC,KAAK,EAAE,GAAGJ,UAAU,GAAGF,OAAO,GAAG,MAAM;EAC7F,IAAIO,KAAK,GAAGL,UAAU,KAAK,IAAI,IAAIA,UAAU,CAACI,IAAI,CAAC,CAAC,KAAK,EAAE,GAAGE,QAAQ,CAACC,cAAc,CAACP,UAAU,CAAC,GAAGd,aAAa,CAACC,WAAW,CAAC;EAC9H,IAAIkB,KAAK,EAAE;IACP,IAAIL,UAAU,KAAK,IAAI,EAAE;MACrBK,KAAK,CAACH,EAAE,GAAGC,OAAO;IACtB;IACAJ,SAAS,GAAGM,KAAK,CAACG,WAAW;IAC7BH,KAAK,CAACI,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IACzC;IACA;EACJ,CAAC,MACI,IAAIR,WAAW,CAACG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IAChCC,KAAK,GAAGC,QAAQ,CAAChB,aAAa,CAAC,cAAcW,WAAW,IAAI,CAAC;IAC7D,IAAII,KAAK,EAAE;MACP,IAAIA,KAAK,CAACH,EAAE,KAAK,EAAE,EAAE;QACjBC,OAAO,GAAGE,KAAK,CAACH,EAAE;MACtB,CAAC,MACI;QACDG,KAAK,CAACH,EAAE,GAAGC,OAAO,GAAG,GAAGF,WAAW,MAAM;MAC7C;MACAF,SAAS,GAAGM,KAAK,CAACG,WAAW;IACjC;EACJ;EACA,OAAO;IAAEH,KAAK;IAAEF,OAAO;IAAEJ;EAAU,CAAC;AACxC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAExD,KAAK,EAAEyD,QAAQ,KAAK;EACpE,IAAIH,MAAM,IAAI3B,YAAY,CAAC4B,SAAS,CAAC,EAAE;IACnC,IAAIG,KAAK,GAAGH,SAAS,CAACtB,aAAa,CAAC,iBAAiB,CAAC;IACtD,IAAI,CAACyB,KAAK,EAAE;MACRA,KAAK,GAAGH,SAAS,CAACI,aAAa,CAACC,aAAa,CAAC,OAAO,CAAC;MACtDF,KAAK,CAACG,IAAI,GAAG,QAAQ;MACrBH,KAAK,CAACtB,SAAS,CAAC0B,GAAG,CAAC,WAAW,CAAC;MAChCP,SAAS,CAACQ,WAAW,CAACL,KAAK,CAAC;IAChC;IACAA,KAAK,CAACD,QAAQ,GAAGA,QAAQ;IACzBC,KAAK,CAACF,IAAI,GAAGA,IAAI;IACjBE,KAAK,CAAC1D,KAAK,GAAGA,KAAK,IAAI,EAAE;EAC7B;AACJ,CAAC;AACD,MAAMgE,KAAK,GAAGA,CAACC,GAAG,EAAEC,CAAC,EAAEC,GAAG,KAAK;EAC3B,OAAOC,IAAI,CAACD,GAAG,CAACF,GAAG,EAAEG,IAAI,CAACH,GAAG,CAACC,CAAC,EAAEC,GAAG,CAAC,CAAC;AAC1C,CAAC;AACD,MAAME,MAAM,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EAC/B,IAAI,CAACD,MAAM,EAAE;IACT,MAAME,OAAO,GAAG,UAAU,GAAGD,MAAM;IACnCE,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;IACtB,SAAS,CAAC;IACV,MAAM,IAAIG,KAAK,CAACH,OAAO,CAAC;EAC5B;AACJ,CAAC;AACD,MAAMI,GAAG,GAAI/F,EAAE,IAAK;EAChB,OAAOA,EAAE,CAACgG,SAAS,IAAIC,IAAI,CAACF,GAAG,CAAC,CAAC;AACrC,CAAC;AACD,MAAMG,YAAY,GAAIlG,EAAE,IAAK;EACzB;EACA;EACA,IAAIA,EAAE,EAAE;IACJ,MAAMmG,cAAc,GAAGnG,EAAE,CAACmG,cAAc;IACxC,IAAIA,cAAc,IAAIA,cAAc,CAACzE,MAAM,GAAG,CAAC,EAAE;MAC7C,MAAM0E,KAAK,GAAGD,cAAc,CAAC,CAAC,CAAC;MAC/B,OAAO;QAAEE,CAAC,EAAED,KAAK,CAACE,OAAO;QAAEC,CAAC,EAAEH,KAAK,CAACI;MAAQ,CAAC;IACjD;IACA,IAAIxG,EAAE,CAACyG,KAAK,KAAKxG,SAAS,EAAE;MACxB,OAAO;QAAEoG,CAAC,EAAErG,EAAE,CAACyG,KAAK;QAAEF,CAAC,EAAEvG,EAAE,CAAC0G;MAAM,CAAC;IACvC;EACJ;EACA,OAAO;IAAEL,CAAC,EAAE,CAAC;IAAEE,CAAC,EAAE;EAAE,CAAC;AACzB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,SAAS,GAAIC,IAAI,IAAK;EACxB,MAAMC,KAAK,GAAGzC,QAAQ,CAAC0C,GAAG,KAAK,KAAK;EACpC,QAAQF,IAAI;IACR,KAAK,OAAO;MACR,OAAOC,KAAK;IAChB,KAAK,KAAK;MACN,OAAO,CAACA,KAAK;IACjB;MACI,MAAM,IAAIf,KAAK,CAAC,IAAIc,IAAI,kEAAkE,CAAC;EACnG;AACJ,CAAC;AACD,MAAMG,aAAa,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;EACnC,MAAMC,QAAQ,GAAGF,KAAK,CAACG,SAAS,IAAIH,KAAK;EACzC,OAAO;IACHG,SAAS,EAAEH,KAAK;IAChBI,IAAI,EAAEC,QAAQ,CAACH,QAAQ,CAACE,IAAI,CAACE,IAAI,CAACJ,QAAQ,CAAC,EAAED,IAAI;EACrD,CAAC;AACL,CAAC;AACD,MAAMI,QAAQ,GAAGA,CAACE,IAAI,EAAEN,IAAI,GAAG,CAAC,KAAK;EACjC,IAAIO,KAAK;EACT,OAAO,CAAC,GAAGC,IAAI,KAAK;IAChBpH,YAAY,CAACmH,KAAK,CAAC;IACnBA,KAAK,GAAGpH,UAAU,CAACmH,IAAI,EAAEN,IAAI,EAAE,GAAGQ,IAAI,CAAC;EAC3C,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;EAC1CD,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAIA,IAAI,GAAG,CAAC,CAAE;EACrDC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAIA,IAAI,GAAG,CAAC,CAAE;EACrD,IAAID,IAAI,KAAKC,IAAI,EAAE;IACf,OAAO,IAAI;EACf;EACA,MAAMC,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACJ,IAAI,CAAC;EAC/B,IAAIE,KAAK,CAACnG,MAAM,KAAKoG,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAAClG,MAAM,EAAE;IAC3C,OAAO,KAAK;EAChB;EACA,KAAK,MAAMsG,EAAE,IAAIH,KAAK,EAAE;IACpB,IAAI,EAAEG,EAAE,IAAIJ,IAAI,CAAC,EAAE;MACf,OAAO,KAAK;IAChB;IACA,IAAID,IAAI,CAACK,EAAE,CAAC,KAAKJ,IAAI,CAACI,EAAE,CAAC,EAAE;MACvB,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf,CAAC;AAED,SAAS7H,gBAAgB,IAAI8H,CAAC,EAAE3H,mBAAmB,IAAI4H,CAAC,EAAE3H,gBAAgB,IAAI4H,CAAC,EAAEtH,iBAAiB,IAAIuH,CAAC,EAAE5D,iBAAiB,IAAI6D,CAAC,EAAEhF,mBAAmB,IAAIiF,CAAC,EAAE9F,cAAc,IAAI+F,CAAC,EAAEzF,YAAY,IAAIH,CAAC,EAAEpB,qBAAqB,IAAIiH,CAAC,EAAE7E,YAAY,IAAI8E,CAAC,EAAEzF,aAAa,IAAI0F,CAAC,EAAE/H,YAAY,IAAIgI,CAAC,EAAExD,KAAK,IAAIyD,CAAC,EAAE7B,aAAa,IAAI1B,CAAC,EAAEsB,SAAS,IAAIkC,CAAC,EAAErD,MAAM,IAAIsD,CAAC,EAAEzB,QAAQ,IAAI0B,CAAC,EAAErI,GAAG,IAAIsI,CAAC,EAAEtB,qBAAqB,IAAIuB,CAAC,EAAE/J,kBAAkB,IAAIgK,CAAC,EAAEnD,GAAG,IAAIoD,CAAC,EAAEjD,YAAY,IAAIkD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}