{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, booleanAttribute, NgModule } from '@angular/core';\nimport { CdkTable, CDK_TABLE, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, STICKY_POSITIONING_LISTENER, HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet, CdkCellDef, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkHeaderCell, CdkFooterCell, CdkCell, CdkHeaderRowDef, CdkFooterRowDef, CdkRowDef, CdkHeaderRow, CdkCellOutlet, CdkFooterRow, CdkRow, CdkNoDataRow, CdkTextColumn, CdkTableModule } from '@angular/cdk/table';\nimport { _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy, _DisposeViewRepeaterStrategy, DataSource } from '@angular/cdk/collections';\nimport { MatCommonModule } from '@angular/material/core';\nimport { BehaviorSubject, Subject, merge, of, combineLatest } from 'rxjs';\nimport { _isNumberValue } from '@angular/cdk/coercion';\nimport { map } from 'rxjs/operators';\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nconst _c0 = [[[\"caption\"]], [[\"colgroup\"], [\"col\"]], \"*\"];\nconst _c1 = [\"caption\", \"colgroup, col\", \"*\"];\nfunction MatTable_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nfunction MatTable_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"thead\", 0);\n    i0.ɵɵelementContainer(1, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"tbody\", 2);\n    i0.ɵɵelementContainer(3, 3)(4, 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"tfoot\", 0);\n    i0.ɵɵelementContainer(6, 5);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatTable_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 1)(1, 3)(2, 4)(3, 5);\n  }\n}\nfunction MatTextColumn_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 3);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.headerText, \" \");\n  }\n}\nfunction MatTextColumn_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"text-align\", ctx_r0.justify);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.dataAccessor(data_r2, ctx_r0.name), \" \");\n  }\n}\nclass MatRecycleRows {\n  static {\n    this.ɵfac = function MatRecycleRows_Factory(t) {\n      return new (t || MatRecycleRows)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRecycleRows,\n      selectors: [[\"mat-table\", \"recycleRows\", \"\"], [\"table\", \"mat-table\", \"\", \"recycleRows\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRecycleRows, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-table[recycleRows], table[mat-table][recycleRows]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass MatTable extends CdkTable {\n  constructor() {\n    super(...arguments);\n    /** Overrides the sticky CSS class set by the `CdkTable`. */\n    this.stickyCssClass = 'mat-mdc-table-sticky';\n    /** Overrides the need to add position: sticky on every sticky cell element in `CdkTable`. */\n    this.needsPositionStickyOnElement = false;\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatTable_BaseFactory;\n      return function MatTable_Factory(t) {\n        return (ɵMatTable_BaseFactory || (ɵMatTable_BaseFactory = i0.ɵɵgetInheritedFactory(MatTable)))(t || MatTable);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTable,\n      selectors: [[\"mat-table\"], [\"table\", \"mat-table\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-table\", \"mdc-data-table__table\"],\n      hostVars: 2,\n      hostBindings: function MatTable_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-table-fixed-layout\", ctx.fixedLayout);\n        }\n      },\n      exportAs: [\"matTable\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkTable,\n        useExisting: MatTable\n      }, {\n        provide: CDK_TABLE,\n        useExisting: MatTable\n      }, {\n        provide: _COALESCED_STYLE_SCHEDULER,\n        useClass: _CoalescedStyleScheduler\n      },\n      // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n      //  is only included in the build if used.\n      {\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _DisposeViewRepeaterStrategy\n      },\n      // Prevent nested tables from seeing this table's StickyPositioningListener.\n      {\n        provide: STICKY_POSITIONING_LISTENER,\n        useValue: null\n      }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 5,\n      vars: 2,\n      consts: [[\"role\", \"rowgroup\"], [\"headerRowOutlet\", \"\"], [\"role\", \"rowgroup\", 1, \"mdc-data-table__content\"], [\"rowOutlet\", \"\"], [\"noDataRowOutlet\", \"\"], [\"footerRowOutlet\", \"\"]],\n      template: function MatTable_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵprojection(0);\n          i0.ɵɵprojection(1, 1);\n          i0.ɵɵtemplate(2, MatTable_Conditional_2_Template, 1, 0)(3, MatTable_Conditional_3_Template, 7, 0)(4, MatTable_Conditional_4_Template, 4, 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(2, ctx._isServer ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(3, ctx._isNativeHtmlTable ? 3 : 4);\n        }\n      },\n      dependencies: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n      styles: [\".mat-mdc-table-sticky{position:sticky !important}.mdc-data-table{-webkit-overflow-scrolling:touch;display:inline-flex;flex-direction:column;box-sizing:border-box;position:relative}.mdc-data-table__table-container{-webkit-overflow-scrolling:touch;overflow-x:auto;width:100%}.mdc-data-table__table{min-width:100%;border:0;white-space:nowrap;border-spacing:0;table-layout:fixed}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px 0 16px}.mdc-data-table__header-cell--checkbox,.mdc-data-table__cell--checkbox{padding-left:4px;padding-right:0}[dir=rtl] .mdc-data-table__header-cell--checkbox,[dir=rtl] .mdc-data-table__cell--checkbox,.mdc-data-table__header-cell--checkbox[dir=rtl],.mdc-data-table__cell--checkbox[dir=rtl]{padding-left:0;padding-right:4px}mat-table{display:block}mat-header-row{min-height:56px}mat-row,mat-footer-row{min-height:48px}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color)}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-header-headline-font, Roboto, sans-serif);line-height:var(--mat-table-header-headline-line-height);font-size:var(--mat-table-header-headline-size, 14px);font-weight:var(--mat-table-header-headline-weight, 500)}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, Roboto, sans-serif);line-height:var(--mat-table-row-item-label-text-line-height);font-size:var(--mat-table-row-item-label-text-size, 14px);font-weight:var(--mat-table-row-item-label-text-weight)}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-footer-supporting-text-font, Roboto, sans-serif);line-height:var(--mat-table-footer-supporting-text-line-height);font-size:var(--mat-table-footer-supporting-text-size, 14px);font-weight:var(--mat-table-footer-supporting-text-weight);letter-spacing:var(--mat-table-footer-supporting-text-tracking)}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking);font-weight:inherit;line-height:inherit}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking);line-height:inherit}.mdc-data-table__row:last-child .mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking)}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\"],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTable, [{\n    type: Component,\n    args: [{\n      selector: 'mat-table, table[mat-table]',\n      exportAs: 'matTable',\n      template: `\n    <ng-content select=\"caption\"/>\n    <ng-content select=\"colgroup, col\"/>\n\n    <!--\n      Unprojected content throws a hydration error so we need this to capture it.\n      It gets removed on the client so it doesn't affect the layout.\n    -->\n    @if (_isServer) {\n      <ng-content/>\n    }\n\n    @if (_isNativeHtmlTable) {\n      <thead role=\"rowgroup\">\n        <ng-container headerRowOutlet/>\n      </thead>\n      <tbody class=\"mdc-data-table__content\" role=\"rowgroup\">\n        <ng-container rowOutlet/>\n        <ng-container noDataRowOutlet/>\n      </tbody>\n      <tfoot role=\"rowgroup\">\n        <ng-container footerRowOutlet/>\n      </tfoot>\n    } @else {\n      <ng-container headerRowOutlet/>\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n      <ng-container footerRowOutlet/>\n    }\n  `,\n      host: {\n        'class': 'mat-mdc-table mdc-data-table__table',\n        '[class.mdc-table-fixed-layout]': 'fixedLayout'\n      },\n      providers: [{\n        provide: CdkTable,\n        useExisting: MatTable\n      }, {\n        provide: CDK_TABLE,\n        useExisting: MatTable\n      }, {\n        provide: _COALESCED_STYLE_SCHEDULER,\n        useClass: _CoalescedStyleScheduler\n      },\n      // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n      //  is only included in the build if used.\n      {\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _DisposeViewRepeaterStrategy\n      },\n      // Prevent nested tables from seeing this table's StickyPositioningListener.\n      {\n        provide: STICKY_POSITIONING_LISTENER,\n        useValue: null\n      }],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      standalone: true,\n      imports: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n      styles: [\".mat-mdc-table-sticky{position:sticky !important}.mdc-data-table{-webkit-overflow-scrolling:touch;display:inline-flex;flex-direction:column;box-sizing:border-box;position:relative}.mdc-data-table__table-container{-webkit-overflow-scrolling:touch;overflow-x:auto;width:100%}.mdc-data-table__table{min-width:100%;border:0;white-space:nowrap;border-spacing:0;table-layout:fixed}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px 0 16px}.mdc-data-table__header-cell--checkbox,.mdc-data-table__cell--checkbox{padding-left:4px;padding-right:0}[dir=rtl] .mdc-data-table__header-cell--checkbox,[dir=rtl] .mdc-data-table__cell--checkbox,.mdc-data-table__header-cell--checkbox[dir=rtl],.mdc-data-table__cell--checkbox[dir=rtl]{padding-left:0;padding-right:4px}mat-table{display:block}mat-header-row{min-height:56px}mat-row,mat-footer-row{min-height:48px}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color)}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-header-headline-font, Roboto, sans-serif);line-height:var(--mat-table-header-headline-line-height);font-size:var(--mat-table-header-headline-size, 14px);font-weight:var(--mat-table-header-headline-weight, 500)}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, Roboto, sans-serif);line-height:var(--mat-table-row-item-label-text-line-height);font-size:var(--mat-table-row-item-label-text-size, 14px);font-weight:var(--mat-table-row-item-label-text-weight)}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-footer-supporting-text-font, Roboto, sans-serif);line-height:var(--mat-table-footer-supporting-text-line-height);font-size:var(--mat-table-footer-supporting-text-size, 14px);font-weight:var(--mat-table-footer-supporting-text-weight);letter-spacing:var(--mat-table-footer-supporting-text-tracking)}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking);font-weight:inherit;line-height:inherit}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking);line-height:inherit}.mdc-data-table__row:last-child .mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking)}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\"]\n    }]\n  }], null, null);\n})();\n\n/**\n * Cell definition for the mat-table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass MatCellDef extends CdkCellDef {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatCellDef_BaseFactory;\n      return function MatCellDef_Factory(t) {\n        return (ɵMatCellDef_BaseFactory || (ɵMatCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatCellDef)))(t || MatCellDef);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCellDef,\n      selectors: [[\"\", \"matCellDef\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkCellDef,\n        useExisting: MatCellDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matCellDef]',\n      providers: [{\n        provide: CdkCellDef,\n        useExisting: MatCellDef\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Header cell definition for the mat-table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass MatHeaderCellDef extends CdkHeaderCellDef {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatHeaderCellDef_BaseFactory;\n      return function MatHeaderCellDef_Factory(t) {\n        return (ɵMatHeaderCellDef_BaseFactory || (ɵMatHeaderCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderCellDef)))(t || MatHeaderCellDef);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatHeaderCellDef,\n      selectors: [[\"\", \"matHeaderCellDef\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkHeaderCellDef,\n        useExisting: MatHeaderCellDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matHeaderCellDef]',\n      providers: [{\n        provide: CdkHeaderCellDef,\n        useExisting: MatHeaderCellDef\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Footer cell definition for the mat-table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass MatFooterCellDef extends CdkFooterCellDef {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatFooterCellDef_BaseFactory;\n      return function MatFooterCellDef_Factory(t) {\n        return (ɵMatFooterCellDef_BaseFactory || (ɵMatFooterCellDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterCellDef)))(t || MatFooterCellDef);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatFooterCellDef,\n      selectors: [[\"\", \"matFooterCellDef\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkFooterCellDef,\n        useExisting: MatFooterCellDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterCellDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matFooterCellDef]',\n      providers: [{\n        provide: CdkFooterCellDef,\n        useExisting: MatFooterCellDef\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Column definition for the mat-table.\n * Defines a set of cells available for a table column.\n */\nclass MatColumnDef extends CdkColumnDef {\n  /** Unique name for this column. */\n  get name() {\n    return this._name;\n  }\n  set name(name) {\n    this._setNameInput(name);\n  }\n  /**\n   * Add \"mat-column-\" prefix in addition to \"cdk-column-\" prefix.\n   * In the future, this will only add \"mat-column-\" and columnCssClassName\n   * will change from type string[] to string.\n   * @docs-private\n   */\n  _updateColumnCssClassName() {\n    super._updateColumnCssClassName();\n    this._columnCssClassName.push(`mat-column-${this.cssClassFriendlyName}`);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatColumnDef_BaseFactory;\n      return function MatColumnDef_Factory(t) {\n        return (ɵMatColumnDef_BaseFactory || (ɵMatColumnDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatColumnDef)))(t || MatColumnDef);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatColumnDef,\n      selectors: [[\"\", \"matColumnDef\", \"\"]],\n      inputs: {\n        name: [i0.ɵɵInputFlags.None, \"matColumnDef\", \"name\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkColumnDef,\n        useExisting: MatColumnDef\n      }, {\n        provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n        useExisting: MatColumnDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatColumnDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matColumnDef]',\n      providers: [{\n        provide: CdkColumnDef,\n        useExisting: MatColumnDef\n      }, {\n        provide: 'MAT_SORT_HEADER_COLUMN_DEF',\n        useExisting: MatColumnDef\n      }],\n      standalone: true\n    }]\n  }], null, {\n    name: [{\n      type: Input,\n      args: ['matColumnDef']\n    }]\n  });\n})();\n/** Header cell template container that adds the right classes and role. */\nclass MatHeaderCell extends CdkHeaderCell {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatHeaderCell_BaseFactory;\n      return function MatHeaderCell_Factory(t) {\n        return (ɵMatHeaderCell_BaseFactory || (ɵMatHeaderCell_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderCell)))(t || MatHeaderCell);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatHeaderCell,\n      selectors: [[\"mat-header-cell\"], [\"th\", \"mat-header-cell\", \"\"]],\n      hostAttrs: [\"role\", \"columnheader\", 1, \"mat-mdc-header-cell\", \"mdc-data-table__header-cell\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderCell, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-header-cell, th[mat-header-cell]',\n      host: {\n        'class': 'mat-mdc-header-cell mdc-data-table__header-cell',\n        'role': 'columnheader'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/** Footer cell template container that adds the right classes and role. */\nclass MatFooterCell extends CdkFooterCell {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatFooterCell_BaseFactory;\n      return function MatFooterCell_Factory(t) {\n        return (ɵMatFooterCell_BaseFactory || (ɵMatFooterCell_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterCell)))(t || MatFooterCell);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatFooterCell,\n      selectors: [[\"mat-footer-cell\"], [\"td\", \"mat-footer-cell\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-footer-cell\", \"mdc-data-table__cell\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterCell, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-footer-cell, td[mat-footer-cell]',\n      host: {\n        'class': 'mat-mdc-footer-cell mdc-data-table__cell'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/** Cell template container that adds the right classes and role. */\nclass MatCell extends CdkCell {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatCell_BaseFactory;\n      return function MatCell_Factory(t) {\n        return (ɵMatCell_BaseFactory || (ɵMatCell_BaseFactory = i0.ɵɵgetInheritedFactory(MatCell)))(t || MatCell);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCell,\n      selectors: [[\"mat-cell\"], [\"td\", \"mat-cell\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-cell\", \"mdc-data-table__cell\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCell, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-cell, td[mat-cell]',\n      host: {\n        'class': 'mat-mdc-cell mdc-data-table__cell'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n// We can't reuse `CDK_ROW_TEMPLATE` because it's incompatible with local compilation mode.\nconst ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Header row definition for the mat-table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass MatHeaderRowDef extends CdkHeaderRowDef {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatHeaderRowDef_BaseFactory;\n      return function MatHeaderRowDef_Factory(t) {\n        return (ɵMatHeaderRowDef_BaseFactory || (ɵMatHeaderRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderRowDef)))(t || MatHeaderRowDef);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatHeaderRowDef,\n      selectors: [[\"\", \"matHeaderRowDef\", \"\"]],\n      inputs: {\n        columns: [i0.ɵɵInputFlags.None, \"matHeaderRowDef\", \"columns\"],\n        sticky: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matHeaderRowDefSticky\", \"sticky\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkHeaderRowDef,\n        useExisting: MatHeaderRowDef\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matHeaderRowDef]',\n      providers: [{\n        provide: CdkHeaderRowDef,\n        useExisting: MatHeaderRowDef\n      }],\n      inputs: [{\n        name: 'columns',\n        alias: 'matHeaderRowDef'\n      }, {\n        name: 'sticky',\n        alias: 'matHeaderRowDefSticky',\n        transform: booleanAttribute\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Footer row definition for the mat-table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass MatFooterRowDef extends CdkFooterRowDef {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatFooterRowDef_BaseFactory;\n      return function MatFooterRowDef_Factory(t) {\n        return (ɵMatFooterRowDef_BaseFactory || (ɵMatFooterRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterRowDef)))(t || MatFooterRowDef);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatFooterRowDef,\n      selectors: [[\"\", \"matFooterRowDef\", \"\"]],\n      inputs: {\n        columns: [i0.ɵɵInputFlags.None, \"matFooterRowDef\", \"columns\"],\n        sticky: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matFooterRowDefSticky\", \"sticky\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkFooterRowDef,\n        useExisting: MatFooterRowDef\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matFooterRowDef]',\n      providers: [{\n        provide: CdkFooterRowDef,\n        useExisting: MatFooterRowDef\n      }],\n      inputs: [{\n        name: 'columns',\n        alias: 'matFooterRowDef'\n      }, {\n        name: 'sticky',\n        alias: 'matFooterRowDefSticky',\n        transform: booleanAttribute\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Data row definition for the mat-table.\n * Captures the data row's template and other properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass MatRowDef extends CdkRowDef {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatRowDef_BaseFactory;\n      return function MatRowDef_Factory(t) {\n        return (ɵMatRowDef_BaseFactory || (ɵMatRowDef_BaseFactory = i0.ɵɵgetInheritedFactory(MatRowDef)))(t || MatRowDef);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRowDef,\n      selectors: [[\"\", \"matRowDef\", \"\"]],\n      inputs: {\n        columns: [i0.ɵɵInputFlags.None, \"matRowDefColumns\", \"columns\"],\n        when: [i0.ɵɵInputFlags.None, \"matRowDefWhen\", \"when\"]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkRowDef,\n        useExisting: MatRowDef\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRowDef, [{\n    type: Directive,\n    args: [{\n      selector: '[matRowDef]',\n      providers: [{\n        provide: CdkRowDef,\n        useExisting: MatRowDef\n      }],\n      inputs: [{\n        name: 'columns',\n        alias: 'matRowDefColumns'\n      }, {\n        name: 'when',\n        alias: 'matRowDefWhen'\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass MatHeaderRow extends CdkHeaderRow {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatHeaderRow_BaseFactory;\n      return function MatHeaderRow_Factory(t) {\n        return (ɵMatHeaderRow_BaseFactory || (ɵMatHeaderRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatHeaderRow)))(t || MatHeaderRow);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatHeaderRow,\n      selectors: [[\"mat-header-row\"], [\"tr\", \"mat-header-row\", \"\"]],\n      hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-header-row\", \"mdc-data-table__header-row\"],\n      exportAs: [\"matHeaderRow\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkHeaderRow,\n        useExisting: MatHeaderRow\n      }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkCellOutlet\", \"\"]],\n      template: function MatHeaderRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [CdkCellOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHeaderRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-header-row, tr[mat-header-row]',\n      template: ROW_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-header-row mdc-data-table__header-row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matHeaderRow',\n      providers: [{\n        provide: CdkHeaderRow,\n        useExisting: MatHeaderRow\n      }],\n      standalone: true,\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass MatFooterRow extends CdkFooterRow {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatFooterRow_BaseFactory;\n      return function MatFooterRow_Factory(t) {\n        return (ɵMatFooterRow_BaseFactory || (ɵMatFooterRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatFooterRow)))(t || MatFooterRow);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatFooterRow,\n      selectors: [[\"mat-footer-row\"], [\"tr\", \"mat-footer-row\", \"\"]],\n      hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-footer-row\", \"mdc-data-table__row\"],\n      exportAs: [\"matFooterRow\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkFooterRow,\n        useExisting: MatFooterRow\n      }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkCellOutlet\", \"\"]],\n      template: function MatFooterRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [CdkCellOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFooterRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-footer-row, tr[mat-footer-row]',\n      template: ROW_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-footer-row mdc-data-table__row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matFooterRow',\n      providers: [{\n        provide: CdkFooterRow,\n        useExisting: MatFooterRow\n      }],\n      standalone: true,\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass MatRow extends CdkRow {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatRow_BaseFactory;\n      return function MatRow_Factory(t) {\n        return (ɵMatRow_BaseFactory || (ɵMatRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatRow)))(t || MatRow);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatRow,\n      selectors: [[\"mat-row\"], [\"tr\", \"mat-row\", \"\"]],\n      hostAttrs: [\"role\", \"row\", 1, \"mat-mdc-row\", \"mdc-data-table__row\"],\n      exportAs: [\"matRow\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkRow,\n        useExisting: MatRow\n      }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkCellOutlet\", \"\"]],\n      template: function MatRow_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainer(0, 0);\n        }\n      },\n      dependencies: [CdkCellOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-row, tr[mat-row]',\n      template: ROW_TEMPLATE,\n      host: {\n        'class': 'mat-mdc-row mdc-data-table__row',\n        'role': 'row'\n      },\n      // See note on CdkTable for explanation on why this uses the default change detection strategy.\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matRow',\n      providers: [{\n        provide: CdkRow,\n        useExisting: MatRow\n      }],\n      standalone: true,\n      imports: [CdkCellOutlet]\n    }]\n  }], null, null);\n})();\n/** Row that can be used to display a message when no data is shown in the table. */\nclass MatNoDataRow extends CdkNoDataRow {\n  constructor() {\n    super(...arguments);\n    this._contentClassName = 'mat-mdc-no-data-row';\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatNoDataRow_BaseFactory;\n      return function MatNoDataRow_Factory(t) {\n        return (ɵMatNoDataRow_BaseFactory || (ɵMatNoDataRow_BaseFactory = i0.ɵɵgetInheritedFactory(MatNoDataRow)))(t || MatNoDataRow);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatNoDataRow,\n      selectors: [[\"ng-template\", \"matNoDataRow\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkNoDataRow,\n        useExisting: MatNoDataRow\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNoDataRow, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matNoDataRow]',\n      providers: [{\n        provide: CdkNoDataRow,\n        useExisting: MatNoDataRow\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass MatTextColumn extends CdkTextColumn {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatTextColumn_BaseFactory;\n      return function MatTextColumn_Factory(t) {\n        return (ɵMatTextColumn_BaseFactory || (ɵMatTextColumn_BaseFactory = i0.ɵɵgetInheritedFactory(MatTextColumn)))(t || MatTextColumn);\n      };\n    })();\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatTextColumn,\n      selectors: [[\"mat-text-column\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 0,\n      consts: [[\"matColumnDef\", \"\"], [\"mat-header-cell\", \"\", 3, \"text-align\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 3, \"text-align\", 4, \"matCellDef\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"]],\n      template: function MatTextColumn_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementContainerStart(0, 0);\n          i0.ɵɵtemplate(1, MatTextColumn_th_1_Template, 2, 3, \"th\", 1)(2, MatTextColumn_td_2_Template, 2, 3, \"td\", 2);\n          i0.ɵɵelementContainerEnd();\n        }\n      },\n      dependencies: [MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTextColumn, [{\n    type: Component,\n    args: [{\n      selector: 'mat-text-column',\n      template: `\n    <ng-container matColumnDef>\n      <th mat-header-cell *matHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td mat-cell *matCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n      encapsulation: ViewEncapsulation.None,\n      // Change detection is intentionally not set to OnPush. This component's template will be provided\n      // to the table to be inserted into its view. This is problematic when change detection runs since\n      // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n      // mean's the template in the table's view will not have the updated value (and in fact will cause\n      // an ExpressionChangedAfterItHasBeenCheckedError).\n      // tslint:disable-next-line:validate-decorators\n      changeDetection: ChangeDetectionStrategy.Default,\n      standalone: true,\n      imports: [MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell]\n    }]\n  }], null, null);\n})();\nconst EXPORTED_DECLARATIONS = [\n// Table\nMatTable, MatRecycleRows,\n// Template defs\nMatHeaderCellDef, MatHeaderRowDef, MatColumnDef, MatCellDef, MatRowDef, MatFooterCellDef, MatFooterRowDef,\n// Cell directives\nMatHeaderCell, MatCell, MatFooterCell,\n// Row directives\nMatHeaderRow, MatRow, MatFooterRow, MatNoDataRow, MatTextColumn];\nclass MatTableModule {\n  static {\n    this.ɵfac = function MatTableModule_Factory(t) {\n      return new (t || MatTableModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatTableModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CdkTableModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTableModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CdkTableModule, ...EXPORTED_DECLARATIONS],\n      exports: [MatCommonModule, EXPORTED_DECLARATIONS]\n    }]\n  }], null, null);\n})();\n\n/**\n * Corresponds to `Number.MAX_SAFE_INTEGER`. Moved out into a variable here due to\n * flaky browser support and the value not being defined in Closure's typings.\n */\nconst MAX_SAFE_INTEGER = 9007199254740991;\n/**\n * Data source that accepts a client-side data array and includes native support of filtering,\n * sorting (using MatSort), and pagination (using MatPaginator).\n *\n * Allows for sort customization by overriding sortingDataAccessor, which defines how data\n * properties are accessed. Also allows for filter customization by overriding filterPredicate,\n * which defines how row data is converted to a string for filter matching.\n *\n * **Note:** This class is meant to be a simple data source to help you get started. As such\n * it isn't equipped to handle some more advanced cases like robust i18n support or server-side\n * interactions. If your app needs to support more advanced use cases, consider implementing your\n * own `DataSource`.\n */\nclass MatTableDataSource extends DataSource {\n  /** Array of data that should be rendered by the table, where each object represents one row. */\n  get data() {\n    return this._data.value;\n  }\n  set data(data) {\n    data = Array.isArray(data) ? data : [];\n    this._data.next(data);\n    // Normally the `filteredData` is updated by the re-render\n    // subscription, but that won't happen if it's inactive.\n    if (!this._renderChangesSubscription) {\n      this._filterData(data);\n    }\n  }\n  /**\n   * Filter term that should be used to filter out objects from the data array. To override how\n   * data objects match to this filter string, provide a custom function for filterPredicate.\n   */\n  get filter() {\n    return this._filter.value;\n  }\n  set filter(filter) {\n    this._filter.next(filter);\n    // Normally the `filteredData` is updated by the re-render\n    // subscription, but that won't happen if it's inactive.\n    if (!this._renderChangesSubscription) {\n      this._filterData(this.data);\n    }\n  }\n  /**\n   * Instance of the MatSort directive used by the table to control its sorting. Sort changes\n   * emitted by the MatSort will trigger an update to the table's rendered data.\n   */\n  get sort() {\n    return this._sort;\n  }\n  set sort(sort) {\n    this._sort = sort;\n    this._updateChangeSubscription();\n  }\n  /**\n   * Instance of the paginator component used by the table to control what page of the data is\n   * displayed. Page changes emitted by the paginator will trigger an update to the\n   * table's rendered data.\n   *\n   * Note that the data source uses the paginator's properties to calculate which page of data\n   * should be displayed. If the paginator receives its properties as template inputs,\n   * e.g. `[pageLength]=100` or `[pageIndex]=1`, then be sure that the paginator's view has been\n   * initialized before assigning it to this data source.\n   */\n  get paginator() {\n    return this._paginator;\n  }\n  set paginator(paginator) {\n    this._paginator = paginator;\n    this._updateChangeSubscription();\n  }\n  constructor(initialData = []) {\n    super();\n    /** Stream emitting render data to the table (depends on ordered data changes). */\n    this._renderData = new BehaviorSubject([]);\n    /** Stream that emits when a new filter string is set on the data source. */\n    this._filter = new BehaviorSubject('');\n    /** Used to react to internal changes of the paginator that are made by the data source itself. */\n    this._internalPageChanges = new Subject();\n    /**\n     * Subscription to the changes that should trigger an update to the table's rendered rows, such\n     * as filtering, sorting, pagination, or base data changes.\n     */\n    this._renderChangesSubscription = null;\n    /**\n     * Data accessor function that is used for accessing data properties for sorting through\n     * the default sortData function.\n     * This default function assumes that the sort header IDs (which defaults to the column name)\n     * matches the data's properties (e.g. column Xyz represents data['Xyz']).\n     * May be set to a custom function for different behavior.\n     * @param data Data object that is being accessed.\n     * @param sortHeaderId The name of the column that represents the data.\n     */\n    this.sortingDataAccessor = (data, sortHeaderId) => {\n      const value = data[sortHeaderId];\n      if (_isNumberValue(value)) {\n        const numberValue = Number(value);\n        // Numbers beyond `MAX_SAFE_INTEGER` can't be compared reliably so we\n        // leave them as strings. For more info: https://goo.gl/y5vbSg\n        return numberValue < MAX_SAFE_INTEGER ? numberValue : value;\n      }\n      return value;\n    };\n    /**\n     * Gets a sorted copy of the data array based on the state of the MatSort. Called\n     * after changes are made to the filtered data or when sort changes are emitted from MatSort.\n     * By default, the function retrieves the active sort and its direction and compares data\n     * by retrieving data using the sortingDataAccessor. May be overridden for a custom implementation\n     * of data ordering.\n     * @param data The array of data that should be sorted.\n     * @param sort The connected MatSort that holds the current sort state.\n     */\n    this.sortData = (data, sort) => {\n      const active = sort.active;\n      const direction = sort.direction;\n      if (!active || direction == '') {\n        return data;\n      }\n      return data.sort((a, b) => {\n        let valueA = this.sortingDataAccessor(a, active);\n        let valueB = this.sortingDataAccessor(b, active);\n        // If there are data in the column that can be converted to a number,\n        // it must be ensured that the rest of the data\n        // is of the same type so as not to order incorrectly.\n        const valueAType = typeof valueA;\n        const valueBType = typeof valueB;\n        if (valueAType !== valueBType) {\n          if (valueAType === 'number') {\n            valueA += '';\n          }\n          if (valueBType === 'number') {\n            valueB += '';\n          }\n        }\n        // If both valueA and valueB exist (truthy), then compare the two. Otherwise, check if\n        // one value exists while the other doesn't. In this case, existing value should come last.\n        // This avoids inconsistent results when comparing values to undefined/null.\n        // If neither value exists, return 0 (equal).\n        let comparatorResult = 0;\n        if (valueA != null && valueB != null) {\n          // Check if one value is greater than the other; if equal, comparatorResult should remain 0.\n          if (valueA > valueB) {\n            comparatorResult = 1;\n          } else if (valueA < valueB) {\n            comparatorResult = -1;\n          }\n        } else if (valueA != null) {\n          comparatorResult = 1;\n        } else if (valueB != null) {\n          comparatorResult = -1;\n        }\n        return comparatorResult * (direction == 'asc' ? 1 : -1);\n      });\n    };\n    /**\n     * Checks if a data object matches the data source's filter string. By default, each data object\n     * is converted to a string of its properties and returns true if the filter has\n     * at least one occurrence in that string. By default, the filter string has its whitespace\n     * trimmed and the match is case-insensitive. May be overridden for a custom implementation of\n     * filter matching.\n     * @param data Data object used to check against the filter.\n     * @param filter Filter string that has been set on the data source.\n     * @returns Whether the filter matches against the data\n     */\n    this.filterPredicate = (data, filter) => {\n      // Transform the data into a lowercase string of all property values.\n      const dataStr = Object.keys(data).reduce((currentTerm, key) => {\n        // Use an obscure Unicode character to delimit the words in the concatenated string.\n        // This avoids matches where the values of two columns combined will match the user's query\n        // (e.g. `Flute` and `Stop` will match `Test`). The character is intended to be something\n        // that has a very low chance of being typed in by somebody in a text field. This one in\n        // particular is \"White up-pointing triangle with dot\" from\n        // https://en.wikipedia.org/wiki/List_of_Unicode_characters\n        return currentTerm + data[key] + '◬';\n      }, '').toLowerCase();\n      // Transform the filter by converting it to lowercase and removing whitespace.\n      const transformedFilter = filter.trim().toLowerCase();\n      return dataStr.indexOf(transformedFilter) != -1;\n    };\n    this._data = new BehaviorSubject(initialData);\n    this._updateChangeSubscription();\n  }\n  /**\n   * Subscribe to changes that should trigger an update to the table's rendered rows. When the\n   * changes occur, process the current state of the filter, sort, and pagination along with\n   * the provided base data and send it to the table for rendering.\n   */\n  _updateChangeSubscription() {\n    // Sorting and/or pagination should be watched if sort and/or paginator are provided.\n    // The events should emit whenever the component emits a change or initializes, or if no\n    // component is provided, a stream with just a null event should be provided.\n    // The `sortChange` and `pageChange` acts as a signal to the combineLatests below so that the\n    // pipeline can progress to the next step. Note that the value from these streams are not used,\n    // they purely act as a signal to progress in the pipeline.\n    const sortChange = this._sort ? merge(this._sort.sortChange, this._sort.initialized) : of(null);\n    const pageChange = this._paginator ? merge(this._paginator.page, this._internalPageChanges, this._paginator.initialized) : of(null);\n    const dataStream = this._data;\n    // Watch for base data or filter changes to provide a filtered set of data.\n    const filteredData = combineLatest([dataStream, this._filter]).pipe(map(([data]) => this._filterData(data)));\n    // Watch for filtered data or sort changes to provide an ordered set of data.\n    const orderedData = combineLatest([filteredData, sortChange]).pipe(map(([data]) => this._orderData(data)));\n    // Watch for ordered data or page changes to provide a paged set of data.\n    const paginatedData = combineLatest([orderedData, pageChange]).pipe(map(([data]) => this._pageData(data)));\n    // Watched for paged data changes and send the result to the table to render.\n    this._renderChangesSubscription?.unsubscribe();\n    this._renderChangesSubscription = paginatedData.subscribe(data => this._renderData.next(data));\n  }\n  /**\n   * Returns a filtered data array where each filter object contains the filter string within\n   * the result of the filterPredicate function. If no filter is set, returns the data array\n   * as provided.\n   */\n  _filterData(data) {\n    // If there is a filter string, filter out data that does not contain it.\n    // Each data object is converted to a string using the function defined by filterPredicate.\n    // May be overridden for customization.\n    this.filteredData = this.filter == null || this.filter === '' ? data : data.filter(obj => this.filterPredicate(obj, this.filter));\n    if (this.paginator) {\n      this._updatePaginator(this.filteredData.length);\n    }\n    return this.filteredData;\n  }\n  /**\n   * Returns a sorted copy of the data if MatSort has a sort applied, otherwise just returns the\n   * data array as provided. Uses the default data accessor for data lookup, unless a\n   * sortDataAccessor function is defined.\n   */\n  _orderData(data) {\n    // If there is no active sort or direction, return the data without trying to sort.\n    if (!this.sort) {\n      return data;\n    }\n    return this.sortData(data.slice(), this.sort);\n  }\n  /**\n   * Returns a paged slice of the provided data array according to the provided paginator's page\n   * index and length. If there is no paginator provided, returns the data array as provided.\n   */\n  _pageData(data) {\n    if (!this.paginator) {\n      return data;\n    }\n    const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n    return data.slice(startIndex, startIndex + this.paginator.pageSize);\n  }\n  /**\n   * Updates the paginator to reflect the length of the filtered data, and makes sure that the page\n   * index does not exceed the paginator's last page. Values are changed in a resolved promise to\n   * guard against making property changes within a round of change detection.\n   */\n  _updatePaginator(filteredDataLength) {\n    Promise.resolve().then(() => {\n      const paginator = this.paginator;\n      if (!paginator) {\n        return;\n      }\n      paginator.length = filteredDataLength;\n      // If the page index is set beyond the page, reduce it to the last page.\n      if (paginator.pageIndex > 0) {\n        const lastPageIndex = Math.ceil(paginator.length / paginator.pageSize) - 1 || 0;\n        const newPageIndex = Math.min(paginator.pageIndex, lastPageIndex);\n        if (newPageIndex !== paginator.pageIndex) {\n          paginator.pageIndex = newPageIndex;\n          // Since the paginator only emits after user-generated changes,\n          // we need our own stream so we know to should re-render the data.\n          this._internalPageChanges.next();\n        }\n      }\n    });\n  }\n  /**\n   * Used by the MatTable. Called when it connects to the data source.\n   * @docs-private\n   */\n  connect() {\n    if (!this._renderChangesSubscription) {\n      this._updateChangeSubscription();\n    }\n    return this._renderData;\n  }\n  /**\n   * Used by the MatTable. Called when it disconnects from the data source.\n   * @docs-private\n   */\n  disconnect() {\n    this._renderChangesSubscription?.unsubscribe();\n    this._renderChangesSubscription = null;\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatCell, MatCellDef, MatColumnDef, MatFooterCell, MatFooterCellDef, MatFooterRow, MatFooterRowDef, MatHeaderCell, MatHeaderCellDef, MatHeaderRow, MatHeaderRowDef, MatNoDataRow, MatRecycleRows, MatRow, MatRowDef, MatTable, MatTableDataSource, MatTableModule, MatTextColumn };", "map": {"version": 3, "names": ["i0", "Directive", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "booleanAttribute", "NgModule", "CdkTable", "CDK_TABLE", "_COALESCED_STYLE_SCHEDULER", "_CoalescedStyleScheduler", "STICKY_POSITIONING_LISTENER", "HeaderRowOutlet", "DataRowOutlet", "NoDataRowOutlet", "FooterRowOutlet", "CdkCellDef", "CdkHeaderCellDef", "CdkFooterCellDef", "CdkColumnDef", "CdkHeaderCell", "CdkFooterCell", "CdkCell", "CdkHeaderRowDef", "CdkFooterRowDef", "CdkRowDef", "CdkHeaderRow", "CdkCellOutlet", "CdkFooterRow", "CdkRow", "CdkNoDataRow", "CdkTextColumn", "CdkTableModule", "_VIEW_REPEATER_STRATEGY", "_RecycleViewRepeaterStrategy", "_DisposeViewRepeaterStrategy", "DataSource", "MatCommonModule", "BehaviorSubject", "Subject", "merge", "of", "combineLatest", "_isNumberValue", "map", "_c0", "_c1", "MatTable_Conditional_2_Template", "rf", "ctx", "ɵɵprojection", "MatTable_Conditional_3_Template", "ɵɵelementStart", "ɵɵelementContainer", "ɵɵelementEnd", "MatTable_Conditional_4_Template", "MatTextColumn_th_1_Template", "ɵɵtext", "ctx_r0", "ɵɵnextContext", "ɵɵstyleProp", "justify", "ɵɵadvance", "ɵɵtextInterpolate1", "headerText", "MatTextColumn_td_2_Template", "data_r2", "$implicit", "dataAccessor", "name", "MatRecycleRows", "ɵfac", "MatRecycleRows_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "standalone", "features", "ɵɵProvidersFeature", "provide", "useClass", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "MatTable", "constructor", "arguments", "stickyCssClass", "needsPositionStickyOnElement", "ɵMatTable_BaseFactory", "MatTable_Factory", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "hostAttrs", "hostVars", "hostBindings", "MatTable_HostBindings", "ɵɵclassProp", "fixedLayout", "exportAs", "useExisting", "useValue", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "MatTable_Template", "ɵɵprojectionDef", "ɵɵtemplate", "ɵɵconditional", "_isServer", "_isNativeHtmlTable", "dependencies", "styles", "encapsulation", "host", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "imports", "MatCellDef", "ɵMatCellDef_BaseFactory", "MatCellDef_Factory", "MatHeaderCellDef", "ɵMatHeaderCellDef_BaseFactory", "MatHeaderCellDef_Factory", "MatFooterCellDef", "ɵMatFooterCellDef_BaseFactory", "MatFooterCellDef_Factory", "MatColumnDef", "_name", "_setNameInput", "_updateColumnCssClassName", "_columnCssClassName", "push", "cssClassFriendlyName", "ɵMatColumnDef_BaseFactory", "MatColumnDef_Factory", "inputs", "ɵɵInputFlags", "MatHeaderCell", "ɵMatHeaderCell_BaseFactory", "MatHeaderCell_Factory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵMatFooterCell_BaseFactory", "MatFooterCell_Factory", "Mat<PERSON>ell", "ɵMatCell_BaseFactory", "MatCell_Factory", "ROW_TEMPLATE", "MatHeaderRowDef", "ɵMatHeaderRowDef_BaseFactory", "MatHeaderRowDef_Factory", "columns", "sticky", "HasDecoratorInputTransform", "ɵɵInputTransformsFeature", "alias", "transform", "MatFooterRowDef", "ɵMatFooterRowDef_BaseFactory", "MatFooterRowDef_Factory", "MatRowDef", "ɵMatRowDef_BaseFactory", "MatRowDef_Factory", "when", "MatHeaderRow", "ɵMatHeaderRow_BaseFactory", "MatHeaderRow_Factory", "MatHeaderRow_Template", "MatFooterRow", "ɵMatFooterRow_BaseFactory", "MatFooterRow_Factory", "MatFooterRow_Template", "MatRow", "ɵMatRow_BaseFactory", "MatRow_Factory", "MatRow_Template", "MatNoDataRow", "_contentClassName", "ɵMatNoDataRow_BaseFactory", "MatNoDataRow_Factory", "MatTextColumn", "ɵMatTextColumn_BaseFactory", "MatTextColumn_Factory", "MatTextColumn_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "EXPORTED_DECLARATIONS", "MatTableModule", "MatTableModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "MAX_SAFE_INTEGER", "MatTableDataSource", "data", "_data", "value", "Array", "isArray", "next", "_renderChangesSubscription", "_filterData", "filter", "_filter", "sort", "_sort", "_updateChangeSubscription", "paginator", "_paginator", "initialData", "_renderData", "_internalPageChanges", "sortingDataAccessor", "sortHeaderId", "numberValue", "Number", "sortData", "active", "direction", "a", "b", "valueA", "valueB", "valueAType", "valueBType", "comparatorResult", "filterPredicate", "dataStr", "Object", "keys", "reduce", "currentTerm", "key", "toLowerCase", "<PERSON><PERSON><PERSON>er", "trim", "indexOf", "sortChange", "initialized", "pageChange", "page", "dataStream", "filteredData", "pipe", "orderedData", "_orderData", "paginatedData", "_pageData", "unsubscribe", "subscribe", "obj", "_updatePaginator", "length", "slice", "startIndex", "pageIndex", "pageSize", "filteredDataLength", "Promise", "resolve", "then", "lastPageIndex", "Math", "ceil", "newPageIndex", "min", "connect", "disconnect"], "sources": ["E:/Fashion/DFashion/DFashion/frontend/node_modules/@angular/material/fesm2022/table.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, booleanAttribute, NgModule } from '@angular/core';\nimport { CdkTable, CDK_TABLE, _COALESCED_STYLE_SCHEDULER, _CoalescedStyleScheduler, STICKY_POSITIONING_LISTENER, HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet, CdkCellDef, CdkHeaderCellDef, CdkFooterCellDef, CdkColumnDef, CdkHeaderCell, CdkFooterCell, CdkCell, CdkHeaderRowDef, CdkFooterRowDef, CdkRowDef, CdkHeaderRow, CdkCellOutlet, CdkFooterRow, CdkRow, CdkNoDataRow, CdkTextColumn, CdkTableModule } from '@angular/cdk/table';\nimport { _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy, _DisposeViewRepeaterStrategy, DataSource } from '@angular/cdk/collections';\nimport { MatCommonModule } from '@angular/material/core';\nimport { BehaviorSubject, Subject, merge, of, combineLatest } from 'rxjs';\nimport { _isNumberValue } from '@angular/cdk/coercion';\nimport { map } from 'rxjs/operators';\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\nclass MatRecycleRows {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatRecycleRows, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatRecycleRows, isStandalone: true, selector: \"mat-table[recycleRows], table[mat-table][recycleRows]\", providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatRecycleRows, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-table[recycleRows], table[mat-table][recycleRows]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                    standalone: true,\n                }]\n        }] });\nclass MatTable extends CdkTable {\n    constructor() {\n        super(...arguments);\n        /** Overrides the sticky CSS class set by the `CdkTable`. */\n        this.stickyCssClass = 'mat-mdc-table-sticky';\n        /** Overrides the need to add position: sticky on every sticky cell element in `CdkTable`. */\n        this.needsPositionStickyOnElement = false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTable, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: MatTable, isStandalone: true, selector: \"mat-table, table[mat-table]\", host: { properties: { \"class.mdc-table-fixed-layout\": \"fixedLayout\" }, classAttribute: \"mat-mdc-table mdc-data-table__table\" }, providers: [\n            { provide: CdkTable, useExisting: MatTable },\n            { provide: CDK_TABLE, useExisting: MatTable },\n            { provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler },\n            // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n            //  is only included in the build if used.\n            { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n            // Prevent nested tables from seeing this table's StickyPositioningListener.\n            { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n        ], exportAs: [\"matTable\"], usesInheritance: true, ngImport: i0, template: `\n    <ng-content select=\"caption\"/>\n    <ng-content select=\"colgroup, col\"/>\n\n    <!--\n      Unprojected content throws a hydration error so we need this to capture it.\n      It gets removed on the client so it doesn't affect the layout.\n    -->\n    @if (_isServer) {\n      <ng-content/>\n    }\n\n    @if (_isNativeHtmlTable) {\n      <thead role=\"rowgroup\">\n        <ng-container headerRowOutlet/>\n      </thead>\n      <tbody class=\"mdc-data-table__content\" role=\"rowgroup\">\n        <ng-container rowOutlet/>\n        <ng-container noDataRowOutlet/>\n      </tbody>\n      <tfoot role=\"rowgroup\">\n        <ng-container footerRowOutlet/>\n      </tfoot>\n    } @else {\n      <ng-container headerRowOutlet/>\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n      <ng-container footerRowOutlet/>\n    }\n  `, isInline: true, styles: [\".mat-mdc-table-sticky{position:sticky !important}.mdc-data-table{-webkit-overflow-scrolling:touch;display:inline-flex;flex-direction:column;box-sizing:border-box;position:relative}.mdc-data-table__table-container{-webkit-overflow-scrolling:touch;overflow-x:auto;width:100%}.mdc-data-table__table{min-width:100%;border:0;white-space:nowrap;border-spacing:0;table-layout:fixed}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px 0 16px}.mdc-data-table__header-cell--checkbox,.mdc-data-table__cell--checkbox{padding-left:4px;padding-right:0}[dir=rtl] .mdc-data-table__header-cell--checkbox,[dir=rtl] .mdc-data-table__cell--checkbox,.mdc-data-table__header-cell--checkbox[dir=rtl],.mdc-data-table__cell--checkbox[dir=rtl]{padding-left:0;padding-right:4px}mat-table{display:block}mat-header-row{min-height:56px}mat-row,mat-footer-row{min-height:48px}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color)}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-header-headline-font, Roboto, sans-serif);line-height:var(--mat-table-header-headline-line-height);font-size:var(--mat-table-header-headline-size, 14px);font-weight:var(--mat-table-header-headline-weight, 500)}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, Roboto, sans-serif);line-height:var(--mat-table-row-item-label-text-line-height);font-size:var(--mat-table-row-item-label-text-size, 14px);font-weight:var(--mat-table-row-item-label-text-weight)}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-footer-supporting-text-font, Roboto, sans-serif);line-height:var(--mat-table-footer-supporting-text-line-height);font-size:var(--mat-table-footer-supporting-text-size, 14px);font-weight:var(--mat-table-footer-supporting-text-weight);letter-spacing:var(--mat-table-footer-supporting-text-tracking)}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking);font-weight:inherit;line-height:inherit}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking);line-height:inherit}.mdc-data-table__row:last-child .mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking)}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\"], dependencies: [{ kind: \"directive\", type: HeaderRowOutlet, selector: \"[headerRowOutlet]\" }, { kind: \"directive\", type: DataRowOutlet, selector: \"[rowOutlet]\" }, { kind: \"directive\", type: NoDataRowOutlet, selector: \"[noDataRowOutlet]\" }, { kind: \"directive\", type: FooterRowOutlet, selector: \"[footerRowOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTable, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-table, table[mat-table]', exportAs: 'matTable', template: `\n    <ng-content select=\"caption\"/>\n    <ng-content select=\"colgroup, col\"/>\n\n    <!--\n      Unprojected content throws a hydration error so we need this to capture it.\n      It gets removed on the client so it doesn't affect the layout.\n    -->\n    @if (_isServer) {\n      <ng-content/>\n    }\n\n    @if (_isNativeHtmlTable) {\n      <thead role=\"rowgroup\">\n        <ng-container headerRowOutlet/>\n      </thead>\n      <tbody class=\"mdc-data-table__content\" role=\"rowgroup\">\n        <ng-container rowOutlet/>\n        <ng-container noDataRowOutlet/>\n      </tbody>\n      <tfoot role=\"rowgroup\">\n        <ng-container footerRowOutlet/>\n      </tfoot>\n    } @else {\n      <ng-container headerRowOutlet/>\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n      <ng-container footerRowOutlet/>\n    }\n  `, host: {\n                        'class': 'mat-mdc-table mdc-data-table__table',\n                        '[class.mdc-table-fixed-layout]': 'fixedLayout',\n                    }, providers: [\n                        { provide: CdkTable, useExisting: MatTable },\n                        { provide: CDK_TABLE, useExisting: MatTable },\n                        { provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler },\n                        // TODO(michaeljamesparsons) Abstract the view repeater strategy to a directive API so this code\n                        //  is only included in the build if used.\n                        { provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy },\n                        // Prevent nested tables from seeing this table's StickyPositioningListener.\n                        { provide: STICKY_POSITIONING_LISTENER, useValue: null },\n                    ], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, standalone: true, imports: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet], styles: [\".mat-mdc-table-sticky{position:sticky !important}.mdc-data-table{-webkit-overflow-scrolling:touch;display:inline-flex;flex-direction:column;box-sizing:border-box;position:relative}.mdc-data-table__table-container{-webkit-overflow-scrolling:touch;overflow-x:auto;width:100%}.mdc-data-table__table{min-width:100%;border:0;white-space:nowrap;border-spacing:0;table-layout:fixed}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px 0 16px}.mdc-data-table__header-cell--checkbox,.mdc-data-table__cell--checkbox{padding-left:4px;padding-right:0}[dir=rtl] .mdc-data-table__header-cell--checkbox,[dir=rtl] .mdc-data-table__cell--checkbox,.mdc-data-table__header-cell--checkbox[dir=rtl],.mdc-data-table__cell--checkbox[dir=rtl]{padding-left:0;padding-right:4px}mat-table{display:block}mat-header-row{min-height:56px}mat-row,mat-footer-row{min-height:48px}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color)}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-header-headline-font, Roboto, sans-serif);line-height:var(--mat-table-header-headline-line-height);font-size:var(--mat-table-header-headline-size, 14px);font-weight:var(--mat-table-header-headline-weight, 500)}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, Roboto, sans-serif);line-height:var(--mat-table-row-item-label-text-line-height);font-size:var(--mat-table-row-item-label-text-size, 14px);font-weight:var(--mat-table-row-item-label-text-weight)}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-footer-supporting-text-font, Roboto, sans-serif);line-height:var(--mat-table-footer-supporting-text-line-height);font-size:var(--mat-table-footer-supporting-text-size, 14px);font-weight:var(--mat-table-footer-supporting-text-weight);letter-spacing:var(--mat-table-footer-supporting-text-tracking)}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking);font-weight:inherit;line-height:inherit}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking);line-height:inherit}.mdc-data-table__row:last-child .mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking)}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}\"] }]\n        }] });\n\n/**\n * Cell definition for the mat-table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\nclass MatCellDef extends CdkCellDef {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCellDef, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCellDef, isStandalone: true, selector: \"[matCellDef]\", providers: [{ provide: CdkCellDef, useExisting: MatCellDef }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matCellDef]',\n                    providers: [{ provide: CdkCellDef, useExisting: MatCellDef }],\n                    standalone: true,\n                }]\n        }] });\n/**\n * Header cell definition for the mat-table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\nclass MatHeaderCellDef extends CdkHeaderCellDef {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatHeaderCellDef, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatHeaderCellDef, isStandalone: true, selector: \"[matHeaderCellDef]\", providers: [{ provide: CdkHeaderCellDef, useExisting: MatHeaderCellDef }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatHeaderCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matHeaderCellDef]',\n                    providers: [{ provide: CdkHeaderCellDef, useExisting: MatHeaderCellDef }],\n                    standalone: true,\n                }]\n        }] });\n/**\n * Footer cell definition for the mat-table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\nclass MatFooterCellDef extends CdkFooterCellDef {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFooterCellDef, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatFooterCellDef, isStandalone: true, selector: \"[matFooterCellDef]\", providers: [{ provide: CdkFooterCellDef, useExisting: MatFooterCellDef }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFooterCellDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matFooterCellDef]',\n                    providers: [{ provide: CdkFooterCellDef, useExisting: MatFooterCellDef }],\n                    standalone: true,\n                }]\n        }] });\n/**\n * Column definition for the mat-table.\n * Defines a set of cells available for a table column.\n */\nclass MatColumnDef extends CdkColumnDef {\n    /** Unique name for this column. */\n    get name() {\n        return this._name;\n    }\n    set name(name) {\n        this._setNameInput(name);\n    }\n    /**\n     * Add \"mat-column-\" prefix in addition to \"cdk-column-\" prefix.\n     * In the future, this will only add \"mat-column-\" and columnCssClassName\n     * will change from type string[] to string.\n     * @docs-private\n     */\n    _updateColumnCssClassName() {\n        super._updateColumnCssClassName();\n        this._columnCssClassName.push(`mat-column-${this.cssClassFriendlyName}`);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatColumnDef, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatColumnDef, isStandalone: true, selector: \"[matColumnDef]\", inputs: { name: [\"matColumnDef\", \"name\"] }, providers: [\n            { provide: CdkColumnDef, useExisting: MatColumnDef },\n            { provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: MatColumnDef },\n        ], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatColumnDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matColumnDef]',\n                    providers: [\n                        { provide: CdkColumnDef, useExisting: MatColumnDef },\n                        { provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: MatColumnDef },\n                    ],\n                    standalone: true,\n                }]\n        }], propDecorators: { name: [{\n                type: Input,\n                args: ['matColumnDef']\n            }] } });\n/** Header cell template container that adds the right classes and role. */\nclass MatHeaderCell extends CdkHeaderCell {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatHeaderCell, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatHeaderCell, isStandalone: true, selector: \"mat-header-cell, th[mat-header-cell]\", host: { attributes: { \"role\": \"columnheader\" }, classAttribute: \"mat-mdc-header-cell mdc-data-table__header-cell\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatHeaderCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-header-cell, th[mat-header-cell]',\n                    host: {\n                        'class': 'mat-mdc-header-cell mdc-data-table__header-cell',\n                        'role': 'columnheader',\n                    },\n                    standalone: true,\n                }]\n        }] });\n/** Footer cell template container that adds the right classes and role. */\nclass MatFooterCell extends CdkFooterCell {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFooterCell, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatFooterCell, isStandalone: true, selector: \"mat-footer-cell, td[mat-footer-cell]\", host: { classAttribute: \"mat-mdc-footer-cell mdc-data-table__cell\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFooterCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-footer-cell, td[mat-footer-cell]',\n                    host: {\n                        'class': 'mat-mdc-footer-cell mdc-data-table__cell',\n                    },\n                    standalone: true,\n                }]\n        }] });\n/** Cell template container that adds the right classes and role. */\nclass MatCell extends CdkCell {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCell, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCell, isStandalone: true, selector: \"mat-cell, td[mat-cell]\", host: { classAttribute: \"mat-mdc-cell mdc-data-table__cell\" }, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCell, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-cell, td[mat-cell]',\n                    host: {\n                        'class': 'mat-mdc-cell mdc-data-table__cell',\n                    },\n                    standalone: true,\n                }]\n        }] });\n\n// We can't reuse `CDK_ROW_TEMPLATE` because it's incompatible with local compilation mode.\nconst ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n/**\n * Header row definition for the mat-table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\nclass MatHeaderRowDef extends CdkHeaderRowDef {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatHeaderRowDef, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatHeaderRowDef, isStandalone: true, selector: \"[matHeaderRowDef]\", inputs: { columns: [\"matHeaderRowDef\", \"columns\"], sticky: [\"matHeaderRowDefSticky\", \"sticky\", booleanAttribute] }, providers: [{ provide: CdkHeaderRowDef, useExisting: MatHeaderRowDef }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatHeaderRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matHeaderRowDef]',\n                    providers: [{ provide: CdkHeaderRowDef, useExisting: MatHeaderRowDef }],\n                    inputs: [\n                        { name: 'columns', alias: 'matHeaderRowDef' },\n                        { name: 'sticky', alias: 'matHeaderRowDefSticky', transform: booleanAttribute },\n                    ],\n                    standalone: true,\n                }]\n        }] });\n/**\n * Footer row definition for the mat-table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\nclass MatFooterRowDef extends CdkFooterRowDef {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFooterRowDef, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatFooterRowDef, isStandalone: true, selector: \"[matFooterRowDef]\", inputs: { columns: [\"matFooterRowDef\", \"columns\"], sticky: [\"matFooterRowDefSticky\", \"sticky\", booleanAttribute] }, providers: [{ provide: CdkFooterRowDef, useExisting: MatFooterRowDef }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFooterRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matFooterRowDef]',\n                    providers: [{ provide: CdkFooterRowDef, useExisting: MatFooterRowDef }],\n                    inputs: [\n                        { name: 'columns', alias: 'matFooterRowDef' },\n                        { name: 'sticky', alias: 'matFooterRowDefSticky', transform: booleanAttribute },\n                    ],\n                    standalone: true,\n                }]\n        }] });\n/**\n * Data row definition for the mat-table.\n * Captures the data row's template and other properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\nclass MatRowDef extends CdkRowDef {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatRowDef, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatRowDef, isStandalone: true, selector: \"[matRowDef]\", inputs: { columns: [\"matRowDefColumns\", \"columns\"], when: [\"matRowDefWhen\", \"when\"] }, providers: [{ provide: CdkRowDef, useExisting: MatRowDef }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatRowDef, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matRowDef]',\n                    providers: [{ provide: CdkRowDef, useExisting: MatRowDef }],\n                    inputs: [\n                        { name: 'columns', alias: 'matRowDefColumns' },\n                        { name: 'when', alias: 'matRowDefWhen' },\n                    ],\n                    standalone: true,\n                }]\n        }] });\n/** Header template container that contains the cell outlet. Adds the right class and role. */\nclass MatHeaderRow extends CdkHeaderRow {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatHeaderRow, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatHeaderRow, isStandalone: true, selector: \"mat-header-row, tr[mat-header-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"mat-mdc-header-row mdc-data-table__header-row\" }, providers: [{ provide: CdkHeaderRow, useExisting: MatHeaderRow }], exportAs: [\"matHeaderRow\"], usesInheritance: true, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatHeaderRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-header-row, tr[mat-header-row]',\n                    template: ROW_TEMPLATE,\n                    host: {\n                        'class': 'mat-mdc-header-row mdc-data-table__header-row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    exportAs: 'matHeaderRow',\n                    providers: [{ provide: CdkHeaderRow, useExisting: MatHeaderRow }],\n                    standalone: true,\n                    imports: [CdkCellOutlet],\n                }]\n        }] });\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\nclass MatFooterRow extends CdkFooterRow {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFooterRow, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatFooterRow, isStandalone: true, selector: \"mat-footer-row, tr[mat-footer-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"mat-mdc-footer-row mdc-data-table__row\" }, providers: [{ provide: CdkFooterRow, useExisting: MatFooterRow }], exportAs: [\"matFooterRow\"], usesInheritance: true, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFooterRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-footer-row, tr[mat-footer-row]',\n                    template: ROW_TEMPLATE,\n                    host: {\n                        'class': 'mat-mdc-footer-row mdc-data-table__row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    exportAs: 'matFooterRow',\n                    providers: [{ provide: CdkFooterRow, useExisting: MatFooterRow }],\n                    standalone: true,\n                    imports: [CdkCellOutlet],\n                }]\n        }] });\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\nclass MatRow extends CdkRow {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatRow, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatRow, isStandalone: true, selector: \"mat-row, tr[mat-row]\", host: { attributes: { \"role\": \"row\" }, classAttribute: \"mat-mdc-row mdc-data-table__row\" }, providers: [{ provide: CdkRow, useExisting: MatRow }], exportAs: [\"matRow\"], usesInheritance: true, ngImport: i0, template: \"<ng-container cdkCellOutlet></ng-container>\", isInline: true, dependencies: [{ kind: \"directive\", type: CdkCellOutlet, selector: \"[cdkCellOutlet]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatRow, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-row, tr[mat-row]',\n                    template: ROW_TEMPLATE,\n                    host: {\n                        'class': 'mat-mdc-row mdc-data-table__row',\n                        'role': 'row',\n                    },\n                    // See note on CdkTable for explanation on why this uses the default change detection strategy.\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    encapsulation: ViewEncapsulation.None,\n                    exportAs: 'matRow',\n                    providers: [{ provide: CdkRow, useExisting: MatRow }],\n                    standalone: true,\n                    imports: [CdkCellOutlet],\n                }]\n        }] });\n/** Row that can be used to display a message when no data is shown in the table. */\nclass MatNoDataRow extends CdkNoDataRow {\n    constructor() {\n        super(...arguments);\n        this._contentClassName = 'mat-mdc-no-data-row';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatNoDataRow, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatNoDataRow, isStandalone: true, selector: \"ng-template[matNoDataRow]\", providers: [{ provide: CdkNoDataRow, useExisting: MatNoDataRow }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatNoDataRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matNoDataRow]',\n                    providers: [{ provide: CdkNoDataRow, useExisting: MatNoDataRow }],\n                    standalone: true,\n                }]\n        }] });\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\nclass MatTextColumn extends CdkTextColumn {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTextColumn, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatTextColumn, isStandalone: true, selector: \"mat-text-column\", usesInheritance: true, ngImport: i0, template: `\n    <ng-container matColumnDef>\n      <th mat-header-cell *matHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td mat-cell *matCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: MatColumnDef, selector: \"[matColumnDef]\", inputs: [\"matColumnDef\"] }, { kind: \"directive\", type: MatHeaderCellDef, selector: \"[matHeaderCellDef]\" }, { kind: \"directive\", type: MatHeaderCell, selector: \"mat-header-cell, th[mat-header-cell]\" }, { kind: \"directive\", type: MatCellDef, selector: \"[matCellDef]\" }, { kind: \"directive\", type: MatCell, selector: \"mat-cell, td[mat-cell]\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTextColumn, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'mat-text-column',\n                    template: `\n    <ng-container matColumnDef>\n      <th mat-header-cell *matHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td mat-cell *matCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n                    encapsulation: ViewEncapsulation.None,\n                    // Change detection is intentionally not set to OnPush. This component's template will be provided\n                    // to the table to be inserted into its view. This is problematic when change detection runs since\n                    // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n                    // mean's the template in the table's view will not have the updated value (and in fact will cause\n                    // an ExpressionChangedAfterItHasBeenCheckedError).\n                    // tslint:disable-next-line:validate-decorators\n                    changeDetection: ChangeDetectionStrategy.Default,\n                    standalone: true,\n                    imports: [MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell],\n                }]\n        }] });\n\nconst EXPORTED_DECLARATIONS = [\n    // Table\n    MatTable,\n    MatRecycleRows,\n    // Template defs\n    MatHeaderCellDef,\n    MatHeaderRowDef,\n    MatColumnDef,\n    MatCellDef,\n    MatRowDef,\n    MatFooterCellDef,\n    MatFooterRowDef,\n    // Cell directives\n    MatHeaderCell,\n    MatCell,\n    MatFooterCell,\n    // Row directives\n    MatHeaderRow,\n    MatRow,\n    MatFooterRow,\n    MatNoDataRow,\n    MatTextColumn,\n];\nclass MatTableModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTableModule, imports: [MatCommonModule, CdkTableModule, \n            // Table\n            MatTable,\n            MatRecycleRows,\n            // Template defs\n            MatHeaderCellDef,\n            MatHeaderRowDef,\n            MatColumnDef,\n            MatCellDef,\n            MatRowDef,\n            MatFooterCellDef,\n            MatFooterRowDef,\n            // Cell directives\n            MatHeaderCell,\n            MatCell,\n            MatFooterCell,\n            // Row directives\n            MatHeaderRow,\n            MatRow,\n            MatFooterRow,\n            MatNoDataRow,\n            MatTextColumn], exports: [MatCommonModule, \n            // Table\n            MatTable,\n            MatRecycleRows,\n            // Template defs\n            MatHeaderCellDef,\n            MatHeaderRowDef,\n            MatColumnDef,\n            MatCellDef,\n            MatRowDef,\n            MatFooterCellDef,\n            MatFooterRowDef,\n            // Cell directives\n            MatHeaderCell,\n            MatCell,\n            MatFooterCell,\n            // Row directives\n            MatHeaderRow,\n            MatRow,\n            MatFooterRow,\n            MatNoDataRow,\n            MatTextColumn] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTableModule, imports: [MatCommonModule, CdkTableModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, CdkTableModule, ...EXPORTED_DECLARATIONS],\n                    exports: [MatCommonModule, EXPORTED_DECLARATIONS],\n                }]\n        }] });\n\n/**\n * Corresponds to `Number.MAX_SAFE_INTEGER`. Moved out into a variable here due to\n * flaky browser support and the value not being defined in Closure's typings.\n */\nconst MAX_SAFE_INTEGER = 9007199254740991;\n/**\n * Data source that accepts a client-side data array and includes native support of filtering,\n * sorting (using MatSort), and pagination (using MatPaginator).\n *\n * Allows for sort customization by overriding sortingDataAccessor, which defines how data\n * properties are accessed. Also allows for filter customization by overriding filterPredicate,\n * which defines how row data is converted to a string for filter matching.\n *\n * **Note:** This class is meant to be a simple data source to help you get started. As such\n * it isn't equipped to handle some more advanced cases like robust i18n support or server-side\n * interactions. If your app needs to support more advanced use cases, consider implementing your\n * own `DataSource`.\n */\nclass MatTableDataSource extends DataSource {\n    /** Array of data that should be rendered by the table, where each object represents one row. */\n    get data() {\n        return this._data.value;\n    }\n    set data(data) {\n        data = Array.isArray(data) ? data : [];\n        this._data.next(data);\n        // Normally the `filteredData` is updated by the re-render\n        // subscription, but that won't happen if it's inactive.\n        if (!this._renderChangesSubscription) {\n            this._filterData(data);\n        }\n    }\n    /**\n     * Filter term that should be used to filter out objects from the data array. To override how\n     * data objects match to this filter string, provide a custom function for filterPredicate.\n     */\n    get filter() {\n        return this._filter.value;\n    }\n    set filter(filter) {\n        this._filter.next(filter);\n        // Normally the `filteredData` is updated by the re-render\n        // subscription, but that won't happen if it's inactive.\n        if (!this._renderChangesSubscription) {\n            this._filterData(this.data);\n        }\n    }\n    /**\n     * Instance of the MatSort directive used by the table to control its sorting. Sort changes\n     * emitted by the MatSort will trigger an update to the table's rendered data.\n     */\n    get sort() {\n        return this._sort;\n    }\n    set sort(sort) {\n        this._sort = sort;\n        this._updateChangeSubscription();\n    }\n    /**\n     * Instance of the paginator component used by the table to control what page of the data is\n     * displayed. Page changes emitted by the paginator will trigger an update to the\n     * table's rendered data.\n     *\n     * Note that the data source uses the paginator's properties to calculate which page of data\n     * should be displayed. If the paginator receives its properties as template inputs,\n     * e.g. `[pageLength]=100` or `[pageIndex]=1`, then be sure that the paginator's view has been\n     * initialized before assigning it to this data source.\n     */\n    get paginator() {\n        return this._paginator;\n    }\n    set paginator(paginator) {\n        this._paginator = paginator;\n        this._updateChangeSubscription();\n    }\n    constructor(initialData = []) {\n        super();\n        /** Stream emitting render data to the table (depends on ordered data changes). */\n        this._renderData = new BehaviorSubject([]);\n        /** Stream that emits when a new filter string is set on the data source. */\n        this._filter = new BehaviorSubject('');\n        /** Used to react to internal changes of the paginator that are made by the data source itself. */\n        this._internalPageChanges = new Subject();\n        /**\n         * Subscription to the changes that should trigger an update to the table's rendered rows, such\n         * as filtering, sorting, pagination, or base data changes.\n         */\n        this._renderChangesSubscription = null;\n        /**\n         * Data accessor function that is used for accessing data properties for sorting through\n         * the default sortData function.\n         * This default function assumes that the sort header IDs (which defaults to the column name)\n         * matches the data's properties (e.g. column Xyz represents data['Xyz']).\n         * May be set to a custom function for different behavior.\n         * @param data Data object that is being accessed.\n         * @param sortHeaderId The name of the column that represents the data.\n         */\n        this.sortingDataAccessor = (data, sortHeaderId) => {\n            const value = data[sortHeaderId];\n            if (_isNumberValue(value)) {\n                const numberValue = Number(value);\n                // Numbers beyond `MAX_SAFE_INTEGER` can't be compared reliably so we\n                // leave them as strings. For more info: https://goo.gl/y5vbSg\n                return numberValue < MAX_SAFE_INTEGER ? numberValue : value;\n            }\n            return value;\n        };\n        /**\n         * Gets a sorted copy of the data array based on the state of the MatSort. Called\n         * after changes are made to the filtered data or when sort changes are emitted from MatSort.\n         * By default, the function retrieves the active sort and its direction and compares data\n         * by retrieving data using the sortingDataAccessor. May be overridden for a custom implementation\n         * of data ordering.\n         * @param data The array of data that should be sorted.\n         * @param sort The connected MatSort that holds the current sort state.\n         */\n        this.sortData = (data, sort) => {\n            const active = sort.active;\n            const direction = sort.direction;\n            if (!active || direction == '') {\n                return data;\n            }\n            return data.sort((a, b) => {\n                let valueA = this.sortingDataAccessor(a, active);\n                let valueB = this.sortingDataAccessor(b, active);\n                // If there are data in the column that can be converted to a number,\n                // it must be ensured that the rest of the data\n                // is of the same type so as not to order incorrectly.\n                const valueAType = typeof valueA;\n                const valueBType = typeof valueB;\n                if (valueAType !== valueBType) {\n                    if (valueAType === 'number') {\n                        valueA += '';\n                    }\n                    if (valueBType === 'number') {\n                        valueB += '';\n                    }\n                }\n                // If both valueA and valueB exist (truthy), then compare the two. Otherwise, check if\n                // one value exists while the other doesn't. In this case, existing value should come last.\n                // This avoids inconsistent results when comparing values to undefined/null.\n                // If neither value exists, return 0 (equal).\n                let comparatorResult = 0;\n                if (valueA != null && valueB != null) {\n                    // Check if one value is greater than the other; if equal, comparatorResult should remain 0.\n                    if (valueA > valueB) {\n                        comparatorResult = 1;\n                    }\n                    else if (valueA < valueB) {\n                        comparatorResult = -1;\n                    }\n                }\n                else if (valueA != null) {\n                    comparatorResult = 1;\n                }\n                else if (valueB != null) {\n                    comparatorResult = -1;\n                }\n                return comparatorResult * (direction == 'asc' ? 1 : -1);\n            });\n        };\n        /**\n         * Checks if a data object matches the data source's filter string. By default, each data object\n         * is converted to a string of its properties and returns true if the filter has\n         * at least one occurrence in that string. By default, the filter string has its whitespace\n         * trimmed and the match is case-insensitive. May be overridden for a custom implementation of\n         * filter matching.\n         * @param data Data object used to check against the filter.\n         * @param filter Filter string that has been set on the data source.\n         * @returns Whether the filter matches against the data\n         */\n        this.filterPredicate = (data, filter) => {\n            // Transform the data into a lowercase string of all property values.\n            const dataStr = Object.keys(data)\n                .reduce((currentTerm, key) => {\n                // Use an obscure Unicode character to delimit the words in the concatenated string.\n                // This avoids matches where the values of two columns combined will match the user's query\n                // (e.g. `Flute` and `Stop` will match `Test`). The character is intended to be something\n                // that has a very low chance of being typed in by somebody in a text field. This one in\n                // particular is \"White up-pointing triangle with dot\" from\n                // https://en.wikipedia.org/wiki/List_of_Unicode_characters\n                return currentTerm + data[key] + '◬';\n            }, '')\n                .toLowerCase();\n            // Transform the filter by converting it to lowercase and removing whitespace.\n            const transformedFilter = filter.trim().toLowerCase();\n            return dataStr.indexOf(transformedFilter) != -1;\n        };\n        this._data = new BehaviorSubject(initialData);\n        this._updateChangeSubscription();\n    }\n    /**\n     * Subscribe to changes that should trigger an update to the table's rendered rows. When the\n     * changes occur, process the current state of the filter, sort, and pagination along with\n     * the provided base data and send it to the table for rendering.\n     */\n    _updateChangeSubscription() {\n        // Sorting and/or pagination should be watched if sort and/or paginator are provided.\n        // The events should emit whenever the component emits a change or initializes, or if no\n        // component is provided, a stream with just a null event should be provided.\n        // The `sortChange` and `pageChange` acts as a signal to the combineLatests below so that the\n        // pipeline can progress to the next step. Note that the value from these streams are not used,\n        // they purely act as a signal to progress in the pipeline.\n        const sortChange = this._sort\n            ? merge(this._sort.sortChange, this._sort.initialized)\n            : of(null);\n        const pageChange = this._paginator\n            ? merge(this._paginator.page, this._internalPageChanges, this._paginator.initialized)\n            : of(null);\n        const dataStream = this._data;\n        // Watch for base data or filter changes to provide a filtered set of data.\n        const filteredData = combineLatest([dataStream, this._filter]).pipe(map(([data]) => this._filterData(data)));\n        // Watch for filtered data or sort changes to provide an ordered set of data.\n        const orderedData = combineLatest([filteredData, sortChange]).pipe(map(([data]) => this._orderData(data)));\n        // Watch for ordered data or page changes to provide a paged set of data.\n        const paginatedData = combineLatest([orderedData, pageChange]).pipe(map(([data]) => this._pageData(data)));\n        // Watched for paged data changes and send the result to the table to render.\n        this._renderChangesSubscription?.unsubscribe();\n        this._renderChangesSubscription = paginatedData.subscribe(data => this._renderData.next(data));\n    }\n    /**\n     * Returns a filtered data array where each filter object contains the filter string within\n     * the result of the filterPredicate function. If no filter is set, returns the data array\n     * as provided.\n     */\n    _filterData(data) {\n        // If there is a filter string, filter out data that does not contain it.\n        // Each data object is converted to a string using the function defined by filterPredicate.\n        // May be overridden for customization.\n        this.filteredData =\n            this.filter == null || this.filter === ''\n                ? data\n                : data.filter(obj => this.filterPredicate(obj, this.filter));\n        if (this.paginator) {\n            this._updatePaginator(this.filteredData.length);\n        }\n        return this.filteredData;\n    }\n    /**\n     * Returns a sorted copy of the data if MatSort has a sort applied, otherwise just returns the\n     * data array as provided. Uses the default data accessor for data lookup, unless a\n     * sortDataAccessor function is defined.\n     */\n    _orderData(data) {\n        // If there is no active sort or direction, return the data without trying to sort.\n        if (!this.sort) {\n            return data;\n        }\n        return this.sortData(data.slice(), this.sort);\n    }\n    /**\n     * Returns a paged slice of the provided data array according to the provided paginator's page\n     * index and length. If there is no paginator provided, returns the data array as provided.\n     */\n    _pageData(data) {\n        if (!this.paginator) {\n            return data;\n        }\n        const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n        return data.slice(startIndex, startIndex + this.paginator.pageSize);\n    }\n    /**\n     * Updates the paginator to reflect the length of the filtered data, and makes sure that the page\n     * index does not exceed the paginator's last page. Values are changed in a resolved promise to\n     * guard against making property changes within a round of change detection.\n     */\n    _updatePaginator(filteredDataLength) {\n        Promise.resolve().then(() => {\n            const paginator = this.paginator;\n            if (!paginator) {\n                return;\n            }\n            paginator.length = filteredDataLength;\n            // If the page index is set beyond the page, reduce it to the last page.\n            if (paginator.pageIndex > 0) {\n                const lastPageIndex = Math.ceil(paginator.length / paginator.pageSize) - 1 || 0;\n                const newPageIndex = Math.min(paginator.pageIndex, lastPageIndex);\n                if (newPageIndex !== paginator.pageIndex) {\n                    paginator.pageIndex = newPageIndex;\n                    // Since the paginator only emits after user-generated changes,\n                    // we need our own stream so we know to should re-render the data.\n                    this._internalPageChanges.next();\n                }\n            }\n        });\n    }\n    /**\n     * Used by the MatTable. Called when it connects to the data source.\n     * @docs-private\n     */\n    connect() {\n        if (!this._renderChangesSubscription) {\n            this._updateChangeSubscription();\n        }\n        return this._renderData;\n    }\n    /**\n     * Used by the MatTable. Called when it disconnects from the data source.\n     * @docs-private\n     */\n    disconnect() {\n        this._renderChangesSubscription?.unsubscribe();\n        this._renderChangesSubscription = null;\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatCell, MatCellDef, MatColumnDef, MatFooterCell, MatFooterCellDef, MatFooterRow, MatFooterRowDef, MatHeaderCell, MatHeaderCellDef, MatHeaderRow, MatHeaderRowDef, MatNoDataRow, MatRecycleRows, MatRow, MatRowDef, MatTable, MatTableDataSource, MatTableModule, MatTextColumn };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,eAAe;AACnI,SAASC,QAAQ,EAAEC,SAAS,EAAEC,0BAA0B,EAAEC,wBAAwB,EAAEC,2BAA2B,EAAEC,eAAe,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,EAAEC,eAAe,EAAEC,eAAe,EAAEC,SAAS,EAAEC,YAAY,EAAEC,aAAa,EAAEC,YAAY,EAAEC,MAAM,EAAEC,YAAY,EAAEC,aAAa,EAAEC,cAAc,QAAQ,oBAAoB;AAC/b,SAASC,uBAAuB,EAAEC,4BAA4B,EAAEC,4BAA4B,EAAEC,UAAU,QAAQ,0BAA0B;AAC1I,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,eAAe,EAAEC,OAAO,EAAEC,KAAK,EAAEC,EAAE,EAAEC,aAAa,QAAQ,MAAM;AACzE,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,GAAG,QAAQ,gBAAgB;;AAEpC;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,gCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAKoGjD,EAAE,CAAAmD,YAAA,KAsCpF,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtCiFjD,EAAE,CAAAqD,cAAA,cA0C1E,CAAC;IA1CuErD,EAAE,CAAAsD,kBAAA,KA2ChE,CAAC;IA3C6DtD,EAAE,CAAAuD,YAAA,CA4CzF,CAAC;IA5CsFvD,EAAE,CAAAqD,cAAA,cA6C1C,CAAC;IA7CuCrD,EAAE,CAAAsD,kBAAA,KA8CtE,CAAC,KACK,CAAC;IA/C6DtD,EAAE,CAAAuD,YAAA,CAgDzF,CAAC;IAhDsFvD,EAAE,CAAAqD,cAAA,cAiD1E,CAAC;IAjDuErD,EAAE,CAAAsD,kBAAA,KAkDhE,CAAC;IAlD6DtD,EAAE,CAAAuD,YAAA,CAmDzF,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAAP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnDsFjD,EAAE,CAAAsD,kBAAA,KAqDlE,CAAC,KACP,CAAC,KACK,CAAC,KACD,CAAC;EAAA;AAAA;AAAA,SAAAG,4BAAAR,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxD+DjD,EAAE,CAAAqD,cAAA,WAyZ9B,CAAC;IAzZ2BrD,EAAE,CAAA0D,MAAA,EA2ZjG,CAAC;IA3Z8F1D,EAAE,CAAAuD,YAAA,CA2Z5F,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAU,MAAA,GA3ZyF3D,EAAE,CAAA4D,aAAA;IAAF5D,EAAE,CAAA6D,WAAA,eAAAF,MAAA,CAAAG,OAyZ/B,CAAC;IAzZ4B9D,EAAE,CAAA+D,SAAA,CA2ZjG,CAAC;IA3Z8F/D,EAAE,CAAAgE,kBAAA,MAAAL,MAAA,CAAAM,UAAA,KA2ZjG,CAAC;EAAA;AAAA;AAAA,SAAAC,4BAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3Z8FjD,EAAE,CAAAqD,cAAA,WA4ZhC,CAAC;IA5Z6BrD,EAAE,CAAA0D,MAAA,EA8ZjG,CAAC;IA9Z8F1D,EAAE,CAAAuD,YAAA,CA8Z5F,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAkB,OAAA,GAAAjB,GAAA,CAAAkB,SAAA;IAAA,MAAAT,MAAA,GA9ZyF3D,EAAE,CAAA4D,aAAA;IAAF5D,EAAE,CAAA6D,WAAA,eAAAF,MAAA,CAAAG,OA4ZjC,CAAC;IA5Z8B9D,EAAE,CAAA+D,SAAA,CA8ZjG,CAAC;IA9Z8F/D,EAAE,CAAAgE,kBAAA,MAAAL,MAAA,CAAAU,YAAA,CAAAF,OAAA,EAAAR,MAAA,CAAAW,IAAA,MA8ZjG,CAAC;EAAA;AAAA;AA/ZN,MAAMC,cAAc,CAAC;EACjB;IAAS,IAAI,CAACC,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFH,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACI,IAAI,kBAD8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EACJN,cAAc;MAAAO,SAAA;MAAAC,UAAA;MAAAC,QAAA,GADZhF,EAAE,CAAAiF,kBAAA,CAC8G,CAAC;QAAEC,OAAO,EAAEhD,uBAAuB;QAAEiD,QAAQ,EAAEhD;MAA6B,CAAC,CAAC;IAAA,EAAiB;EAAE;AACrT;AACA;EAAA,QAAAiD,SAAA,oBAAAA,SAAA,KAHoGpF,EAAE,CAAAqF,iBAAA,CAGXd,cAAc,EAAc,CAAC;IAC5GM,IAAI,EAAE5E,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uDAAuD;MACjEC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEhD,uBAAuB;QAAEiD,QAAQ,EAAEhD;MAA6B,CAAC,CAAC;MACzF4C,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMU,QAAQ,SAASjF,QAAQ,CAAC;EAC5BkF,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB;IACA,IAAI,CAACC,cAAc,GAAG,sBAAsB;IAC5C;IACA,IAAI,CAACC,4BAA4B,GAAG,KAAK;EAC7C;EACA;IAAS,IAAI,CAACrB,IAAI;MAAA,IAAAsB,qBAAA;MAAA,gBAAAC,iBAAArB,CAAA;QAAA,QAAAoB,qBAAA,KAAAA,qBAAA,GAnB8E9F,EAAE,CAAAgG,qBAAA,CAmBQP,QAAQ,IAAAf,CAAA,IAARe,QAAQ;MAAA;IAAA,IAAqD;EAAE;EACzK;IAAS,IAAI,CAACQ,IAAI,kBApB8EjG,EAAE,CAAAkG,iBAAA;MAAArB,IAAA,EAoBJY,QAAQ;MAAAX,SAAA;MAAAqB,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,sBAAArD,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApBNjD,EAAE,CAAAuG,WAAA,2BAAArD,GAAA,CAAAsD,WAoBG,CAAC;QAAA;MAAA;MAAAC,QAAA;MAAA1B,UAAA;MAAAC,QAAA,GApBNhF,EAAE,CAAAiF,kBAAA,CAoB8M,CACxS;QAAEC,OAAO,EAAE1E,QAAQ;QAAEkG,WAAW,EAAEjB;MAAS,CAAC,EAC5C;QAAEP,OAAO,EAAEzE,SAAS;QAAEiG,WAAW,EAAEjB;MAAS,CAAC,EAC7C;QAAEP,OAAO,EAAExE,0BAA0B;QAAEyE,QAAQ,EAAExE;MAAyB,CAAC;MAC3E;MACA;MACA;QAAEuE,OAAO,EAAEhD,uBAAuB;QAAEiD,QAAQ,EAAE/C;MAA6B,CAAC;MAC5E;MACA;QAAE8C,OAAO,EAAEtE,2BAA2B;QAAE+F,QAAQ,EAAE;MAAK,CAAC,CAC3D,GA7B2F3G,EAAE,CAAA4G,0BAAA,EAAF5G,EAAE,CAAA6G,mBAAA;MAAAC,kBAAA,EAAA/D,GAAA;MAAAgE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kBAAAlE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjD,EAAE,CAAAoH,eAAA,CAAAtE,GAAA;UAAF9C,EAAE,CAAAmD,YAAA,EA8BrE,CAAC;UA9BkEnD,EAAE,CAAAmD,YAAA,KA+B/D,CAAC;UA/B4DnD,EAAE,CAAAqH,UAAA,IAAArE,+BAAA,MAqClF,CAAC,IAAAI,+BAAA,MAIQ,CAAC,IAAAI,+BAAA,MAWlB,CAAC;QAAA;QAAA,IAAAP,EAAA;UApDuFjD,EAAE,CAAA+D,SAAA,EAuClG,CAAC;UAvC+F/D,EAAE,CAAAsH,aAAA,IAAApE,GAAA,CAAAqE,SAAA,SAuClG,CAAC;UAvC+FvH,EAAE,CAAA+D,SAAA,CAyDlG,CAAC;UAzD+F/D,EAAE,CAAAsH,aAAA,IAAApE,GAAA,CAAAsE,kBAAA,QAyDlG,CAAC;QAAA;MAAA;MAAAC,YAAA,GAC22K5G,eAAe,EAA8DC,aAAa,EAAwDC,eAAe,EAA8DC,eAAe;MAAA0G,MAAA;MAAAC,aAAA;IAAA,EAAoI;EAAE;AACpuL;AACA;EAAA,QAAAvC,SAAA,oBAAAA,SAAA,KA5DoGpF,EAAE,CAAAqF,iBAAA,CA4DXI,QAAQ,EAAc,CAAC;IACtGZ,IAAI,EAAE3E,SAAS;IACfoF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAEkB,QAAQ,EAAE,UAAU;MAAES,QAAQ,EAAE;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MAAEU,IAAI,EAAE;QACa,OAAO,EAAE,qCAAqC;QAC9C,gCAAgC,EAAE;MACtC,CAAC;MAAEpC,SAAS,EAAE,CACV;QAAEN,OAAO,EAAE1E,QAAQ;QAAEkG,WAAW,EAAEjB;MAAS,CAAC,EAC5C;QAAEP,OAAO,EAAEzE,SAAS;QAAEiG,WAAW,EAAEjB;MAAS,CAAC,EAC7C;QAAEP,OAAO,EAAExE,0BAA0B;QAAEyE,QAAQ,EAAExE;MAAyB,CAAC;MAC3E;MACA;MACA;QAAEuE,OAAO,EAAEhD,uBAAuB;QAAEiD,QAAQ,EAAE/C;MAA6B,CAAC;MAC5E;MACA;QAAE8C,OAAO,EAAEtE,2BAA2B;QAAE+F,QAAQ,EAAE;MAAK,CAAC,CAC3D;MAAEgB,aAAa,EAAExH,iBAAiB,CAAC0H,IAAI;MAAEC,eAAe,EAAE1H,uBAAuB,CAAC2H,OAAO;MAAEhD,UAAU,EAAE,IAAI;MAAEiD,OAAO,EAAE,CAACnH,eAAe,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,CAAC;MAAE0G,MAAM,EAAE,CAAC,qyKAAqyK;IAAE,CAAC;EACx/K,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMO,UAAU,SAAShH,UAAU,CAAC;EAChC;IAAS,IAAI,CAACuD,IAAI;MAAA,IAAA0D,uBAAA;MAAA,gBAAAC,mBAAAzD,CAAA;QAAA,QAAAwD,uBAAA,KAAAA,uBAAA,GA/G8ElI,EAAE,CAAAgG,qBAAA,CA+GQiC,UAAU,IAAAvD,CAAA,IAAVuD,UAAU;MAAA;IAAA,IAAqD;EAAE;EAC3K;IAAS,IAAI,CAACtD,IAAI,kBAhH8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EAgHJoD,UAAU;MAAAnD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAhHRhF,EAAE,CAAAiF,kBAAA,CAgHiE,CAAC;QAAEC,OAAO,EAAEjE,UAAU;QAAEyF,WAAW,EAAEuB;MAAW,CAAC,CAAC,GAhHrHjI,EAAE,CAAA4G,0BAAA;IAAA,EAgH2J;EAAE;AACnQ;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAlHoGpF,EAAE,CAAAqF,iBAAA,CAkHX4C,UAAU,EAAc,CAAC;IACxGpD,IAAI,EAAE5E,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEjE,UAAU;QAAEyF,WAAW,EAAEuB;MAAW,CAAC,CAAC;MAC7DlD,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMqD,gBAAgB,SAASlH,gBAAgB,CAAC;EAC5C;IAAS,IAAI,CAACsD,IAAI;MAAA,IAAA6D,6BAAA;MAAA,gBAAAC,yBAAA5D,CAAA;QAAA,QAAA2D,6BAAA,KAAAA,6BAAA,GA/H8ErI,EAAE,CAAAgG,qBAAA,CA+HQoC,gBAAgB,IAAA1D,CAAA,IAAhB0D,gBAAgB;MAAA;IAAA,IAAqD;EAAE;EACjL;IAAS,IAAI,CAACzD,IAAI,kBAhI8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EAgIJuD,gBAAgB;MAAAtD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAhIdhF,EAAE,CAAAiF,kBAAA,CAgI6E,CAAC;QAAEC,OAAO,EAAEhE,gBAAgB;QAAEwF,WAAW,EAAE0B;MAAiB,CAAC,CAAC,GAhI7IpI,EAAE,CAAA4G,0BAAA;IAAA,EAgImL;EAAE;AAC3R;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAlIoGpF,EAAE,CAAAqF,iBAAA,CAkIX+C,gBAAgB,EAAc,CAAC;IAC9GvD,IAAI,EAAE5E,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEhE,gBAAgB;QAAEwF,WAAW,EAAE0B;MAAiB,CAAC,CAAC;MACzErD,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMwD,gBAAgB,SAASpH,gBAAgB,CAAC;EAC5C;IAAS,IAAI,CAACqD,IAAI;MAAA,IAAAgE,6BAAA;MAAA,gBAAAC,yBAAA/D,CAAA;QAAA,QAAA8D,6BAAA,KAAAA,6BAAA,GA/I8ExI,EAAE,CAAAgG,qBAAA,CA+IQuC,gBAAgB,IAAA7D,CAAA,IAAhB6D,gBAAgB;MAAA;IAAA,IAAqD;EAAE;EACjL;IAAS,IAAI,CAAC5D,IAAI,kBAhJ8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EAgJJ0D,gBAAgB;MAAAzD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAhJdhF,EAAE,CAAAiF,kBAAA,CAgJ6E,CAAC;QAAEC,OAAO,EAAE/D,gBAAgB;QAAEuF,WAAW,EAAE6B;MAAiB,CAAC,CAAC,GAhJ7IvI,EAAE,CAAA4G,0BAAA;IAAA,EAgJmL;EAAE;AAC3R;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAlJoGpF,EAAE,CAAAqF,iBAAA,CAkJXkD,gBAAgB,EAAc,CAAC;IAC9G1D,IAAI,EAAE5E,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAE/D,gBAAgB;QAAEuF,WAAW,EAAE6B;MAAiB,CAAC,CAAC;MACzExD,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAM2D,YAAY,SAAStH,YAAY,CAAC;EACpC;EACA,IAAIkD,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACqE,KAAK;EACrB;EACA,IAAIrE,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACsE,aAAa,CAACtE,IAAI,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIuE,yBAAyBA,CAAA,EAAG;IACxB,KAAK,CAACA,yBAAyB,CAAC,CAAC;IACjC,IAAI,CAACC,mBAAmB,CAACC,IAAI,CAAC,cAAc,IAAI,CAACC,oBAAoB,EAAE,CAAC;EAC5E;EACA;IAAS,IAAI,CAACxE,IAAI;MAAA,IAAAyE,yBAAA;MAAA,gBAAAC,qBAAAxE,CAAA;QAAA,QAAAuE,yBAAA,KAAAA,yBAAA,GAhL8EjJ,EAAE,CAAAgG,qBAAA,CAgLQ0C,YAAY,IAAAhE,CAAA,IAAZgE,YAAY;MAAA;IAAA,IAAqD;EAAE;EAC7K;IAAS,IAAI,CAAC/D,IAAI,kBAjL8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EAiLJ6D,YAAY;MAAA5D,SAAA;MAAAqE,MAAA;QAAA7E,IAAA,GAjLVtE,EAAE,CAAAoJ,YAAA,CAAAvB,IAAA;MAAA;MAAA9C,UAAA;MAAAC,QAAA,GAAFhF,EAAE,CAAAiF,kBAAA,CAiLiH,CAC3M;QAAEC,OAAO,EAAE9D,YAAY;QAAEsF,WAAW,EAAEgC;MAAa,CAAC,EACpD;QAAExD,OAAO,EAAE,4BAA4B;QAAEwB,WAAW,EAAEgC;MAAa,CAAC,CACvE,GApL2F1I,EAAE,CAAA4G,0BAAA;IAAA,EAoLrD;EAAE;AACnD;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAtLoGpF,EAAE,CAAAqF,iBAAA,CAsLXqD,YAAY,EAAc,CAAC;IAC1G7D,IAAI,EAAE5E,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAE,CACP;QAAEN,OAAO,EAAE9D,YAAY;QAAEsF,WAAW,EAAEgC;MAAa,CAAC,EACpD;QAAExD,OAAO,EAAE,4BAA4B;QAAEwB,WAAW,EAAEgC;MAAa,CAAC,CACvE;MACD3D,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAET,IAAI,EAAE,CAAC;MACrBO,IAAI,EAAExE,KAAK;MACXiF,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAM+D,aAAa,SAAShI,aAAa,CAAC;EACtC;IAAS,IAAI,CAACmD,IAAI;MAAA,IAAA8E,0BAAA;MAAA,gBAAAC,sBAAA7E,CAAA;QAAA,QAAA4E,0BAAA,KAAAA,0BAAA,GAtM8EtJ,EAAE,CAAAgG,qBAAA,CAsMQqD,aAAa,IAAA3E,CAAA,IAAb2E,aAAa;MAAA;IAAA,IAAqD;EAAE;EAC9K;IAAS,IAAI,CAAC1E,IAAI,kBAvM8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EAuMJwE,aAAa;MAAAvE,SAAA;MAAAqB,SAAA,WAAsG,cAAc;MAAApB,UAAA;MAAAC,QAAA,GAvM/HhF,EAAE,CAAA4G,0BAAA;IAAA,EAuM4O;EAAE;AACpV;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAzMoGpF,EAAE,CAAAqF,iBAAA,CAyMXgE,aAAa,EAAc,CAAC;IAC3GxE,IAAI,EAAE5E,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChDqC,IAAI,EAAE;QACF,OAAO,EAAE,iDAAiD;QAC1D,MAAM,EAAE;MACZ,CAAC;MACD7C,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMyE,aAAa,SAASlI,aAAa,CAAC;EACtC;IAAS,IAAI,CAACkD,IAAI;MAAA,IAAAiF,0BAAA;MAAA,gBAAAC,sBAAAhF,CAAA;QAAA,QAAA+E,0BAAA,KAAAA,0BAAA,GAtN8EzJ,EAAE,CAAAgG,qBAAA,CAsNQwD,aAAa,IAAA9E,CAAA,IAAb8E,aAAa;MAAA;IAAA,IAAqD;EAAE;EAC9K;IAAS,IAAI,CAAC7E,IAAI,kBAvN8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EAuNJ2E,aAAa;MAAA1E,SAAA;MAAAqB,SAAA;MAAApB,UAAA;MAAAC,QAAA,GAvNXhF,EAAE,CAAA4G,0BAAA;IAAA,EAuN6L;EAAE;AACrS;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAzNoGpF,EAAE,CAAAqF,iBAAA,CAyNXmE,aAAa,EAAc,CAAC;IAC3G3E,IAAI,EAAE5E,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChDqC,IAAI,EAAE;QACF,OAAO,EAAE;MACb,CAAC;MACD7C,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAM4E,OAAO,SAASpI,OAAO,CAAC;EAC1B;IAAS,IAAI,CAACiD,IAAI;MAAA,IAAAoF,oBAAA;MAAA,gBAAAC,gBAAAnF,CAAA;QAAA,QAAAkF,oBAAA,KAAAA,oBAAA,GArO8E5J,EAAE,CAAAgG,qBAAA,CAqOQ2D,OAAO,IAAAjF,CAAA,IAAPiF,OAAO;MAAA;IAAA,IAAqD;EAAE;EACxK;IAAS,IAAI,CAAChF,IAAI,kBAtO8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EAsOJ8E,OAAO;MAAA7E,SAAA;MAAAqB,SAAA;MAAApB,UAAA;MAAAC,QAAA,GAtOLhF,EAAE,CAAA4G,0BAAA;IAAA,EAsOkK;EAAE;AAC1Q;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAxOoGpF,EAAE,CAAAqF,iBAAA,CAwOXsE,OAAO,EAAc,CAAC;IACrG9E,IAAI,EAAE5E,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClCqC,IAAI,EAAE;QACF,OAAO,EAAE;MACb,CAAC;MACD7C,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA,MAAM+E,YAAY,GAAG,6CAA6C;AAClE;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASvI,eAAe,CAAC;EAC1C;IAAS,IAAI,CAACgD,IAAI;MAAA,IAAAwF,4BAAA;MAAA,gBAAAC,wBAAAvF,CAAA;QAAA,QAAAsF,4BAAA,KAAAA,4BAAA,GA1P8EhK,EAAE,CAAAgG,qBAAA,CA0PQ+D,eAAe,IAAArF,CAAA,IAAfqF,eAAe;MAAA;IAAA,IAAqD;EAAE;EAChL;IAAS,IAAI,CAACpF,IAAI,kBA3P8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EA2PJkF,eAAe;MAAAjF,SAAA;MAAAqE,MAAA;QAAAe,OAAA,GA3PblK,EAAE,CAAAoJ,YAAA,CAAAvB,IAAA;QAAAsC,MAAA,GAAFnK,EAAE,CAAAoJ,YAAA,CAAAgB,0BAAA,qCA2P+J9J,gBAAgB;MAAA;MAAAyE,UAAA;MAAAC,QAAA,GA3PjLhF,EAAE,CAAAiF,kBAAA,CA2P+L,CAAC;QAAEC,OAAO,EAAE1D,eAAe;QAAEkF,WAAW,EAAEqD;MAAgB,CAAC,CAAC,GA3P7P/J,EAAE,CAAAqK,wBAAA,EAAFrK,EAAE,CAAA4G,0BAAA;IAAA,EA2PmS;EAAE;AAC3Y;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KA7PoGpF,EAAE,CAAAqF,iBAAA,CA6PX0E,eAAe,EAAc,CAAC;IAC7GlF,IAAI,EAAE5E,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAE1D,eAAe;QAAEkF,WAAW,EAAEqD;MAAgB,CAAC,CAAC;MACvEZ,MAAM,EAAE,CACJ;QAAE7E,IAAI,EAAE,SAAS;QAAEgG,KAAK,EAAE;MAAkB,CAAC,EAC7C;QAAEhG,IAAI,EAAE,QAAQ;QAAEgG,KAAK,EAAE,uBAAuB;QAAEC,SAAS,EAAEjK;MAAiB,CAAC,CAClF;MACDyE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMyF,eAAe,SAAS/I,eAAe,CAAC;EAC1C;IAAS,IAAI,CAAC+C,IAAI;MAAA,IAAAiG,4BAAA;MAAA,gBAAAC,wBAAAhG,CAAA;QAAA,QAAA+F,4BAAA,KAAAA,4BAAA,GA9Q8EzK,EAAE,CAAAgG,qBAAA,CA8QQwE,eAAe,IAAA9F,CAAA,IAAf8F,eAAe;MAAA;IAAA,IAAqD;EAAE;EAChL;IAAS,IAAI,CAAC7F,IAAI,kBA/Q8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EA+QJ2F,eAAe;MAAA1F,SAAA;MAAAqE,MAAA;QAAAe,OAAA,GA/QblK,EAAE,CAAAoJ,YAAA,CAAAvB,IAAA;QAAAsC,MAAA,GAAFnK,EAAE,CAAAoJ,YAAA,CAAAgB,0BAAA,qCA+Q+J9J,gBAAgB;MAAA;MAAAyE,UAAA;MAAAC,QAAA,GA/QjLhF,EAAE,CAAAiF,kBAAA,CA+Q+L,CAAC;QAAEC,OAAO,EAAEzD,eAAe;QAAEiF,WAAW,EAAE8D;MAAgB,CAAC,CAAC,GA/Q7PxK,EAAE,CAAAqK,wBAAA,EAAFrK,EAAE,CAAA4G,0BAAA;IAAA,EA+QmS;EAAE;AAC3Y;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAjRoGpF,EAAE,CAAAqF,iBAAA,CAiRXmF,eAAe,EAAc,CAAC;IAC7G3F,IAAI,EAAE5E,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEzD,eAAe;QAAEiF,WAAW,EAAE8D;MAAgB,CAAC,CAAC;MACvErB,MAAM,EAAE,CACJ;QAAE7E,IAAI,EAAE,SAAS;QAAEgG,KAAK,EAAE;MAAkB,CAAC,EAC7C;QAAEhG,IAAI,EAAE,QAAQ;QAAEgG,KAAK,EAAE,uBAAuB;QAAEC,SAAS,EAAEjK;MAAiB,CAAC,CAClF;MACDyE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAM4F,SAAS,SAASjJ,SAAS,CAAC;EAC9B;IAAS,IAAI,CAAC8C,IAAI;MAAA,IAAAoG,sBAAA;MAAA,gBAAAC,kBAAAnG,CAAA;QAAA,QAAAkG,sBAAA,KAAAA,sBAAA,GAnS8E5K,EAAE,CAAAgG,qBAAA,CAmSQ2E,SAAS,IAAAjG,CAAA,IAATiG,SAAS;MAAA;IAAA,IAAqD;EAAE;EAC1K;IAAS,IAAI,CAAChG,IAAI,kBApS8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EAoSJ8F,SAAS;MAAA7F,SAAA;MAAAqE,MAAA;QAAAe,OAAA,GApSPlK,EAAE,CAAAoJ,YAAA,CAAAvB,IAAA;QAAAiD,IAAA,GAAF9K,EAAE,CAAAoJ,YAAA,CAAAvB,IAAA;MAAA;MAAA9C,UAAA;MAAAC,QAAA,GAAFhF,EAAE,CAAAiF,kBAAA,CAoSsJ,CAAC;QAAEC,OAAO,EAAExD,SAAS;QAAEgF,WAAW,EAAEiE;MAAU,CAAC,CAAC,GApSxM3K,EAAE,CAAA4G,0BAAA;IAAA,EAoS8O;EAAE;AACtV;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAtSoGpF,EAAE,CAAAqF,iBAAA,CAsSXsF,SAAS,EAAc,CAAC;IACvG9F,IAAI,EAAE5E,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aAAa;MACvBC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAExD,SAAS;QAAEgF,WAAW,EAAEiE;MAAU,CAAC,CAAC;MAC3DxB,MAAM,EAAE,CACJ;QAAE7E,IAAI,EAAE,SAAS;QAAEgG,KAAK,EAAE;MAAmB,CAAC,EAC9C;QAAEhG,IAAI,EAAE,MAAM;QAAEgG,KAAK,EAAE;MAAgB,CAAC,CAC3C;MACDvF,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMgG,YAAY,SAASpJ,YAAY,CAAC;EACpC;IAAS,IAAI,CAAC6C,IAAI;MAAA,IAAAwG,yBAAA;MAAA,gBAAAC,qBAAAvG,CAAA;QAAA,QAAAsG,yBAAA,KAAAA,yBAAA,GApT8EhL,EAAE,CAAAgG,qBAAA,CAoTQ+E,YAAY,IAAArG,CAAA,IAAZqG,YAAY;MAAA;IAAA,IAAqD;EAAE;EAC7K;IAAS,IAAI,CAAC9E,IAAI,kBArT8EjG,EAAE,CAAAkG,iBAAA;MAAArB,IAAA,EAqTJkG,YAAY;MAAAjG,SAAA;MAAAqB,SAAA,WAAoG,KAAK;MAAAM,QAAA;MAAA1B,UAAA;MAAAC,QAAA,GArTnHhF,EAAE,CAAAiF,kBAAA,CAqTmM,CAAC;QAAEC,OAAO,EAAEvD,YAAY;QAAE+E,WAAW,EAAEqE;MAAa,CAAC,CAAC,GArT3P/K,EAAE,CAAA4G,0BAAA,EAAF5G,EAAE,CAAA6G,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAgE,sBAAAjI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjD,EAAE,CAAAsD,kBAAA,KAqTiX,CAAC;QAAA;MAAA;MAAAmE,YAAA,GAA6D7F,aAAa;MAAA+F,aAAA;IAAA,EAAkI;EAAE;AACtqB;AACA;EAAA,QAAAvC,SAAA,oBAAAA,SAAA,KAvToGpF,EAAE,CAAAqF,iBAAA,CAuTX0F,YAAY,EAAc,CAAC;IAC1GlG,IAAI,EAAE3E,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9C2B,QAAQ,EAAE4C,YAAY;MACtBlC,IAAI,EAAE;QACF,OAAO,EAAE,+CAA+C;QACxD,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAE,eAAe,EAAE1H,uBAAuB,CAAC2H,OAAO;MAChDJ,aAAa,EAAExH,iBAAiB,CAAC0H,IAAI;MACrCpB,QAAQ,EAAE,cAAc;MACxBjB,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEvD,YAAY;QAAE+E,WAAW,EAAEqE;MAAa,CAAC,CAAC;MACjEhG,UAAU,EAAE,IAAI;MAChBiD,OAAO,EAAE,CAACpG,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMuJ,YAAY,SAAStJ,YAAY,CAAC;EACpC;IAAS,IAAI,CAAC2C,IAAI;MAAA,IAAA4G,yBAAA;MAAA,gBAAAC,qBAAA3G,CAAA;QAAA,QAAA0G,yBAAA,KAAAA,yBAAA,GA5U8EpL,EAAE,CAAAgG,qBAAA,CA4UQmF,YAAY,IAAAzG,CAAA,IAAZyG,YAAY;MAAA;IAAA,IAAqD;EAAE;EAC7K;IAAS,IAAI,CAAClF,IAAI,kBA7U8EjG,EAAE,CAAAkG,iBAAA;MAAArB,IAAA,EA6UJsG,YAAY;MAAArG,SAAA;MAAAqB,SAAA,WAAoG,KAAK;MAAAM,QAAA;MAAA1B,UAAA;MAAAC,QAAA,GA7UnHhF,EAAE,CAAAiF,kBAAA,CA6U4L,CAAC;QAAEC,OAAO,EAAErD,YAAY;QAAE6E,WAAW,EAAEyE;MAAa,CAAC,CAAC,GA7UpPnL,EAAE,CAAA4G,0BAAA,EAAF5G,EAAE,CAAA6G,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAoE,sBAAArI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjD,EAAE,CAAAsD,kBAAA,KA6U0W,CAAC;QAAA;MAAA;MAAAmE,YAAA,GAA6D7F,aAAa;MAAA+F,aAAA;IAAA,EAAkI;EAAE;AAC/pB;AACA;EAAA,QAAAvC,SAAA,oBAAAA,SAAA,KA/UoGpF,EAAE,CAAAqF,iBAAA,CA+UX8F,YAAY,EAAc,CAAC;IAC1GtG,IAAI,EAAE3E,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9C2B,QAAQ,EAAE4C,YAAY;MACtBlC,IAAI,EAAE;QACF,OAAO,EAAE,wCAAwC;QACjD,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAE,eAAe,EAAE1H,uBAAuB,CAAC2H,OAAO;MAChDJ,aAAa,EAAExH,iBAAiB,CAAC0H,IAAI;MACrCpB,QAAQ,EAAE,cAAc;MACxBjB,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAErD,YAAY;QAAE6E,WAAW,EAAEyE;MAAa,CAAC,CAAC;MACjEpG,UAAU,EAAE,IAAI;MAChBiD,OAAO,EAAE,CAACpG,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAM2J,MAAM,SAASzJ,MAAM,CAAC;EACxB;IAAS,IAAI,CAAC0C,IAAI;MAAA,IAAAgH,mBAAA;MAAA,gBAAAC,eAAA/G,CAAA;QAAA,QAAA8G,mBAAA,KAAAA,mBAAA,GApW8ExL,EAAE,CAAAgG,qBAAA,CAoWQuF,MAAM,IAAA7G,CAAA,IAAN6G,MAAM;MAAA;IAAA,IAAqD;EAAE;EACvK;IAAS,IAAI,CAACtF,IAAI,kBArW8EjG,EAAE,CAAAkG,iBAAA;MAAArB,IAAA,EAqWJ0G,MAAM;MAAAzG,SAAA;MAAAqB,SAAA,WAAsF,KAAK;MAAAM,QAAA;MAAA1B,UAAA;MAAAC,QAAA,GArW/FhF,EAAE,CAAAiF,kBAAA,CAqWiK,CAAC;QAAEC,OAAO,EAAEpD,MAAM;QAAE4E,WAAW,EAAE6E;MAAO,CAAC,CAAC,GArW7MvL,EAAE,CAAA4G,0BAAA,EAAF5G,EAAE,CAAA6G,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAwE,gBAAAzI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjD,EAAE,CAAAsD,kBAAA,KAqW6T,CAAC;QAAA;MAAA;MAAAmE,YAAA,GAA6D7F,aAAa;MAAA+F,aAAA;IAAA,EAAkI;EAAE;AAClnB;AACA;EAAA,QAAAvC,SAAA,oBAAAA,SAAA,KAvWoGpF,EAAE,CAAAqF,iBAAA,CAuWXkG,MAAM,EAAc,CAAC;IACpG1G,IAAI,EAAE3E,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChC2B,QAAQ,EAAE4C,YAAY;MACtBlC,IAAI,EAAE;QACF,OAAO,EAAE,iCAAiC;QAC1C,MAAM,EAAE;MACZ,CAAC;MACD;MACA;MACAE,eAAe,EAAE1H,uBAAuB,CAAC2H,OAAO;MAChDJ,aAAa,EAAExH,iBAAiB,CAAC0H,IAAI;MACrCpB,QAAQ,EAAE,QAAQ;MAClBjB,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEpD,MAAM;QAAE4E,WAAW,EAAE6E;MAAO,CAAC,CAAC;MACrDxG,UAAU,EAAE,IAAI;MAChBiD,OAAO,EAAE,CAACpG,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAM+J,YAAY,SAAS5J,YAAY,CAAC;EACpC2D,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACiG,iBAAiB,GAAG,qBAAqB;EAClD;EACA;IAAS,IAAI,CAACpH,IAAI;MAAA,IAAAqH,yBAAA;MAAA,gBAAAC,qBAAApH,CAAA;QAAA,QAAAmH,yBAAA,KAAAA,yBAAA,GAhY8E7L,EAAE,CAAAgG,qBAAA,CAgYQ2F,YAAY,IAAAjH,CAAA,IAAZiH,YAAY;MAAA;IAAA,IAAqD;EAAE;EAC7K;IAAS,IAAI,CAAChH,IAAI,kBAjY8E3E,EAAE,CAAA4E,iBAAA;MAAAC,IAAA,EAiYJ8G,YAAY;MAAA7G,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAjYVhF,EAAE,CAAAiF,kBAAA,CAiYgF,CAAC;QAAEC,OAAO,EAAEnD,YAAY;QAAE2E,WAAW,EAAEiF;MAAa,CAAC,CAAC,GAjYxI3L,EAAE,CAAA4G,0BAAA;IAAA,EAiY8K;EAAE;AACtR;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAnYoGpF,EAAE,CAAAqF,iBAAA,CAmYXsG,YAAY,EAAc,CAAC;IAC1G9G,IAAI,EAAE5E,SAAS;IACfqF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2BAA2B;MACrCC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEnD,YAAY;QAAE2E,WAAW,EAAEiF;MAAa,CAAC,CAAC;MACjE5G,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgH,aAAa,SAAS/J,aAAa,CAAC;EACtC;IAAS,IAAI,CAACwC,IAAI;MAAA,IAAAwH,0BAAA;MAAA,gBAAAC,sBAAAvH,CAAA;QAAA,QAAAsH,0BAAA,KAAAA,0BAAA,GAtZ8EhM,EAAE,CAAAgG,qBAAA,CAsZQ+F,aAAa,IAAArH,CAAA,IAAbqH,aAAa;MAAA;IAAA,IAAqD;EAAE;EAC9K;IAAS,IAAI,CAAC9F,IAAI,kBAvZ8EjG,EAAE,CAAAkG,iBAAA;MAAArB,IAAA,EAuZJkH,aAAa;MAAAjH,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAvZXhF,EAAE,CAAA4G,0BAAA,EAAF5G,EAAE,CAAA6G,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAgF,uBAAAjJ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjD,EAAE,CAAAmM,uBAAA,KAwZxE,CAAC;UAxZqEnM,EAAE,CAAAqH,UAAA,IAAA5D,2BAAA,eAyZ9B,CAAC,IAAAS,2BAAA,eAGH,CAAC;UA5Z6BlE,EAAE,CAAAoM,qBAAA;QAAA;MAAA;MAAA3E,YAAA,GAgavCiB,YAAY,EAAqFN,gBAAgB,EAA+DiB,aAAa,EAAiFpB,UAAU,EAAyD0B,OAAO;MAAAhC,aAAA;IAAA,EAAyI;EAAE;AACliB;AACA;EAAA,QAAAvC,SAAA,oBAAAA,SAAA,KAlaoGpF,EAAE,CAAAqF,iBAAA,CAkaX0G,aAAa,EAAc,CAAC;IAC3GlH,IAAI,EAAE3E,SAAS;IACfoF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3B2B,QAAQ,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBS,aAAa,EAAExH,iBAAiB,CAAC0H,IAAI;MACrC;MACA;MACA;MACA;MACA;MACA;MACAC,eAAe,EAAE1H,uBAAuB,CAAC2H,OAAO;MAChDhD,UAAU,EAAE,IAAI;MAChBiD,OAAO,EAAE,CAACU,YAAY,EAAEN,gBAAgB,EAAEiB,aAAa,EAAEpB,UAAU,EAAE0B,OAAO;IAChF,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM0C,qBAAqB,GAAG;AAC1B;AACA5G,QAAQ,EACRlB,cAAc;AACd;AACA6D,gBAAgB,EAChB2B,eAAe,EACfrB,YAAY,EACZT,UAAU,EACV0C,SAAS,EACTpC,gBAAgB,EAChBiC,eAAe;AACf;AACAnB,aAAa,EACbM,OAAO,EACPH,aAAa;AACb;AACAuB,YAAY,EACZQ,MAAM,EACNJ,YAAY,EACZQ,YAAY,EACZI,aAAa,CAChB;AACD,MAAMO,cAAc,CAAC;EACjB;IAAS,IAAI,CAAC9H,IAAI,YAAA+H,uBAAA7H,CAAA;MAAA,YAAAA,CAAA,IAAwF4H,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACE,IAAI,kBAtd8ExM,EAAE,CAAAyM,gBAAA;MAAA5H,IAAA,EAsdSyH;IAAc,EA0ChG;EAAE;EAC3B;IAAS,IAAI,CAACI,IAAI,kBAjgB8E1M,EAAE,CAAA2M,gBAAA;MAAA3E,OAAA,GAigBmC1F,eAAe,EAAEL,cAAc,EAAEK,eAAe;IAAA,EAAI;EAAE;AAC/L;AACA;EAAA,QAAA8C,SAAA,oBAAAA,SAAA,KAngBoGpF,EAAE,CAAAqF,iBAAA,CAmgBXiH,cAAc,EAAc,CAAC;IAC5GzH,IAAI,EAAEtE,QAAQ;IACd+E,IAAI,EAAE,CAAC;MACC0C,OAAO,EAAE,CAAC1F,eAAe,EAAEL,cAAc,EAAE,GAAGoK,qBAAqB,CAAC;MACpEO,OAAO,EAAE,CAACtK,eAAe,EAAE+J,qBAAqB;IACpD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMQ,gBAAgB,GAAG,gBAAgB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,SAASzK,UAAU,CAAC;EACxC;EACA,IAAI0K,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK,CAACC,KAAK;EAC3B;EACA,IAAIF,IAAIA,CAACA,IAAI,EAAE;IACXA,IAAI,GAAGG,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE;IACtC,IAAI,CAACC,KAAK,CAACI,IAAI,CAACL,IAAI,CAAC;IACrB;IACA;IACA,IAAI,CAAC,IAAI,CAACM,0BAA0B,EAAE;MAClC,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIQ,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACC,OAAO,CAACP,KAAK;EAC7B;EACA,IAAIM,MAAMA,CAACA,MAAM,EAAE;IACf,IAAI,CAACC,OAAO,CAACJ,IAAI,CAACG,MAAM,CAAC;IACzB;IACA;IACA,IAAI,CAAC,IAAI,CAACF,0BAA0B,EAAE;MAClC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACP,IAAI,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIU,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACC,KAAK,GAAGD,IAAI;IACjB,IAAI,CAACE,yBAAyB,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,SAASA,CAACA,SAAS,EAAE;IACrB,IAAI,CAACC,UAAU,GAAGD,SAAS;IAC3B,IAAI,CAACD,yBAAyB,CAAC,CAAC;EACpC;EACAjI,WAAWA,CAACoI,WAAW,GAAG,EAAE,EAAE;IAC1B,KAAK,CAAC,CAAC;IACP;IACA,IAAI,CAACC,WAAW,GAAG,IAAIxL,eAAe,CAAC,EAAE,CAAC;IAC1C;IACA,IAAI,CAACiL,OAAO,GAAG,IAAIjL,eAAe,CAAC,EAAE,CAAC;IACtC;IACA,IAAI,CAACyL,oBAAoB,GAAG,IAAIxL,OAAO,CAAC,CAAC;IACzC;AACR;AACA;AACA;IACQ,IAAI,CAAC6K,0BAA0B,GAAG,IAAI;IACtC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACY,mBAAmB,GAAG,CAAClB,IAAI,EAAEmB,YAAY,KAAK;MAC/C,MAAMjB,KAAK,GAAGF,IAAI,CAACmB,YAAY,CAAC;MAChC,IAAItL,cAAc,CAACqK,KAAK,CAAC,EAAE;QACvB,MAAMkB,WAAW,GAAGC,MAAM,CAACnB,KAAK,CAAC;QACjC;QACA;QACA,OAAOkB,WAAW,GAAGtB,gBAAgB,GAAGsB,WAAW,GAAGlB,KAAK;MAC/D;MACA,OAAOA,KAAK;IAChB,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACoB,QAAQ,GAAG,CAACtB,IAAI,EAAEU,IAAI,KAAK;MAC5B,MAAMa,MAAM,GAAGb,IAAI,CAACa,MAAM;MAC1B,MAAMC,SAAS,GAAGd,IAAI,CAACc,SAAS;MAChC,IAAI,CAACD,MAAM,IAAIC,SAAS,IAAI,EAAE,EAAE;QAC5B,OAAOxB,IAAI;MACf;MACA,OAAOA,IAAI,CAACU,IAAI,CAAC,CAACe,CAAC,EAAEC,CAAC,KAAK;QACvB,IAAIC,MAAM,GAAG,IAAI,CAACT,mBAAmB,CAACO,CAAC,EAAEF,MAAM,CAAC;QAChD,IAAIK,MAAM,GAAG,IAAI,CAACV,mBAAmB,CAACQ,CAAC,EAAEH,MAAM,CAAC;QAChD;QACA;QACA;QACA,MAAMM,UAAU,GAAG,OAAOF,MAAM;QAChC,MAAMG,UAAU,GAAG,OAAOF,MAAM;QAChC,IAAIC,UAAU,KAAKC,UAAU,EAAE;UAC3B,IAAID,UAAU,KAAK,QAAQ,EAAE;YACzBF,MAAM,IAAI,EAAE;UAChB;UACA,IAAIG,UAAU,KAAK,QAAQ,EAAE;YACzBF,MAAM,IAAI,EAAE;UAChB;QACJ;QACA;QACA;QACA;QACA;QACA,IAAIG,gBAAgB,GAAG,CAAC;QACxB,IAAIJ,MAAM,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,EAAE;UAClC;UACA,IAAID,MAAM,GAAGC,MAAM,EAAE;YACjBG,gBAAgB,GAAG,CAAC;UACxB,CAAC,MACI,IAAIJ,MAAM,GAAGC,MAAM,EAAE;YACtBG,gBAAgB,GAAG,CAAC,CAAC;UACzB;QACJ,CAAC,MACI,IAAIJ,MAAM,IAAI,IAAI,EAAE;UACrBI,gBAAgB,GAAG,CAAC;QACxB,CAAC,MACI,IAAIH,MAAM,IAAI,IAAI,EAAE;UACrBG,gBAAgB,GAAG,CAAC,CAAC;QACzB;QACA,OAAOA,gBAAgB,IAAIP,SAAS,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC3D,CAAC,CAAC;IACN,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACQ,eAAe,GAAG,CAAChC,IAAI,EAAEQ,MAAM,KAAK;MACrC;MACA,MAAMyB,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACnC,IAAI,CAAC,CAC5BoC,MAAM,CAAC,CAACC,WAAW,EAAEC,GAAG,KAAK;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA,OAAOD,WAAW,GAAGrC,IAAI,CAACsC,GAAG,CAAC,GAAG,GAAG;MACxC,CAAC,EAAE,EAAE,CAAC,CACDC,WAAW,CAAC,CAAC;MAClB;MACA,MAAMC,iBAAiB,GAAGhC,MAAM,CAACiC,IAAI,CAAC,CAAC,CAACF,WAAW,CAAC,CAAC;MACrD,OAAON,OAAO,CAACS,OAAO,CAACF,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;IACD,IAAI,CAACvC,KAAK,GAAG,IAAIzK,eAAe,CAACuL,WAAW,CAAC;IAC7C,IAAI,CAACH,yBAAyB,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;EACIA,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,MAAM+B,UAAU,GAAG,IAAI,CAAChC,KAAK,GACvBjL,KAAK,CAAC,IAAI,CAACiL,KAAK,CAACgC,UAAU,EAAE,IAAI,CAAChC,KAAK,CAACiC,WAAW,CAAC,GACpDjN,EAAE,CAAC,IAAI,CAAC;IACd,MAAMkN,UAAU,GAAG,IAAI,CAAC/B,UAAU,GAC5BpL,KAAK,CAAC,IAAI,CAACoL,UAAU,CAACgC,IAAI,EAAE,IAAI,CAAC7B,oBAAoB,EAAE,IAAI,CAACH,UAAU,CAAC8B,WAAW,CAAC,GACnFjN,EAAE,CAAC,IAAI,CAAC;IACd,MAAMoN,UAAU,GAAG,IAAI,CAAC9C,KAAK;IAC7B;IACA,MAAM+C,YAAY,GAAGpN,aAAa,CAAC,CAACmN,UAAU,EAAE,IAAI,CAACtC,OAAO,CAAC,CAAC,CAACwC,IAAI,CAACnN,GAAG,CAAC,CAAC,CAACkK,IAAI,CAAC,KAAK,IAAI,CAACO,WAAW,CAACP,IAAI,CAAC,CAAC,CAAC;IAC5G;IACA,MAAMkD,WAAW,GAAGtN,aAAa,CAAC,CAACoN,YAAY,EAAEL,UAAU,CAAC,CAAC,CAACM,IAAI,CAACnN,GAAG,CAAC,CAAC,CAACkK,IAAI,CAAC,KAAK,IAAI,CAACmD,UAAU,CAACnD,IAAI,CAAC,CAAC,CAAC;IAC1G;IACA,MAAMoD,aAAa,GAAGxN,aAAa,CAAC,CAACsN,WAAW,EAAEL,UAAU,CAAC,CAAC,CAACI,IAAI,CAACnN,GAAG,CAAC,CAAC,CAACkK,IAAI,CAAC,KAAK,IAAI,CAACqD,SAAS,CAACrD,IAAI,CAAC,CAAC,CAAC;IAC1G;IACA,IAAI,CAACM,0BAA0B,EAAEgD,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAChD,0BAA0B,GAAG8C,aAAa,CAACG,SAAS,CAACvD,IAAI,IAAI,IAAI,CAACgB,WAAW,CAACX,IAAI,CAACL,IAAI,CAAC,CAAC;EAClG;EACA;AACJ;AACA;AACA;AACA;EACIO,WAAWA,CAACP,IAAI,EAAE;IACd;IACA;IACA;IACA,IAAI,CAACgD,YAAY,GACb,IAAI,CAACxC,MAAM,IAAI,IAAI,IAAI,IAAI,CAACA,MAAM,KAAK,EAAE,GACnCR,IAAI,GACJA,IAAI,CAACQ,MAAM,CAACgD,GAAG,IAAI,IAAI,CAACxB,eAAe,CAACwB,GAAG,EAAE,IAAI,CAAChD,MAAM,CAAC,CAAC;IACpE,IAAI,IAAI,CAACK,SAAS,EAAE;MAChB,IAAI,CAAC4C,gBAAgB,CAAC,IAAI,CAACT,YAAY,CAACU,MAAM,CAAC;IACnD;IACA,OAAO,IAAI,CAACV,YAAY;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACIG,UAAUA,CAACnD,IAAI,EAAE;IACb;IACA,IAAI,CAAC,IAAI,CAACU,IAAI,EAAE;MACZ,OAAOV,IAAI;IACf;IACA,OAAO,IAAI,CAACsB,QAAQ,CAACtB,IAAI,CAAC2D,KAAK,CAAC,CAAC,EAAE,IAAI,CAACjD,IAAI,CAAC;EACjD;EACA;AACJ;AACA;AACA;EACI2C,SAASA,CAACrD,IAAI,EAAE;IACZ,IAAI,CAAC,IAAI,CAACa,SAAS,EAAE;MACjB,OAAOb,IAAI;IACf;IACA,MAAM4D,UAAU,GAAG,IAAI,CAAC/C,SAAS,CAACgD,SAAS,GAAG,IAAI,CAAChD,SAAS,CAACiD,QAAQ;IACrE,OAAO9D,IAAI,CAAC2D,KAAK,CAACC,UAAU,EAAEA,UAAU,GAAG,IAAI,CAAC/C,SAAS,CAACiD,QAAQ,CAAC;EACvE;EACA;AACJ;AACA;AACA;AACA;EACIL,gBAAgBA,CAACM,kBAAkB,EAAE;IACjCC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,MAAMrD,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,IAAI,CAACA,SAAS,EAAE;QACZ;MACJ;MACAA,SAAS,CAAC6C,MAAM,GAAGK,kBAAkB;MACrC;MACA,IAAIlD,SAAS,CAACgD,SAAS,GAAG,CAAC,EAAE;QACzB,MAAMM,aAAa,GAAGC,IAAI,CAACC,IAAI,CAACxD,SAAS,CAAC6C,MAAM,GAAG7C,SAAS,CAACiD,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;QAC/E,MAAMQ,YAAY,GAAGF,IAAI,CAACG,GAAG,CAAC1D,SAAS,CAACgD,SAAS,EAAEM,aAAa,CAAC;QACjE,IAAIG,YAAY,KAAKzD,SAAS,CAACgD,SAAS,EAAE;UACtChD,SAAS,CAACgD,SAAS,GAAGS,YAAY;UAClC;UACA;UACA,IAAI,CAACrD,oBAAoB,CAACZ,IAAI,CAAC,CAAC;QACpC;MACJ;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACImE,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAAClE,0BAA0B,EAAE;MAClC,IAAI,CAACM,yBAAyB,CAAC,CAAC;IACpC;IACA,OAAO,IAAI,CAACI,WAAW;EAC3B;EACA;AACJ;AACA;AACA;EACIyD,UAAUA,CAAA,EAAG;IACT,IAAI,CAACnE,0BAA0B,EAAEgD,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAChD,0BAA0B,GAAG,IAAI;EAC1C;AACJ;;AAEA;AACA;AACA;;AAEA,SAAS1D,OAAO,EAAE1B,UAAU,EAAES,YAAY,EAAEc,aAAa,EAAEjB,gBAAgB,EAAE4C,YAAY,EAAEX,eAAe,EAAEnB,aAAa,EAAEjB,gBAAgB,EAAE2C,YAAY,EAAEhB,eAAe,EAAE4B,YAAY,EAAEpH,cAAc,EAAEgH,MAAM,EAAEZ,SAAS,EAAElF,QAAQ,EAAEqH,kBAAkB,EAAER,cAAc,EAAEP,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}