{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/admin-api.service\";\nimport * as i2 from \"../services/admin-auth.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/progress-spinner\";\nfunction AdminDashboardComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"mat-spinner\", 4);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading dashboard data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AdminDashboardComponent_div_2_mat_card_14_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const stat_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(stat_r3.prefix);\n  }\n}\nfunction AdminDashboardComponent_div_2_mat_card_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 37)(1, \"mat-card-content\")(2, \"div\", 38)(3, \"div\", 39)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 40);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 41)(9, \"div\", 42);\n    i0.ɵɵtemplate(10, AdminDashboardComponent_div_2_mat_card_14_span_10_Template, 2, 1, \"span\", 43);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 44);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const stat_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"border-left-color\", stat_r3.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-color\", stat_r3.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r3.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getChangeClass(stat_r3.changeType));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", stat_r3.change, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", stat_r3.prefix);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", stat_r3.title === \"Revenue\" ? ctx_r1.formatCurrency(stat_r3.value) : ctx_r1.formatNumber(stat_r3.value), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r3.title);\n  }\n}\nfunction AdminDashboardComponent_div_2_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 47)(5, \"div\", 48);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 49);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const activity_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", activity_r4.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r4.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r4.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r4.time);\n  }\n}\nfunction AdminDashboardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"h1\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Here's what's happening with your business today.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 8)(9, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function AdminDashboardComponent_div_2_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.refreshData());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Refresh Data \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 10);\n    i0.ɵɵtemplate(14, AdminDashboardComponent_div_2_mat_card_14_Template, 14, 10, \"mat-card\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 13)(17, \"mat-card\", 14)(18, \"mat-card-header\")(19, \"mat-card-title\");\n    i0.ɵɵtext(20, \"User Growth\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"mat-card-subtitle\");\n    i0.ɵɵtext(22, \"Monthly new user registrations\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"mat-card-content\")(24, \"div\", 15);\n    i0.ɵɵelement(25, \"canvas\", 16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"mat-card\", 14)(27, \"mat-card-header\")(28, \"mat-card-title\");\n    i0.ɵɵtext(29, \"Order Trends\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"mat-card-subtitle\");\n    i0.ɵɵtext(31, \"Daily orders this week\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"mat-card-content\")(33, \"div\", 15);\n    i0.ɵɵelement(34, \"canvas\", 17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"mat-card\", 14)(36, \"mat-card-header\")(37, \"mat-card-title\");\n    i0.ɵɵtext(38, \"Revenue Distribution\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"mat-card-subtitle\");\n    i0.ɵɵtext(40, \"Revenue by category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"mat-card-content\")(42, \"div\", 15);\n    i0.ɵɵelement(43, \"canvas\", 18);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(44, \"div\", 19)(45, \"mat-card\", 20)(46, \"mat-card-header\")(47, \"mat-card-title\");\n    i0.ɵɵtext(48, \"Recent Activities\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"mat-card-subtitle\");\n    i0.ɵɵtext(50, \"Latest system activities\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"mat-card-content\")(52, \"div\", 21);\n    i0.ɵɵtemplate(53, AdminDashboardComponent_div_2_div_53_Template, 9, 5, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 23)(55, \"button\", 24);\n    i0.ɵɵtext(56, \"View All Activities\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(57, \"mat-card\", 25)(58, \"mat-card-header\")(59, \"mat-card-title\");\n    i0.ɵɵtext(60, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"mat-card-subtitle\");\n    i0.ɵɵtext(62, \"Common administrative tasks\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"mat-card-content\")(64, \"div\", 26)(65, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function AdminDashboardComponent_div_2_Template_button_click_65_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToSection(\"users\"));\n    });\n    i0.ɵɵelementStart(66, \"mat-icon\");\n    i0.ɵɵtext(67, \"person_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"span\");\n    i0.ɵɵtext(69, \"Add User\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function AdminDashboardComponent_div_2_Template_button_click_70_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToSection(\"products\"));\n    });\n    i0.ɵɵelementStart(71, \"mat-icon\");\n    i0.ɵɵtext(72, \"add_box\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"span\");\n    i0.ɵɵtext(74, \"Add Product\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(75, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function AdminDashboardComponent_div_2_Template_button_click_75_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToSection(\"orders\"));\n    });\n    i0.ɵɵelementStart(76, \"mat-icon\");\n    i0.ɵɵtext(77, \"list_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"span\");\n    i0.ɵɵtext(79, \"View Orders\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(80, \"button\", 28)(81, \"mat-icon\");\n    i0.ɵɵtext(82, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"span\");\n    i0.ɵɵtext(84, \"View Reports\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(85, \"button\", 28)(86, \"mat-icon\");\n    i0.ɵɵtext(87, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"span\");\n    i0.ɵɵtext(89, \"Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(90, \"button\", 28)(91, \"mat-icon\");\n    i0.ɵɵtext(92, \"help\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(93, \"span\");\n    i0.ɵɵtext(94, \"Help Center\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(95, \"mat-card\", 29)(96, \"mat-card-header\")(97, \"mat-card-title\");\n    i0.ɵɵtext(98, \"System Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(99, \"mat-card-subtitle\");\n    i0.ɵɵtext(100, \"Current system health and performance\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(101, \"mat-card-content\")(102, \"div\", 30)(103, \"div\", 31);\n    i0.ɵɵelement(104, \"div\", 32);\n    i0.ɵɵelementStart(105, \"div\", 33)(106, \"div\", 34);\n    i0.ɵɵtext(107, \"API Server\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(108, \"div\", 35);\n    i0.ɵɵtext(109, \"Online\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(110, \"div\", 31);\n    i0.ɵɵelement(111, \"div\", 32);\n    i0.ɵɵelementStart(112, \"div\", 33)(113, \"div\", 34);\n    i0.ɵɵtext(114, \"Database\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(115, \"div\", 35);\n    i0.ɵɵtext(116, \"Connected\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(117, \"div\", 31);\n    i0.ɵɵelement(118, \"div\", 36);\n    i0.ɵɵelementStart(119, \"div\", 33)(120, \"div\", 34);\n    i0.ɵɵtext(121, \"Storage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(122, \"div\", 35);\n    i0.ɵɵtext(123, \"85% Used\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(124, \"div\", 31);\n    i0.ɵɵelement(125, \"div\", 32);\n    i0.ɵɵelementStart(126, \"div\", 33)(127, \"div\", 34);\n    i0.ɵɵtext(128, \"Payment Gateway\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(129, \"div\", 35);\n    i0.ɵɵtext(130, \"Active\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Welcome back, \", (tmp_1_0 = i0.ɵɵpipeBind1(5, 9, ctx_r1.currentUser$)) == null ? null : tmp_1_0.fullName, \"!\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.quickStats);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"data\", ctx_r1.userGrowthChartData)(\"options\", ctx_r1.chartOptions);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"data\", ctx_r1.orderTrendsChartData)(\"options\", ctx_r1.chartOptions);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"data\", ctx_r1.revenueChartData)(\"options\", ctx_r1.chartOptions);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.recentActivities);\n  }\n}\n// Chart.js imports removed - using simple chart placeholders instead\nexport class AdminDashboardComponent {\n  constructor(apiService, authService) {\n    this.apiService = apiService;\n    this.authService = authService;\n    this.destroy$ = new Subject();\n    this.isLoading = true;\n    this.dashboardStats = null;\n    this.currentUser$ = this.authService.currentUser$;\n    // Chart data (simplified without Chart.js dependency)\n    this.userGrowthChartData = {\n      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],\n      data: [65, 78, 90, 81, 95, 105]\n    };\n    this.orderTrendsChartData = {\n      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n      data: [12, 19, 15, 25, 22, 30, 28]\n    };\n    this.revenueChartData = {\n      labels: ['Products', 'Services', 'Subscriptions'],\n      data: [65, 25, 10]\n    };\n    // Quick stats cards\n    this.quickStats = [{\n      title: 'Total Users',\n      value: 0,\n      change: '+12%',\n      changeType: 'positive',\n      icon: 'people',\n      color: '#2196f3'\n    }, {\n      title: 'Active Products',\n      value: 0,\n      change: '+8%',\n      changeType: 'positive',\n      icon: 'inventory',\n      color: '#4caf50'\n    }, {\n      title: 'Total Orders',\n      value: 0,\n      change: '+15%',\n      changeType: 'positive',\n      icon: 'shopping_cart',\n      color: '#ff9800'\n    }, {\n      title: 'Revenue',\n      value: 0,\n      change: '+23%',\n      changeType: 'positive',\n      icon: 'attach_money',\n      color: '#9c27b0',\n      prefix: '₹'\n    }];\n    // Recent activities\n    this.recentActivities = [{\n      type: 'order',\n      message: 'New order #DF12345 received',\n      time: '2 minutes ago',\n      icon: 'shopping_cart',\n      color: '#4caf50'\n    }, {\n      type: 'user',\n      message: 'New user registration: John Doe',\n      time: '5 minutes ago',\n      icon: 'person_add',\n      color: '#2196f3'\n    }, {\n      type: 'product',\n      message: 'Product \"Summer Dress\" approved',\n      time: '10 minutes ago',\n      icon: 'check_circle',\n      color: '#4caf50'\n    }, {\n      type: 'payment',\n      message: 'Payment of ₹2,500 received',\n      time: '15 minutes ago',\n      icon: 'payment',\n      color: '#9c27b0'\n    }];\n  }\n  ngOnInit() {\n    this.loadDashboardData();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  loadDashboardData() {\n    this.isLoading = true;\n    this.apiService.getDashboardStats().pipe(takeUntil(this.destroy$), finalize(() => this.isLoading = false)).subscribe({\n      next: stats => {\n        this.dashboardStats = stats;\n        this.updateQuickStats(stats);\n        this.updateCharts(stats);\n      },\n      error: error => {\n        console.error('Failed to load dashboard data:', error);\n        // Use mock data for demo\n        this.loadMockData();\n      }\n    });\n  }\n  updateQuickStats(stats) {\n    this.quickStats[0].value = stats.overview.users.total;\n    this.quickStats[1].value = stats.overview.products.active;\n    this.quickStats[2].value = stats.overview.orders.total;\n    this.quickStats[3].value = stats.revenue.totalRevenue;\n  }\n  updateCharts(stats) {\n    // Update chart data with actual stats\n    this.userGrowthChartData.data = [65, 78, 90, 81, 95, 105];\n    this.orderTrendsChartData.data = [12, 19, 15, 25, 22, 30, 28];\n    this.revenueChartData.data = [65, 25, 10];\n  }\n  loadMockData() {\n    // Mock data for demo purposes\n    this.quickStats[0].value = 1250;\n    this.quickStats[1].value = 89;\n    this.quickStats[2].value = 342;\n    this.quickStats[3].value = 125000;\n    this.updateCharts({});\n  }\n  formatCurrency(value) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(value);\n  }\n  formatNumber(value) {\n    return new Intl.NumberFormat('en-IN').format(value);\n  }\n  getChangeClass(changeType) {\n    return changeType === 'positive' ? 'positive-change' : 'negative-change';\n  }\n  refreshData() {\n    this.loadDashboardData();\n  }\n  navigateToSection(section) {\n    // Navigation logic based on user permissions\n    switch (section) {\n      case 'users':\n        if (this.authService.hasPermission('users', 'view')) {\n          // Navigate to users\n        }\n        break;\n      case 'products':\n        if (this.authService.hasPermission('products', 'view')) {\n          // Navigate to products\n        }\n        break;\n      case 'orders':\n        if (this.authService.hasPermission('orders', 'view')) {\n          // Navigate to orders\n        }\n        break;\n    }\n  }\n  static {\n    this.ɵfac = function AdminDashboardComponent_Factory(t) {\n      return new (t || AdminDashboardComponent)(i0.ɵɵdirectiveInject(i1.AdminApiService), i0.ɵɵdirectiveInject(i2.AdminAuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminDashboardComponent,\n      selectors: [[\"app-admin-dashboard\"]],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"dashboard-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"dashboard-content\", 4, \"ngIf\"], [1, \"loading-container\"], [\"diameter\", \"50\"], [1, \"dashboard-content\"], [1, \"welcome-section\"], [1, \"welcome-text\"], [1, \"welcome-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"stats-grid\"], [\"class\", \"stat-card\", 3, \"border-left-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"charts-section\"], [1, \"charts-grid\"], [1, \"chart-card\"], [1, \"chart-container\"], [\"baseChart\", \"\", \"type\", \"line\", 3, \"data\", \"options\"], [\"baseChart\", \"\", \"type\", \"bar\", 3, \"data\", \"options\"], [\"baseChart\", \"\", \"type\", \"doughnut\", 3, \"data\", \"options\"], [1, \"bottom-section\"], [1, \"activities-card\"], [1, \"activities-list\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"activities-footer\"], [\"mat-button\", \"\", \"color\", \"primary\"], [1, \"actions-card\"], [1, \"actions-grid\"], [\"mat-raised-button\", \"\", 1, \"action-button\", 3, \"click\"], [\"mat-raised-button\", \"\", 1, \"action-button\"], [1, \"status-card\"], [1, \"status-grid\"], [1, \"status-item\"], [1, \"status-indicator\", \"online\"], [1, \"status-info\"], [1, \"status-label\"], [1, \"status-value\"], [1, \"status-indicator\", \"warning\"], [1, \"stat-card\"], [1, \"stat-header\"], [1, \"stat-icon\"], [1, \"stat-change\", 3, \"ngClass\"], [1, \"stat-content\"], [1, \"stat-value\"], [4, \"ngIf\"], [1, \"stat-title\"], [1, \"activity-item\"], [1, \"activity-icon\"], [1, \"activity-content\"], [1, \"activity-message\"], [1, \"activity-time\"]],\n      template: function AdminDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, AdminDashboardComponent_div_1_Template, 4, 0, \"div\", 1)(2, AdminDashboardComponent_div_2_Template, 131, 11, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i4.MatIcon, i5.MatButton, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatProgressSpinner, i3.AsyncPipe],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  min-height: 100%;\\n  background: #f5f5f5;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 400px;\\n  gap: 1rem;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.dashboard-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2rem;\\n}\\n\\n.welcome-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 2rem;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.welcome-section[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  font-size: 2rem;\\n  font-weight: 600;\\n}\\n.welcome-section[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  opacity: 0.9;\\n  font-size: 1.1rem;\\n}\\n.welcome-section[_ngcontent-%COMP%]   .welcome-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n}\\n.welcome-section[_ngcontent-%COMP%]   .welcome-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 1.5rem;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%] {\\n  border-left: 4px solid;\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]   .stat-change[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 600;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 6px;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]   .stat-change.positive-change[_ngcontent-%COMP%] {\\n  background: rgba(76, 175, 80, 0.1);\\n  color: #4caf50;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-header[_ngcontent-%COMP%]   .stat-change.negative-change[_ngcontent-%COMP%] {\\n  background: rgba(244, 67, 54, 0.1);\\n  color: #f44336;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: #333;\\n  margin-bottom: 0.25rem;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .stat-content[_ngcontent-%COMP%]   .stat-title[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.95rem;\\n  font-weight: 500;\\n}\\n\\n.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: 1.5rem;\\n}\\n.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-top: 0.25rem;\\n}\\n.charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%] {\\n  height: 300px;\\n  position: relative;\\n}\\n\\n.bottom-section[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 1.5rem;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1rem 0;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-message[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 0.25rem;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-time[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.85rem;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .activities-card[_ngcontent-%COMP%]   .activities-footer[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 1rem;\\n  padding-top: 1rem;\\n  border-top: 1px solid #f0f0f0;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 1rem;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 1.5rem 1rem;\\n  height: auto;\\n  background: #f8f9fa;\\n  color: #333;\\n  border: 1px solid #e9ecef;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  transform: translateY(-1px);\\n}\\n.bottom-section[_ngcontent-%COMP%]   .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n.bottom-section[_ngcontent-%COMP%]   .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 500;\\n}\\n\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 1.5rem;\\n}\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1rem;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n}\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n}\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-indicator.online[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n}\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-indicator.warning[_ngcontent-%COMP%] {\\n  background: #ff9800;\\n}\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-indicator.error[_ngcontent-%COMP%] {\\n  background: #f44336;\\n}\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-info[_ngcontent-%COMP%]   .status-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 0.25rem;\\n}\\n.status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%]   .status-item[_ngcontent-%COMP%]   .status-info[_ngcontent-%COMP%]   .status-value[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n@media (max-width: 1200px) {\\n  .charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .bottom-section[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .welcome-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    text-align: center;\\n  }\\n  .welcome-section[_ngcontent-%COMP%]   .welcome-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .charts-section[_ngcontent-%COMP%]   .charts-grid[_ngcontent-%COMP%]   .chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%] {\\n    height: 250px;\\n  }\\n  .actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n@media (max-width: 480px) {\\n  .dashboard-content[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n  .welcome-section[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n  }\\n  .status-card[_ngcontent-%COMP%]   .status-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n  .mat-card {\\n  border-radius: 12px !important;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;\\n}\\n  .mat-card-header {\\n  padding-bottom: 0 !important;\\n}\\n  .mat-spinner circle {\\n  stroke: #667eea;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "finalize", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "stat_r3", "prefix", "ɵɵtemplate", "AdminDashboardComponent_div_2_mat_card_14_span_10_Template", "ɵɵstyleProp", "color", "icon", "ɵɵproperty", "ctx_r1", "getChangeClass", "changeType", "ɵɵtextInterpolate1", "change", "title", "formatCurrency", "value", "formatNumber", "activity_r4", "message", "time", "ɵɵlistener", "AdminDashboardComponent_div_2_Template_button_click_9_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "refreshData", "AdminDashboardComponent_div_2_mat_card_14_Template", "AdminDashboardComponent_div_2_div_53_Template", "AdminDashboardComponent_div_2_Template_button_click_65_listener", "navigateToSection", "AdminDashboardComponent_div_2_Template_button_click_70_listener", "AdminDashboardComponent_div_2_Template_button_click_75_listener", "tmp_1_0", "ɵɵpipeBind1", "currentUser$", "fullName", "quickStats", "userGrowthChartData", "chartOptions", "orderTrendsChartData", "revenueChartData", "recentActivities", "AdminDashboardComponent", "constructor", "apiService", "authService", "destroy$", "isLoading", "dashboardStats", "labels", "data", "type", "ngOnInit", "loadDashboardData", "ngOnDestroy", "next", "complete", "getDashboardStats", "pipe", "subscribe", "stats", "updateQuickStats", "updateCharts", "error", "console", "loadMockData", "overview", "users", "total", "products", "active", "orders", "revenue", "totalRevenue", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "section", "hasPermission", "ɵɵdirectiveInject", "i1", "AdminApiService", "i2", "AdminAuthService", "selectors", "decls", "vars", "consts", "template", "AdminDashboardComponent_Template", "rf", "ctx", "AdminDashboardComponent_div_1_Template", "AdminDashboardComponent_div_2_Template"], "sources": ["E:\\DFashion\\frontend\\src\\app\\admin\\dashboard\\admin-dashboard.component.ts", "E:\\DFashion\\frontend\\src\\app\\admin\\dashboard\\admin-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { takeUntil, finalize } from 'rxjs/operators';\nimport { AdminApiService, DashboardStats } from '../services/admin-api.service';\nimport { AdminAuthService } from '../services/admin-auth.service';\n// Chart.js imports removed - using simple chart placeholders instead\n\n@Component({\n  selector: 'app-admin-dashboard',\n  templateUrl: './admin-dashboard.component.html',\n  styleUrls: ['./admin-dashboard.component.scss']\n})\nexport class AdminDashboardComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  \n  isLoading = true;\n  dashboardStats: DashboardStats | null = null;\n  currentUser$ = this.authService.currentUser$;\n\n  // Chart data (simplified without Chart.js dependency)\n  userGrowthChartData = {\n    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],\n    data: [65, 78, 90, 81, 95, 105]\n  };\n\n  orderTrendsChartData = {\n    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\n    data: [12, 19, 15, 25, 22, 30, 28]\n  };\n\n  revenueChartData = {\n    labels: ['Products', 'Services', 'Subscriptions'],\n    data: [65, 25, 10]\n  };\n\n  // Quick stats cards\n  quickStats = [\n    {\n      title: 'Total Users',\n      value: 0,\n      change: '+12%',\n      changeType: 'positive',\n      icon: 'people',\n      color: '#2196f3'\n    },\n    {\n      title: 'Active Products',\n      value: 0,\n      change: '+8%',\n      changeType: 'positive',\n      icon: 'inventory',\n      color: '#4caf50'\n    },\n    {\n      title: 'Total Orders',\n      value: 0,\n      change: '+15%',\n      changeType: 'positive',\n      icon: 'shopping_cart',\n      color: '#ff9800'\n    },\n    {\n      title: 'Revenue',\n      value: 0,\n      change: '+23%',\n      changeType: 'positive',\n      icon: 'attach_money',\n      color: '#9c27b0',\n      prefix: '₹'\n    }\n  ];\n\n  // Recent activities\n  recentActivities = [\n    {\n      type: 'order',\n      message: 'New order #DF12345 received',\n      time: '2 minutes ago',\n      icon: 'shopping_cart',\n      color: '#4caf50'\n    },\n    {\n      type: 'user',\n      message: 'New user registration: John Doe',\n      time: '5 minutes ago',\n      icon: 'person_add',\n      color: '#2196f3'\n    },\n    {\n      type: 'product',\n      message: 'Product \"Summer Dress\" approved',\n      time: '10 minutes ago',\n      icon: 'check_circle',\n      color: '#4caf50'\n    },\n    {\n      type: 'payment',\n      message: 'Payment of ₹2,500 received',\n      time: '15 minutes ago',\n      icon: 'payment',\n      color: '#9c27b0'\n    }\n  ];\n\n  constructor(\n    private apiService: AdminApiService,\n    private authService: AdminAuthService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadDashboardData();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadDashboardData(): void {\n    this.isLoading = true;\n    \n    this.apiService.getDashboardStats()\n      .pipe(\n        takeUntil(this.destroy$),\n        finalize(() => this.isLoading = false)\n      )\n      .subscribe({\n        next: (stats) => {\n          this.dashboardStats = stats;\n          this.updateQuickStats(stats);\n          this.updateCharts(stats);\n        },\n        error: (error) => {\n          console.error('Failed to load dashboard data:', error);\n          // Use mock data for demo\n          this.loadMockData();\n        }\n      });\n  }\n\n  private updateQuickStats(stats: DashboardStats): void {\n    this.quickStats[0].value = stats.overview.users.total;\n    this.quickStats[1].value = stats.overview.products.active;\n    this.quickStats[2].value = stats.overview.orders.total;\n    this.quickStats[3].value = stats.revenue.totalRevenue;\n  }\n\n  private updateCharts(stats: DashboardStats): void {\n    // Update chart data with actual stats\n    this.userGrowthChartData.data = [65, 78, 90, 81, 95, 105];\n    this.orderTrendsChartData.data = [12, 19, 15, 25, 22, 30, 28];\n    this.revenueChartData.data = [65, 25, 10];\n  }\n\n  private loadMockData(): void {\n    // Mock data for demo purposes\n    this.quickStats[0].value = 1250;\n    this.quickStats[1].value = 89;\n    this.quickStats[2].value = 342;\n    this.quickStats[3].value = 125000;\n\n    this.updateCharts({} as DashboardStats);\n  }\n\n  formatCurrency(value: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(value);\n  }\n\n  formatNumber(value: number): string {\n    return new Intl.NumberFormat('en-IN').format(value);\n  }\n\n  getChangeClass(changeType: string): string {\n    return changeType === 'positive' ? 'positive-change' : 'negative-change';\n  }\n\n  refreshData(): void {\n    this.loadDashboardData();\n  }\n\n  navigateToSection(section: string): void {\n    // Navigation logic based on user permissions\n    switch (section) {\n      case 'users':\n        if (this.authService.hasPermission('users', 'view')) {\n          // Navigate to users\n        }\n        break;\n      case 'products':\n        if (this.authService.hasPermission('products', 'view')) {\n          // Navigate to products\n        }\n        break;\n      case 'orders':\n        if (this.authService.hasPermission('orders', 'view')) {\n          // Navigate to orders\n        }\n        break;\n    }\n  }\n}\n", "<div class=\"dashboard-container\">\n  <!-- Loading Spinner -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <mat-spinner diameter=\"50\"></mat-spinner>\n    <p>Loading dashboard data...</p>\n  </div>\n\n  <!-- Dashboard Content -->\n  <div *ngIf=\"!isLoading\" class=\"dashboard-content\">\n    <!-- Welcome Section -->\n    <div class=\"welcome-section\">\n      <div class=\"welcome-text\">\n        <h1>Welcome back, {{ (currentUser$ | async)?.fullName }}!</h1>\n        <p>Here's what's happening with your business today.</p>\n      </div>\n      <div class=\"welcome-actions\">\n        <button mat-raised-button color=\"primary\" (click)=\"refreshData()\">\n          <mat-icon>refresh</mat-icon>\n          Refresh Data\n        </button>\n      </div>\n    </div>\n\n    <!-- Quick Stats Cards -->\n    <div class=\"stats-grid\">\n      <mat-card *ngFor=\"let stat of quickStats\" class=\"stat-card\" [style.border-left-color]=\"stat.color\">\n        <mat-card-content>\n          <div class=\"stat-header\">\n            <div class=\"stat-icon\" [style.background-color]=\"stat.color\">\n              <mat-icon>{{ stat.icon }}</mat-icon>\n            </div>\n            <div class=\"stat-change\" [ngClass]=\"getChangeClass(stat.changeType)\">\n              {{ stat.change }}\n            </div>\n          </div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">\n              <span *ngIf=\"stat.prefix\">{{ stat.prefix }}</span>\n              {{ stat.title === 'Revenue' ? formatCurrency(stat.value) : formatNumber(stat.value) }}\n            </div>\n            <div class=\"stat-title\">{{ stat.title }}</div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Charts Section -->\n    <div class=\"charts-section\">\n      <div class=\"charts-grid\">\n        <!-- User Growth Chart -->\n        <mat-card class=\"chart-card\">\n          <mat-card-header>\n            <mat-card-title>User Growth</mat-card-title>\n            <mat-card-subtitle>Monthly new user registrations</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"chart-container\">\n              <canvas baseChart\n                      [data]=\"userGrowthChartData\"\n                      [options]=\"chartOptions\"\n                      type=\"line\">\n              </canvas>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Order Trends Chart -->\n        <mat-card class=\"chart-card\">\n          <mat-card-header>\n            <mat-card-title>Order Trends</mat-card-title>\n            <mat-card-subtitle>Daily orders this week</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"chart-container\">\n              <canvas baseChart\n                      [data]=\"orderTrendsChartData\"\n                      [options]=\"chartOptions\"\n                      type=\"bar\">\n              </canvas>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Revenue Distribution Chart -->\n        <mat-card class=\"chart-card\">\n          <mat-card-header>\n            <mat-card-title>Revenue Distribution</mat-card-title>\n            <mat-card-subtitle>Revenue by category</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"chart-container\">\n              <canvas baseChart\n                      [data]=\"revenueChartData\"\n                      [options]=\"chartOptions\"\n                      type=\"doughnut\">\n              </canvas>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n\n    <!-- Bottom Section -->\n    <div class=\"bottom-section\">\n      <!-- Recent Activities -->\n      <mat-card class=\"activities-card\">\n        <mat-card-header>\n          <mat-card-title>Recent Activities</mat-card-title>\n          <mat-card-subtitle>Latest system activities</mat-card-subtitle>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"activities-list\">\n            <div *ngFor=\"let activity of recentActivities\" class=\"activity-item\">\n              <div class=\"activity-icon\" [style.background-color]=\"activity.color\">\n                <mat-icon>{{ activity.icon }}</mat-icon>\n              </div>\n              <div class=\"activity-content\">\n                <div class=\"activity-message\">{{ activity.message }}</div>\n                <div class=\"activity-time\">{{ activity.time }}</div>\n              </div>\n            </div>\n          </div>\n          <div class=\"activities-footer\">\n            <button mat-button color=\"primary\">View All Activities</button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Quick Actions -->\n      <mat-card class=\"actions-card\">\n        <mat-card-header>\n          <mat-card-title>Quick Actions</mat-card-title>\n          <mat-card-subtitle>Common administrative tasks</mat-card-subtitle>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"actions-grid\">\n            <button mat-raised-button class=\"action-button\" (click)=\"navigateToSection('users')\">\n              <mat-icon>person_add</mat-icon>\n              <span>Add User</span>\n            </button>\n            <button mat-raised-button class=\"action-button\" (click)=\"navigateToSection('products')\">\n              <mat-icon>add_box</mat-icon>\n              <span>Add Product</span>\n            </button>\n            <button mat-raised-button class=\"action-button\" (click)=\"navigateToSection('orders')\">\n              <mat-icon>list_alt</mat-icon>\n              <span>View Orders</span>\n            </button>\n            <button mat-raised-button class=\"action-button\">\n              <mat-icon>analytics</mat-icon>\n              <span>View Reports</span>\n            </button>\n            <button mat-raised-button class=\"action-button\">\n              <mat-icon>settings</mat-icon>\n              <span>Settings</span>\n            </button>\n            <button mat-raised-button class=\"action-button\">\n              <mat-icon>help</mat-icon>\n              <span>Help Center</span>\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- System Status -->\n    <mat-card class=\"status-card\">\n      <mat-card-header>\n        <mat-card-title>System Status</mat-card-title>\n        <mat-card-subtitle>Current system health and performance</mat-card-subtitle>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"status-grid\">\n          <div class=\"status-item\">\n            <div class=\"status-indicator online\"></div>\n            <div class=\"status-info\">\n              <div class=\"status-label\">API Server</div>\n              <div class=\"status-value\">Online</div>\n            </div>\n          </div>\n          <div class=\"status-item\">\n            <div class=\"status-indicator online\"></div>\n            <div class=\"status-info\">\n              <div class=\"status-label\">Database</div>\n              <div class=\"status-value\">Connected</div>\n            </div>\n          </div>\n          <div class=\"status-item\">\n            <div class=\"status-indicator warning\"></div>\n            <div class=\"status-info\">\n              <div class=\"status-label\">Storage</div>\n              <div class=\"status-value\">85% Used</div>\n            </div>\n          </div>\n          <div class=\"status-item\">\n            <div class=\"status-indicator online\"></div>\n            <div class=\"status-info\">\n              <div class=\"status-label\">Payment Gateway</div>\n              <div class=\"status-value\">Active</div>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;;;;;;;;;;;ICAlDC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,qBAAyC;IACzCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAC9BH,EAD8B,CAAAI,YAAA,EAAI,EAC5B;;;;;IAgCMJ,EAAA,CAAAC,cAAA,WAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAxBJ,EAAA,CAAAK,SAAA,EAAiB;IAAjBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAC,MAAA,CAAiB;;;;;IAR3CR,EAJR,CAAAC,cAAA,mBAAmG,uBAC/E,cACS,cACsC,eACjD;IAAAD,EAAA,CAAAG,MAAA,GAAe;IAC3BH,EAD2B,CAAAI,YAAA,EAAW,EAChC;IACNJ,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IAEJJ,EADF,CAAAC,cAAA,cAA0B,cACA;IACtBD,EAAA,CAAAS,UAAA,KAAAC,0DAAA,mBAA0B;IAC1BV,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAG,MAAA,IAAgB;IAG9CH,EAH8C,CAAAI,YAAA,EAAM,EAC1C,EACW,EACV;;;;;IAlBiDJ,EAAA,CAAAW,WAAA,sBAAAJ,OAAA,CAAAK,KAAA,CAAsC;IAGrEZ,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAW,WAAA,qBAAAJ,OAAA,CAAAK,KAAA,CAAqC;IAChDZ,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAM,IAAA,CAAe;IAEFb,EAAA,CAAAK,SAAA,EAA2C;IAA3CL,EAAA,CAAAc,UAAA,YAAAC,MAAA,CAAAC,cAAA,CAAAT,OAAA,CAAAU,UAAA,EAA2C;IAClEjB,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAkB,kBAAA,MAAAX,OAAA,CAAAY,MAAA,MACF;IAISnB,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAc,UAAA,SAAAP,OAAA,CAAAC,MAAA,CAAiB;IACxBR,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAkB,kBAAA,MAAAX,OAAA,CAAAa,KAAA,iBAAAL,MAAA,CAAAM,cAAA,CAAAd,OAAA,CAAAe,KAAA,IAAAP,MAAA,CAAAQ,YAAA,CAAAhB,OAAA,CAAAe,KAAA,OACF;IACwBtB,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAa,KAAA,CAAgB;;;;;IA0EpCpB,EAFJ,CAAAC,cAAA,cAAqE,cACE,eACzD;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAC/BH,EAD+B,CAAAI,YAAA,EAAW,EACpC;IAEJJ,EADF,CAAAC,cAAA,cAA8B,cACE;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC1DJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAElDH,EAFkD,CAAAI,YAAA,EAAM,EAChD,EACF;;;;IAPuBJ,EAAA,CAAAK,SAAA,EAAyC;IAAzCL,EAAA,CAAAW,WAAA,qBAAAa,WAAA,CAAAZ,KAAA,CAAyC;IACxDZ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAkB,WAAA,CAAAX,IAAA,CAAmB;IAGCb,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,iBAAA,CAAAkB,WAAA,CAAAC,OAAA,CAAsB;IACzBzB,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAkB,WAAA,CAAAE,IAAA,CAAmB;;;;;;IA1GtD1B,EAJN,CAAAC,cAAA,aAAkD,aAEnB,aACD,SACpB;IAAAD,EAAA,CAAAG,MAAA,GAAqD;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9DJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,wDAAiD;IACtDH,EADsD,CAAAI,YAAA,EAAI,EACpD;IAEJJ,EADF,CAAAC,cAAA,aAA6B,gBACuC;IAAxBD,EAAA,CAAA2B,UAAA,mBAAAC,+DAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAC,GAAA;MAAA,MAAAf,MAAA,GAAAf,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAASjB,MAAA,CAAAkB,WAAA,EAAa;IAAA,EAAC;IAC/DjC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,sBACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;IAGNJ,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAS,UAAA,KAAAyB,kDAAA,yBAAmG;IAmBrGlC,EAAA,CAAAI,YAAA,EAAM;IAQEJ,EALR,CAAAC,cAAA,eAA4B,eACD,oBAEM,uBACV,sBACC;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAC5CJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,sCAA8B;IACnDH,EADmD,CAAAI,YAAA,EAAoB,EACrD;IAEhBJ,EADF,CAAAC,cAAA,wBAAkB,eACa;IAC3BD,EAAA,CAAAE,SAAA,kBAIS;IAGfF,EAFI,CAAAI,YAAA,EAAM,EACW,EACV;IAKPJ,EAFJ,CAAAC,cAAA,oBAA6B,uBACV,sBACC;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAC7CJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,8BAAsB;IAC3CH,EAD2C,CAAAI,YAAA,EAAoB,EAC7C;IAEhBJ,EADF,CAAAC,cAAA,wBAAkB,eACa;IAC3BD,EAAA,CAAAE,SAAA,kBAIS;IAGfF,EAFI,CAAAI,YAAA,EAAM,EACW,EACV;IAKPJ,EAFJ,CAAAC,cAAA,oBAA6B,uBACV,sBACC;IAAAD,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IACrDJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,2BAAmB;IACxCH,EADwC,CAAAI,YAAA,EAAoB,EAC1C;IAEhBJ,EADF,CAAAC,cAAA,wBAAkB,eACa;IAC3BD,EAAA,CAAAE,SAAA,kBAIS;IAKnBF,EAJQ,CAAAI,YAAA,EAAM,EACW,EACV,EACP,EACF;IAOAJ,EAJN,CAAAC,cAAA,eAA4B,oBAEQ,uBACf,sBACC;IAAAD,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAClDJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,gCAAwB;IAC7CH,EAD6C,CAAAI,YAAA,EAAoB,EAC/C;IAEhBJ,EADF,CAAAC,cAAA,wBAAkB,eACa;IAC3BD,EAAA,CAAAS,UAAA,KAAA0B,6CAAA,kBAAqE;IASvEnC,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,eAA+B,kBACM;IAAAD,EAAA,CAAAG,MAAA,2BAAmB;IAG5DH,EAH4D,CAAAI,YAAA,EAAS,EAC3D,EACW,EACV;IAKPJ,EAFJ,CAAAC,cAAA,oBAA+B,uBACZ,sBACC;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAC9CJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,mCAA2B;IAChDH,EADgD,CAAAI,YAAA,EAAoB,EAClD;IAGdJ,EAFJ,CAAAC,cAAA,wBAAkB,eACU,kBAC6D;IAArCD,EAAA,CAAA2B,UAAA,mBAAAS,gEAAA;MAAApC,EAAA,CAAA6B,aAAA,CAAAC,GAAA;MAAA,MAAAf,MAAA,GAAAf,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAASjB,MAAA,CAAAsB,iBAAA,CAAkB,OAAO,CAAC;IAAA,EAAC;IAClFrC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAChBH,EADgB,CAAAI,YAAA,EAAO,EACd;IACTJ,EAAA,CAAAC,cAAA,kBAAwF;IAAxCD,EAAA,CAAA2B,UAAA,mBAAAW,gEAAA;MAAAtC,EAAA,CAAA6B,aAAA,CAAAC,GAAA;MAAA,MAAAf,MAAA,GAAAf,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAASjB,MAAA,CAAAsB,iBAAA,CAAkB,UAAU,CAAC;IAAA,EAAC;IACrFrC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IACnBH,EADmB,CAAAI,YAAA,EAAO,EACjB;IACTJ,EAAA,CAAAC,cAAA,kBAAsF;IAAtCD,EAAA,CAAA2B,UAAA,mBAAAY,gEAAA;MAAAvC,EAAA,CAAA6B,aAAA,CAAAC,GAAA;MAAA,MAAAf,MAAA,GAAAf,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAASjB,MAAA,CAAAsB,iBAAA,CAAkB,QAAQ,CAAC;IAAA,EAAC;IACnFrC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IACnBH,EADmB,CAAAI,YAAA,EAAO,EACjB;IAEPJ,EADF,CAAAC,cAAA,kBAAgD,gBACpC;IAAAD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC9BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IACpBH,EADoB,CAAAI,YAAA,EAAO,EAClB;IAEPJ,EADF,CAAAC,cAAA,kBAAgD,gBACpC;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC7BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAChBH,EADgB,CAAAI,YAAA,EAAO,EACd;IAEPJ,EADF,CAAAC,cAAA,kBAAgD,gBACpC;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACzBJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAK3BH,EAL2B,CAAAI,YAAA,EAAO,EACjB,EACL,EACW,EACV,EACP;IAKFJ,EAFJ,CAAAC,cAAA,oBAA8B,uBACX,sBACC;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAiB;IAC9CJ,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAG,MAAA,8CAAqC;IAC1DH,EAD0D,CAAAI,YAAA,EAAoB,EAC5D;IAGdJ,EAFJ,CAAAC,cAAA,yBAAkB,gBACS,gBACE;IACvBD,EAAA,CAAAE,SAAA,gBAA2C;IAEzCF,EADF,CAAAC,cAAA,gBAAyB,gBACG;IAAAD,EAAA,CAAAG,MAAA,mBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC1CJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,eAAM;IAEpCH,EAFoC,CAAAI,YAAA,EAAM,EAClC,EACF;IACNJ,EAAA,CAAAC,cAAA,gBAAyB;IACvBD,EAAA,CAAAE,SAAA,gBAA2C;IAEzCF,EADF,CAAAC,cAAA,gBAAyB,gBACG;IAAAD,EAAA,CAAAG,MAAA,iBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACxCJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,kBAAS;IAEvCH,EAFuC,CAAAI,YAAA,EAAM,EACrC,EACF;IACNJ,EAAA,CAAAC,cAAA,gBAAyB;IACvBD,EAAA,CAAAE,SAAA,gBAA4C;IAE1CF,EADF,CAAAC,cAAA,gBAAyB,gBACG;IAAAD,EAAA,CAAAG,MAAA,gBAAO;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACvCJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,iBAAQ;IAEtCH,EAFsC,CAAAI,YAAA,EAAM,EACpC,EACF;IACNJ,EAAA,CAAAC,cAAA,gBAAyB;IACvBD,EAAA,CAAAE,SAAA,gBAA2C;IAEzCF,EADF,CAAAC,cAAA,gBAAyB,gBACG;IAAAD,EAAA,CAAAG,MAAA,wBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC/CJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,eAAM;IAM5CH,EAN4C,CAAAI,YAAA,EAAM,EAClC,EACF,EACF,EACW,EACV,EACP;;;;;IAhMIJ,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAAkB,kBAAA,oBAAAsB,OAAA,GAAAxC,EAAA,CAAAyC,WAAA,OAAA1B,MAAA,CAAA2B,YAAA,oBAAAF,OAAA,CAAAG,QAAA,MAAqD;IAahC3C,EAAA,CAAAK,SAAA,IAAa;IAAbL,EAAA,CAAAc,UAAA,YAAAC,MAAA,CAAA6B,UAAA,CAAa;IAiCxB5C,EAAA,CAAAK,SAAA,IAA4B;IAC5BL,EADA,CAAAc,UAAA,SAAAC,MAAA,CAAA8B,mBAAA,CAA4B,YAAA9B,MAAA,CAAA+B,YAAA,CACJ;IAgBxB9C,EAAA,CAAAK,SAAA,GAA6B;IAC7BL,EADA,CAAAc,UAAA,SAAAC,MAAA,CAAAgC,oBAAA,CAA6B,YAAAhC,MAAA,CAAA+B,YAAA,CACL;IAgBxB9C,EAAA,CAAAK,SAAA,GAAyB;IACzBL,EADA,CAAAc,UAAA,SAAAC,MAAA,CAAAiC,gBAAA,CAAyB,YAAAjC,MAAA,CAAA+B,YAAA,CACD;IAmBR9C,EAAA,CAAAK,SAAA,IAAmB;IAAnBL,EAAA,CAAAc,UAAA,YAAAC,MAAA,CAAAkC,gBAAA,CAAmB;;;AD3GzD;AAOA,OAAM,MAAOC,uBAAuB;EA4FlCC,YACUC,UAA2B,EAC3BC,WAA6B;IAD7B,KAAAD,UAAU,GAAVA,UAAU;IACV,KAAAC,WAAW,GAAXA,WAAW;IA7Fb,KAAAC,QAAQ,GAAG,IAAIzD,OAAO,EAAQ;IAEtC,KAAA0D,SAAS,GAAG,IAAI;IAChB,KAAAC,cAAc,GAA0B,IAAI;IAC5C,KAAAd,YAAY,GAAG,IAAI,CAACW,WAAW,CAACX,YAAY;IAE5C;IACA,KAAAG,mBAAmB,GAAG;MACpBY,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MAClDC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG;KAC/B;IAED,KAAAX,oBAAoB,GAAG;MACrBU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MACzDC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;KAClC;IAED,KAAAV,gBAAgB,GAAG;MACjBS,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,eAAe,CAAC;MACjDC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;KAClB;IAED;IACA,KAAAd,UAAU,GAAG,CACX;MACExB,KAAK,EAAE,aAAa;MACpBE,KAAK,EAAE,CAAC;MACRH,MAAM,EAAE,MAAM;MACdF,UAAU,EAAE,UAAU;MACtBJ,IAAI,EAAE,QAAQ;MACdD,KAAK,EAAE;KACR,EACD;MACEQ,KAAK,EAAE,iBAAiB;MACxBE,KAAK,EAAE,CAAC;MACRH,MAAM,EAAE,KAAK;MACbF,UAAU,EAAE,UAAU;MACtBJ,IAAI,EAAE,WAAW;MACjBD,KAAK,EAAE;KACR,EACD;MACEQ,KAAK,EAAE,cAAc;MACrBE,KAAK,EAAE,CAAC;MACRH,MAAM,EAAE,MAAM;MACdF,UAAU,EAAE,UAAU;MACtBJ,IAAI,EAAE,eAAe;MACrBD,KAAK,EAAE;KACR,EACD;MACEQ,KAAK,EAAE,SAAS;MAChBE,KAAK,EAAE,CAAC;MACRH,MAAM,EAAE,MAAM;MACdF,UAAU,EAAE,UAAU;MACtBJ,IAAI,EAAE,cAAc;MACpBD,KAAK,EAAE,SAAS;MAChBJ,MAAM,EAAE;KACT,CACF;IAED;IACA,KAAAyC,gBAAgB,GAAG,CACjB;MACEU,IAAI,EAAE,OAAO;MACblC,OAAO,EAAE,6BAA6B;MACtCC,IAAI,EAAE,eAAe;MACrBb,IAAI,EAAE,eAAe;MACrBD,KAAK,EAAE;KACR,EACD;MACE+C,IAAI,EAAE,MAAM;MACZlC,OAAO,EAAE,iCAAiC;MAC1CC,IAAI,EAAE,eAAe;MACrBb,IAAI,EAAE,YAAY;MAClBD,KAAK,EAAE;KACR,EACD;MACE+C,IAAI,EAAE,SAAS;MACflC,OAAO,EAAE,iCAAiC;MAC1CC,IAAI,EAAE,gBAAgB;MACtBb,IAAI,EAAE,cAAc;MACpBD,KAAK,EAAE;KACR,EACD;MACE+C,IAAI,EAAE,SAAS;MACflC,OAAO,EAAE,4BAA4B;MACrCC,IAAI,EAAE,gBAAgB;MACtBb,IAAI,EAAE,SAAS;MACfD,KAAK,EAAE;KACR,CACF;EAKE;EAEHgD,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACR,QAAQ,CAACS,IAAI,EAAE;IACpB,IAAI,CAACT,QAAQ,CAACU,QAAQ,EAAE;EAC1B;EAEAH,iBAAiBA,CAAA;IACf,IAAI,CAACN,SAAS,GAAG,IAAI;IAErB,IAAI,CAACH,UAAU,CAACa,iBAAiB,EAAE,CAChCC,IAAI,CACHpE,SAAS,CAAC,IAAI,CAACwD,QAAQ,CAAC,EACxBvD,QAAQ,CAAC,MAAM,IAAI,CAACwD,SAAS,GAAG,KAAK,CAAC,CACvC,CACAY,SAAS,CAAC;MACTJ,IAAI,EAAGK,KAAK,IAAI;QACd,IAAI,CAACZ,cAAc,GAAGY,KAAK;QAC3B,IAAI,CAACC,gBAAgB,CAACD,KAAK,CAAC;QAC5B,IAAI,CAACE,YAAY,CAACF,KAAK,CAAC;MAC1B,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD;QACA,IAAI,CAACE,YAAY,EAAE;MACrB;KACD,CAAC;EACN;EAEQJ,gBAAgBA,CAACD,KAAqB;IAC5C,IAAI,CAACxB,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAG8C,KAAK,CAACM,QAAQ,CAACC,KAAK,CAACC,KAAK;IACrD,IAAI,CAAChC,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAG8C,KAAK,CAACM,QAAQ,CAACG,QAAQ,CAACC,MAAM;IACzD,IAAI,CAAClC,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAG8C,KAAK,CAACM,QAAQ,CAACK,MAAM,CAACH,KAAK;IACtD,IAAI,CAAChC,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAG8C,KAAK,CAACY,OAAO,CAACC,YAAY;EACvD;EAEQX,YAAYA,CAACF,KAAqB;IACxC;IACA,IAAI,CAACvB,mBAAmB,CAACa,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IACzD,IAAI,CAACX,oBAAoB,CAACW,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC7D,IAAI,CAACV,gBAAgB,CAACU,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC3C;EAEQe,YAAYA,CAAA;IAClB;IACA,IAAI,CAAC7B,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAG,IAAI;IAC/B,IAAI,CAACsB,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAG,EAAE;IAC7B,IAAI,CAACsB,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAG,GAAG;IAC9B,IAAI,CAACsB,UAAU,CAAC,CAAC,CAAC,CAACtB,KAAK,GAAG,MAAM;IAEjC,IAAI,CAACgD,YAAY,CAAC,EAAoB,CAAC;EACzC;EAEAjD,cAAcA,CAACC,KAAa;IAC1B,OAAO,IAAI4D,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACjE,KAAK,CAAC;EAClB;EAEAC,YAAYA,CAACD,KAAa;IACxB,OAAO,IAAI4D,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACI,MAAM,CAACjE,KAAK,CAAC;EACrD;EAEAN,cAAcA,CAACC,UAAkB;IAC/B,OAAOA,UAAU,KAAK,UAAU,GAAG,iBAAiB,GAAG,iBAAiB;EAC1E;EAEAgB,WAAWA,CAAA;IACT,IAAI,CAAC4B,iBAAiB,EAAE;EAC1B;EAEAxB,iBAAiBA,CAACmD,OAAe;IAC/B;IACA,QAAQA,OAAO;MACb,KAAK,OAAO;QACV,IAAI,IAAI,CAACnC,WAAW,CAACoC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE;UACnD;QAAA;QAEF;MACF,KAAK,UAAU;QACb,IAAI,IAAI,CAACpC,WAAW,CAACoC,aAAa,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE;UACtD;QAAA;QAEF;MACF,KAAK,QAAQ;QACX,IAAI,IAAI,CAACpC,WAAW,CAACoC,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE;UACpD;QAAA;QAEF;;EAEN;;;uBA/LWvC,uBAAuB,EAAAlD,EAAA,CAAA0F,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAA5F,EAAA,CAAA0F,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAvB5C,uBAAuB;MAAA6C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZpCrG,EAAA,CAAAC,cAAA,aAAiC;UAQ/BD,EANA,CAAAS,UAAA,IAAA8F,sCAAA,iBAAiD,IAAAC,sCAAA,oBAMC;UAqMpDxG,EAAA,CAAAI,YAAA,EAAM;;;UA3MEJ,EAAA,CAAAK,SAAA,EAAe;UAAfL,EAAA,CAAAc,UAAA,SAAAwF,GAAA,CAAA/C,SAAA,CAAe;UAMfvD,EAAA,CAAAK,SAAA,EAAgB;UAAhBL,EAAA,CAAAc,UAAA,UAAAwF,GAAA,CAAA/C,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}