.category-selection-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.category-selection-header {
  text-align: center;
  margin-bottom: 3rem;
  
  h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    
    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }
  
  p {
    font-size: 1.2rem;
    color: #7f8c8d;
    max-width: 600px;
    margin: 0 auto;
    
    @media (max-width: 768px) {
      font-size: 1rem;
    }
  }
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.category-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  
  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }
  
  &:focus {
    outline: 3px solid #3498db;
    outline-offset: 2px;
  }
  
  &:active {
    transform: translateY(-5px);
  }
}

.category-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0) 0%,
      rgba(0, 0, 0, 0.3) 70%,
      rgba(0, 0, 0, 0.7) 100%
    );
    display: flex;
    align-items: flex-end;
    padding: 1rem;
  }
  
  .product-count {
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
  }
}

.category-card:hover .category-image img {
  transform: scale(1.1);
}

.category-info {
  padding: 1.5rem;
  
  h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #7f8c8d;
    margin-bottom: 1rem;
    line-height: 1.5;
  }
}

.category-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  
  .target-gender {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
  }
  
  .avg-price {
    color: #27ae60;
    font-weight: 600;
    font-size: 0.9rem;
  }
}

.category-action {
  padding: 0 1.5rem 1.5rem;
  
  .select-btn {
    width: 100%;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    
    &:hover {
      background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
      transform: translateY(-2px);
    }
    
    &:active {
      transform: translateY(0);
    }
    
    i {
      transition: transform 0.3s ease;
    }
  }
}

.category-card:hover .select-btn i {
  transform: translateX(5px);
}

.alternative-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
}

.alt-action-btn {
  background: white;
  border: 2px solid #e0e0e0;
  color: #2c3e50;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  
  &:hover {
    border-color: #3498db;
    color: #3498db;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.2);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  i {
    font-size: 0.9rem;
  }
  
  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
    max-width: 300px;
  }
}

.search-all-btn {
  &:hover {
    border-color: #27ae60;
    color: #27ae60;
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.2);
  }
}

.refine-search-btn {
  &:hover {
    border-color: #f39c12;
    color: #f39c12;
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.2);
  }
}

// Loading state
.category-card.loading {
  .category-image {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .category-card,
  .category-image img,
  .select-btn,
  .alt-action-btn {
    transition: none;
  }
  
  .category-card:hover {
    transform: none;
  }
  
  .category-card:hover .category-image img {
    transform: none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .category-card {
    border: 2px solid #000;
  }
  
  .category-info h3 {
    color: #000;
  }
  
  .select-btn {
    background: #000;
    color: #fff;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .category-selection-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
  
  .category-selection-header h2 {
    color: #ecf0f1;
  }
  
  .category-selection-header p {
    color: #bdc3c7;
  }
  
  .category-card {
    background: #34495e;
  }
  
  .category-info h3 {
    color: #ecf0f1;
  }
  
  .category-info p {
    color: #bdc3c7;
  }
  
  .alt-action-btn {
    background: #34495e;
    border-color: #7f8c8d;
    color: #ecf0f1;
  }
}
