{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction NoDataComponent_div_8_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function NoDataComponent_div_8_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPrimaryAction());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.primaryAction, \" \");\n  }\n}\nfunction NoDataComponent_div_8_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function NoDataComponent_div_8_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSecondaryAction());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.secondaryAction, \" \");\n  }\n}\nfunction NoDataComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtemplate(1, NoDataComponent_div_8_button_1_Template, 2, 1, \"button\", 8)(2, NoDataComponent_div_8_button_2_Template, 2, 1, \"button\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.primaryAction);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.secondaryAction);\n  }\n}\nfunction NoDataComponent_div_9_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 14);\n    i0.ɵɵlistener(\"click\", function NoDataComponent_div_9_li_4_Template_li_click_0_listener() {\n      const suggestion_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSuggestionClick(suggestion_r5));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const suggestion_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", suggestion_r5, \" \");\n  }\n}\nfunction NoDataComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"h4\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, NoDataComponent_div_9_li_4_Template, 2, 1, \"li\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.suggestionsTitle || \"Suggestions:\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.suggestions);\n  }\n}\nexport let NoDataComponent = /*#__PURE__*/(() => {\n  class NoDataComponent {\n    constructor() {\n      this.title = 'No Data Available';\n      this.message = 'There is no data to display at the moment.';\n      this.iconClass = 'fas fa-inbox';\n      this.containerClass = '';\n      this.showActions = false;\n    }\n    onPrimaryAction() {\n      // Emit event or handle primary action\n      console.log('Primary action clicked');\n    }\n    onSecondaryAction() {\n      // Emit event or handle secondary action\n      console.log('Secondary action clicked');\n    }\n    onSuggestionClick(suggestion) {\n      // Emit event or handle suggestion click\n      console.log('Suggestion clicked:', suggestion);\n    }\n    static {\n      this.ɵfac = function NoDataComponent_Factory(t) {\n        return new (t || NoDataComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: NoDataComponent,\n        selectors: [[\"app-no-data\"]],\n        inputs: {\n          title: \"title\",\n          message: \"message\",\n          iconClass: \"iconClass\",\n          containerClass: \"containerClass\",\n          showActions: \"showActions\",\n          primaryAction: \"primaryAction\",\n          secondaryAction: \"secondaryAction\",\n          suggestions: \"suggestions\",\n          suggestionsTitle: \"suggestionsTitle\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 10,\n        vars: 8,\n        consts: [[1, \"no-data-container\"], [1, \"no-data-content\"], [1, \"no-data-icon\"], [1, \"no-data-title\"], [1, \"no-data-message\"], [\"class\", \"no-data-actions\", 4, \"ngIf\"], [\"class\", \"no-data-suggestions\", 4, \"ngIf\"], [1, \"no-data-actions\"], [\"class\", \"btn-primary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn-secondary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn-primary\", 3, \"click\"], [1, \"btn-secondary\", 3, \"click\"], [1, \"no-data-suggestions\"], [3, \"click\", 4, \"ngFor\", \"ngForOf\"], [3, \"click\"]],\n        template: function NoDataComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n            i0.ɵɵelement(3, \"i\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"h3\", 3);\n            i0.ɵɵtext(5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p\", 4);\n            i0.ɵɵtext(7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(8, NoDataComponent_div_8_Template, 3, 2, \"div\", 5)(9, NoDataComponent_div_9_Template, 5, 2, \"div\", 6);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassMap(ctx.containerClass);\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassMap(ctx.iconClass);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.title);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.message);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showActions);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.suggestions && ctx.suggestions.length > 0);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf],\n        styles: [\".no-data-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;min-height:200px;padding:2rem;text-align:center}.no-data-container.full-height[_ngcontent-%COMP%]{min-height:400px}.no-data-container.compact[_ngcontent-%COMP%]{min-height:150px;padding:1rem}.no-data-content[_ngcontent-%COMP%]{max-width:400px;width:100%}.no-data-icon[_ngcontent-%COMP%]{margin-bottom:1.5rem}.no-data-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;color:#dee2e6;opacity:.8}.no-data-title[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;color:#495057;margin:0 0 .75rem}.no-data-message[_ngcontent-%COMP%]{font-size:1rem;color:#6c757d;margin:0 0 1.5rem;line-height:1.5}.no-data-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;justify-content:center;margin-bottom:1.5rem}.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%]{padding:.75rem 1.5rem;border-radius:6px;font-weight:500;cursor:pointer;border:none;transition:all .2s ease;font-size:.9rem}.btn-primary[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-primary[_ngcontent-%COMP%]:hover{background:#0056b3}.btn-secondary[_ngcontent-%COMP%]{background:#f8f9fa;color:#6c757d;border:1px solid #dee2e6}.btn-secondary[_ngcontent-%COMP%]:hover{background:#e9ecef;color:#495057}.no-data-suggestions[_ngcontent-%COMP%]{text-align:left}.no-data-suggestions[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1rem;font-weight:600;color:#495057;margin:0 0 .75rem}.no-data-suggestions[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0}.no-data-suggestions[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{padding:.5rem .75rem;background:#f8f9fa;border-radius:4px;margin-bottom:.5rem;cursor:pointer;transition:all .2s ease;color:#495057}.no-data-suggestions[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover{background:#e9ecef;color:#007bff}.no-data-suggestions[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child{margin-bottom:0}@media (max-width: 768px){.no-data-container[_ngcontent-%COMP%]{padding:1rem}.no-data-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem}.no-data-title[_ngcontent-%COMP%]{font-size:1.25rem}.no-data-message[_ngcontent-%COMP%]{font-size:.9rem}.no-data-actions[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%]{width:100%;max-width:200px}}\"]\n      });\n    }\n  }\n  return NoDataComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}