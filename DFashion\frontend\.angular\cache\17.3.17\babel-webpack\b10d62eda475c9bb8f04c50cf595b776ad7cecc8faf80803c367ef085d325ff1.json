{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./storage.service\";\nimport * as i3 from \"@ionic/angular\";\nexport class CartService {\n  constructor(http, storageService, toastController) {\n    this.http = http;\n    this.storageService = storageService;\n    this.toastController = toastController;\n    this.API_URL = environment.apiUrl;\n    this.cartItems = new BehaviorSubject([]);\n    this.cartSummary = new BehaviorSubject(null);\n    this.cartItemCount = new BehaviorSubject(0);\n    this.cartItems$ = this.cartItems.asObservable();\n    this.cartSummary$ = this.cartSummary.asObservable();\n    this.cartItemCount$ = this.cartItemCount.asObservable();\n    this.loadCart();\n  }\n  // Get cart from API\n  getCart() {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.get(`${this.API_URL}/cart-new`, options);\n  }\n  // Get cart count only (lightweight endpoint)\n  getCartCount() {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.get(`${this.API_URL}/cart-new/count`, options);\n  }\n  // Load cart and update local state\n  loadCart() {\n    // Check if user is logged in\n    const token = localStorage.getItem('token');\n    if (token) {\n      // User is logged in - load from API\n      this.loadCartFromAPI();\n    } else {\n      // Guest user - load from local storage\n      this.loadCartFromStorage();\n    }\n  }\n  // Load cart from API for logged-in users\n  loadCartFromAPI() {\n    // Check if user is authenticated\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.log('❌ No authentication token, using local storage fallback');\n      this.loadCartFromStorage();\n      return;\n    }\n    // Prevent multiple simultaneous API calls\n    if (this.isLoadingCart) {\n      console.log('🔄 Cart already loading, skipping duplicate request');\n      return;\n    }\n    this.isLoadingCart = true;\n    this.getCart().subscribe({\n      next: response => {\n        this.isLoadingCart = false;\n        if (response.success && response.cart) {\n          this.cartItems.next(response.cart.items || []);\n          this.cartSummary.next(response.summary);\n          this.updateCartCount();\n          console.log('✅ Cart loaded from API:', response.cart.items?.length || 0, 'items');\n        } else {\n          // No cart data from API, initialize empty cart\n          this.cartItems.next([]);\n          this.cartSummary.next(null);\n          this.updateCartCount();\n        }\n      },\n      error: error => {\n        this.isLoadingCart = false;\n        console.error('❌ API cart error:', error);\n        if (error.status === 401) {\n          console.log('❌ Authentication failed, clearing token');\n          localStorage.removeItem('token');\n          this.cartItems.next([]);\n          this.cartSummary.next(null);\n          this.updateCartCount();\n        } else if (error.status === 500) {\n          console.log('❌ Server error, using local storage fallback');\n          this.loadCartFromStorage();\n        } else {\n          console.log('❌ API error, using local storage fallback');\n          this.loadCartFromStorage();\n        }\n      }\n    });\n  }\n  loadCartFromStorage() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Check if storage service is available\n        if (!_this.storageService) {\n          console.log('Storage service not available, using empty cart');\n          _this.cartItems.next([]);\n          _this.updateCartCount();\n          return;\n        }\n        // Wait a bit for storage to initialize\n        yield new Promise(resolve => setTimeout(resolve, 100));\n        const cart = yield _this.storageService.getCart();\n        _this.cartItems.next(cart || []);\n        _this.updateCartCount();\n      } catch (error) {\n        console.error('Error loading cart from storage:', error);\n        _this.cartItems.next([]);\n        _this.updateCartCount();\n      }\n    })();\n  }\n  saveCartToStorage() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this2.storageService) {\n          console.log('Storage service not available, skipping cart save');\n          return;\n        }\n        yield _this2.storageService.setCart(_this2.cartItems.value);\n      } catch (error) {\n        console.error('Error saving cart to storage:', error);\n      }\n    })();\n  }\n  updateCartCount() {\n    const items = this.cartItems.value || [];\n    const count = items.reduce((total, item) => total + item.quantity, 0);\n    this.cartItemCount.next(count);\n    console.log('🛒 Cart count updated:', count);\n  }\n  // Method to refresh cart on user login\n  refreshCartOnLogin() {\n    console.log('🔄 Refreshing cart on login...');\n    this.loadCartFromAPI();\n  }\n  // Method to refresh only cart count (lightweight)\n  refreshCartCount() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.getCartCount().subscribe({\n        next: response => {\n          if (response.success) {\n            this.cartItemCount.next(response.count);\n            console.log('🛒 Cart count refreshed:', response.count);\n          }\n        },\n        error: error => {\n          console.error('❌ Error refreshing cart count:', error);\n          if (error.status === 401) {\n            console.log('❌ Authentication failed, clearing token');\n            localStorage.removeItem('token');\n            this.cartItemCount.next(0);\n          }\n        }\n      });\n    } else {\n      // No token, set count to 0\n      this.cartItemCount.next(0);\n    }\n  }\n  // Method to clear cart on logout\n  clearCartOnLogout() {\n    console.log('🔄 Clearing cart on logout...');\n    this.cartItems.next([]);\n    this.cartSummary.next(null);\n    this.cartItemCount.next(0);\n  }\n  // Add item to cart via API\n  addToCart(productId, quantity = 1, size, color) {\n    const payload = {\n      productId,\n      quantity,\n      size,\n      color\n    };\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.post(`${this.API_URL}/cart-new/add`, payload, options).pipe(tap(response => {\n      if (response.success) {\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      }\n    }));\n  }\n  // Legacy method for backward compatibility - works for guest users\n  addToCartLegacy(_x) {\n    var _this3 = this;\n    return _asyncToGenerator(function* (product, quantity = 1, size, color) {\n      try {\n        const productId = product._id || product.id;\n        // Try API first, but fallback to local storage for guest users\n        try {\n          const response = yield _this3.addToCart(productId, quantity, size, color).toPromise();\n          if (response?.success) {\n            yield _this3.showToast('Item added to cart', 'success');\n            _this3.loadCart(); // Refresh cart\n            return true;\n          }\n        } catch (apiError) {\n          console.log('API not available, using local storage');\n        }\n        // Fallback to local storage (for guest users)\n        const cartItem = {\n          _id: `${productId}_${size || 'default'}_${color || 'default'}`,\n          product: {\n            _id: productId,\n            name: product.name,\n            price: product.price,\n            originalPrice: product.originalPrice,\n            images: product.images || [],\n            brand: product.brand || '',\n            discount: product.discount\n          },\n          quantity,\n          size,\n          color,\n          addedAt: new Date()\n        };\n        const currentCart = _this3.cartItems.value;\n        const existingItemIndex = currentCart.findIndex(item => item.product._id === productId && (item.size || 'default') === (size || 'default') && (item.color || 'default') === (color || 'default'));\n        if (existingItemIndex >= 0) {\n          currentCart[existingItemIndex].quantity += quantity;\n        } else {\n          currentCart.push(cartItem);\n        }\n        _this3.cartItems.next(currentCart);\n        _this3.updateCartCount();\n        yield _this3.saveCartToStorage();\n        yield _this3.showToast('Item added to cart', 'success');\n        return true;\n      } catch (error) {\n        console.error('Error adding to cart:', error);\n        yield _this3.showToast('Failed to add item to cart', 'danger');\n        return false;\n      }\n    }).apply(this, arguments);\n  }\n  // Remove item from cart via API\n  removeFromCart(itemId) {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.delete(`${this.API_URL}/cart-new/remove/${itemId}`, options).pipe(tap(response => {\n      if (response.success) {\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      }\n    }));\n  }\n  // Bulk remove items from cart\n  bulkRemoveFromCart(itemIds) {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      body: {\n        itemIds\n      },\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {\n      body: {\n        itemIds\n      }\n    };\n    return this.http.delete(`${this.API_URL}/cart-new/bulk-remove`, options).pipe(tap(response => {\n      if (response.success) {\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      }\n    }));\n  }\n  // Legacy method\n  removeFromCartLegacy(itemId) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield _this4.removeFromCart(itemId).toPromise();\n        if (response?.success) {\n          yield _this4.showToast('Item removed from cart', 'success');\n          _this4.loadCart(); // Refresh cart\n        }\n      } catch (error) {\n        console.error('Error removing from cart:', error);\n        yield _this4.showToast('Failed to remove item from cart', 'danger');\n      }\n    })();\n  }\n  // Update cart item quantity via API\n  updateCartItem(itemId, quantity) {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.put(`${this.API_URL}/cart-new/update/${itemId}`, {\n      quantity\n    }, options).pipe(tap(response => {\n      if (response.success) {\n        // Immediately refresh cart to get updated count\n        this.loadCartFromAPI();\n      }\n    }));\n  }\n  // Legacy method\n  updateQuantity(itemId, quantity) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (quantity <= 0) {\n          yield _this5.removeFromCartLegacy(itemId);\n          return;\n        }\n        const response = yield _this5.updateCartItem(itemId, quantity).toPromise();\n        if (response?.success) {\n          _this5.loadCart(); // Refresh cart\n        }\n      } catch (error) {\n        console.error('Error updating quantity:', error);\n        yield _this5.showToast('Failed to update quantity', 'danger');\n      }\n    })();\n  }\n  // Clear cart via API\n  clearCartAPI() {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    } : {};\n    return this.http.delete(`${this.API_URL}/cart-new/clear`, options);\n  }\n  clearCart() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield _this6.clearCartAPI().toPromise();\n        if (response?.success) {\n          _this6.cartItems.next([]);\n          _this6.cartSummary.next(null);\n          _this6.updateCartCount();\n          yield _this6.showToast('Cart cleared', 'success');\n        }\n      } catch (error) {\n        console.error('Error clearing cart:', error);\n        yield _this6.showToast('Failed to clear cart', 'danger');\n      }\n    })();\n  }\n  getCartTotal() {\n    const summary = this.cartSummary.value;\n    if (summary) {\n      return summary.total;\n    }\n    // Fallback calculation\n    return this.cartItems.value.reduce((total, item) => {\n      const price = item.product.price;\n      return total + price * item.quantity;\n    }, 0);\n  }\n  getCartItemCount() {\n    return this.cartItemCount.value;\n  }\n  isInCart(productId, size, color) {\n    return this.cartItems.value.some(item => item.product._id === productId && (item.size || 'default') === (size || 'default') && (item.color || 'default') === (color || 'default'));\n  }\n  getCartItem(productId, size, color) {\n    return this.cartItems.value.find(item => item.product._id === productId && (item.size || 'default') === (size || 'default') && (item.color || 'default') === (color || 'default'));\n  }\n  showToast(message, color) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const toast = yield _this7.toastController.create({\n        message: message,\n        duration: 2000,\n        color: color,\n        position: 'bottom'\n      });\n      toast.present();\n    })();\n  }\n  static {\n    this.ɵfac = function CartService_Factory(t) {\n      return new (t || CartService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.StorageService), i0.ɵɵinject(i3.ToastController));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CartService,\n      factory: CartService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "environment", "CartService", "constructor", "http", "storageService", "toastController", "API_URL", "apiUrl", "cartItems", "cartSummary", "cartItemCount", "cartItems$", "asObservable", "cartSummary$", "cartItemCount$", "loadCart", "getCart", "token", "localStorage", "getItem", "options", "headers", "get", "getCartCount", "loadCartFromAPI", "loadCartFromStorage", "console", "log", "isLoadingCart", "subscribe", "next", "response", "success", "cart", "items", "summary", "updateCartCount", "length", "error", "status", "removeItem", "_this", "_asyncToGenerator", "Promise", "resolve", "setTimeout", "saveCartToStorage", "_this2", "setCart", "value", "count", "reduce", "total", "item", "quantity", "refreshCartOnLogin", "refreshCartCount", "clearCartOnLogout", "addToCart", "productId", "size", "color", "payload", "post", "pipe", "addToCartLegacy", "_x", "_this3", "product", "_id", "id", "to<PERSON>romise", "showToast", "apiError", "cartItem", "name", "price", "originalPrice", "images", "brand", "discount", "addedAt", "Date", "currentCart", "existingItemIndex", "findIndex", "push", "apply", "arguments", "removeFromCart", "itemId", "delete", "bulkRemoveFromCart", "itemIds", "body", "removeFromCartLegacy", "_this4", "updateCartItem", "put", "updateQuantity", "_this5", "clearCartAPI", "clearCart", "_this6", "getCartTotal", "getCartItemCount", "isInCart", "some", "getCartItem", "find", "message", "_this7", "toast", "create", "duration", "position", "present", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "StorageService", "i3", "ToastController", "factory", "ɵfac", "providedIn"], "sources": ["E:\\DFashion\\frontend\\src\\app\\core\\services\\cart.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable, of } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport { HttpClient } from '@angular/common/http';\nimport { StorageService } from './storage.service';\nimport { ToastController } from '@ionic/angular';\nimport { environment } from '../../../environments/environment';\n\nexport interface CartItem {\n  _id: string;\n  product: {\n    _id: string;\n    name: string;\n    price: number;\n    originalPrice?: number;\n    images: { url: string; isPrimary: boolean }[];\n    brand: string;\n    discount?: number;\n  };\n  quantity: number;\n  size?: string;\n  color?: string;\n  addedAt: Date;\n}\n\nexport interface CartSummary {\n  itemCount: number;\n  totalQuantity: number;\n  subtotal: number;\n  discount: number;\n  total: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class CartService {\n  private readonly API_URL = environment.apiUrl;\n  private cartItems = new BehaviorSubject<CartItem[]>([]);\n  private cartSummary = new BehaviorSubject<CartSummary | null>(null);\n  private cartItemCount = new BehaviorSubject<number>(0);\n\n  public cartItems$ = this.cartItems.asObservable();\n  public cartSummary$ = this.cartSummary.asObservable();\n  public cartItemCount$ = this.cartItemCount.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private storageService: StorageService,\n    private toastController: ToastController\n  ) {\n    this.loadCart();\n  }\n\n  // Get cart from API\n  getCart(): Observable<{ success: boolean; cart: any; summary: any }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.get<{ success: boolean; cart: any; summary: any }>(`${this.API_URL}/cart-new`, options);\n  }\n\n  // Get cart count only (lightweight endpoint)\n  getCartCount(): Observable<{ success: boolean; count: number; totalItems: number; itemCount: number }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.get<{ success: boolean; count: number; totalItems: number; itemCount: number }>(`${this.API_URL}/cart-new/count`, options);\n  }\n\n  // Load cart and update local state\n  loadCart() {\n    // Check if user is logged in\n    const token = localStorage.getItem('token');\n\n    if (token) {\n      // User is logged in - load from API\n      this.loadCartFromAPI();\n    } else {\n      // Guest user - load from local storage\n      this.loadCartFromStorage();\n    }\n  }\n\n  // Load cart from API for logged-in users\n  private loadCartFromAPI() {\n    // Check if user is authenticated\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.log('❌ No authentication token, using local storage fallback');\n      this.loadCartFromStorage();\n      return;\n    }\n\n    // Prevent multiple simultaneous API calls\n    if (this.isLoadingCart) {\n      console.log('🔄 Cart already loading, skipping duplicate request');\n      return;\n    }\n\n    this.isLoadingCart = true;\n\n    this.getCart().subscribe({\n      next: (response) => {\n        this.isLoadingCart = false;\n        if (response.success && response.cart) {\n          this.cartItems.next(response.cart.items || []);\n          this.cartSummary.next(response.summary);\n          this.updateCartCount();\n          console.log('✅ Cart loaded from API:', response.cart.items?.length || 0, 'items');\n        } else {\n          // No cart data from API, initialize empty cart\n          this.cartItems.next([]);\n          this.cartSummary.next(null);\n          this.updateCartCount();\n        }\n      },\n      error: (error) => {\n        this.isLoadingCart = false;\n        console.error('❌ API cart error:', error);\n\n        if (error.status === 401) {\n          console.log('❌ Authentication failed, clearing token');\n          localStorage.removeItem('token');\n          this.cartItems.next([]);\n          this.cartSummary.next(null);\n          this.updateCartCount();\n        } else if (error.status === 500) {\n          console.log('❌ Server error, using local storage fallback');\n          this.loadCartFromStorage();\n        } else {\n          console.log('❌ API error, using local storage fallback');\n          this.loadCartFromStorage();\n        }\n      }\n    });\n  }\n\n  private async loadCartFromStorage() {\n    try {\n      // Check if storage service is available\n      if (!this.storageService) {\n        console.log('Storage service not available, using empty cart');\n        this.cartItems.next([]);\n        this.updateCartCount();\n        return;\n      }\n\n      // Wait a bit for storage to initialize\n      await new Promise(resolve => setTimeout(resolve, 100));\n      const cart = await this.storageService.getCart();\n      this.cartItems.next(cart || []);\n      this.updateCartCount();\n    } catch (error) {\n      console.error('Error loading cart from storage:', error);\n      this.cartItems.next([]);\n      this.updateCartCount();\n    }\n  }\n\n  private async saveCartToStorage() {\n    try {\n      if (!this.storageService) {\n        console.log('Storage service not available, skipping cart save');\n        return;\n      }\n      await this.storageService.setCart(this.cartItems.value);\n    } catch (error) {\n      console.error('Error saving cart to storage:', error);\n    }\n  }\n\n  private updateCartCount() {\n    const items = this.cartItems.value || [];\n    const count = items.reduce((total, item) => total + item.quantity, 0);\n    this.cartItemCount.next(count);\n    console.log('🛒 Cart count updated:', count);\n  }\n\n  // Method to refresh cart on user login\n  refreshCartOnLogin() {\n    console.log('🔄 Refreshing cart on login...');\n    this.loadCartFromAPI();\n  }\n\n  // Method to refresh only cart count (lightweight)\n  refreshCartCount() {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.getCartCount().subscribe({\n        next: (response) => {\n          if (response.success) {\n            this.cartItemCount.next(response.count);\n            console.log('🛒 Cart count refreshed:', response.count);\n          }\n        },\n        error: (error) => {\n          console.error('❌ Error refreshing cart count:', error);\n          if (error.status === 401) {\n            console.log('❌ Authentication failed, clearing token');\n            localStorage.removeItem('token');\n            this.cartItemCount.next(0);\n          }\n        }\n      });\n    } else {\n      // No token, set count to 0\n      this.cartItemCount.next(0);\n    }\n  }\n\n  // Method to clear cart on logout\n  clearCartOnLogout() {\n    console.log('🔄 Clearing cart on logout...');\n    this.cartItems.next([]);\n    this.cartSummary.next(null);\n    this.cartItemCount.next(0);\n  }\n\n  // Add item to cart via API\n  addToCart(productId: string, quantity: number = 1, size?: string, color?: string): Observable<{ success: boolean; message: string }> {\n    const payload = { productId, quantity, size, color };\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.post<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/add`, payload, options).pipe(\n      tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      })\n    );\n  }\n\n  // Legacy method for backward compatibility - works for guest users\n  async addToCartLegacy(product: any, quantity: number = 1, size?: string, color?: string): Promise<boolean> {\n    try {\n      const productId = product._id || product.id;\n\n      // Try API first, but fallback to local storage for guest users\n      try {\n        const response = await this.addToCart(productId, quantity, size, color).toPromise();\n        if (response?.success) {\n          await this.showToast('Item added to cart', 'success');\n          this.loadCart(); // Refresh cart\n          return true;\n        }\n      } catch (apiError) {\n        console.log('API not available, using local storage');\n      }\n\n      // Fallback to local storage (for guest users)\n      const cartItem: CartItem = {\n        _id: `${productId}_${size || 'default'}_${color || 'default'}`,\n        product: {\n          _id: productId,\n          name: product.name,\n          price: product.price,\n          originalPrice: product.originalPrice,\n          images: product.images || [],\n          brand: product.brand || '',\n          discount: product.discount\n        },\n        quantity,\n        size,\n        color,\n        addedAt: new Date()\n      };\n\n      const currentCart = this.cartItems.value;\n      const existingItemIndex = currentCart.findIndex(item =>\n        item.product._id === productId &&\n        (item.size || 'default') === (size || 'default') &&\n        (item.color || 'default') === (color || 'default')\n      );\n\n      if (existingItemIndex >= 0) {\n        currentCart[existingItemIndex].quantity += quantity;\n      } else {\n        currentCart.push(cartItem);\n      }\n\n      this.cartItems.next(currentCart);\n      this.updateCartCount();\n      await this.saveCartToStorage();\n      await this.showToast('Item added to cart', 'success');\n      return true;\n\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n      await this.showToast('Failed to add item to cart', 'danger');\n      return false;\n    }\n  }\n\n  // Remove item from cart via API\n  removeFromCart(itemId: string): Observable<{ success: boolean; message: string }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.delete<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/remove/${itemId}`, options).pipe(\n      tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      })\n    );\n  }\n\n  // Bulk remove items from cart\n  bulkRemoveFromCart(itemIds: string[]): Observable<{ success: boolean; message: string; removedCount: number }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      body: { itemIds },\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {\n      body: { itemIds }\n    };\n    return this.http.delete<{ success: boolean; message: string; removedCount: number }>(`${this.API_URL}/cart-new/bulk-remove`, options).pipe(\n      tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      })\n    );\n  }\n\n  // Legacy method\n  async removeFromCartLegacy(itemId: string): Promise<void> {\n    try {\n      const response = await this.removeFromCart(itemId).toPromise();\n      if (response?.success) {\n        await this.showToast('Item removed from cart', 'success');\n        this.loadCart(); // Refresh cart\n      }\n    } catch (error) {\n      console.error('Error removing from cart:', error);\n      await this.showToast('Failed to remove item from cart', 'danger');\n    }\n  }\n\n  // Update cart item quantity via API\n  updateCartItem(itemId: string, quantity: number): Observable<{ success: boolean; message: string }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.put<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/update/${itemId}`, { quantity }, options).pipe(\n      tap(response => {\n        if (response.success) {\n          // Immediately refresh cart to get updated count\n          this.loadCartFromAPI();\n        }\n      })\n    );\n  }\n\n  // Legacy method\n  async updateQuantity(itemId: string, quantity: number): Promise<void> {\n    try {\n      if (quantity <= 0) {\n        await this.removeFromCartLegacy(itemId);\n        return;\n      }\n\n      const response = await this.updateCartItem(itemId, quantity).toPromise();\n      if (response?.success) {\n        this.loadCart(); // Refresh cart\n      }\n    } catch (error) {\n      console.error('Error updating quantity:', error);\n      await this.showToast('Failed to update quantity', 'danger');\n    }\n  }\n\n  // Clear cart via API\n  clearCartAPI(): Observable<{ success: boolean; message: string }> {\n    const token = localStorage.getItem('token');\n    const options = token ? {\n      headers: { 'Authorization': `Bearer ${token}` }\n    } : {};\n    return this.http.delete<{ success: boolean; message: string }>(`${this.API_URL}/cart-new/clear`, options);\n  }\n\n  async clearCart(): Promise<void> {\n    try {\n      const response = await this.clearCartAPI().toPromise();\n      if (response?.success) {\n        this.cartItems.next([]);\n        this.cartSummary.next(null);\n        this.updateCartCount();\n        await this.showToast('Cart cleared', 'success');\n      }\n    } catch (error) {\n      console.error('Error clearing cart:', error);\n      await this.showToast('Failed to clear cart', 'danger');\n    }\n  }\n\n  getCartTotal(): number {\n    const summary = this.cartSummary.value;\n    if (summary) {\n      return summary.total;\n    }\n\n    // Fallback calculation\n    return this.cartItems.value.reduce((total, item) => {\n      const price = item.product.price;\n      return total + (price * item.quantity);\n    }, 0);\n  }\n\n  getCartItemCount(): number {\n    return this.cartItemCount.value;\n  }\n\n  isInCart(productId: string, size?: string, color?: string): boolean {\n    return this.cartItems.value.some(item =>\n      item.product._id === productId &&\n      (item.size || 'default') === (size || 'default') &&\n      (item.color || 'default') === (color || 'default')\n    );\n  }\n\n  getCartItem(productId: string, size?: string, color?: string): CartItem | undefined {\n    return this.cartItems.value.find(item =>\n      item.product._id === productId &&\n      (item.size || 'default') === (size || 'default') &&\n      (item.color || 'default') === (color || 'default')\n    );\n  }\n\n  private async showToast(message: string, color: string) {\n    const toast = await this.toastController.create({\n      message: message,\n      duration: 2000,\n      color: color,\n      position: 'bottom'\n    });\n    toast.present();\n  }\n}\n"], "mappings": ";AACA,SAASA,eAAe,QAAwB,MAAM;AACtD,SAASC,GAAG,QAAQ,gBAAgB;AAIpC,SAASC,WAAW,QAAQ,mCAAmC;;;;;AA8B/D,OAAM,MAAOC,WAAW;EAUtBC,YACUC,IAAgB,EAChBC,cAA8B,EAC9BC,eAAgC;IAFhC,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IAZR,KAAAC,OAAO,GAAGN,WAAW,CAACO,MAAM;IACrC,KAAAC,SAAS,GAAG,IAAIV,eAAe,CAAa,EAAE,CAAC;IAC/C,KAAAW,WAAW,GAAG,IAAIX,eAAe,CAAqB,IAAI,CAAC;IAC3D,KAAAY,aAAa,GAAG,IAAIZ,eAAe,CAAS,CAAC,CAAC;IAE/C,KAAAa,UAAU,GAAG,IAAI,CAACH,SAAS,CAACI,YAAY,EAAE;IAC1C,KAAAC,YAAY,GAAG,IAAI,CAACJ,WAAW,CAACG,YAAY,EAAE;IAC9C,KAAAE,cAAc,GAAG,IAAI,CAACJ,aAAa,CAACE,YAAY,EAAE;IAOvD,IAAI,CAACG,QAAQ,EAAE;EACjB;EAEA;EACAC,OAAOA,CAAA;IACL,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACd,IAAI,CAACmB,GAAG,CAAgD,GAAG,IAAI,CAAChB,OAAO,WAAW,EAAEc,OAAO,CAAC;EAC1G;EAEA;EACAG,YAAYA,CAAA;IACV,MAAMN,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACd,IAAI,CAACmB,GAAG,CAA6E,GAAG,IAAI,CAAChB,OAAO,iBAAiB,EAAEc,OAAO,CAAC;EAC7I;EAEA;EACAL,QAAQA,CAAA;IACN;IACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAIF,KAAK,EAAE;MACT;MACA,IAAI,CAACO,eAAe,EAAE;KACvB,MAAM;MACL;MACA,IAAI,CAACC,mBAAmB,EAAE;;EAE9B;EAEA;EACQD,eAAeA,CAAA;IACrB;IACA,MAAMP,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVS,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;MACtE,IAAI,CAACF,mBAAmB,EAAE;MAC1B;;IAGF;IACA,IAAI,IAAI,CAACG,aAAa,EAAE;MACtBF,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE;;IAGF,IAAI,CAACC,aAAa,GAAG,IAAI;IAEzB,IAAI,CAACZ,OAAO,EAAE,CAACa,SAAS,CAAC;MACvBC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACH,aAAa,GAAG,KAAK;QAC1B,IAAIG,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;UACrC,IAAI,CAACzB,SAAS,CAACsB,IAAI,CAACC,QAAQ,CAACE,IAAI,CAACC,KAAK,IAAI,EAAE,CAAC;UAC9C,IAAI,CAACzB,WAAW,CAACqB,IAAI,CAACC,QAAQ,CAACI,OAAO,CAAC;UACvC,IAAI,CAACC,eAAe,EAAE;UACtBV,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEI,QAAQ,CAACE,IAAI,CAACC,KAAK,EAAEG,MAAM,IAAI,CAAC,EAAE,OAAO,CAAC;SAClF,MAAM;UACL;UACA,IAAI,CAAC7B,SAAS,CAACsB,IAAI,CAAC,EAAE,CAAC;UACvB,IAAI,CAACrB,WAAW,CAACqB,IAAI,CAAC,IAAI,CAAC;UAC3B,IAAI,CAACM,eAAe,EAAE;;MAE1B,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACV,aAAa,GAAG,KAAK;QAC1BF,OAAO,CAACY,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QAEzC,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;UACxBb,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;UACtDT,YAAY,CAACsB,UAAU,CAAC,OAAO,CAAC;UAChC,IAAI,CAAChC,SAAS,CAACsB,IAAI,CAAC,EAAE,CAAC;UACvB,IAAI,CAACrB,WAAW,CAACqB,IAAI,CAAC,IAAI,CAAC;UAC3B,IAAI,CAACM,eAAe,EAAE;SACvB,MAAM,IAAIE,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;UAC/Bb,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3D,IAAI,CAACF,mBAAmB,EAAE;SAC3B,MAAM;UACLC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;UACxD,IAAI,CAACF,mBAAmB,EAAE;;MAE9B;KACD,CAAC;EACJ;EAEcA,mBAAmBA,CAAA;IAAA,IAAAgB,KAAA;IAAA,OAAAC,iBAAA;MAC/B,IAAI;QACF;QACA,IAAI,CAACD,KAAI,CAACrC,cAAc,EAAE;UACxBsB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;UAC9Dc,KAAI,CAACjC,SAAS,CAACsB,IAAI,CAAC,EAAE,CAAC;UACvBW,KAAI,CAACL,eAAe,EAAE;UACtB;;QAGF;QACA,MAAM,IAAIO,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;QACtD,MAAMX,IAAI,SAASQ,KAAI,CAACrC,cAAc,CAACY,OAAO,EAAE;QAChDyB,KAAI,CAACjC,SAAS,CAACsB,IAAI,CAACG,IAAI,IAAI,EAAE,CAAC;QAC/BQ,KAAI,CAACL,eAAe,EAAE;OACvB,CAAC,OAAOE,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDG,KAAI,CAACjC,SAAS,CAACsB,IAAI,CAAC,EAAE,CAAC;QACvBW,KAAI,CAACL,eAAe,EAAE;;IACvB;EACH;EAEcU,iBAAiBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MAC7B,IAAI;QACF,IAAI,CAACK,MAAI,CAAC3C,cAAc,EAAE;UACxBsB,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;UAChE;;QAEF,MAAMoB,MAAI,CAAC3C,cAAc,CAAC4C,OAAO,CAACD,MAAI,CAACvC,SAAS,CAACyC,KAAK,CAAC;OACxD,CAAC,OAAOX,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;IACtD;EACH;EAEQF,eAAeA,CAAA;IACrB,MAAMF,KAAK,GAAG,IAAI,CAAC1B,SAAS,CAACyC,KAAK,IAAI,EAAE;IACxC,MAAMC,KAAK,GAAGhB,KAAK,CAACiB,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACC,QAAQ,EAAE,CAAC,CAAC;IACrE,IAAI,CAAC5C,aAAa,CAACoB,IAAI,CAACoB,KAAK,CAAC;IAC9BxB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEuB,KAAK,CAAC;EAC9C;EAEA;EACAK,kBAAkBA,CAAA;IAChB7B,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7C,IAAI,CAACH,eAAe,EAAE;EACxB;EAEA;EACAgC,gBAAgBA,CAAA;IACd,MAAMvC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT,IAAI,CAACM,YAAY,EAAE,CAACM,SAAS,CAAC;QAC5BC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpB,IAAI,CAACtB,aAAa,CAACoB,IAAI,CAACC,QAAQ,CAACmB,KAAK,CAAC;YACvCxB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEI,QAAQ,CAACmB,KAAK,CAAC;;QAE3D,CAAC;QACDZ,KAAK,EAAGA,KAAK,IAAI;UACfZ,OAAO,CAACY,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACtD,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;YACxBb,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;YACtDT,YAAY,CAACsB,UAAU,CAAC,OAAO,CAAC;YAChC,IAAI,CAAC9B,aAAa,CAACoB,IAAI,CAAC,CAAC,CAAC;;QAE9B;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACpB,aAAa,CAACoB,IAAI,CAAC,CAAC,CAAC;;EAE9B;EAEA;EACA2B,iBAAiBA,CAAA;IACf/B,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,IAAI,CAACnB,SAAS,CAACsB,IAAI,CAAC,EAAE,CAAC;IACvB,IAAI,CAACrB,WAAW,CAACqB,IAAI,CAAC,IAAI,CAAC;IAC3B,IAAI,CAACpB,aAAa,CAACoB,IAAI,CAAC,CAAC,CAAC;EAC5B;EAEA;EACA4B,SAASA,CAACC,SAAiB,EAAEL,QAAA,GAAmB,CAAC,EAAEM,IAAa,EAAEC,KAAc;IAC9E,MAAMC,OAAO,GAAG;MAAEH,SAAS;MAAEL,QAAQ;MAAEM,IAAI;MAAEC;IAAK,CAAE;IACpD,MAAM5C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACd,IAAI,CAAC4D,IAAI,CAAwC,GAAG,IAAI,CAACzD,OAAO,eAAe,EAAEwD,OAAO,EAAE1C,OAAO,CAAC,CAAC4C,IAAI,CACjHjE,GAAG,CAACgC,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB;QACA,IAAI,CAACR,eAAe,EAAE;;IAE1B,CAAC,CAAC,CACH;EACH;EAEA;EACMyC,eAAeA,CAAAC,EAAA,EAAkE;IAAA,IAAAC,MAAA;IAAA,OAAAzB,iBAAA,YAAjE0B,OAAY,EAAEd,QAAA,GAAmB,CAAC,EAAEM,IAAa,EAAEC,KAAc;MACrF,IAAI;QACF,MAAMF,SAAS,GAAGS,OAAO,CAACC,GAAG,IAAID,OAAO,CAACE,EAAE;QAE3C;QACA,IAAI;UACF,MAAMvC,QAAQ,SAASoC,MAAI,CAACT,SAAS,CAACC,SAAS,EAAEL,QAAQ,EAAEM,IAAI,EAAEC,KAAK,CAAC,CAACU,SAAS,EAAE;UACnF,IAAIxC,QAAQ,EAAEC,OAAO,EAAE;YACrB,MAAMmC,MAAI,CAACK,SAAS,CAAC,oBAAoB,EAAE,SAAS,CAAC;YACrDL,MAAI,CAACpD,QAAQ,EAAE,CAAC,CAAC;YACjB,OAAO,IAAI;;SAEd,CAAC,OAAO0D,QAAQ,EAAE;UACjB/C,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAGvD;QACA,MAAM+C,QAAQ,GAAa;UACzBL,GAAG,EAAE,GAAGV,SAAS,IAAIC,IAAI,IAAI,SAAS,IAAIC,KAAK,IAAI,SAAS,EAAE;UAC9DO,OAAO,EAAE;YACPC,GAAG,EAAEV,SAAS;YACdgB,IAAI,EAAEP,OAAO,CAACO,IAAI;YAClBC,KAAK,EAAER,OAAO,CAACQ,KAAK;YACpBC,aAAa,EAAET,OAAO,CAACS,aAAa;YACpCC,MAAM,EAAEV,OAAO,CAACU,MAAM,IAAI,EAAE;YAC5BC,KAAK,EAAEX,OAAO,CAACW,KAAK,IAAI,EAAE;YAC1BC,QAAQ,EAAEZ,OAAO,CAACY;WACnB;UACD1B,QAAQ;UACRM,IAAI;UACJC,KAAK;UACLoB,OAAO,EAAE,IAAIC,IAAI;SAClB;QAED,MAAMC,WAAW,GAAGhB,MAAI,CAAC3D,SAAS,CAACyC,KAAK;QACxC,MAAMmC,iBAAiB,GAAGD,WAAW,CAACE,SAAS,CAAChC,IAAI,IAClDA,IAAI,CAACe,OAAO,CAACC,GAAG,KAAKV,SAAS,IAC9B,CAACN,IAAI,CAACO,IAAI,IAAI,SAAS,OAAOA,IAAI,IAAI,SAAS,CAAC,IAChD,CAACP,IAAI,CAACQ,KAAK,IAAI,SAAS,OAAOA,KAAK,IAAI,SAAS,CAAC,CACnD;QAED,IAAIuB,iBAAiB,IAAI,CAAC,EAAE;UAC1BD,WAAW,CAACC,iBAAiB,CAAC,CAAC9B,QAAQ,IAAIA,QAAQ;SACpD,MAAM;UACL6B,WAAW,CAACG,IAAI,CAACZ,QAAQ,CAAC;;QAG5BP,MAAI,CAAC3D,SAAS,CAACsB,IAAI,CAACqD,WAAW,CAAC;QAChChB,MAAI,CAAC/B,eAAe,EAAE;QACtB,MAAM+B,MAAI,CAACrB,iBAAiB,EAAE;QAC9B,MAAMqB,MAAI,CAACK,SAAS,CAAC,oBAAoB,EAAE,SAAS,CAAC;QACrD,OAAO,IAAI;OAEZ,CAAC,OAAOlC,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAM6B,MAAI,CAACK,SAAS,CAAC,4BAA4B,EAAE,QAAQ,CAAC;QAC5D,OAAO,KAAK;;IACb,GAAAe,KAAA,OAAAC,SAAA;EACH;EAEA;EACAC,cAAcA,CAACC,MAAc;IAC3B,MAAMzE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACd,IAAI,CAACwF,MAAM,CAAwC,GAAG,IAAI,CAACrF,OAAO,oBAAoBoF,MAAM,EAAE,EAAEtE,OAAO,CAAC,CAAC4C,IAAI,CACvHjE,GAAG,CAACgC,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB;QACA,IAAI,CAACR,eAAe,EAAE;;IAE1B,CAAC,CAAC,CACH;EACH;EAEA;EACAoE,kBAAkBA,CAACC,OAAiB;IAClC,MAAM5E,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtB6E,IAAI,EAAE;QAAED;MAAO,CAAE;MACjBxE,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG;MACF6E,IAAI,EAAE;QAAED;MAAO;KAChB;IACD,OAAO,IAAI,CAAC1F,IAAI,CAACwF,MAAM,CAA8D,GAAG,IAAI,CAACrF,OAAO,uBAAuB,EAAEc,OAAO,CAAC,CAAC4C,IAAI,CACxIjE,GAAG,CAACgC,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB;QACA,IAAI,CAACR,eAAe,EAAE;;IAE1B,CAAC,CAAC,CACH;EACH;EAEA;EACMuE,oBAAoBA,CAACL,MAAc;IAAA,IAAAM,MAAA;IAAA,OAAAtD,iBAAA;MACvC,IAAI;QACF,MAAMX,QAAQ,SAASiE,MAAI,CAACP,cAAc,CAACC,MAAM,CAAC,CAACnB,SAAS,EAAE;QAC9D,IAAIxC,QAAQ,EAAEC,OAAO,EAAE;UACrB,MAAMgE,MAAI,CAACxB,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC;UACzDwB,MAAI,CAACjF,QAAQ,EAAE,CAAC,CAAC;;OAEpB,CAAC,OAAOuB,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAM0D,MAAI,CAACxB,SAAS,CAAC,iCAAiC,EAAE,QAAQ,CAAC;;IAClE;EACH;EAEA;EACAyB,cAAcA,CAACP,MAAc,EAAEpC,QAAgB;IAC7C,MAAMrC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACd,IAAI,CAAC+F,GAAG,CAAwC,GAAG,IAAI,CAAC5F,OAAO,oBAAoBoF,MAAM,EAAE,EAAE;MAAEpC;IAAQ,CAAE,EAAElC,OAAO,CAAC,CAAC4C,IAAI,CAClIjE,GAAG,CAACgC,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB;QACA,IAAI,CAACR,eAAe,EAAE;;IAE1B,CAAC,CAAC,CACH;EACH;EAEA;EACM2E,cAAcA,CAACT,MAAc,EAAEpC,QAAgB;IAAA,IAAA8C,MAAA;IAAA,OAAA1D,iBAAA;MACnD,IAAI;QACF,IAAIY,QAAQ,IAAI,CAAC,EAAE;UACjB,MAAM8C,MAAI,CAACL,oBAAoB,CAACL,MAAM,CAAC;UACvC;;QAGF,MAAM3D,QAAQ,SAASqE,MAAI,CAACH,cAAc,CAACP,MAAM,EAAEpC,QAAQ,CAAC,CAACiB,SAAS,EAAE;QACxE,IAAIxC,QAAQ,EAAEC,OAAO,EAAE;UACrBoE,MAAI,CAACrF,QAAQ,EAAE,CAAC,CAAC;;OAEpB,CAAC,OAAOuB,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAM8D,MAAI,CAAC5B,SAAS,CAAC,2BAA2B,EAAE,QAAQ,CAAC;;IAC5D;EACH;EAEA;EACA6B,YAAYA,CAAA;IACV,MAAMpF,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,OAAO,GAAGH,KAAK,GAAG;MACtBI,OAAO,EAAE;QAAE,eAAe,EAAE,UAAUJ,KAAK;MAAE;KAC9C,GAAG,EAAE;IACN,OAAO,IAAI,CAACd,IAAI,CAACwF,MAAM,CAAwC,GAAG,IAAI,CAACrF,OAAO,iBAAiB,EAAEc,OAAO,CAAC;EAC3G;EAEMkF,SAASA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA7D,iBAAA;MACb,IAAI;QACF,MAAMX,QAAQ,SAASwE,MAAI,CAACF,YAAY,EAAE,CAAC9B,SAAS,EAAE;QACtD,IAAIxC,QAAQ,EAAEC,OAAO,EAAE;UACrBuE,MAAI,CAAC/F,SAAS,CAACsB,IAAI,CAAC,EAAE,CAAC;UACvByE,MAAI,CAAC9F,WAAW,CAACqB,IAAI,CAAC,IAAI,CAAC;UAC3ByE,MAAI,CAACnE,eAAe,EAAE;UACtB,MAAMmE,MAAI,CAAC/B,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC;;OAElD,CAAC,OAAOlC,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,MAAMiE,MAAI,CAAC/B,SAAS,CAAC,sBAAsB,EAAE,QAAQ,CAAC;;IACvD;EACH;EAEAgC,YAAYA,CAAA;IACV,MAAMrE,OAAO,GAAG,IAAI,CAAC1B,WAAW,CAACwC,KAAK;IACtC,IAAId,OAAO,EAAE;MACX,OAAOA,OAAO,CAACiB,KAAK;;IAGtB;IACA,OAAO,IAAI,CAAC5C,SAAS,CAACyC,KAAK,CAACE,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAI;MACjD,MAAMuB,KAAK,GAAGvB,IAAI,CAACe,OAAO,CAACQ,KAAK;MAChC,OAAOxB,KAAK,GAAIwB,KAAK,GAAGvB,IAAI,CAACC,QAAS;IACxC,CAAC,EAAE,CAAC,CAAC;EACP;EAEAmD,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC/F,aAAa,CAACuC,KAAK;EACjC;EAEAyD,QAAQA,CAAC/C,SAAiB,EAAEC,IAAa,EAAEC,KAAc;IACvD,OAAO,IAAI,CAACrD,SAAS,CAACyC,KAAK,CAAC0D,IAAI,CAACtD,IAAI,IACnCA,IAAI,CAACe,OAAO,CAACC,GAAG,KAAKV,SAAS,IAC9B,CAACN,IAAI,CAACO,IAAI,IAAI,SAAS,OAAOA,IAAI,IAAI,SAAS,CAAC,IAChD,CAACP,IAAI,CAACQ,KAAK,IAAI,SAAS,OAAOA,KAAK,IAAI,SAAS,CAAC,CACnD;EACH;EAEA+C,WAAWA,CAACjD,SAAiB,EAAEC,IAAa,EAAEC,KAAc;IAC1D,OAAO,IAAI,CAACrD,SAAS,CAACyC,KAAK,CAAC4D,IAAI,CAACxD,IAAI,IACnCA,IAAI,CAACe,OAAO,CAACC,GAAG,KAAKV,SAAS,IAC9B,CAACN,IAAI,CAACO,IAAI,IAAI,SAAS,OAAOA,IAAI,IAAI,SAAS,CAAC,IAChD,CAACP,IAAI,CAACQ,KAAK,IAAI,SAAS,OAAOA,KAAK,IAAI,SAAS,CAAC,CACnD;EACH;EAEcW,SAASA,CAACsC,OAAe,EAAEjD,KAAa;IAAA,IAAAkD,MAAA;IAAA,OAAArE,iBAAA;MACpD,MAAMsE,KAAK,SAASD,MAAI,CAAC1G,eAAe,CAAC4G,MAAM,CAAC;QAC9CH,OAAO,EAAEA,OAAO;QAChBI,QAAQ,EAAE,IAAI;QACdrD,KAAK,EAAEA,KAAK;QACZsD,QAAQ,EAAE;OACX,CAAC;MACFH,KAAK,CAACI,OAAO,EAAE;IAAC;EAClB;;;uBA3ZWnH,WAAW,EAAAoH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,eAAA;IAAA;EAAA;;;aAAX3H,WAAW;MAAA4H,OAAA,EAAX5H,WAAW,CAAA6H,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}