{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/role-management.service\";\nimport * as i2 from \"../../../core/services/permission-management.service\";\nimport * as i3 from \"@angular/common\";\nfunction DepartmentDashboardComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"span\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 13);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r1.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r1.label);\n  }\n}\nfunction DepartmentDashboardComponent_div_12_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function DepartmentDashboardComponent_div_12_button_4_Template_button_click_0_listener() {\n      const action_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.handleQuickAction(action_r3.action));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const action_r3 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"background\", action_r3.color);\n    i0.ɵɵproperty(\"title\", action_r3.label);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(action_r3.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r3.label);\n  }\n}\nfunction DepartmentDashboardComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"h3\");\n    i0.ɵɵtext(2, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 15);\n    i0.ɵɵtemplate(4, DepartmentDashboardComponent_div_12_button_4_Template, 4, 6, \"button\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.getAvailableQuickActions());\n  }\n}\nfunction DepartmentDashboardComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 27)(6, \"div\", 28);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 29);\n    i0.ɵɵelement(9, \"i\");\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 30);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const widget_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(widget_r5.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(widget_r5.data.value);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(widget_r5.data.trend);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r3.getTrendIcon(widget_r5.data.trend));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(widget_r5.data.change);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(widget_r5.data.label);\n  }\n}\nfunction DepartmentDashboardComponent_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 33)(5, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function DepartmentDashboardComponent_div_15_div_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const widget_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.refreshWidget(widget_r5.id));\n    });\n    i0.ɵɵelement(6, \"i\", 35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 36)(8, \"div\", 37);\n    i0.ɵɵelement(9, \"i\", 38);\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const widget_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(widget_r5.title);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", widget_r5.title, \" Chart\");\n  }\n}\nfunction DepartmentDashboardComponent_div_15_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function DepartmentDashboardComponent_div_15_div_3_div_7_Template_div_click_0_listener() {\n      const item_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.handleListItemClick(item_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 45);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 46)(4, \"div\", 47);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 48);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 49)(9, \"span\", 50);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 51);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background\", item_r8.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(item_r8.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r8.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.subtitle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r8.time);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(item_r8.status);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r8.statusText);\n  }\n}\nfunction DepartmentDashboardComponent_div_15_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"a\", 41);\n    i0.ɵɵtext(5, \"View All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 42);\n    i0.ɵɵtemplate(7, DepartmentDashboardComponent_div_15_div_3_div_7_Template, 13, 10, \"div\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const widget_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(widget_r5.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", widget_r5.data.items);\n  }\n}\nfunction DepartmentDashboardComponent_div_15_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 54);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 55)(7, \"div\", 56);\n    i0.ɵɵelement(8, \"div\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 58)(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 59);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const widget_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(widget_r5.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", widget_r5.data.percentage, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"width\", widget_r5.data.percentage, \"%\")(\"background\", widget_r5.data.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", widget_r5.data.current, \" / \", widget_r5.data.target, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(widget_r5.data.label);\n  }\n}\nfunction DepartmentDashboardComponent_div_15_div_5_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"div\", 72);\n    i0.ɵɵelementStart(4, \"div\", 73)(5, \"div\", 74);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 75);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const activity_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r10.time);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background\", activity_r10.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r10.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r10.description);\n  }\n}\nfunction DepartmentDashboardComponent_div_15_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 62)(5, \"select\", 63);\n    i0.ɵɵlistener(\"change\", function DepartmentDashboardComponent_div_15_div_5_Template_select_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.filterActivity($event));\n    });\n    i0.ɵɵelementStart(6, \"option\", 64);\n    i0.ɵɵtext(7, \"All Activities\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"option\", 65);\n    i0.ɵɵtext(9, \"Today\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"option\", 66);\n    i0.ɵɵtext(11, \"This Week\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"div\", 67)(13, \"div\", 68);\n    i0.ɵɵtemplate(14, DepartmentDashboardComponent_div_15_div_5_div_14_Template, 9, 5, \"div\", 69);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const widget_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(widget_r5.title);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", widget_r5.data.activities);\n  }\n}\nfunction DepartmentDashboardComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, DepartmentDashboardComponent_div_15_div_1_Template, 14, 8, \"div\", 19)(2, DepartmentDashboardComponent_div_15_div_2_Template, 12, 2, \"div\", 20)(3, DepartmentDashboardComponent_div_15_div_3_Template, 8, 2, \"div\", 21)(4, DepartmentDashboardComponent_div_15_div_4_Template, 14, 9, \"div\", 22)(5, DepartmentDashboardComponent_div_15_div_5_Template, 15, 2, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const widget_r5 = ctx.$implicit;\n    i0.ɵɵattribute(\"data-size\", widget_r5.size)(\"data-type\", widget_r5.type);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", widget_r5.type === \"metric\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", widget_r5.type === \"chart\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", widget_r5.type === \"list\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", widget_r5.type === \"progress\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", widget_r5.type === \"activity\");\n  }\n}\nexport let DepartmentDashboardComponent = /*#__PURE__*/(() => {\n  class DepartmentDashboardComponent {\n    constructor(roleManagementService, permissionManagementService) {\n      this.roleManagementService = roleManagementService;\n      this.permissionManagementService = permissionManagementService;\n      this.department = null;\n      this.userRole = null;\n      this.departmentConfig = null;\n      this.destroy$ = new Subject();\n      this.departmentConfigs = {\n        administration: {\n          department: 'administration',\n          name: 'Administration',\n          color: '#FF6B6B',\n          icon: 'fas fa-shield-alt',\n          widgets: [{\n            id: 'user_stats',\n            title: 'User Statistics',\n            type: 'metric',\n            size: 'small',\n            data: {\n              value: '1,247',\n              change: '+12%',\n              trend: 'up',\n              label: 'Total Users'\n            }\n          }, {\n            id: 'system_health',\n            title: 'System Health',\n            type: 'progress',\n            size: 'medium',\n            data: {\n              percentage: 94,\n              current: 94,\n              target: 100,\n              label: 'System Performance',\n              color: '#4CAF50'\n            }\n          }, {\n            id: 'recent_activities',\n            title: 'Recent Activities',\n            type: 'activity',\n            size: 'large',\n            data: {\n              activities: [{\n                time: '2 min ago',\n                title: 'New user registered',\n                description: '<EMAIL>',\n                color: '#4CAF50'\n              }, {\n                time: '15 min ago',\n                title: 'System backup completed',\n                description: 'Daily backup successful',\n                color: '#2196F3'\n              }]\n            }\n          }],\n          quickActions: [{\n            id: 'add_user',\n            label: 'Add User',\n            icon: 'fas fa-user-plus',\n            color: '#4CAF50',\n            action: 'add_user'\n          }, {\n            id: 'system_settings',\n            label: 'System Settings',\n            icon: 'fas fa-cogs',\n            color: '#FF9800',\n            action: 'system_settings'\n          }, {\n            id: 'backup',\n            label: 'Backup System',\n            icon: 'fas fa-download',\n            color: '#2196F3',\n            action: 'backup'\n          }]\n        },\n        sales: {\n          department: 'sales',\n          name: 'Sales',\n          color: '#45B7D1',\n          icon: 'fas fa-chart-line',\n          widgets: [{\n            id: 'monthly_revenue',\n            title: 'Monthly Revenue',\n            type: 'metric',\n            size: 'small',\n            data: {\n              value: '₹2.4M',\n              change: '+18%',\n              trend: 'up',\n              label: 'This Month'\n            }\n          }, {\n            id: 'sales_target',\n            title: 'Sales Target',\n            type: 'progress',\n            size: 'medium',\n            data: {\n              percentage: 87,\n              current: 87,\n              target: 100,\n              label: 'Monthly Target',\n              color: '#45B7D1'\n            }\n          }, {\n            id: 'top_performers',\n            title: 'Top Performers',\n            type: 'list',\n            size: 'medium',\n            data: {\n              items: [{\n                title: 'Rahul Sharma',\n                subtitle: '₹450K revenue',\n                icon: 'fas fa-trophy',\n                color: '#FFD700',\n                time: 'This month',\n                status: 'top',\n                statusText: 'Top'\n              }, {\n                title: 'Priya Patel',\n                subtitle: '₹380K revenue',\n                icon: 'fas fa-medal',\n                color: '#C0C0C0',\n                time: 'This month',\n                status: 'second',\n                statusText: '2nd'\n              }]\n            }\n          }],\n          quickActions: [{\n            id: 'add_lead',\n            label: 'Add Lead',\n            icon: 'fas fa-user-plus',\n            color: '#4CAF50',\n            action: 'add_lead'\n          }, {\n            id: 'create_quote',\n            label: 'Create Quote',\n            icon: 'fas fa-file-invoice',\n            color: '#FF9800',\n            action: 'create_quote'\n          }, {\n            id: 'view_pipeline',\n            label: 'Sales Pipeline',\n            icon: 'fas fa-funnel-dollar',\n            color: '#2196F3',\n            action: 'view_pipeline'\n          }]\n        },\n        marketing: {\n          department: 'marketing',\n          name: 'Marketing',\n          color: '#F38BA8',\n          icon: 'fas fa-bullhorn',\n          widgets: [{\n            id: 'campaign_reach',\n            title: 'Campaign Reach',\n            type: 'metric',\n            size: 'small',\n            data: {\n              value: '1.2M',\n              change: '+25%',\n              trend: 'up',\n              label: 'This Week'\n            }\n          }, {\n            id: 'engagement_rate',\n            title: 'Engagement Rate',\n            type: 'metric',\n            size: 'small',\n            data: {\n              value: '4.8%',\n              change: '+0.3%',\n              trend: 'up',\n              label: 'Average'\n            }\n          }, {\n            id: 'active_campaigns',\n            title: 'Active Campaigns',\n            type: 'list',\n            size: 'large',\n            data: {\n              items: [{\n                title: 'Summer Sale 2024',\n                subtitle: 'Instagram & Facebook',\n                icon: 'fas fa-fire',\n                color: '#FF5722',\n                time: '2 days left',\n                status: 'active',\n                statusText: 'Active'\n              }, {\n                title: 'New Collection Launch',\n                subtitle: 'Multi-platform',\n                icon: 'fas fa-rocket',\n                color: '#9C27B0',\n                time: '1 week left',\n                status: 'scheduled',\n                statusText: 'Scheduled'\n              }]\n            }\n          }],\n          quickActions: [{\n            id: 'create_campaign',\n            label: 'Create Campaign',\n            icon: 'fas fa-plus',\n            color: '#4CAF50',\n            action: 'create_campaign'\n          }, {\n            id: 'content_calendar',\n            label: 'Content Calendar',\n            icon: 'fas fa-calendar',\n            color: '#FF9800',\n            action: 'content_calendar'\n          }, {\n            id: 'analytics',\n            label: 'Analytics',\n            icon: 'fas fa-chart-bar',\n            color: '#2196F3',\n            action: 'analytics'\n          }]\n        },\n        accounting: {\n          department: 'accounting',\n          name: 'Accounting',\n          color: '#FFD93D',\n          icon: 'fas fa-calculator',\n          widgets: [{\n            id: 'monthly_profit',\n            title: 'Monthly Profit',\n            type: 'metric',\n            size: 'small',\n            data: {\n              value: '₹580K',\n              change: '+12%',\n              trend: 'up',\n              label: 'Net Profit'\n            }\n          }, {\n            id: 'pending_invoices',\n            title: 'Pending Invoices',\n            type: 'metric',\n            size: 'small',\n            data: {\n              value: '23',\n              change: '-5',\n              trend: 'down',\n              label: 'Outstanding'\n            }\n          }, {\n            id: 'expense_breakdown',\n            title: 'Expense Breakdown',\n            type: 'chart',\n            size: 'medium',\n            data: {\n              chartType: 'pie',\n              categories: ['Operations', 'Marketing', 'Salaries', 'Other']\n            }\n          }],\n          quickActions: [{\n            id: 'create_invoice',\n            label: 'Create Invoice',\n            icon: 'fas fa-file-invoice',\n            color: '#4CAF50',\n            action: 'create_invoice'\n          }, {\n            id: 'expense_report',\n            label: 'Expense Report',\n            icon: 'fas fa-receipt',\n            color: '#FF9800',\n            action: 'expense_report'\n          }, {\n            id: 'financial_summary',\n            label: 'Financial Summary',\n            icon: 'fas fa-chart-pie',\n            color: '#2196F3',\n            action: 'financial_summary'\n          }]\n        },\n        support: {\n          department: 'support',\n          name: 'Customer Support',\n          color: '#FF8C42',\n          icon: 'fas fa-headset',\n          widgets: [{\n            id: 'open_tickets',\n            title: 'Open Tickets',\n            type: 'metric',\n            size: 'small',\n            data: {\n              value: '47',\n              change: '-8',\n              trend: 'down',\n              label: 'Active Tickets'\n            }\n          }, {\n            id: 'response_time',\n            title: 'Avg Response Time',\n            type: 'metric',\n            size: 'small',\n            data: {\n              value: '2.3h',\n              change: '-0.5h',\n              trend: 'down',\n              label: 'This Week'\n            }\n          }, {\n            id: 'recent_tickets',\n            title: 'Recent Tickets',\n            type: 'list',\n            size: 'large',\n            data: {\n              items: [{\n                title: 'Payment Issue',\n                subtitle: 'Customer: John Doe',\n                icon: 'fas fa-credit-card',\n                color: '#F44336',\n                time: '5 min ago',\n                status: 'urgent',\n                statusText: 'Urgent'\n              }, {\n                title: 'Product Question',\n                subtitle: 'Customer: Jane Smith',\n                icon: 'fas fa-question-circle',\n                color: '#FF9800',\n                time: '15 min ago',\n                status: 'normal',\n                statusText: 'Normal'\n              }]\n            }\n          }],\n          quickActions: [{\n            id: 'create_ticket',\n            label: 'Create Ticket',\n            icon: 'fas fa-ticket-alt',\n            color: '#4CAF50',\n            action: 'create_ticket'\n          }, {\n            id: 'knowledge_base',\n            label: 'Knowledge Base',\n            icon: 'fas fa-book',\n            color: '#FF9800',\n            action: 'knowledge_base'\n          }, {\n            id: 'customer_feedback',\n            label: 'Customer Feedback',\n            icon: 'fas fa-comments',\n            color: '#2196F3',\n            action: 'customer_feedback'\n          }]\n        },\n        content: {\n          department: 'content',\n          name: 'Content Management',\n          color: '#B19CD9',\n          icon: 'fas fa-edit',\n          widgets: [{\n            id: 'published_content',\n            title: 'Published This Month',\n            type: 'metric',\n            size: 'small',\n            data: {\n              value: '156',\n              change: '+23',\n              trend: 'up',\n              label: 'Articles'\n            }\n          }, {\n            id: 'content_performance',\n            title: 'Content Performance',\n            type: 'chart',\n            size: 'medium',\n            data: {\n              chartType: 'line',\n              metric: 'engagement'\n            }\n          }, {\n            id: 'editorial_calendar',\n            title: 'Editorial Calendar',\n            type: 'calendar',\n            size: 'large',\n            data: {\n              events: []\n            }\n          }],\n          quickActions: [{\n            id: 'create_article',\n            label: 'Create Article',\n            icon: 'fas fa-pen',\n            color: '#4CAF50',\n            action: 'create_article'\n          }, {\n            id: 'schedule_post',\n            label: 'Schedule Post',\n            icon: 'fas fa-clock',\n            color: '#FF9800',\n            action: 'schedule_post'\n          }, {\n            id: 'content_analytics',\n            label: 'Content Analytics',\n            icon: 'fas fa-chart-line',\n            color: '#2196F3',\n            action: 'content_analytics'\n          }]\n        },\n        vendor_management: {\n          department: 'vendor_management',\n          name: 'Vendor Management',\n          color: '#FFB6C1',\n          icon: 'fas fa-store',\n          widgets: [{\n            id: 'active_vendors',\n            title: 'Active Vendors',\n            type: 'metric',\n            size: 'small',\n            data: {\n              value: '89',\n              change: '+5',\n              trend: 'up',\n              label: 'Partners'\n            }\n          }, {\n            id: 'contract_renewals',\n            title: 'Contract Renewals',\n            type: 'metric',\n            size: 'small',\n            data: {\n              value: '12',\n              change: '0',\n              trend: 'stable',\n              label: 'Due This Month'\n            }\n          }, {\n            id: 'vendor_performance',\n            title: 'Vendor Performance',\n            type: 'chart',\n            size: 'medium',\n            data: {\n              chartType: 'bar',\n              metric: 'rating'\n            }\n          }],\n          quickActions: [{\n            id: 'add_vendor',\n            label: 'Add Vendor',\n            icon: 'fas fa-plus',\n            color: '#4CAF50',\n            action: 'add_vendor'\n          }, {\n            id: 'review_contracts',\n            label: 'Review Contracts',\n            icon: 'fas fa-file-contract',\n            color: '#FF9800',\n            action: 'review_contracts'\n          }, {\n            id: 'vendor_reports',\n            label: 'Vendor Reports',\n            icon: 'fas fa-chart-bar',\n            color: '#2196F3',\n            action: 'vendor_reports'\n          }]\n        }\n      };\n    }\n    ngOnInit() {\n      if (this.department) {\n        this.departmentConfig = this.departmentConfigs[this.department];\n      }\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    getDepartmentGradient() {\n      if (!this.departmentConfig) return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';\n      const color = this.departmentConfig.color;\n      return `linear-gradient(135deg, ${color}40 0%, ${color}80 100%)`;\n    }\n    getRoleDisplayName() {\n      if (!this.userRole) return '';\n      return this.roleManagementService.getRoleConfig(this.userRole).displayName;\n    }\n    getWelcomeMessage() {\n      const hour = new Date().getHours();\n      if (hour < 12) return 'Good Morning';\n      if (hour < 17) return 'Good Afternoon';\n      return 'Good Evening';\n    }\n    getHeaderStats() {\n      // Mock data - would be replaced with real data\n      return [{\n        value: '94%',\n        label: 'Performance'\n      }, {\n        value: '23',\n        label: 'Active Tasks'\n      }, {\n        value: '5.2K',\n        label: 'This Month'\n      }];\n    }\n    getAvailableQuickActions() {\n      if (!this.departmentConfig || !this.userRole) return [];\n      return this.departmentConfig.quickActions.filter(action => {\n        if (!action.permission) return true;\n        const [module, actionName] = action.permission.split('.');\n        return this.permissionManagementService.hasPermission(this.userRole, module, actionName);\n      });\n    }\n    getAvailableWidgets() {\n      if (!this.departmentConfig || !this.userRole) return [];\n      return this.departmentConfig.widgets.filter(widget => {\n        if (!widget.permission) return true;\n        const [module, action] = widget.permission.split('.');\n        return this.permissionManagementService.hasPermission(this.userRole, module, action);\n      });\n    }\n    handleQuickAction(action) {\n      console.log('Quick action triggered:', action);\n      // Implement action handling\n    }\n    handleListItemClick(item) {\n      console.log('List item clicked:', item);\n      // Implement item click handling\n    }\n    refreshWidget(widgetId) {\n      console.log('Refreshing widget:', widgetId);\n      // Implement widget refresh\n    }\n    filterActivity(event) {\n      console.log('Filtering activity:', event.target.value);\n      // Implement activity filtering\n    }\n    getTrendIcon(trend) {\n      switch (trend) {\n        case 'up':\n          return 'fas fa-arrow-up';\n        case 'down':\n          return 'fas fa-arrow-down';\n        default:\n          return 'fas fa-minus';\n      }\n    }\n    static {\n      this.ɵfac = function DepartmentDashboardComponent_Factory(t) {\n        return new (t || DepartmentDashboardComponent)(i0.ɵɵdirectiveInject(i1.RoleManagementService), i0.ɵɵdirectiveInject(i2.PermissionManagementService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DepartmentDashboardComponent,\n        selectors: [[\"app-department-dashboard\"]],\n        inputs: {\n          department: \"department\",\n          userRole: \"userRole\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 16,\n        vars: 11,\n        consts: [[1, \"department-dashboard\"], [1, \"department-header\"], [1, \"header-content\"], [1, \"department-icon\"], [1, \"header-text\"], [1, \"header-stats\"], [\"class\", \"stat-item\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"quick-actions\", 4, \"ngIf\"], [1, \"dashboard-widgets\"], [1, \"widgets-grid\"], [\"class\", \"widget-container\", 4, \"ngFor\", \"ngForOf\"], [1, \"stat-item\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"quick-actions\"], [1, \"actions-grid\"], [\"class\", \"action-btn\", 3, \"background\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"action-btn\", 3, \"click\", \"title\"], [1, \"widget-container\"], [\"class\", \"metric-widget\", 4, \"ngIf\"], [\"class\", \"chart-widget\", 4, \"ngIf\"], [\"class\", \"list-widget\", 4, \"ngIf\"], [\"class\", \"progress-widget\", 4, \"ngIf\"], [\"class\", \"activity-widget\", 4, \"ngIf\"], [1, \"metric-widget\"], [1, \"metric-header\"], [1, \"fas\", \"fa-chart-line\"], [1, \"metric-content\"], [1, \"metric-value\"], [1, \"metric-change\"], [1, \"metric-label\"], [1, \"chart-widget\"], [1, \"chart-header\"], [1, \"chart-controls\"], [1, \"chart-btn\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"chart-content\"], [1, \"chart-placeholder\"], [1, \"fas\", \"fa-chart-bar\"], [1, \"list-widget\"], [1, \"list-header\"], [\"href\", \"#\", 1, \"view-all\"], [1, \"list-content\"], [\"class\", \"list-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-item\", 3, \"click\"], [1, \"item-icon\"], [1, \"item-content\"], [1, \"item-title\"], [1, \"item-subtitle\"], [1, \"item-meta\"], [1, \"item-time\"], [1, \"item-status\"], [1, \"progress-widget\"], [1, \"progress-header\"], [1, \"progress-percentage\"], [1, \"progress-content\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-details\"], [1, \"progress-label\"], [1, \"activity-widget\"], [1, \"activity-header\"], [1, \"activity-filter\"], [3, \"change\"], [\"value\", \"all\"], [\"value\", \"today\"], [\"value\", \"week\"], [1, \"activity-content\"], [1, \"activity-timeline\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"activity-item\"], [1, \"activity-time\"], [1, \"activity-dot\"], [1, \"activity-details\"], [1, \"activity-title\"], [1, \"activity-description\"]],\n        template: function DepartmentDashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"i\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 4)(6, \"h1\");\n            i0.ɵɵtext(7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p\");\n            i0.ɵɵtext(9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 5);\n            i0.ɵɵtemplate(11, DepartmentDashboardComponent_div_11_Template, 5, 2, \"div\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(12, DepartmentDashboardComponent_div_12_Template, 5, 1, \"div\", 7);\n            i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9);\n            i0.ɵɵtemplate(15, DepartmentDashboardComponent_div_15_Template, 6, 7, \"div\", 10);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵattribute(\"data-department\", ctx.department);\n            i0.ɵɵadvance();\n            i0.ɵɵstyleProp(\"background\", ctx.getDepartmentGradient());\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassMap(ctx.departmentConfig == null ? null : ctx.departmentConfig.icon);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\"\", ctx.departmentConfig == null ? null : ctx.departmentConfig.name, \" Dashboard\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate2(\"\", ctx.getRoleDisplayName(), \" - \", ctx.getWelcomeMessage(), \"\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.getHeaderStats());\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.getAvailableQuickActions().length > 0);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.getAvailableWidgets());\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf],\n        styles: [\".department-dashboard[_ngcontent-%COMP%]{max-width:1400px;margin:0 auto;padding:1rem;background:#f8f9fa;min-height:100vh}.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]{border-radius:20px;padding:2rem;margin-bottom:2rem;color:#fff;position:relative;overflow:hidden}.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grain\\\" width=\\\"100\\\" height=\\\"100\\\" patternUnits=\\\"userSpaceOnUse\\\"><circle cx=\\\"50\\\" cy=\\\"50\\\" r=\\\"1\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grain)\\\"/></svg>');opacity:.3}.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{position:relative;z-index:2;display:grid;grid-template-columns:auto 1fr auto;gap:2rem;align-items:center}@media (max-width: 768px){.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{grid-template-columns:1fr;text-align:center;gap:1rem}}.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .department-icon[_ngcontent-%COMP%]{width:80px;height:80px;background:#fff3;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:2rem;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);box-shadow:0 8px 32px #0000001a}@media (max-width: 768px){.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .department-icon[_ngcontent-%COMP%]{width:60px;height:60px;font-size:1.5rem;margin:0 auto}}.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:2.5rem;font-weight:700;text-shadow:0 2px 4px rgba(0,0,0,.1)}@media (max-width: 768px){.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem}}.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:1.1rem;opacity:.9}.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-stats[_ngcontent-%COMP%]{display:flex;gap:2rem}@media (max-width: 768px){.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-stats[_ngcontent-%COMP%]{justify-content:center;gap:1rem}}.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{text-align:center}.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{display:block;font-size:1.8rem;font-weight:700;line-height:1}@media (max-width: 768px){.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{font-size:1.4rem}}.department-dashboard[_ngcontent-%COMP%]   .department-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:.9rem;opacity:.8;margin-top:.25rem}.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:1.5rem;margin-bottom:2rem;box-shadow:0 2px 12px #0000000f}.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 1rem;color:#262626;font-size:1.2rem;font-weight:600}.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1rem}@media (max-width: 768px){.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(150px,1fr))}}.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;padding:1rem 1.5rem;border:none;border-radius:12px;color:#fff;cursor:pointer;transition:all .3s ease;font-weight:600;text-decoration:none;box-shadow:0 4px 12px #00000026}.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 20px #0003}.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:active{transform:translateY(0)}.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1rem}.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.9rem}.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(12,1fr);gap:1.5rem;grid-auto-rows:minmax(200px,auto)}@media (max-width: 768px){.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:1.5rem;box-shadow:0 2px 12px #0000000f;transition:all .3s ease}.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 20px #0000001a}.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=small][_ngcontent-%COMP%]{grid-column:span 3}@media (max-width: 1200px){.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=small][_ngcontent-%COMP%]{grid-column:span 4}}@media (max-width: 768px){.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=small][_ngcontent-%COMP%]{grid-column:span 1}}.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=medium][_ngcontent-%COMP%]{grid-column:span 6}@media (max-width: 768px){.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=medium][_ngcontent-%COMP%]{grid-column:span 1}}.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=large][_ngcontent-%COMP%]{grid-column:span 8}@media (max-width: 768px){.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=large][_ngcontent-%COMP%]{grid-column:span 1}}.department-dashboard[_ngcontent-%COMP%]   .dashboard-widgets[_ngcontent-%COMP%]   .widgets-grid[_ngcontent-%COMP%]   .widget-container[data-size=full][_ngcontent-%COMP%]{grid-column:span 12}.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem}.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;color:#8e8e8e;font-size:.9rem;font-weight:500;text-transform:uppercase;letter-spacing:.5px}.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:silver;font-size:1.1rem}.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%]   .metric-value[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;color:#262626;line-height:1;margin-bottom:.5rem}.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%]   .metric-change[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;font-size:.85rem;font-weight:600;margin-bottom:.25rem}.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%]   .metric-change.up[_ngcontent-%COMP%]{color:#4caf50}.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%]   .metric-change.down[_ngcontent-%COMP%]{color:#f44336}.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%]   .metric-change.stable[_ngcontent-%COMP%]{color:#ff9800}.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%]   .metric-label[_ngcontent-%COMP%]{color:#8e8e8e;font-size:.8rem}.department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem}.department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;color:#262626;font-size:1.1rem;font-weight:600}.department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-header[_ngcontent-%COMP%]   .chart-controls[_ngcontent-%COMP%]   .chart-btn[_ngcontent-%COMP%]{width:32px;height:32px;border:none;background:#f8f9fa;border-radius:8px;cursor:pointer;color:#6c757d;transition:all .3s ease}.department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-header[_ngcontent-%COMP%]   .chart-controls[_ngcontent-%COMP%]   .chart-btn[_ngcontent-%COMP%]:hover{background:#e9ecef;color:#495057}.department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-content[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]{height:200px;display:flex;flex-direction:column;align-items:center;justify-content:center;background:#f8f9fa;border-radius:12px;color:#6c757d}.department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-content[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem;margin-bottom:.5rem}.department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-content[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.9rem}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;color:#262626;font-size:1.1rem;font-weight:600}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-header[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%]{color:#667eea;text-decoration:none;font-size:.85rem;font-weight:500}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-header[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%]:hover{text-decoration:underline}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;padding:.75rem 0;border-bottom:1px solid #f0f0f0;cursor:pointer;transition:all .3s ease}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]:hover{background:#f8f9fa;margin:0 -1rem;padding-left:1rem;padding-right:1rem;border-radius:8px}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff;font-size:.9rem}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]{flex:1}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%]{font-weight:600;color:#262626;font-size:.9rem;margin-bottom:.25rem}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-subtitle[_ngcontent-%COMP%]{color:#8e8e8e;font-size:.8rem}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]{text-align:right}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .item-time[_ngcontent-%COMP%]{display:block;color:#8e8e8e;font-size:.75rem;margin-bottom:.25rem}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .item-status[_ngcontent-%COMP%]{padding:.25rem .5rem;border-radius:12px;font-size:.7rem;font-weight:600;text-transform:uppercase}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .item-status.urgent[_ngcontent-%COMP%]{background:#ffebee;color:#f44336}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .item-status.normal[_ngcontent-%COMP%]{background:#fff3e0;color:#ff9800}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .item-status.top[_ngcontent-%COMP%]{background:#fff8e1;color:#ffc107}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-meta[_ngcontent-%COMP%]   .item-status.active[_ngcontent-%COMP%]{background:#e8f5e8;color:#4caf50}.department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem}.department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;color:#262626;font-size:1.1rem;font-weight:600}.department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700;color:#262626}.department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-content[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{height:12px;background:#f0f0f0;border-radius:6px;overflow:hidden;margin-bottom:1rem}.department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-content[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%]{height:100%;border-radius:6px;transition:width .3s ease}.department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-content[_ngcontent-%COMP%]   .progress-details[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;font-size:.85rem;color:#8e8e8e}.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem}.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;color:#262626;font-size:1.1rem;font-weight:600}.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-header[_ngcontent-%COMP%]   .activity-filter[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{border:1px solid #e0e0e0;border-radius:6px;padding:.25rem .5rem;font-size:.8rem;background:#fff}.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]{display:grid;grid-template-columns:auto auto 1fr;gap:1rem;align-items:center;padding:.75rem 0;border-bottom:1px solid #f0f0f0}.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-time[_ngcontent-%COMP%]{font-size:.75rem;color:#8e8e8e;white-space:nowrap}.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-dot[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%}.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%]   .activity-title[_ngcontent-%COMP%]{font-weight:600;color:#262626;font-size:.9rem;margin-bottom:.25rem}.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%]   .activity-description[_ngcontent-%COMP%]{color:#8e8e8e;font-size:.8rem}@media (prefers-color-scheme: dark){.department-dashboard[_ngcontent-%COMP%]{background:#121212}.department-dashboard[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%], .department-dashboard[_ngcontent-%COMP%]   .widget-container[_ngcontent-%COMP%]{background:#1e1e1e;color:#fff}.department-dashboard[_ngcontent-%COMP%]   .metric-widget[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%]   .metric-value[_ngcontent-%COMP%], .department-dashboard[_ngcontent-%COMP%]   .chart-widget[_ngcontent-%COMP%]   .chart-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#fff}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]{border-bottom-color:#333}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]:hover{background:#2a2a2a}.department-dashboard[_ngcontent-%COMP%]   .list-widget[_ngcontent-%COMP%]   .list-content[_ngcontent-%COMP%]   .list-item[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%]{color:#fff}.department-dashboard[_ngcontent-%COMP%]   .progress-widget[_ngcontent-%COMP%]   .progress-content[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{background:#333}.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]{border-bottom-color:#333}.department-dashboard[_ngcontent-%COMP%]   .activity-widget[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-timeline[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-title[_ngcontent-%COMP%]{color:#fff}}\"]\n      });\n    }\n  }\n  return DepartmentDashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}