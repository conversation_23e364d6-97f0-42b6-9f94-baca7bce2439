{"ast": null, "code": "import _asyncToGenerator from \"E:/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { isPlatformServer } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, NgModule } from '@angular/core';\nimport { Storage } from '@ionic/storage';\nexport { Storage } from '@ionic/storage';\nconst StorageConfigToken = new InjectionToken('STORAGE_CONFIG_TOKEN');\nclass NoopStorage extends Storage {\n  constructor() {\n    super();\n  }\n  create() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      return _this;\n    })();\n  }\n  defineDriver() {\n    return _asyncToGenerator(function* () {})();\n  }\n  get driver() {\n    return 'noop';\n  }\n  get(key) {\n    return _asyncToGenerator(function* () {\n      return null;\n    })();\n  }\n  set(key, value) {\n    return _asyncToGenerator(function* () {})();\n  }\n  remove(key) {\n    return _asyncToGenerator(function* () {})();\n  }\n  clear() {\n    return _asyncToGenerator(function* () {})();\n  }\n  length() {\n    return _asyncToGenerator(function* () {\n      return 0;\n    })();\n  }\n  keys() {\n    return _asyncToGenerator(function* () {\n      return [];\n    })();\n  }\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  forEach(iteratorCallback) {\n    return _asyncToGenerator(function* () {})();\n  }\n  setEncryptionKey(key) {}\n}\nfunction provideStorage(platformId, storageConfig) {\n  if (isPlatformServer(platformId)) {\n    // When running in a server context return the NoopStorage\n    return new NoopStorage();\n  }\n  return new Storage(storageConfig);\n}\nclass IonicStorageModule {\n  static forRoot(storageConfig = null) {\n    return {\n      ngModule: IonicStorageModule,\n      providers: [{\n        provide: StorageConfigToken,\n        useValue: storageConfig\n      }, {\n        provide: Storage,\n        useFactory: provideStorage,\n        deps: [PLATFORM_ID, StorageConfigToken]\n      }]\n    };\n  }\n}\nIonicStorageModule.ɵfac = function IonicStorageModule_Factory(t) {\n  return new (t || IonicStorageModule)();\n};\nIonicStorageModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: IonicStorageModule\n});\nIonicStorageModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonicStorageModule, [{\n    type: NgModule\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { IonicStorageModule, StorageConfigToken, provideStorage };", "map": {"version": 3, "names": ["isPlatformServer", "i0", "InjectionToken", "PLATFORM_ID", "NgModule", "Storage", "StorageConfigToken", "NoopStorage", "constructor", "create", "_this", "_asyncToGenerator", "defineDriver", "driver", "get", "key", "set", "value", "remove", "clear", "length", "keys", "for<PERSON>ach", "iteratorCallback", "setEncryptionKey", "provideStorage", "platformId", "storageConfig", "IonicStorageModule", "forRoot", "ngModule", "providers", "provide", "useValue", "useFactory", "deps", "ɵfac", "IonicStorageModule_Factory", "t", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "ngDevMode", "ɵsetClassMetadata"], "sources": ["E:/DFashion/frontend/node_modules/@ionic/storage-angular/fesm2020/ionic-storage-angular.mjs"], "sourcesContent": ["import { isPlatformServer } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, NgModule } from '@angular/core';\nimport { Storage } from '@ionic/storage';\nexport { Storage } from '@ionic/storage';\n\nconst StorageConfigToken = new InjectionToken('STORAGE_CONFIG_TOKEN');\nclass NoopStorage extends Storage {\n    constructor() {\n        super();\n    }\n    async create() {\n        return this;\n    }\n    async defineDriver() { }\n    get driver() {\n        return 'noop';\n    }\n    async get(key) {\n        return null;\n    }\n    async set(key, value) { }\n    async remove(key) { }\n    async clear() { }\n    async length() {\n        return 0;\n    }\n    async keys() {\n        return [];\n    }\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    async forEach(iteratorCallback) { }\n    setEncryptionKey(key) { }\n}\nfunction provideStorage(platformId, storageConfig) {\n    if (isPlatformServer(platformId)) {\n        // When running in a server context return the NoopStorage\n        return new NoopStorage();\n    }\n    return new Storage(storageConfig);\n}\nclass IonicStorageModule {\n    static forRoot(storageConfig = null) {\n        return {\n            ngModule: IonicStorageModule,\n            providers: [\n                { provide: StorageConfigToken, useValue: storageConfig },\n                {\n                    provide: Storage,\n                    useFactory: provideStorage,\n                    deps: [PLATFORM_ID, StorageConfigToken],\n                },\n            ],\n        };\n    }\n}\nIonicStorageModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.1.2\", ngImport: i0, type: IonicStorageModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nIonicStorageModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.1.2\", ngImport: i0, type: IonicStorageModule });\nIonicStorageModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.1.2\", ngImport: i0, type: IonicStorageModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.1.2\", ngImport: i0, type: IonicStorageModule, decorators: [{\n            type: NgModule\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { IonicStorageModule, StorageConfigToken, provideStorage };\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,iBAAiB;AAClD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,eAAe;AACrE,SAASC,OAAO,QAAQ,gBAAgB;AACxC,SAASA,OAAO,QAAQ,gBAAgB;AAExC,MAAMC,kBAAkB,GAAG,IAAIJ,cAAc,CAAC,sBAAsB,CAAC;AACrE,MAAMK,WAAW,SAASF,OAAO,CAAC;EAC9BG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;EACX;EACMC,MAAMA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACX,OAAOD,KAAI;IAAC;EAChB;EACME,YAAYA,CAAA,EAAG;IAAA,OAAAD,iBAAA;EAAE;EACvB,IAAIE,MAAMA,CAAA,EAAG;IACT,OAAO,MAAM;EACjB;EACMC,GAAGA,CAACC,GAAG,EAAE;IAAA,OAAAJ,iBAAA;MACX,OAAO,IAAI;IAAC;EAChB;EACMK,GAAGA,CAACD,GAAG,EAAEE,KAAK,EAAE;IAAA,OAAAN,iBAAA;EAAE;EAClBO,MAAMA,CAACH,GAAG,EAAE;IAAA,OAAAJ,iBAAA;EAAE;EACdQ,KAAKA,CAAA,EAAG;IAAA,OAAAR,iBAAA;EAAE;EACVS,MAAMA,CAAA,EAAG;IAAA,OAAAT,iBAAA;MACX,OAAO,CAAC;IAAC;EACb;EACMU,IAAIA,CAAA,EAAG;IAAA,OAAAV,iBAAA;MACT,OAAO,EAAE;IAAC;EACd;EACA;EACMW,OAAOA,CAACC,gBAAgB,EAAE;IAAA,OAAAZ,iBAAA;EAAE;EAClCa,gBAAgBA,CAACT,GAAG,EAAE,CAAE;AAC5B;AACA,SAASU,cAAcA,CAACC,UAAU,EAAEC,aAAa,EAAE;EAC/C,IAAI3B,gBAAgB,CAAC0B,UAAU,CAAC,EAAE;IAC9B;IACA,OAAO,IAAInB,WAAW,CAAC,CAAC;EAC5B;EACA,OAAO,IAAIF,OAAO,CAACsB,aAAa,CAAC;AACrC;AACA,MAAMC,kBAAkB,CAAC;EACrB,OAAOC,OAAOA,CAACF,aAAa,GAAG,IAAI,EAAE;IACjC,OAAO;MACHG,QAAQ,EAAEF,kBAAkB;MAC5BG,SAAS,EAAE,CACP;QAAEC,OAAO,EAAE1B,kBAAkB;QAAE2B,QAAQ,EAAEN;MAAc,CAAC,EACxD;QACIK,OAAO,EAAE3B,OAAO;QAChB6B,UAAU,EAAET,cAAc;QAC1BU,IAAI,EAAE,CAAChC,WAAW,EAAEG,kBAAkB;MAC1C,CAAC;IAET,CAAC;EACL;AACJ;AACAsB,kBAAkB,CAACQ,IAAI,YAAAC,2BAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFV,kBAAkB;AAAA,CAAkD;AACnLA,kBAAkB,CAACW,IAAI,kBAD8EtC,EAAE,CAAAuC,gBAAA;EAAAC,IAAA,EACSb;AAAkB,EAAG;AACrIA,kBAAkB,CAACc,IAAI,kBAF8EzC,EAAE,CAAA0C,gBAAA,IAE8B;AACrI;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqG3C,EAAE,CAAA4C,iBAAA,CAGZjB,kBAAkB,EAAc,CAAC;IAChHa,IAAI,EAAErC;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASwB,kBAAkB,EAAEtB,kBAAkB,EAAEmB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}