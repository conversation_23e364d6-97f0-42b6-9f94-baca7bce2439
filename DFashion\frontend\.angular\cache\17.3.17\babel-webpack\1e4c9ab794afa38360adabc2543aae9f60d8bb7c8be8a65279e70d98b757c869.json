{"ast": null, "code": "import { __asyncValues, __awaiter } from \"tslib\";\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isPromise } from '../util/isPromise';\nimport { Observable } from '../Observable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isIterable } from '../util/isIterable';\nimport { isReadableStreamLike, readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nimport { isFunction } from '../util/isFunction';\nimport { reportUnhandledError } from '../util/reportUnhandledError';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport function innerFrom(input) {\n  if (input instanceof Observable) {\n    return input;\n  }\n  if (input != null) {\n    if (isInteropObservable(input)) {\n      return fromInteropObservable(input);\n    }\n    if (isArrayLike(input)) {\n      return fromArrayLike(input);\n    }\n    if (isPromise(input)) {\n      return fromPromise(input);\n    }\n    if (isAsyncIterable(input)) {\n      return fromAsyncIterable(input);\n    }\n    if (isIterable(input)) {\n      return fromIterable(input);\n    }\n    if (isReadableStreamLike(input)) {\n      return fromReadableStreamLike(input);\n    }\n  }\n  throw createInvalidObservableTypeError(input);\n}\nexport function fromInteropObservable(obj) {\n  return new Observable(subscriber => {\n    const obs = obj[Symbol_observable]();\n    if (isFunction(obs.subscribe)) {\n      return obs.subscribe(subscriber);\n    }\n    throw new TypeError('Provided object does not correctly implement Symbol.observable');\n  });\n}\nexport function fromArrayLike(array) {\n  return new Observable(subscriber => {\n    for (let i = 0; i < array.length && !subscriber.closed; i++) {\n      subscriber.next(array[i]);\n    }\n    subscriber.complete();\n  });\n}\nexport function fromPromise(promise) {\n  return new Observable(subscriber => {\n    promise.then(value => {\n      if (!subscriber.closed) {\n        subscriber.next(value);\n        subscriber.complete();\n      }\n    }, err => subscriber.error(err)).then(null, reportUnhandledError);\n  });\n}\nexport function fromIterable(iterable) {\n  return new Observable(subscriber => {\n    for (const value of iterable) {\n      subscriber.next(value);\n      if (subscriber.closed) {\n        return;\n      }\n    }\n    subscriber.complete();\n  });\n}\nexport function fromAsyncIterable(asyncIterable) {\n  return new Observable(subscriber => {\n    process(asyncIterable, subscriber).catch(err => subscriber.error(err));\n  });\n}\nexport function fromReadableStreamLike(readableStream) {\n  return fromAsyncIterable(readableStreamLikeToAsyncGenerator(readableStream));\n}\nfunction process(asyncIterable, subscriber) {\n  var asyncIterable_1, asyncIterable_1_1;\n  var e_1, _a;\n  return __awaiter(this, void 0, void 0, function* () {\n    try {\n      for (asyncIterable_1 = __asyncValues(asyncIterable); asyncIterable_1_1 = yield asyncIterable_1.next(), !asyncIterable_1_1.done;) {\n        const value = asyncIterable_1_1.value;\n        subscriber.next(value);\n        if (subscriber.closed) {\n          return;\n        }\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (asyncIterable_1_1 && !asyncIterable_1_1.done && (_a = asyncIterable_1.return)) yield _a.call(asyncIterable_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    subscriber.complete();\n  });\n}\n//# sourceMappingURL=innerFrom.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}