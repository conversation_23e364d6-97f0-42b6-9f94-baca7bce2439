{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/shop-data.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6];\nfunction TrendingNowComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_button_10_Template_button_click_0_listener() {\n      const filter_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.setActiveFilter(filter_r2.value));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r2.activeFilter === filter_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", filter_r2.label, \" \");\n  }\n}\nfunction TrendingNowComponent_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"div\", 20);\n    i0.ɵɵelementStart(2, \"div\", 21);\n    i0.ɵɵelement(3, \"div\", 22)(4, \"div\", 23)(5, \"div\", 22);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TrendingNowComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17);\n    i0.ɵɵtemplate(2, TrendingNowComponent_div_15_div_2_Template, 6, 0, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", product_r5.pricing.discountPercentage, \"% OFF \");\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_div_20_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 56);\n  }\n  if (rf & 2) {\n    const star_r6 = ctx.$implicit;\n    const product_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r6 <= product_r5.rating.average);\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53);\n    i0.ɵɵtemplate(2, TrendingNowComponent_div_16_div_3_div_20_i_2_Template, 1, 2, \"i\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getStarArray(product_r5.rating.average));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r5.rating.count, \")\");\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, product_r5.pricing.mrp, \"1.0-0\"), \" \");\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 58);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" In Stock (\", product_r5.availability.totalStock, \") \");\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 59);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Only \", product_r5.availability.totalStock, \" left \");\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 60);\n    i0.ɵɵtext(2, \" Out of Stock \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TrendingNowComponent_div_16_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_div_16_div_3_Template_div_click_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToProduct(product_r5));\n    })(\"keydown.enter\", function TrendingNowComponent_div_16_div_3_Template_div_keydown_enter_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToProduct(product_r5));\n    })(\"keydown.space\", function TrendingNowComponent_div_16_div_3_Template_div_keydown_space_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToProduct(product_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 29)(2, \"img\", 30);\n    i0.ɵɵlistener(\"error\", function TrendingNowComponent_div_16_div_3_Template_img_error_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onImageError($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵelement(4, \"i\", 5);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, TrendingNowComponent_div_16_div_3_div_7_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementStart(8, \"div\", 33)(9, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_div_16_div_3_Template_button_click_9_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.toggleWishlist(product_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(10, \"i\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_div_16_div_3_Template_button_click_11_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.quickView(product_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(12, \"i\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_div_16_div_3_Template_button_click_13_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.addToCart(product_r5);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(14, \"i\", 38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 39)(16, \"div\", 40);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"h3\", 41);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, TrendingNowComponent_div_16_div_3_div_20_Template, 5, 2, \"div\", 42);\n    i0.ɵɵelementStart(21, \"div\", 43)(22, \"span\", 44);\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, TrendingNowComponent_div_16_div_3_span_25_Template, 3, 4, \"span\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 46)(27, \"div\", 47);\n    i0.ɵɵelement(28, \"i\", 36);\n    i0.ɵɵelementStart(29, \"span\");\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 47);\n    i0.ɵɵelement(32, \"i\", 35);\n    i0.ɵɵelementStart(33, \"span\");\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 47);\n    i0.ɵɵelement(36, \"i\", 48);\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 49);\n    i0.ɵɵtemplate(40, TrendingNowComponent_div_16_div_3_span_40_Template, 3, 1, \"span\", 50)(41, TrendingNowComponent_div_16_div_3_span_41_Template, 3, 1, \"span\", 50)(42, TrendingNowComponent_div_16_div_3_span_42_Template, 3, 0, \"span\", 50);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-label\", \"View \" + product_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.getProductImage(product_r5), i0.ɵɵsanitizeUrl)(\"alt\", product_r5.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", product_r5.trendingScore, \"%\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.pricing.discountPercentage > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", \"Add to wishlist\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"wishlisted\", ctx_r2.isInWishlist(product_r5._id));\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Quick view\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", product_r5.availability.status !== \"in-stock\");\n    i0.ɵɵattribute(\"aria-label\", \"Add to cart\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r5.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r5.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.rating.count > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(24, 24, product_r5.pricing.sellingPrice, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r5.pricing.discountPercentage > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.formatNumber(product_r5.analytics.views));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.formatNumber(product_r5.analytics.likes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.formatNumber(product_r5.analytics.purchases));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(product_r5.availability.status);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.availability.status === \"in-stock\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.availability.status === \"low-stock\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.availability.status === \"out-of-stock\");\n  }\n}\nfunction TrendingNowComponent_div_16_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_div_16_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.scrollProducts(\"left\"));\n    });\n    i0.ɵɵelement(1, \"i\", 62);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canScrollLeft);\n  }\n}\nfunction TrendingNowComponent_div_16_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_div_16_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.scrollProducts(\"right\"));\n    });\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canScrollRight);\n  }\n}\nfunction TrendingNowComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 17, 0);\n    i0.ɵɵtemplate(3, TrendingNowComponent_div_16_div_3_Template, 43, 27, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TrendingNowComponent_div_16_button_4_Template, 2, 1, \"button\", 26)(5, TrendingNowComponent_div_16_button_5_Template, 2, 1, \"button\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.products)(\"ngForTrackBy\", ctx_r2.trackByProductId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showScrollButtons);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showScrollButtons);\n  }\n}\nfunction TrendingNowComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵelement(2, \"i\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No Trending Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Check back later for the hottest trends!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function TrendingNowComponent_div_17_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.loadProducts());\n    });\n    i0.ɵɵelement(8, \"i\", 69);\n    i0.ɵɵtext(9, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let TrendingNowComponent = /*#__PURE__*/(() => {\n  class TrendingNowComponent {\n    constructor(shopDataService, router) {\n      this.shopDataService = shopDataService;\n      this.router = router;\n      this.maxProducts = 12;\n      this.showHeader = true;\n      this.showScrollButtons = true;\n      this.products = [];\n      this.isLoading = true;\n      this.activeFilter = 'all';\n      this.wishlistItems = new Set();\n      this.canScrollLeft = false;\n      this.canScrollRight = true;\n      this.trendFilters = [{\n        label: 'All',\n        value: 'all'\n      }, {\n        label: 'Today',\n        value: 'today'\n      }, {\n        label: 'This Week',\n        value: 'week'\n      }, {\n        label: 'This Month',\n        value: 'month'\n      }];\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      this.loadProducts();\n      this.loadWishlist();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    loadProducts() {\n      this.isLoading = true;\n      this.shopDataService.loadTrendingProducts(this.maxProducts).pipe(takeUntil(this.destroy$)).subscribe({\n        next: products => {\n          this.products = products;\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading trending products:', error);\n          this.isLoading = false;\n        }\n      });\n    }\n    setActiveFilter(filter) {\n      this.activeFilter = filter;\n      // In a real implementation, this would filter the products\n      // For now, we'll just reload the products\n      this.loadProducts();\n    }\n    navigateToProduct(product) {\n      // Track product click\n      this.trackProductClick(product);\n      // Navigate to product detail page\n      this.router.navigate(['/product', product._id]);\n    }\n    viewAllTrending() {\n      this.router.navigate(['/shop/trending']);\n    }\n    toggleWishlist(product) {\n      if (this.wishlistItems.has(product._id)) {\n        this.wishlistItems.delete(product._id);\n      } else {\n        this.wishlistItems.add(product._id);\n      }\n      this.saveWishlist();\n    }\n    isInWishlist(productId) {\n      return this.wishlistItems.has(productId);\n    }\n    quickView(product) {\n      // Implement quick view modal\n      console.log('Quick view:', product);\n    }\n    addToCart(product) {\n      if (product.availability.status !== 'in-stock') {\n        return;\n      }\n      // Add to cart logic\n      console.log('Add to cart:', product);\n      // Show success message\n      this.showAddToCartSuccess(product);\n    }\n    scrollProducts(direction) {\n      const container = document.querySelector('.products-scroll');\n      if (!container) return;\n      const scrollAmount = 300;\n      const currentScroll = container.scrollLeft;\n      if (direction === 'left') {\n        container.scrollTo({\n          left: currentScroll - scrollAmount,\n          behavior: 'smooth'\n        });\n      } else {\n        container.scrollTo({\n          left: currentScroll + scrollAmount,\n          behavior: 'smooth'\n        });\n      }\n      // Update scroll button states\n      setTimeout(() => {\n        this.updateScrollButtons();\n      }, 300);\n    }\n    getProductImage(product) {\n      const primaryImage = product.images.find(img => img.isPrimary);\n      return primaryImage ? primaryImage.url : product.images[0]?.url || '';\n    }\n    getStarArray(rating) {\n      return Array(5).fill(0).map((_, i) => i + 1);\n    }\n    formatNumber(num) {\n      if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + 'M';\n      } else if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'K';\n      }\n      return num.toString();\n    }\n    onImageError(event) {\n      event.target.src = 'https://via.placeholder.com/300x400/f0f0f0/666?text=Product+Image';\n    }\n    trackByProductId(index, product) {\n      return product._id;\n    }\n    updateScrollButtons() {\n      const container = document.querySelector('.products-scroll');\n      if (!container) return;\n      this.canScrollLeft = container.scrollLeft > 0;\n      this.canScrollRight = container.scrollLeft < container.scrollWidth - container.clientWidth;\n    }\n    trackProductClick(product) {\n      if (typeof window.gtag !== 'undefined') {\n        window.gtag('event', 'product_click', {\n          product_name: product.name,\n          product_id: product._id,\n          product_brand: product.brand,\n          product_category: product.category,\n          event_category: 'engagement'\n        });\n      }\n    }\n    loadWishlist() {\n      const stored = localStorage.getItem('dfashion_wishlist');\n      if (stored) {\n        try {\n          const wishlist = JSON.parse(stored);\n          this.wishlistItems = new Set(wishlist);\n        } catch (e) {\n          console.warn('Failed to load wishlist');\n        }\n      }\n    }\n    saveWishlist() {\n      localStorage.setItem('dfashion_wishlist', JSON.stringify(Array.from(this.wishlistItems)));\n    }\n    showAddToCartSuccess(product) {\n      // Show a toast or notification\n      console.log(`${product.name} added to cart!`);\n      // In a real implementation, you might show a toast notification\n      // this.toastService.show(`${product.name} added to cart!`, 'success');\n    }\n    static {\n      this.ɵfac = function TrendingNowComponent_Factory(t) {\n        return new (t || TrendingNowComponent)(i0.ɵɵdirectiveInject(i1.ShopDataService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TrendingNowComponent,\n        selectors: [[\"app-trending-now\"]],\n        inputs: {\n          maxProducts: \"maxProducts\",\n          showHeader: \"showHeader\",\n          showScrollButtons: \"showScrollButtons\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 18,\n        vars: 4,\n        consts: [[\"productsScroll\", \"\"], [1, \"trending-now-section\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [1, \"fas\", \"fa-fire\"], [1, \"section-subtitle\"], [1, \"header-actions\"], [1, \"trend-filters\"], [\"class\", \"filter-btn\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"products-container\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"filter-btn\", 3, \"click\"], [1, \"loading-container\"], [1, \"products-scroll\"], [\"class\", \"product-card-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card-skeleton\"], [1, \"skeleton-image\"], [1, \"skeleton-content\"], [1, \"skeleton-text\"], [1, \"skeleton-text\", \"short\"], [1, \"products-container\"], [\"class\", \"product-card\", \"tabindex\", \"0\", 3, \"click\", \"keydown.enter\", \"keydown.space\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"scroll-btn scroll-left\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"scroll-btn scroll-right\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"tabindex\", \"0\", 1, \"product-card\", 3, \"click\", \"keydown.enter\", \"keydown.space\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"error\", \"src\", \"alt\"], [1, \"trending-badge\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"quick-actions\"], [1, \"quick-action-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"fas\", \"fa-eye\"], [1, \"quick-action-btn\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [\"class\", \"product-rating\", 4, \"ngIf\"], [1, \"product-pricing\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"trending-stats\"], [1, \"stat-item\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"stock-status\"], [4, \"ngIf\"], [1, \"discount-badge\"], [1, \"product-rating\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 3, \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"fas\", \"fa-star\"], [1, \"original-price\"], [1, \"fas\", \"fa-check-circle\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"fas\", \"fa-times-circle\"], [1, \"scroll-btn\", \"scroll-left\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"scroll-btn\", \"scroll-right\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"empty-state\"], [1, \"empty-icon\"], [1, \"fas\", \"fa-fire-extinguisher\"], [1, \"retry-btn\", 3, \"click\"], [1, \"fas\", \"fa-refresh\"]],\n        template: function TrendingNowComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"h2\", 4);\n            i0.ɵɵelement(4, \"i\", 5);\n            i0.ɵɵtext(5, \" Trending Now \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p\", 6);\n            i0.ɵɵtext(7, \"Hot picks that everyone's talking about\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n            i0.ɵɵtemplate(10, TrendingNowComponent_button_10_Template, 2, 3, \"button\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"button\", 10);\n            i0.ɵɵlistener(\"click\", function TrendingNowComponent_Template_button_click_11_listener() {\n              return ctx.viewAllTrending();\n            });\n            i0.ɵɵelementStart(12, \"span\");\n            i0.ɵɵtext(13, \"View All\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(14, \"i\", 11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(15, TrendingNowComponent_div_15_Template, 3, 2, \"div\", 12)(16, TrendingNowComponent_div_16_Template, 6, 4, \"div\", 13)(17, TrendingNowComponent_div_17_Template, 10, 0, \"div\", 14);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngForOf\", ctx.trendFilters);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.products.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.products.length === 0);\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.DecimalPipe],\n        styles: [\".trending-now-section[_ngcontent-%COMP%]{padding:2rem 0;background:#fff}.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:2rem;padding:0 1rem;gap:1rem}@media (max-width: 768px){.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}}.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#262626;margin:0 0 .5rem;display:flex;align-items:center;gap:.5rem}.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ff6b6b;font-size:1.8rem;animation:_ngcontent-%COMP%_pulse 2s infinite}@media (max-width: 768px){.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:1.5rem}}.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{color:#8e8e8e;font-size:1rem;margin:0}.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem}@media (max-width: 768px){.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{flex-direction:column;width:100%;align-items:stretch}}.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%]{display:flex;gap:.5rem;background:#f8f9fa;padding:.25rem;border-radius:25px}@media (max-width: 768px){.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%]{justify-content:center}}.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]{padding:.5rem 1rem;border:none;background:transparent;color:#666;border-radius:20px;cursor:pointer;transition:all .3s ease;font-weight:500;font-size:.85rem}.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]:hover{background:#667eea1a;color:#667eea}.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%]   .filter-btn.active[_ngcontent-%COMP%]{background:#667eea;color:#fff;box-shadow:0 2px 8px #667eea4d}.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1.5rem;background:linear-gradient(135deg,#ff6b6b,#ff8e53);color:#fff;border:none;border-radius:25px;font-weight:600;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 12px #ff6b6b4d;white-space:nowrap}.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 20px #ff6b6b66}.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{transition:transform .3s ease}.trending-now-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:translate(3px)}.trending-now-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{padding:0 1rem}.trending-now-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]{display:flex;gap:1rem;overflow-x:hidden}.trending-now-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]{min-width:280px;background:#f8f9fa;border-radius:16px;overflow:hidden}.trending-now-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-image[_ngcontent-%COMP%]{width:100%;height:300px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite}.trending-now-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]{padding:1rem}.trending-now-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-text[_ngcontent-%COMP%]{height:16px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;border-radius:4px;margin-bottom:.5rem}.trending-now-section[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]   .product-card-skeleton[_ngcontent-%COMP%]   .skeleton-content[_ngcontent-%COMP%]   .skeleton-text.short[_ngcontent-%COMP%]{width:60%}.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]{position:relative;padding:0 1rem}.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]{display:flex;gap:1rem;overflow-x:auto;scroll-behavior:smooth;padding-bottom:1rem}.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]::-webkit-scrollbar{height:6px}.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px}.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:3px}.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .products-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#a1a1a1}.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .scroll-btn[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);width:48px;height:48px;background:#fff;border:1px solid #e0e0e0;border-radius:50%;cursor:pointer;display:flex;align-items:center;justify-content:center;box-shadow:0 4px 12px #0000001a;transition:all .3s ease;z-index:2}.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .scroll-btn[_ngcontent-%COMP%]:hover{background:#667eea;color:#fff;border-color:#667eea}.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .scroll-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed;background:#f8f9fa}.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .scroll-btn.scroll-left[_ngcontent-%COMP%]{left:-24px}.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .scroll-btn.scroll-right[_ngcontent-%COMP%]{right:-24px}@media (max-width: 768px){.trending-now-section[_ngcontent-%COMP%]   .products-container[_ngcontent-%COMP%]   .scroll-btn[_ngcontent-%COMP%]{display:none}}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{min-width:280px;background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 2px 12px #0000000f;cursor:pointer;transition:all .3s ease;position:relative}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 12px 32px #00000026}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover   .quick-actions[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%]{transform:scale(1.05)}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]:focus{outline:3px solid #667eea;outline-offset:2px}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]{position:relative;height:300px;overflow:hidden;background:#f8f9fa}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]{position:absolute;top:12px;left:12px;background:linear-gradient(135deg,#ff6b6b,#ff8e53);color:#fff;padding:.5rem .75rem;border-radius:20px;font-size:.8rem;font-weight:600;display:flex;align-items:center;gap:.25rem;box-shadow:0 2px 8px #ff6b6b4d}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%]{position:absolute;top:12px;right:12px;background:#e91e63;color:#fff;padding:.25rem .5rem;border-radius:12px;font-size:.75rem;font-weight:600}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]{position:absolute;bottom:12px;right:12px;display:flex;flex-direction:column;gap:.5rem;opacity:0;transform:translateY(10px);transition:all .3s ease}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]{width:40px;height:40px;background:#ffffffe6;border:none;border-radius:50%;cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]:hover{background:#667eea;color:#fff;transform:scale(1.1)}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]   .fa-heart.wishlisted[_ngcontent-%COMP%]{color:#e91e63}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]{padding:1rem}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%]{color:#8e8e8e;font-size:.8rem;font-weight:500;text-transform:uppercase;letter-spacing:.5px;margin-bottom:.25rem}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{font-size:1rem;font-weight:600;color:#262626;margin:0 0 .5rem;line-height:1.3;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.5rem}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]{display:flex;gap:.125rem}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .fa-star[_ngcontent-%COMP%]{color:#ddd;font-size:.8rem}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .fa-star.filled[_ngcontent-%COMP%]{color:#ffc107}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%]{color:#8e8e8e;font-size:.8rem}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]{margin-bottom:.75rem}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700;color:#262626}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%]{font-size:.9rem;color:#8e8e8e;text-decoration:line-through;margin-left:.5rem}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:.75rem}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;font-size:.75rem;color:#666}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#ff6b6b;width:12px}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status[_ngcontent-%COMP%]{font-size:.8rem;font-weight:500;display:flex;align-items:center;gap:.25rem}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status.in-stock[_ngcontent-%COMP%]{color:#4caf50}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status.low-stock[_ngcontent-%COMP%]{color:#ff9800}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status.out-of-stock[_ngcontent-%COMP%]{color:#f44336}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .stock-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.7rem}.trending-now-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{text-align:center;padding:3rem 1rem;color:#8e8e8e}.trending-now-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:4rem;margin-bottom:1rem;color:#ddd}.trending-now-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 .5rem;color:#666}.trending-now-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 1.5rem}.trending-now-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:#ff6b6b;color:#fff;border:none;padding:.75rem 1.5rem;border-radius:8px;cursor:pointer;display:flex;align-items:center;gap:.5rem;margin:0 auto}@keyframes _ngcontent-%COMP%_loading{0%{background-position:200% 0}to{background-position:-200% 0}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1)}50%{transform:scale(1.1)}}@media (prefers-color-scheme: dark){.trending-now-section[_ngcontent-%COMP%]{background:#121212}.trending-now-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{color:#fff}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{background:#1e1e1e}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{color:#fff}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]{background:#2a2a2a}.trending-now-section[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]   .quick-action-btn[_ngcontent-%COMP%]{background:#1e1e1ee6;color:#fff}.trending-now-section[_ngcontent-%COMP%]   .scroll-btn[_ngcontent-%COMP%]{background:#1e1e1e;border-color:#333;color:#fff}.trending-now-section[_ngcontent-%COMP%]   .scroll-btn[_ngcontent-%COMP%]:hover{background:#667eea}.trending-now-section[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%]{background:#2a2a2a}.trending-now-section[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]{color:#fff}.trending-now-section[_ngcontent-%COMP%]   .trend-filters[_ngcontent-%COMP%]   .filter-btn[_ngcontent-%COMP%]:hover{background:#667eea33}}\"]\n      });\n    }\n  }\n  return TrendingNowComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}