.home-sidebar {
  width: 320px;
  padding: 0 16px;
  position: sticky;
  top: 84px;
  height: fit-content;
  max-height: calc(100vh - 100px);
  overflow-y: auto;

  @media (max-width: 1024px) {
    display: none;
  }

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #dbdbdb;
    border-radius: 2px;

    &:hover {
      background: #c7c7c7;
    }
  }
}

// Profile Card
.profile-card {
  display: flex;
  align-items: center;
  padding: 16px 0;
  margin-bottom: 24px;
  border-bottom: 1px solid #efefef;

  .profile-avatar {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 12px;
  }

  .profile-info {
    flex: 1;

    .profile-username {
      font-size: 14px;
      font-weight: 600;
      color: #262626;
      margin: 0 0 2px 0;
    }

    .profile-name {
      font-size: 14px;
      color: #8e8e8e;
    }
  }

  .switch-btn {
    background: none;
    border: none;
    color: #0095f6;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;

    &:hover {
      color: #00376b;
    }
  }
}

// Section Styles
.sidebar-section {
  margin-bottom: 32px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      font-size: 14px;
      font-weight: 600;
      color: #8e8e8e;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        font-size: 12px;
        color: #0095f6;
      }
    }

    .see-all-btn {
      background: none;
      border: none;
      color: #262626;
      font-size: 12px;
      font-weight: 400;
      cursor: pointer;
      transition: color 0.2s ease;

      &:hover {
        color: #0095f6;
      }
    }
  }
}

// Collection Items
.collection-items {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .collection-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #fafafa;
    }

    .collection-image {
      width: 44px;
      height: 44px;
      border-radius: 6px;
      object-fit: cover;
    }

    .collection-info {
      flex: 1;

      .collection-name {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .collection-price {
        font-size: 12px;
        color: #8e8e8e;
      }

      .collection-discount {
        font-size: 10px;
        color: #ed4956;
        font-weight: 600;
        margin-top: 2px;
      }
    }
  }
}

// Featured Grid
.featured-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;

  .featured-item {
    cursor: pointer;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-2px);
    }

    .featured-image {
      width: 100%;
      height: 120px;
      object-fit: cover;
    }

    .featured-info {
      padding: 8px;
      background: white;

      .featured-name {
        display: block;
        font-size: 12px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .featured-brand {
        display: block;
        font-size: 11px;
        color: #8e8e8e;
        margin-bottom: 4px;
      }

      .featured-price {
        .current-price {
          font-size: 12px;
          font-weight: 600;
          color: #262626;
        }

        .original-price {
          font-size: 10px;
          color: #8e8e8e;
          text-decoration: line-through;
          margin-left: 4px;
        }
      }
    }
  }
}

// Arrivals List
.arrivals-list {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .arrival-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #fafafa;
    }

    .arrival-image {
      width: 44px;
      height: 44px;
      border-radius: 6px;
      object-fit: cover;
    }

    .new-badge {
      position: absolute;
      top: 4px;
      left: 4px;
      background: #ed4956;
      color: white;
      font-size: 8px;
      font-weight: 600;
      padding: 2px 4px;
      border-radius: 3px;
    }

    .arrival-info {
      flex: 1;

      .arrival-name {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .arrival-brand {
        display: block;
        font-size: 12px;
        color: #8e8e8e;
        margin-bottom: 2px;
      }

      .arrival-price {
        font-size: 12px;
        font-weight: 600;
        color: #262626;
      }
    }
  }
}

// Categories Grid
.categories-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;

  .category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 8px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #efefef;

    &:hover {
      background-color: #fafafa;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .category-image {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
      margin-bottom: 8px;
      border: 2px solid #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .category-name {
      font-size: 12px;
      font-weight: 500;
      color: #262626;
      text-align: center;
    }

    .category-icon {
      width: 32px;
      height: 32px;
      border-radius: 6px;
      background: linear-gradient(45deg, #0095f6, #00d4ff);
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        color: white;
        font-size: 14px;
      }
    }

    .category-info {
      flex: 1;

      .category-name {
        display: block;
        font-size: 12px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 2px;
      }

      .category-count {
        font-size: 10px;
        color: #8e8e8e;
      }
    }

    .trending-indicator {
      position: absolute;
      top: 4px;
      right: 4px;

      i {
        color: #ed4956;
        font-size: 10px;
      }
    }
  }
}

// Footer
.sidebar-footer {
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #efefef;

  .footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;

    a {
      font-size: 11px;
      color: #c7c7c7;
      text-decoration: none;

      &:hover {
        color: #8e8e8e;
      }
    }
  }

  .copyright {
    font-size: 11px;
    color: #c7c7c7;
  }
}
