.home-sidebar {
  width: 340px;
  padding: 0;
  position: sticky;
  top: 84px;
  height: fit-content;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  overflow-x: visible;
  background: transparent;
  display: flex;
  flex-direction: column;
  gap: 20px;

  @media (max-width: 1200px) {
    width: 300px;
    gap: 16px;
  }

  @media (max-width: 1024px) {
    display: none;
  }

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.02);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 3px;

    &:hover {
      background: linear-gradient(135deg, #5a67d8, #6b46c1);
    }
  }

  // Ensure proper spacing and prevent content hiding
  > * {
    flex-shrink: 0;
  }
}

// Profile Card
.profile-card {
  display: flex;
  align-items: center;
  padding: 20px;
  margin-bottom: 24px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  gap: 12px;

  .profile-avatar-container {
    position: relative;

    .profile-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid rgba(102, 126, 234, 0.2);
      transition: all 0.3s ease;

      &:hover {
        border-color: #667eea;
        transform: scale(1.05);
      }
    }
  }

  .profile-info {
    flex: 1;
    min-width: 0;

    .profile-username {
      font-size: 16px;
      font-weight: 700;
      color: #2d3748;
      margin: 0 0 4px 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .profile-name {
      font-size: 14px;
      color: #718096;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .switch-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    color: white;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    white-space: nowrap;

    &:hover {
      background: linear-gradient(135deg, #5a67d8, #6b46c1);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    i {
      font-size: 10px;
    }
  }
}

// Section Styles
.sidebar-section {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.04),
    0 1px 4px rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(0, 0, 0, 0.04);
  overflow: visible;
  width: 100%;
  flex-shrink: 0;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 8px;

    h3 {
      font-size: 16px;
      font-weight: 700;
      color: #2d3748;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 10px;
      flex: 1;
      min-width: 0;

      i {
        font-size: 14px;
        color: #667eea;
        flex-shrink: 0;
      }
    }

    .view-all-btn {
      background: linear-gradient(135deg, #667eea, #764ba2);
      border: none;
      color: white;
      font-size: 12px;
      font-weight: 600;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 12px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 6px;
      white-space: nowrap;
      flex-shrink: 0;

      &:hover {
        background: linear-gradient(135deg, #5a67d8, #6b46c1);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }

      i {
        font-size: 10px;
        transition: transform 0.3s ease;
      }

      &:hover i {
        transform: translateX(2px);
      }
    }
  }

  // Component wrapper to ensure proper spacing
  .component-wrapper {
    width: 100%;
    overflow: visible;
  }
}

// Collection Items
.collection-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;

  .collection-item {
    display: flex;
    align-items: center;
    gap: 14px;
    padding: 12px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(102, 126, 234, 0.02);
    border: 1px solid rgba(102, 126, 234, 0.08);
    position: relative;
    overflow: visible;

    &:hover {
      background: rgba(102, 126, 234, 0.05);
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
    }

    .collection-image-container {
      position: relative;
      flex-shrink: 0;

      .collection-image {
        width: 56px;
        height: 56px;
        border-radius: 12px;
        object-fit: cover;
        border: 2px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
      }

      .collection-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(102, 126, 234, 0.8);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.3s ease;

        i {
          color: white;
          font-size: 16px;
        }
      }

      &:hover .collection-overlay {
        opacity: 1;
      }
    }

    .collection-info {
      flex: 1;
      min-width: 0;

      .collection-name {
        display: block;
        font-size: 15px;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1.2;
      }

      .collection-price {
        font-size: 14px;
        color: #667eea;
        font-weight: 600;
        margin-bottom: 4px;
      }

      .collection-discount {
        font-size: 11px;
        color: #e53e3e;
        font-weight: 700;
        background: rgba(229, 62, 62, 0.1);
        padding: 2px 6px;
        border-radius: 6px;
        display: inline-flex;
        align-items: center;
        gap: 4px;

        i {
          font-size: 9px;
        }
      }
    }
  }
}

// Featured Grid
.featured-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  width: 100%;

  .featured-item {
    cursor: pointer;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.04),
      0 1px 4px rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;

    &:hover {
      transform: translateY(-4px);
      box-shadow:
        0 8px 32px rgba(102, 126, 234, 0.15),
        0 4px 16px rgba(0, 0, 0, 0.08);
    }

    .featured-image-container {
      position: relative;
      overflow: hidden;

      .featured-image {
        width: 100%;
        height: 120px;
        object-fit: cover;
        transition: all 0.3s ease;
      }

      .featured-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(102, 126, 234, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.3s ease;

        i {
          color: white;
          font-size: 18px;
        }
      }

      &:hover .featured-overlay {
        opacity: 1;
      }

      &:hover .featured-image {
        transform: scale(1.05);
      }
    }

    .featured-info {
      padding: 12px;
      background: white;

      .featured-name {
        display: block;
        font-size: 13px;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1.2;
      }

      .featured-brand {
        display: block;
        font-size: 11px;
        color: #718096;
        margin-bottom: 6px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .featured-price {
        display: flex;
        align-items: center;
        gap: 6px;

        .current-price {
          font-size: 13px;
          font-weight: 700;
          color: #667eea;
        }

        .original-price {
          font-size: 11px;
          color: #a0aec0;
          text-decoration: line-through;
        }
      }
    }
  }

  @media (max-width: 1200px) {
    gap: 12px;

    .featured-item {
      .featured-image-container .featured-image {
        height: 100px;
      }

      .featured-info {
        padding: 10px;

        .featured-name {
          font-size: 12px;
        }

        .featured-price .current-price {
          font-size: 12px;
        }
      }
    }
  }
}

// Arrivals List
.arrivals-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;

  .arrival-item {
    display: flex;
    align-items: center;
    gap: 14px;
    padding: 12px;
    border-radius: 12px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    background: rgba(102, 126, 234, 0.02);
    border: 1px solid rgba(102, 126, 234, 0.08);
    overflow: visible;

    &:hover {
      background: rgba(102, 126, 234, 0.05);
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
    }

    .arrival-image {
      width: 56px;
      height: 56px;
      border-radius: 12px;
      object-fit: cover;
      border: 2px solid rgba(102, 126, 234, 0.1);
      transition: all 0.3s ease;
      flex-shrink: 0;
    }

    .new-badge {
      position: absolute;
      top: 8px;
      left: 8px;
      background: linear-gradient(135deg, #e53e3e, #c53030);
      color: white;
      font-size: 9px;
      font-weight: 700;
      padding: 3px 6px;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);
      z-index: 1;
    }

    .arrival-info {
      flex: 1;
      min-width: 0;

      .arrival-name {
        display: block;
        font-size: 15px;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1.2;
      }

      .arrival-brand {
        display: block;
        font-size: 12px;
        color: #718096;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .arrival-price {
        font-size: 14px;
        font-weight: 700;
        color: #667eea;
      }
    }
  }
}

// Categories Grid
.categories-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  width: 100%;

  .category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 12px;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.04),
      0 1px 4px rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(0, 0, 0, 0.04);

    &:hover {
      background: rgba(102, 126, 234, 0.02);
      transform: translateY(-4px);
      box-shadow:
        0 8px 32px rgba(102, 126, 234, 0.15),
        0 4px 16px rgba(0, 0, 0, 0.08);
    }

    .category-image {
      width: 64px;
      height: 64px;
      border-radius: 50%;
      object-fit: cover;
      margin-bottom: 12px;
      border: 3px solid rgba(102, 126, 234, 0.1);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
    }

    .category-name {
      font-size: 13px;
      font-weight: 600;
      color: #2d3748;
      text-align: center;
      line-height: 1.2;
    }

    .category-icon {
      width: 36px;
      height: 36px;
      border-radius: 8px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      transition: all 0.3s ease;

      i {
        color: white;
        font-size: 16px;
      }
    }

    .category-info {
      flex: 1;
      text-align: center;

      .category-name {
        display: block;
        font-size: 13px;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 4px;
        line-height: 1.2;
      }

      .category-count {
        font-size: 11px;
        color: #718096;
        font-weight: 500;
      }
    }

    .trending-indicator {
      position: absolute;
      top: 8px;
      right: 8px;

      i {
        color: #e53e3e;
        font-size: 12px;
      }
    }

    &:hover {
      .category-icon {
        background: linear-gradient(135deg, #5a67d8, #6b46c1);
        transform: scale(1.05);
      }

      .category-image {
        border-color: #667eea;
        transform: scale(1.05);
      }
    }
  }
}

// Footer
.sidebar-footer {
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #efefef;

  .footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;

    a {
      font-size: 11px;
      color: #c7c7c7;
      text-decoration: none;

      &:hover {
        color: #8e8e8e;
      }
    }
  }

  .copyright {
    font-size: 11px;
    color: #c7c7c7;
  }
}
