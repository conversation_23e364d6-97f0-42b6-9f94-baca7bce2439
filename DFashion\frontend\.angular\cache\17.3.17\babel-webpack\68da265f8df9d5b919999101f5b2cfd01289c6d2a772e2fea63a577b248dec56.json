{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nfunction PaymentFailedComponent_div_10_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"span\", 33);\n    i0.ɵɵtext(2, \"Order ID:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.orderId);\n  }\n}\nfunction PaymentFailedComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"h3\");\n    i0.ɵɵtext(2, \"Error Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 32)(4, \"span\", 33);\n    i0.ɵɵtext(5, \"Reason:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 34);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, PaymentFailedComponent_div_10_div_8_Template, 5, 1, \"div\", 35);\n    i0.ɵɵelementStart(9, \"div\", 32)(10, \"span\", 33);\n    i0.ɵɵtext(11, \"Time:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 34);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.errorReason);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.orderId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 3, ctx_r0.failureTime, \"medium\"));\n  }\n}\nexport let PaymentFailedComponent = /*#__PURE__*/(() => {\n  class PaymentFailedComponent {\n    constructor(route, router) {\n      this.route = route;\n      this.router = router;\n      this.orderId = '';\n      this.errorReason = '';\n      this.failureTime = new Date();\n    }\n    ngOnInit() {\n      // Get error details from query parameters\n      this.route.queryParams.subscribe(params => {\n        this.orderId = params['orderId'] || '';\n        this.errorReason = params['reason'] || 'Payment processing failed';\n      });\n    }\n    retryPayment() {\n      if (this.orderId) {\n        this.router.navigate(['/checkout'], {\n          queryParams: {\n            retry: this.orderId\n          }\n        });\n      } else {\n        this.router.navigate(['/checkout']);\n      }\n    }\n    goToCart() {\n      this.router.navigate(['/cart']);\n    }\n    continueShopping() {\n      this.router.navigate(['/']);\n    }\n    openChat() {\n      // Implement live chat functionality\n      alert('Live chat feature coming soon! Please use email or phone support for now.');\n    }\n    static {\n      this.ɵfac = function PaymentFailedComponent_Factory(t) {\n        return new (t || PaymentFailedComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PaymentFailedComponent,\n        selectors: [[\"app-payment-failed\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 74,\n        vars: 1,\n        consts: [[1, \"payment-failed-container\"], [1, \"failed-card\"], [1, \"failed-icon\"], [1, \"error-circle\"], [1, \"error-cross\"], [1, \"failed-content\"], [1, \"failed-message\"], [\"class\", \"error-details\", 4, \"ngIf\"], [1, \"support-info\"], [1, \"fas\", \"fa-info-circle\"], [1, \"action-buttons\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-redo\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"btn\", \"btn-outline\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"common-issues\"], [1, \"issue-item\"], [1, \"fas\", \"fa-credit-card\"], [1, \"fas\", \"fa-shield-alt\"], [1, \"fas\", \"fa-wifi\"], [1, \"fas\", \"fa-clock\"], [1, \"contact-support\"], [1, \"support-options\"], [\"href\", \"mailto:<EMAIL>\", 1, \"support-option\"], [1, \"fas\", \"fa-envelope\"], [\"href\", \"tel:+919876543210\", 1, \"support-option\"], [1, \"fas\", \"fa-phone\"], [1, \"support-option\", 3, \"click\"], [1, \"fas\", \"fa-comments\"], [1, \"error-details\"], [1, \"detail-row\"], [1, \"label\"], [1, \"value\"], [\"class\", \"detail-row\", 4, \"ngIf\"]],\n        template: function PaymentFailedComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"div\", 4);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 5)(6, \"h1\");\n            i0.ɵɵtext(7, \"Payment Failed\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p\", 6);\n            i0.ɵɵtext(9, \" We're sorry, but your payment could not be processed. Please try again or use a different payment method. \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(10, PaymentFailedComponent_div_10_Template, 15, 6, \"div\", 7);\n            i0.ɵɵelementStart(11, \"div\", 8);\n            i0.ɵɵelement(12, \"i\", 9);\n            i0.ɵɵelementStart(13, \"p\");\n            i0.ɵɵtext(14, \"If you continue to experience issues, please contact our support team.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 10)(16, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function PaymentFailedComponent_Template_button_click_16_listener() {\n              return ctx.retryPayment();\n            });\n            i0.ɵɵelement(17, \"i\", 12);\n            i0.ɵɵtext(18, \" Try Again \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function PaymentFailedComponent_Template_button_click_19_listener() {\n              return ctx.goToCart();\n            });\n            i0.ɵɵelement(20, \"i\", 14);\n            i0.ɵɵtext(21, \" Back to Cart \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function PaymentFailedComponent_Template_button_click_22_listener() {\n              return ctx.continueShopping();\n            });\n            i0.ɵɵelement(23, \"i\", 16);\n            i0.ɵɵtext(24, \" Continue Shopping \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"div\", 17)(26, \"h3\");\n            i0.ɵɵtext(27, \"Common Issues & Solutions\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"div\", 18);\n            i0.ɵɵelement(29, \"i\", 19);\n            i0.ɵɵelementStart(30, \"div\")(31, \"h4\");\n            i0.ɵɵtext(32, \"Insufficient Funds\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"p\");\n            i0.ɵɵtext(34, \"Please check your account balance or try a different card.\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(35, \"div\", 18);\n            i0.ɵɵelement(36, \"i\", 20);\n            i0.ɵɵelementStart(37, \"div\")(38, \"h4\");\n            i0.ɵɵtext(39, \"Security Check\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"p\");\n            i0.ɵɵtext(41, \"Your bank may have blocked the transaction. Please contact your bank.\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(42, \"div\", 18);\n            i0.ɵɵelement(43, \"i\", 21);\n            i0.ɵɵelementStart(44, \"div\")(45, \"h4\");\n            i0.ɵɵtext(46, \"Network Issues\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"p\");\n            i0.ɵɵtext(48, \"Check your internet connection and try again.\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(49, \"div\", 18);\n            i0.ɵɵelement(50, \"i\", 22);\n            i0.ɵɵelementStart(51, \"div\")(52, \"h4\");\n            i0.ɵɵtext(53, \"Session Timeout\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"p\");\n            i0.ɵɵtext(55, \"Your session may have expired. Please start the checkout process again.\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(56, \"div\", 23)(57, \"h3\");\n            i0.ɵɵtext(58, \"Need Help?\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"p\");\n            i0.ɵɵtext(60, \"Our customer support team is here to help you complete your purchase.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(61, \"div\", 24)(62, \"a\", 25);\n            i0.ɵɵelement(63, \"i\", 26);\n            i0.ɵɵelementStart(64, \"span\");\n            i0.ɵɵtext(65, \"Email Support\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(66, \"a\", 27);\n            i0.ɵɵelement(67, \"i\", 28);\n            i0.ɵɵelementStart(68, \"span\");\n            i0.ɵɵtext(69, \"Call Support\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(70, \"button\", 29);\n            i0.ɵɵlistener(\"click\", function PaymentFailedComponent_Template_button_click_70_listener() {\n              return ctx.openChat();\n            });\n            i0.ɵɵelement(71, \"i\", 30);\n            i0.ɵɵelementStart(72, \"span\");\n            i0.ɵɵtext(73, \"Live Chat\");\n            i0.ɵɵelementEnd()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngIf\", ctx.errorReason);\n          }\n        },\n        dependencies: [CommonModule, i2.NgIf, i2.DatePipe, RouterModule],\n        styles: [\".payment-failed-container[_ngcontent-%COMP%]{min-height:calc(100vh - 80px);background:linear-gradient(135deg,#f5576c,#f093fb);padding:40px 20px;display:flex;align-items:center;justify-content:center}.failed-card[_ngcontent-%COMP%]{background:#fff;border-radius:20px;padding:40px;max-width:600px;width:100%;box-shadow:0 20px 60px #0003;text-align:center}.failed-icon[_ngcontent-%COMP%]{margin-bottom:30px}.error-circle[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;background:linear-gradient(135deg,#f5576c,#f093fb);margin:0 auto;position:relative;animation:_ngcontent-%COMP%_scaleIn .6s ease-out}.error-cross[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:30px;height:30px}.error-cross[_ngcontent-%COMP%]:before, .error-cross[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:50%;left:50%;width:4px;height:30px;background:#fff;border-radius:2px}.error-cross[_ngcontent-%COMP%]:before{transform:translate(-50%,-50%) rotate(45deg);animation:_ngcontent-%COMP%_crossDraw1 .4s ease-out .3s both}.error-cross[_ngcontent-%COMP%]:after{transform:translate(-50%,-50%) rotate(-45deg);animation:_ngcontent-%COMP%_crossDraw2 .4s ease-out .5s both}@keyframes _ngcontent-%COMP%_scaleIn{0%{transform:scale(0);opacity:0}50%{transform:scale(1.1)}to{transform:scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_crossDraw1{0%{height:0}to{height:30px}}@keyframes _ngcontent-%COMP%_crossDraw2{0%{height:0}to{height:30px}}.failed-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:32px;font-weight:700;color:#2d3748;margin-bottom:16px;animation:_ngcontent-%COMP%_fadeInUp .6s ease-out .2s both}.failed-content[_ngcontent-%COMP%]   .failed-message[_ngcontent-%COMP%]{font-size:16px;color:#718096;line-height:1.6;margin-bottom:30px;animation:_ngcontent-%COMP%_fadeInUp .6s ease-out .4s both}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.error-details[_ngcontent-%COMP%]{background:#fef5e7;border:1px solid #f6ad55;border-radius:12px;padding:20px;margin-bottom:30px;text-align:left;animation:_ngcontent-%COMP%_fadeInUp .6s ease-out .6s both}.error-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#c05621;margin-bottom:16px;text-align:center}.error-details[_ngcontent-%COMP%]   .detail-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:8px 0;border-bottom:1px solid #fed7aa}.error-details[_ngcontent-%COMP%]   .detail-row[_ngcontent-%COMP%]:last-child{border-bottom:none}.error-details[_ngcontent-%COMP%]   .detail-row[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-weight:500;color:#9c4221}.error-details[_ngcontent-%COMP%]   .detail-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{font-weight:600;color:#c05621}.support-info[_ngcontent-%COMP%]{background:#e6fffa;border:1px solid #81e6d9;border-radius:8px;padding:16px;margin-bottom:30px;display:flex;align-items:center;gap:12px;animation:_ngcontent-%COMP%_fadeInUp .6s ease-out .8s both}.support-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#319795;font-size:20px}.support-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#2c7a7b;font-size:14px}.action-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;margin-bottom:40px;animation:_ngcontent-%COMP%_fadeInUp .6s ease-out 1s both}.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:14px 24px;border-radius:8px;font-size:16px;font-weight:600;cursor:pointer;transition:all .3s ease;display:flex;align-items:center;justify-content:center;gap:8px;text-decoration:none;border:none}.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}.action-buttons[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff}.action-buttons[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #667eea66}.action-buttons[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]{background:#4a5568;color:#fff}.action-buttons[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover{background:#2d3748;transform:translateY(-2px)}.action-buttons[_ngcontent-%COMP%]   .btn-outline[_ngcontent-%COMP%]{background:transparent;border:2px solid #f5576c;color:#f5576c}.action-buttons[_ngcontent-%COMP%]   .btn-outline[_ngcontent-%COMP%]:hover{background:#f5576c;color:#fff;transform:translateY(-2px)}.common-issues[_ngcontent-%COMP%]{text-align:left;margin-bottom:40px;animation:_ngcontent-%COMP%_fadeInUp .6s ease-out 1.2s both}.common-issues[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#2d3748;margin-bottom:20px;text-align:center}.common-issues[_ngcontent-%COMP%]   .issue-item[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:20px;padding:16px;background:#f8fafc;border-radius:8px}.common-issues[_ngcontent-%COMP%]   .issue-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#f5576c;font-size:20px;margin-top:4px;flex-shrink:0}.common-issues[_ngcontent-%COMP%]   .issue-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#2d3748;margin:0 0 8px}.common-issues[_ngcontent-%COMP%]   .issue-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:#718096;margin:0;line-height:1.5}.contact-support[_ngcontent-%COMP%]{text-align:center;animation:_ngcontent-%COMP%_fadeInUp .6s ease-out 1.4s both}.contact-support[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#2d3748;margin-bottom:12px}.contact-support[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:#718096;margin-bottom:20px}.contact-support[_ngcontent-%COMP%]   .support-options[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:16px;flex-wrap:wrap}.contact-support[_ngcontent-%COMP%]   .support-option[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:8px;padding:16px;background:#f8fafc;border:1px solid #e2e8f0;border-radius:8px;text-decoration:none;color:#4a5568;transition:all .3s ease;cursor:pointer;min-width:120px}.contact-support[_ngcontent-%COMP%]   .support-option[_ngcontent-%COMP%]:hover{background:#667eea;color:#fff;transform:translateY(-2px);box-shadow:0 4px 12px #667eea4d}.contact-support[_ngcontent-%COMP%]   .support-option[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:20px}.contact-support[_ngcontent-%COMP%]   .support-option[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px;font-weight:500}@media (max-width: 768px){.payment-failed-container[_ngcontent-%COMP%]{padding:20px 16px}.failed-card[_ngcontent-%COMP%]{padding:30px 20px}.failed-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:28px}.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:12px 20px;font-size:14px}.support-options[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.support-options[_ngcontent-%COMP%]   .support-option[_ngcontent-%COMP%]{width:100%;max-width:200px}}@media (max-width: 480px){.error-details[_ngcontent-%COMP%]   .detail-row[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:4px}.common-issues[_ngcontent-%COMP%]   .issue-item[_ngcontent-%COMP%]{flex-direction:column;gap:8px}}\"]\n      });\n    }\n  }\n  return PaymentFailedComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}