{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nfunction VendorDashboardComponent_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36)(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 37);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const activity_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(activity_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r1.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 4, activity_r1.timestamp, \"short\"));\n  }\n}\nexport class VendorDashboardComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.currentUser = null;\n    this.stats = {\n      totalProducts: 0,\n      totalOrders: 0,\n      totalRevenue: 0,\n      totalPosts: 0\n    };\n    this.recentActivity = [{\n      icon: 'fas fa-plus text-success',\n      message: 'New product \"Summer Dress\" was added',\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)\n    }, {\n      icon: 'fas fa-shopping-cart text-primary',\n      message: 'Order #12345 was placed',\n      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000)\n    }, {\n      icon: 'fas fa-camera text-info',\n      message: 'New post was published',\n      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000)\n    }];\n  }\n  ngOnInit() {\n    this.loadUserData();\n    this.loadStats();\n  }\n  loadUserData() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n  loadStats() {\n    // Load stats from API\n    this.stats = {\n      totalProducts: 0,\n      totalOrders: 0,\n      totalRevenue: 0,\n      totalPosts: 0\n    };\n  }\n  static {\n    this.ɵfac = function VendorDashboardComponent_Factory(t) {\n      return new (t || VendorDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VendorDashboardComponent,\n      selectors: [[\"app-vendor-dashboard\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 105,\n      vars: 9,\n      consts: [[1, \"vendor-dashboard\"], [1, \"dashboard-header\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-icon\"], [1, \"fas\", \"fa-box\"], [1, \"stat-content\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"fas\", \"fa-rupee-sign\"], [1, \"fas\", \"fa-images\"], [1, \"quick-actions\"], [1, \"actions-grid\"], [\"routerLink\", \"/vendor/products/create\", 1, \"action-card\"], [1, \"action-icon\"], [1, \"fas\", \"fa-plus\"], [1, \"action-content\"], [\"routerLink\", \"/vendor/posts/create\", 1, \"action-card\"], [1, \"fas\", \"fa-camera\"], [\"routerLink\", \"/vendor/stories/create\", 1, \"action-card\"], [1, \"fas\", \"fa-video\"], [\"routerLink\", \"/vendor/orders\", 1, \"action-card\"], [1, \"fas\", \"fa-list\"], [1, \"recent-activity\"], [1, \"activity-list\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"vendor-menu\"], [1, \"menu-grid\"], [\"routerLink\", \"/vendor/products\", 1, \"menu-item\"], [\"routerLink\", \"/vendor/posts\", 1, \"menu-item\"], [\"routerLink\", \"/vendor/stories\", 1, \"menu-item\"], [1, \"fas\", \"fa-play-circle\"], [\"routerLink\", \"/vendor/orders\", 1, \"menu-item\"], [\"routerLink\", \"/vendor/analytics\", 1, \"menu-item\"], [1, \"fas\", \"fa-chart-bar\"], [1, \"activity-item\"], [1, \"activity-icon\"], [1, \"activity-content\"], [1, \"activity-time\"]],\n      template: function VendorDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"Vendor Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\");\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 2)(7, \"div\", 3)(8, \"div\", 4);\n          i0.ɵɵelement(9, \"i\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 6)(11, \"h3\");\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"p\");\n          i0.ɵɵtext(14, \"Total Products\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 3)(16, \"div\", 4);\n          i0.ɵɵelement(17, \"i\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 6)(19, \"h3\");\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p\");\n          i0.ɵɵtext(22, \"Total Orders\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 3)(24, \"div\", 4);\n          i0.ɵɵelement(25, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 6)(27, \"h3\");\n          i0.ɵɵtext(28);\n          i0.ɵɵpipe(29, \"number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"p\");\n          i0.ɵɵtext(31, \"Total Revenue\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"div\", 3)(33, \"div\", 4);\n          i0.ɵɵelement(34, \"i\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 6)(36, \"h3\");\n          i0.ɵɵtext(37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"p\");\n          i0.ɵɵtext(39, \"Posts & Stories\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(40, \"div\", 10)(41, \"h2\");\n          i0.ɵɵtext(42, \"Quick Actions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 11)(44, \"a\", 12)(45, \"div\", 13);\n          i0.ɵɵelement(46, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 15)(48, \"h3\");\n          i0.ɵɵtext(49, \"Add Product\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"p\");\n          i0.ɵɵtext(51, \"Create a new product listing\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(52, \"a\", 16)(53, \"div\", 13);\n          i0.ɵɵelement(54, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"div\", 15)(56, \"h3\");\n          i0.ɵɵtext(57, \"Create Post\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"p\");\n          i0.ɵɵtext(59, \"Share a new product post\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(60, \"a\", 18)(61, \"div\", 13);\n          i0.ɵɵelement(62, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 15)(64, \"h3\");\n          i0.ɵɵtext(65, \"Add Story\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"p\");\n          i0.ɵɵtext(67, \"Create a product story\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(68, \"a\", 20)(69, \"div\", 13);\n          i0.ɵɵelement(70, \"i\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"div\", 15)(72, \"h3\");\n          i0.ɵɵtext(73, \"View Orders\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"p\");\n          i0.ɵɵtext(75, \"Manage your orders\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(76, \"div\", 22)(77, \"h2\");\n          i0.ɵɵtext(78, \"Recent Activity\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 23);\n          i0.ɵɵtemplate(80, VendorDashboardComponent_div_80_Template, 9, 7, \"div\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(81, \"div\", 25)(82, \"h2\");\n          i0.ɵɵtext(83, \"Vendor Tools\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"div\", 26)(85, \"a\", 27);\n          i0.ɵɵelement(86, \"i\", 5);\n          i0.ɵɵelementStart(87, \"span\");\n          i0.ɵɵtext(88, \"My Products\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(89, \"a\", 28);\n          i0.ɵɵelement(90, \"i\", 9);\n          i0.ɵɵelementStart(91, \"span\");\n          i0.ɵɵtext(92, \"My Posts\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(93, \"a\", 29);\n          i0.ɵɵelement(94, \"i\", 30);\n          i0.ɵɵelementStart(95, \"span\");\n          i0.ɵɵtext(96, \"My Stories\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(97, \"a\", 31);\n          i0.ɵɵelement(98, \"i\", 7);\n          i0.ɵɵelementStart(99, \"span\");\n          i0.ɵɵtext(100, \"Orders\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(101, \"a\", 32);\n          i0.ɵɵelement(102, \"i\", 33);\n          i0.ɵɵelementStart(103, \"span\");\n          i0.ɵɵtext(104, \"Analytics\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"Welcome back, \", ctx.currentUser == null ? null : ctx.currentUser.fullName, \"!\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.stats.totalProducts);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.stats.totalOrders);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(29, 6, ctx.stats.totalRevenue, \"1.0-0\"), \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.stats.totalPosts);\n          i0.ɵɵadvance(43);\n          i0.ɵɵproperty(\"ngForOf\", ctx.recentActivity);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.DecimalPipe, i2.DatePipe, RouterModule, i3.RouterLink],\n      styles: [\".vendor-dashboard[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 600;\\n  margin-bottom: 8px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1.1rem;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 40px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #eee;\\n  border-radius: 8px;\\n  padding: 24px;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  background: #007bff;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 1.2rem;\\n}\\n\\n.stat-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n\\n.stat-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.quick-actions[_ngcontent-%COMP%], .recent-activity[_ngcontent-%COMP%], .vendor-menu[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n\\n.quick-actions[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .recent-activity[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .vendor-menu[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  font-weight: 600;\\n  margin-bottom: 20px;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 20px;\\n}\\n\\n.action-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #eee;\\n  border-radius: 8px;\\n  padding: 24px;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  text-decoration: none;\\n  color: inherit;\\n  transition: all 0.2s;\\n}\\n\\n.action-card[_ngcontent-%COMP%]:hover {\\n  border-color: #007bff;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);\\n}\\n\\n.action-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #007bff;\\n  font-size: 1.2rem;\\n}\\n\\n.action-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n\\n.action-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.activity-list[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #eee;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 16px 20px;\\n  border-bottom: 1px solid #f5f5f5;\\n}\\n\\n.activity-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.activity-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  background: #f8f9fa;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #007bff;\\n}\\n\\n.activity-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n  font-weight: 500;\\n}\\n\\n.activity-time[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.85rem;\\n}\\n\\n.menu-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 16px;\\n}\\n\\n.menu-item[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #eee;\\n  border-radius: 8px;\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 12px;\\n  text-decoration: none;\\n  color: inherit;\\n  transition: all 0.2s;\\n}\\n\\n.menu-item[_ngcontent-%COMP%]:hover {\\n  border-color: #007bff;\\n  background: #f8f9ff;\\n}\\n\\n.menu-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: #007bff;\\n}\\n\\n.menu-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n@media (max-width: 768px) {\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .actions-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .menu-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵclassMap", "activity_r1", "icon", "ɵɵtextInterpolate", "message", "ɵɵpipeBind2", "timestamp", "VendorDashboardComponent", "constructor", "authService", "currentUser", "stats", "totalProducts", "totalOrders", "totalRevenue", "totalPosts", "recentActivity", "Date", "now", "ngOnInit", "loadUserData", "loadStats", "currentUser$", "subscribe", "user", "ɵɵdirectiveInject", "i1", "AuthService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "VendorDashboardComponent_Template", "rf", "ctx", "ɵɵtemplate", "VendorDashboardComponent_div_80_Template", "ɵɵtextInterpolate1", "fullName", "ɵɵproperty", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DecimalPipe", "DatePipe", "i3", "RouterLink", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\features\\vendor\\pages\\dashboard\\vendor-dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { AuthService } from '../../../../core/services/auth.service';\n\n@Component({\n  selector: 'app-vendor-dashboard',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  template: `\n    <div class=\"vendor-dashboard\">\n      <div class=\"dashboard-header\">\n        <h1>Vendor Dashboard</h1>\n        <p>Welcome back, {{ currentUser?.fullName }}!</p>\n      </div>\n\n      <!-- Quick Stats -->\n      <div class=\"stats-grid\">\n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">\n            <i class=\"fas fa-box\"></i>\n          </div>\n          <div class=\"stat-content\">\n            <h3>{{ stats.totalProducts }}</h3>\n            <p>Total Products</p>\n          </div>\n        </div>\n\n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">\n            <i class=\"fas fa-shopping-cart\"></i>\n          </div>\n          <div class=\"stat-content\">\n            <h3>{{ stats.totalOrders }}</h3>\n            <p>Total Orders</p>\n          </div>\n        </div>\n\n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">\n            <i class=\"fas fa-rupee-sign\"></i>\n          </div>\n          <div class=\"stat-content\">\n            <h3>₹{{ stats.totalRevenue | number:'1.0-0' }}</h3>\n            <p>Total Revenue</p>\n          </div>\n        </div>\n\n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">\n            <i class=\"fas fa-images\"></i>\n          </div>\n          <div class=\"stat-content\">\n            <h3>{{ stats.totalPosts }}</h3>\n            <p>Posts & Stories</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Quick Actions -->\n      <div class=\"quick-actions\">\n        <h2>Quick Actions</h2>\n        <div class=\"actions-grid\">\n          <a routerLink=\"/vendor/products/create\" class=\"action-card\">\n            <div class=\"action-icon\">\n              <i class=\"fas fa-plus\"></i>\n            </div>\n            <div class=\"action-content\">\n              <h3>Add Product</h3>\n              <p>Create a new product listing</p>\n            </div>\n          </a>\n\n          <a routerLink=\"/vendor/posts/create\" class=\"action-card\">\n            <div class=\"action-icon\">\n              <i class=\"fas fa-camera\"></i>\n            </div>\n            <div class=\"action-content\">\n              <h3>Create Post</h3>\n              <p>Share a new product post</p>\n            </div>\n          </a>\n\n          <a routerLink=\"/vendor/stories/create\" class=\"action-card\">\n            <div class=\"action-icon\">\n              <i class=\"fas fa-video\"></i>\n            </div>\n            <div class=\"action-content\">\n              <h3>Add Story</h3>\n              <p>Create a product story</p>\n            </div>\n          </a>\n\n          <a routerLink=\"/vendor/orders\" class=\"action-card\">\n            <div class=\"action-icon\">\n              <i class=\"fas fa-list\"></i>\n            </div>\n            <div class=\"action-content\">\n              <h3>View Orders</h3>\n              <p>Manage your orders</p>\n            </div>\n          </a>\n        </div>\n      </div>\n\n      <!-- Recent Activity -->\n      <div class=\"recent-activity\">\n        <h2>Recent Activity</h2>\n        <div class=\"activity-list\">\n          <div class=\"activity-item\" *ngFor=\"let activity of recentActivity\">\n            <div class=\"activity-icon\">\n              <i [class]=\"activity.icon\"></i>\n            </div>\n            <div class=\"activity-content\">\n              <p>{{ activity.message }}</p>\n              <span class=\"activity-time\">{{ activity.timestamp | date:'short' }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Navigation Menu -->\n      <div class=\"vendor-menu\">\n        <h2>Vendor Tools</h2>\n        <div class=\"menu-grid\">\n          <a routerLink=\"/vendor/products\" class=\"menu-item\">\n            <i class=\"fas fa-box\"></i>\n            <span>My Products</span>\n          </a>\n          <a routerLink=\"/vendor/posts\" class=\"menu-item\">\n            <i class=\"fas fa-images\"></i>\n            <span>My Posts</span>\n          </a>\n          <a routerLink=\"/vendor/stories\" class=\"menu-item\">\n            <i class=\"fas fa-play-circle\"></i>\n            <span>My Stories</span>\n          </a>\n          <a routerLink=\"/vendor/orders\" class=\"menu-item\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            <span>Orders</span>\n          </a>\n          <a routerLink=\"/vendor/analytics\" class=\"menu-item\">\n            <i class=\"fas fa-chart-bar\"></i>\n            <span>Analytics</span>\n          </a>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .vendor-dashboard {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 20px;\n    }\n\n    .dashboard-header {\n      margin-bottom: 30px;\n    }\n\n    .dashboard-header h1 {\n      font-size: 2rem;\n      font-weight: 600;\n      margin-bottom: 8px;\n    }\n\n    .dashboard-header p {\n      color: #666;\n      font-size: 1.1rem;\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 20px;\n      margin-bottom: 40px;\n    }\n\n    .stat-card {\n      background: white;\n      border: 1px solid #eee;\n      border-radius: 8px;\n      padding: 24px;\n      display: flex;\n      align-items: center;\n      gap: 16px;\n    }\n\n    .stat-icon {\n      width: 50px;\n      height: 50px;\n      background: #007bff;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: white;\n      font-size: 1.2rem;\n    }\n\n    .stat-content h3 {\n      font-size: 1.8rem;\n      font-weight: 600;\n      margin-bottom: 4px;\n    }\n\n    .stat-content p {\n      color: #666;\n      font-size: 0.9rem;\n    }\n\n    .quick-actions, .recent-activity, .vendor-menu {\n      margin-bottom: 40px;\n    }\n\n    .quick-actions h2, .recent-activity h2, .vendor-menu h2 {\n      font-size: 1.4rem;\n      font-weight: 600;\n      margin-bottom: 20px;\n    }\n\n    .actions-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 20px;\n    }\n\n    .action-card {\n      background: white;\n      border: 1px solid #eee;\n      border-radius: 8px;\n      padding: 24px;\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      text-decoration: none;\n      color: inherit;\n      transition: all 0.2s;\n    }\n\n    .action-card:hover {\n      border-color: #007bff;\n      transform: translateY(-2px);\n      box-shadow: 0 4px 12px rgba(0,123,255,0.15);\n    }\n\n    .action-icon {\n      width: 50px;\n      height: 50px;\n      background: #f8f9fa;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #007bff;\n      font-size: 1.2rem;\n    }\n\n    .action-content h3 {\n      font-size: 1.1rem;\n      font-weight: 600;\n      margin-bottom: 4px;\n    }\n\n    .action-content p {\n      color: #666;\n      font-size: 0.9rem;\n    }\n\n    .activity-list {\n      background: white;\n      border: 1px solid #eee;\n      border-radius: 8px;\n      overflow: hidden;\n    }\n\n    .activity-item {\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      padding: 16px 20px;\n      border-bottom: 1px solid #f5f5f5;\n    }\n\n    .activity-item:last-child {\n      border-bottom: none;\n    }\n\n    .activity-icon {\n      width: 40px;\n      height: 40px;\n      background: #f8f9fa;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #007bff;\n    }\n\n    .activity-content p {\n      margin-bottom: 4px;\n      font-weight: 500;\n    }\n\n    .activity-time {\n      color: #666;\n      font-size: 0.85rem;\n    }\n\n    .menu-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 16px;\n    }\n\n    .menu-item {\n      background: white;\n      border: 1px solid #eee;\n      border-radius: 8px;\n      padding: 20px;\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 12px;\n      text-decoration: none;\n      color: inherit;\n      transition: all 0.2s;\n    }\n\n    .menu-item:hover {\n      border-color: #007bff;\n      background: #f8f9ff;\n    }\n\n    .menu-item i {\n      font-size: 1.5rem;\n      color: #007bff;\n    }\n\n    .menu-item span {\n      font-weight: 500;\n    }\n\n    @media (max-width: 768px) {\n      .stats-grid {\n        grid-template-columns: 1fr;\n      }\n\n      .actions-grid {\n        grid-template-columns: 1fr;\n      }\n\n      .menu-grid {\n        grid-template-columns: repeat(2, 1fr);\n      }\n    }\n  `]\n})\nexport class VendorDashboardComponent implements OnInit {\n  currentUser: any = null;\n  stats = {\n    totalProducts: 0,\n    totalOrders: 0,\n    totalRevenue: 0,\n    totalPosts: 0\n  };\n\n  recentActivity = [\n    {\n      icon: 'fas fa-plus text-success',\n      message: 'New product \"Summer Dress\" was added',\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)\n    },\n    {\n      icon: 'fas fa-shopping-cart text-primary',\n      message: 'Order #12345 was placed',\n      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000)\n    },\n    {\n      icon: 'fas fa-camera text-info',\n      message: 'New post was published',\n      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000)\n    }\n  ];\n\n  constructor(private authService: AuthService) {}\n\n  ngOnInit() {\n    this.loadUserData();\n    this.loadStats();\n  }\n\n  loadUserData() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  loadStats() {\n    // Load stats from API\n    this.stats = {\n      totalProducts: 0,\n      totalOrders: 0,\n      totalRevenue: 0,\n      totalPosts: 0\n    };\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;;;;;;IA4GlCC,EADF,CAAAC,cAAA,cAAmE,cACtC;IACzBD,EAAA,CAAAE,SAAA,QAA+B;IACjCF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA8B,QACzB;IAAAD,EAAA,CAAAI,MAAA,GAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAC7BH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,GAAuC;;IAEvEJ,EAFuE,CAAAG,YAAA,EAAO,EACtE,EACF;;;;IANCH,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,UAAA,CAAAC,WAAA,CAAAC,IAAA,CAAuB;IAGvBR,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAS,iBAAA,CAAAF,WAAA,CAAAG,OAAA,CAAsB;IACGV,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAW,WAAA,OAAAJ,WAAA,CAAAK,SAAA,WAAuC;;;AAmPjF,OAAM,MAAOC,wBAAwB;EA2BnCC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IA1B/B,KAAAC,WAAW,GAAQ,IAAI;IACvB,KAAAC,KAAK,GAAG;MACNC,aAAa,EAAE,CAAC;MAChBC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE;KACb;IAED,KAAAC,cAAc,GAAG,CACf;MACEd,IAAI,EAAE,0BAA0B;MAChCE,OAAO,EAAE,sCAAsC;MAC/CE,SAAS,EAAE,IAAIW,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;KACpD,EACD;MACEhB,IAAI,EAAE,mCAAmC;MACzCE,OAAO,EAAE,yBAAyB;MAClCE,SAAS,EAAE,IAAIW,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;KACpD,EACD;MACEhB,IAAI,EAAE,yBAAyB;MAC/BE,OAAO,EAAE,wBAAwB;MACjCE,SAAS,EAAE,IAAIW,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;KACpD,CACF;EAE8C;EAE/CC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAD,YAAYA,CAAA;IACV,IAAI,CAACX,WAAW,CAACa,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACd,WAAW,GAAGc,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAH,SAASA,CAAA;IACP;IACA,IAAI,CAACV,KAAK,GAAG;MACXC,aAAa,EAAE,CAAC;MAChBC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE;KACb;EACH;;;uBAhDWR,wBAAwB,EAAAb,EAAA,CAAA+B,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAxBpB,wBAAwB;MAAAqB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApC,EAAA,CAAAqC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1V7B3C,EAFJ,CAAAC,cAAA,aAA8B,aACE,SACxB;UAAAD,EAAA,CAAAI,MAAA,uBAAgB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAI,MAAA,GAA0C;UAC/CJ,EAD+C,CAAAG,YAAA,EAAI,EAC7C;UAKFH,EAFJ,CAAAC,cAAA,aAAwB,aACC,aACE;UACrBD,EAAA,CAAAE,SAAA,WAA0B;UAC5BF,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,cAA0B,UACpB;UAAAD,EAAA,CAAAI,MAAA,IAAyB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,sBAAc;UAErBJ,EAFqB,CAAAG,YAAA,EAAI,EACjB,EACF;UAGJH,EADF,CAAAC,cAAA,cAAuB,cACE;UACrBD,EAAA,CAAAE,SAAA,YAAoC;UACtCF,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,cAA0B,UACpB;UAAAD,EAAA,CAAAI,MAAA,IAAuB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,oBAAY;UAEnBJ,EAFmB,CAAAG,YAAA,EAAI,EACf,EACF;UAGJH,EADF,CAAAC,cAAA,cAAuB,cACE;UACrBD,EAAA,CAAAE,SAAA,YAAiC;UACnCF,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,cAA0B,UACpB;UAAAD,EAAA,CAAAI,MAAA,IAA0C;;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,qBAAa;UAEpBJ,EAFoB,CAAAG,YAAA,EAAI,EAChB,EACF;UAGJH,EADF,CAAAC,cAAA,cAAuB,cACE;UACrBD,EAAA,CAAAE,SAAA,YAA6B;UAC/BF,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,cAA0B,UACpB;UAAAD,EAAA,CAAAI,MAAA,IAAsB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAC/BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,uBAAe;UAGxBJ,EAHwB,CAAAG,YAAA,EAAI,EAClB,EACF,EACF;UAIJH,EADF,CAAAC,cAAA,eAA2B,UACrB;UAAAD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAGlBH,EAFJ,CAAAC,cAAA,eAA0B,aACoC,eACjC;UACvBD,EAAA,CAAAE,SAAA,aAA2B;UAC7BF,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA4B,UACtB;UAAAD,EAAA,CAAAI,MAAA,mBAAW;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,oCAA4B;UAEnCJ,EAFmC,CAAAG,YAAA,EAAI,EAC/B,EACJ;UAGFH,EADF,CAAAC,cAAA,aAAyD,eAC9B;UACvBD,EAAA,CAAAE,SAAA,aAA6B;UAC/BF,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA4B,UACtB;UAAAD,EAAA,CAAAI,MAAA,mBAAW;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,gCAAwB;UAE/BJ,EAF+B,CAAAG,YAAA,EAAI,EAC3B,EACJ;UAGFH,EADF,CAAAC,cAAA,aAA2D,eAChC;UACvBD,EAAA,CAAAE,SAAA,aAA4B;UAC9BF,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA4B,UACtB;UAAAD,EAAA,CAAAI,MAAA,iBAAS;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,8BAAsB;UAE7BJ,EAF6B,CAAAG,YAAA,EAAI,EACzB,EACJ;UAGFH,EADF,CAAAC,cAAA,aAAmD,eACxB;UACvBD,EAAA,CAAAE,SAAA,aAA2B;UAC7BF,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAA4B,UACtB;UAAAD,EAAA,CAAAI,MAAA,mBAAW;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,0BAAkB;UAI7BJ,EAJ6B,CAAAG,YAAA,EAAI,EACrB,EACJ,EACA,EACF;UAIJH,EADF,CAAAC,cAAA,eAA6B,UACvB;UAAAD,EAAA,CAAAI,MAAA,uBAAe;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAA6C,UAAA,KAAAC,wCAAA,kBAAmE;UAUvE9C,EADE,CAAAG,YAAA,EAAM,EACF;UAIJH,EADF,CAAAC,cAAA,eAAyB,UACnB;UAAAD,EAAA,CAAAI,MAAA,oBAAY;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAEnBH,EADF,CAAAC,cAAA,eAAuB,aAC8B;UACjDD,EAAA,CAAAE,SAAA,YAA0B;UAC1BF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAI,MAAA,mBAAW;UACnBJ,EADmB,CAAAG,YAAA,EAAO,EACtB;UACJH,EAAA,CAAAC,cAAA,aAAgD;UAC9CD,EAAA,CAAAE,SAAA,YAA6B;UAC7BF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAChBJ,EADgB,CAAAG,YAAA,EAAO,EACnB;UACJH,EAAA,CAAAC,cAAA,aAAkD;UAChDD,EAAA,CAAAE,SAAA,aAAkC;UAClCF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UAClBJ,EADkB,CAAAG,YAAA,EAAO,EACrB;UACJH,EAAA,CAAAC,cAAA,aAAiD;UAC/CD,EAAA,CAAAE,SAAA,YAAoC;UACpCF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAI,MAAA,eAAM;UACdJ,EADc,CAAAG,YAAA,EAAO,EACjB;UACJH,EAAA,CAAAC,cAAA,cAAoD;UAClDD,EAAA,CAAAE,SAAA,cAAgC;UAChCF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAI,MAAA,kBAAS;UAIvBJ,EAJuB,CAAAG,YAAA,EAAO,EACpB,EACA,EACF,EACF;;;UAtICH,EAAA,CAAAK,SAAA,GAA0C;UAA1CL,EAAA,CAAA+C,kBAAA,mBAAAH,GAAA,CAAA5B,WAAA,kBAAA4B,GAAA,CAAA5B,WAAA,CAAAgC,QAAA,MAA0C;UAUrChD,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAS,iBAAA,CAAAmC,GAAA,CAAA3B,KAAA,CAAAC,aAAA,CAAyB;UAUzBlB,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAS,iBAAA,CAAAmC,GAAA,CAAA3B,KAAA,CAAAE,WAAA,CAAuB;UAUvBnB,EAAA,CAAAK,SAAA,GAA0C;UAA1CL,EAAA,CAAA+C,kBAAA,WAAA/C,EAAA,CAAAW,WAAA,QAAAiC,GAAA,CAAA3B,KAAA,CAAAG,YAAA,eAA0C;UAU1CpB,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAS,iBAAA,CAAAmC,GAAA,CAAA3B,KAAA,CAAAI,UAAA,CAAsB;UAwDoBrB,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAiD,UAAA,YAAAL,GAAA,CAAAtB,cAAA,CAAiB;;;qBArG/DxB,YAAY,EAAAoD,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,WAAA,EAAAF,EAAA,CAAAG,QAAA,EAAEtD,YAAY,EAAAuD,EAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}