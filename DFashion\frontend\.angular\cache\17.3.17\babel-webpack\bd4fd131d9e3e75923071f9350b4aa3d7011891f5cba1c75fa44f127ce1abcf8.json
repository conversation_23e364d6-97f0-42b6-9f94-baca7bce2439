{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"@angular/common\";\nfunction SidebarComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"div\", 12)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_4_Template_button_click_7_listener() {\n      const user_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.followUser(user_r2.id));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", user_r2.avatar, i0.ɵɵsanitizeUrl)(\"alt\", user_r2.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r2.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r2.followedBy);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", user_r2.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_8_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind1(2, 1, product_r5.originalPrice), \" \");\n  }\n}\nfunction SidebarComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"div\", 15)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵtemplate(8, SidebarComponent_div_8_span_8_Template, 3, 3, \"span\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 17)(10, \"span\", 18);\n    i0.ɵɵtext(11, \"\\uD83D\\uDD25 Trending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 19);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_8_Template_button_click_15_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.quickBuy(product_r5._id));\n    });\n    i0.ɵɵtext(16, \" Quick Buy \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r5.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r5.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(7, 6, product_r5.price), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r5.originalPrice);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(14, 8, product_r5.analytics.views), \"k views\");\n  }\n}\nfunction SidebarComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"div\", 23)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 24)(8, \"span\", 25);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 26);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_12_Template_button_click_12_listener() {\n      const influencer_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.followInfluencer(influencer_r7.id));\n    });\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const influencer_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", influencer_r7.avatar, i0.ɵɵsanitizeUrl)(\"alt\", influencer_r7.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(influencer_r7.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.formatFollowerCount(influencer_r7.followersCount), \" followers\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r7.postsCount, \" posts\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r7.engagement, \"% engagement\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", influencer_r7.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_17_Template_div_click_0_listener() {\n      const category_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.browseCategory(category_r9.slug));\n    });\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", category_r9.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r9.name);\n  }\n}\nexport class SidebarComponent {\n  constructor(productService) {\n    this.productService = productService;\n    this.suggestedUsers = [];\n    this.trendingProducts = [];\n    this.topInfluencers = [];\n    this.categories = [];\n  }\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n  loadSuggestedUsers() {\n    // Load from API - empty for now\n    this.suggestedUsers = [];\n  }\n  loadTrendingProducts() {\n    // Load from API - empty for now\n    this.trendingProducts = [];\n  }\n  loadTopInfluencers() {\n    // Mock data for demo\n    this.topInfluencers = [{\n      id: '1',\n      username: 'style_guru_raj',\n      fullName: 'Raj Patel',\n      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n      followersCount: 5600,\n      postsCount: 45,\n      engagement: 98,\n      isFollowing: false\n    }, {\n      id: '2',\n      username: 'ethnic_elegance',\n      fullName: 'Anita Desai',\n      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',\n      followersCount: 8900,\n      postsCount: 67,\n      engagement: 95,\n      isFollowing: false\n    }, {\n      id: '3',\n      username: 'fashionista_maya',\n      fullName: 'Maya Sharma',\n      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',\n      followersCount: 1200,\n      postsCount: 12,\n      engagement: 92,\n      isFollowing: false\n    }];\n  }\n  loadCategories() {\n    this.categories = [{\n      name: 'Women',\n      slug: 'women',\n      image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n    }, {\n      name: 'Men',\n      slug: 'men',\n      image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n    }, {\n      name: 'Kids',\n      slug: 'children',\n      image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n    }, {\n      name: 'Ethnic',\n      slug: 'ethnic',\n      image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n    }];\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n  followUser(userId) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n  followInfluencer(influencerId) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n  quickBuy(productId) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n  browseCategory(categorySlug) {\n    console.log('Browse category:', categorySlug);\n    // TODO: Navigate to category page\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.ProductService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 18,\n      vars: 4,\n      consts: [[1, \"sidebar\"], [1, \"suggestions\"], [\"class\", \"suggestion-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"trending\"], [\"class\", \"trending-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"influencers\"], [\"class\", \"influencer-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"categories\"], [1, \"category-grid\"], [\"class\", \"category-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"suggestion-item\"], [3, \"src\", \"alt\"], [1, \"suggestion-info\"], [1, \"follow-btn\", 3, \"click\"], [1, \"trending-item\"], [1, \"trending-info\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"trending-stats\"], [1, \"trending-badge\"], [1, \"views\"], [1, \"quick-buy-btn\", 3, \"click\"], [1, \"original-price\"], [1, \"influencer-item\"], [1, \"influencer-info\"], [1, \"influencer-stats\"], [1, \"posts-count\"], [1, \"engagement\"], [1, \"category-item\", 3, \"click\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"aside\", 0)(1, \"div\", 1)(2, \"h3\");\n          i0.ɵɵtext(3, \"Suggested for you\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, SidebarComponent_div_4_Template, 9, 5, \"div\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"h3\");\n          i0.ɵɵtext(7, \"Trending Products\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, SidebarComponent_div_8_Template, 17, 10, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 5)(10, \"h3\");\n          i0.ɵɵtext(11, \"Top Fashion Influencers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, SidebarComponent_div_12_Template, 14, 7, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 7)(14, \"h3\");\n          i0.ɵɵtext(15, \"Shop by Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 8);\n          i0.ɵɵtemplate(17, SidebarComponent_div_17_Template, 4, 3, \"div\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.suggestedUsers);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.trendingProducts);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.topInfluencers);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe],\n      styles: [\".sidebar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 80px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%], .trending[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  padding: 16px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .trending[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  color: #8e8e8e;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 12px;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%], .influencer-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 2px;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n  margin-bottom: 4px;\\n}\\n\\n.influencer-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.influencer-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #8e8e8e;\\n}\\n\\n.follow-btn[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: #fff;\\n  border: none;\\n  padding: 6px 16px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n}\\n\\n.follow-btn[_ngcontent-%COMP%]:hover {\\n  background: #0084d6;\\n}\\n\\n.trending-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid #f1f5f9;\\n}\\n\\n.trending-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n  margin-bottom: 0;\\n  padding-bottom: 0;\\n}\\n\\n.trending-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n}\\n\\n.trending-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.trending-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n\\n.trending-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--primary-color);\\n  margin-bottom: 8px;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n  color: #8e8e8e;\\n  font-weight: 400;\\n  margin-left: 4px;\\n}\\n\\n.trending-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.trending-badge[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 600;\\n  background: #fef3c7;\\n  color: #92400e;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.views[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #8e8e8e;\\n}\\n\\n.quick-buy-btn[_ngcontent-%COMP%] {\\n  background: #f1f5f9;\\n  color: #64748b;\\n  border: 1px solid #e2e8f0;\\n  padding: 6px 12px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n  align-self: flex-start;\\n}\\n\\n.quick-buy-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  color: #fff;\\n  border-color: var(--primary-color);\\n}\\n\\n.category-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 12px;\\n}\\n\\n.category-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 12px;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover {\\n  background: #f8fafc;\\n  border-color: var(--primary-color);\\n}\\n\\n.category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n  margin-bottom: 8px;\\n}\\n\\n.category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 500;\\n  text-align: center;\\n}\\n\\n@media (max-width: 1024px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    order: -1;\\n    position: static;\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n    gap: 20px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "SidebarComponent_div_4_Template_button_click_7_listener", "user_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "followUser", "id", "ɵɵadvance", "ɵɵproperty", "avatar", "ɵɵsanitizeUrl", "fullName", "ɵɵtextInterpolate", "username", "<PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1", "isFollowing", "ɵɵpipeBind1", "product_r5", "originalPrice", "ɵɵtemplate", "SidebarComponent_div_8_span_8_Template", "SidebarComponent_div_8_Template_button_click_15_listener", "_r4", "quickBuy", "_id", "images", "url", "name", "price", "analytics", "views", "SidebarComponent_div_12_Template_button_click_12_listener", "influencer_r7", "_r6", "followInfluencer", "formatFollowerCount", "followersCount", "postsCount", "engagement", "SidebarComponent_div_17_Template_div_click_0_listener", "category_r9", "_r8", "browseCategory", "slug", "image", "SidebarComponent", "constructor", "productService", "suggestedUsers", "trendingProducts", "topInfluencers", "categories", "ngOnInit", "loadSuggestedUsers", "loadTrendingProducts", "loadTopInfluencers", "loadCategories", "count", "toFixed", "toString", "userId", "user", "find", "u", "influencerId", "influencer", "i", "productId", "console", "log", "categorySlug", "ɵɵdirectiveInject", "i1", "ProductService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "SidebarComponent_div_4_Template", "SidebarComponent_div_8_Template", "SidebarComponent_div_12_Template", "SidebarComponent_div_17_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { ProductService } from '../../../../core/services/product.service';\nimport { Product } from '../../../../core/models/product.model';\n\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <aside class=\"sidebar\">\n      <!-- Suggested Users -->\n      <div class=\"suggestions\">\n        <h3>Suggested for you</h3>\n        <div *ngFor=\"let user of suggestedUsers\" class=\"suggestion-item\">\n          <img [src]=\"user.avatar\" [alt]=\"user.fullName\">\n          <div class=\"suggestion-info\">\n            <h5>{{ user.username }}</h5>\n            <p>{{ user.followedBy }}</p>\n          </div>\n          <button class=\"follow-btn\" (click)=\"followUser(user.id)\">\n            {{ user.isFollowing ? 'Following' : 'Follow' }}\n          </button>\n        </div>\n      </div>\n\n      <!-- Trending Products -->\n      <div class=\"trending\">\n        <h3>Trending Products</h3>\n        <div *ngFor=\"let product of trendingProducts\" class=\"trending-item\">\n          <img [src]=\"product.images[0].url\" [alt]=\"product.name\">\n          <div class=\"trending-info\">\n            <h5>{{ product.name }}</h5>\n            <p>₹{{ product.price | number }} \n              <span class=\"original-price\" *ngIf=\"product.originalPrice\">\n                ₹{{ product.originalPrice | number }}\n              </span>\n            </p>\n            <div class=\"trending-stats\">\n              <span class=\"trending-badge\">🔥 Trending</span>\n              <span class=\"views\">{{ product.analytics.views | number }}k views</span>\n            </div>\n          </div>\n          <button class=\"quick-buy-btn\" (click)=\"quickBuy(product._id)\">\n            Quick Buy\n          </button>\n        </div>\n      </div>\n\n      <!-- Top Fashion Influencers -->\n      <div class=\"influencers\">\n        <h3>Top Fashion Influencers</h3>\n        <div *ngFor=\"let influencer of topInfluencers\" class=\"influencer-item\">\n          <img [src]=\"influencer.avatar\" [alt]=\"influencer.fullName\">\n          <div class=\"influencer-info\">\n            <h5>{{ influencer.username }}</h5>\n            <p>{{ formatFollowerCount(influencer.followersCount) }} followers</p>\n            <div class=\"influencer-stats\">\n              <span class=\"posts-count\">{{ influencer.postsCount }} posts</span>\n              <span class=\"engagement\">{{ influencer.engagement }}% engagement</span>\n            </div>\n          </div>\n          <button class=\"follow-btn\" (click)=\"followInfluencer(influencer.id)\">\n            {{ influencer.isFollowing ? 'Following' : 'Follow' }}\n          </button>\n        </div>\n      </div>\n\n      <!-- Shop by Category -->\n      <div class=\"categories\">\n        <h3>Shop by Category</h3>\n        <div class=\"category-grid\">\n          <div *ngFor=\"let category of categories\" class=\"category-item\" (click)=\"browseCategory(category.slug)\">\n            <img [src]=\"category.image\" [alt]=\"category.name\">\n            <span>{{ category.name }}</span>\n          </div>\n        </div>\n      </div>\n    </aside>\n  `,\n  styles: [`\n    .sidebar {\n      position: sticky;\n      top: 80px;\n      height: fit-content;\n      display: flex;\n      flex-direction: column;\n      gap: 24px;\n    }\n\n    .suggestions,\n    .trending,\n    .influencers,\n    .categories {\n      background: #fff;\n      border: 1px solid #dbdbdb;\n      border-radius: 8px;\n      padding: 16px;\n    }\n\n    .suggestions h3,\n    .trending h3,\n    .influencers h3,\n    .categories h3 {\n      font-size: 16px;\n      font-weight: 600;\n      margin-bottom: 16px;\n      color: #8e8e8e;\n    }\n\n    .suggestion-item,\n    .influencer-item {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      margin-bottom: 12px;\n    }\n\n    .suggestion-item img,\n    .influencer-item img {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      object-fit: cover;\n    }\n\n    .suggestion-info,\n    .influencer-info {\n      flex: 1;\n    }\n\n    .suggestion-info h5,\n    .influencer-info h5 {\n      font-size: 14px;\n      font-weight: 600;\n      margin-bottom: 2px;\n    }\n\n    .suggestion-info p {\n      font-size: 12px;\n      color: #8e8e8e;\n    }\n\n    .influencer-info p {\n      font-size: 12px;\n      color: #8e8e8e;\n      margin-bottom: 4px;\n    }\n\n    .influencer-stats {\n      display: flex;\n      gap: 8px;\n    }\n\n    .influencer-stats span {\n      font-size: 11px;\n      color: #8e8e8e;\n    }\n\n    .follow-btn {\n      background: var(--primary-color);\n      color: #fff;\n      border: none;\n      padding: 6px 16px;\n      border-radius: 4px;\n      font-size: 12px;\n      font-weight: 600;\n      cursor: pointer;\n      transition: all 0.2s;\n    }\n\n    .follow-btn:hover {\n      background: #0084d6;\n    }\n\n    .trending-item {\n      display: flex;\n      align-items: flex-start;\n      gap: 12px;\n      margin-bottom: 16px;\n      padding-bottom: 16px;\n      border-bottom: 1px solid #f1f5f9;\n    }\n\n    .trending-item:last-child {\n      border-bottom: none;\n      margin-bottom: 0;\n      padding-bottom: 0;\n    }\n\n    .trending-item img {\n      width: 60px;\n      height: 60px;\n      border-radius: 8px;\n      object-fit: cover;\n    }\n\n    .trending-info {\n      flex: 1;\n    }\n\n    .trending-info h5 {\n      font-size: 14px;\n      font-weight: 600;\n      margin-bottom: 4px;\n    }\n\n    .trending-info p {\n      font-size: 14px;\n      font-weight: 600;\n      color: var(--primary-color);\n      margin-bottom: 8px;\n    }\n\n    .original-price {\n      text-decoration: line-through;\n      color: #8e8e8e;\n      font-weight: 400;\n      margin-left: 4px;\n    }\n\n    .trending-stats {\n      display: flex;\n      flex-direction: column;\n      gap: 4px;\n    }\n\n    .trending-badge {\n      font-size: 10px;\n      padding: 2px 6px;\n      border-radius: 4px;\n      font-weight: 600;\n      background: #fef3c7;\n      color: #92400e;\n      width: fit-content;\n    }\n\n    .views {\n      font-size: 11px;\n      color: #8e8e8e;\n    }\n\n    .quick-buy-btn {\n      background: #f1f5f9;\n      color: #64748b;\n      border: 1px solid #e2e8f0;\n      padding: 6px 12px;\n      border-radius: 4px;\n      font-size: 12px;\n      font-weight: 600;\n      cursor: pointer;\n      transition: all 0.2s;\n      align-self: flex-start;\n    }\n\n    .quick-buy-btn:hover {\n      background: var(--primary-color);\n      color: #fff;\n      border-color: var(--primary-color);\n    }\n\n    .category-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 12px;\n    }\n\n    .category-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      padding: 12px;\n      border: 1px solid #e2e8f0;\n      border-radius: 8px;\n      cursor: pointer;\n      transition: all 0.2s;\n    }\n\n    .category-item:hover {\n      background: #f8fafc;\n      border-color: var(--primary-color);\n    }\n\n    .category-item img {\n      width: 40px;\n      height: 40px;\n      border-radius: 8px;\n      object-fit: cover;\n      margin-bottom: 8px;\n    }\n\n    .category-item span {\n      font-size: 12px;\n      font-weight: 500;\n      text-align: center;\n    }\n\n    @media (max-width: 1024px) {\n      .sidebar {\n        order: -1;\n        position: static;\n        display: grid;\n        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n        gap: 20px;\n      }\n    }\n\n    @media (max-width: 768px) {\n      .sidebar {\n        grid-template-columns: 1fr;\n      }\n    }\n  `]\n})\nexport class SidebarComponent implements OnInit {\n  suggestedUsers: any[] = [];\n  trendingProducts: Product[] = [];\n  topInfluencers: any[] = [];\n  categories: any[] = [];\n\n  constructor(private productService: ProductService) {}\n\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n\n  loadSuggestedUsers() {\n    // Load from API - empty for now\n    this.suggestedUsers = [];\n  }\n\n  loadTrendingProducts() {\n    // Load from API - empty for now\n    this.trendingProducts = [];\n  }\n\n  loadTopInfluencers() {\n    // Mock data for demo\n    this.topInfluencers = [\n      {\n        id: '1',\n        username: 'style_guru_raj',\n        fullName: 'Raj Patel',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n        followersCount: 5600,\n        postsCount: 45,\n        engagement: 98,\n        isFollowing: false\n      },\n      {\n        id: '2',\n        username: 'ethnic_elegance',\n        fullName: 'Anita Desai',\n        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',\n        followersCount: 8900,\n        postsCount: 67,\n        engagement: 95,\n        isFollowing: false\n      },\n      {\n        id: '3',\n        username: 'fashionista_maya',\n        fullName: 'Maya Sharma',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',\n        followersCount: 1200,\n        postsCount: 12,\n        engagement: 92,\n        isFollowing: false\n      }\n    ];\n  }\n\n  loadCategories() {\n    this.categories = [\n      {\n        name: 'Women',\n        slug: 'women',\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n      },\n      {\n        name: 'Men',\n        slug: 'men',\n        image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n      },\n      {\n        name: 'Kids',\n        slug: 'children',\n        image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n      },\n      {\n        name: 'Ethnic',\n        slug: 'ethnic',\n        image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n      }\n    ];\n  }\n\n  formatFollowerCount(count: number): string {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n\n  followUser(userId: string) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n\n  followInfluencer(influencerId: string) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n\n  quickBuy(productId: string) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n\n  browseCategory(categorySlug: string) {\n    console.log('Browse category:', categorySlug);\n    // TODO: Navigate to category page\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;;;IActCC,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,SAAA,cAA+C;IAE7CF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAqB;IAC1BH,EAD0B,CAAAI,YAAA,EAAI,EACxB;IACNJ,EAAA,CAAAC,cAAA,iBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAC,wDAAA;MAAA,MAAAC,OAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAP,OAAA,CAAAQ,EAAA,CAAmB;IAAA,EAAC;IACtDf,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;;IARCJ,EAAA,CAAAgB,SAAA,EAAmB;IAAChB,EAApB,CAAAiB,UAAA,QAAAV,OAAA,CAAAW,MAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAmB,QAAAZ,OAAA,CAAAa,QAAA,CAAsB;IAExCpB,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAqB,iBAAA,CAAAd,OAAA,CAAAe,QAAA,CAAmB;IACpBtB,EAAA,CAAAgB,SAAA,GAAqB;IAArBhB,EAAA,CAAAqB,iBAAA,CAAAd,OAAA,CAAAgB,UAAA,CAAqB;IAGxBvB,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAwB,kBAAA,MAAAjB,OAAA,CAAAkB,WAAA,+BACF;;;;;IAYIzB,EAAA,CAAAC,cAAA,eAA2D;IACzDD,EAAA,CAAAG,MAAA,GACF;;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IADLJ,EAAA,CAAAgB,SAAA,EACF;IADEhB,EAAA,CAAAwB,kBAAA,YAAAxB,EAAA,CAAA0B,WAAA,OAAAC,UAAA,CAAAC,aAAA,OACF;;;;;;IAPN5B,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAE,SAAA,cAAwD;IAEtDF,EADF,CAAAC,cAAA,cAA2B,SACrB;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GACD;;IAAAH,EAAA,CAAA6B,UAAA,IAAAC,sCAAA,mBAA2D;IAG7D9B,EAAA,CAAAI,YAAA,EAAI;IAEFJ,EADF,CAAAC,cAAA,cAA4B,gBACG;IAAAD,EAAA,CAAAG,MAAA,6BAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC/CJ,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAG,MAAA,IAA6C;;IAErEH,EAFqE,CAAAI,YAAA,EAAO,EACpE,EACF;IACNJ,EAAA,CAAAC,cAAA,kBAA8D;IAAhCD,EAAA,CAAAK,UAAA,mBAAA0B,yDAAA;MAAA,MAAAJ,UAAA,GAAA3B,EAAA,CAAAQ,aAAA,CAAAwB,GAAA,EAAAtB,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAsB,QAAA,CAAAN,UAAA,CAAAO,GAAA,CAAqB;IAAA,EAAC;IAC3DlC,EAAA,CAAAG,MAAA,mBACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;;IAhBCJ,EAAA,CAAAgB,SAAA,EAA6B;IAAChB,EAA9B,CAAAiB,UAAA,QAAAU,UAAA,CAAAQ,MAAA,IAAAC,GAAA,EAAApC,EAAA,CAAAmB,aAAA,CAA6B,QAAAQ,UAAA,CAAAU,IAAA,CAAqB;IAEjDrC,EAAA,CAAAgB,SAAA,GAAkB;IAAlBhB,EAAA,CAAAqB,iBAAA,CAAAM,UAAA,CAAAU,IAAA,CAAkB;IACnBrC,EAAA,CAAAgB,SAAA,GACD;IADChB,EAAA,CAAAwB,kBAAA,WAAAxB,EAAA,CAAA0B,WAAA,OAAAC,UAAA,CAAAW,KAAA,OACD;IAA8BtC,EAAA,CAAAgB,SAAA,GAA2B;IAA3BhB,EAAA,CAAAiB,UAAA,SAAAU,UAAA,CAAAC,aAAA,CAA2B;IAMrC5B,EAAA,CAAAgB,SAAA,GAA6C;IAA7ChB,EAAA,CAAAwB,kBAAA,KAAAxB,EAAA,CAAA0B,WAAA,QAAAC,UAAA,CAAAY,SAAA,CAAAC,KAAA,aAA6C;;;;;;IAYvExC,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAE,SAAA,cAA2D;IAEzDF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAA8D;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEnEJ,EADF,CAAAC,cAAA,cAA8B,eACF;IAAAD,EAAA,CAAAG,MAAA,GAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAClEJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAuC;IAEpEH,EAFoE,CAAAI,YAAA,EAAO,EACnE,EACF;IACNJ,EAAA,CAAAC,cAAA,kBAAqE;IAA1CD,EAAA,CAAAK,UAAA,mBAAAoC,0DAAA;MAAA,MAAAC,aAAA,GAAA1C,EAAA,CAAAQ,aAAA,CAAAmC,GAAA,EAAAjC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAiC,gBAAA,CAAAF,aAAA,CAAA3B,EAAA,CAA+B;IAAA,EAAC;IAClEf,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;;;IAZCJ,EAAA,CAAAgB,SAAA,EAAyB;IAAChB,EAA1B,CAAAiB,UAAA,QAAAyB,aAAA,CAAAxB,MAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAyB,QAAAuB,aAAA,CAAAtB,QAAA,CAA4B;IAEpDpB,EAAA,CAAAgB,SAAA,GAAyB;IAAzBhB,EAAA,CAAAqB,iBAAA,CAAAqB,aAAA,CAAApB,QAAA,CAAyB;IAC1BtB,EAAA,CAAAgB,SAAA,GAA8D;IAA9DhB,EAAA,CAAAwB,kBAAA,KAAAb,MAAA,CAAAkC,mBAAA,CAAAH,aAAA,CAAAI,cAAA,gBAA8D;IAErC9C,EAAA,CAAAgB,SAAA,GAAiC;IAAjChB,EAAA,CAAAwB,kBAAA,KAAAkB,aAAA,CAAAK,UAAA,WAAiC;IAClC/C,EAAA,CAAAgB,SAAA,GAAuC;IAAvChB,EAAA,CAAAwB,kBAAA,KAAAkB,aAAA,CAAAM,UAAA,iBAAuC;IAIlEhD,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAwB,kBAAA,MAAAkB,aAAA,CAAAjB,WAAA,+BACF;;;;;;IAQAzB,EAAA,CAAAC,cAAA,cAAuG;IAAxCD,EAAA,CAAAK,UAAA,mBAAA4C,sDAAA;MAAA,MAAAC,WAAA,GAAAlD,EAAA,CAAAQ,aAAA,CAAA2C,GAAA,EAAAzC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAyC,cAAA,CAAAF,WAAA,CAAAG,IAAA,CAA6B;IAAA,EAAC;IACpGrD,EAAA,CAAAE,SAAA,cAAkD;IAClDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAC3BH,EAD2B,CAAAI,YAAA,EAAO,EAC5B;;;;IAFCJ,EAAA,CAAAgB,SAAA,EAAsB;IAAChB,EAAvB,CAAAiB,UAAA,QAAAiC,WAAA,CAAAI,KAAA,EAAAtD,EAAA,CAAAmB,aAAA,CAAsB,QAAA+B,WAAA,CAAAb,IAAA,CAAsB;IAC3CrC,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAqB,iBAAA,CAAA6B,WAAA,CAAAb,IAAA,CAAmB;;;AAgPrC,OAAM,MAAOkB,gBAAgB;EAM3BC,YAAoBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IALlC,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,UAAU,GAAU,EAAE;EAE+B;EAErDC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAH,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACL,cAAc,GAAG,EAAE;EAC1B;EAEAM,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACL,gBAAgB,GAAG,EAAE;EAC5B;EAEAM,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACL,cAAc,GAAG,CACpB;MACE7C,EAAE,EAAE,GAAG;MACPO,QAAQ,EAAE,gBAAgB;MAC1BF,QAAQ,EAAE,WAAW;MACrBF,MAAM,EAAE,oEAAoE;MAC5E4B,cAAc,EAAE,IAAI;MACpBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdvB,WAAW,EAAE;KACd,EACD;MACEV,EAAE,EAAE,GAAG;MACPO,QAAQ,EAAE,iBAAiB;MAC3BF,QAAQ,EAAE,aAAa;MACvBF,MAAM,EAAE,iEAAiE;MACzE4B,cAAc,EAAE,IAAI;MACpBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdvB,WAAW,EAAE;KACd,EACD;MACEV,EAAE,EAAE,GAAG;MACPO,QAAQ,EAAE,kBAAkB;MAC5BF,QAAQ,EAAE,aAAa;MACvBF,MAAM,EAAE,oEAAoE;MAC5E4B,cAAc,EAAE,IAAI;MACpBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdvB,WAAW,EAAE;KACd,CACF;EACH;EAEAyC,cAAcA,CAAA;IACZ,IAAI,CAACL,UAAU,GAAG,CAChB;MACExB,IAAI,EAAE,OAAO;MACbgB,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;KACR,EACD;MACEjB,IAAI,EAAE,KAAK;MACXgB,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE;KACR,EACD;MACEjB,IAAI,EAAE,MAAM;MACZgB,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,EACD;MACEjB,IAAI,EAAE,QAAQ;MACdgB,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;KACR,CACF;EACH;EAEAT,mBAAmBA,CAACsB,KAAa;IAC/B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEAvD,UAAUA,CAACwD,MAAc;IACvB,MAAMC,IAAI,GAAG,IAAI,CAACb,cAAc,CAACc,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1D,EAAE,KAAKuD,MAAM,CAAC;IAC3D,IAAIC,IAAI,EAAE;MACRA,IAAI,CAAC9C,WAAW,GAAG,CAAC8C,IAAI,CAAC9C,WAAW;;EAExC;EAEAmB,gBAAgBA,CAAC8B,YAAoB;IACnC,MAAMC,UAAU,GAAG,IAAI,CAACf,cAAc,CAACY,IAAI,CAACI,CAAC,IAAIA,CAAC,CAAC7D,EAAE,KAAK2D,YAAY,CAAC;IACvE,IAAIC,UAAU,EAAE;MACdA,UAAU,CAAClD,WAAW,GAAG,CAACkD,UAAU,CAAClD,WAAW;;EAEpD;EAEAQ,QAAQA,CAAC4C,SAAiB;IACxBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,SAAS,CAAC;IAC5C;EACF;EAEAzB,cAAcA,CAAC4B,YAAoB;IACjCF,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,YAAY,CAAC;IAC7C;EACF;;;uBAnHWzB,gBAAgB,EAAAvD,EAAA,CAAAiF,iBAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAhB5B,gBAAgB;MAAA6B,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAtF,EAAA,CAAAuF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7SrB7F,EAHJ,CAAAC,cAAA,eAAuB,aAEI,SACnB;UAAAD,EAAA,CAAAG,MAAA,wBAAiB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC1BJ,EAAA,CAAA6B,UAAA,IAAAkE,+BAAA,iBAAiE;UAUnE/F,EAAA,CAAAI,YAAA,EAAM;UAIJJ,EADF,CAAAC,cAAA,aAAsB,SAChB;UAAAD,EAAA,CAAAG,MAAA,wBAAiB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC1BJ,EAAA,CAAA6B,UAAA,IAAAmE,+BAAA,mBAAoE;UAkBtEhG,EAAA,CAAAI,YAAA,EAAM;UAIJJ,EADF,CAAAC,cAAA,aAAyB,UACnB;UAAAD,EAAA,CAAAG,MAAA,+BAAuB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChCJ,EAAA,CAAA6B,UAAA,KAAAoE,gCAAA,kBAAuE;UAczEjG,EAAA,CAAAI,YAAA,EAAM;UAIJJ,EADF,CAAAC,cAAA,cAAwB,UAClB;UAAAD,EAAA,CAAAG,MAAA,wBAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzBJ,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAA6B,UAAA,KAAAqE,gCAAA,iBAAuG;UAM7GlG,EAFI,CAAAI,YAAA,EAAM,EACF,EACA;;;UAhEkBJ,EAAA,CAAAgB,SAAA,GAAiB;UAAjBhB,EAAA,CAAAiB,UAAA,YAAA6E,GAAA,CAAApC,cAAA,CAAiB;UAed1D,EAAA,CAAAgB,SAAA,GAAmB;UAAnBhB,EAAA,CAAAiB,UAAA,YAAA6E,GAAA,CAAAnC,gBAAA,CAAmB;UAuBhB3D,EAAA,CAAAgB,SAAA,GAAiB;UAAjBhB,EAAA,CAAAiB,UAAA,YAAA6E,GAAA,CAAAlC,cAAA,CAAiB;UAoBjB5D,EAAA,CAAAgB,SAAA,GAAa;UAAbhB,EAAA,CAAAiB,UAAA,YAAA6E,GAAA,CAAAjC,UAAA,CAAa;;;qBAhErC9D,YAAY,EAAAoG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}