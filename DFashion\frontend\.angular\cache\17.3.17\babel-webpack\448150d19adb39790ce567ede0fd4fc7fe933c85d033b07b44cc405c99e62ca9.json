{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nfunction CategorySelectionComponent_div_7_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Avg: \\u20B9\", i0.ɵɵpipeBind2(2, 1, category_r2.avgPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction CategorySelectionComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function CategorySelectionComponent_div_7_Template_div_click_0_listener() {\n      const category_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectCategory(category_r2));\n    })(\"keydown.enter\", function CategorySelectionComponent_div_7_Template_div_keydown_enter_0_listener() {\n      const category_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectCategory(category_r2));\n    })(\"keydown.space\", function CategorySelectionComponent_div_7_Template_div_keydown_space_0_listener() {\n      const category_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectCategory(category_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 10);\n    i0.ɵɵelement(2, \"img\", 11);\n    i0.ɵɵelementStart(3, \"div\", 12)(4, \"div\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 14)(7, \"h3\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 15)(12, \"span\", 16);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, CategorySelectionComponent_div_7_span_14_Template, 3, 4, \"span\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 18)(16, \"button\", 19);\n    i0.ɵɵelement(17, \"i\", 20);\n    i0.ɵɵtext(18, \" Browse \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const category_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-label\", \"Select \" + category_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", category_r2.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r2.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", category_r2.productCount, \" Products\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r2.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.formatTargetGender(category_r2.targetGender));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", category_r2.avgPrice);\n  }\n}\nexport class CategorySelectionComponent {\n  constructor(router) {\n    this.router = router;\n    this.categories = [];\n    this.originalQuery = '';\n    this.categorySelected = new EventEmitter();\n    this.searchAllRequested = new EventEmitter();\n    this.refineSearchRequested = new EventEmitter();\n  }\n  ngOnInit() {\n    // Track category selection page view\n    this.trackPageView();\n  }\n  selectCategory(category) {\n    // Track category selection\n    this.trackCategorySelection(category);\n    // Emit category selection event\n    this.categorySelected.emit(category);\n    // Navigate to search results with category filter\n    this.router.navigate(['/search'], {\n      queryParams: {\n        q: this.originalQuery,\n        category: category.category,\n        subcategory: category.subcategory,\n        targetGender: category.targetGender\n      }\n    });\n  }\n  searchAllCategories() {\n    // Track search all action\n    this.trackSearchAllAction();\n    // Emit search all event\n    this.searchAllRequested.emit(this.originalQuery);\n    // Navigate to search results without category filter\n    this.router.navigate(['/search'], {\n      queryParams: {\n        q: this.originalQuery\n      }\n    });\n  }\n  refineSearch() {\n    // Track refine search action\n    this.trackRefineSearchAction();\n    // Emit refine search event\n    this.refineSearchRequested.emit(this.originalQuery);\n    // Navigate back to search page with query pre-filled\n    this.router.navigate(['/search'], {\n      queryParams: {\n        q: this.originalQuery,\n        refine: 'true'\n      }\n    });\n  }\n  formatTargetGender(gender) {\n    switch (gender.toLowerCase()) {\n      case 'men':\n        return 'Men\\'s Fashion';\n      case 'women':\n        return 'Women\\'s Fashion';\n      case 'unisex':\n        return 'Unisex';\n      case 'boys':\n        return 'Boys\\' Fashion';\n      case 'girls':\n        return 'Girls\\' Fashion';\n      default:\n        return 'Fashion';\n    }\n  }\n  trackPageView() {\n    // Analytics tracking for category selection page\n    if (typeof window.gtag !== 'undefined') {\n      window.gtag('event', 'page_view', {\n        page_title: 'Category Selection',\n        page_location: window.location.href,\n        custom_parameters: {\n          original_query: this.originalQuery,\n          categories_shown: this.categories.length\n        }\n      });\n    }\n  }\n  trackCategorySelection(category) {\n    // Analytics tracking for category selection\n    if (typeof window.gtag !== 'undefined') {\n      window.gtag('event', 'select_content', {\n        content_type: 'category',\n        content_id: category.id,\n        custom_parameters: {\n          category_name: category.name,\n          target_gender: category.targetGender,\n          product_count: category.productCount,\n          original_query: this.originalQuery\n        }\n      });\n    }\n  }\n  trackSearchAllAction() {\n    // Analytics tracking for search all action\n    if (typeof window.gtag !== 'undefined') {\n      window.gtag('event', 'search', {\n        search_term: this.originalQuery,\n        custom_parameters: {\n          search_type: 'all_categories',\n          from_category_selection: true\n        }\n      });\n    }\n  }\n  trackRefineSearchAction() {\n    // Analytics tracking for refine search action\n    if (typeof window.gtag !== 'undefined') {\n      window.gtag('event', 'search', {\n        search_term: this.originalQuery,\n        custom_parameters: {\n          search_type: 'refine',\n          from_category_selection: true\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function CategorySelectionComponent_Factory(t) {\n      return new (t || CategorySelectionComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CategorySelectionComponent,\n      selectors: [[\"app-category-selection\"]],\n      inputs: {\n        categories: \"categories\",\n        originalQuery: \"originalQuery\"\n      },\n      outputs: {\n        categorySelected: \"categorySelected\",\n        searchAllRequested: \"searchAllRequested\",\n        refineSearchRequested: \"refineSearchRequested\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 15,\n      vars: 2,\n      consts: [[1, \"category-selection-container\"], [1, \"category-selection-header\"], [1, \"category-grid\"], [\"class\", \"category-card\", \"tabindex\", \"0\", 3, \"click\", \"keydown.enter\", \"keydown.space\", 4, \"ngFor\", \"ngForOf\"], [1, \"alternative-actions\"], [\"type\", \"button\", 1, \"alt-action-btn\", \"search-all-btn\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"type\", \"button\", 1, \"alt-action-btn\", \"refine-search-btn\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [\"tabindex\", \"0\", 1, \"category-card\", 3, \"click\", \"keydown.enter\", \"keydown.space\"], [1, \"category-image\"], [\"loading\", \"lazy\", 3, \"src\", \"alt\"], [1, \"category-overlay\"], [1, \"product-count\"], [1, \"category-info\"], [1, \"category-meta\"], [1, \"target-gender\"], [\"class\", \"avg-price\", 4, \"ngIf\"], [1, \"category-action\"], [\"type\", \"button\", 1, \"select-btn\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"avg-price\"]],\n      template: function CategorySelectionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\");\n          i0.ɵɵtext(5, \"We found multiple categories that match your search. Please select one:\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 2);\n          i0.ɵɵtemplate(7, CategorySelectionComponent_div_7_Template, 19, 8, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 4)(9, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function CategorySelectionComponent_Template_button_click_9_listener() {\n            return ctx.searchAllCategories();\n          });\n          i0.ɵɵelement(10, \"i\", 6);\n          i0.ɵɵtext(11, \" Search All Categories \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function CategorySelectionComponent_Template_button_click_12_listener() {\n            return ctx.refineSearch();\n          });\n          i0.ɵɵelement(13, \"i\", 8);\n          i0.ɵɵtext(14, \" Refine Search \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"Choose Category for \\\"\", ctx.originalQuery, \"\\\"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe],\n      styles: [\".category-selection-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 2rem;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n  min-height: 100vh;\\n}\\n@media (max-width: 768px) {\\n  .category-selection-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n}\\n\\n.category-selection-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 3rem;\\n}\\n.category-selection-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 1rem;\\n}\\n@media (max-width: 768px) {\\n  .category-selection-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n}\\n.category-selection-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #7f8c8d;\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n@media (max-width: 768px) {\\n  .category-selection-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n\\n.category-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 2rem;\\n  margin-bottom: 3rem;\\n}\\n@media (max-width: 768px) {\\n  .category-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 1.5rem;\\n  }\\n}\\n\\n.category-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 20px;\\n  overflow: hidden;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  position: relative;\\n}\\n.category-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n}\\n.category-card[_ngcontent-%COMP%]:focus {\\n  outline: 3px solid #3498db;\\n  outline-offset: 2px;\\n}\\n.category-card[_ngcontent-%COMP%]:active {\\n  transform: translateY(-5px);\\n}\\n\\n.category-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n.category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.category-image[_ngcontent-%COMP%]   .category-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.3) 70%, rgba(0, 0, 0, 0.7) 100%);\\n  display: flex;\\n  align-items: flex-end;\\n  padding: 1rem;\\n}\\n.category-image[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #2c3e50;\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.category-card[_ngcontent-%COMP%]:hover   .category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.category-info[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.category-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 0.5rem;\\n}\\n.category-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  margin-bottom: 1rem;\\n  line-height: 1.5;\\n}\\n\\n.category-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.category-meta[_ngcontent-%COMP%]   .target-gender[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 0.3rem 0.8rem;\\n  border-radius: 15px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n}\\n.category-meta[_ngcontent-%COMP%]   .avg-price[_ngcontent-%COMP%] {\\n  color: #27ae60;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n}\\n\\n.category-action[_ngcontent-%COMP%] {\\n  padding: 0 1.5rem 1.5rem;\\n}\\n.category-action[_ngcontent-%COMP%]   .select-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\\n  color: white;\\n  border: none;\\n  padding: 1rem;\\n  border-radius: 10px;\\n  font-weight: 600;\\n  font-size: 1rem;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n}\\n.category-action[_ngcontent-%COMP%]   .select-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);\\n  transform: translateY(-2px);\\n}\\n.category-action[_ngcontent-%COMP%]   .select-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.category-action[_ngcontent-%COMP%]   .select-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n\\n.category-card[_ngcontent-%COMP%]:hover   .select-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transform: translateX(5px);\\n}\\n\\n.alternative-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n}\\n@media (max-width: 768px) {\\n  .alternative-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n}\\n\\n.alt-action-btn[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 2px solid #e0e0e0;\\n  color: #2c3e50;\\n  padding: 1rem 2rem;\\n  border-radius: 50px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 1rem;\\n}\\n.alt-action-btn[_ngcontent-%COMP%]:hover {\\n  border-color: #3498db;\\n  color: #3498db;\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.2);\\n}\\n.alt-action-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.alt-action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n@media (max-width: 768px) {\\n  .alt-action-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n    max-width: 300px;\\n  }\\n}\\n\\n.search-all-btn[_ngcontent-%COMP%]:hover {\\n  border-color: #27ae60;\\n  color: #27ae60;\\n  box-shadow: 0 5px 15px rgba(39, 174, 96, 0.2);\\n}\\n\\n.refine-search-btn[_ngcontent-%COMP%]:hover {\\n  border-color: #f39c12;\\n  color: #f39c12;\\n  box-shadow: 0 5px 15px rgba(243, 156, 18, 0.2);\\n}\\n\\n.category-card.loading[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  .category-card[_ngcontent-%COMP%], .category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .select-btn[_ngcontent-%COMP%], .alt-action-btn[_ngcontent-%COMP%] {\\n    transition: none;\\n  }\\n  .category-card[_ngcontent-%COMP%]:hover {\\n    transform: none;\\n  }\\n  .category-card[_ngcontent-%COMP%]:hover   .category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    transform: none;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .category-card[_ngcontent-%COMP%] {\\n    border: 2px solid #000;\\n  }\\n  .category-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    color: #000;\\n  }\\n  .select-btn[_ngcontent-%COMP%] {\\n    background: #000;\\n    color: #fff;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .category-selection-container[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);\\n  }\\n  .category-selection-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    color: #ecf0f1;\\n  }\\n  .category-selection-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    color: #bdc3c7;\\n  }\\n  .category-card[_ngcontent-%COMP%] {\\n    background: #34495e;\\n  }\\n  .category-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    color: #ecf0f1;\\n  }\\n  .category-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    color: #bdc3c7;\\n  }\\n  .alt-action-btn[_ngcontent-%COMP%] {\\n    background: #34495e;\\n    border-color: #7f8c8d;\\n    color: #ecf0f1;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "category_r2", "avgPrice", "ɵɵlistener", "CategorySelectionComponent_div_7_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "selectCategory", "CategorySelectionComponent_div_7_Template_div_keydown_enter_0_listener", "CategorySelectionComponent_div_7_Template_div_keydown_space_0_listener", "ɵɵelement", "ɵɵtemplate", "CategorySelectionComponent_div_7_span_14_Template", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "name", "productCount", "ɵɵtextInterpolate", "description", "formatTargetGender", "targetGender", "CategorySelectionComponent", "constructor", "router", "categories", "originalQuery", "categorySelected", "searchAllRequested", "refineSearchRequested", "ngOnInit", "trackPageView", "category", "trackCategorySelection", "emit", "navigate", "queryParams", "q", "subcategory", "searchAllCategories", "trackSearchAllAction", "refineSearch", "trackRefineSearchAction", "refine", "gender", "toLowerCase", "window", "gtag", "page_title", "page_location", "location", "href", "custom_parameters", "original_query", "categories_shown", "length", "content_type", "content_id", "id", "category_name", "target_gender", "product_count", "search_term", "search_type", "from_category_selection", "ɵɵdirectiveInject", "i1", "Router", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "CategorySelectionComponent_Template", "rf", "ctx", "CategorySelectionComponent_div_7_Template", "CategorySelectionComponent_Template_button_click_9_listener", "CategorySelectionComponent_Template_button_click_12_listener", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "styles"], "sources": ["E:\\Fashion\\DFashion\\DFashion\\frontend\\src\\app\\shared\\components\\category-selection\\category-selection.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { CategoryOption } from '../../../core/services/advanced-search.service';\n\n@Component({\n  selector: 'app-category-selection',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"category-selection-container\">\n      <div class=\"category-selection-header\">\n        <h2>Choose Category for \"{{ originalQuery }}\"</h2>\n        <p>We found multiple categories that match your search. Please select one:</p>\n      </div>\n      \n      <div class=\"category-grid\">\n        <div \n          *ngFor=\"let category of categories\" \n          class=\"category-card\"\n          (click)=\"selectCategory(category)\"\n          [attr.aria-label]=\"'Select ' + category.name\"\n          tabindex=\"0\"\n          (keydown.enter)=\"selectCategory(category)\"\n          (keydown.space)=\"selectCategory(category)\">\n          \n          <div class=\"category-image\">\n            <img [src]=\"category.image\" [alt]=\"category.name\" loading=\"lazy\">\n            <div class=\"category-overlay\">\n              <div class=\"product-count\">{{ category.productCount }} Products</div>\n            </div>\n          </div>\n          \n          <div class=\"category-info\">\n            <h3>{{ category.name }}</h3>\n            <p>{{ category.description }}</p>\n            <div class=\"category-meta\">\n              <span class=\"target-gender\">{{ formatTargetGender(category.targetGender) }}</span>\n              <span class=\"avg-price\" *ngIf=\"category.avgPrice\">\n                Avg: ₹{{ category.avgPrice | number:'1.0-0' }}\n              </span>\n            </div>\n          </div>\n          \n          <div class=\"category-action\">\n            <button class=\"select-btn\" type=\"button\">\n              <i class=\"fas fa-arrow-right\"></i>\n              Browse\n            </button>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"alternative-actions\">\n        <button \n          class=\"alt-action-btn search-all-btn\" \n          (click)=\"searchAllCategories()\"\n          type=\"button\">\n          <i class=\"fas fa-search\"></i>\n          Search All Categories\n        </button>\n        \n        <button \n          class=\"alt-action-btn refine-search-btn\" \n          (click)=\"refineSearch()\"\n          type=\"button\">\n          <i class=\"fas fa-edit\"></i>\n          Refine Search\n        </button>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./category-selection.component.scss']\n})\nexport class CategorySelectionComponent implements OnInit {\n  @Input() categories: CategoryOption[] = [];\n  @Input() originalQuery: string = '';\n  \n  @Output() categorySelected = new EventEmitter<CategoryOption>();\n  @Output() searchAllRequested = new EventEmitter<string>();\n  @Output() refineSearchRequested = new EventEmitter<string>();\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    // Track category selection page view\n    this.trackPageView();\n  }\n\n  selectCategory(category: CategoryOption): void {\n    // Track category selection\n    this.trackCategorySelection(category);\n    \n    // Emit category selection event\n    this.categorySelected.emit(category);\n    \n    // Navigate to search results with category filter\n    this.router.navigate(['/search'], {\n      queryParams: {\n        q: this.originalQuery,\n        category: category.category,\n        subcategory: category.subcategory,\n        targetGender: category.targetGender\n      }\n    });\n  }\n\n  searchAllCategories(): void {\n    // Track search all action\n    this.trackSearchAllAction();\n    \n    // Emit search all event\n    this.searchAllRequested.emit(this.originalQuery);\n    \n    // Navigate to search results without category filter\n    this.router.navigate(['/search'], {\n      queryParams: { q: this.originalQuery }\n    });\n  }\n\n  refineSearch(): void {\n    // Track refine search action\n    this.trackRefineSearchAction();\n    \n    // Emit refine search event\n    this.refineSearchRequested.emit(this.originalQuery);\n    \n    // Navigate back to search page with query pre-filled\n    this.router.navigate(['/search'], {\n      queryParams: { q: this.originalQuery, refine: 'true' }\n    });\n  }\n\n  formatTargetGender(gender: string): string {\n    switch (gender.toLowerCase()) {\n      case 'men':\n        return 'Men\\'s Fashion';\n      case 'women':\n        return 'Women\\'s Fashion';\n      case 'unisex':\n        return 'Unisex';\n      case 'boys':\n        return 'Boys\\' Fashion';\n      case 'girls':\n        return 'Girls\\' Fashion';\n      default:\n        return 'Fashion';\n    }\n  }\n\n  private trackPageView(): void {\n    // Analytics tracking for category selection page\n    if (typeof (window as any).gtag !== 'undefined') {\n      (window as any).gtag('event', 'page_view', {\n        page_title: 'Category Selection',\n        page_location: window.location.href,\n        custom_parameters: {\n          original_query: this.originalQuery,\n          categories_shown: this.categories.length\n        }\n      });\n    }\n  }\n\n  private trackCategorySelection(category: CategoryOption): void {\n    // Analytics tracking for category selection\n    if (typeof (window as any).gtag !== 'undefined') {\n      (window as any).gtag('event', 'select_content', {\n        content_type: 'category',\n        content_id: category.id,\n        custom_parameters: {\n          category_name: category.name,\n          target_gender: category.targetGender,\n          product_count: category.productCount,\n          original_query: this.originalQuery\n        }\n      });\n    }\n  }\n\n  private trackSearchAllAction(): void {\n    // Analytics tracking for search all action\n    if (typeof (window as any).gtag !== 'undefined') {\n      (window as any).gtag('event', 'search', {\n        search_term: this.originalQuery,\n        custom_parameters: {\n          search_type: 'all_categories',\n          from_category_selection: true\n        }\n      });\n    }\n  }\n\n  private trackRefineSearchAction(): void {\n    // Analytics tracking for refine search action\n    if (typeof (window as any).gtag !== 'undefined') {\n      (window as any).gtag('event', 'search', {\n        search_term: this.originalQuery,\n        custom_parameters: {\n          search_type: 'refine',\n          from_category_selection: true\n        }\n      });\n    }\n  }\n}\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAgB,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;;;;;;IAqChCC,EAAA,CAAAC,cAAA,eAAkD;IAChDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,iBAAAL,EAAA,CAAAM,WAAA,OAAAC,WAAA,CAAAC,QAAA,gBACF;;;;;;IAvBNR,EAAA,CAAAC,cAAA,aAO6C;IAA3CD,EAJA,CAAAS,UAAA,mBAAAC,+DAAA;MAAA,MAAAH,WAAA,GAAAP,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAG,cAAA,CAAAV,WAAA,CAAwB;IAAA,EAAC,2BAAAW,uEAAA;MAAA,MAAAX,WAAA,GAAAP,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAGjBF,MAAA,CAAAG,cAAA,CAAAV,WAAA,CAAwB;IAAA,EAAC,2BAAAY,uEAAA;MAAA,MAAAZ,WAAA,GAAAP,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CACzBF,MAAA,CAAAG,cAAA,CAAAV,WAAA,CAAwB;IAAA,EAAC;IAE1CP,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAoB,SAAA,cAAiE;IAE/DpB,EADF,CAAAC,cAAA,cAA8B,cACD;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAEnEF,EAFmE,CAAAG,YAAA,EAAM,EACjE,EACF;IAGJH,EADF,CAAAC,cAAA,cAA2B,SACrB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE/BH,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAE,MAAA,IAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClFH,EAAA,CAAAqB,UAAA,KAAAC,iDAAA,mBAAkD;IAItDtB,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAA6B,kBACc;IACvCD,EAAA,CAAAoB,SAAA,aAAkC;IAClCpB,EAAA,CAAAE,MAAA,gBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;;IAvBGH,EAAA,CAAAI,SAAA,GAAsB;IAACJ,EAAvB,CAAAuB,UAAA,QAAAhB,WAAA,CAAAiB,KAAA,EAAAxB,EAAA,CAAAyB,aAAA,CAAsB,QAAAlB,WAAA,CAAAmB,IAAA,CAAsB;IAEpB1B,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAK,kBAAA,KAAAE,WAAA,CAAAoB,YAAA,cAAoC;IAK7D3B,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAA4B,iBAAA,CAAArB,WAAA,CAAAmB,IAAA,CAAmB;IACpB1B,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAA4B,iBAAA,CAAArB,WAAA,CAAAsB,WAAA,CAA0B;IAEC7B,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAA4B,iBAAA,CAAAd,MAAA,CAAAgB,kBAAA,CAAAvB,WAAA,CAAAwB,YAAA,EAA+C;IAClD/B,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAuB,UAAA,SAAAhB,WAAA,CAAAC,QAAA,CAAuB;;;AAoC9D,OAAM,MAAOwB,0BAA0B;EAQrCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAPjB,KAAAC,UAAU,GAAqB,EAAE;IACjC,KAAAC,aAAa,GAAW,EAAE;IAEzB,KAAAC,gBAAgB,GAAG,IAAIvC,YAAY,EAAkB;IACrD,KAAAwC,kBAAkB,GAAG,IAAIxC,YAAY,EAAU;IAC/C,KAAAyC,qBAAqB,GAAG,IAAIzC,YAAY,EAAU;EAEvB;EAErC0C,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAxB,cAAcA,CAACyB,QAAwB;IACrC;IACA,IAAI,CAACC,sBAAsB,CAACD,QAAQ,CAAC;IAErC;IACA,IAAI,CAACL,gBAAgB,CAACO,IAAI,CAACF,QAAQ,CAAC;IAEpC;IACA,IAAI,CAACR,MAAM,CAACW,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;MAChCC,WAAW,EAAE;QACXC,CAAC,EAAE,IAAI,CAACX,aAAa;QACrBM,QAAQ,EAAEA,QAAQ,CAACA,QAAQ;QAC3BM,WAAW,EAAEN,QAAQ,CAACM,WAAW;QACjCjB,YAAY,EAAEW,QAAQ,CAACX;;KAE1B,CAAC;EACJ;EAEAkB,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAACC,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAACZ,kBAAkB,CAACM,IAAI,CAAC,IAAI,CAACR,aAAa,CAAC;IAEhD;IACA,IAAI,CAACF,MAAM,CAACW,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;MAChCC,WAAW,EAAE;QAAEC,CAAC,EAAE,IAAI,CAACX;MAAa;KACrC,CAAC;EACJ;EAEAe,YAAYA,CAAA;IACV;IACA,IAAI,CAACC,uBAAuB,EAAE;IAE9B;IACA,IAAI,CAACb,qBAAqB,CAACK,IAAI,CAAC,IAAI,CAACR,aAAa,CAAC;IAEnD;IACA,IAAI,CAACF,MAAM,CAACW,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;MAChCC,WAAW,EAAE;QAAEC,CAAC,EAAE,IAAI,CAACX,aAAa;QAAEiB,MAAM,EAAE;MAAM;KACrD,CAAC;EACJ;EAEAvB,kBAAkBA,CAACwB,MAAc;IAC/B,QAAQA,MAAM,CAACC,WAAW,EAAE;MAC1B,KAAK,KAAK;QACR,OAAO,gBAAgB;MACzB,KAAK,OAAO;QACV,OAAO,kBAAkB;MAC3B,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB,KAAK,MAAM;QACT,OAAO,gBAAgB;MACzB,KAAK,OAAO;QACV,OAAO,iBAAiB;MAC1B;QACE,OAAO,SAAS;;EAEtB;EAEQd,aAAaA,CAAA;IACnB;IACA,IAAI,OAAQe,MAAc,CAACC,IAAI,KAAK,WAAW,EAAE;MAC9CD,MAAc,CAACC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE;QACzCC,UAAU,EAAE,oBAAoB;QAChCC,aAAa,EAAEH,MAAM,CAACI,QAAQ,CAACC,IAAI;QACnCC,iBAAiB,EAAE;UACjBC,cAAc,EAAE,IAAI,CAAC3B,aAAa;UAClC4B,gBAAgB,EAAE,IAAI,CAAC7B,UAAU,CAAC8B;;OAErC,CAAC;;EAEN;EAEQtB,sBAAsBA,CAACD,QAAwB;IACrD;IACA,IAAI,OAAQc,MAAc,CAACC,IAAI,KAAK,WAAW,EAAE;MAC9CD,MAAc,CAACC,IAAI,CAAC,OAAO,EAAE,gBAAgB,EAAE;QAC9CS,YAAY,EAAE,UAAU;QACxBC,UAAU,EAAEzB,QAAQ,CAAC0B,EAAE;QACvBN,iBAAiB,EAAE;UACjBO,aAAa,EAAE3B,QAAQ,CAAChB,IAAI;UAC5B4C,aAAa,EAAE5B,QAAQ,CAACX,YAAY;UACpCwC,aAAa,EAAE7B,QAAQ,CAACf,YAAY;UACpCoC,cAAc,EAAE,IAAI,CAAC3B;;OAExB,CAAC;;EAEN;EAEQc,oBAAoBA,CAAA;IAC1B;IACA,IAAI,OAAQM,MAAc,CAACC,IAAI,KAAK,WAAW,EAAE;MAC9CD,MAAc,CAACC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE;QACtCe,WAAW,EAAE,IAAI,CAACpC,aAAa;QAC/B0B,iBAAiB,EAAE;UACjBW,WAAW,EAAE,gBAAgB;UAC7BC,uBAAuB,EAAE;;OAE5B,CAAC;;EAEN;EAEQtB,uBAAuBA,CAAA;IAC7B;IACA,IAAI,OAAQI,MAAc,CAACC,IAAI,KAAK,WAAW,EAAE;MAC9CD,MAAc,CAACC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE;QACtCe,WAAW,EAAE,IAAI,CAACpC,aAAa;QAC/B0B,iBAAiB,EAAE;UACjBW,WAAW,EAAE,QAAQ;UACrBC,uBAAuB,EAAE;;OAE5B,CAAC;;EAEN;;;uBAlIW1C,0BAA0B,EAAAhC,EAAA,CAAA2E,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA1B7C,0BAA0B;MAAA8C,SAAA;MAAAC,MAAA;QAAA5C,UAAA;QAAAC,aAAA;MAAA;MAAA4C,OAAA;QAAA3C,gBAAA;QAAAC,kBAAA;QAAAC,qBAAA;MAAA;MAAA0C,UAAA;MAAAC,QAAA,GAAAlF,EAAA,CAAAmF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9D/BzF,EAFJ,CAAAC,cAAA,aAA0C,aACD,SACjC;UAAAD,EAAA,CAAAE,MAAA,GAAyC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,8EAAuE;UAC5EF,EAD4E,CAAAG,YAAA,EAAI,EAC1E;UAENH,EAAA,CAAAC,cAAA,aAA2B;UACzBD,EAAA,CAAAqB,UAAA,IAAAsE,yCAAA,kBAO6C;UA2B/C3F,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,aAAiC,gBAIf;UADdD,EAAA,CAAAS,UAAA,mBAAAmF,4DAAA;YAAA,OAASF,GAAA,CAAAzC,mBAAA,EAAqB;UAAA,EAAC;UAE/BjD,EAAA,CAAAoB,SAAA,YAA6B;UAC7BpB,EAAA,CAAAE,MAAA,+BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,iBAGgB;UADdD,EAAA,CAAAS,UAAA,mBAAAoF,6DAAA;YAAA,OAASH,GAAA,CAAAvC,YAAA,EAAc;UAAA,EAAC;UAExBnD,EAAA,CAAAoB,SAAA,YAA2B;UAC3BpB,EAAA,CAAAE,MAAA,uBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;UA1DEH,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAK,kBAAA,2BAAAqF,GAAA,CAAAtD,aAAA,OAAyC;UAMtBpC,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAuB,UAAA,YAAAmE,GAAA,CAAAvD,UAAA,CAAa;;;qBAVhCpC,YAAY,EAAA+F,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}