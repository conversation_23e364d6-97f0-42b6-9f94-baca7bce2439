{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/admin-auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/snack-bar\";\nexport let PermissionGuard = /*#__PURE__*/(() => {\n  class PermissionGuard {\n    constructor(authService, router, snackBar) {\n      this.authService = authService;\n      this.router = router;\n      this.snackBar = snackBar;\n    }\n    canActivate(route, state) {\n      const requiredPermission = route.data?.['permission'];\n      const requiredRole = route.data?.['role'];\n      // Check if user is authenticated\n      if (!this.authService.isAuthenticated()) {\n        this.router.navigate(['/admin/login']);\n        return false;\n      }\n      // Check role-based access\n      if (requiredRole) {\n        const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];\n        if (!this.authService.hasRole(allowedRoles)) {\n          this.showAccessDeniedMessage('You do not have the required role to access this page.');\n          this.router.navigate(['/admin/dashboard']);\n          return false;\n        }\n      }\n      // Check permission-based access\n      if (requiredPermission) {\n        const [module, action] = requiredPermission.split(':');\n        if (!this.authService.hasPermission(module, action)) {\n          this.showAccessDeniedMessage('You do not have permission to access this page.');\n          this.router.navigate(['/admin/dashboard']);\n          return false;\n        }\n      }\n      return true;\n    }\n    showAccessDeniedMessage(message) {\n      this.snackBar.open(message, 'Close', {\n        duration: 5000,\n        panelClass: ['error-snackbar']\n      });\n    }\n    static {\n      this.ɵfac = function PermissionGuard_Factory(t) {\n        return new (t || PermissionGuard)(i0.ɵɵinject(i1.AdminAuthService), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PermissionGuard,\n        factory: PermissionGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return PermissionGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}