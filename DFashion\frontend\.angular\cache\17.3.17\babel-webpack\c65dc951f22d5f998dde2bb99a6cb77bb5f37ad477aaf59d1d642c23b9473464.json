{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let NotificationService = /*#__PURE__*/(() => {\n  class NotificationService {\n    constructor() {\n      this.notificationsSubject = new BehaviorSubject([]);\n      this.notifications$ = this.notificationsSubject.asObservable();\n    }\n    show(notification) {\n      const id = Date.now().toString();\n      const newNotification = {\n        ...notification,\n        id,\n        duration: notification.duration || 5000\n      };\n      const currentNotifications = this.notificationsSubject.value;\n      this.notificationsSubject.next([...currentNotifications, newNotification]);\n      // Auto remove after duration\n      if (newNotification.duration && newNotification.duration > 0) {\n        setTimeout(() => {\n          this.remove(id);\n        }, newNotification.duration);\n      }\n    }\n    success(title, message, duration) {\n      this.show({\n        type: 'success',\n        title,\n        message,\n        duration\n      });\n    }\n    error(title, message, duration) {\n      this.show({\n        type: 'error',\n        title,\n        message,\n        duration\n      });\n    }\n    info(title, message, duration) {\n      this.show({\n        type: 'info',\n        title,\n        message,\n        duration\n      });\n    }\n    warning(title, message, duration) {\n      this.show({\n        type: 'warning',\n        title,\n        message,\n        duration\n      });\n    }\n    remove(id) {\n      const currentNotifications = this.notificationsSubject.value;\n      const filteredNotifications = currentNotifications.filter(n => n.id !== id);\n      this.notificationsSubject.next(filteredNotifications);\n    }\n    clear() {\n      this.notificationsSubject.next([]);\n    }\n    static {\n      this.ɵfac = function NotificationService_Factory(t) {\n        return new (t || NotificationService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: NotificationService,\n        factory: NotificationService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return NotificationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}