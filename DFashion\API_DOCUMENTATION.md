# DFashion API Documentation

## Base URL
```
http://localhost:5000/api
```

## Authentication
Most endpoints require authentication via J<PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <token>
```

---

## 🔐 Authentication Endpoints

### POST /auth/register
**Description**: Register a new user  
**Access**: Public  
**Body**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "fullName": "string",
  "role": "customer|vendor"
}
```
**Response**:
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": { "id": "...", "username": "...", "email": "..." },
    "token": "jwt_token"
  }
}
```

### POST /auth/login
**Description**: User login  
**Access**: Public  
**Body**:
```json
{
  "email": "string",
  "password": "string"
}
```

### POST /auth/admin/login
**Description**: Admin login  
**Access**: Public  
**Body**:
```json
{
  "email": "string",
  "password": "string"
}
```

### GET /auth/me
**Description**: Get current user profile  
**Access**: Private  
**Headers**: `Authorization: Bearer <token>`

### POST /auth/logout
**Description**: Logout user  
**Access**: Private

---

## 👤 User Endpoints

### GET /users/profile
**Description**: Get user profile  
**Access**: Private

### PUT /users/profile
**Description**: Update user profile  
**Access**: Private  
**Body**:
```json
{
  "fullName": "string",
  "bio": "string",
  "avatar": "string",
  "location": "string"
}
```

### GET /users/suggested
**Description**: Get suggested users to follow  
**Access**: Public  
**Query Params**: `?limit=5`

### GET /users/influencers
**Description**: Get top fashion influencers  
**Access**: Public  
**Query Params**: `?limit=4`

### POST /users/:id/follow
**Description**: Follow/unfollow a user  
**Access**: Private

---

## 🛍️ Product Endpoints

### GET /products
**Description**: Get all products with filters  
**Access**: Public  
**Query Params**:
- `page=1` - Page number
- `limit=12` - Items per page
- `category=women|men|children|ethnic` - Filter by category
- `subcategory=string` - Filter by subcategory
- `brand=string` - Filter by brand
- `minPrice=number` - Minimum price
- `maxPrice=number` - Maximum price
- `search=string` - Search term
- `sortBy=createdAt|price|rating` - Sort field
- `sortOrder=asc|desc` - Sort order

### GET /products/featured
**Description**: Get featured products  
**Access**: Public  
**Query Params**: `?limit=12`

### GET /products/trending
**Description**: Get trending products  
**Access**: Public  
**Query Params**: `?limit=12`

### GET /products/new-arrivals
**Description**: Get new arrival products  
**Access**: Public  
**Query Params**: `?limit=12`

### GET /products/suggested
**Description**: Get suggested products  
**Access**: Public  
**Query Params**: `?page=1&limit=12`

### GET /products/categories
**Description**: Get all product categories with counts  
**Access**: Public

### GET /products/filters
**Description**: Get filter options (price range, sizes, colors, brands)  
**Access**: Public  
**Query Params**: `?category=string`

### GET /products/category/:slug
**Description**: Get products by category  
**Access**: Public

### GET /products/:id
**Description**: Get single product by ID  
**Access**: Public

### POST /products
**Description**: Create new product (Vendor only)  
**Access**: Private (Vendor)  
**Body**:
```json
{
  "name": "string",
  "description": "string",
  "price": "number",
  "originalPrice": "number",
  "category": "men|women|children",
  "subcategory": "string",
  "brand": "string",
  "images": [{"url": "string", "alt": "string"}],
  "sizes": [{"size": "string", "stock": "number"}],
  "colors": [{"name": "string", "hex": "string"}],
  "specifications": {}
}
```

### PUT /products/:id
**Description**: Update product (Vendor only - own products)  
**Access**: Private (Vendor)

### DELETE /products/:id
**Description**: Delete product (Vendor only - own products)  
**Access**: Private (Vendor)

### POST /products/:id/review
**Description**: Add product review  
**Access**: Private  
**Body**:
```json
{
  "rating": "number (1-5)",
  "comment": "string",
  "images": ["string"]
}
```

---

## 🛒 Cart Endpoints

### GET /cart
**Description**: Get user's cart  
**Access**: Private

### POST /cart/add
**Description**: Add item to cart  
**Access**: Private  
**Body**:
```json
{
  "productId": "string",
  "quantity": "number",
  "size": "string",
  "color": "string"
}
```

### PUT /cart/update/:itemId
**Description**: Update cart item quantity  
**Access**: Private  
**Body**:
```json
{
  "quantity": "number"
}
```

### DELETE /cart/remove/:itemId
**Description**: Remove item from cart  
**Access**: Private

### DELETE /cart/clear
**Description**: Clear entire cart  
**Access**: Private

### GET /cart/count
**Description**: Get cart items count  
**Access**: Private

### GET /cart/total-count
**Description**: Get total count (cart + wishlist)  
**Access**: Private

---

## ❤️ Wishlist Endpoints

### GET /wishlist
**Description**: Get user's wishlist  
**Access**: Private

### POST /wishlist/add
**Description**: Add item to wishlist  
**Access**: Private  
**Body**:
```json
{
  "productId": "string"
}
```

### DELETE /wishlist/remove/:productId
**Description**: Remove item from wishlist  
**Access**: Private

### POST /wishlist/move-to-cart/:productId
**Description**: Move item from wishlist to cart  
**Access**: Private

### DELETE /wishlist/clear
**Description**: Clear entire wishlist  
**Access**: Private

---

## 📦 Order Endpoints

### GET /orders
**Description**: Get user's orders  
**Access**: Private  
**Query Params**: `?page=1&limit=10&status=pending|confirmed|shipped|delivered|cancelled`

### GET /orders/:id
**Description**: Get order by ID  
**Access**: Private

### POST /orders
**Description**: Create new order  
**Access**: Private  
**Body**:
```json
{
  "items": [
    {
      "productId": "string",
      "quantity": "number",
      "size": "string",
      "color": "string",
      "price": "number"
    }
  ],
  "shippingAddress": {
    "fullName": "string",
    "address": "string",
    "city": "string",
    "state": "string",
    "pincode": "string",
    "phone": "string"
  },
  "paymentMethod": "cod|online",
  "totalAmount": "number"
}
```

### PUT /orders/:id/cancel
**Description**: Cancel order  
**Access**: Private

---

## 📱 Social Media Endpoints

### GET /posts
**Description**: Get social media posts  
**Access**: Public  
**Query Params**: `?page=1&limit=10&userId=string`

### POST /posts
**Description**: Create new post  
**Access**: Private  
**Body**:
```json
{
  "caption": "string",
  "mediaUrl": "string",
  "mediaType": "image|video",
  "products": [
    {
      "navigationType": "product|category|vendor|brand",
      "position": {"x": "number", "y": "number"},
      "product": {"_id": "string", "name": "string", "price": "number"},
      "category": {"name": "string", "slug": "string"},
      "vendor": {"_id": "string", "username": "string"},
      "brand": {"name": "string", "slug": "string"}
    }
  ]
}
```

### GET /posts/:id
**Description**: Get post by ID  
**Access**: Public

### POST /posts/:id/like
**Description**: Like/unlike post  
**Access**: Private

### POST /posts/:id/comment
**Description**: Add comment to post  
**Access**: Private  
**Body**:
```json
{
  "text": "string"
}
```

### GET /stories
**Description**: Get stories  
**Access**: Public

### POST /stories
**Description**: Create new story  
**Access**: Private

---

## 📊 Categories & Brands Endpoints

### GET /categories
**Description**: Get all categories  
**Access**: Public

### GET /categories/featured
**Description**: Get featured categories  
**Access**: Public

### GET /brands
**Description**: Get all brands  
**Access**: Public

### GET /brands/featured
**Description**: Get featured brands  
**Access**: Public

---

## 🏪 Vendor Endpoints

### GET /vendor
**Description**: Get vendor dashboard (auth required)  
**Access**: Private (Vendor)

### GET /vendor/products
**Description**: Get vendor's products  
**Access**: Private (Vendor)

### GET /vendor/orders
**Description**: Get vendor's orders  
**Access**: Private (Vendor)

### GET /vendor/analytics
**Description**: Get vendor analytics  
**Access**: Private (Vendor)

---

## 📈 Analytics Endpoints

### GET /analytics/overview
**Description**: Get analytics overview  
**Access**: Private

---

## 🔍 Search Endpoints

### GET /search
**Description**: Advanced search  
**Access**: Public  
**Query Params**: `?q=string&category=string&filters=object`

---

## 📤 Upload Endpoints

### POST /upload/image
**Description**: Upload single image  
**Access**: Private  
**Body**: FormData with 'image' field

### POST /upload/multiple
**Description**: Upload multiple images  
**Access**: Private  
**Body**: FormData with 'images' field

---

## ⚙️ Admin Endpoints

### GET /admin/dashboard
**Description**: Get admin dashboard stats  
**Access**: Private (Admin)

### GET /admin/users
**Description**: Get all users  
**Access**: Private (Admin)

### GET /admin/orders
**Description**: Get all orders  
**Access**: Private (Admin)

### PUT /admin/orders/:id/status
**Description**: Update order status  
**Access**: Private (Admin)

---

## 🔧 Utility Endpoints

### GET /health
**Description**: API health check  
**Access**: Public

### GET /docs
**Description**: API documentation  
**Access**: Public

---

## Error Responses

All endpoints return errors in this format:
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error (in development)"
}
```

## Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error
