{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = [\"storyVideo\"];\nfunction StoriesViewerComponent_div_0_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r4 === ctx_r1.currentIndex)(\"completed\", i_r4 < ctx_r1.currentIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getProgressWidth(i_r4), \"%\")(\"animation-duration\", story_r3.media.duration, \"s\");\n  }\n}\nfunction StoriesViewerComponent_div_0_img_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 37, 0);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.media.url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.caption);\n  }\n}\nfunction StoriesViewerComponent_div_0_video_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"video\", 38, 1);\n    i0.ɵɵlistener(\"ended\", function StoriesViewerComponent_div_0_video_16_Template_video_ended_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.media.url, i0.ɵɵsanitizeUrl)(\"poster\", ctx_r1.currentStory.media.thumbnail, i0.ɵɵsanitizeUrl)(\"muted\", ctx_r1.isMuted)(\"autoplay\", true)(\"loop\", false);\n  }\n}\nfunction StoriesViewerComponent_div_0_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_18_Template_div_click_0_listener() {\n      const productTag_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showProductModal(productTag_r7.product));\n    });\n    i0.ɵɵelementStart(1, \"div\", 40);\n    i0.ɵɵelement(2, \"i\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42)(4, \"span\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 44);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const productTag_r7 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", productTag_r7.position.x, \"%\")(\"top\", productTag_r7.position.y, \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(productTag_r7.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(8, 6, productTag_r7.product.price, \"1.0-0\"), \"\");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.currentStory.caption, \" \");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_23_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.buyNow());\n    });\n    i0.ɵɵelement(2, \"i\", 48);\n    i0.ɵɵtext(3, \" Buy Now \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_23_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart());\n    });\n    i0.ɵɵelement(5, \"i\", 50);\n    i0.ɵɵtext(6, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_23_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist());\n    });\n    i0.ɵɵelement(8, \"i\", 29);\n    i0.ɵɵtext(9, \" Wishlist \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StoriesViewerComponent_div_0_button_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_button_31_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleSound());\n    });\n    i0.ɵɵelement(1, \"i\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"fa-volume-up\", !ctx_r1.isMuted)(\"fa-volume-mute\", ctx_r1.isMuted);\n  }\n}\nfunction StoriesViewerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleStoryClick($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 6)(2, \"div\", 7);\n    i0.ɵɵelement(3, \"img\", 8);\n    i0.ɵɵelementStart(4, \"div\", 9)(5, \"span\", 10);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 11);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 12)(10, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStories());\n    });\n    i0.ɵɵelement(11, \"i\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 15);\n    i0.ɵɵtemplate(13, StoriesViewerComponent_div_0_div_13_Template, 2, 8, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 17);\n    i0.ɵɵtemplate(15, StoriesViewerComponent_div_0_img_15_Template, 2, 2, \"img\", 18)(16, StoriesViewerComponent_div_0_video_16_Template, 2, 5, \"video\", 19);\n    i0.ɵɵelementStart(17, \"div\", 20);\n    i0.ɵɵtemplate(18, StoriesViewerComponent_div_0_div_18_Template, 9, 9, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, StoriesViewerComponent_div_0_div_19_Template, 2, 1, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_20_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_21_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 25);\n    i0.ɵɵtemplate(23, StoriesViewerComponent_div_0_div_23_Template, 10, 0, \"div\", 26);\n    i0.ɵɵelementStart(24, \"div\", 27)(25, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleLike());\n    });\n    i0.ɵɵelement(26, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(28, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareStory());\n    });\n    i0.ɵɵelement(30, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, StoriesViewerComponent_div_0_button_31_Template, 2, 4, \"button\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.user.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentStory.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.currentStory.createdAt));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"video\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentStory.products);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.caption);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.products.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isLiked);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"video\");\n  }\n}\nfunction StoriesViewerComponent_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, ctx_r1.selectedProduct.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction StoriesViewerComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 55);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 56)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelement(6, \"i\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 57);\n    i0.ɵɵelement(8, \"img\", 58);\n    i0.ɵɵelementStart(9, \"div\", 59)(10, \"p\", 60);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 61)(13, \"span\", 62);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, StoriesViewerComponent_div_1_span_16_Template, 3, 4, \"span\", 63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 64)(18, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.buyProductNow());\n    });\n    i0.ɵɵtext(19, \"Buy Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addProductToCart());\n    });\n    i0.ɵɵtext(21, \"Add to Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addProductToWishlist());\n    });\n    i0.ɵɵtext(23, \"Add to Wishlist\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.selectedProduct.images[0] == null ? null : ctx_r1.selectedProduct.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.selectedProduct.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(15, 6, ctx_r1.selectedProduct.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedProduct.originalPrice);\n  }\n}\nfunction StoriesViewerComponent_div_2_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵelement(1, \"img\", 77);\n    i0.ɵɵelementStart(2, \"div\", 78)(3, \"span\", 79);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 80);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 81);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const comment_r12 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", comment_r12.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", comment_r12.user.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(comment_r12.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r12.text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(comment_r12.commentedAt));\n  }\n}\nfunction StoriesViewerComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeComments());\n    });\n    i0.ɵɵelementStart(1, \"div\", 55);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 56)(3, \"h3\");\n    i0.ɵɵtext(4, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeComments());\n    });\n    i0.ɵɵelement(6, \"i\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 70);\n    i0.ɵɵtemplate(8, StoriesViewerComponent_div_2_div_8_Template, 9, 5, \"div\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 72)(10, \"input\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StoriesViewerComponent_div_2_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newComment, $event) || (ctx_r1.newComment = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function StoriesViewerComponent_div_2_Template_input_keyup_enter_10_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addComment());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addComment());\n    });\n    i0.ɵɵelement(12, \"i\", 75);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.comments);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newComment);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.newComment.trim());\n  }\n}\nexport class StoriesViewerComponent {\n  constructor(router, route) {\n    this.router = router;\n    this.route = route;\n    this.stories = [];\n    this.currentIndex = 0;\n    this.isLiked = false;\n    this.isMuted = true;\n    this.selectedProduct = null;\n    this.showCommentsModal = false;\n    this.comments = [];\n    this.newComment = '';\n    this.storyDuration = 15000; // 15 seconds default\n  }\n  ngOnInit() {\n    // Handle query parameters for story index\n    this.route.queryParams.subscribe(queryParams => {\n      if (queryParams['index']) {\n        this.currentIndex = parseInt(queryParams['index'], 10) || 0;\n      }\n    });\n    this.route.params.subscribe(params => {\n      if (params['userId']) {\n        this.loadUserStories(params['userId']);\n      } else {\n        this.loadStories();\n      }\n      if (params['storyId']) {\n        this.jumpToStory(params['storyId']);\n      }\n    });\n    this.setupKeyboardListeners();\n    this.setupTouchListeners();\n  }\n  ngOnDestroy() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n    }\n  }\n  loadStories() {\n    // Load stories from real API\n    fetch('http://localhost:5000/api/stories').then(response => response.json()).then(data => {\n      if (data.success) {\n        this.stories = data.stories.filter(story => story.isActive);\n        if (this.stories.length > 0) {\n          // Use the index from query params or default to 0\n          const startIndex = Math.min(this.currentStoryIndex, this.stories.length - 1);\n          this.currentStoryIndex = startIndex;\n          this.currentStory = this.stories[startIndex];\n          this.startStoryTimer();\n        }\n      }\n    }).catch(error => {\n      console.error('Error loading stories:', error);\n      // Show error message to user\n      this.showErrorMessage('Failed to load stories. Please try again.');\n    });\n  }\n  loadUserStories(userId) {\n    // Load specific user's stories from real API\n    fetch(`http://localhost:5000/api/stories/user/${userId}`).then(response => response.json()).then(data => {\n      if (data.success) {\n        this.stories = data.stories.filter(story => story.isActive);\n        if (this.stories.length > 0) {\n          this.currentStory = this.stories[0];\n          this.startStoryTimer();\n        }\n      }\n    }).catch(error => {\n      console.error('Error loading user stories:', error);\n    });\n  }\n  jumpToStory(storyId) {\n    const index = this.stories.findIndex(s => s._id === storyId);\n    if (index !== -1) {\n      this.currentIndex = index;\n      this.currentStory = this.stories[index];\n      this.startStoryTimer();\n    }\n  }\n  startStoryTimer() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n    }\n    const duration = this.currentStory.media.type === 'video' ? this.currentStory.media.duration * 1000 : this.storyDuration;\n    this.progressTimer = setTimeout(() => {\n      this.nextStory();\n    }, duration);\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.currentIndex++;\n      this.currentStory = this.stories[this.currentIndex];\n      this.startStoryTimer();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.currentIndex--;\n      this.currentStory = this.stories[this.currentIndex];\n      this.startStoryTimer();\n    }\n  }\n  handleStoryClick(event) {\n    const rect = event.target.getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n    if (clickX < width * 0.3) {\n      this.previousStory();\n    } else if (clickX > width * 0.7) {\n      this.nextStory();\n    }\n  }\n  getProgressWidth(index) {\n    if (index < this.currentIndex) return 100;\n    if (index > this.currentIndex) return 0;\n    return 0; // Will be animated by CSS\n  }\n  closeStories() {\n    // Navigate back to the previous page or home\n    if (window.history.length > 1) {\n      window.history.back();\n    } else {\n      this.router.navigate(['/social']);\n    }\n  }\n  // E-commerce actions\n  buyNow() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n  addToCart() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      // TODO: Add to cart via service\n      console.log('Add to cart from story:', product);\n    }\n  }\n  addToWishlist() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      // TODO: Add to wishlist via service\n      console.log('Add to wishlist from story:', product);\n    }\n  }\n  // Social actions\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    // TODO: Like/unlike story via API\n  }\n  openComments() {\n    this.showCommentsModal = true;\n    this.loadComments();\n  }\n  closeComments() {\n    this.showCommentsModal = false;\n  }\n  shareStory() {\n    // TODO: Implement share functionality\n    console.log('Share story:', this.currentStory);\n  }\n  toggleSound() {\n    this.isMuted = !this.isMuted;\n    if (this.storyVideo) {\n      this.storyVideo.nativeElement.muted = this.isMuted;\n    }\n  }\n  // Product modal\n  showProductModal(product) {\n    this.selectedProduct = product;\n  }\n  closeProductModal() {\n    this.selectedProduct = null;\n  }\n  buyProductNow() {\n    if (this.selectedProduct) {\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: this.selectedProduct._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n  addProductToCart() {\n    if (this.selectedProduct) {\n      // TODO: Add to cart via service\n      console.log('Add product to cart:', this.selectedProduct);\n      this.closeProductModal();\n    }\n  }\n  addProductToWishlist() {\n    if (this.selectedProduct) {\n      // TODO: Add to wishlist via service\n      console.log('Add product to wishlist:', this.selectedProduct);\n      this.closeProductModal();\n    }\n  }\n  // Comments\n  loadComments() {\n    // TODO: Load comments from API\n    this.comments = [{\n      user: {\n        username: 'user1',\n        fullName: 'User One',\n        avatar: ''\n      },\n      text: 'Love this product!',\n      commentedAt: new Date()\n    }];\n  }\n  addComment() {\n    if (this.newComment.trim()) {\n      // TODO: Add comment via API\n      console.log('Add comment:', this.newComment);\n      this.newComment = '';\n    }\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diffMs = now.getTime() - new Date(date).getTime();\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    if (diffHours < 1) return 'now';\n    if (diffHours < 24) return `${diffHours}h`;\n    return `${Math.floor(diffHours / 24)}d`;\n  }\n  // Removed mock data - now using real API data from seeder\n  setupKeyboardListeners() {\n    document.addEventListener('keydown', event => {\n      if (event.key === 'ArrowLeft') {\n        this.previousStory();\n      } else if (event.key === 'ArrowRight') {\n        this.nextStory();\n      } else if (event.key === 'Escape') {\n        this.closeStories();\n      }\n    });\n  }\n  setupTouchListeners() {\n    // Touch listeners for mobile swipe gestures\n    let startX = 0;\n    let startY = 0;\n    document.addEventListener('touchstart', event => {\n      startX = event.touches[0].clientX;\n      startY = event.touches[0].clientY;\n    });\n    document.addEventListener('touchend', event => {\n      const endX = event.changedTouches[0].clientX;\n      const endY = event.changedTouches[0].clientY;\n      const diffX = startX - endX;\n      const diffY = startY - endY;\n      // Horizontal swipe\n      if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {\n        if (diffX > 0) {\n          this.nextStory(); // Swipe left - next story\n        } else {\n          this.previousStory(); // Swipe right - previous story\n        }\n      }\n      // Vertical swipe down to close\n      else if (diffY < -100) {\n        this.closeStories();\n      }\n    });\n  }\n  jumpToStory(storyId) {\n    const storyIndex = this.stories.findIndex(story => story._id === storyId);\n    if (storyIndex > -1) {\n      this.currentStoryIndex = storyIndex;\n      this.currentStory = this.stories[storyIndex];\n      this.startStoryTimer();\n    }\n  }\n  showErrorMessage(message) {\n    // TODO: Implement proper toast/notification system\n    console.error('Stories Error:', message);\n    alert(message); // Temporary fallback\n  }\n  static {\n    this.ɵfac = function StoriesViewerComponent_Factory(t) {\n      return new (t || StoriesViewerComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoriesViewerComponent,\n      selectors: [[\"app-stories-viewer\"]],\n      viewQuery: function StoriesViewerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storyVideo = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 3,\n      consts: [[\"storyMedia\", \"\"], [\"storyVideo\", \"\"], [\"class\", \"stories-viewer\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"product-modal\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"comments-modal\", 3, \"click\", 4, \"ngIf\"], [1, \"stories-viewer\", 3, \"click\"], [1, \"story-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"username\"], [1, \"timestamp\"], [1, \"story-controls\"], [1, \"btn-close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"progress-container\"], [\"class\", \"progress-bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-content\"], [\"class\", \"story-media\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"story-media\", 3, \"src\", \"poster\", \"muted\", \"autoplay\", \"loop\", \"ended\", 4, \"ngIf\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"left\", \"top\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"story-caption\", 4, \"ngIf\"], [1, \"nav-area\", \"nav-prev\", 3, \"click\"], [1, \"nav-area\", \"nav-next\", 3, \"click\"], [1, \"story-actions\"], [\"class\", \"ecommerce-actions\", 4, \"ngIf\"], [1, \"social-actions\"], [1, \"social-btn\", \"like\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"social-btn\", \"comment\", 3, \"click\"], [1, \"fas\", \"fa-comment\"], [1, \"social-btn\", \"share\", 3, \"click\"], [1, \"fas\", \"fa-share\"], [\"class\", \"social-btn sound\", 3, \"click\", 4, \"ngIf\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"story-media\", 3, \"src\", \"alt\"], [1, \"story-media\", 3, \"ended\", \"src\", \"poster\", \"muted\", \"autoplay\", \"loop\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"product-tag-info\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"story-caption\"], [1, \"ecommerce-actions\"], [1, \"action-btn\", \"buy-now\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [1, \"action-btn\", \"add-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"action-btn\", \"wishlist\", 3, \"click\"], [1, \"social-btn\", \"sound\", 3, \"click\"], [1, \"fas\"], [1, \"product-modal\", 3, \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"modal-header\"], [1, \"modal-body\"], [1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-details\"], [1, \"brand\"], [1, \"price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"modal-actions\"], [1, \"btn-primary\", 3, \"click\"], [1, \"btn-secondary\", 3, \"click\"], [1, \"btn-outline\", 3, \"click\"], [1, \"original-price\"], [1, \"comments-modal\", 3, \"click\"], [1, \"comments-list\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [1, \"comment-input\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [3, \"click\", \"disabled\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"comment\"], [1, \"comment-avatar\", 3, \"src\", \"alt\"], [1, \"comment-content\"], [1, \"comment-username\"], [1, \"comment-text\"], [1, \"comment-time\"]],\n      template: function StoriesViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, StoriesViewerComponent_div_0_Template, 32, 13, \"div\", 2)(1, StoriesViewerComponent_div_1_Template, 24, 9, \"div\", 3)(2, StoriesViewerComponent_div_2_Template, 13, 3, \"div\", 4);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStory);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedProduct);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showCommentsModal);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\".stories-viewer[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 1000;\\n  display: flex;\\n  flex-direction: column;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.story-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  position: relative;\\n  z-index: 10;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 2px solid #fff;\\n}\\n\\n.user-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  color: #fff;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n}\\n\\n.timestamp[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 0.8rem;\\n}\\n\\n.btn-close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 1.2rem;\\n  cursor: pointer;\\n  padding: 8px;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n  padding: 0 20px;\\n  position: absolute;\\n  top: 8px;\\n  left: 0;\\n  right: 0;\\n  z-index: 10;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 1px;\\n  overflow: hidden;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #fff;\\n  width: 0%;\\n  transition: width 0.1s ease;\\n}\\n\\n.progress-bar.active[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progress linear;\\n}\\n\\n.progress-bar.completed[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progress {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n.story-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.story-media[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  max-height: 100%;\\n  object-fit: contain;\\n}\\n\\n.product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: all;\\n  cursor: pointer;\\n  transform: translate(-50%, -50%);\\n}\\n\\n.product-tag-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #333;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.product-tag-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 40px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(0, 0, 0, 0.8);\\n  color: #fff;\\n  padding: 8px 12px;\\n  border-radius: 8px;\\n  white-space: nowrap;\\n  font-size: 0.8rem;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.product-tag[_ngcontent-%COMP%]:hover   .product-tag-info[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: translate(-50%, -50%) scale(1);\\n  }\\n  50% {\\n    transform: translate(-50%, -50%) scale(1.1);\\n  }\\n}\\n.story-caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 120px;\\n  left: 20px;\\n  right: 20px;\\n  color: #fff;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n  background: rgba(0, 0, 0, 0.5);\\n  padding: 12px;\\n  border-radius: 8px;\\n}\\n\\n.nav-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 30%;\\n  cursor: pointer;\\n  z-index: 5;\\n}\\n\\n.nav-prev[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n\\n.nav-next[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n\\n.story-actions[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n}\\n\\n.ecommerce-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px;\\n  border: none;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  transition: all 0.2s ease;\\n}\\n\\n.buy-now[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n  color: #fff;\\n}\\n\\n.add-cart[_ngcontent-%COMP%] {\\n  background: #4ecdc4;\\n  color: #fff;\\n}\\n\\n.wishlist[_ngcontent-%COMP%] {\\n  background: #ff9ff3;\\n  color: #fff;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\\n}\\n\\n.social-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 24px;\\n}\\n\\n.social-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border: none;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.2);\\n  color: #fff;\\n  font-size: 1.1rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n}\\n\\n.social-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: scale(1.1);\\n}\\n\\n.social-btn.liked[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n  color: #fff;\\n}\\n\\n.product-modal[_ngcontent-%COMP%], .comments-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.8);\\n  z-index: 1100;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 12px;\\n  max-width: 400px;\\n  width: 100%;\\n  max-height: 80vh;\\n  overflow-y: auto;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n  border-bottom: 1px solid #eee;\\n}\\n\\n.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  object-fit: cover;\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.product-details[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.brand[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-bottom: 8px;\\n}\\n\\n.price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.current-price[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 700;\\n  color: #333;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #999;\\n  text-decoration: line-through;\\n}\\n\\n.modal-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%], .btn-outline[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  border: none;\\n  border-radius: 6px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: #fff;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: #6c757d;\\n  color: #fff;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: #007bff;\\n  border: 1px solid #007bff;\\n}\\n\\n.comments-list[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow-y: auto;\\n  margin-bottom: 16px;\\n}\\n\\n.comment[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n\\n.comment-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n}\\n\\n.comment-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.comment-username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  color: #333;\\n}\\n\\n.comment-text[_ngcontent-%COMP%] {\\n  margin: 4px 0;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n}\\n\\n.comment-time[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.comment-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  align-items: center;\\n  padding-top: 16px;\\n  border-top: 1px solid #eee;\\n}\\n\\n.comment-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 20px;\\n  font-size: 0.9rem;\\n}\\n\\n.comment-input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border: none;\\n  border-radius: 50%;\\n  background: #007bff;\\n  color: #fff;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.comment-input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n@media (max-width: 768px) {\\n  .ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .action-btn[_ngcontent-%COMP%] {\\n    padding: 14px;\\n    font-size: 1rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵclassProp", "i_r4", "ctx_r1", "currentIndex", "ɵɵadvance", "ɵɵstyleProp", "getProgressWidth", "story_r3", "media", "duration", "ɵɵproperty", "currentStory", "url", "ɵɵsanitizeUrl", "caption", "ɵɵlistener", "StoriesViewerComponent_div_0_video_16_Template_video_ended_0_listener", "ɵɵrestoreView", "_r5", "ɵɵnextContext", "ɵɵresetView", "nextStory", "thumbnail", "isMuted", "StoriesViewerComponent_div_0_div_18_Template_div_click_0_listener", "productTag_r7", "_r6", "$implicit", "showProductModal", "product", "ɵɵtext", "position", "x", "y", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "price", "StoriesViewerComponent_div_0_div_23_Template_button_click_1_listener", "_r8", "buyNow", "StoriesViewerComponent_div_0_div_23_Template_button_click_4_listener", "addToCart", "StoriesViewerComponent_div_0_div_23_Template_button_click_7_listener", "addToWishlist", "StoriesViewerComponent_div_0_button_31_Template_button_click_0_listener", "_r9", "toggleSound", "StoriesViewerComponent_div_0_Template_div_click_0_listener", "$event", "_r1", "handleStoryClick", "StoriesViewerComponent_div_0_Template_button_click_10_listener", "closeStories", "ɵɵtemplate", "StoriesViewerComponent_div_0_div_13_Template", "StoriesViewerComponent_div_0_img_15_Template", "StoriesViewerComponent_div_0_video_16_Template", "StoriesViewerComponent_div_0_div_18_Template", "StoriesViewerComponent_div_0_div_19_Template", "StoriesViewerComponent_div_0_Template_div_click_20_listener", "previousStory", "StoriesViewerComponent_div_0_Template_div_click_21_listener", "StoriesViewerComponent_div_0_div_23_Template", "StoriesViewerComponent_div_0_Template_button_click_25_listener", "toggleLike", "StoriesViewerComponent_div_0_Template_button_click_27_listener", "openComments", "StoriesViewerComponent_div_0_Template_button_click_29_listener", "shareStory", "StoriesViewerComponent_div_0_button_31_Template", "user", "avatar", "fullName", "username", "getTimeAgo", "createdAt", "stories", "type", "products", "length", "isLiked", "selectedProduct", "originalPrice", "StoriesViewerComponent_div_1_Template_div_click_0_listener", "_r10", "closeProductModal", "StoriesViewerComponent_div_1_Template_div_click_1_listener", "stopPropagation", "StoriesViewerComponent_div_1_Template_button_click_5_listener", "StoriesViewerComponent_div_1_span_16_Template", "StoriesViewerComponent_div_1_Template_button_click_18_listener", "buyProductNow", "StoriesViewerComponent_div_1_Template_button_click_20_listener", "addProductToCart", "StoriesViewerComponent_div_1_Template_button_click_22_listener", "addProductToWishlist", "images", "brand", "comment_r12", "text", "commentedAt", "StoriesViewerComponent_div_2_Template_div_click_0_listener", "_r11", "closeComments", "StoriesViewerComponent_div_2_Template_div_click_1_listener", "StoriesViewerComponent_div_2_Template_button_click_5_listener", "StoriesViewerComponent_div_2_div_8_Template", "ɵɵtwoWayListener", "StoriesViewerComponent_div_2_Template_input_ngModelChange_10_listener", "ɵɵtwoWayBindingSet", "newComment", "StoriesViewerComponent_div_2_Template_input_keyup_enter_10_listener", "addComment", "StoriesViewerComponent_div_2_Template_button_click_11_listener", "comments", "ɵɵtwoWayProperty", "trim", "StoriesViewerComponent", "constructor", "router", "route", "showCommentsModal", "storyDuration", "ngOnInit", "queryParams", "subscribe", "parseInt", "params", "loadUserStories", "loadStories", "jumpToStory", "setupKeyboardListeners", "setupTouchListeners", "ngOnDestroy", "progressTimer", "clearTimeout", "fetch", "then", "response", "json", "data", "success", "filter", "story", "isActive", "startIndex", "Math", "min", "currentStoryIndex", "startStoryTimer", "catch", "error", "console", "showErrorMessage", "userId", "storyId", "index", "findIndex", "s", "_id", "setTimeout", "event", "rect", "target", "getBoundingClientRect", "clickX", "clientX", "left", "width", "window", "history", "back", "navigate", "productId", "source", "log", "loadComments", "storyVideo", "nativeElement", "muted", "Date", "date", "now", "diffMs", "getTime", "diffHours", "floor", "document", "addEventListener", "key", "startX", "startY", "touches", "clientY", "endX", "changedTouches", "endY", "diffX", "diffY", "abs", "storyIndex", "message", "alert", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "selectors", "viewQuery", "StoriesViewerComponent_Query", "rf", "ctx", "StoriesViewerComponent_div_0_Template", "StoriesViewerComponent_div_1_Template", "StoriesViewerComponent_div_2_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\stories\\stories-viewer.component.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>estroy, ViewChild, ElementRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\n\ninterface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar?: string;\n  };\n  media: {\n    type: 'image' | 'video';\n    url: string;\n    duration: number;\n    thumbnail?: string;\n  };\n  caption?: string;\n  products: {\n    _id: string;\n    product: {\n      _id: string;\n      name: string;\n      price: number;\n      originalPrice?: number;\n      images: { url: string; alt: string }[];\n      brand: string;\n    };\n    position: { x: number; y: number };\n  }[];\n  viewers: { user: string; viewedAt: Date }[];\n  isActive: boolean;\n  expiresAt: Date;\n  createdAt: Date;\n}\n\n@Component({\n  selector: 'app-stories-viewer',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"stories-viewer\" *ngIf=\"currentStory\" (click)=\"handleStoryClick($event)\">\n      <!-- Header -->\n      <div class=\"story-header\">\n        <div class=\"user-info\">\n          <img [src]=\"currentStory.user.avatar || '/assets/images/default-avatar.png'\" \n               [alt]=\"currentStory.user.fullName\" class=\"user-avatar\">\n          <div class=\"user-details\">\n            <span class=\"username\">{{ currentStory.user.username }}</span>\n            <span class=\"timestamp\">{{ getTimeAgo(currentStory.createdAt) }}</span>\n          </div>\n        </div>\n        \n        <div class=\"story-controls\">\n          <button class=\"btn-close\" (click)=\"closeStories()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n      </div>\n\n      <!-- Progress Bars -->\n      <div class=\"progress-container\">\n        <div class=\"progress-bar\" \n             *ngFor=\"let story of stories; let i = index\"\n             [class.active]=\"i === currentIndex\"\n             [class.completed]=\"i < currentIndex\">\n          <div class=\"progress-fill\" \n               [style.width.%]=\"getProgressWidth(i)\"\n               [style.animation-duration.s]=\"story.media.duration\"></div>\n        </div>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story-content\">\n        <!-- Image Story -->\n        <img *ngIf=\"currentStory.media.type === 'image'\" \n             [src]=\"currentStory.media.url\" \n             [alt]=\"currentStory.caption\"\n             class=\"story-media\"\n             #storyMedia>\n\n        <!-- Video Story -->\n        <video *ngIf=\"currentStory.media.type === 'video'\"\n               [src]=\"currentStory.media.url\"\n               class=\"story-media\"\n               [poster]=\"currentStory.media.thumbnail\"\n               [muted]=\"isMuted\"\n               [autoplay]=\"true\"\n               [loop]=\"false\"\n               #storyVideo\n               (ended)=\"nextStory()\">\n        </video>\n\n        <!-- Product Tags -->\n        <div class=\"product-tags\">\n          <div class=\"product-tag\" \n               *ngFor=\"let productTag of currentStory.products\"\n               [style.left.%]=\"productTag.position.x\"\n               [style.top.%]=\"productTag.position.y\"\n               (click)=\"showProductModal(productTag.product)\">\n            <div class=\"product-tag-icon\">\n              <i class=\"fas fa-shopping-bag\"></i>\n            </div>\n            <div class=\"product-tag-info\">\n              <span class=\"product-name\">{{ productTag.product.name }}</span>\n              <span class=\"product-price\">₹{{ productTag.product.price | number:'1.0-0' }}</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Caption -->\n        <div class=\"story-caption\" *ngIf=\"currentStory.caption\">\n          {{ currentStory.caption }}\n        </div>\n      </div>\n\n      <!-- Navigation Areas -->\n      <div class=\"nav-area nav-prev\" (click)=\"previousStory()\"></div>\n      <div class=\"nav-area nav-next\" (click)=\"nextStory()\"></div>\n\n      <!-- Bottom Actions -->\n      <div class=\"story-actions\">\n        <!-- E-commerce Actions -->\n        <div class=\"ecommerce-actions\" *ngIf=\"currentStory.products.length > 0\">\n          <button class=\"action-btn buy-now\" (click)=\"buyNow()\">\n            <i class=\"fas fa-bolt\"></i>\n            Buy Now\n          </button>\n          <button class=\"action-btn add-cart\" (click)=\"addToCart()\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            Add to Cart\n          </button>\n          <button class=\"action-btn wishlist\" (click)=\"addToWishlist()\">\n            <i class=\"fas fa-heart\"></i>\n            Wishlist\n          </button>\n        </div>\n\n        <!-- Social Actions -->\n        <div class=\"social-actions\">\n          <button class=\"social-btn like\" [class.liked]=\"isLiked\" (click)=\"toggleLike()\">\n            <i class=\"fas fa-heart\"></i>\n          </button>\n          <button class=\"social-btn comment\" (click)=\"openComments()\">\n            <i class=\"fas fa-comment\"></i>\n          </button>\n          <button class=\"social-btn share\" (click)=\"shareStory()\">\n            <i class=\"fas fa-share\"></i>\n          </button>\n          <button class=\"social-btn sound\" (click)=\"toggleSound()\" *ngIf=\"currentStory.media.type === 'video'\">\n            <i class=\"fas\" [class.fa-volume-up]=\"!isMuted\" [class.fa-volume-mute]=\"isMuted\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Product Modal -->\n    <div class=\"product-modal\" *ngIf=\"selectedProduct\" (click)=\"closeProductModal()\">\n      <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n        <div class=\"modal-header\">\n          <h3>{{ selectedProduct.name }}</h3>\n          <button class=\"btn-close\" (click)=\"closeProductModal()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        \n        <div class=\"modal-body\">\n          <img [src]=\"selectedProduct.images[0]?.url\" [alt]=\"selectedProduct.name\" class=\"product-image\">\n          \n          <div class=\"product-details\">\n            <p class=\"brand\">{{ selectedProduct.brand }}</p>\n            <div class=\"price\">\n              <span class=\"current-price\">₹{{ selectedProduct.price | number:'1.0-0' }}</span>\n              <span class=\"original-price\" *ngIf=\"selectedProduct.originalPrice\">\n                ₹{{ selectedProduct.originalPrice | number:'1.0-0' }}\n              </span>\n            </div>\n          </div>\n          \n          <div class=\"modal-actions\">\n            <button class=\"btn-primary\" (click)=\"buyProductNow()\">Buy Now</button>\n            <button class=\"btn-secondary\" (click)=\"addProductToCart()\">Add to Cart</button>\n            <button class=\"btn-outline\" (click)=\"addProductToWishlist()\">Add to Wishlist</button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Comments Modal -->\n    <div class=\"comments-modal\" *ngIf=\"showCommentsModal\" (click)=\"closeComments()\">\n      <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n        <div class=\"modal-header\">\n          <h3>Comments</h3>\n          <button class=\"btn-close\" (click)=\"closeComments()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        \n        <div class=\"comments-list\">\n          <div class=\"comment\" *ngFor=\"let comment of comments\">\n            <img [src]=\"comment.user.avatar || '/assets/images/default-avatar.png'\" \n                 [alt]=\"comment.user.fullName\" class=\"comment-avatar\">\n            <div class=\"comment-content\">\n              <span class=\"comment-username\">{{ comment.user.username }}</span>\n              <p class=\"comment-text\">{{ comment.text }}</p>\n              <span class=\"comment-time\">{{ getTimeAgo(comment.commentedAt) }}</span>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"comment-input\">\n          <input type=\"text\" \n                 [(ngModel)]=\"newComment\" \n                 placeholder=\"Add a comment...\"\n                 (keyup.enter)=\"addComment()\">\n          <button (click)=\"addComment()\" [disabled]=\"!newComment.trim()\">\n            <i class=\"fas fa-paper-plane\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .stories-viewer {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: #000;\n      z-index: 1000;\n      display: flex;\n      flex-direction: column;\n      user-select: none;\n    }\n\n    .story-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 20px;\n      background: linear-gradient(180deg, rgba(0,0,0,0.6) 0%, transparent 100%);\n      position: relative;\n      z-index: 10;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .user-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      border: 2px solid #fff;\n    }\n\n    .user-details {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .username {\n      color: #fff;\n      font-weight: 600;\n      font-size: 0.9rem;\n    }\n\n    .timestamp {\n      color: rgba(255,255,255,0.7);\n      font-size: 0.8rem;\n    }\n\n    .btn-close {\n      background: none;\n      border: none;\n      color: #fff;\n      font-size: 1.2rem;\n      cursor: pointer;\n      padding: 8px;\n    }\n\n    .progress-container {\n      display: flex;\n      gap: 2px;\n      padding: 0 20px;\n      position: absolute;\n      top: 8px;\n      left: 0;\n      right: 0;\n      z-index: 10;\n    }\n\n    .progress-bar {\n      flex: 1;\n      height: 2px;\n      background: rgba(255,255,255,0.3);\n      border-radius: 1px;\n      overflow: hidden;\n    }\n\n    .progress-fill {\n      height: 100%;\n      background: #fff;\n      width: 0%;\n      transition: width 0.1s ease;\n    }\n\n    .progress-bar.active .progress-fill {\n      animation: progress linear;\n    }\n\n    .progress-bar.completed .progress-fill {\n      width: 100%;\n    }\n\n    @keyframes progress {\n      from { width: 0%; }\n      to { width: 100%; }\n    }\n\n    .story-content {\n      flex: 1;\n      position: relative;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .story-media {\n      max-width: 100%;\n      max-height: 100%;\n      object-fit: contain;\n    }\n\n    .product-tags {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      pointer-events: none;\n    }\n\n    .product-tag {\n      position: absolute;\n      pointer-events: all;\n      cursor: pointer;\n      transform: translate(-50%, -50%);\n    }\n\n    .product-tag-icon {\n      width: 32px;\n      height: 32px;\n      background: rgba(255,255,255,0.9);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #333;\n      animation: pulse 2s infinite;\n    }\n\n    .product-tag-info {\n      position: absolute;\n      top: 40px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0,0,0,0.8);\n      color: #fff;\n      padding: 8px 12px;\n      border-radius: 8px;\n      white-space: nowrap;\n      font-size: 0.8rem;\n      opacity: 0;\n      transition: opacity 0.3s ease;\n    }\n\n    .product-tag:hover .product-tag-info {\n      opacity: 1;\n    }\n\n    @keyframes pulse {\n      0%, 100% { transform: translate(-50%, -50%) scale(1); }\n      50% { transform: translate(-50%, -50%) scale(1.1); }\n    }\n\n    .story-caption {\n      position: absolute;\n      bottom: 120px;\n      left: 20px;\n      right: 20px;\n      color: #fff;\n      font-size: 0.9rem;\n      line-height: 1.4;\n      background: rgba(0,0,0,0.5);\n      padding: 12px;\n      border-radius: 8px;\n    }\n\n    .nav-area {\n      position: absolute;\n      top: 0;\n      bottom: 0;\n      width: 30%;\n      cursor: pointer;\n      z-index: 5;\n    }\n\n    .nav-prev {\n      left: 0;\n    }\n\n    .nav-next {\n      right: 0;\n    }\n\n    .story-actions {\n      padding: 20px;\n      background: linear-gradient(0deg, rgba(0,0,0,0.6) 0%, transparent 100%);\n    }\n\n    .ecommerce-actions {\n      display: flex;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n\n    .action-btn {\n      flex: 1;\n      padding: 12px;\n      border: none;\n      border-radius: 8px;\n      font-weight: 600;\n      font-size: 0.9rem;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      transition: all 0.2s ease;\n    }\n\n    .buy-now {\n      background: #ff6b6b;\n      color: #fff;\n    }\n\n    .add-cart {\n      background: #4ecdc4;\n      color: #fff;\n    }\n\n    .wishlist {\n      background: #ff9ff3;\n      color: #fff;\n    }\n\n    .action-btn:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 4px 12px rgba(0,0,0,0.3);\n    }\n\n    .social-actions {\n      display: flex;\n      justify-content: center;\n      gap: 24px;\n    }\n\n    .social-btn {\n      width: 44px;\n      height: 44px;\n      border: none;\n      border-radius: 50%;\n      background: rgba(255,255,255,0.2);\n      color: #fff;\n      font-size: 1.1rem;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      transition: all 0.2s ease;\n    }\n\n    .social-btn:hover {\n      background: rgba(255,255,255,0.3);\n      transform: scale(1.1);\n    }\n\n    .social-btn.liked {\n      background: #ff6b6b;\n      color: #fff;\n    }\n\n    .product-modal, .comments-modal {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: rgba(0,0,0,0.8);\n      z-index: 1100;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n    }\n\n    .modal-content {\n      background: #fff;\n      border-radius: 12px;\n      max-width: 400px;\n      width: 100%;\n      max-height: 80vh;\n      overflow-y: auto;\n    }\n\n    .modal-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 20px;\n      border-bottom: 1px solid #eee;\n    }\n\n    .modal-header h3 {\n      margin: 0;\n      font-size: 1.1rem;\n      font-weight: 600;\n    }\n\n    .modal-body {\n      padding: 20px;\n    }\n\n    .product-image {\n      width: 100%;\n      height: 200px;\n      object-fit: cover;\n      border-radius: 8px;\n      margin-bottom: 16px;\n    }\n\n    .product-details {\n      margin-bottom: 20px;\n    }\n\n    .brand {\n      color: #666;\n      font-size: 0.9rem;\n      margin-bottom: 8px;\n    }\n\n    .price {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .current-price {\n      font-size: 1.2rem;\n      font-weight: 700;\n      color: #333;\n    }\n\n    .original-price {\n      font-size: 0.9rem;\n      color: #999;\n      text-decoration: line-through;\n    }\n\n    .modal-actions {\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n    }\n\n    .btn-primary, .btn-secondary, .btn-outline {\n      padding: 12px;\n      border: none;\n      border-radius: 6px;\n      font-weight: 600;\n      cursor: pointer;\n      transition: all 0.2s ease;\n    }\n\n    .btn-primary {\n      background: #007bff;\n      color: #fff;\n    }\n\n    .btn-secondary {\n      background: #6c757d;\n      color: #fff;\n    }\n\n    .btn-outline {\n      background: transparent;\n      color: #007bff;\n      border: 1px solid #007bff;\n    }\n\n    .comments-list {\n      max-height: 300px;\n      overflow-y: auto;\n      margin-bottom: 16px;\n    }\n\n    .comment {\n      display: flex;\n      gap: 12px;\n      margin-bottom: 16px;\n    }\n\n    .comment-avatar {\n      width: 32px;\n      height: 32px;\n      border-radius: 50%;\n    }\n\n    .comment-content {\n      flex: 1;\n    }\n\n    .comment-username {\n      font-weight: 600;\n      font-size: 0.9rem;\n      color: #333;\n    }\n\n    .comment-text {\n      margin: 4px 0;\n      font-size: 0.9rem;\n      line-height: 1.4;\n    }\n\n    .comment-time {\n      font-size: 0.8rem;\n      color: #666;\n    }\n\n    .comment-input {\n      display: flex;\n      gap: 8px;\n      align-items: center;\n      padding-top: 16px;\n      border-top: 1px solid #eee;\n    }\n\n    .comment-input input {\n      flex: 1;\n      padding: 8px 12px;\n      border: 1px solid #ddd;\n      border-radius: 20px;\n      font-size: 0.9rem;\n    }\n\n    .comment-input button {\n      width: 36px;\n      height: 36px;\n      border: none;\n      border-radius: 50%;\n      background: #007bff;\n      color: #fff;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .comment-input button:disabled {\n      background: #ccc;\n      cursor: not-allowed;\n    }\n\n    @media (max-width: 768px) {\n      .ecommerce-actions {\n        flex-direction: column;\n      }\n      \n      .action-btn {\n        padding: 14px;\n        font-size: 1rem;\n      }\n    }\n  `]\n})\nexport class StoriesViewerComponent implements OnInit, OnDestroy {\n  @ViewChild('storyVideo') storyVideo!: ElementRef<HTMLVideoElement>;\n  \n  stories: Story[] = [];\n  currentIndex = 0;\n  currentStory!: Story;\n  isLiked = false;\n  isMuted = true;\n  selectedProduct: any = null;\n  showCommentsModal = false;\n  comments: any[] = [];\n  newComment = '';\n  \n  private progressTimer: any;\n  private storyDuration = 15000; // 15 seconds default\n\n  constructor(\n    private router: Router,\n    private route: ActivatedRoute\n  ) {}\n\n  ngOnInit() {\n    // Handle query parameters for story index\n    this.route.queryParams.subscribe(queryParams => {\n      if (queryParams['index']) {\n        this.currentIndex = parseInt(queryParams['index'], 10) || 0;\n      }\n    });\n\n    this.route.params.subscribe(params => {\n      if (params['userId']) {\n        this.loadUserStories(params['userId']);\n      } else {\n        this.loadStories();\n      }\n      if (params['storyId']) {\n        this.jumpToStory(params['storyId']);\n      }\n    });\n\n    this.setupKeyboardListeners();\n    this.setupTouchListeners();\n  }\n\n  ngOnDestroy() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n    }\n  }\n\n  loadStories() {\n    // Load stories from real API\n    fetch('http://localhost:5000/api/stories')\n      .then(response => response.json())\n      .then(data => {\n        if (data.success) {\n          this.stories = data.stories.filter((story: any) => story.isActive);\n          if (this.stories.length > 0) {\n            // Use the index from query params or default to 0\n            const startIndex = Math.min(this.currentStoryIndex, this.stories.length - 1);\n            this.currentStoryIndex = startIndex;\n            this.currentStory = this.stories[startIndex];\n            this.startStoryTimer();\n          }\n        }\n      })\n      .catch(error => {\n        console.error('Error loading stories:', error);\n        // Show error message to user\n        this.showErrorMessage('Failed to load stories. Please try again.');\n      });\n  }\n\n  loadUserStories(userId: string) {\n    // Load specific user's stories from real API\n    fetch(`http://localhost:5000/api/stories/user/${userId}`)\n      .then(response => response.json())\n      .then(data => {\n        if (data.success) {\n          this.stories = data.stories.filter((story: any) => story.isActive);\n          if (this.stories.length > 0) {\n            this.currentStory = this.stories[0];\n            this.startStoryTimer();\n          }\n        }\n      })\n      .catch(error => {\n        console.error('Error loading user stories:', error);\n      });\n  }\n\n  jumpToStory(storyId: string) {\n    const index = this.stories.findIndex(s => s._id === storyId);\n    if (index !== -1) {\n      this.currentIndex = index;\n      this.currentStory = this.stories[index];\n      this.startStoryTimer();\n    }\n  }\n\n  startStoryTimer() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n    }\n    \n    const duration = this.currentStory.media.type === 'video' \n      ? this.currentStory.media.duration * 1000 \n      : this.storyDuration;\n    \n    this.progressTimer = setTimeout(() => {\n      this.nextStory();\n    }, duration);\n  }\n\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.currentIndex++;\n      this.currentStory = this.stories[this.currentIndex];\n      this.startStoryTimer();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.currentIndex--;\n      this.currentStory = this.stories[this.currentIndex];\n      this.startStoryTimer();\n    }\n  }\n\n  handleStoryClick(event: MouseEvent) {\n    const rect = (event.target as HTMLElement).getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n    \n    if (clickX < width * 0.3) {\n      this.previousStory();\n    } else if (clickX > width * 0.7) {\n      this.nextStory();\n    }\n  }\n\n  getProgressWidth(index: number): number {\n    if (index < this.currentIndex) return 100;\n    if (index > this.currentIndex) return 0;\n    return 0; // Will be animated by CSS\n  }\n\n  closeStories() {\n    // Navigate back to the previous page or home\n    if (window.history.length > 1) {\n      window.history.back();\n    } else {\n      this.router.navigate(['/social']);\n    }\n  }\n\n  // E-commerce actions\n  buyNow() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      this.router.navigate(['/checkout'], { \n        queryParams: { productId: product._id, source: 'story' } \n      });\n    }\n  }\n\n  addToCart() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      // TODO: Add to cart via service\n      console.log('Add to cart from story:', product);\n    }\n  }\n\n  addToWishlist() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      // TODO: Add to wishlist via service\n      console.log('Add to wishlist from story:', product);\n    }\n  }\n\n  // Social actions\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    // TODO: Like/unlike story via API\n  }\n\n  openComments() {\n    this.showCommentsModal = true;\n    this.loadComments();\n  }\n\n  closeComments() {\n    this.showCommentsModal = false;\n  }\n\n  shareStory() {\n    // TODO: Implement share functionality\n    console.log('Share story:', this.currentStory);\n  }\n\n  toggleSound() {\n    this.isMuted = !this.isMuted;\n    if (this.storyVideo) {\n      this.storyVideo.nativeElement.muted = this.isMuted;\n    }\n  }\n\n  // Product modal\n  showProductModal(product: any) {\n    this.selectedProduct = product;\n  }\n\n  closeProductModal() {\n    this.selectedProduct = null;\n  }\n\n  buyProductNow() {\n    if (this.selectedProduct) {\n      this.router.navigate(['/checkout'], { \n        queryParams: { productId: this.selectedProduct._id, source: 'story' } \n      });\n    }\n  }\n\n  addProductToCart() {\n    if (this.selectedProduct) {\n      // TODO: Add to cart via service\n      console.log('Add product to cart:', this.selectedProduct);\n      this.closeProductModal();\n    }\n  }\n\n  addProductToWishlist() {\n    if (this.selectedProduct) {\n      // TODO: Add to wishlist via service\n      console.log('Add product to wishlist:', this.selectedProduct);\n      this.closeProductModal();\n    }\n  }\n\n  // Comments\n  loadComments() {\n    // TODO: Load comments from API\n    this.comments = [\n      {\n        user: { username: 'user1', fullName: 'User One', avatar: '' },\n        text: 'Love this product!',\n        commentedAt: new Date()\n      }\n    ];\n  }\n\n  addComment() {\n    if (this.newComment.trim()) {\n      // TODO: Add comment via API\n      console.log('Add comment:', this.newComment);\n      this.newComment = '';\n    }\n  }\n\n  getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diffMs = now.getTime() - new Date(date).getTime();\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    \n    if (diffHours < 1) return 'now';\n    if (diffHours < 24) return `${diffHours}h`;\n    return `${Math.floor(diffHours / 24)}d`;\n  }\n\n  // Removed mock data - now using real API data from seeder\n\n  setupKeyboardListeners() {\n    document.addEventListener('keydown', (event) => {\n      if (event.key === 'ArrowLeft') {\n        this.previousStory();\n      } else if (event.key === 'ArrowRight') {\n        this.nextStory();\n      } else if (event.key === 'Escape') {\n        this.closeStories();\n      }\n    });\n  }\n\n  setupTouchListeners() {\n    // Touch listeners for mobile swipe gestures\n    let startX = 0;\n    let startY = 0;\n\n    document.addEventListener('touchstart', (event) => {\n      startX = event.touches[0].clientX;\n      startY = event.touches[0].clientY;\n    });\n\n    document.addEventListener('touchend', (event) => {\n      const endX = event.changedTouches[0].clientX;\n      const endY = event.changedTouches[0].clientY;\n      const diffX = startX - endX;\n      const diffY = startY - endY;\n\n      // Horizontal swipe\n      if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {\n        if (diffX > 0) {\n          this.nextStory(); // Swipe left - next story\n        } else {\n          this.previousStory(); // Swipe right - previous story\n        }\n      }\n      // Vertical swipe down to close\n      else if (diffY < -100) {\n        this.closeStories();\n      }\n    });\n  }\n\n  jumpToStory(storyId: string) {\n    const storyIndex = this.stories.findIndex(story => story._id === storyId);\n    if (storyIndex > -1) {\n      this.currentStoryIndex = storyIndex;\n      this.currentStory = this.stories[storyIndex];\n      this.startStoryTimer();\n    }\n  }\n\n  showErrorMessage(message: string) {\n    // TODO: Implement proper toast/notification system\n    console.error('Stories Error:', message);\n    alert(message); // Temporary fallback\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;IA6DpCC,EAAA,CAAAC,cAAA,cAG0C;IACxCD,EAAA,CAAAE,SAAA,cAE+D;IACjEF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAJDH,EADA,CAAAI,WAAA,WAAAC,IAAA,KAAAC,MAAA,CAAAC,YAAA,CAAmC,cAAAF,IAAA,GAAAC,MAAA,CAAAC,YAAA,CACC;IAElCP,EAAA,CAAAQ,SAAA,EAAqC;IACrCR,EADA,CAAAS,WAAA,UAAAH,MAAA,CAAAI,gBAAA,CAAAL,IAAA,OAAqC,uBAAAM,QAAA,CAAAC,KAAA,CAAAC,QAAA,MACc;;;;;IAO1Db,EAAA,CAAAE,SAAA,iBAIiB;;;;IAFZF,EADA,CAAAc,UAAA,QAAAR,MAAA,CAAAS,YAAA,CAAAH,KAAA,CAAAI,GAAA,EAAAhB,EAAA,CAAAiB,aAAA,CAA8B,QAAAX,MAAA,CAAAS,YAAA,CAAAG,OAAA,CACF;;;;;;IAKjClB,EAAA,CAAAC,cAAA,mBAQ6B;IAAtBD,EAAA,CAAAmB,UAAA,mBAAAC,sEAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAmB,SAAA,EAAW;IAAA,EAAC;IAC5BzB,EAAA,CAAAG,YAAA,EAAQ;;;;IAHDH,EALA,CAAAc,UAAA,QAAAR,MAAA,CAAAS,YAAA,CAAAH,KAAA,CAAAI,GAAA,EAAAhB,EAAA,CAAAiB,aAAA,CAA8B,WAAAX,MAAA,CAAAS,YAAA,CAAAH,KAAA,CAAAc,SAAA,EAAA1B,EAAA,CAAAiB,aAAA,CAES,UAAAX,MAAA,CAAAqB,OAAA,CACtB,kBACA,eACH;;;;;;IAOnB3B,EAAA,CAAAC,cAAA,cAIoD;IAA/CD,EAAA,CAAAmB,UAAA,mBAAAS,kEAAA;MAAA,MAAAC,aAAA,GAAA7B,EAAA,CAAAqB,aAAA,CAAAS,GAAA,EAAAC,SAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAA0B,gBAAA,CAAAH,aAAA,CAAAI,OAAA,CAAoC;IAAA,EAAC;IACjDjC,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA8B,eACD;IAAAD,EAAA,CAAAkC,MAAA,GAA6B;IAAAlC,EAAA,CAAAG,YAAA,EAAO;IAC/DH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAkC,MAAA,GAAgD;;IAEhFlC,EAFgF,CAAAG,YAAA,EAAO,EAC/E,EACF;;;;IATDH,EADA,CAAAS,WAAA,SAAAoB,aAAA,CAAAM,QAAA,CAAAC,CAAA,MAAsC,QAAAP,aAAA,CAAAM,QAAA,CAAAE,CAAA,MACD;IAMXrC,EAAA,CAAAQ,SAAA,GAA6B;IAA7BR,EAAA,CAAAsC,iBAAA,CAAAT,aAAA,CAAAI,OAAA,CAAAM,IAAA,CAA6B;IAC5BvC,EAAA,CAAAQ,SAAA,GAAgD;IAAhDR,EAAA,CAAAwC,kBAAA,WAAAxC,EAAA,CAAAyC,WAAA,OAAAZ,aAAA,CAAAI,OAAA,CAAAS,KAAA,eAAgD;;;;;IAMlF1C,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAkC,MAAA,GACF;IAAAlC,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAwC,kBAAA,MAAAlC,MAAA,CAAAS,YAAA,CAAAG,OAAA,MACF;;;;;;IAWElB,EADF,CAAAC,cAAA,cAAwE,iBAChB;IAAnBD,EAAA,CAAAmB,UAAA,mBAAAwB,qEAAA;MAAA3C,EAAA,CAAAqB,aAAA,CAAAuB,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAuC,MAAA,EAAQ;IAAA,EAAC;IACnD7C,EAAA,CAAAE,SAAA,YAA2B;IAC3BF,EAAA,CAAAkC,MAAA,gBACF;IAAAlC,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA0D;IAAtBD,EAAA,CAAAmB,UAAA,mBAAA2B,qEAAA;MAAA9C,EAAA,CAAAqB,aAAA,CAAAuB,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAyC,SAAA,EAAW;IAAA,EAAC;IACvD/C,EAAA,CAAAE,SAAA,YAAoC;IACpCF,EAAA,CAAAkC,MAAA,oBACF;IAAAlC,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA8D;IAA1BD,EAAA,CAAAmB,UAAA,mBAAA6B,qEAAA;MAAAhD,EAAA,CAAAqB,aAAA,CAAAuB,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAA2C,aAAA,EAAe;IAAA,EAAC;IAC3DjD,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAkC,MAAA,iBACF;IACFlC,EADE,CAAAG,YAAA,EAAS,EACL;;;;;;IAaJH,EAAA,CAAAC,cAAA,iBAAqG;IAApED,EAAA,CAAAmB,UAAA,mBAAA+B,wEAAA;MAAAlD,EAAA,CAAAqB,aAAA,CAAA8B,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAA8C,WAAA,EAAa;IAAA,EAAC;IACtDpD,EAAA,CAAAE,SAAA,YAAoF;IACtFF,EAAA,CAAAG,YAAA,EAAS;;;;IADQH,EAAA,CAAAQ,SAAA,EAA+B;IAACR,EAAhC,CAAAI,WAAA,kBAAAE,MAAA,CAAAqB,OAAA,CAA+B,mBAAArB,MAAA,CAAAqB,OAAA,CAAiC;;;;;;IA7GvF3B,EAAA,CAAAC,cAAA,aAAoF;IAAnCD,EAAA,CAAAmB,UAAA,mBAAAkC,2DAAAC,MAAA;MAAAtD,EAAA,CAAAqB,aAAA,CAAAkC,GAAA;MAAA,MAAAjD,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAkD,gBAAA,CAAAF,MAAA,CAAwB;IAAA,EAAC;IAG/EtD,EADF,CAAAC,cAAA,aAA0B,aACD;IACrBD,EAAA,CAAAE,SAAA,aAC4D;IAE1DF,EADF,CAAAC,cAAA,aAA0B,eACD;IAAAD,EAAA,CAAAkC,MAAA,GAAgC;IAAAlC,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAkC,MAAA,GAAwC;IAEpElC,EAFoE,CAAAG,YAAA,EAAO,EACnE,EACF;IAGJH,EADF,CAAAC,cAAA,cAA4B,kBACyB;IAAzBD,EAAA,CAAAmB,UAAA,mBAAAsC,+DAAA;MAAAzD,EAAA,CAAAqB,aAAA,CAAAkC,GAAA;MAAA,MAAAjD,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAoD,YAAA,EAAc;IAAA,EAAC;IAChD1D,EAAA,CAAAE,SAAA,aAA4B;IAGlCF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGNH,EAAA,CAAAC,cAAA,eAAgC;IAC9BD,EAAA,CAAA2D,UAAA,KAAAC,4CAAA,kBAG0C;IAK5C5D,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA2B;IASzBD,EAPA,CAAA2D,UAAA,KAAAE,4CAAA,kBAIiB,KAAAC,8CAAA,oBAWY;IAI7B9D,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAA2D,UAAA,KAAAI,4CAAA,kBAIoD;IAStD/D,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA2D,UAAA,KAAAK,4CAAA,kBAAwD;IAG1DhE,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAyD;IAA1BD,EAAA,CAAAmB,UAAA,mBAAA8C,4DAAA;MAAAjE,EAAA,CAAAqB,aAAA,CAAAkC,GAAA;MAAA,MAAAjD,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAA4D,aAAA,EAAe;IAAA,EAAC;IAAClE,EAAA,CAAAG,YAAA,EAAM;IAC/DH,EAAA,CAAAC,cAAA,eAAqD;IAAtBD,EAAA,CAAAmB,UAAA,mBAAAgD,4DAAA;MAAAnE,EAAA,CAAAqB,aAAA,CAAAkC,GAAA;MAAA,MAAAjD,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAmB,SAAA,EAAW;IAAA,EAAC;IAACzB,EAAA,CAAAG,YAAA,EAAM;IAG3DH,EAAA,CAAAC,cAAA,eAA2B;IAEzBD,EAAA,CAAA2D,UAAA,KAAAS,4CAAA,mBAAwE;IAiBtEpE,EADF,CAAAC,cAAA,eAA4B,kBACqD;IAAvBD,EAAA,CAAAmB,UAAA,mBAAAkD,+DAAA;MAAArE,EAAA,CAAAqB,aAAA,CAAAkC,GAAA;MAAA,MAAAjD,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAgE,UAAA,EAAY;IAAA,EAAC;IAC5EtE,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4D;IAAzBD,EAAA,CAAAmB,UAAA,mBAAAoD,+DAAA;MAAAvE,EAAA,CAAAqB,aAAA,CAAAkC,GAAA;MAAA,MAAAjD,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAkE,YAAA,EAAc;IAAA,EAAC;IACzDxE,EAAA,CAAAE,SAAA,aAA8B;IAChCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAwD;IAAvBD,EAAA,CAAAmB,UAAA,mBAAAsD,+DAAA;MAAAzE,EAAA,CAAAqB,aAAA,CAAAkC,GAAA;MAAA,MAAAjD,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAoE,UAAA,EAAY;IAAA,EAAC;IACrD1E,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAA2D,UAAA,KAAAgB,+CAAA,qBAAqG;IAK3G3E,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IA7GKH,EAAA,CAAAQ,SAAA,GAAuE;IACvER,EADA,CAAAc,UAAA,QAAAR,MAAA,CAAAS,YAAA,CAAA6D,IAAA,CAAAC,MAAA,yCAAA7E,EAAA,CAAAiB,aAAA,CAAuE,QAAAX,MAAA,CAAAS,YAAA,CAAA6D,IAAA,CAAAE,QAAA,CACrC;IAEd9E,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAAsC,iBAAA,CAAAhC,MAAA,CAAAS,YAAA,CAAA6D,IAAA,CAAAG,QAAA,CAAgC;IAC/B/E,EAAA,CAAAQ,SAAA,GAAwC;IAAxCR,EAAA,CAAAsC,iBAAA,CAAAhC,MAAA,CAAA0E,UAAA,CAAA1E,MAAA,CAAAS,YAAA,CAAAkE,SAAA,EAAwC;IAc7CjF,EAAA,CAAAQ,SAAA,GAAY;IAAZR,EAAA,CAAAc,UAAA,YAAAR,MAAA,CAAA4E,OAAA,CAAY;IAY7BlF,EAAA,CAAAQ,SAAA,GAAyC;IAAzCR,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,YAAA,CAAAH,KAAA,CAAAuE,IAAA,aAAyC;IAOvCnF,EAAA,CAAAQ,SAAA,EAAyC;IAAzCR,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,YAAA,CAAAH,KAAA,CAAAuE,IAAA,aAAyC;IAcnBnF,EAAA,CAAAQ,SAAA,GAAwB;IAAxBR,EAAA,CAAAc,UAAA,YAAAR,MAAA,CAAAS,YAAA,CAAAqE,QAAA,CAAwB;IAe1BpF,EAAA,CAAAQ,SAAA,EAA0B;IAA1BR,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,YAAA,CAAAG,OAAA,CAA0B;IAYtBlB,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,YAAA,CAAAqE,QAAA,CAAAC,MAAA,KAAsC;IAiBpCrF,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAI,WAAA,UAAAE,MAAA,CAAAgF,OAAA,CAAuB;IASGtF,EAAA,CAAAQ,SAAA,GAAyC;IAAzCR,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,YAAA,CAAAH,KAAA,CAAAuE,IAAA,aAAyC;;;;;IAwB/FnF,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAkC,MAAA,GACF;;IAAAlC,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAwC,kBAAA,YAAAxC,EAAA,CAAAyC,WAAA,OAAAnC,MAAA,CAAAiF,eAAA,CAAAC,aAAA,gBACF;;;;;;IAlBVxF,EAAA,CAAAC,cAAA,cAAiF;IAA9BD,EAAA,CAAAmB,UAAA,mBAAAsE,2DAAA;MAAAzF,EAAA,CAAAqB,aAAA,CAAAqE,IAAA;MAAA,MAAApF,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAqF,iBAAA,EAAmB;IAAA,EAAC;IAC9E3F,EAAA,CAAAC,cAAA,cAA8D;IAAnCD,EAAA,CAAAmB,UAAA,mBAAAyE,2DAAAtC,MAAA;MAAAtD,EAAA,CAAAqB,aAAA,CAAAqE,IAAA;MAAA,OAAA1F,EAAA,CAAAwB,WAAA,CAAS8B,MAAA,CAAAuC,eAAA,EAAwB;IAAA,EAAC;IAEzD7F,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAkC,MAAA,GAA0B;IAAAlC,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,iBAAwD;IAA9BD,EAAA,CAAAmB,UAAA,mBAAA2E,8DAAA;MAAA9F,EAAA,CAAAqB,aAAA,CAAAqE,IAAA;MAAA,MAAApF,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAqF,iBAAA,EAAmB;IAAA,EAAC;IACrD3F,EAAA,CAAAE,SAAA,YAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAENH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,SAAA,cAA+F;IAG7FF,EADF,CAAAC,cAAA,cAA6B,aACV;IAAAD,EAAA,CAAAkC,MAAA,IAA2B;IAAAlC,EAAA,CAAAG,YAAA,EAAI;IAE9CH,EADF,CAAAC,cAAA,eAAmB,gBACW;IAAAD,EAAA,CAAAkC,MAAA,IAA6C;;IAAAlC,EAAA,CAAAG,YAAA,EAAO;IAChFH,EAAA,CAAA2D,UAAA,KAAAoC,6CAAA,mBAAmE;IAIvE/F,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAA2B,kBAC6B;IAA1BD,EAAA,CAAAmB,UAAA,mBAAA6E,+DAAA;MAAAhG,EAAA,CAAAqB,aAAA,CAAAqE,IAAA;MAAA,MAAApF,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAA2F,aAAA,EAAe;IAAA,EAAC;IAACjG,EAAA,CAAAkC,MAAA,eAAO;IAAAlC,EAAA,CAAAG,YAAA,EAAS;IACtEH,EAAA,CAAAC,cAAA,kBAA2D;IAA7BD,EAAA,CAAAmB,UAAA,mBAAA+E,+DAAA;MAAAlG,EAAA,CAAAqB,aAAA,CAAAqE,IAAA;MAAA,MAAApF,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAA6F,gBAAA,EAAkB;IAAA,EAAC;IAACnG,EAAA,CAAAkC,MAAA,mBAAW;IAAAlC,EAAA,CAAAG,YAAA,EAAS;IAC/EH,EAAA,CAAAC,cAAA,kBAA6D;IAAjCD,EAAA,CAAAmB,UAAA,mBAAAiF,+DAAA;MAAApG,EAAA,CAAAqB,aAAA,CAAAqE,IAAA;MAAA,MAAApF,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAA+F,oBAAA,EAAsB;IAAA,EAAC;IAACrG,EAAA,CAAAkC,MAAA,uBAAe;IAIpFlC,EAJoF,CAAAG,YAAA,EAAS,EACjF,EACF,EACF,EACF;;;;IA1BIH,EAAA,CAAAQ,SAAA,GAA0B;IAA1BR,EAAA,CAAAsC,iBAAA,CAAAhC,MAAA,CAAAiF,eAAA,CAAAhD,IAAA,CAA0B;IAOzBvC,EAAA,CAAAQ,SAAA,GAAsC;IAACR,EAAvC,CAAAc,UAAA,QAAAR,MAAA,CAAAiF,eAAA,CAAAe,MAAA,qBAAAhG,MAAA,CAAAiF,eAAA,CAAAe,MAAA,IAAAtF,GAAA,EAAAhB,EAAA,CAAAiB,aAAA,CAAsC,QAAAX,MAAA,CAAAiF,eAAA,CAAAhD,IAAA,CAA6B;IAGrDvC,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAAsC,iBAAA,CAAAhC,MAAA,CAAAiF,eAAA,CAAAgB,KAAA,CAA2B;IAEdvG,EAAA,CAAAQ,SAAA,GAA6C;IAA7CR,EAAA,CAAAwC,kBAAA,WAAAxC,EAAA,CAAAyC,WAAA,QAAAnC,MAAA,CAAAiF,eAAA,CAAA7C,KAAA,eAA6C;IAC3C1C,EAAA,CAAAQ,SAAA,GAAmC;IAAnCR,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAiF,eAAA,CAAAC,aAAA,CAAmC;;;;;IA0BrExF,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAE,SAAA,cAC0D;IAExDF,EADF,CAAAC,cAAA,cAA6B,eACI;IAAAD,EAAA,CAAAkC,MAAA,GAA2B;IAAAlC,EAAA,CAAAG,YAAA,EAAO;IACjEH,EAAA,CAAAC,cAAA,YAAwB;IAAAD,EAAA,CAAAkC,MAAA,GAAkB;IAAAlC,EAAA,CAAAG,YAAA,EAAI;IAC9CH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAkC,MAAA,GAAqC;IAEpElC,EAFoE,CAAAG,YAAA,EAAO,EACnE,EACF;;;;;IAPCH,EAAA,CAAAQ,SAAA,EAAkE;IAClER,EADA,CAAAc,UAAA,QAAA0F,WAAA,CAAA5B,IAAA,CAAAC,MAAA,yCAAA7E,EAAA,CAAAiB,aAAA,CAAkE,QAAAuF,WAAA,CAAA5B,IAAA,CAAAE,QAAA,CACrC;IAED9E,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAAsC,iBAAA,CAAAkE,WAAA,CAAA5B,IAAA,CAAAG,QAAA,CAA2B;IAClC/E,EAAA,CAAAQ,SAAA,GAAkB;IAAlBR,EAAA,CAAAsC,iBAAA,CAAAkE,WAAA,CAAAC,IAAA,CAAkB;IACfzG,EAAA,CAAAQ,SAAA,GAAqC;IAArCR,EAAA,CAAAsC,iBAAA,CAAAhC,MAAA,CAAA0E,UAAA,CAAAwB,WAAA,CAAAE,WAAA,EAAqC;;;;;;IAhB1E1G,EAAA,CAAAC,cAAA,cAAgF;IAA1BD,EAAA,CAAAmB,UAAA,mBAAAwF,2DAAA;MAAA3G,EAAA,CAAAqB,aAAA,CAAAuF,IAAA;MAAA,MAAAtG,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAuG,aAAA,EAAe;IAAA,EAAC;IAC7E7G,EAAA,CAAAC,cAAA,cAA8D;IAAnCD,EAAA,CAAAmB,UAAA,mBAAA2F,2DAAAxD,MAAA;MAAAtD,EAAA,CAAAqB,aAAA,CAAAuF,IAAA;MAAA,OAAA5G,EAAA,CAAAwB,WAAA,CAAS8B,MAAA,CAAAuC,eAAA,EAAwB;IAAA,EAAC;IAEzD7F,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAkC,MAAA,eAAQ;IAAAlC,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,iBAAoD;IAA1BD,EAAA,CAAAmB,UAAA,mBAAA4F,8DAAA;MAAA/G,EAAA,CAAAqB,aAAA,CAAAuF,IAAA;MAAA,MAAAtG,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAuG,aAAA,EAAe;IAAA,EAAC;IACjD7G,EAAA,CAAAE,SAAA,YAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAENH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAA2D,UAAA,IAAAqD,2CAAA,kBAAsD;IASxDhH,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,cAA2B,iBAIW;IAF7BD,EAAA,CAAAiH,gBAAA,2BAAAC,sEAAA5D,MAAA;MAAAtD,EAAA,CAAAqB,aAAA,CAAAuF,IAAA;MAAA,MAAAtG,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAAvB,EAAA,CAAAmH,kBAAA,CAAA7G,MAAA,CAAA8G,UAAA,EAAA9D,MAAA,MAAAhD,MAAA,CAAA8G,UAAA,GAAA9D,MAAA;MAAA,OAAAtD,EAAA,CAAAwB,WAAA,CAAA8B,MAAA;IAAA,EAAwB;IAExBtD,EAAA,CAAAmB,UAAA,yBAAAkG,oEAAA;MAAArH,EAAA,CAAAqB,aAAA,CAAAuF,IAAA;MAAA,MAAAtG,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAelB,MAAA,CAAAgH,UAAA,EAAY;IAAA,EAAC;IAHnCtH,EAAA,CAAAG,YAAA,EAGoC;IACpCH,EAAA,CAAAC,cAAA,kBAA+D;IAAvDD,EAAA,CAAAmB,UAAA,mBAAAoG,+DAAA;MAAAvH,EAAA,CAAAqB,aAAA,CAAAuF,IAAA;MAAA,MAAAtG,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAgH,UAAA,EAAY;IAAA,EAAC;IAC5BtH,EAAA,CAAAE,SAAA,aAAkC;IAI1CF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IArByCH,EAAA,CAAAQ,SAAA,GAAW;IAAXR,EAAA,CAAAc,UAAA,YAAAR,MAAA,CAAAkH,QAAA,CAAW;IAa7CxH,EAAA,CAAAQ,SAAA,GAAwB;IAAxBR,EAAA,CAAAyH,gBAAA,YAAAnH,MAAA,CAAA8G,UAAA,CAAwB;IAGApH,EAAA,CAAAQ,SAAA,EAA+B;IAA/BR,EAAA,CAAAc,UAAA,cAAAR,MAAA,CAAA8G,UAAA,CAAAM,IAAA,GAA+B;;;AA0dxE,OAAM,MAAOC,sBAAsB;EAgBjCC,YACUC,MAAc,EACdC,KAAqB;IADrB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IAff,KAAA5C,OAAO,GAAY,EAAE;IACrB,KAAA3E,YAAY,GAAG,CAAC;IAEhB,KAAA+E,OAAO,GAAG,KAAK;IACf,KAAA3D,OAAO,GAAG,IAAI;IACd,KAAA4D,eAAe,GAAQ,IAAI;IAC3B,KAAAwC,iBAAiB,GAAG,KAAK;IACzB,KAAAP,QAAQ,GAAU,EAAE;IACpB,KAAAJ,UAAU,GAAG,EAAE;IAGP,KAAAY,aAAa,GAAG,KAAK,CAAC,CAAC;EAK5B;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACH,KAAK,CAACI,WAAW,CAACC,SAAS,CAACD,WAAW,IAAG;MAC7C,IAAIA,WAAW,CAAC,OAAO,CAAC,EAAE;QACxB,IAAI,CAAC3H,YAAY,GAAG6H,QAAQ,CAACF,WAAW,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;;IAE/D,CAAC,CAAC;IAEF,IAAI,CAACJ,KAAK,CAACO,MAAM,CAACF,SAAS,CAACE,MAAM,IAAG;MACnC,IAAIA,MAAM,CAAC,QAAQ,CAAC,EAAE;QACpB,IAAI,CAACC,eAAe,CAACD,MAAM,CAAC,QAAQ,CAAC,CAAC;OACvC,MAAM;QACL,IAAI,CAACE,WAAW,EAAE;;MAEpB,IAAIF,MAAM,CAAC,SAAS,CAAC,EAAE;QACrB,IAAI,CAACG,WAAW,CAACH,MAAM,CAAC,SAAS,CAAC,CAAC;;IAEvC,CAAC,CAAC;IAEF,IAAI,CAACI,sBAAsB,EAAE;IAC7B,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAEAL,WAAWA,CAAA;IACT;IACAO,KAAK,CAAC,mCAAmC,CAAC,CACvCC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAG;MACX,IAAIA,IAAI,CAACC,OAAO,EAAE;QAChB,IAAI,CAACjE,OAAO,GAAGgE,IAAI,CAAChE,OAAO,CAACkE,MAAM,CAAEC,KAAU,IAAKA,KAAK,CAACC,QAAQ,CAAC;QAClE,IAAI,IAAI,CAACpE,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;UAC3B;UACA,MAAMkE,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAACxE,OAAO,CAACG,MAAM,GAAG,CAAC,CAAC;UAC5E,IAAI,CAACqE,iBAAiB,GAAGH,UAAU;UACnC,IAAI,CAACxI,YAAY,GAAG,IAAI,CAACmE,OAAO,CAACqE,UAAU,CAAC;UAC5C,IAAI,CAACI,eAAe,EAAE;;;IAG5B,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C;MACA,IAAI,CAACE,gBAAgB,CAAC,2CAA2C,CAAC;IACpE,CAAC,CAAC;EACN;EAEAzB,eAAeA,CAAC0B,MAAc;IAC5B;IACAlB,KAAK,CAAC,0CAA0CkB,MAAM,EAAE,CAAC,CACtDjB,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAG;MACX,IAAIA,IAAI,CAACC,OAAO,EAAE;QAChB,IAAI,CAACjE,OAAO,GAAGgE,IAAI,CAAChE,OAAO,CAACkE,MAAM,CAAEC,KAAU,IAAKA,KAAK,CAACC,QAAQ,CAAC;QAClE,IAAI,IAAI,CAACpE,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;UAC3B,IAAI,CAACtE,YAAY,GAAG,IAAI,CAACmE,OAAO,CAAC,CAAC,CAAC;UACnC,IAAI,CAACyE,eAAe,EAAE;;;IAG5B,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,CAAC;EACN;EAEArB,WAAWA,CAACyB,OAAe;IACzB,MAAMC,KAAK,GAAG,IAAI,CAAChF,OAAO,CAACiF,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKJ,OAAO,CAAC;IAC5D,IAAIC,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAAC3J,YAAY,GAAG2J,KAAK;MACzB,IAAI,CAACnJ,YAAY,GAAG,IAAI,CAACmE,OAAO,CAACgF,KAAK,CAAC;MACvC,IAAI,CAACP,eAAe,EAAE;;EAE1B;EAEAA,eAAeA,CAAA;IACb,IAAI,IAAI,CAACf,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAGlC,MAAM/H,QAAQ,GAAG,IAAI,CAACE,YAAY,CAACH,KAAK,CAACuE,IAAI,KAAK,OAAO,GACrD,IAAI,CAACpE,YAAY,CAACH,KAAK,CAACC,QAAQ,GAAG,IAAI,GACvC,IAAI,CAACmH,aAAa;IAEtB,IAAI,CAACY,aAAa,GAAG0B,UAAU,CAAC,MAAK;MACnC,IAAI,CAAC7I,SAAS,EAAE;IAClB,CAAC,EAAEZ,QAAQ,CAAC;EACd;EAEAY,SAASA,CAAA;IACP,IAAI,IAAI,CAAClB,YAAY,GAAG,IAAI,CAAC2E,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAAC9E,YAAY,EAAE;MACnB,IAAI,CAACQ,YAAY,GAAG,IAAI,CAACmE,OAAO,CAAC,IAAI,CAAC3E,YAAY,CAAC;MACnD,IAAI,CAACoJ,eAAe,EAAE;KACvB,MAAM;MACL,IAAI,CAACjG,YAAY,EAAE;;EAEvB;EAEAQ,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC3D,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAACQ,YAAY,GAAG,IAAI,CAACmE,OAAO,CAAC,IAAI,CAAC3E,YAAY,CAAC;MACnD,IAAI,CAACoJ,eAAe,EAAE;;EAE1B;EAEAnG,gBAAgBA,CAAC+G,KAAiB;IAChC,MAAMC,IAAI,GAAID,KAAK,CAACE,MAAsB,CAACC,qBAAqB,EAAE;IAClE,MAAMC,MAAM,GAAGJ,KAAK,CAACK,OAAO,GAAGJ,IAAI,CAACK,IAAI;IACxC,MAAMC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAExB,IAAIH,MAAM,GAAGG,KAAK,GAAG,GAAG,EAAE;MACxB,IAAI,CAAC5G,aAAa,EAAE;KACrB,MAAM,IAAIyG,MAAM,GAAGG,KAAK,GAAG,GAAG,EAAE;MAC/B,IAAI,CAACrJ,SAAS,EAAE;;EAEpB;EAEAf,gBAAgBA,CAACwJ,KAAa;IAC5B,IAAIA,KAAK,GAAG,IAAI,CAAC3J,YAAY,EAAE,OAAO,GAAG;IACzC,IAAI2J,KAAK,GAAG,IAAI,CAAC3J,YAAY,EAAE,OAAO,CAAC;IACvC,OAAO,CAAC,CAAC,CAAC;EACZ;EAEAmD,YAAYA,CAAA;IACV;IACA,IAAIqH,MAAM,CAACC,OAAO,CAAC3F,MAAM,GAAG,CAAC,EAAE;MAC7B0F,MAAM,CAACC,OAAO,CAACC,IAAI,EAAE;KACtB,MAAM;MACL,IAAI,CAACpD,MAAM,CAACqD,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;;EAErC;EAEA;EACArI,MAAMA,CAAA;IACJ,IAAI,IAAI,CAAC9B,YAAY,CAACqE,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MACzC,MAAMpD,OAAO,GAAG,IAAI,CAAClB,YAAY,CAACqE,QAAQ,CAAC,CAAC,CAAC,CAACnD,OAAO;MACrD,IAAI,CAAC4F,MAAM,CAACqD,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClChD,WAAW,EAAE;UAAEiD,SAAS,EAAElJ,OAAO,CAACoI,GAAG;UAAEe,MAAM,EAAE;QAAO;OACvD,CAAC;;EAEN;EAEArI,SAASA,CAAA;IACP,IAAI,IAAI,CAAChC,YAAY,CAACqE,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MACzC,MAAMpD,OAAO,GAAG,IAAI,CAAClB,YAAY,CAACqE,QAAQ,CAAC,CAAC,CAAC,CAACnD,OAAO;MACrD;MACA6H,OAAO,CAACuB,GAAG,CAAC,yBAAyB,EAAEpJ,OAAO,CAAC;;EAEnD;EAEAgB,aAAaA,CAAA;IACX,IAAI,IAAI,CAAClC,YAAY,CAACqE,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MACzC,MAAMpD,OAAO,GAAG,IAAI,CAAClB,YAAY,CAACqE,QAAQ,CAAC,CAAC,CAAC,CAACnD,OAAO;MACrD;MACA6H,OAAO,CAACuB,GAAG,CAAC,6BAA6B,EAAEpJ,OAAO,CAAC;;EAEvD;EAEA;EACAqC,UAAUA,CAAA;IACR,IAAI,CAACgB,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B;EACF;EAEAd,YAAYA,CAAA;IACV,IAAI,CAACuD,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACuD,YAAY,EAAE;EACrB;EAEAzE,aAAaA,CAAA;IACX,IAAI,CAACkB,iBAAiB,GAAG,KAAK;EAChC;EAEArD,UAAUA,CAAA;IACR;IACAoF,OAAO,CAACuB,GAAG,CAAC,cAAc,EAAE,IAAI,CAACtK,YAAY,CAAC;EAChD;EAEAqC,WAAWA,CAAA;IACT,IAAI,CAACzB,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B,IAAI,IAAI,CAAC4J,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI,CAAC9J,OAAO;;EAEtD;EAEA;EACAK,gBAAgBA,CAACC,OAAY;IAC3B,IAAI,CAACsD,eAAe,GAAGtD,OAAO;EAChC;EAEA0D,iBAAiBA,CAAA;IACf,IAAI,CAACJ,eAAe,GAAG,IAAI;EAC7B;EAEAU,aAAaA,CAAA;IACX,IAAI,IAAI,CAACV,eAAe,EAAE;MACxB,IAAI,CAACsC,MAAM,CAACqD,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClChD,WAAW,EAAE;UAAEiD,SAAS,EAAE,IAAI,CAAC5F,eAAe,CAAC8E,GAAG;UAAEe,MAAM,EAAE;QAAO;OACpE,CAAC;;EAEN;EAEAjF,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACZ,eAAe,EAAE;MACxB;MACAuE,OAAO,CAACuB,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC9F,eAAe,CAAC;MACzD,IAAI,CAACI,iBAAiB,EAAE;;EAE5B;EAEAU,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACd,eAAe,EAAE;MACxB;MACAuE,OAAO,CAACuB,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC9F,eAAe,CAAC;MAC7D,IAAI,CAACI,iBAAiB,EAAE;;EAE5B;EAEA;EACA2F,YAAYA,CAAA;IACV;IACA,IAAI,CAAC9D,QAAQ,GAAG,CACd;MACE5C,IAAI,EAAE;QAAEG,QAAQ,EAAE,OAAO;QAAED,QAAQ,EAAE,UAAU;QAAED,MAAM,EAAE;MAAE,CAAE;MAC7D4B,IAAI,EAAE,oBAAoB;MAC1BC,WAAW,EAAE,IAAIgF,IAAI;KACtB,CACF;EACH;EAEApE,UAAUA,CAAA;IACR,IAAI,IAAI,CAACF,UAAU,CAACM,IAAI,EAAE,EAAE;MAC1B;MACAoC,OAAO,CAACuB,GAAG,CAAC,cAAc,EAAE,IAAI,CAACjE,UAAU,CAAC;MAC5C,IAAI,CAACA,UAAU,GAAG,EAAE;;EAExB;EAEApC,UAAUA,CAAC2G,IAAU;IACnB,MAAMC,GAAG,GAAG,IAAIF,IAAI,EAAE;IACtB,MAAMG,MAAM,GAAGD,GAAG,CAACE,OAAO,EAAE,GAAG,IAAIJ,IAAI,CAACC,IAAI,CAAC,CAACG,OAAO,EAAE;IACvD,MAAMC,SAAS,GAAGvC,IAAI,CAACwC,KAAK,CAACH,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEvD,IAAIE,SAAS,GAAG,CAAC,EAAE,OAAO,KAAK;IAC/B,IAAIA,SAAS,GAAG,EAAE,EAAE,OAAO,GAAGA,SAAS,GAAG;IAC1C,OAAO,GAAGvC,IAAI,CAACwC,KAAK,CAACD,SAAS,GAAG,EAAE,CAAC,GAAG;EACzC;EAEA;EAEAtD,sBAAsBA,CAAA;IACpBwD,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAG3B,KAAK,IAAI;MAC7C,IAAIA,KAAK,CAAC4B,GAAG,KAAK,WAAW,EAAE;QAC7B,IAAI,CAACjI,aAAa,EAAE;OACrB,MAAM,IAAIqG,KAAK,CAAC4B,GAAG,KAAK,YAAY,EAAE;QACrC,IAAI,CAAC1K,SAAS,EAAE;OACjB,MAAM,IAAI8I,KAAK,CAAC4B,GAAG,KAAK,QAAQ,EAAE;QACjC,IAAI,CAACzI,YAAY,EAAE;;IAEvB,CAAC,CAAC;EACJ;EAEAgF,mBAAmBA,CAAA;IACjB;IACA,IAAI0D,MAAM,GAAG,CAAC;IACd,IAAIC,MAAM,GAAG,CAAC;IAEdJ,QAAQ,CAACC,gBAAgB,CAAC,YAAY,EAAG3B,KAAK,IAAI;MAChD6B,MAAM,GAAG7B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAAC1B,OAAO;MACjCyB,MAAM,GAAG9B,KAAK,CAAC+B,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IACnC,CAAC,CAAC;IAEFN,QAAQ,CAACC,gBAAgB,CAAC,UAAU,EAAG3B,KAAK,IAAI;MAC9C,MAAMiC,IAAI,GAAGjC,KAAK,CAACkC,cAAc,CAAC,CAAC,CAAC,CAAC7B,OAAO;MAC5C,MAAM8B,IAAI,GAAGnC,KAAK,CAACkC,cAAc,CAAC,CAAC,CAAC,CAACF,OAAO;MAC5C,MAAMI,KAAK,GAAGP,MAAM,GAAGI,IAAI;MAC3B,MAAMI,KAAK,GAAGP,MAAM,GAAGK,IAAI;MAE3B;MACA,IAAIlD,IAAI,CAACqD,GAAG,CAACF,KAAK,CAAC,GAAGnD,IAAI,CAACqD,GAAG,CAACD,KAAK,CAAC,IAAIpD,IAAI,CAACqD,GAAG,CAACF,KAAK,CAAC,GAAG,EAAE,EAAE;QAC7D,IAAIA,KAAK,GAAG,CAAC,EAAE;UACb,IAAI,CAAClL,SAAS,EAAE,CAAC,CAAC;SACnB,MAAM;UACL,IAAI,CAACyC,aAAa,EAAE,CAAC,CAAC;;;MAG1B;MAAA,KACK,IAAI0I,KAAK,GAAG,CAAC,GAAG,EAAE;QACrB,IAAI,CAAClJ,YAAY,EAAE;;IAEvB,CAAC,CAAC;EACJ;EAEA8E,WAAWA,CAACyB,OAAe;IACzB,MAAM6C,UAAU,GAAG,IAAI,CAAC5H,OAAO,CAACiF,SAAS,CAACd,KAAK,IAAIA,KAAK,CAACgB,GAAG,KAAKJ,OAAO,CAAC;IACzE,IAAI6C,UAAU,GAAG,CAAC,CAAC,EAAE;MACnB,IAAI,CAACpD,iBAAiB,GAAGoD,UAAU;MACnC,IAAI,CAAC/L,YAAY,GAAG,IAAI,CAACmE,OAAO,CAAC4H,UAAU,CAAC;MAC5C,IAAI,CAACnD,eAAe,EAAE;;EAE1B;EAEAI,gBAAgBA,CAACgD,OAAe;IAC9B;IACAjD,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEkD,OAAO,CAAC;IACxCC,KAAK,CAACD,OAAO,CAAC,CAAC,CAAC;EAClB;;;uBA7UWpF,sBAAsB,EAAA3H,EAAA,CAAAiN,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAnN,EAAA,CAAAiN,iBAAA,CAAAC,EAAA,CAAAE,cAAA;IAAA;EAAA;;;YAAtBzF,sBAAsB;MAAA0F,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UApf/BxN,EApJA,CAAA2D,UAAA,IAAA+J,qCAAA,mBAAoF,IAAAC,qCAAA,kBAoHH,IAAAC,qCAAA,kBAgCD;;;UApJnD5N,EAAA,CAAAc,UAAA,SAAA2M,GAAA,CAAA1M,YAAA,CAAkB;UAoHnBf,EAAA,CAAAQ,SAAA,EAAqB;UAArBR,EAAA,CAAAc,UAAA,SAAA2M,GAAA,CAAAlI,eAAA,CAAqB;UAgCpBvF,EAAA,CAAAQ,SAAA,EAAuB;UAAvBR,EAAA,CAAAc,UAAA,SAAA2M,GAAA,CAAA1F,iBAAA,CAAuB;;;qBAtJ5CjI,YAAY,EAAA+N,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAEjO,WAAW,EAAAkO,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}