{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = [\"storyVideo\"];\nconst _c1 = [\"storyThumbnails\"];\nfunction StoriesViewerComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r4 === ctx_r1.currentIndex)(\"completed\", i_r4 < ctx_r1.currentIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getProgressWidth(i_r4), \"%\")(\"animation-duration\", ctx_r1.getStoryDuration(story_r3), \"s\");\n  }\n}\nfunction StoriesViewerComponent_div_0_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleSound());\n    });\n    i0.ɵɵelement(1, \"i\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"muted\", ctx_r1.isMuted);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"fa-volume-up\", !ctx_r1.isMuted)(\"fa-volume-mute\", ctx_r1.isMuted);\n  }\n}\nfunction StoriesViewerComponent_div_0_img_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 48, 0);\n    i0.ɵɵlistener(\"load\", function StoriesViewerComponent_div_0_img_20_Template_img_load_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onMediaLoaded());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.media.url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.caption);\n  }\n}\nfunction StoriesViewerComponent_div_0_video_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"video\", 49, 1);\n    i0.ɵɵlistener(\"loadeddata\", function StoriesViewerComponent_div_0_video_21_Template_video_loadeddata_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onMediaLoaded());\n    })(\"ended\", function StoriesViewerComponent_div_0_video_21_Template_video_ended_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.media.url, i0.ɵɵsanitizeUrl)(\"poster\", ctx_r1.currentStory.media.thumbnail, i0.ɵɵsanitizeUrl)(\"muted\", ctx_r1.isMuted)(\"autoplay\", !ctx_r1.isPaused)(\"loop\", false);\n  }\n}\nfunction StoriesViewerComponent_div_0_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_22_div_1_Template_div_click_0_listener($event) {\n      const productTag_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.showProductModal(productTag_r9.product);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(1, \"div\", 51);\n    i0.ɵɵelement(2, \"div\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 53)(4, \"span\", 54);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 55);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const productTag_r9 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", productTag_r9.position.x, \"%\")(\"top\", productTag_r9.position.y, \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(productTag_r9.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(8, 6, productTag_r9.product.price, \"1.0-0\"), \"\");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, StoriesViewerComponent_div_0_div_22_div_1_Template, 9, 9, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"show-tags\", ctx_r1.showProductTags);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentStory.products);\n  }\n}\nfunction StoriesViewerComponent_div_0_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_23_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      ctx_r1.toggleProductTags();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 57);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Tap to view products\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StoriesViewerComponent_div_0_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.currentStory.caption, \" \");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_25_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_25_div_6_Template_div_click_0_listener() {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.jumpToStoryIndex(i_r13));\n    });\n    i0.ɵɵelement(1, \"img\", 68);\n    i0.ɵɵelementStart(2, \"div\", 69);\n    i0.ɵɵelement(3, \"img\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 71);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const story_r14 = ctx.$implicit;\n    const i_r13 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", i_r13 === ctx_r1.currentIndex)(\"viewed\", i_r13 < ctx_r1.currentIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getStoryThumbnail(story_r14), i0.ɵɵsanitizeUrl)(\"alt\", story_r14.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", story_r14.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", story_r14.user.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getThumbnailProgress(i_r13), \"%\");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60)(2, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_25_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.scrollStoriesLeft());\n    });\n    i0.ɵɵelement(3, \"i\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 63, 2);\n    i0.ɵɵtemplate(6, StoriesViewerComponent_div_0_div_25_div_6_Template, 5, 10, \"div\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_25_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.scrollStoriesRight());\n    });\n    i0.ɵɵelement(8, \"i\", 66);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canScrollLeft);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canScrollRight);\n  }\n}\nfunction StoriesViewerComponent_div_0_img_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 72, 0);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.media.url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.caption);\n  }\n}\nfunction StoriesViewerComponent_div_0_video_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"video\", 73, 1);\n    i0.ɵɵlistener(\"ended\", function StoriesViewerComponent_div_0_video_28_Template_video_ended_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.media.url, i0.ɵɵsanitizeUrl)(\"poster\", ctx_r1.currentStory.media.thumbnail, i0.ɵɵsanitizeUrl)(\"muted\", ctx_r1.isMuted)(\"autoplay\", true)(\"loop\", false);\n  }\n}\nfunction StoriesViewerComponent_div_0_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_30_Template_div_click_0_listener() {\n      const productTag_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showProductModal(productTag_r17.product));\n    });\n    i0.ɵɵelementStart(1, \"div\", 74);\n    i0.ɵɵelement(2, \"i\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 75)(4, \"span\", 54);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 55);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const productTag_r17 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", productTag_r17.position.x, \"%\")(\"top\", productTag_r17.position.y, \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(productTag_r17.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(8, 6, productTag_r17.product.price, \"1.0-0\"), \"\");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.currentStory.caption, \" \");\n  }\n}\nfunction StoriesViewerComponent_div_0_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_35_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.buyNow());\n    });\n    i0.ɵɵelement(2, \"i\", 78);\n    i0.ɵɵtext(3, \" Buy Now \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_35_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart());\n    });\n    i0.ɵɵelement(5, \"i\", 80);\n    i0.ɵɵtext(6, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_div_35_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist());\n    });\n    i0.ɵɵelement(8, \"i\", 39);\n    i0.ɵɵtext(9, \" Wishlist \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction StoriesViewerComponent_div_0_button_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_button_43_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleSound());\n    });\n    i0.ɵɵelement(1, \"i\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"fa-volume-up\", !ctx_r1.isMuted)(\"fa-volume-mute\", ctx_r1.isMuted);\n  }\n}\nfunction StoriesViewerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleStoryClick($event));\n    })(\"keydown\", function StoriesViewerComponent_div_0_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleKeyDown($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 7);\n    i0.ɵɵtemplate(2, StoriesViewerComponent_div_0_div_2_Template, 2, 8, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 9)(4, \"div\", 10);\n    i0.ɵɵelement(5, \"img\", 11);\n    i0.ɵɵelementStart(6, \"div\", 12)(7, \"span\", 13);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 14);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 15);\n    i0.ɵɵtemplate(12, StoriesViewerComponent_div_0_button_12_Template, 2, 6, \"button\", 16);\n    i0.ɵɵelementStart(13, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.togglePause());\n    });\n    i0.ɵɵelement(14, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStories());\n    });\n    i0.ɵɵelement(16, \"i\", 20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 21);\n    i0.ɵɵlistener(\"touchstart\", function StoriesViewerComponent_div_0_Template_div_touchstart_17_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchStart($event));\n    })(\"touchend\", function StoriesViewerComponent_div_0_Template_div_touchend_17_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchEnd($event));\n    })(\"mousedown\", function StoriesViewerComponent_div_0_Template_div_mousedown_17_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMouseDown($event));\n    })(\"mouseup\", function StoriesViewerComponent_div_0_Template_div_mouseup_17_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMouseUp($event));\n    });\n    i0.ɵɵelementStart(18, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_18_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_19_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, StoriesViewerComponent_div_0_img_20_Template, 2, 2, \"img\", 24)(21, StoriesViewerComponent_div_0_video_21_Template, 2, 5, \"video\", 25)(22, StoriesViewerComponent_div_0_div_22_Template, 2, 3, \"div\", 26)(23, StoriesViewerComponent_div_0_div_23_Template, 4, 0, \"div\", 27)(24, StoriesViewerComponent_div_0_div_24_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, StoriesViewerComponent_div_0_div_25_Template, 9, 3, \"div\", 29);\n    i0.ɵɵelementStart(26, \"div\", 30);\n    i0.ɵɵtemplate(27, StoriesViewerComponent_div_0_img_27_Template, 2, 2, \"img\", 31)(28, StoriesViewerComponent_div_0_video_28_Template, 2, 5, \"video\", 32);\n    i0.ɵɵelementStart(29, \"div\", 33);\n    i0.ɵɵtemplate(30, StoriesViewerComponent_div_0_div_30_Template, 9, 9, \"div\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, StoriesViewerComponent_div_0_div_31_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_32_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_div_click_33_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStory());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 35);\n    i0.ɵɵtemplate(35, StoriesViewerComponent_div_0_div_35_Template, 10, 0, \"div\", 36);\n    i0.ɵɵelementStart(36, \"div\", 37)(37, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleLike());\n    });\n    i0.ɵɵelement(38, \"i\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(40, \"i\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_0_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareStory());\n    });\n    i0.ɵɵelement(42, \"i\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(43, StoriesViewerComponent_div_0_button_43_Template, 2, 4, \"button\", 44);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentStory.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStory.user.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentStory.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.currentStory.createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"paused\", ctx_r1.isPaused);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"fa-pause\", !ctx_r1.isPaused)(\"fa-play\", ctx_r1.isPaused);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.products && ctx_r1.currentStory.products.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.products && ctx_r1.currentStory.products.length > 0 && !ctx_r1.showProductTags);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.stories.length > 5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"video\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentStory.products);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.caption);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.products.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isLiked);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStory.media.type === \"video\");\n  }\n}\nfunction StoriesViewerComponent_div_1_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 97);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, ctx_r1.selectedProduct.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction StoriesViewerComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 84);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 85)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeProductModal());\n    });\n    i0.ɵɵelement(6, \"i\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 86);\n    i0.ɵɵelement(8, \"img\", 87);\n    i0.ɵɵelementStart(9, \"div\", 88)(10, \"p\", 89);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 90)(13, \"span\", 91);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, StoriesViewerComponent_div_1_span_16_Template, 3, 4, \"span\", 92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 93)(18, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.buyProductNow());\n    });\n    i0.ɵɵtext(19, \"Buy Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addProductToCart());\n    });\n    i0.ɵɵtext(21, \"Add to Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_1_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addProductToWishlist());\n    });\n    i0.ɵɵtext(23, \"Add to Wishlist\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.selectedProduct.images[0] == null ? null : ctx_r1.selectedProduct.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.selectedProduct.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProduct.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(15, 6, ctx_r1.selectedProduct.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedProduct.originalPrice);\n  }\n}\nfunction StoriesViewerComponent_div_2_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵelement(1, \"img\", 106);\n    i0.ɵɵelementStart(2, \"div\", 107)(3, \"span\", 108);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 109);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 110);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const comment_r22 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", comment_r22.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", comment_r22.user.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(comment_r22.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r22.text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(comment_r22.commentedAt));\n  }\n}\nfunction StoriesViewerComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeComments());\n    });\n    i0.ɵɵelementStart(1, \"div\", 84);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 85)(3, \"h3\");\n    i0.ɵɵtext(4, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeComments());\n    });\n    i0.ɵɵelement(6, \"i\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 99);\n    i0.ɵɵtemplate(8, StoriesViewerComponent_div_2_div_8_Template, 9, 5, \"div\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 101)(10, \"input\", 102);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function StoriesViewerComponent_div_2_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newComment, $event) || (ctx_r1.newComment = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function StoriesViewerComponent_div_2_Template_input_keyup_enter_10_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addComment());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function StoriesViewerComponent_div_2_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addComment());\n    });\n    i0.ɵɵelement(12, \"i\", 104);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.comments);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newComment);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.newComment.trim());\n  }\n}\nexport class StoriesViewerComponent {\n  constructor(router, route) {\n    this.router = router;\n    this.route = route;\n    this.stories = [];\n    this.currentIndex = 0;\n    this.isLiked = false;\n    this.isMuted = true;\n    this.isPaused = false;\n    this.showProductTags = false;\n    this.selectedProduct = null;\n    this.showCommentsModal = false;\n    this.comments = [];\n    this.newComment = '';\n    // Touch handling\n    this.touchStartTime = 0;\n    // Navigation slider properties\n    this.canScrollLeft = false;\n    this.canScrollRight = false;\n    this.storyDuration = 15000; // 15 seconds default\n  }\n  ngOnInit() {\n    // Handle query parameters for story index\n    this.route.queryParams.subscribe(queryParams => {\n      if (queryParams['index']) {\n        this.currentIndex = parseInt(queryParams['index'], 10) || 0;\n      }\n    });\n    this.route.params.subscribe(params => {\n      if (params['userId']) {\n        this.loadUserStories(params['userId']);\n      } else {\n        this.loadStories();\n      }\n      if (params['storyId']) {\n        this.jumpToStory(params['storyId']);\n      }\n    });\n    // Add keyboard listeners for better UX\n    this.addKeyboardListeners();\n    this.addTouchListeners();\n    // Initialize navigation slider after view init\n    setTimeout(() => {\n      this.updateScrollButtons();\n      this.updateNavigationSlider();\n    }, 100);\n  }\n  ngOnDestroy() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n    }\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n    }\n  }\n  // Instagram-like interactions\n  togglePause() {\n    this.isPaused = !this.isPaused;\n    if (this.storyVideo) {\n      if (this.isPaused) {\n        this.storyVideo.nativeElement.pause();\n        clearTimeout(this.progressTimer);\n      } else {\n        this.storyVideo.nativeElement.play();\n        this.startStoryTimer();\n      }\n    }\n  }\n  toggleProductTags() {\n    this.showProductTags = !this.showProductTags;\n    if (this.showProductTags) {\n      setTimeout(() => {\n        this.showProductTags = false;\n      }, 3000);\n    }\n  }\n  onTouchStart(event) {\n    this.touchStartTime = Date.now();\n    this.longPressTimer = setTimeout(() => {\n      this.isPaused = true;\n      if (this.storyVideo) {\n        this.storyVideo.nativeElement.pause();\n      }\n      clearTimeout(this.progressTimer);\n    }, 200);\n  }\n  onTouchEnd(event) {\n    clearTimeout(this.longPressTimer);\n    if (this.isPaused && Date.now() - this.touchStartTime > 200) {\n      this.isPaused = false;\n      if (this.storyVideo) {\n        this.storyVideo.nativeElement.play();\n      }\n      this.startStoryTimer();\n    }\n  }\n  onMouseDown(event) {\n    this.touchStartTime = Date.now();\n    this.longPressTimer = setTimeout(() => {\n      this.isPaused = true;\n      if (this.storyVideo) {\n        this.storyVideo.nativeElement.pause();\n      }\n      clearTimeout(this.progressTimer);\n    }, 200);\n  }\n  onMouseUp(event) {\n    clearTimeout(this.longPressTimer);\n    if (this.isPaused && Date.now() - this.touchStartTime > 200) {\n      this.isPaused = false;\n      if (this.storyVideo) {\n        this.storyVideo.nativeElement.play();\n      }\n      this.startStoryTimer();\n    }\n  }\n  onMediaLoaded() {\n    // Media loaded, start timer\n    this.startStoryTimer();\n  }\n  getStoryDuration(story) {\n    return story.media.type === 'video' ? story.media.duration : 15;\n  }\n  handleKeyDown(event) {\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n      case ' ':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  addKeyboardListeners() {\n    // Already handled in template with (keydown)\n  }\n  addTouchListeners() {\n    // Already handled in template with touch events\n  }\n  loadStories() {\n    // Load stories from real API\n    fetch('http://localhost:5000/api/stories').then(response => response.json()).then(data => {\n      if (data.success) {\n        this.stories = data.stories.filter(story => story.isActive);\n        if (this.stories.length > 0) {\n          // Use the index from query params or default to 0\n          const startIndex = Math.min(this.currentIndex, this.stories.length - 1);\n          this.currentIndex = startIndex;\n          this.currentStory = this.stories[startIndex];\n          this.startStoryTimer();\n          // Initialize navigation slider\n          setTimeout(() => {\n            this.updateScrollButtons();\n            this.updateNavigationSlider();\n          }, 100);\n        }\n      }\n    }).catch(error => {\n      console.error('Error loading stories:', error);\n      // Show error message to user\n      alert('Failed to load stories. Please try again.');\n    });\n  }\n  loadUserStories(userId) {\n    // Load specific user's stories from real API\n    fetch(`http://localhost:5000/api/stories/user/${userId}`).then(response => response.json()).then(data => {\n      if (data.success) {\n        this.stories = data.stories.filter(story => story.isActive);\n        if (this.stories.length > 0) {\n          this.currentStory = this.stories[0];\n          this.startStoryTimer();\n        }\n      }\n    }).catch(error => {\n      console.error('Error loading user stories:', error);\n    });\n  }\n  jumpToStory(storyId) {\n    const index = this.stories.findIndex(s => s._id === storyId);\n    if (index !== -1) {\n      this.currentIndex = index;\n      this.currentStory = this.stories[index];\n      this.startStoryTimer();\n    }\n  }\n  startStoryTimer() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n    }\n    const duration = this.currentStory.media.type === 'video' ? this.currentStory.media.duration * 1000 : this.storyDuration;\n    this.progressTimer = setTimeout(() => {\n      this.nextStory();\n    }, duration);\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.currentIndex++;\n      this.currentStory = this.stories[this.currentIndex];\n      this.startStoryTimer();\n      this.updateNavigationSlider();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.currentIndex--;\n      this.currentStory = this.stories[this.currentIndex];\n      this.startStoryTimer();\n      this.updateNavigationSlider();\n    }\n  }\n  jumpToStoryIndex(index) {\n    if (index >= 0 && index < this.stories.length && index !== this.currentIndex) {\n      this.currentIndex = index;\n      this.currentStory = this.stories[index];\n      this.startStoryTimer();\n      this.updateNavigationSlider();\n    }\n  }\n  handleStoryClick(event) {\n    const rect = event.target.getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n    if (clickX < width * 0.3) {\n      this.previousStory();\n    } else if (clickX > width * 0.7) {\n      this.nextStory();\n    }\n  }\n  getProgressWidth(index) {\n    if (index < this.currentIndex) return 100;\n    if (index > this.currentIndex) return 0;\n    return 0; // Will be animated by CSS\n  }\n  // Navigation slider methods\n  getStoryThumbnail(story) {\n    if (story.media.type === 'video' && story.media.thumbnail) {\n      return story.media.thumbnail;\n    }\n    return story.media.url;\n  }\n  getThumbnailProgress(index) {\n    if (index < this.currentIndex) return 100;\n    if (index > this.currentIndex) return 0;\n    if (index === this.currentIndex) {\n      // Calculate current progress based on timer\n      return 0; // Will be updated by progress animation\n    }\n    return 0;\n  }\n  scrollStoriesLeft() {\n    if (this.storyThumbnails) {\n      const container = this.storyThumbnails.nativeElement;\n      container.scrollBy({\n        left: -200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateScrollButtons(), 300);\n    }\n  }\n  scrollStoriesRight() {\n    if (this.storyThumbnails) {\n      const container = this.storyThumbnails.nativeElement;\n      container.scrollBy({\n        left: 200,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateScrollButtons(), 300);\n    }\n  }\n  updateNavigationSlider() {\n    if (this.storyThumbnails && this.stories.length > 5) {\n      const container = this.storyThumbnails.nativeElement;\n      const thumbnailWidth = 56; // 48px + 8px gap\n      const containerWidth = container.clientWidth;\n      const currentThumbnailPosition = this.currentIndex * thumbnailWidth;\n      // Center the current thumbnail\n      const scrollPosition = currentThumbnailPosition - containerWidth / 2 + thumbnailWidth / 2;\n      container.scrollTo({\n        left: scrollPosition,\n        behavior: 'smooth'\n      });\n      setTimeout(() => this.updateScrollButtons(), 300);\n    }\n  }\n  updateScrollButtons() {\n    if (this.storyThumbnails) {\n      const container = this.storyThumbnails.nativeElement;\n      this.canScrollLeft = container.scrollLeft > 0;\n      this.canScrollRight = container.scrollLeft < container.scrollWidth - container.clientWidth;\n    }\n  }\n  closeStories() {\n    // Navigate back to the previous page or home\n    if (window.history.length > 1) {\n      window.history.back();\n    } else {\n      this.router.navigate(['/social']);\n    }\n  }\n  // E-commerce actions\n  buyNow() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n  addToCart() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      // TODO: Add to cart via service\n      console.log('Add to cart from story:', product);\n    }\n  }\n  addToWishlist() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      // TODO: Add to wishlist via service\n      console.log('Add to wishlist from story:', product);\n    }\n  }\n  // Social actions\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    // TODO: Like/unlike story via API\n  }\n  openComments() {\n    this.showCommentsModal = true;\n    this.loadComments();\n  }\n  closeComments() {\n    this.showCommentsModal = false;\n  }\n  shareStory() {\n    // TODO: Implement share functionality\n    console.log('Share story:', this.currentStory);\n  }\n  toggleSound() {\n    this.isMuted = !this.isMuted;\n    if (this.storyVideo) {\n      this.storyVideo.nativeElement.muted = this.isMuted;\n    }\n  }\n  // Product modal\n  showProductModal(product) {\n    this.selectedProduct = product;\n  }\n  closeProductModal() {\n    this.selectedProduct = null;\n  }\n  buyProductNow() {\n    if (this.selectedProduct) {\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: this.selectedProduct._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n  addProductToCart() {\n    if (this.selectedProduct) {\n      // TODO: Add to cart via service\n      console.log('Add product to cart:', this.selectedProduct);\n      this.closeProductModal();\n    }\n  }\n  addProductToWishlist() {\n    if (this.selectedProduct) {\n      // TODO: Add to wishlist via service\n      console.log('Add product to wishlist:', this.selectedProduct);\n      this.closeProductModal();\n    }\n  }\n  // Comments\n  loadComments() {\n    // Load comments from API\n    this.comments = [];\n  }\n  addComment() {\n    if (this.newComment.trim()) {\n      // TODO: Add comment via API\n      console.log('Add comment:', this.newComment);\n      this.newComment = '';\n    }\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diffMs = now.getTime() - new Date(date).getTime();\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    if (diffHours < 1) return 'now';\n    if (diffHours < 24) return `${diffHours}h`;\n    return `${Math.floor(diffHours / 24)}d`;\n  }\n  // Keyboard and touch event listeners\n  addKeyboardListeners() {\n    document.addEventListener('keydown', event => {\n      if (event.key === 'ArrowLeft') {\n        this.previousStory();\n      } else if (event.key === 'ArrowRight') {\n        this.nextStory();\n      } else if (event.key === 'Escape') {\n        this.closeStories();\n      }\n    });\n  }\n  addTouchListeners() {\n    let startX = 0;\n    let startY = 0;\n    document.addEventListener('touchstart', event => {\n      startX = event.touches[0].clientX;\n      startY = event.touches[0].clientY;\n    });\n    document.addEventListener('touchend', event => {\n      const endX = event.changedTouches[0].clientX;\n      const endY = event.changedTouches[0].clientY;\n      const diffX = startX - endX;\n      const diffY = startY - endY;\n      // Horizontal swipe\n      if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {\n        if (diffX > 0) {\n          this.nextStory(); // Swipe left - next story\n        } else {\n          this.previousStory(); // Swipe right - previous story\n        }\n      }\n      // Vertical swipe down to close\n      else if (diffY < -100) {\n        this.closeStories();\n      }\n    });\n  }\n  static {\n    this.ɵfac = function StoriesViewerComponent_Factory(t) {\n      return new (t || StoriesViewerComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StoriesViewerComponent,\n      selectors: [[\"app-stories-viewer\"]],\n      viewQuery: function StoriesViewerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storyVideo = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storyThumbnails = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 3,\n      consts: [[\"storyMedia\", \"\"], [\"storyVideo\", \"\"], [\"storyThumbnails\", \"\"], [\"class\", \"stories-viewer\", \"tabindex\", \"0\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [\"class\", \"product-modal\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"comments-modal\", 3, \"click\", 4, \"ngIf\"], [\"tabindex\", \"0\", 1, \"stories-viewer\", 3, \"click\", \"keydown\"], [1, \"progress-container\"], [\"class\", \"progress-bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"username\"], [1, \"timestamp\"], [1, \"story-controls\"], [\"class\", \"btn-sound\", 3, \"muted\", \"click\", 4, \"ngIf\"], [1, \"btn-pause\", 3, \"click\"], [1, \"fas\"], [1, \"btn-close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story-content\", 3, \"touchstart\", \"touchend\", \"mousedown\", \"mouseup\"], [1, \"nav-area\", \"nav-prev\", 3, \"click\"], [1, \"nav-area\", \"nav-next\", 3, \"click\"], [\"class\", \"story-media\", 3, \"src\", \"alt\", \"load\", 4, \"ngIf\"], [\"class\", \"story-media\", 3, \"src\", \"poster\", \"muted\", \"autoplay\", \"loop\", \"loadeddata\", \"ended\", 4, \"ngIf\"], [\"class\", \"product-tags\", 3, \"show-tags\", 4, \"ngIf\"], [\"class\", \"shopping-indicator\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"story-caption\", 4, \"ngIf\"], [\"class\", \"story-navigation\", 4, \"ngIf\"], [1, \"story-content\"], [\"class\", \"story-media\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"story-media\", 3, \"src\", \"poster\", \"muted\", \"autoplay\", \"loop\", \"ended\", 4, \"ngIf\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"left\", \"top\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-actions\"], [\"class\", \"ecommerce-actions\", 4, \"ngIf\"], [1, \"social-actions\"], [1, \"social-btn\", \"like\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"social-btn\", \"comment\", 3, \"click\"], [1, \"fas\", \"fa-comment\"], [1, \"social-btn\", \"share\", 3, \"click\"], [1, \"fas\", \"fa-share\"], [\"class\", \"social-btn sound\", 3, \"click\", 4, \"ngIf\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"btn-sound\", 3, \"click\"], [1, \"story-media\", 3, \"load\", \"src\", \"alt\"], [1, \"story-media\", 3, \"loadeddata\", \"ended\", \"src\", \"poster\", \"muted\", \"autoplay\", \"loop\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-dot\"], [1, \"product-pulse\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"shopping-indicator\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"story-caption\"], [1, \"story-navigation\"], [1, \"nav-slider-container\"], [1, \"nav-slider-btn\", \"prev\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"story-thumbnails\"], [\"class\", \"story-thumbnail\", 3, \"active\", \"viewed\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-slider-btn\", \"next\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"story-thumbnail\", 3, \"click\"], [1, \"thumbnail-image\", 3, \"src\", \"alt\"], [1, \"thumbnail-overlay\"], [1, \"user-thumbnail-avatar\", 3, \"src\", \"alt\"], [1, \"thumbnail-progress\"], [1, \"story-media\", 3, \"src\", \"alt\"], [1, \"story-media\", 3, \"ended\", \"src\", \"poster\", \"muted\", \"autoplay\", \"loop\"], [1, \"product-tag-icon\"], [1, \"product-tag-info\"], [1, \"ecommerce-actions\"], [1, \"action-btn\", \"buy-now\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [1, \"action-btn\", \"add-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"action-btn\", \"wishlist\", 3, \"click\"], [1, \"social-btn\", \"sound\", 3, \"click\"], [1, \"product-modal\", 3, \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"modal-header\"], [1, \"modal-body\"], [1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-details\"], [1, \"brand\"], [1, \"price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"modal-actions\"], [1, \"btn-primary\", 3, \"click\"], [1, \"btn-secondary\", 3, \"click\"], [1, \"btn-outline\", 3, \"click\"], [1, \"original-price\"], [1, \"comments-modal\", 3, \"click\"], [1, \"comments-list\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [1, \"comment-input\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [3, \"click\", \"disabled\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"comment\"], [1, \"comment-avatar\", 3, \"src\", \"alt\"], [1, \"comment-content\"], [1, \"comment-username\"], [1, \"comment-text\"], [1, \"comment-time\"]],\n      template: function StoriesViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, StoriesViewerComponent_div_0_Template, 44, 26, \"div\", 3)(1, StoriesViewerComponent_div_1_Template, 24, 9, \"div\", 4)(2, StoriesViewerComponent_div_2_Template, 13, 3, \"div\", 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStory);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedProduct);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showCommentsModal);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\".stories-viewer[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 1000;\\n  display: flex;\\n  flex-direction: column;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.story-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  position: relative;\\n  z-index: 10;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 2px solid #fff;\\n}\\n\\n.user-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  color: #fff;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n}\\n\\n.timestamp[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 0.8rem;\\n}\\n\\n.btn-close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 1.2rem;\\n  cursor: pointer;\\n  padding: 8px;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n  padding: 0 20px;\\n  position: absolute;\\n  top: 8px;\\n  left: 0;\\n  right: 0;\\n  z-index: 10;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 1px;\\n  overflow: hidden;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #fff;\\n  width: 0%;\\n  transition: width 0.1s ease;\\n}\\n\\n.progress-bar.active[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progress linear;\\n}\\n\\n.progress-bar.completed[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progress {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n\\n\\n.story-navigation[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 70px;\\n  left: 0;\\n  right: 0;\\n  z-index: 15;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.4) 0%, transparent 100%);\\n  padding: 12px 0;\\n}\\n\\n.nav-slider-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 0 16px;\\n}\\n\\n.nav-slider-btn[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border: none;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.2);\\n  color: #fff;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n\\n.nav-slider-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: scale(1.1);\\n}\\n\\n.nav-slider-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n\\n.story-thumbnails[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  overflow-x: auto;\\n  scroll-behavior: smooth;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n  flex: 1;\\n  padding: 4px 0;\\n}\\n\\n.story-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n.story-thumbnail[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n  border: 2px solid transparent;\\n}\\n\\n.story-thumbnail.active[_ngcontent-%COMP%] {\\n  border-color: #fff;\\n  transform: scale(1.1);\\n}\\n\\n.story-thumbnail.viewed[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n}\\n\\n.story-thumbnail[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.thumbnail-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.thumbnail-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -2px;\\n  right: -2px;\\n  width: 20px;\\n  height: 20px;\\n  border-radius: 50%;\\n  border: 2px solid #000;\\n  overflow: hidden;\\n}\\n\\n.user-thumbnail-avatar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.thumbnail-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  height: 3px;\\n  background: #fff;\\n  transition: width 0.3s ease;\\n  border-radius: 0 0 50px 50px;\\n}\\n\\n.story-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.story-media[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  max-height: 100%;\\n  object-fit: contain;\\n}\\n\\n.product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: all;\\n  cursor: pointer;\\n  transform: translate(-50%, -50%);\\n}\\n\\n.product-tag-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #333;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.product-tag-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 40px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(0, 0, 0, 0.8);\\n  color: #fff;\\n  padding: 8px 12px;\\n  border-radius: 8px;\\n  white-space: nowrap;\\n  font-size: 0.8rem;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.product-tag[_ngcontent-%COMP%]:hover   .product-tag-info[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: translate(-50%, -50%) scale(1);\\n  }\\n  50% {\\n    transform: translate(-50%, -50%) scale(1.1);\\n  }\\n}\\n.story-caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 120px;\\n  left: 20px;\\n  right: 20px;\\n  color: #fff;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n  background: rgba(0, 0, 0, 0.5);\\n  padding: 12px;\\n  border-radius: 8px;\\n}\\n\\n.nav-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 30%;\\n  cursor: pointer;\\n  z-index: 5;\\n}\\n\\n.nav-prev[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n\\n.nav-next[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n\\n.story-actions[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n}\\n\\n.ecommerce-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px;\\n  border: none;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 6px;\\n  transition: all 0.2s ease;\\n}\\n\\n.buy-now[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n  color: #fff;\\n}\\n\\n.add-cart[_ngcontent-%COMP%] {\\n  background: #4ecdc4;\\n  color: #fff;\\n}\\n\\n.wishlist[_ngcontent-%COMP%] {\\n  background: #ff9ff3;\\n  color: #fff;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\\n}\\n\\n.social-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 24px;\\n}\\n\\n.social-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border: none;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.2);\\n  color: #fff;\\n  font-size: 1.1rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n}\\n\\n.social-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: scale(1.1);\\n}\\n\\n.social-btn.liked[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n  color: #fff;\\n}\\n\\n.product-modal[_ngcontent-%COMP%], .comments-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.8);\\n  z-index: 1100;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 12px;\\n  max-width: 400px;\\n  width: 100%;\\n  max-height: 80vh;\\n  overflow-y: auto;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 20px;\\n  border-bottom: 1px solid #eee;\\n}\\n\\n.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  object-fit: cover;\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.product-details[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.brand[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-bottom: 8px;\\n}\\n\\n.price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.current-price[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 700;\\n  color: #333;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #999;\\n  text-decoration: line-through;\\n}\\n\\n.modal-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%], .btn-outline[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  border: none;\\n  border-radius: 6px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: #fff;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: #6c757d;\\n  color: #fff;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: #007bff;\\n  border: 1px solid #007bff;\\n}\\n\\n.comments-list[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow-y: auto;\\n  margin-bottom: 16px;\\n}\\n\\n.comment[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n\\n.comment-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n}\\n\\n.comment-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.comment-username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  color: #333;\\n}\\n\\n.comment-text[_ngcontent-%COMP%] {\\n  margin: 4px 0;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n}\\n\\n.comment-time[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n\\n.comment-input[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  align-items: center;\\n  padding-top: 16px;\\n  border-top: 1px solid #eee;\\n}\\n\\n.comment-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 20px;\\n  font-size: 0.9rem;\\n}\\n\\n.comment-input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border: none;\\n  border-radius: 50%;\\n  background: #007bff;\\n  color: #fff;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.comment-input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .story-navigation[_ngcontent-%COMP%] {\\n    top: 60px;\\n    padding: 8px 0;\\n  }\\n  .nav-slider-container[_ngcontent-%COMP%] {\\n    padding: 0 12px;\\n    gap: 6px;\\n  }\\n  .nav-slider-btn[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n    font-size: 0.8rem;\\n  }\\n  .story-thumbnails[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n  .story-thumbnail[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .thumbnail-overlay[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-navigation[_ngcontent-%COMP%] {\\n    top: 55px;\\n    padding: 6px 0;\\n  }\\n  .nav-slider-container[_ngcontent-%COMP%] {\\n    padding: 0 8px;\\n    gap: 4px;\\n  }\\n  .nav-slider-btn[_ngcontent-%COMP%] {\\n    width: 24px;\\n    height: 24px;\\n    font-size: 0.7rem;\\n  }\\n  .story-thumbnails[_ngcontent-%COMP%] {\\n    gap: 4px;\\n  }\\n  .story-thumbnail[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .thumbnail-overlay[_ngcontent-%COMP%] {\\n    width: 14px;\\n    height: 14px;\\n  }\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .story-header[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .user-avatar[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .username[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .timestamp[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n  .progress-container[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n    top: 6px;\\n  }\\n  .story-caption[_ngcontent-%COMP%] {\\n    bottom: 140px;\\n    left: 16px;\\n    right: 16px;\\n    font-size: 0.85rem;\\n    padding: 10px;\\n  }\\n  .story-actions[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n    margin-bottom: 20px;\\n  }\\n  .action-btn[_ngcontent-%COMP%] {\\n    padding: 14px;\\n    font-size: 1rem;\\n    min-height: 48px; \\n\\n  }\\n  .social-actions[_ngcontent-%COMP%] {\\n    gap: 20px;\\n  }\\n  .social-btn[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n    font-size: 1.2rem;\\n  }\\n  .product-tag-icon[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n    font-size: 0.9rem;\\n  }\\n  .product-tag-info[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n    padding: 6px 10px;\\n  }\\n  .modal-content[_ngcontent-%COMP%] {\\n    margin: 10px;\\n    max-width: calc(100vw - 20px);\\n    max-height: calc(100vh - 20px);\\n  }\\n  .modal-body[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .product-image[_ngcontent-%COMP%] {\\n    height: 180px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-header[_ngcontent-%COMP%] {\\n    padding: 10px 12px;\\n  }\\n  .user-avatar[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .username[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .progress-container[_ngcontent-%COMP%] {\\n    padding: 0 12px;\\n    top: 4px;\\n  }\\n  .story-caption[_ngcontent-%COMP%] {\\n    bottom: 120px;\\n    left: 12px;\\n    right: 12px;\\n    font-size: 0.8rem;\\n    padding: 8px;\\n  }\\n  .story-actions[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    margin-bottom: 16px;\\n  }\\n  .action-btn[_ngcontent-%COMP%] {\\n    padding: 12px;\\n    font-size: 0.9rem;\\n    min-height: 44px;\\n  }\\n  .social-actions[_ngcontent-%COMP%] {\\n    gap: 16px;\\n  }\\n  .social-btn[_ngcontent-%COMP%] {\\n    width: 44px;\\n    height: 44px;\\n    font-size: 1.1rem;\\n  }\\n}\\n@media (min-width: 769px) {\\n  .stories-viewer[_ngcontent-%COMP%] {\\n    max-width: 400px;\\n    margin: 0 auto;\\n    border-radius: 12px;\\n    overflow: hidden;\\n    height: 90vh;\\n    top: 5vh;\\n  }\\n  .story-media[_ngcontent-%COMP%] {\\n    border-radius: 8px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 12px;\\n  }\\n  .action-btn[_ngcontent-%COMP%] {\\n    padding: 10px 16px;\\n    font-size: 0.9rem;\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .stories-viewer[_ngcontent-%COMP%] {\\n    max-width: 450px;\\n    height: 85vh;\\n    top: 7.5vh;\\n  }\\n}\\n\\n\\n.nav-area[_ngcontent-%COMP%] {\\n  -webkit-tap-highlight-color: transparent;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.stories-viewer[_ngcontent-%COMP%]   *[_ngcontent-%COMP%] {\\n  -webkit-touch-callout: none;\\n  -webkit-user-select: none;\\n  user-select: none;\\n}\\n\\n\\n\\n.story-media[_ngcontent-%COMP%] {\\n  transition: opacity 0.3s ease;\\n}\\n\\n.story-media.loading[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n}\\n\\n\\n\\n.btn-close[_ngcontent-%COMP%]:focus, .action-btn[_ngcontent-%COMP%]:focus, .social-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #007bff;\\n  outline-offset: 2px;\\n}\\n\\n\\n\\n.stories-viewer[_ngcontent-%COMP%] {\\n  will-change: transform;\\n  transform: translateZ(0);\\n}\\n\\n.story-media[_ngcontent-%COMP%] {\\n  will-change: opacity;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  will-change: width;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵclassProp", "i_r4", "ctx_r1", "currentIndex", "ɵɵadvance", "ɵɵstyleProp", "getProgressWidth", "getStoryDuration", "story_r3", "ɵɵlistener", "StoriesViewerComponent_div_0_button_12_Template_button_click_0_listener", "ɵɵrestoreView", "_r5", "ɵɵnextContext", "ɵɵresetView", "toggleSound", "isMuted", "StoriesViewerComponent_div_0_img_20_Template_img_load_0_listener", "_r6", "onMediaLoaded", "ɵɵproperty", "currentStory", "media", "url", "ɵɵsanitizeUrl", "caption", "StoriesViewerComponent_div_0_video_21_Template_video_loadeddata_0_listener", "_r7", "StoriesViewerComponent_div_0_video_21_Template_video_ended_0_listener", "nextStory", "thumbnail", "isPaused", "StoriesViewerComponent_div_0_div_22_div_1_Template_div_click_0_listener", "$event", "productTag_r9", "_r8", "$implicit", "showProductModal", "product", "stopPropagation", "ɵɵtext", "position", "x", "y", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "price", "ɵɵtemplate", "StoriesViewerComponent_div_0_div_22_div_1_Template", "showProductTags", "products", "StoriesViewerComponent_div_0_div_23_Template_div_click_0_listener", "_r10", "toggleProductTags", "StoriesViewerComponent_div_0_div_25_div_6_Template_div_click_0_listener", "i_r13", "_r12", "index", "jumpToStoryIndex", "getStoryThumbnail", "story_r14", "user", "username", "avatar", "fullName", "getThumbnailProgress", "StoriesViewerComponent_div_0_div_25_Template_button_click_2_listener", "_r11", "scrollStoriesLeft", "StoriesViewerComponent_div_0_div_25_div_6_Template", "StoriesViewerComponent_div_0_div_25_Template_button_click_7_listener", "scrollStoriesRight", "canScrollLeft", "stories", "canScrollRight", "StoriesViewerComponent_div_0_video_28_Template_video_ended_0_listener", "_r15", "StoriesViewerComponent_div_0_div_30_Template_div_click_0_listener", "productTag_r17", "_r16", "StoriesViewerComponent_div_0_div_35_Template_button_click_1_listener", "_r18", "buyNow", "StoriesViewerComponent_div_0_div_35_Template_button_click_4_listener", "addToCart", "StoriesViewerComponent_div_0_div_35_Template_button_click_7_listener", "addToWishlist", "StoriesViewerComponent_div_0_button_43_Template_button_click_0_listener", "_r19", "StoriesViewerComponent_div_0_Template_div_click_0_listener", "_r1", "handleStoryClick", "StoriesViewerComponent_div_0_Template_div_keydown_0_listener", "handleKeyDown", "StoriesViewerComponent_div_0_div_2_Template", "StoriesViewerComponent_div_0_button_12_Template", "StoriesViewerComponent_div_0_Template_button_click_13_listener", "toggle<PERSON><PERSON>e", "StoriesViewerComponent_div_0_Template_button_click_15_listener", "closeStories", "StoriesViewerComponent_div_0_Template_div_touchstart_17_listener", "onTouchStart", "StoriesViewerComponent_div_0_Template_div_touchend_17_listener", "onTouchEnd", "StoriesViewerComponent_div_0_Template_div_mousedown_17_listener", "onMouseDown", "StoriesViewerComponent_div_0_Template_div_mouseup_17_listener", "onMouseUp", "StoriesViewerComponent_div_0_Template_div_click_18_listener", "previousStory", "StoriesViewerComponent_div_0_Template_div_click_19_listener", "StoriesViewerComponent_div_0_img_20_Template", "StoriesViewerComponent_div_0_video_21_Template", "StoriesViewerComponent_div_0_div_22_Template", "StoriesViewerComponent_div_0_div_23_Template", "StoriesViewerComponent_div_0_div_24_Template", "StoriesViewerComponent_div_0_div_25_Template", "StoriesViewerComponent_div_0_img_27_Template", "StoriesViewerComponent_div_0_video_28_Template", "StoriesViewerComponent_div_0_div_30_Template", "StoriesViewerComponent_div_0_div_31_Template", "StoriesViewerComponent_div_0_Template_div_click_32_listener", "StoriesViewerComponent_div_0_Template_div_click_33_listener", "StoriesViewerComponent_div_0_div_35_Template", "StoriesViewerComponent_div_0_Template_button_click_37_listener", "toggleLike", "StoriesViewerComponent_div_0_Template_button_click_39_listener", "openComments", "StoriesViewerComponent_div_0_Template_button_click_41_listener", "shareStory", "StoriesViewerComponent_div_0_button_43_Template", "getTimeAgo", "createdAt", "type", "length", "isLiked", "selectedProduct", "originalPrice", "StoriesViewerComponent_div_1_Template_div_click_0_listener", "_r20", "closeProductModal", "StoriesViewerComponent_div_1_Template_div_click_1_listener", "StoriesViewerComponent_div_1_Template_button_click_5_listener", "StoriesViewerComponent_div_1_span_16_Template", "StoriesViewerComponent_div_1_Template_button_click_18_listener", "buyProductNow", "StoriesViewerComponent_div_1_Template_button_click_20_listener", "addProductToCart", "StoriesViewerComponent_div_1_Template_button_click_22_listener", "addProductToWishlist", "images", "brand", "comment_r22", "text", "commentedAt", "StoriesViewerComponent_div_2_Template_div_click_0_listener", "_r21", "closeComments", "StoriesViewerComponent_div_2_Template_div_click_1_listener", "StoriesViewerComponent_div_2_Template_button_click_5_listener", "StoriesViewerComponent_div_2_div_8_Template", "ɵɵtwoWayListener", "StoriesViewerComponent_div_2_Template_input_ngModelChange_10_listener", "ɵɵtwoWayBindingSet", "newComment", "StoriesViewerComponent_div_2_Template_input_keyup_enter_10_listener", "addComment", "StoriesViewerComponent_div_2_Template_button_click_11_listener", "comments", "ɵɵtwoWayProperty", "trim", "StoriesViewerComponent", "constructor", "router", "route", "showCommentsModal", "touchStartTime", "storyDuration", "ngOnInit", "queryParams", "subscribe", "parseInt", "params", "loadUserStories", "loadStories", "jumpToStory", "addKeyboardListeners", "addTouchListeners", "setTimeout", "updateScrollButtons", "updateNavigationSlider", "ngOnDestroy", "progressTimer", "clearTimeout", "longPressTimer", "storyVideo", "nativeElement", "pause", "play", "startStoryTimer", "event", "Date", "now", "story", "duration", "key", "fetch", "then", "response", "json", "data", "success", "filter", "isActive", "startIndex", "Math", "min", "catch", "error", "console", "alert", "userId", "storyId", "findIndex", "s", "_id", "rect", "target", "getBoundingClientRect", "clickX", "clientX", "left", "width", "storyThumbnails", "container", "scrollBy", "behavior", "thumbnailWidth", "containerWidth", "clientWidth", "currentThumbnailPosition", "scrollPosition", "scrollTo", "scrollLeft", "scrollWidth", "window", "history", "back", "navigate", "productId", "source", "log", "loadComments", "muted", "date", "diffMs", "getTime", "diffHours", "floor", "document", "addEventListener", "startX", "startY", "touches", "clientY", "endX", "changedTouches", "endY", "diffX", "diffY", "abs", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "selectors", "viewQuery", "StoriesViewerComponent_Query", "rf", "ctx", "StoriesViewerComponent_div_0_Template", "StoriesViewerComponent_div_1_Template", "StoriesViewerComponent_div_2_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\DFashion\\frontend\\src\\app\\features\\stories\\stories-viewer.component.ts"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>estroy, ViewChild, ElementRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\n\ninterface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar?: string;\n  };\n  media: {\n    type: 'image' | 'video';\n    url: string;\n    duration: number;\n    thumbnail?: string;\n  };\n  caption?: string;\n  products: {\n    _id: string;\n    product: {\n      _id: string;\n      name: string;\n      price: number;\n      originalPrice?: number;\n      images: { url: string; alt: string }[];\n      brand: string;\n    };\n    position: { x: number; y: number };\n  }[];\n  viewers: { user: string; viewedAt: Date }[];\n  isActive: boolean;\n  expiresAt: Date;\n  createdAt: Date;\n}\n\n@Component({\n  selector: 'app-stories-viewer',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"stories-viewer\" *ngIf=\"currentStory\"\n         (click)=\"handleStoryClick($event)\"\n         (keydown)=\"handleKeyDown($event)\"\n         tabindex=\"0\">\n\n      <!-- Progress Bars -->\n      <div class=\"progress-container\">\n        <div class=\"progress-bar\"\n             *ngFor=\"let story of stories; let i = index\"\n             [class.active]=\"i === currentIndex\"\n             [class.completed]=\"i < currentIndex\">\n          <div class=\"progress-fill\"\n               [style.width.%]=\"getProgressWidth(i)\"\n               [style.animation-duration.s]=\"getStoryDuration(story)\"></div>\n        </div>\n      </div>\n\n      <!-- Header -->\n      <div class=\"story-header\">\n        <div class=\"user-info\">\n          <img [src]=\"currentStory.user.avatar || '/assets/images/default-avatar.png'\"\n               [alt]=\"currentStory.user.fullName\" class=\"user-avatar\">\n          <div class=\"user-details\">\n            <span class=\"username\">{{ currentStory.user.username }}</span>\n            <span class=\"timestamp\">{{ getTimeAgo(currentStory.createdAt) }}</span>\n          </div>\n        </div>\n\n        <div class=\"story-controls\">\n          <button class=\"btn-sound\"\n                  *ngIf=\"currentStory.media.type === 'video'\"\n                  (click)=\"toggleSound()\"\n                  [class.muted]=\"isMuted\">\n            <i class=\"fas\" [class.fa-volume-up]=\"!isMuted\" [class.fa-volume-mute]=\"isMuted\"></i>\n          </button>\n          <button class=\"btn-pause\" (click)=\"togglePause()\" [class.paused]=\"isPaused\">\n            <i class=\"fas\" [class.fa-pause]=\"!isPaused\" [class.fa-play]=\"isPaused\"></i>\n          </button>\n          <button class=\"btn-close\" (click)=\"closeStories()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story-content\"\n           (touchstart)=\"onTouchStart($event)\"\n           (touchend)=\"onTouchEnd($event)\"\n           (mousedown)=\"onMouseDown($event)\"\n           (mouseup)=\"onMouseUp($event)\">\n\n        <!-- Navigation Areas -->\n        <div class=\"nav-area nav-prev\" (click)=\"previousStory()\"></div>\n        <div class=\"nav-area nav-next\" (click)=\"nextStory()\"></div>\n\n        <!-- Image Story -->\n        <img *ngIf=\"currentStory.media.type === 'image'\"\n             [src]=\"currentStory.media.url\"\n             [alt]=\"currentStory.caption\"\n             class=\"story-media\"\n             (load)=\"onMediaLoaded()\"\n             #storyMedia>\n\n        <!-- Video Story -->\n        <video *ngIf=\"currentStory.media.type === 'video'\"\n               [src]=\"currentStory.media.url\"\n               class=\"story-media\"\n               [poster]=\"currentStory.media.thumbnail\"\n               [muted]=\"isMuted\"\n               [autoplay]=\"!isPaused\"\n               [loop]=\"false\"\n               #storyVideo\n               (loadeddata)=\"onMediaLoaded()\"\n               (ended)=\"nextStory()\">\n        </video>\n\n        <!-- Product Tags (Instagram-style) -->\n        <div class=\"product-tags\"\n             [class.show-tags]=\"showProductTags\"\n             *ngIf=\"currentStory.products && currentStory.products.length > 0\">\n          <div class=\"product-tag\"\n               *ngFor=\"let productTag of currentStory.products\"\n               [style.left.%]=\"productTag.position.x\"\n               [style.top.%]=\"productTag.position.y\"\n               (click)=\"showProductModal(productTag.product); $event.stopPropagation()\">\n            <div class=\"product-dot\">\n              <div class=\"product-pulse\"></div>\n            </div>\n            <div class=\"product-info\">\n              <span class=\"product-name\">{{ productTag.product.name }}</span>\n              <span class=\"product-price\">₹{{ productTag.product.price | number:'1.0-0' }}</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Shopping indicator -->\n        <div class=\"shopping-indicator\"\n             *ngIf=\"currentStory.products && currentStory.products.length > 0 && !showProductTags\"\n             (click)=\"toggleProductTags(); $event.stopPropagation()\">\n          <i class=\"fas fa-shopping-bag\"></i>\n          <span>Tap to view products</span>\n        </div>\n\n        <!-- Caption -->\n        <div class=\"story-caption\" *ngIf=\"currentStory.caption\">\n          {{ currentStory.caption }}\n        </div>\n      </div>\n\n      <!-- Story Navigation Slider (for many stories) -->\n      <div class=\"story-navigation\" *ngIf=\"stories.length > 5\">\n        <div class=\"nav-slider-container\">\n          <button class=\"nav-slider-btn prev\"\n                  (click)=\"scrollStoriesLeft()\"\n                  [disabled]=\"!canScrollLeft\">\n            <i class=\"fas fa-chevron-left\"></i>\n          </button>\n\n          <div class=\"story-thumbnails\" #storyThumbnails>\n            <div class=\"story-thumbnail\"\n                 *ngFor=\"let story of stories; let i = index\"\n                 [class.active]=\"i === currentIndex\"\n                 [class.viewed]=\"i < currentIndex\"\n                 (click)=\"jumpToStoryIndex(i)\">\n              <img [src]=\"getStoryThumbnail(story)\"\n                   [alt]=\"story.user.username\"\n                   class=\"thumbnail-image\">\n              <div class=\"thumbnail-overlay\">\n                <img [src]=\"story.user.avatar || '/assets/images/default-avatar.png'\"\n                     [alt]=\"story.user.fullName\"\n                     class=\"user-thumbnail-avatar\">\n              </div>\n              <div class=\"thumbnail-progress\"\n                   [style.width.%]=\"getThumbnailProgress(i)\"></div>\n            </div>\n          </div>\n\n          <button class=\"nav-slider-btn next\"\n                  (click)=\"scrollStoriesRight()\"\n                  [disabled]=\"!canScrollRight\">\n            <i class=\"fas fa-chevron-right\"></i>\n          </button>\n        </div>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story-content\">\n        <!-- Image Story -->\n        <img *ngIf=\"currentStory.media.type === 'image'\" \n             [src]=\"currentStory.media.url\" \n             [alt]=\"currentStory.caption\"\n             class=\"story-media\"\n             #storyMedia>\n\n        <!-- Video Story -->\n        <video *ngIf=\"currentStory.media.type === 'video'\"\n               [src]=\"currentStory.media.url\"\n               class=\"story-media\"\n               [poster]=\"currentStory.media.thumbnail\"\n               [muted]=\"isMuted\"\n               [autoplay]=\"true\"\n               [loop]=\"false\"\n               #storyVideo\n               (ended)=\"nextStory()\">\n        </video>\n\n        <!-- Product Tags -->\n        <div class=\"product-tags\">\n          <div class=\"product-tag\" \n               *ngFor=\"let productTag of currentStory.products\"\n               [style.left.%]=\"productTag.position.x\"\n               [style.top.%]=\"productTag.position.y\"\n               (click)=\"showProductModal(productTag.product)\">\n            <div class=\"product-tag-icon\">\n              <i class=\"fas fa-shopping-bag\"></i>\n            </div>\n            <div class=\"product-tag-info\">\n              <span class=\"product-name\">{{ productTag.product.name }}</span>\n              <span class=\"product-price\">₹{{ productTag.product.price | number:'1.0-0' }}</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Caption -->\n        <div class=\"story-caption\" *ngIf=\"currentStory.caption\">\n          {{ currentStory.caption }}\n        </div>\n      </div>\n\n      <!-- Navigation Areas -->\n      <div class=\"nav-area nav-prev\" (click)=\"previousStory()\"></div>\n      <div class=\"nav-area nav-next\" (click)=\"nextStory()\"></div>\n\n      <!-- Bottom Actions -->\n      <div class=\"story-actions\">\n        <!-- E-commerce Actions -->\n        <div class=\"ecommerce-actions\" *ngIf=\"currentStory.products.length > 0\">\n          <button class=\"action-btn buy-now\" (click)=\"buyNow()\">\n            <i class=\"fas fa-bolt\"></i>\n            Buy Now\n          </button>\n          <button class=\"action-btn add-cart\" (click)=\"addToCart()\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            Add to Cart\n          </button>\n          <button class=\"action-btn wishlist\" (click)=\"addToWishlist()\">\n            <i class=\"fas fa-heart\"></i>\n            Wishlist\n          </button>\n        </div>\n\n        <!-- Social Actions -->\n        <div class=\"social-actions\">\n          <button class=\"social-btn like\" [class.liked]=\"isLiked\" (click)=\"toggleLike()\">\n            <i class=\"fas fa-heart\"></i>\n          </button>\n          <button class=\"social-btn comment\" (click)=\"openComments()\">\n            <i class=\"fas fa-comment\"></i>\n          </button>\n          <button class=\"social-btn share\" (click)=\"shareStory()\">\n            <i class=\"fas fa-share\"></i>\n          </button>\n          <button class=\"social-btn sound\" (click)=\"toggleSound()\" *ngIf=\"currentStory.media.type === 'video'\">\n            <i class=\"fas\" [class.fa-volume-up]=\"!isMuted\" [class.fa-volume-mute]=\"isMuted\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Product Modal -->\n    <div class=\"product-modal\" *ngIf=\"selectedProduct\" (click)=\"closeProductModal()\">\n      <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n        <div class=\"modal-header\">\n          <h3>{{ selectedProduct.name }}</h3>\n          <button class=\"btn-close\" (click)=\"closeProductModal()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        \n        <div class=\"modal-body\">\n          <img [src]=\"selectedProduct.images[0]?.url\" [alt]=\"selectedProduct.name\" class=\"product-image\">\n          \n          <div class=\"product-details\">\n            <p class=\"brand\">{{ selectedProduct.brand }}</p>\n            <div class=\"price\">\n              <span class=\"current-price\">₹{{ selectedProduct.price | number:'1.0-0' }}</span>\n              <span class=\"original-price\" *ngIf=\"selectedProduct.originalPrice\">\n                ₹{{ selectedProduct.originalPrice | number:'1.0-0' }}\n              </span>\n            </div>\n          </div>\n          \n          <div class=\"modal-actions\">\n            <button class=\"btn-primary\" (click)=\"buyProductNow()\">Buy Now</button>\n            <button class=\"btn-secondary\" (click)=\"addProductToCart()\">Add to Cart</button>\n            <button class=\"btn-outline\" (click)=\"addProductToWishlist()\">Add to Wishlist</button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Comments Modal -->\n    <div class=\"comments-modal\" *ngIf=\"showCommentsModal\" (click)=\"closeComments()\">\n      <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n        <div class=\"modal-header\">\n          <h3>Comments</h3>\n          <button class=\"btn-close\" (click)=\"closeComments()\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n        \n        <div class=\"comments-list\">\n          <div class=\"comment\" *ngFor=\"let comment of comments\">\n            <img [src]=\"comment.user.avatar || '/assets/images/default-avatar.png'\" \n                 [alt]=\"comment.user.fullName\" class=\"comment-avatar\">\n            <div class=\"comment-content\">\n              <span class=\"comment-username\">{{ comment.user.username }}</span>\n              <p class=\"comment-text\">{{ comment.text }}</p>\n              <span class=\"comment-time\">{{ getTimeAgo(comment.commentedAt) }}</span>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"comment-input\">\n          <input type=\"text\" \n                 [(ngModel)]=\"newComment\" \n                 placeholder=\"Add a comment...\"\n                 (keyup.enter)=\"addComment()\">\n          <button (click)=\"addComment()\" [disabled]=\"!newComment.trim()\">\n            <i class=\"fas fa-paper-plane\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .stories-viewer {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: #000;\n      z-index: 1000;\n      display: flex;\n      flex-direction: column;\n      user-select: none;\n    }\n\n    .story-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 20px;\n      background: linear-gradient(180deg, rgba(0,0,0,0.6) 0%, transparent 100%);\n      position: relative;\n      z-index: 10;\n    }\n\n    .user-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .user-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      border: 2px solid #fff;\n    }\n\n    .user-details {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .username {\n      color: #fff;\n      font-weight: 600;\n      font-size: 0.9rem;\n    }\n\n    .timestamp {\n      color: rgba(255,255,255,0.7);\n      font-size: 0.8rem;\n    }\n\n    .btn-close {\n      background: none;\n      border: none;\n      color: #fff;\n      font-size: 1.2rem;\n      cursor: pointer;\n      padding: 8px;\n    }\n\n    .progress-container {\n      display: flex;\n      gap: 2px;\n      padding: 0 20px;\n      position: absolute;\n      top: 8px;\n      left: 0;\n      right: 0;\n      z-index: 10;\n    }\n\n    .progress-bar {\n      flex: 1;\n      height: 2px;\n      background: rgba(255,255,255,0.3);\n      border-radius: 1px;\n      overflow: hidden;\n    }\n\n    .progress-fill {\n      height: 100%;\n      background: #fff;\n      width: 0%;\n      transition: width 0.1s ease;\n    }\n\n    .progress-bar.active .progress-fill {\n      animation: progress linear;\n    }\n\n    .progress-bar.completed .progress-fill {\n      width: 100%;\n    }\n\n    @keyframes progress {\n      from { width: 0%; }\n      to { width: 100%; }\n    }\n\n    /* Story Navigation Slider */\n    .story-navigation {\n      position: absolute;\n      top: 70px;\n      left: 0;\n      right: 0;\n      z-index: 15;\n      background: linear-gradient(180deg, rgba(0,0,0,0.4) 0%, transparent 100%);\n      padding: 12px 0;\n    }\n\n    .nav-slider-container {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      padding: 0 16px;\n    }\n\n    .nav-slider-btn {\n      width: 32px;\n      height: 32px;\n      border: none;\n      border-radius: 50%;\n      background: rgba(255,255,255,0.2);\n      color: #fff;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      transition: all 0.2s ease;\n      flex-shrink: 0;\n    }\n\n    .nav-slider-btn:hover:not(:disabled) {\n      background: rgba(255,255,255,0.3);\n      transform: scale(1.1);\n    }\n\n    .nav-slider-btn:disabled {\n      opacity: 0.3;\n      cursor: not-allowed;\n    }\n\n    .story-thumbnails {\n      display: flex;\n      gap: 8px;\n      overflow-x: auto;\n      scroll-behavior: smooth;\n      scrollbar-width: none;\n      -ms-overflow-style: none;\n      flex: 1;\n      padding: 4px 0;\n    }\n\n    .story-thumbnails::-webkit-scrollbar {\n      display: none;\n    }\n\n    .story-thumbnail {\n      position: relative;\n      width: 48px;\n      height: 48px;\n      border-radius: 50%;\n      overflow: hidden;\n      cursor: pointer;\n      transition: all 0.2s ease;\n      flex-shrink: 0;\n      border: 2px solid transparent;\n    }\n\n    .story-thumbnail.active {\n      border-color: #fff;\n      transform: scale(1.1);\n    }\n\n    .story-thumbnail.viewed {\n      opacity: 0.6;\n    }\n\n    .story-thumbnail:hover {\n      transform: scale(1.05);\n    }\n\n    .thumbnail-image {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n\n    .thumbnail-overlay {\n      position: absolute;\n      bottom: -2px;\n      right: -2px;\n      width: 20px;\n      height: 20px;\n      border-radius: 50%;\n      border: 2px solid #000;\n      overflow: hidden;\n    }\n\n    .user-thumbnail-avatar {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n\n    .thumbnail-progress {\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      height: 3px;\n      background: #fff;\n      transition: width 0.3s ease;\n      border-radius: 0 0 50px 50px;\n    }\n\n    .story-content {\n      flex: 1;\n      position: relative;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .story-media {\n      max-width: 100%;\n      max-height: 100%;\n      object-fit: contain;\n    }\n\n    .product-tags {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      pointer-events: none;\n    }\n\n    .product-tag {\n      position: absolute;\n      pointer-events: all;\n      cursor: pointer;\n      transform: translate(-50%, -50%);\n    }\n\n    .product-tag-icon {\n      width: 32px;\n      height: 32px;\n      background: rgba(255,255,255,0.9);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #333;\n      animation: pulse 2s infinite;\n    }\n\n    .product-tag-info {\n      position: absolute;\n      top: 40px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0,0,0,0.8);\n      color: #fff;\n      padding: 8px 12px;\n      border-radius: 8px;\n      white-space: nowrap;\n      font-size: 0.8rem;\n      opacity: 0;\n      transition: opacity 0.3s ease;\n    }\n\n    .product-tag:hover .product-tag-info {\n      opacity: 1;\n    }\n\n    @keyframes pulse {\n      0%, 100% { transform: translate(-50%, -50%) scale(1); }\n      50% { transform: translate(-50%, -50%) scale(1.1); }\n    }\n\n    .story-caption {\n      position: absolute;\n      bottom: 120px;\n      left: 20px;\n      right: 20px;\n      color: #fff;\n      font-size: 0.9rem;\n      line-height: 1.4;\n      background: rgba(0,0,0,0.5);\n      padding: 12px;\n      border-radius: 8px;\n    }\n\n    .nav-area {\n      position: absolute;\n      top: 0;\n      bottom: 0;\n      width: 30%;\n      cursor: pointer;\n      z-index: 5;\n    }\n\n    .nav-prev {\n      left: 0;\n    }\n\n    .nav-next {\n      right: 0;\n    }\n\n    .story-actions {\n      padding: 20px;\n      background: linear-gradient(0deg, rgba(0,0,0,0.6) 0%, transparent 100%);\n    }\n\n    .ecommerce-actions {\n      display: flex;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n\n    .action-btn {\n      flex: 1;\n      padding: 12px;\n      border: none;\n      border-radius: 8px;\n      font-weight: 600;\n      font-size: 0.9rem;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      transition: all 0.2s ease;\n    }\n\n    .buy-now {\n      background: #ff6b6b;\n      color: #fff;\n    }\n\n    .add-cart {\n      background: #4ecdc4;\n      color: #fff;\n    }\n\n    .wishlist {\n      background: #ff9ff3;\n      color: #fff;\n    }\n\n    .action-btn:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 4px 12px rgba(0,0,0,0.3);\n    }\n\n    .social-actions {\n      display: flex;\n      justify-content: center;\n      gap: 24px;\n    }\n\n    .social-btn {\n      width: 44px;\n      height: 44px;\n      border: none;\n      border-radius: 50%;\n      background: rgba(255,255,255,0.2);\n      color: #fff;\n      font-size: 1.1rem;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      transition: all 0.2s ease;\n    }\n\n    .social-btn:hover {\n      background: rgba(255,255,255,0.3);\n      transform: scale(1.1);\n    }\n\n    .social-btn.liked {\n      background: #ff6b6b;\n      color: #fff;\n    }\n\n    .product-modal, .comments-modal {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100vw;\n      height: 100vh;\n      background: rgba(0,0,0,0.8);\n      z-index: 1100;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n    }\n\n    .modal-content {\n      background: #fff;\n      border-radius: 12px;\n      max-width: 400px;\n      width: 100%;\n      max-height: 80vh;\n      overflow-y: auto;\n    }\n\n    .modal-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 20px;\n      border-bottom: 1px solid #eee;\n    }\n\n    .modal-header h3 {\n      margin: 0;\n      font-size: 1.1rem;\n      font-weight: 600;\n    }\n\n    .modal-body {\n      padding: 20px;\n    }\n\n    .product-image {\n      width: 100%;\n      height: 200px;\n      object-fit: cover;\n      border-radius: 8px;\n      margin-bottom: 16px;\n    }\n\n    .product-details {\n      margin-bottom: 20px;\n    }\n\n    .brand {\n      color: #666;\n      font-size: 0.9rem;\n      margin-bottom: 8px;\n    }\n\n    .price {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .current-price {\n      font-size: 1.2rem;\n      font-weight: 700;\n      color: #333;\n    }\n\n    .original-price {\n      font-size: 0.9rem;\n      color: #999;\n      text-decoration: line-through;\n    }\n\n    .modal-actions {\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n    }\n\n    .btn-primary, .btn-secondary, .btn-outline {\n      padding: 12px;\n      border: none;\n      border-radius: 6px;\n      font-weight: 600;\n      cursor: pointer;\n      transition: all 0.2s ease;\n    }\n\n    .btn-primary {\n      background: #007bff;\n      color: #fff;\n    }\n\n    .btn-secondary {\n      background: #6c757d;\n      color: #fff;\n    }\n\n    .btn-outline {\n      background: transparent;\n      color: #007bff;\n      border: 1px solid #007bff;\n    }\n\n    .comments-list {\n      max-height: 300px;\n      overflow-y: auto;\n      margin-bottom: 16px;\n    }\n\n    .comment {\n      display: flex;\n      gap: 12px;\n      margin-bottom: 16px;\n    }\n\n    .comment-avatar {\n      width: 32px;\n      height: 32px;\n      border-radius: 50%;\n    }\n\n    .comment-content {\n      flex: 1;\n    }\n\n    .comment-username {\n      font-weight: 600;\n      font-size: 0.9rem;\n      color: #333;\n    }\n\n    .comment-text {\n      margin: 4px 0;\n      font-size: 0.9rem;\n      line-height: 1.4;\n    }\n\n    .comment-time {\n      font-size: 0.8rem;\n      color: #666;\n    }\n\n    .comment-input {\n      display: flex;\n      gap: 8px;\n      align-items: center;\n      padding-top: 16px;\n      border-top: 1px solid #eee;\n    }\n\n    .comment-input input {\n      flex: 1;\n      padding: 8px 12px;\n      border: 1px solid #ddd;\n      border-radius: 20px;\n      font-size: 0.9rem;\n    }\n\n    .comment-input button {\n      width: 36px;\n      height: 36px;\n      border: none;\n      border-radius: 50%;\n      background: #007bff;\n      color: #fff;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .comment-input button:disabled {\n      background: #ccc;\n      cursor: not-allowed;\n    }\n\n    /* Responsive Navigation Slider */\n    @media (max-width: 768px) {\n      .story-navigation {\n        top: 60px;\n        padding: 8px 0;\n      }\n\n      .nav-slider-container {\n        padding: 0 12px;\n        gap: 6px;\n      }\n\n      .nav-slider-btn {\n        width: 28px;\n        height: 28px;\n        font-size: 0.8rem;\n      }\n\n      .story-thumbnails {\n        gap: 6px;\n      }\n\n      .story-thumbnail {\n        width: 40px;\n        height: 40px;\n      }\n\n      .thumbnail-overlay {\n        width: 16px;\n        height: 16px;\n      }\n    }\n\n    @media (max-width: 480px) {\n      .story-navigation {\n        top: 55px;\n        padding: 6px 0;\n      }\n\n      .nav-slider-container {\n        padding: 0 8px;\n        gap: 4px;\n      }\n\n      .nav-slider-btn {\n        width: 24px;\n        height: 24px;\n        font-size: 0.7rem;\n      }\n\n      .story-thumbnails {\n        gap: 4px;\n      }\n\n      .story-thumbnail {\n        width: 36px;\n        height: 36px;\n      }\n\n      .thumbnail-overlay {\n        width: 14px;\n        height: 14px;\n      }\n    }\n\n    /* Enhanced Responsive Design */\n    @media (max-width: 768px) {\n      .story-header {\n        padding: 12px 16px;\n      }\n\n      .user-avatar {\n        width: 36px;\n        height: 36px;\n      }\n\n      .username {\n        font-size: 0.85rem;\n      }\n\n      .timestamp {\n        font-size: 0.75rem;\n      }\n\n      .progress-container {\n        padding: 0 16px;\n        top: 6px;\n      }\n\n      .story-caption {\n        bottom: 140px;\n        left: 16px;\n        right: 16px;\n        font-size: 0.85rem;\n        padding: 10px;\n      }\n\n      .story-actions {\n        padding: 16px;\n      }\n\n      .ecommerce-actions {\n        flex-direction: column;\n        gap: 10px;\n        margin-bottom: 20px;\n      }\n\n      .action-btn {\n        padding: 14px;\n        font-size: 1rem;\n        min-height: 48px; /* Touch-friendly */\n      }\n\n      .social-actions {\n        gap: 20px;\n      }\n\n      .social-btn {\n        width: 48px;\n        height: 48px;\n        font-size: 1.2rem;\n      }\n\n      .product-tag-icon {\n        width: 28px;\n        height: 28px;\n        font-size: 0.9rem;\n      }\n\n      .product-tag-info {\n        font-size: 0.75rem;\n        padding: 6px 10px;\n      }\n\n      .modal-content {\n        margin: 10px;\n        max-width: calc(100vw - 20px);\n        max-height: calc(100vh - 20px);\n      }\n\n      .modal-body {\n        padding: 16px;\n      }\n\n      .product-image {\n        height: 180px;\n      }\n    }\n\n    @media (max-width: 480px) {\n      .story-header {\n        padding: 10px 12px;\n      }\n\n      .user-avatar {\n        width: 32px;\n        height: 32px;\n      }\n\n      .username {\n        font-size: 0.8rem;\n      }\n\n      .progress-container {\n        padding: 0 12px;\n        top: 4px;\n      }\n\n      .story-caption {\n        bottom: 120px;\n        left: 12px;\n        right: 12px;\n        font-size: 0.8rem;\n        padding: 8px;\n      }\n\n      .story-actions {\n        padding: 12px;\n      }\n\n      .ecommerce-actions {\n        gap: 8px;\n        margin-bottom: 16px;\n      }\n\n      .action-btn {\n        padding: 12px;\n        font-size: 0.9rem;\n        min-height: 44px;\n      }\n\n      .social-actions {\n        gap: 16px;\n      }\n\n      .social-btn {\n        width: 44px;\n        height: 44px;\n        font-size: 1.1rem;\n      }\n    }\n\n    @media (min-width: 769px) {\n      .stories-viewer {\n        max-width: 400px;\n        margin: 0 auto;\n        border-radius: 12px;\n        overflow: hidden;\n        height: 90vh;\n        top: 5vh;\n      }\n\n      .story-media {\n        border-radius: 8px;\n      }\n\n      .ecommerce-actions {\n        flex-direction: row;\n        gap: 12px;\n      }\n\n      .action-btn {\n        padding: 10px 16px;\n        font-size: 0.9rem;\n      }\n    }\n\n    @media (min-width: 1024px) {\n      .stories-viewer {\n        max-width: 450px;\n        height: 85vh;\n        top: 7.5vh;\n      }\n    }\n\n    /* Touch and Gesture Improvements */\n    .nav-area {\n      -webkit-tap-highlight-color: transparent;\n      user-select: none;\n    }\n\n    .stories-viewer * {\n      -webkit-touch-callout: none;\n      -webkit-user-select: none;\n      -khtml-user-select: none;\n      -moz-user-select: none;\n      -ms-user-select: none;\n      user-select: none;\n    }\n\n    /* Loading States */\n    .story-media {\n      transition: opacity 0.3s ease;\n    }\n\n    .story-media.loading {\n      opacity: 0.5;\n    }\n\n    /* Accessibility Improvements */\n    .btn-close:focus,\n    .action-btn:focus,\n    .social-btn:focus {\n      outline: 2px solid #007bff;\n      outline-offset: 2px;\n    }\n\n    /* Performance Optimizations */\n    .stories-viewer {\n      will-change: transform;\n      transform: translateZ(0);\n    }\n\n    .story-media {\n      will-change: opacity;\n    }\n\n    .progress-fill {\n      will-change: width;\n    }\n  `]\n})\nexport class StoriesViewerComponent implements OnInit, OnDestroy {\n  @ViewChild('storyVideo') storyVideo!: ElementRef<HTMLVideoElement>;\n  @ViewChild('storyThumbnails') storyThumbnails!: ElementRef<HTMLDivElement>;\n\n  stories: Story[] = [];\n  currentIndex = 0;\n  currentStory!: Story;\n  isLiked = false;\n  isMuted = true;\n  isPaused = false;\n  showProductTags = false;\n  selectedProduct: any = null;\n  showCommentsModal = false;\n  comments: any[] = [];\n  newComment = '';\n\n  // Touch handling\n  private touchStartTime = 0;\n  private longPressTimer: any;\n\n  // Navigation slider properties\n  canScrollLeft = false;\n  canScrollRight = false;\n\n  private progressTimer: any;\n  private storyDuration = 15000; // 15 seconds default\n\n  constructor(\n    private router: Router,\n    private route: ActivatedRoute\n  ) {}\n\n  ngOnInit() {\n    // Handle query parameters for story index\n    this.route.queryParams.subscribe(queryParams => {\n      if (queryParams['index']) {\n        this.currentIndex = parseInt(queryParams['index'], 10) || 0;\n      }\n    });\n\n    this.route.params.subscribe(params => {\n      if (params['userId']) {\n        this.loadUserStories(params['userId']);\n      } else {\n        this.loadStories();\n      }\n      if (params['storyId']) {\n        this.jumpToStory(params['storyId']);\n      }\n    });\n\n    // Add keyboard listeners for better UX\n    this.addKeyboardListeners();\n    this.addTouchListeners();\n\n    // Initialize navigation slider after view init\n    setTimeout(() => {\n      this.updateScrollButtons();\n      this.updateNavigationSlider();\n    }, 100);\n  }\n\n  ngOnDestroy() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n    }\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n    }\n  }\n\n  // Instagram-like interactions\n  togglePause() {\n    this.isPaused = !this.isPaused;\n    if (this.storyVideo) {\n      if (this.isPaused) {\n        this.storyVideo.nativeElement.pause();\n        clearTimeout(this.progressTimer);\n      } else {\n        this.storyVideo.nativeElement.play();\n        this.startStoryTimer();\n      }\n    }\n  }\n\n  toggleProductTags() {\n    this.showProductTags = !this.showProductTags;\n    if (this.showProductTags) {\n      setTimeout(() => {\n        this.showProductTags = false;\n      }, 3000);\n    }\n  }\n\n  onTouchStart(event: TouchEvent) {\n    this.touchStartTime = Date.now();\n    this.longPressTimer = setTimeout(() => {\n      this.isPaused = true;\n      if (this.storyVideo) {\n        this.storyVideo.nativeElement.pause();\n      }\n      clearTimeout(this.progressTimer);\n    }, 200);\n  }\n\n  onTouchEnd(event: TouchEvent) {\n    clearTimeout(this.longPressTimer);\n    if (this.isPaused && Date.now() - this.touchStartTime > 200) {\n      this.isPaused = false;\n      if (this.storyVideo) {\n        this.storyVideo.nativeElement.play();\n      }\n      this.startStoryTimer();\n    }\n  }\n\n  onMouseDown(event: MouseEvent) {\n    this.touchStartTime = Date.now();\n    this.longPressTimer = setTimeout(() => {\n      this.isPaused = true;\n      if (this.storyVideo) {\n        this.storyVideo.nativeElement.pause();\n      }\n      clearTimeout(this.progressTimer);\n    }, 200);\n  }\n\n  onMouseUp(event: MouseEvent) {\n    clearTimeout(this.longPressTimer);\n    if (this.isPaused && Date.now() - this.touchStartTime > 200) {\n      this.isPaused = false;\n      if (this.storyVideo) {\n        this.storyVideo.nativeElement.play();\n      }\n      this.startStoryTimer();\n    }\n  }\n\n  onMediaLoaded() {\n    // Media loaded, start timer\n    this.startStoryTimer();\n  }\n\n  getStoryDuration(story: Story): number {\n    return story.media.type === 'video' ? story.media.duration : 15;\n  }\n\n  handleKeyDown(event: KeyboardEvent) {\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n      case ' ':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n\n  addKeyboardListeners() {\n    // Already handled in template with (keydown)\n  }\n\n  addTouchListeners() {\n    // Already handled in template with touch events\n  }\n\n  loadStories() {\n    // Load stories from real API\n    fetch('http://localhost:5000/api/stories')\n      .then(response => response.json())\n      .then(data => {\n        if (data.success) {\n          this.stories = data.stories.filter((story: any) => story.isActive);\n          if (this.stories.length > 0) {\n            // Use the index from query params or default to 0\n            const startIndex = Math.min(this.currentIndex, this.stories.length - 1);\n            this.currentIndex = startIndex;\n            this.currentStory = this.stories[startIndex];\n            this.startStoryTimer();\n            // Initialize navigation slider\n            setTimeout(() => {\n              this.updateScrollButtons();\n              this.updateNavigationSlider();\n            }, 100);\n          }\n        }\n      })\n      .catch(error => {\n        console.error('Error loading stories:', error);\n        // Show error message to user\n        alert('Failed to load stories. Please try again.');\n      });\n  }\n\n  loadUserStories(userId: string) {\n    // Load specific user's stories from real API\n    fetch(`http://localhost:5000/api/stories/user/${userId}`)\n      .then(response => response.json())\n      .then(data => {\n        if (data.success) {\n          this.stories = data.stories.filter((story: any) => story.isActive);\n          if (this.stories.length > 0) {\n            this.currentStory = this.stories[0];\n            this.startStoryTimer();\n          }\n        }\n      })\n      .catch(error => {\n        console.error('Error loading user stories:', error);\n      });\n  }\n\n  jumpToStory(storyId: string) {\n    const index = this.stories.findIndex(s => s._id === storyId);\n    if (index !== -1) {\n      this.currentIndex = index;\n      this.currentStory = this.stories[index];\n      this.startStoryTimer();\n    }\n  }\n\n  startStoryTimer() {\n    if (this.progressTimer) {\n      clearTimeout(this.progressTimer);\n    }\n    \n    const duration = this.currentStory.media.type === 'video' \n      ? this.currentStory.media.duration * 1000 \n      : this.storyDuration;\n    \n    this.progressTimer = setTimeout(() => {\n      this.nextStory();\n    }, duration);\n  }\n\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.currentIndex++;\n      this.currentStory = this.stories[this.currentIndex];\n      this.startStoryTimer();\n      this.updateNavigationSlider();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.currentIndex--;\n      this.currentStory = this.stories[this.currentIndex];\n      this.startStoryTimer();\n      this.updateNavigationSlider();\n    }\n  }\n\n  jumpToStoryIndex(index: number) {\n    if (index >= 0 && index < this.stories.length && index !== this.currentIndex) {\n      this.currentIndex = index;\n      this.currentStory = this.stories[index];\n      this.startStoryTimer();\n      this.updateNavigationSlider();\n    }\n  }\n\n  handleStoryClick(event: MouseEvent) {\n    const rect = (event.target as HTMLElement).getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n    \n    if (clickX < width * 0.3) {\n      this.previousStory();\n    } else if (clickX > width * 0.7) {\n      this.nextStory();\n    }\n  }\n\n  getProgressWidth(index: number): number {\n    if (index < this.currentIndex) return 100;\n    if (index > this.currentIndex) return 0;\n    return 0; // Will be animated by CSS\n  }\n\n  // Navigation slider methods\n  getStoryThumbnail(story: Story): string {\n    if (story.media.type === 'video' && story.media.thumbnail) {\n      return story.media.thumbnail;\n    }\n    return story.media.url;\n  }\n\n  getThumbnailProgress(index: number): number {\n    if (index < this.currentIndex) return 100;\n    if (index > this.currentIndex) return 0;\n    if (index === this.currentIndex) {\n      // Calculate current progress based on timer\n      return 0; // Will be updated by progress animation\n    }\n    return 0;\n  }\n\n  scrollStoriesLeft() {\n    if (this.storyThumbnails) {\n      const container = this.storyThumbnails.nativeElement;\n      container.scrollBy({ left: -200, behavior: 'smooth' });\n      setTimeout(() => this.updateScrollButtons(), 300);\n    }\n  }\n\n  scrollStoriesRight() {\n    if (this.storyThumbnails) {\n      const container = this.storyThumbnails.nativeElement;\n      container.scrollBy({ left: 200, behavior: 'smooth' });\n      setTimeout(() => this.updateScrollButtons(), 300);\n    }\n  }\n\n  updateNavigationSlider() {\n    if (this.storyThumbnails && this.stories.length > 5) {\n      const container = this.storyThumbnails.nativeElement;\n      const thumbnailWidth = 56; // 48px + 8px gap\n      const containerWidth = container.clientWidth;\n      const currentThumbnailPosition = this.currentIndex * thumbnailWidth;\n\n      // Center the current thumbnail\n      const scrollPosition = currentThumbnailPosition - (containerWidth / 2) + (thumbnailWidth / 2);\n      container.scrollTo({ left: scrollPosition, behavior: 'smooth' });\n\n      setTimeout(() => this.updateScrollButtons(), 300);\n    }\n  }\n\n  updateScrollButtons() {\n    if (this.storyThumbnails) {\n      const container = this.storyThumbnails.nativeElement;\n      this.canScrollLeft = container.scrollLeft > 0;\n      this.canScrollRight = container.scrollLeft < (container.scrollWidth - container.clientWidth);\n    }\n  }\n\n  closeStories() {\n    // Navigate back to the previous page or home\n    if (window.history.length > 1) {\n      window.history.back();\n    } else {\n      this.router.navigate(['/social']);\n    }\n  }\n\n  // E-commerce actions\n  buyNow() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      this.router.navigate(['/checkout'], { \n        queryParams: { productId: product._id, source: 'story' } \n      });\n    }\n  }\n\n  addToCart() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      // TODO: Add to cart via service\n      console.log('Add to cart from story:', product);\n    }\n  }\n\n  addToWishlist() {\n    if (this.currentStory.products.length > 0) {\n      const product = this.currentStory.products[0].product;\n      // TODO: Add to wishlist via service\n      console.log('Add to wishlist from story:', product);\n    }\n  }\n\n  // Social actions\n  toggleLike() {\n    this.isLiked = !this.isLiked;\n    // TODO: Like/unlike story via API\n  }\n\n  openComments() {\n    this.showCommentsModal = true;\n    this.loadComments();\n  }\n\n  closeComments() {\n    this.showCommentsModal = false;\n  }\n\n  shareStory() {\n    // TODO: Implement share functionality\n    console.log('Share story:', this.currentStory);\n  }\n\n  toggleSound() {\n    this.isMuted = !this.isMuted;\n    if (this.storyVideo) {\n      this.storyVideo.nativeElement.muted = this.isMuted;\n    }\n  }\n\n  // Product modal\n  showProductModal(product: any) {\n    this.selectedProduct = product;\n  }\n\n  closeProductModal() {\n    this.selectedProduct = null;\n  }\n\n  buyProductNow() {\n    if (this.selectedProduct) {\n      this.router.navigate(['/checkout'], { \n        queryParams: { productId: this.selectedProduct._id, source: 'story' } \n      });\n    }\n  }\n\n  addProductToCart() {\n    if (this.selectedProduct) {\n      // TODO: Add to cart via service\n      console.log('Add product to cart:', this.selectedProduct);\n      this.closeProductModal();\n    }\n  }\n\n  addProductToWishlist() {\n    if (this.selectedProduct) {\n      // TODO: Add to wishlist via service\n      console.log('Add product to wishlist:', this.selectedProduct);\n      this.closeProductModal();\n    }\n  }\n\n  // Comments\n  loadComments() {\n    // Load comments from API\n    this.comments = [];\n  }\n\n  addComment() {\n    if (this.newComment.trim()) {\n      // TODO: Add comment via API\n      console.log('Add comment:', this.newComment);\n      this.newComment = '';\n    }\n  }\n\n  getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diffMs = now.getTime() - new Date(date).getTime();\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    \n    if (diffHours < 1) return 'now';\n    if (diffHours < 24) return `${diffHours}h`;\n    return `${Math.floor(diffHours / 24)}d`;\n  }\n\n\n\n  // Keyboard and touch event listeners\n  addKeyboardListeners() {\n    document.addEventListener('keydown', (event) => {\n      if (event.key === 'ArrowLeft') {\n        this.previousStory();\n      } else if (event.key === 'ArrowRight') {\n        this.nextStory();\n      } else if (event.key === 'Escape') {\n        this.closeStories();\n      }\n    });\n  }\n\n  addTouchListeners() {\n    let startX = 0;\n    let startY = 0;\n\n    document.addEventListener('touchstart', (event) => {\n      startX = event.touches[0].clientX;\n      startY = event.touches[0].clientY;\n    });\n\n    document.addEventListener('touchend', (event) => {\n      const endX = event.changedTouches[0].clientX;\n      const endY = event.changedTouches[0].clientY;\n      const diffX = startX - endX;\n      const diffY = startY - endY;\n\n      // Horizontal swipe\n      if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {\n        if (diffX > 0) {\n          this.nextStory(); // Swipe left - next story\n        } else {\n          this.previousStory(); // Swipe right - previous story\n        }\n      }\n      // Vertical swipe down to close\n      else if (diffY < -100) {\n        this.closeStories();\n      }\n    });\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;IA+CpCC,EAAA,CAAAC,cAAA,cAG0C;IACxCD,EAAA,CAAAE,SAAA,cAEkE;IACpEF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAJDH,EADA,CAAAI,WAAA,WAAAC,IAAA,KAAAC,MAAA,CAAAC,YAAA,CAAmC,cAAAF,IAAA,GAAAC,MAAA,CAAAC,YAAA,CACC;IAElCP,EAAA,CAAAQ,SAAA,EAAqC;IACrCR,EADA,CAAAS,WAAA,UAAAH,MAAA,CAAAI,gBAAA,CAAAL,IAAA,OAAqC,uBAAAC,MAAA,CAAAK,gBAAA,CAAAC,QAAA,OACiB;;;;;;IAgB3DZ,EAAA,CAAAC,cAAA,iBAGgC;IADxBD,EAAA,CAAAa,UAAA,mBAAAC,wEAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAa,WAAA,EAAa;IAAA,EAAC;IAE7BnB,EAAA,CAAAE,SAAA,YAAoF;IACtFF,EAAA,CAAAG,YAAA,EAAS;;;;IAFDH,EAAA,CAAAI,WAAA,UAAAE,MAAA,CAAAc,OAAA,CAAuB;IACdpB,EAAA,CAAAQ,SAAA,EAA+B;IAACR,EAAhC,CAAAI,WAAA,kBAAAE,MAAA,CAAAc,OAAA,CAA+B,mBAAAd,MAAA,CAAAc,OAAA,CAAiC;;;;;;IAuBnFpB,EAAA,CAAAC,cAAA,iBAKiB;IADZD,EAAA,CAAAa,UAAA,kBAAAQ,iEAAA;MAAArB,EAAA,CAAAe,aAAA,CAAAO,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAQZ,MAAA,CAAAiB,aAAA,EAAe;IAAA,EAAC;IAJ7BvB,EAAA,CAAAG,YAAA,EAKiB;;;;IAHZH,EADA,CAAAwB,UAAA,QAAAlB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAAC,GAAA,EAAA3B,EAAA,CAAA4B,aAAA,CAA8B,QAAAtB,MAAA,CAAAmB,YAAA,CAAAI,OAAA,CACF;;;;;;IAMjC7B,EAAA,CAAAC,cAAA,mBAS6B;IAAtBD,EADA,CAAAa,UAAA,wBAAAiB,2EAAA;MAAA9B,EAAA,CAAAe,aAAA,CAAAgB,GAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAcZ,MAAA,CAAAiB,aAAA,EAAe;IAAA,EAAC,mBAAAS,sEAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAgB,GAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CACrBZ,MAAA,CAAA2B,SAAA,EAAW;IAAA,EAAC;IAC5BjC,EAAA,CAAAG,YAAA,EAAQ;;;;IAJDH,EALA,CAAAwB,UAAA,QAAAlB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAAC,GAAA,EAAA3B,EAAA,CAAA4B,aAAA,CAA8B,WAAAtB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAAQ,SAAA,EAAAlC,EAAA,CAAA4B,aAAA,CAES,UAAAtB,MAAA,CAAAc,OAAA,CACtB,cAAAd,MAAA,CAAA6B,QAAA,CACK,eACR;;;;;;IAUnBnC,EAAA,CAAAC,cAAA,cAI8E;IAAzED,EAAA,CAAAa,UAAA,mBAAAuB,wEAAAC,MAAA;MAAA,MAAAC,aAAA,GAAAtC,EAAA,CAAAe,aAAA,CAAAwB,GAAA,EAAAC,SAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAASX,MAAA,CAAAmC,gBAAA,CAAAH,aAAA,CAAAI,OAAA,CAAoC;MAAA,OAAA1C,EAAA,CAAAkB,WAAA,CAAEmB,MAAA,CAAAM,eAAA,EAAwB;IAAA,EAAC;IAC3E3C,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAE,SAAA,cAAiC;IACnCF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,eACG;IAAAD,EAAA,CAAA4C,MAAA,GAA6B;IAAA5C,EAAA,CAAAG,YAAA,EAAO;IAC/DH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAA4C,MAAA,GAAgD;;IAEhF5C,EAFgF,CAAAG,YAAA,EAAO,EAC/E,EACF;;;;IATDH,EADA,CAAAS,WAAA,SAAA6B,aAAA,CAAAO,QAAA,CAAAC,CAAA,MAAsC,QAAAR,aAAA,CAAAO,QAAA,CAAAE,CAAA,MACD;IAMX/C,EAAA,CAAAQ,SAAA,GAA6B;IAA7BR,EAAA,CAAAgD,iBAAA,CAAAV,aAAA,CAAAI,OAAA,CAAAO,IAAA,CAA6B;IAC5BjD,EAAA,CAAAQ,SAAA,GAAgD;IAAhDR,EAAA,CAAAkD,kBAAA,WAAAlD,EAAA,CAAAmD,WAAA,OAAAb,aAAA,CAAAI,OAAA,CAAAU,KAAA,eAAgD;;;;;IAblFpD,EAAA,CAAAC,cAAA,cAEuE;IACrED,EAAA,CAAAqD,UAAA,IAAAC,kDAAA,kBAI8E;IAShFtD,EAAA,CAAAG,YAAA,EAAM;;;;IAfDH,EAAA,CAAAI,WAAA,cAAAE,MAAA,CAAAiD,eAAA,CAAmC;IAGVvD,EAAA,CAAAQ,SAAA,EAAwB;IAAxBR,EAAA,CAAAwB,UAAA,YAAAlB,MAAA,CAAAmB,YAAA,CAAA+B,QAAA,CAAwB;;;;;;IAetDxD,EAAA,CAAAC,cAAA,cAE6D;IAAxDD,EAAA,CAAAa,UAAA,mBAAA4C,kEAAApB,MAAA;MAAArC,EAAA,CAAAe,aAAA,CAAA2C,IAAA;MAAA,MAAApD,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAASX,MAAA,CAAAqD,iBAAA,EAAmB;MAAA,OAAA3D,EAAA,CAAAkB,WAAA,CAAEmB,MAAA,CAAAM,eAAA,EAAwB;IAAA,EAAC;IAC1D3C,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAA4C,MAAA,2BAAoB;IAC5B5C,EAD4B,CAAAG,YAAA,EAAO,EAC7B;;;;;IAGNH,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAA4C,MAAA,GACF;IAAA5C,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAkD,kBAAA,MAAA5C,MAAA,CAAAmB,YAAA,CAAAI,OAAA,MACF;;;;;;IAaI7B,EAAA,CAAAC,cAAA,cAImC;IAA9BD,EAAA,CAAAa,UAAA,mBAAA+C,wEAAA;MAAA,MAAAC,KAAA,GAAA7D,EAAA,CAAAe,aAAA,CAAA+C,IAAA,EAAAC,KAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA0D,gBAAA,CAAAH,KAAA,CAAmB;IAAA,EAAC;IAChC7D,EAAA,CAAAE,SAAA,cAE6B;IAC7BF,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAE,SAAA,cAEmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,SAAA,cACqD;IACvDF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAZDH,EADA,CAAAI,WAAA,WAAAyD,KAAA,KAAAvD,MAAA,CAAAC,YAAA,CAAmC,WAAAsD,KAAA,GAAAvD,MAAA,CAAAC,YAAA,CACF;IAE/BP,EAAA,CAAAQ,SAAA,EAAgC;IAChCR,EADA,CAAAwB,UAAA,QAAAlB,MAAA,CAAA2D,iBAAA,CAAAC,SAAA,GAAAlE,EAAA,CAAA4B,aAAA,CAAgC,QAAAsC,SAAA,CAAAC,IAAA,CAAAC,QAAA,CACL;IAGzBpE,EAAA,CAAAQ,SAAA,GAAgE;IAChER,EADA,CAAAwB,UAAA,QAAA0C,SAAA,CAAAC,IAAA,CAAAE,MAAA,yCAAArE,EAAA,CAAA4B,aAAA,CAAgE,QAAAsC,SAAA,CAAAC,IAAA,CAAAG,QAAA,CACrC;IAI7BtE,EAAA,CAAAQ,SAAA,EAAyC;IAAzCR,EAAA,CAAAS,WAAA,UAAAH,MAAA,CAAAiE,oBAAA,CAAAV,KAAA,OAAyC;;;;;;IArBlD7D,EAFJ,CAAAC,cAAA,cAAyD,cACrB,iBAGI;IAD5BD,EAAA,CAAAa,UAAA,mBAAA2D,qEAAA;MAAAxE,EAAA,CAAAe,aAAA,CAAA0D,IAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAoE,iBAAA,EAAmB;IAAA,EAAC;IAEnC1E,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,iBAA+C;IAC7CD,EAAA,CAAAqD,UAAA,IAAAsB,kDAAA,mBAImC;IAYrC3E,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,iBAEqC;IAD7BD,EAAA,CAAAa,UAAA,mBAAA+D,qEAAA;MAAA5E,EAAA,CAAAe,aAAA,CAAA0D,IAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAuE,kBAAA,EAAoB;IAAA,EAAC;IAEpC7E,EAAA,CAAAE,SAAA,YAAoC;IAG1CF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IA7BMH,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAAwB,UAAA,cAAAlB,MAAA,CAAAwE,aAAA,CAA2B;IAMV9E,EAAA,CAAAQ,SAAA,GAAY;IAAZR,EAAA,CAAAwB,UAAA,YAAAlB,MAAA,CAAAyE,OAAA,CAAY;IAmB7B/E,EAAA,CAAAQ,SAAA,EAA4B;IAA5BR,EAAA,CAAAwB,UAAA,cAAAlB,MAAA,CAAA0E,cAAA,CAA4B;;;;;IAStChF,EAAA,CAAAE,SAAA,iBAIiB;;;;IAFZF,EADA,CAAAwB,UAAA,QAAAlB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAAC,GAAA,EAAA3B,EAAA,CAAA4B,aAAA,CAA8B,QAAAtB,MAAA,CAAAmB,YAAA,CAAAI,OAAA,CACF;;;;;;IAKjC7B,EAAA,CAAAC,cAAA,mBAQ6B;IAAtBD,EAAA,CAAAa,UAAA,mBAAAoE,sEAAA;MAAAjF,EAAA,CAAAe,aAAA,CAAAmE,IAAA;MAAA,MAAA5E,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA2B,SAAA,EAAW;IAAA,EAAC;IAC5BjC,EAAA,CAAAG,YAAA,EAAQ;;;;IAHDH,EALA,CAAAwB,UAAA,QAAAlB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAAC,GAAA,EAAA3B,EAAA,CAAA4B,aAAA,CAA8B,WAAAtB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAAQ,SAAA,EAAAlC,EAAA,CAAA4B,aAAA,CAES,UAAAtB,MAAA,CAAAc,OAAA,CACtB,kBACA,eACH;;;;;;IAOnBpB,EAAA,CAAAC,cAAA,cAIoD;IAA/CD,EAAA,CAAAa,UAAA,mBAAAsE,kEAAA;MAAA,MAAAC,cAAA,GAAApF,EAAA,CAAAe,aAAA,CAAAsE,IAAA,EAAA7C,SAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAmC,gBAAA,CAAA2C,cAAA,CAAA1C,OAAA,CAAoC;IAAA,EAAC;IACjD1C,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,YAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA8B,eACD;IAAAD,EAAA,CAAA4C,MAAA,GAA6B;IAAA5C,EAAA,CAAAG,YAAA,EAAO;IAC/DH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAA4C,MAAA,GAAgD;;IAEhF5C,EAFgF,CAAAG,YAAA,EAAO,EAC/E,EACF;;;;IATDH,EADA,CAAAS,WAAA,SAAA2E,cAAA,CAAAvC,QAAA,CAAAC,CAAA,MAAsC,QAAAsC,cAAA,CAAAvC,QAAA,CAAAE,CAAA,MACD;IAMX/C,EAAA,CAAAQ,SAAA,GAA6B;IAA7BR,EAAA,CAAAgD,iBAAA,CAAAoC,cAAA,CAAA1C,OAAA,CAAAO,IAAA,CAA6B;IAC5BjD,EAAA,CAAAQ,SAAA,GAAgD;IAAhDR,EAAA,CAAAkD,kBAAA,WAAAlD,EAAA,CAAAmD,WAAA,OAAAiC,cAAA,CAAA1C,OAAA,CAAAU,KAAA,eAAgD;;;;;IAMlFpD,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAA4C,MAAA,GACF;IAAA5C,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAkD,kBAAA,MAAA5C,MAAA,CAAAmB,YAAA,CAAAI,OAAA,MACF;;;;;;IAWE7B,EADF,CAAAC,cAAA,cAAwE,iBAChB;IAAnBD,EAAA,CAAAa,UAAA,mBAAAyE,qEAAA;MAAAtF,EAAA,CAAAe,aAAA,CAAAwE,IAAA;MAAA,MAAAjF,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAkF,MAAA,EAAQ;IAAA,EAAC;IACnDxF,EAAA,CAAAE,SAAA,YAA2B;IAC3BF,EAAA,CAAA4C,MAAA,gBACF;IAAA5C,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA0D;IAAtBD,EAAA,CAAAa,UAAA,mBAAA4E,qEAAA;MAAAzF,EAAA,CAAAe,aAAA,CAAAwE,IAAA;MAAA,MAAAjF,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAoF,SAAA,EAAW;IAAA,EAAC;IACvD1F,EAAA,CAAAE,SAAA,YAAoC;IACpCF,EAAA,CAAA4C,MAAA,oBACF;IAAA5C,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA8D;IAA1BD,EAAA,CAAAa,UAAA,mBAAA8E,qEAAA;MAAA3F,EAAA,CAAAe,aAAA,CAAAwE,IAAA;MAAA,MAAAjF,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAsF,aAAA,EAAe;IAAA,EAAC;IAC3D5F,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAA4C,MAAA,iBACF;IACF5C,EADE,CAAAG,YAAA,EAAS,EACL;;;;;;IAaJH,EAAA,CAAAC,cAAA,iBAAqG;IAApED,EAAA,CAAAa,UAAA,mBAAAgF,wEAAA;MAAA7F,EAAA,CAAAe,aAAA,CAAA+E,IAAA;MAAA,MAAAxF,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAa,WAAA,EAAa;IAAA,EAAC;IACtDnB,EAAA,CAAAE,SAAA,YAAoF;IACtFF,EAAA,CAAAG,YAAA,EAAS;;;;IADQH,EAAA,CAAAQ,SAAA,EAA+B;IAACR,EAAhC,CAAAI,WAAA,kBAAAE,MAAA,CAAAc,OAAA,CAA+B,mBAAAd,MAAA,CAAAc,OAAA,CAAiC;;;;;;IA/NvFpB,EAAA,CAAAC,cAAA,aAGkB;IADbD,EADA,CAAAa,UAAA,mBAAAkF,2DAAA1D,MAAA;MAAArC,EAAA,CAAAe,aAAA,CAAAiF,GAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA2F,gBAAA,CAAA5D,MAAA,CAAwB;IAAA,EAAC,qBAAA6D,6DAAA7D,MAAA;MAAArC,EAAA,CAAAe,aAAA,CAAAiF,GAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CACvBZ,MAAA,CAAA6F,aAAA,CAAA9D,MAAA,CAAqB;IAAA,EAAC;IAIpCrC,EAAA,CAAAC,cAAA,aAAgC;IAC9BD,EAAA,CAAAqD,UAAA,IAAA+C,2CAAA,iBAG0C;IAK5CpG,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,aAA0B,cACD;IACrBD,EAAA,CAAAE,SAAA,cAC4D;IAE1DF,EADF,CAAAC,cAAA,cAA0B,eACD;IAAAD,EAAA,CAAA4C,MAAA,GAAgC;IAAA5C,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAA4C,MAAA,IAAwC;IAEpE5C,EAFoE,CAAAG,YAAA,EAAO,EACnE,EACF;IAENH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAqD,UAAA,KAAAgD,+CAAA,qBAGgC;IAGhCrG,EAAA,CAAAC,cAAA,kBAA4E;IAAlDD,EAAA,CAAAa,UAAA,mBAAAyF,+DAAA;MAAAtG,EAAA,CAAAe,aAAA,CAAAiF,GAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAiG,WAAA,EAAa;IAAA,EAAC;IAC/CvG,EAAA,CAAAE,SAAA,aAA2E;IAC7EF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAmD;IAAzBD,EAAA,CAAAa,UAAA,mBAAA2F,+DAAA;MAAAxG,EAAA,CAAAe,aAAA,CAAAiF,GAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAmG,YAAA,EAAc;IAAA,EAAC;IAChDzG,EAAA,CAAAE,SAAA,aAA4B;IAGlCF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGNH,EAAA,CAAAC,cAAA,eAImC;IAA9BD,EAHA,CAAAa,UAAA,wBAAA6F,iEAAArE,MAAA;MAAArC,EAAA,CAAAe,aAAA,CAAAiF,GAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAcZ,MAAA,CAAAqG,YAAA,CAAAtE,MAAA,CAAoB;IAAA,EAAC,sBAAAuE,+DAAAvE,MAAA;MAAArC,EAAA,CAAAe,aAAA,CAAAiF,GAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CACvBZ,MAAA,CAAAuG,UAAA,CAAAxE,MAAA,CAAkB;IAAA,EAAC,uBAAAyE,gEAAAzE,MAAA;MAAArC,EAAA,CAAAe,aAAA,CAAAiF,GAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAClBZ,MAAA,CAAAyG,WAAA,CAAA1E,MAAA,CAAmB;IAAA,EAAC,qBAAA2E,8DAAA3E,MAAA;MAAArC,EAAA,CAAAe,aAAA,CAAAiF,GAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CACtBZ,MAAA,CAAA2G,SAAA,CAAA5E,MAAA,CAAiB;IAAA,EAAC;IAGhCrC,EAAA,CAAAC,cAAA,eAAyD;IAA1BD,EAAA,CAAAa,UAAA,mBAAAqG,4DAAA;MAAAlH,EAAA,CAAAe,aAAA,CAAAiF,GAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA6G,aAAA,EAAe;IAAA,EAAC;IAACnH,EAAA,CAAAG,YAAA,EAAM;IAC/DH,EAAA,CAAAC,cAAA,eAAqD;IAAtBD,EAAA,CAAAa,UAAA,mBAAAuG,4DAAA;MAAApH,EAAA,CAAAe,aAAA,CAAAiF,GAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA2B,SAAA,EAAW;IAAA,EAAC;IAACjC,EAAA,CAAAG,YAAA,EAAM;IAmD3DH,EAhDA,CAAAqD,UAAA,KAAAgE,4CAAA,kBAKiB,KAAAC,8CAAA,oBAYY,KAAAC,4CAAA,kBAM0C,KAAAC,4CAAA,kBAmBV,KAAAC,4CAAA,kBAML;IAG1DzH,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAqD,UAAA,KAAAqE,4CAAA,kBAAyD;IAoCzD1H,EAAA,CAAAC,cAAA,eAA2B;IASzBD,EAPA,CAAAqD,UAAA,KAAAsE,4CAAA,kBAIiB,KAAAC,8CAAA,oBAWY;IAI7B5H,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAqD,UAAA,KAAAwE,4CAAA,kBAIoD;IAStD7H,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAqD,UAAA,KAAAyE,4CAAA,kBAAwD;IAG1D9H,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAyD;IAA1BD,EAAA,CAAAa,UAAA,mBAAAkH,4DAAA;MAAA/H,EAAA,CAAAe,aAAA,CAAAiF,GAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA6G,aAAA,EAAe;IAAA,EAAC;IAACnH,EAAA,CAAAG,YAAA,EAAM;IAC/DH,EAAA,CAAAC,cAAA,eAAqD;IAAtBD,EAAA,CAAAa,UAAA,mBAAAmH,4DAAA;MAAAhI,EAAA,CAAAe,aAAA,CAAAiF,GAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA2B,SAAA,EAAW;IAAA,EAAC;IAACjC,EAAA,CAAAG,YAAA,EAAM;IAG3DH,EAAA,CAAAC,cAAA,eAA2B;IAEzBD,EAAA,CAAAqD,UAAA,KAAA4E,4CAAA,mBAAwE;IAiBtEjI,EADF,CAAAC,cAAA,eAA4B,kBACqD;IAAvBD,EAAA,CAAAa,UAAA,mBAAAqH,+DAAA;MAAAlI,EAAA,CAAAe,aAAA,CAAAiF,GAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA6H,UAAA,EAAY;IAAA,EAAC;IAC5EnI,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4D;IAAzBD,EAAA,CAAAa,UAAA,mBAAAuH,+DAAA;MAAApI,EAAA,CAAAe,aAAA,CAAAiF,GAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA+H,YAAA,EAAc;IAAA,EAAC;IACzDrI,EAAA,CAAAE,SAAA,aAA8B;IAChCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAwD;IAAvBD,EAAA,CAAAa,UAAA,mBAAAyH,+DAAA;MAAAtI,EAAA,CAAAe,aAAA,CAAAiF,GAAA;MAAA,MAAA1F,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAiI,UAAA,EAAY;IAAA,EAAC;IACrDvI,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAqD,UAAA,KAAAmF,+CAAA,qBAAqG;IAK3GxI,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IA3NqBH,EAAA,CAAAQ,SAAA,GAAY;IAAZR,EAAA,CAAAwB,UAAA,YAAAlB,MAAA,CAAAyE,OAAA,CAAY;IAY5B/E,EAAA,CAAAQ,SAAA,GAAuE;IACvER,EADA,CAAAwB,UAAA,QAAAlB,MAAA,CAAAmB,YAAA,CAAA0C,IAAA,CAAAE,MAAA,yCAAArE,EAAA,CAAA4B,aAAA,CAAuE,QAAAtB,MAAA,CAAAmB,YAAA,CAAA0C,IAAA,CAAAG,QAAA,CACrC;IAEdtE,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAAgD,iBAAA,CAAA1C,MAAA,CAAAmB,YAAA,CAAA0C,IAAA,CAAAC,QAAA,CAAgC;IAC/BpE,EAAA,CAAAQ,SAAA,GAAwC;IAAxCR,EAAA,CAAAgD,iBAAA,CAAA1C,MAAA,CAAAmI,UAAA,CAAAnI,MAAA,CAAAmB,YAAA,CAAAiH,SAAA,EAAwC;IAMzD1I,EAAA,CAAAQ,SAAA,GAAyC;IAAzCR,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAAiH,IAAA,aAAyC;IAKA3I,EAAA,CAAAQ,SAAA,EAAyB;IAAzBR,EAAA,CAAAI,WAAA,WAAAE,MAAA,CAAA6B,QAAA,CAAyB;IAC1DnC,EAAA,CAAAQ,SAAA,EAA4B;IAACR,EAA7B,CAAAI,WAAA,cAAAE,MAAA,CAAA6B,QAAA,CAA4B,YAAA7B,MAAA,CAAA6B,QAAA,CAA2B;IAoBpEnC,EAAA,CAAAQ,SAAA,GAAyC;IAAzCR,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAAiH,IAAA,aAAyC;IAQvC3I,EAAA,CAAAQ,SAAA,EAAyC;IAAzCR,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAAiH,IAAA,aAAyC;IAe3C3I,EAAA,CAAAQ,SAAA,EAA+D;IAA/DR,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAmB,YAAA,CAAA+B,QAAA,IAAAlD,MAAA,CAAAmB,YAAA,CAAA+B,QAAA,CAAAoF,MAAA,KAA+D;IAkB/D5I,EAAA,CAAAQ,SAAA,EAAmF;IAAnFR,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAmB,YAAA,CAAA+B,QAAA,IAAAlD,MAAA,CAAAmB,YAAA,CAAA+B,QAAA,CAAAoF,MAAA,SAAAtI,MAAA,CAAAiD,eAAA,CAAmF;IAO7DvD,EAAA,CAAAQ,SAAA,EAA0B;IAA1BR,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAmB,YAAA,CAAAI,OAAA,CAA0B;IAMzB7B,EAAA,CAAAQ,SAAA,EAAwB;IAAxBR,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAyE,OAAA,CAAA6D,MAAA,KAAwB;IAsC/C5I,EAAA,CAAAQ,SAAA,GAAyC;IAAzCR,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAAiH,IAAA,aAAyC;IAOvC3I,EAAA,CAAAQ,SAAA,EAAyC;IAAzCR,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAAiH,IAAA,aAAyC;IAcnB3I,EAAA,CAAAQ,SAAA,GAAwB;IAAxBR,EAAA,CAAAwB,UAAA,YAAAlB,MAAA,CAAAmB,YAAA,CAAA+B,QAAA,CAAwB;IAe1BxD,EAAA,CAAAQ,SAAA,EAA0B;IAA1BR,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAmB,YAAA,CAAAI,OAAA,CAA0B;IAYtB7B,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAmB,YAAA,CAAA+B,QAAA,CAAAoF,MAAA,KAAsC;IAiBpC5I,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAI,WAAA,UAAAE,MAAA,CAAAuI,OAAA,CAAuB;IASG7I,EAAA,CAAAQ,SAAA,GAAyC;IAAzCR,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAmB,YAAA,CAAAC,KAAA,CAAAiH,IAAA,aAAyC;;;;;IAwB/F3I,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAA4C,MAAA,GACF;;IAAA5C,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAQ,SAAA,EACF;IADER,EAAA,CAAAkD,kBAAA,YAAAlD,EAAA,CAAAmD,WAAA,OAAA7C,MAAA,CAAAwI,eAAA,CAAAC,aAAA,gBACF;;;;;;IAlBV/I,EAAA,CAAAC,cAAA,cAAiF;IAA9BD,EAAA,CAAAa,UAAA,mBAAAmI,2DAAA;MAAAhJ,EAAA,CAAAe,aAAA,CAAAkI,IAAA;MAAA,MAAA3I,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA4I,iBAAA,EAAmB;IAAA,EAAC;IAC9ElJ,EAAA,CAAAC,cAAA,cAA8D;IAAnCD,EAAA,CAAAa,UAAA,mBAAAsI,2DAAA9G,MAAA;MAAArC,EAAA,CAAAe,aAAA,CAAAkI,IAAA;MAAA,OAAAjJ,EAAA,CAAAkB,WAAA,CAASmB,MAAA,CAAAM,eAAA,EAAwB;IAAA,EAAC;IAEzD3C,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAA4C,MAAA,GAA0B;IAAA5C,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,iBAAwD;IAA9BD,EAAA,CAAAa,UAAA,mBAAAuI,8DAAA;MAAApJ,EAAA,CAAAe,aAAA,CAAAkI,IAAA;MAAA,MAAA3I,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA4I,iBAAA,EAAmB;IAAA,EAAC;IACrDlJ,EAAA,CAAAE,SAAA,YAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAENH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,SAAA,cAA+F;IAG7FF,EADF,CAAAC,cAAA,cAA6B,aACV;IAAAD,EAAA,CAAA4C,MAAA,IAA2B;IAAA5C,EAAA,CAAAG,YAAA,EAAI;IAE9CH,EADF,CAAAC,cAAA,eAAmB,gBACW;IAAAD,EAAA,CAAA4C,MAAA,IAA6C;;IAAA5C,EAAA,CAAAG,YAAA,EAAO;IAChFH,EAAA,CAAAqD,UAAA,KAAAgG,6CAAA,mBAAmE;IAIvErJ,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAA2B,kBAC6B;IAA1BD,EAAA,CAAAa,UAAA,mBAAAyI,+DAAA;MAAAtJ,EAAA,CAAAe,aAAA,CAAAkI,IAAA;MAAA,MAAA3I,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAiJ,aAAA,EAAe;IAAA,EAAC;IAACvJ,EAAA,CAAA4C,MAAA,eAAO;IAAA5C,EAAA,CAAAG,YAAA,EAAS;IACtEH,EAAA,CAAAC,cAAA,kBAA2D;IAA7BD,EAAA,CAAAa,UAAA,mBAAA2I,+DAAA;MAAAxJ,EAAA,CAAAe,aAAA,CAAAkI,IAAA;MAAA,MAAA3I,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAmJ,gBAAA,EAAkB;IAAA,EAAC;IAACzJ,EAAA,CAAA4C,MAAA,mBAAW;IAAA5C,EAAA,CAAAG,YAAA,EAAS;IAC/EH,EAAA,CAAAC,cAAA,kBAA6D;IAAjCD,EAAA,CAAAa,UAAA,mBAAA6I,+DAAA;MAAA1J,EAAA,CAAAe,aAAA,CAAAkI,IAAA;MAAA,MAAA3I,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAqJ,oBAAA,EAAsB;IAAA,EAAC;IAAC3J,EAAA,CAAA4C,MAAA,uBAAe;IAIpF5C,EAJoF,CAAAG,YAAA,EAAS,EACjF,EACF,EACF,EACF;;;;IA1BIH,EAAA,CAAAQ,SAAA,GAA0B;IAA1BR,EAAA,CAAAgD,iBAAA,CAAA1C,MAAA,CAAAwI,eAAA,CAAA7F,IAAA,CAA0B;IAOzBjD,EAAA,CAAAQ,SAAA,GAAsC;IAACR,EAAvC,CAAAwB,UAAA,QAAAlB,MAAA,CAAAwI,eAAA,CAAAc,MAAA,qBAAAtJ,MAAA,CAAAwI,eAAA,CAAAc,MAAA,IAAAjI,GAAA,EAAA3B,EAAA,CAAA4B,aAAA,CAAsC,QAAAtB,MAAA,CAAAwI,eAAA,CAAA7F,IAAA,CAA6B;IAGrDjD,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAAgD,iBAAA,CAAA1C,MAAA,CAAAwI,eAAA,CAAAe,KAAA,CAA2B;IAEd7J,EAAA,CAAAQ,SAAA,GAA6C;IAA7CR,EAAA,CAAAkD,kBAAA,WAAAlD,EAAA,CAAAmD,WAAA,QAAA7C,MAAA,CAAAwI,eAAA,CAAA1F,KAAA,eAA6C;IAC3CpD,EAAA,CAAAQ,SAAA,GAAmC;IAAnCR,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAwI,eAAA,CAAAC,aAAA,CAAmC;;;;;IA0BrE/I,EAAA,CAAAC,cAAA,eAAsD;IACpDD,EAAA,CAAAE,SAAA,eAC0D;IAExDF,EADF,CAAAC,cAAA,eAA6B,gBACI;IAAAD,EAAA,CAAA4C,MAAA,GAA2B;IAAA5C,EAAA,CAAAG,YAAA,EAAO;IACjEH,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAA4C,MAAA,GAAkB;IAAA5C,EAAA,CAAAG,YAAA,EAAI;IAC9CH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAA4C,MAAA,GAAqC;IAEpE5C,EAFoE,CAAAG,YAAA,EAAO,EACnE,EACF;;;;;IAPCH,EAAA,CAAAQ,SAAA,EAAkE;IAClER,EADA,CAAAwB,UAAA,QAAAsI,WAAA,CAAA3F,IAAA,CAAAE,MAAA,yCAAArE,EAAA,CAAA4B,aAAA,CAAkE,QAAAkI,WAAA,CAAA3F,IAAA,CAAAG,QAAA,CACrC;IAEDtE,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAAgD,iBAAA,CAAA8G,WAAA,CAAA3F,IAAA,CAAAC,QAAA,CAA2B;IAClCpE,EAAA,CAAAQ,SAAA,GAAkB;IAAlBR,EAAA,CAAAgD,iBAAA,CAAA8G,WAAA,CAAAC,IAAA,CAAkB;IACf/J,EAAA,CAAAQ,SAAA,GAAqC;IAArCR,EAAA,CAAAgD,iBAAA,CAAA1C,MAAA,CAAAmI,UAAA,CAAAqB,WAAA,CAAAE,WAAA,EAAqC;;;;;;IAhB1EhK,EAAA,CAAAC,cAAA,cAAgF;IAA1BD,EAAA,CAAAa,UAAA,mBAAAoJ,2DAAA;MAAAjK,EAAA,CAAAe,aAAA,CAAAmJ,IAAA;MAAA,MAAA5J,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA6J,aAAA,EAAe;IAAA,EAAC;IAC7EnK,EAAA,CAAAC,cAAA,cAA8D;IAAnCD,EAAA,CAAAa,UAAA,mBAAAuJ,2DAAA/H,MAAA;MAAArC,EAAA,CAAAe,aAAA,CAAAmJ,IAAA;MAAA,OAAAlK,EAAA,CAAAkB,WAAA,CAASmB,MAAA,CAAAM,eAAA,EAAwB;IAAA,EAAC;IAEzD3C,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAA4C,MAAA,eAAQ;IAAA5C,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,iBAAoD;IAA1BD,EAAA,CAAAa,UAAA,mBAAAwJ,8DAAA;MAAArK,EAAA,CAAAe,aAAA,CAAAmJ,IAAA;MAAA,MAAA5J,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA6J,aAAA,EAAe;IAAA,EAAC;IACjDnK,EAAA,CAAAE,SAAA,YAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAENH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAqD,UAAA,IAAAiH,2CAAA,mBAAsD;IASxDtK,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,eAA2B,kBAIW;IAF7BD,EAAA,CAAAuK,gBAAA,2BAAAC,sEAAAnI,MAAA;MAAArC,EAAA,CAAAe,aAAA,CAAAmJ,IAAA;MAAA,MAAA5J,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAyK,kBAAA,CAAAnK,MAAA,CAAAoK,UAAA,EAAArI,MAAA,MAAA/B,MAAA,CAAAoK,UAAA,GAAArI,MAAA;MAAA,OAAArC,EAAA,CAAAkB,WAAA,CAAAmB,MAAA;IAAA,EAAwB;IAExBrC,EAAA,CAAAa,UAAA,yBAAA8J,oEAAA;MAAA3K,EAAA,CAAAe,aAAA,CAAAmJ,IAAA;MAAA,MAAA5J,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAeZ,MAAA,CAAAsK,UAAA,EAAY;IAAA,EAAC;IAHnC5K,EAAA,CAAAG,YAAA,EAGoC;IACpCH,EAAA,CAAAC,cAAA,mBAA+D;IAAvDD,EAAA,CAAAa,UAAA,mBAAAgK,+DAAA;MAAA7K,EAAA,CAAAe,aAAA,CAAAmJ,IAAA;MAAA,MAAA5J,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAsK,UAAA,EAAY;IAAA,EAAC;IAC5B5K,EAAA,CAAAE,SAAA,cAAkC;IAI1CF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IArByCH,EAAA,CAAAQ,SAAA,GAAW;IAAXR,EAAA,CAAAwB,UAAA,YAAAlB,MAAA,CAAAwK,QAAA,CAAW;IAa7C9K,EAAA,CAAAQ,SAAA,GAAwB;IAAxBR,EAAA,CAAA+K,gBAAA,YAAAzK,MAAA,CAAAoK,UAAA,CAAwB;IAGA1K,EAAA,CAAAQ,SAAA,EAA+B;IAA/BR,EAAA,CAAAwB,UAAA,cAAAlB,MAAA,CAAAoK,UAAA,CAAAM,IAAA,GAA+B;;;AA41BxE,OAAM,MAAOC,sBAAsB;EA2BjCC,YACUC,MAAc,EACdC,KAAqB;IADrB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IAzBf,KAAArG,OAAO,GAAY,EAAE;IACrB,KAAAxE,YAAY,GAAG,CAAC;IAEhB,KAAAsI,OAAO,GAAG,KAAK;IACf,KAAAzH,OAAO,GAAG,IAAI;IACd,KAAAe,QAAQ,GAAG,KAAK;IAChB,KAAAoB,eAAe,GAAG,KAAK;IACvB,KAAAuF,eAAe,GAAQ,IAAI;IAC3B,KAAAuC,iBAAiB,GAAG,KAAK;IACzB,KAAAP,QAAQ,GAAU,EAAE;IACpB,KAAAJ,UAAU,GAAG,EAAE;IAEf;IACQ,KAAAY,cAAc,GAAG,CAAC;IAG1B;IACA,KAAAxG,aAAa,GAAG,KAAK;IACrB,KAAAE,cAAc,GAAG,KAAK;IAGd,KAAAuG,aAAa,GAAG,KAAK,CAAC,CAAC;EAK5B;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,KAAK,CAACK,WAAW,CAACC,SAAS,CAACD,WAAW,IAAG;MAC7C,IAAIA,WAAW,CAAC,OAAO,CAAC,EAAE;QACxB,IAAI,CAAClL,YAAY,GAAGoL,QAAQ,CAACF,WAAW,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;;IAE/D,CAAC,CAAC;IAEF,IAAI,CAACL,KAAK,CAACQ,MAAM,CAACF,SAAS,CAACE,MAAM,IAAG;MACnC,IAAIA,MAAM,CAAC,QAAQ,CAAC,EAAE;QACpB,IAAI,CAACC,eAAe,CAACD,MAAM,CAAC,QAAQ,CAAC,CAAC;OACvC,MAAM;QACL,IAAI,CAACE,WAAW,EAAE;;MAEpB,IAAIF,MAAM,CAAC,SAAS,CAAC,EAAE;QACrB,IAAI,CAACG,WAAW,CAACH,MAAM,CAAC,SAAS,CAAC,CAAC;;IAEvC,CAAC,CAAC;IAEF;IACA,IAAI,CAACI,oBAAoB,EAAE;IAC3B,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,mBAAmB,EAAE;MAC1B,IAAI,CAACC,sBAAsB,EAAE;IAC/B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAElC,IAAI,IAAI,CAACE,cAAc,EAAE;MACvBD,YAAY,CAAC,IAAI,CAACC,cAAc,CAAC;;EAErC;EAEA;EACAjG,WAAWA,CAAA;IACT,IAAI,CAACpE,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;IAC9B,IAAI,IAAI,CAACsK,UAAU,EAAE;MACnB,IAAI,IAAI,CAACtK,QAAQ,EAAE;QACjB,IAAI,CAACsK,UAAU,CAACC,aAAa,CAACC,KAAK,EAAE;QACrCJ,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;OACjC,MAAM;QACL,IAAI,CAACG,UAAU,CAACC,aAAa,CAACE,IAAI,EAAE;QACpC,IAAI,CAACC,eAAe,EAAE;;;EAG5B;EAEAlJ,iBAAiBA,CAAA;IACf,IAAI,CAACJ,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC5C,IAAI,IAAI,CAACA,eAAe,EAAE;MACxB2I,UAAU,CAAC,MAAK;QACd,IAAI,CAAC3I,eAAe,GAAG,KAAK;MAC9B,CAAC,EAAE,IAAI,CAAC;;EAEZ;EAEAoD,YAAYA,CAACmG,KAAiB;IAC5B,IAAI,CAACxB,cAAc,GAAGyB,IAAI,CAACC,GAAG,EAAE;IAChC,IAAI,CAACR,cAAc,GAAGN,UAAU,CAAC,MAAK;MACpC,IAAI,CAAC/J,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAACsK,UAAU,EAAE;QACnB,IAAI,CAACA,UAAU,CAACC,aAAa,CAACC,KAAK,EAAE;;MAEvCJ,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;IAClC,CAAC,EAAE,GAAG,CAAC;EACT;EAEAzF,UAAUA,CAACiG,KAAiB;IAC1BP,YAAY,CAAC,IAAI,CAACC,cAAc,CAAC;IACjC,IAAI,IAAI,CAACrK,QAAQ,IAAI4K,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC1B,cAAc,GAAG,GAAG,EAAE;MAC3D,IAAI,CAACnJ,QAAQ,GAAG,KAAK;MACrB,IAAI,IAAI,CAACsK,UAAU,EAAE;QACnB,IAAI,CAACA,UAAU,CAACC,aAAa,CAACE,IAAI,EAAE;;MAEtC,IAAI,CAACC,eAAe,EAAE;;EAE1B;EAEA9F,WAAWA,CAAC+F,KAAiB;IAC3B,IAAI,CAACxB,cAAc,GAAGyB,IAAI,CAACC,GAAG,EAAE;IAChC,IAAI,CAACR,cAAc,GAAGN,UAAU,CAAC,MAAK;MACpC,IAAI,CAAC/J,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAACsK,UAAU,EAAE;QACnB,IAAI,CAACA,UAAU,CAACC,aAAa,CAACC,KAAK,EAAE;;MAEvCJ,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;IAClC,CAAC,EAAE,GAAG,CAAC;EACT;EAEArF,SAASA,CAAC6F,KAAiB;IACzBP,YAAY,CAAC,IAAI,CAACC,cAAc,CAAC;IACjC,IAAI,IAAI,CAACrK,QAAQ,IAAI4K,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC1B,cAAc,GAAG,GAAG,EAAE;MAC3D,IAAI,CAACnJ,QAAQ,GAAG,KAAK;MACrB,IAAI,IAAI,CAACsK,UAAU,EAAE;QACnB,IAAI,CAACA,UAAU,CAACC,aAAa,CAACE,IAAI,EAAE;;MAEtC,IAAI,CAACC,eAAe,EAAE;;EAE1B;EAEAtL,aAAaA,CAAA;IACX;IACA,IAAI,CAACsL,eAAe,EAAE;EACxB;EAEAlM,gBAAgBA,CAACsM,KAAY;IAC3B,OAAOA,KAAK,CAACvL,KAAK,CAACiH,IAAI,KAAK,OAAO,GAAGsE,KAAK,CAACvL,KAAK,CAACwL,QAAQ,GAAG,EAAE;EACjE;EAEA/G,aAAaA,CAAC2G,KAAoB;IAChC,QAAQA,KAAK,CAACK,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAAChG,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;MACjB,KAAK,GAAG;QACN,IAAI,CAAClF,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAACwE,YAAY,EAAE;QACnB;;EAEN;EAEAuF,oBAAoBA,CAAA;IAClB;EAAA;EAGFC,iBAAiBA,CAAA;IACf;EAAA;EAGFH,WAAWA,CAAA;IACT;IACAsB,KAAK,CAAC,mCAAmC,CAAC,CACvCC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAG;MACX,IAAIA,IAAI,CAACC,OAAO,EAAE;QAChB,IAAI,CAAC1I,OAAO,GAAGyI,IAAI,CAACzI,OAAO,CAAC2I,MAAM,CAAET,KAAU,IAAKA,KAAK,CAACU,QAAQ,CAAC;QAClE,IAAI,IAAI,CAAC5I,OAAO,CAAC6D,MAAM,GAAG,CAAC,EAAE;UAC3B;UACA,MAAMgF,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACvN,YAAY,EAAE,IAAI,CAACwE,OAAO,CAAC6D,MAAM,GAAG,CAAC,CAAC;UACvE,IAAI,CAACrI,YAAY,GAAGqN,UAAU;UAC9B,IAAI,CAACnM,YAAY,GAAG,IAAI,CAACsD,OAAO,CAAC6I,UAAU,CAAC;UAC5C,IAAI,CAACf,eAAe,EAAE;UACtB;UACAX,UAAU,CAAC,MAAK;YACd,IAAI,CAACC,mBAAmB,EAAE;YAC1B,IAAI,CAACC,sBAAsB,EAAE;UAC/B,CAAC,EAAE,GAAG,CAAC;;;IAGb,CAAC,CAAC,CACD2B,KAAK,CAACC,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C;MACAE,KAAK,CAAC,2CAA2C,CAAC;IACpD,CAAC,CAAC;EACN;EAEArC,eAAeA,CAACsC,MAAc;IAC5B;IACAf,KAAK,CAAC,0CAA0Ce,MAAM,EAAE,CAAC,CACtDd,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAG;MACX,IAAIA,IAAI,CAACC,OAAO,EAAE;QAChB,IAAI,CAAC1I,OAAO,GAAGyI,IAAI,CAACzI,OAAO,CAAC2I,MAAM,CAAET,KAAU,IAAKA,KAAK,CAACU,QAAQ,CAAC;QAClE,IAAI,IAAI,CAAC5I,OAAO,CAAC6D,MAAM,GAAG,CAAC,EAAE;UAC3B,IAAI,CAACnH,YAAY,GAAG,IAAI,CAACsD,OAAO,CAAC,CAAC,CAAC;UACnC,IAAI,CAAC8H,eAAe,EAAE;;;IAG5B,CAAC,CAAC,CACDkB,KAAK,CAACC,KAAK,IAAG;MACbC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,CAAC;EACN;EAEAjC,WAAWA,CAACqC,OAAe;IACzB,MAAMrK,KAAK,GAAG,IAAI,CAACgB,OAAO,CAACsJ,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKH,OAAO,CAAC;IAC5D,IAAIrK,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAACxD,YAAY,GAAGwD,KAAK;MACzB,IAAI,CAACtC,YAAY,GAAG,IAAI,CAACsD,OAAO,CAAChB,KAAK,CAAC;MACvC,IAAI,CAAC8I,eAAe,EAAE;;EAE1B;EAEAA,eAAeA,CAAA;IACb,IAAI,IAAI,CAACP,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAGlC,MAAMY,QAAQ,GAAG,IAAI,CAACzL,YAAY,CAACC,KAAK,CAACiH,IAAI,KAAK,OAAO,GACrD,IAAI,CAAClH,YAAY,CAACC,KAAK,CAACwL,QAAQ,GAAG,IAAI,GACvC,IAAI,CAAC3B,aAAa;IAEtB,IAAI,CAACe,aAAa,GAAGJ,UAAU,CAAC,MAAK;MACnC,IAAI,CAACjK,SAAS,EAAE;IAClB,CAAC,EAAEiL,QAAQ,CAAC;EACd;EAEAjL,SAASA,CAAA;IACP,IAAI,IAAI,CAAC1B,YAAY,GAAG,IAAI,CAACwE,OAAO,CAAC6D,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAACrI,YAAY,EAAE;MACnB,IAAI,CAACkB,YAAY,GAAG,IAAI,CAACsD,OAAO,CAAC,IAAI,CAACxE,YAAY,CAAC;MACnD,IAAI,CAACsM,eAAe,EAAE;MACtB,IAAI,CAACT,sBAAsB,EAAE;KAC9B,MAAM;MACL,IAAI,CAAC3F,YAAY,EAAE;;EAEvB;EAEAU,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC5G,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAACkB,YAAY,GAAG,IAAI,CAACsD,OAAO,CAAC,IAAI,CAACxE,YAAY,CAAC;MACnD,IAAI,CAACsM,eAAe,EAAE;MACtB,IAAI,CAACT,sBAAsB,EAAE;;EAEjC;EAEApI,gBAAgBA,CAACD,KAAa;IAC5B,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACgB,OAAO,CAAC6D,MAAM,IAAI7E,KAAK,KAAK,IAAI,CAACxD,YAAY,EAAE;MAC5E,IAAI,CAACA,YAAY,GAAGwD,KAAK;MACzB,IAAI,CAACtC,YAAY,GAAG,IAAI,CAACsD,OAAO,CAAChB,KAAK,CAAC;MACvC,IAAI,CAAC8I,eAAe,EAAE;MACtB,IAAI,CAACT,sBAAsB,EAAE;;EAEjC;EAEAnG,gBAAgBA,CAAC6G,KAAiB;IAChC,MAAM0B,IAAI,GAAI1B,KAAK,CAAC2B,MAAsB,CAACC,qBAAqB,EAAE;IAClE,MAAMC,MAAM,GAAG7B,KAAK,CAAC8B,OAAO,GAAGJ,IAAI,CAACK,IAAI;IACxC,MAAMC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAExB,IAAIH,MAAM,GAAGG,KAAK,GAAG,GAAG,EAAE;MACxB,IAAI,CAAC3H,aAAa,EAAE;KACrB,MAAM,IAAIwH,MAAM,GAAGG,KAAK,GAAG,GAAG,EAAE;MAC/B,IAAI,CAAC7M,SAAS,EAAE;;EAEpB;EAEAvB,gBAAgBA,CAACqD,KAAa;IAC5B,IAAIA,KAAK,GAAG,IAAI,CAACxD,YAAY,EAAE,OAAO,GAAG;IACzC,IAAIwD,KAAK,GAAG,IAAI,CAACxD,YAAY,EAAE,OAAO,CAAC;IACvC,OAAO,CAAC,CAAC,CAAC;EACZ;EAEA;EACA0D,iBAAiBA,CAACgJ,KAAY;IAC5B,IAAIA,KAAK,CAACvL,KAAK,CAACiH,IAAI,KAAK,OAAO,IAAIsE,KAAK,CAACvL,KAAK,CAACQ,SAAS,EAAE;MACzD,OAAO+K,KAAK,CAACvL,KAAK,CAACQ,SAAS;;IAE9B,OAAO+K,KAAK,CAACvL,KAAK,CAACC,GAAG;EACxB;EAEA4C,oBAAoBA,CAACR,KAAa;IAChC,IAAIA,KAAK,GAAG,IAAI,CAACxD,YAAY,EAAE,OAAO,GAAG;IACzC,IAAIwD,KAAK,GAAG,IAAI,CAACxD,YAAY,EAAE,OAAO,CAAC;IACvC,IAAIwD,KAAK,KAAK,IAAI,CAACxD,YAAY,EAAE;MAC/B;MACA,OAAO,CAAC,CAAC,CAAC;;IAEZ,OAAO,CAAC;EACV;EAEAmE,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACqK,eAAe,EAAE;MACxB,MAAMC,SAAS,GAAG,IAAI,CAACD,eAAe,CAACrC,aAAa;MACpDsC,SAAS,CAACC,QAAQ,CAAC;QAAEJ,IAAI,EAAE,CAAC,GAAG;QAAEK,QAAQ,EAAE;MAAQ,CAAE,CAAC;MACtDhD,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EAEAtH,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACkK,eAAe,EAAE;MACxB,MAAMC,SAAS,GAAG,IAAI,CAACD,eAAe,CAACrC,aAAa;MACpDsC,SAAS,CAACC,QAAQ,CAAC;QAAEJ,IAAI,EAAE,GAAG;QAAEK,QAAQ,EAAE;MAAQ,CAAE,CAAC;MACrDhD,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EAEAC,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAAC2C,eAAe,IAAI,IAAI,CAAChK,OAAO,CAAC6D,MAAM,GAAG,CAAC,EAAE;MACnD,MAAMoG,SAAS,GAAG,IAAI,CAACD,eAAe,CAACrC,aAAa;MACpD,MAAMyC,cAAc,GAAG,EAAE,CAAC,CAAC;MAC3B,MAAMC,cAAc,GAAGJ,SAAS,CAACK,WAAW;MAC5C,MAAMC,wBAAwB,GAAG,IAAI,CAAC/O,YAAY,GAAG4O,cAAc;MAEnE;MACA,MAAMI,cAAc,GAAGD,wBAAwB,GAAIF,cAAc,GAAG,CAAE,GAAID,cAAc,GAAG,CAAE;MAC7FH,SAAS,CAACQ,QAAQ,CAAC;QAAEX,IAAI,EAAEU,cAAc;QAAEL,QAAQ,EAAE;MAAQ,CAAE,CAAC;MAEhEhD,UAAU,CAAC,MAAM,IAAI,CAACC,mBAAmB,EAAE,EAAE,GAAG,CAAC;;EAErD;EAEAA,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAAC4C,eAAe,EAAE;MACxB,MAAMC,SAAS,GAAG,IAAI,CAACD,eAAe,CAACrC,aAAa;MACpD,IAAI,CAAC5H,aAAa,GAAGkK,SAAS,CAACS,UAAU,GAAG,CAAC;MAC7C,IAAI,CAACzK,cAAc,GAAGgK,SAAS,CAACS,UAAU,GAAIT,SAAS,CAACU,WAAW,GAAGV,SAAS,CAACK,WAAY;;EAEhG;EAEA5I,YAAYA,CAAA;IACV;IACA,IAAIkJ,MAAM,CAACC,OAAO,CAAChH,MAAM,GAAG,CAAC,EAAE;MAC7B+G,MAAM,CAACC,OAAO,CAACC,IAAI,EAAE;KACtB,MAAM;MACL,IAAI,CAAC1E,MAAM,CAAC2E,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;;EAErC;EAEA;EACAtK,MAAMA,CAAA;IACJ,IAAI,IAAI,CAAC/D,YAAY,CAAC+B,QAAQ,CAACoF,MAAM,GAAG,CAAC,EAAE;MACzC,MAAMlG,OAAO,GAAG,IAAI,CAACjB,YAAY,CAAC+B,QAAQ,CAAC,CAAC,CAAC,CAACd,OAAO;MACrD,IAAI,CAACyI,MAAM,CAAC2E,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClCrE,WAAW,EAAE;UAAEsE,SAAS,EAAErN,OAAO,CAAC6L,GAAG;UAAEyB,MAAM,EAAE;QAAO;OACvD,CAAC;;EAEN;EAEAtK,SAASA,CAAA;IACP,IAAI,IAAI,CAACjE,YAAY,CAAC+B,QAAQ,CAACoF,MAAM,GAAG,CAAC,EAAE;MACzC,MAAMlG,OAAO,GAAG,IAAI,CAACjB,YAAY,CAAC+B,QAAQ,CAAC,CAAC,CAAC,CAACd,OAAO;MACrD;MACAuL,OAAO,CAACgC,GAAG,CAAC,yBAAyB,EAAEvN,OAAO,CAAC;;EAEnD;EAEAkD,aAAaA,CAAA;IACX,IAAI,IAAI,CAACnE,YAAY,CAAC+B,QAAQ,CAACoF,MAAM,GAAG,CAAC,EAAE;MACzC,MAAMlG,OAAO,GAAG,IAAI,CAACjB,YAAY,CAAC+B,QAAQ,CAAC,CAAC,CAAC,CAACd,OAAO;MACrD;MACAuL,OAAO,CAACgC,GAAG,CAAC,6BAA6B,EAAEvN,OAAO,CAAC;;EAEvD;EAEA;EACAyF,UAAUA,CAAA;IACR,IAAI,CAACU,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B;EACF;EAEAR,YAAYA,CAAA;IACV,IAAI,CAACgD,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAAC6E,YAAY,EAAE;EACrB;EAEA/F,aAAaA,CAAA;IACX,IAAI,CAACkB,iBAAiB,GAAG,KAAK;EAChC;EAEA9C,UAAUA,CAAA;IACR;IACA0F,OAAO,CAACgC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACxO,YAAY,CAAC;EAChD;EAEAN,WAAWA,CAAA;IACT,IAAI,CAACC,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B,IAAI,IAAI,CAACqL,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACC,aAAa,CAACyD,KAAK,GAAG,IAAI,CAAC/O,OAAO;;EAEtD;EAEA;EACAqB,gBAAgBA,CAACC,OAAY;IAC3B,IAAI,CAACoG,eAAe,GAAGpG,OAAO;EAChC;EAEAwG,iBAAiBA,CAAA;IACf,IAAI,CAACJ,eAAe,GAAG,IAAI;EAC7B;EAEAS,aAAaA,CAAA;IACX,IAAI,IAAI,CAACT,eAAe,EAAE;MACxB,IAAI,CAACqC,MAAM,CAAC2E,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClCrE,WAAW,EAAE;UAAEsE,SAAS,EAAE,IAAI,CAACjH,eAAe,CAACyF,GAAG;UAAEyB,MAAM,EAAE;QAAO;OACpE,CAAC;;EAEN;EAEAvG,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACX,eAAe,EAAE;MACxB;MACAmF,OAAO,CAACgC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACnH,eAAe,CAAC;MACzD,IAAI,CAACI,iBAAiB,EAAE;;EAE5B;EAEAS,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACb,eAAe,EAAE;MACxB;MACAmF,OAAO,CAACgC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACnH,eAAe,CAAC;MAC7D,IAAI,CAACI,iBAAiB,EAAE;;EAE5B;EAEA;EACAgH,YAAYA,CAAA;IACV;IACA,IAAI,CAACpF,QAAQ,GAAG,EAAE;EACpB;EAEAF,UAAUA,CAAA;IACR,IAAI,IAAI,CAACF,UAAU,CAACM,IAAI,EAAE,EAAE;MAC1B;MACAiD,OAAO,CAACgC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACvF,UAAU,CAAC;MAC5C,IAAI,CAACA,UAAU,GAAG,EAAE;;EAExB;EAEAjC,UAAUA,CAAC2H,IAAU;IACnB,MAAMpD,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMsD,MAAM,GAAGrD,GAAG,CAACsD,OAAO,EAAE,GAAG,IAAIvD,IAAI,CAACqD,IAAI,CAAC,CAACE,OAAO,EAAE;IACvD,MAAMC,SAAS,GAAG1C,IAAI,CAAC2C,KAAK,CAACH,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEvD,IAAIE,SAAS,GAAG,CAAC,EAAE,OAAO,KAAK;IAC/B,IAAIA,SAAS,GAAG,EAAE,EAAE,OAAO,GAAGA,SAAS,GAAG;IAC1C,OAAO,GAAG1C,IAAI,CAAC2C,KAAK,CAACD,SAAS,GAAG,EAAE,CAAC,GAAG;EACzC;EAIA;EACAvE,oBAAoBA,CAAA;IAClByE,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAG5D,KAAK,IAAI;MAC7C,IAAIA,KAAK,CAACK,GAAG,KAAK,WAAW,EAAE;QAC7B,IAAI,CAAChG,aAAa,EAAE;OACrB,MAAM,IAAI2F,KAAK,CAACK,GAAG,KAAK,YAAY,EAAE;QACrC,IAAI,CAAClL,SAAS,EAAE;OACjB,MAAM,IAAI6K,KAAK,CAACK,GAAG,KAAK,QAAQ,EAAE;QACjC,IAAI,CAAC1G,YAAY,EAAE;;IAEvB,CAAC,CAAC;EACJ;EAEAwF,iBAAiBA,CAAA;IACf,IAAI0E,MAAM,GAAG,CAAC;IACd,IAAIC,MAAM,GAAG,CAAC;IAEdH,QAAQ,CAACC,gBAAgB,CAAC,YAAY,EAAG5D,KAAK,IAAI;MAChD6D,MAAM,GAAG7D,KAAK,CAAC+D,OAAO,CAAC,CAAC,CAAC,CAACjC,OAAO;MACjCgC,MAAM,GAAG9D,KAAK,CAAC+D,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IACnC,CAAC,CAAC;IAEFL,QAAQ,CAACC,gBAAgB,CAAC,UAAU,EAAG5D,KAAK,IAAI;MAC9C,MAAMiE,IAAI,GAAGjE,KAAK,CAACkE,cAAc,CAAC,CAAC,CAAC,CAACpC,OAAO;MAC5C,MAAMqC,IAAI,GAAGnE,KAAK,CAACkE,cAAc,CAAC,CAAC,CAAC,CAACF,OAAO;MAC5C,MAAMI,KAAK,GAAGP,MAAM,GAAGI,IAAI;MAC3B,MAAMI,KAAK,GAAGP,MAAM,GAAGK,IAAI;MAE3B;MACA,IAAIpD,IAAI,CAACuD,GAAG,CAACF,KAAK,CAAC,GAAGrD,IAAI,CAACuD,GAAG,CAACD,KAAK,CAAC,IAAItD,IAAI,CAACuD,GAAG,CAACF,KAAK,CAAC,GAAG,EAAE,EAAE;QAC7D,IAAIA,KAAK,GAAG,CAAC,EAAE;UACb,IAAI,CAACjP,SAAS,EAAE,CAAC,CAAC;SACnB,MAAM;UACL,IAAI,CAACkF,aAAa,EAAE,CAAC,CAAC;;;MAG1B;MAAA,KACK,IAAIgK,KAAK,GAAG,CAAC,GAAG,EAAE;QACrB,IAAI,CAAC1K,YAAY,EAAE;;IAEvB,CAAC,CAAC;EACJ;;;uBAzfWwE,sBAAsB,EAAAjL,EAAA,CAAAqR,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAvR,EAAA,CAAAqR,iBAAA,CAAAC,EAAA,CAAAE,cAAA;IAAA;EAAA;;;YAAtBvG,sBAAsB;MAAAwG,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;UAt3B/B5R,EAtQA,CAAAqD,UAAA,IAAAyO,qCAAA,mBAGkB,IAAAC,qCAAA,kBAmO+D,IAAAC,qCAAA,kBAgCD;;;UAtQnDhS,EAAA,CAAAwB,UAAA,SAAAqQ,GAAA,CAAApQ,YAAA,CAAkB;UAsOnBzB,EAAA,CAAAQ,SAAA,EAAqB;UAArBR,EAAA,CAAAwB,UAAA,SAAAqQ,GAAA,CAAA/I,eAAA,CAAqB;UAgCpB9I,EAAA,CAAAQ,SAAA,EAAuB;UAAvBR,EAAA,CAAAwB,UAAA,SAAAqQ,GAAA,CAAAxG,iBAAA,CAAuB;;;qBAxQ5CvL,YAAY,EAAAmS,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAErS,WAAW,EAAAsS,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}