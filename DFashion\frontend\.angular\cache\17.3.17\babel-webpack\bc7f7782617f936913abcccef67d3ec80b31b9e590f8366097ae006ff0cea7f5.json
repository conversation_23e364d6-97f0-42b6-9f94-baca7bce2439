{"ast": null, "code": "import _asyncToGenerator from \"E:/Fahion/DFashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ViewAddStoriesComponent } from '../../components/instagram-stories/view-add-stories.component';\nimport { PaymentModalComponent } from '../../../../shared/components/payment-modal/payment-modal.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/product.service\";\nimport * as i3 from \"../../../../core/services/auth.service\";\nimport * as i4 from \"../../../../core/services/button-actions.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nfunction HomeComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"div\", 5);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading amazing fashion...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HomeComponent_div_2_article_5_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(post_r3.location);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_12_button_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵelement(1, \"img\", 71);\n    i0.ɵɵelementStart(2, \"div\", 72)(3, \"span\", 73);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 74);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const productTag_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r5.getTagImageUrl(productTag_r5), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r5.getTagName(productTag_r5));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getTagName(productTag_r5));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.getTagSubtitle(productTag_r5));\n  }\n}\nfunction HomeComponent_div_2_article_5_div_12_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_12_button_1_Template_button_click_0_listener() {\n      const productTag_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r5.onProductTagClick(productTag_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 67);\n    i0.ɵɵelement(2, \"i\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, HomeComponent_div_2_article_5_div_12_button_1_div_3_Template, 7, 4, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const productTag_r5 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassMap(\"tag-type-\" + (productTag_r5.navigationType || \"product\"));\n    i0.ɵɵstyleProp(\"left\", (productTag_r5.position == null ? null : productTag_r5.position.x) || 50, \"%\")(\"top\", (productTag_r5.position == null ? null : productTag_r5.position.y) || 50, \"%\");\n    i0.ɵɵproperty(\"title\", ctx_r5.getTagTooltip(productTag_r5));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"dot-\" + (productTag_r5.navigationType || \"product\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r5.getTagIcon(productTag_r5.navigationType || \"product\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", productTag_r5.showPreview);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtemplate(1, HomeComponent_div_2_article_5_div_12_button_1_Template, 4, 12, \"button\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", post_r3.products);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"span\", 76);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r5.formatLikesCount(post_r3.likes), \" likes\");\n  }\n}\nfunction HomeComponent_div_2_article_5_div_29_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵelement(1, \"img\", 88);\n    i0.ɵɵelementStart(2, \"div\", 89)(3, \"span\", 73);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 74);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r8 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r8.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r8.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.formatPrice(product_r8.price));\n  }\n}\nfunction HomeComponent_div_2_article_5_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 78);\n    i0.ɵɵtemplate(2, HomeComponent_div_2_article_5_div_29_div_2_Template, 7, 4, \"div\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 80)(4, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_29_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const post_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.buyNow(post_r3.products[0]));\n    });\n    i0.ɵɵelement(5, \"i\", 82);\n    i0.ɵɵtext(6, \" Buy Now \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_29_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const post_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.addToWishlist(post_r3.products[0]));\n    });\n    i0.ɵɵelement(8, \"i\", 84);\n    i0.ɵɵtext(9, \" Wishlist \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_29_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const post_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.addToCart(post_r3.products[0]));\n    });\n    i0.ɵɵelement(11, \"i\", 86);\n    i0.ɵɵtext(12, \" Add to Cart \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const post_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", post_r3.products.slice(0, 2));\n  }\n}\nfunction HomeComponent_div_2_article_5_div_30_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"span\", 95);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 96);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const comment_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r10.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r10.text);\n  }\n}\nfunction HomeComponent_div_2_article_5_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_div_30_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const post_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.toggleComments(post_r3));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 92);\n    i0.ɵɵtemplate(4, HomeComponent_div_2_article_5_div_30_div_4_Template, 5, 2, \"div\", 93);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" View all \", post_r3.comments.length, \" comments \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", post_r3.comments.slice(0, 2));\n  }\n}\nfunction HomeComponent_div_2_article_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"article\", 34)(1, \"header\", 35)(2, \"div\", 36);\n    i0.ɵɵelement(3, \"img\", 37);\n    i0.ɵɵelementStart(4, \"div\", 38)(5, \"h3\", 39);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, HomeComponent_div_2_article_5_span_7_Template, 2, 1, \"span\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 41);\n    i0.ɵɵelement(9, \"i\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 43);\n    i0.ɵɵelement(11, \"img\", 44);\n    i0.ɵɵtemplate(12, HomeComponent_div_2_article_5_div_12_Template, 2, 1, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 46)(14, \"div\", 47)(15, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_15_listener() {\n      const post_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.toggleLike(post_r3));\n    });\n    i0.ɵɵelement(16, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_17_listener() {\n      const post_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.focusCommentInput(post_r3));\n    });\n    i0.ɵɵelement(18, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_19_listener() {\n      const post_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.sharePost(post_r3));\n    });\n    i0.ɵɵelement(20, \"i\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_21_listener() {\n      const post_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.toggleSave(post_r3));\n    });\n    i0.ɵɵelement(22, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, HomeComponent_div_2_article_5_div_23_Template, 3, 1, \"div\", 54);\n    i0.ɵɵelementStart(24, \"div\", 55)(25, \"span\", 39);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 56);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(29, HomeComponent_div_2_article_5_div_29_Template, 13, 1, \"div\", 57)(30, HomeComponent_div_2_article_5_div_30_Template, 5, 2, \"div\", 58);\n    i0.ɵɵelementStart(31, \"div\", 59);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 60)(34, \"input\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HomeComponent_div_2_article_5_Template_input_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r5.newComment, $event) || (ctx_r5.newComment = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function HomeComponent_div_2_article_5_Template_input_keyup_enter_34_listener() {\n      const post_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.addComment(post_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_article_5_Template_button_click_35_listener() {\n      const post_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.addComment(post_r3));\n    });\n    i0.ɵɵtext(36, \"Post\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const post_r3 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", post_r3.user.avatar, i0.ɵɵsanitizeUrl)(\"alt\", post_r3.user.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(post_r3.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r3.location);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", post_r3.mediaUrl, i0.ɵɵsanitizeUrl)(\"alt\", post_r3.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r3.products && post_r3.products.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"liked\", post_r3.isLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(post_r3.isLiked ? \"fas fa-heart\" : \"far fa-heart\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"saved\", post_r3.isSaved);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(post_r3.isSaved ? \"fas fa-bookmark\" : \"far fa-bookmark\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r3.likes > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(post_r3.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(post_r3.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r3.products && post_r3.products.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r3.comments && post_r3.comments.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.getTimeAgo(post_r3.createdAt), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.newComment);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r5.newComment || !ctx_r5.newComment.trim());\n  }\n}\nfunction HomeComponent_div_2_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵelement(1, \"img\", 98);\n    i0.ɵɵelementStart(2, \"div\", 99)(3, \"h4\", 100);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 101);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 102);\n    i0.ɵɵtext(8, \"Switch\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r5.currentUser.avatar, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r5.currentUser.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.currentUser.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.currentUser.fullName);\n  }\n}\nfunction HomeComponent_div_2_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 103);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_div_15_Template_div_click_0_listener() {\n      const item_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onProductClick(item_r12));\n    });\n    i0.ɵɵelement(1, \"img\", 104);\n    i0.ɵɵelementStart(2, \"div\", 105)(3, \"span\", 106);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 107);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", item_r12.images[0] == null ? null : item_r12.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", item_r12.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.formatPrice(item_r12.price));\n  }\n}\nfunction HomeComponent_div_2_div_23_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 116);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r14 = i0.ɵɵnextContext().$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r5.formatPrice(product_r14.originalPrice));\n  }\n}\nfunction HomeComponent_div_2_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 108);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_div_23_Template_div_click_0_listener() {\n      const product_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onProductClick(product_r14));\n    });\n    i0.ɵɵelement(1, \"img\", 109);\n    i0.ɵɵelementStart(2, \"div\", 110)(3, \"span\", 111);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 112);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 113)(8, \"span\", 114);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, HomeComponent_div_2_div_23_span_10_Template, 2, 1, \"span\", 115);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r14 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r14.images[0] == null ? null : product_r14.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r14.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r14.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r14.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.formatPrice(product_r14.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r14.originalPrice);\n  }\n}\nfunction HomeComponent_div_2_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_div_31_Template_div_click_0_listener() {\n      const product_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onProductClick(product_r16));\n    });\n    i0.ɵɵelement(1, \"img\", 118);\n    i0.ɵɵelementStart(2, \"div\", 119);\n    i0.ɵɵelement(3, \"i\", 120);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Hot\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 121)(7, \"span\", 122);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 123)(10, \"span\");\n    i0.ɵɵelement(11, \"i\", 124);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵelement(14, \"i\", 125);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"span\", 126);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r16 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r16.images[0] == null ? null : product_r16.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r16.name);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(product_r16.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.formatNumber((product_r16.analytics == null ? null : product_r16.analytics.views) || 0), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.formatNumber((product_r16.analytics == null ? null : product_r16.analytics.likes) || 0), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.formatPrice(product_r16.price));\n  }\n}\nfunction HomeComponent_div_2_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_div_39_Template_div_click_0_listener() {\n      const product_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onProductClick(product_r18));\n    });\n    i0.ɵɵelement(1, \"img\", 128);\n    i0.ɵɵelementStart(2, \"div\", 129);\n    i0.ɵɵtext(3, \"New\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 130)(5, \"span\", 131);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 132);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 133);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r18 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r18.images[0] == null ? null : product_r18.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r18.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r18.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r18.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.formatPrice(product_r18.price));\n  }\n}\nfunction HomeComponent_div_2_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 134);\n    i0.ɵɵelement(1, \"img\", 135);\n    i0.ɵɵelementStart(2, \"div\", 136)(3, \"span\", 137);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 138);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 139);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_div_47_Template_button_click_7_listener() {\n      const user_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.followUser(user_r20));\n    });\n    i0.ɵɵtext(8, \"Follow\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r20 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", user_r20.avatar, i0.ɵɵsanitizeUrl)(\"alt\", user_r20.username);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r20.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r5.formatNumber(user_r20.followers), \" followers\");\n  }\n}\nfunction HomeComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"section\", 8);\n    i0.ɵɵelement(3, \"app-view-add-stories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"section\", 9);\n    i0.ɵɵtemplate(5, HomeComponent_div_2_article_5_Template, 37, 23, \"article\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"aside\", 11);\n    i0.ɵɵtemplate(7, HomeComponent_div_2_div_7_Template, 9, 4, \"div\", 12);\n    i0.ɵɵelementStart(8, \"section\", 13)(9, \"div\", 14)(10, \"h3\");\n    i0.ɵɵtext(11, \"Summer Collection 2024\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function HomeComponent_div_2_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.viewSummerCollection());\n    });\n    i0.ɵɵtext(13, \"See All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 16);\n    i0.ɵɵtemplate(15, HomeComponent_div_2_div_15_Template, 7, 4, \"div\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"section\", 13)(17, \"div\", 14)(18, \"h3\");\n    i0.ɵɵtext(19, \"Featured Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 18);\n    i0.ɵɵtext(21, \"See All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 19);\n    i0.ɵɵtemplate(23, HomeComponent_div_2_div_23_Template, 11, 6, \"div\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"section\", 13)(25, \"div\", 14)(26, \"h3\");\n    i0.ɵɵtext(27, \"Trending Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 21);\n    i0.ɵɵtext(29, \"See All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 22);\n    i0.ɵɵtemplate(31, HomeComponent_div_2_div_31_Template, 18, 6, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"section\", 13)(33, \"div\", 14)(34, \"h3\");\n    i0.ɵɵtext(35, \"New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"button\", 24);\n    i0.ɵɵtext(37, \"See All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 25);\n    i0.ɵɵtemplate(39, HomeComponent_div_2_div_39_Template, 11, 5, \"div\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"section\", 13)(41, \"div\", 14)(42, \"h3\");\n    i0.ɵɵtext(43, \"Suggested for You\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"button\", 27);\n    i0.ɵɵtext(45, \"See All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 28);\n    i0.ɵɵtemplate(47, HomeComponent_div_2_div_47_Template, 9, 4, \"div\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 30)(49, \"div\", 31)(50, \"a\", 32);\n    i0.ɵɵtext(51, \"About\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"a\", 32);\n    i0.ɵɵtext(53, \"Help\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"a\", 32);\n    i0.ɵɵtext(55, \"Press\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"a\", 32);\n    i0.ɵɵtext(57, \"API\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"a\", 32);\n    i0.ɵɵtext(59, \"Jobs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"a\", 32);\n    i0.ɵɵtext(61, \"Privacy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"a\", 32);\n    i0.ɵɵtext(63, \"Terms\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"div\", 33)(65, \"span\");\n    i0.ɵɵtext(66, \"\\u00A9 2024 DFashion\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.instagramPosts)(\"ngForTrackBy\", ctx_r5.trackByPostId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.currentUser);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.summerCollection.slice(0, 3));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.featuredProducts.slice(0, 4));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.trendingProducts.slice(0, 4));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.newArrivals.slice(0, 4));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.suggestedUsers.slice(0, 3));\n  }\n}\nexport class HomeComponent {\n  constructor(router, productService, authService, buttonActionsService) {\n    this.router = router;\n    this.productService = productService;\n    this.authService = authService;\n    this.buttonActionsService = buttonActionsService;\n    this.featuredProducts = [];\n    this.trendingProducts = [];\n    this.newArrivals = [];\n    this.trendingPosts = [];\n    this.categories = [];\n    this.isLoading = true;\n    this.isAuthenticated = false;\n    // Instagram-style data\n    this.instagramPosts = [];\n    this.summerCollection = [];\n    this.suggestedUsers = [];\n    this.currentUser = null;\n    this.newComment = '';\n    // Payment Modal\n    this.showPaymentModal = false;\n    this.paymentModalData = null;\n  }\n  ngOnInit() {\n    this.loadHomeData();\n    this.checkAuthStatus();\n    this.loadInstagramData();\n  }\n  checkAuthStatus() {\n    this.authService.currentUser$.subscribe(user => {\n      this.isAuthenticated = !!user;\n      this.currentUser = user;\n    });\n  }\n  loadInstagramData() {\n    this.loadInstagramPosts();\n    this.loadSummerCollection();\n    this.loadSuggestedUsers();\n  }\n  loadInstagramPosts() {\n    // Load real Instagram-style posts from API\n    this.authService.getPosts().subscribe({\n      next: response => {\n        this.instagramPosts = response.data || [];\n        console.log('✅ Instagram posts loaded:', this.instagramPosts.length);\n      },\n      error: error => {\n        console.error('❌ Error loading Instagram posts:', error);\n        this.instagramPosts = [];\n      }\n    });\n    {\n      id: '2', user;\n      {\n        username: 'style_guru_alex', avatar;\n        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150';\n      }\n      mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=600', caption;\n      'Elegant evening look with this silk dress 💫 Perfect for date nights! #EveningWear #SilkDress', location;\n      'New York, NY', likes;\n      892, isLiked;\n      true, isSaved;\n      false, createdAt;\n      new Date(Date.now() - 5 * 60 * 60 * 1000),\n      // 5 hours ago\n      products;\n      [{\n        navigationType: 'product',\n        position: {\n          x: 50,\n          y: 60\n        },\n        product: {\n          _id: '2',\n          name: 'Silk Slip Dress',\n          price: 159.99,\n          images: [{\n            url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=200'\n          }]\n        }\n      }, {\n        navigationType: 'category',\n        position: {\n          x: 25,\n          y: 35\n        },\n        category: {\n          _id: 'cat1',\n          name: 'Evening Wear',\n          slug: 'evening-wear',\n          productCount: 89,\n          image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=200'\n        }\n      }], comments;\n      [{\n        username: 'elegant_style',\n        text: 'Absolutely gorgeous! 💕'\n      }];\n    }\n    {\n      id: '3', user;\n      {\n        username: 'trendy_sarah', avatar;\n        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150';\n      }\n      mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600', caption;\n      'Cozy vibes in this amazing knit sweater! Perfect for autumn days 🍂 #CozyStyle #AutumnFashion', location;\n      'San Francisco, CA', likes;\n      567, isLiked;\n      false, isSaved;\n      true, createdAt;\n      new Date(Date.now() - 8 * 60 * 60 * 1000),\n      // 8 hours ago\n      products;\n      [{\n        navigationType: 'product',\n        position: {\n          x: 40,\n          y: 50\n        },\n        product: {\n          _id: '3',\n          name: 'Cozy Knit Sweater',\n          price: 79.99,\n          images: [{\n            url: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=200'\n          }]\n        }\n      }, {\n        navigationType: 'vendor',\n        position: {\n          x: 65,\n          y: 30\n        },\n        vendor: {\n          _id: 'vendor1',\n          username: 'cozy_boutique',\n          fullName: 'Cozy Boutique',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',\n          location: 'San Francisco, CA'\n        }\n      }], comments;\n      [];\n    }\n    ;\n  }\n  loadSummerCollection() {\n    // Load summer collection from API\n    this.productService.getProductsByCategory('summer').subscribe({\n      next: response => {\n        this.summerCollection = response.data || [];\n      },\n      error: error => {\n        console.error('Error loading summer collection:', error);\n        this.summerCollection = [];\n      }\n    });\n  }\n  loadSuggestedUsers() {\n    // Load suggested users from API\n    this.authService.getSuggestedUsers().subscribe({\n      next: response => {\n        this.suggestedUsers = response.data || [];\n      },\n      error: error => {\n        console.error('Error loading suggested users:', error);\n        this.suggestedUsers = [];\n      }\n    });\n  }\n  loadHomeData() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        // Load all data in parallel\n        const [featured, trending, arrivals] = yield Promise.all([_this.productService.getFeaturedProducts().toPromise(), _this.productService.getTrendingProducts().toPromise(), _this.productService.getNewArrivals().toPromise()]);\n        _this.featuredProducts = (featured?.products || featured?.data || featured || []).slice(0, 8);\n        _this.trendingProducts = (trending?.products || trending?.data || trending || []).slice(0, 8);\n        _this.newArrivals = (arrivals?.products || arrivals?.data || arrivals || []).slice(0, 8);\n        // If no data loaded, use fallback\n        if (_this.featuredProducts.length === 0) {\n          _this.featuredProducts = _this.getFallbackProducts();\n        }\n        if (_this.trendingProducts.length === 0) {\n          _this.trendingProducts = _this.getFallbackProducts();\n        }\n        if (_this.newArrivals.length === 0) {\n          _this.newArrivals = _this.getFallbackProducts();\n        }\n        // Load categories\n        _this.loadCategories();\n        // Load trending posts (mock data for now)\n        _this.loadTrendingPosts();\n      } catch (error) {\n        console.error('Error loading home data:', error);\n        _this.loadFallbackData();\n      } finally {\n        _this.isLoading = false;\n      }\n    })();\n  }\n  loadCategories() {\n    this.categories = [{\n      name: 'Women',\n      icon: 'fas fa-female',\n      color: 'linear-gradient(45deg, #f093fb, #f5576c)'\n    }, {\n      name: 'Men',\n      icon: 'fas fa-male',\n      color: 'linear-gradient(45deg, #667eea, #764ba2)'\n    }, {\n      name: 'Kids',\n      icon: 'fas fa-child',\n      color: 'linear-gradient(45deg, #4facfe, #00f2fe)'\n    }, {\n      name: 'Accessories',\n      icon: 'fas fa-gem',\n      color: 'linear-gradient(45deg, #43e97b, #38f9d7)'\n    }, {\n      name: 'Shoes',\n      icon: 'fas fa-shoe-prints',\n      color: 'linear-gradient(45deg, #fa709a, #fee140)'\n    }, {\n      name: 'Bags',\n      icon: 'fas fa-shopping-bag',\n      color: 'linear-gradient(45deg, #a8edea, #fed6e3)'\n    }];\n  }\n  loadTrendingPosts() {\n    this.trendingPosts = [{\n      title: 'Summer Fashion Trends 2024',\n      mediaUrl: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',\n      mediaType: 'image',\n      analytics: {\n        views: 15420,\n        likes: 892\n      }\n    }, {\n      title: 'Street Style Inspiration',\n      mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400',\n      mediaType: 'image',\n      analytics: {\n        views: 12890,\n        likes: 1205\n      }\n    }];\n  }\n  getFallbackProducts() {\n    return [{\n      _id: '1',\n      name: 'Vintage Denim Jacket',\n      brand: 'Urban Threads',\n      price: 89.99,\n      originalPrice: 129.99,\n      discount: 31,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400'\n      }],\n      rating: {\n        average: 4.2,\n        count: 156\n      },\n      analytics: {\n        views: 15420,\n        likes: 892\n      }\n    }, {\n      _id: '2',\n      name: 'Silk Slip Dress',\n      brand: 'Ethereal',\n      price: 159.99,\n      originalPrice: 199.99,\n      discount: 20,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400'\n      }],\n      rating: {\n        average: 4.5,\n        count: 89\n      },\n      analytics: {\n        views: 12890,\n        likes: 1205\n      }\n    }, {\n      _id: '3',\n      name: 'Cozy Knit Sweater',\n      brand: 'Cozy Co',\n      price: 79.99,\n      originalPrice: 99.99,\n      discount: 20,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400'\n      }],\n      rating: {\n        average: 4.3,\n        count: 203\n      },\n      analytics: {\n        views: 8750,\n        likes: 567\n      }\n    }, {\n      _id: '4',\n      name: 'High-Waisted Jeans',\n      brand: 'Urban Threads',\n      price: 119.99,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=400'\n      }],\n      rating: {\n        average: 4.6,\n        count: 342\n      },\n      analytics: {\n        views: 22100,\n        likes: 1456\n      }\n    }, {\n      _id: '5',\n      name: 'Floral Summer Dress',\n      brand: 'Ethereal',\n      price: 139.99,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400'\n      }],\n      rating: {\n        average: 4.4,\n        count: 128\n      },\n      analytics: {\n        views: 9870,\n        likes: 743\n      }\n    }, {\n      _id: '6',\n      name: 'Leather Ankle Boots',\n      brand: 'Urban Threads',\n      price: 199.99,\n      originalPrice: 249.99,\n      discount: 20,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400'\n      }],\n      rating: {\n        average: 4.7,\n        count: 89\n      },\n      analytics: {\n        views: 15600,\n        likes: 1123\n      }\n    }, {\n      _id: '7',\n      name: 'Oversized Blazer',\n      brand: 'Ethereal',\n      price: 189.99,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=400'\n      }],\n      rating: {\n        average: 4.1,\n        count: 76\n      },\n      analytics: {\n        views: 7890,\n        likes: 456\n      }\n    }, {\n      _id: '8',\n      name: 'Casual T-Shirt',\n      brand: 'Cozy Co',\n      price: 29.99,\n      originalPrice: 39.99,\n      discount: 25,\n      images: [{\n        url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400'\n      }],\n      rating: {\n        average: 4.0,\n        count: 234\n      },\n      analytics: {\n        views: 12340,\n        likes: 678\n      }\n    }];\n  }\n  loadFallbackData() {\n    this.featuredProducts = this.getFallbackProducts();\n    this.trendingProducts = this.getFallbackProducts();\n    this.newArrivals = this.getFallbackProducts();\n  }\n  // Navigation methods\n  onProductClick(product) {\n    this.router.navigate(['/product', product._id || product.id]);\n  }\n  onCategoryClick(category) {\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        category: category.name.toLowerCase()\n      }\n    });\n  }\n  viewAllCategories() {\n    this.router.navigate(['/shop']);\n  }\n  // Instagram-style interaction methods\n  toggleLike(post) {\n    if (!post.id && !post._id) return;\n    const postId = post.id || post._id;\n    const action = post.isLiked ? 'unlikePost' : 'likePost';\n    const observable = post.isLiked ? this.buttonActionsService.unlikePost(postId) : this.buttonActionsService.likePost(postId);\n    // Optimistic update\n    post.isLiked = !post.isLiked;\n    post.likes += post.isLiked ? 1 : -1;\n    observable.subscribe({\n      next: result => {\n        if (!result.success) {\n          // Revert on failure\n          post.isLiked = !post.isLiked;\n          post.likes += post.isLiked ? 1 : -1;\n          console.error('Failed to toggle like:', result.message);\n        }\n      },\n      error: error => {\n        // Revert on error\n        post.isLiked = !post.isLiked;\n        post.likes += post.isLiked ? 1 : -1;\n        console.error('Error toggling like:', error);\n      }\n    });\n  }\n  toggleSave(post) {\n    if (!post.id && !post._id) return;\n    const postId = post.id || post._id;\n    // Optimistic update\n    post.isSaved = !post.isSaved;\n    this.buttonActionsService.savePost(postId).subscribe({\n      next: result => {\n        if (!result.success) {\n          // Revert on failure\n          post.isSaved = !post.isSaved;\n          console.error('Failed to save post:', result.message);\n        }\n      },\n      error: error => {\n        // Revert on error\n        post.isSaved = !post.isSaved;\n        console.error('Error saving post:', error);\n      }\n    });\n  }\n  sharePost(post) {\n    if (!post.id && !post._id) return;\n    const postId = post.id || post._id;\n    this.buttonActionsService.sharePost(postId).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Post shared successfully');\n        } else {\n          console.error('Failed to share post:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error sharing post:', error);\n      }\n    });\n  }\n  focusCommentInput(post) {\n    // Focus on comment input for this post\n    console.log('Focus comment for post:', post.id);\n  }\n  addComment(post) {\n    if (!this.newComment || !this.newComment.trim()) return;\n    if (!post.id && !post._id) return;\n    const postId = post.id || post._id;\n    const commentText = this.newComment.trim();\n    this.buttonActionsService.commentOnPost({\n      postId: postId,\n      text: commentText\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          // Add comment to local state\n          if (!post.comments) {\n            post.comments = [];\n          }\n          post.comments.push({\n            username: this.currentUser?.username || 'user',\n            text: commentText\n          });\n          this.newComment = '';\n          console.log('Comment added successfully');\n        } else {\n          console.error('Failed to add comment:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error adding comment:', error);\n      }\n    });\n  }\n  showProductDetails(product) {\n    console.log('Show product details:', product);\n    // Toggle product preview\n    product.showPreview = !product.showPreview;\n  }\n  // E-commerce methods\n  buyNow(product) {\n    console.log('Buy now:', product);\n    if (!product.id && !product._id) return;\n    const productId = product.id || product._id;\n    this.buttonActionsService.buyNow({\n      productId: productId,\n      quantity: 1,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Buy now successful, redirecting to checkout');\n          // Navigation will be handled by the service\n        } else {\n          console.error('Failed to buy now:', result.message);\n          // Fallback to payment modal if needed\n          this.showPaymentModalFallback(product);\n        }\n      },\n      error: error => {\n        console.error('Error in buy now:', error);\n        // Fallback to payment modal\n        this.showPaymentModalFallback(product);\n      }\n    });\n  }\n  showPaymentModalFallback(product) {\n    if (!this.currentUser) {\n      this.router.navigate(['/auth/login'], {\n        queryParams: {\n          returnUrl: '/home'\n        }\n      });\n      return;\n    }\n    // Prepare payment modal data for single product purchase\n    this.paymentModalData = {\n      amount: product.price,\n      orderData: {\n        items: [{\n          id: product.id,\n          name: product.name,\n          price: product.price,\n          quantity: 1,\n          image: product.image\n        }],\n        subtotal: product.price,\n        tax: product.price * 0.18,\n        shipping: 0,\n        discount: 0,\n        total: product.price + product.price * 0.18\n      },\n      userDetails: {\n        name: this.currentUser.fullName || this.currentUser.username,\n        email: this.currentUser.email,\n        phone: this.currentUser.phone || ''\n      }\n    };\n    this.showPaymentModal = true;\n  }\n  addToWishlist(product) {\n    console.log('Add to wishlist:', product);\n    if (!product.id && !product._id) return;\n    const productId = product.id || product._id;\n    this.buttonActionsService.addToWishlist({\n      productId: productId,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Product added to wishlist successfully');\n        } else {\n          console.error('Failed to add to wishlist:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error adding to wishlist:', error);\n      }\n    });\n  }\n  addToCart(product) {\n    console.log('Add to cart:', product);\n    if (!product.id && !product._id) return;\n    const productId = product.id || product._id;\n    this.buttonActionsService.addToCart({\n      productId: productId,\n      quantity: 1,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: result => {\n        if (result.success) {\n          console.log('Product added to cart successfully');\n        } else {\n          console.error('Failed to add to cart:', result.message);\n        }\n      },\n      error: error => {\n        console.error('Error adding to cart:', error);\n      }\n    });\n  }\n  followUser(user) {\n    console.log('Follow user:', user.username);\n    // Implement follow functionality\n  }\n  viewSummerCollection() {\n    this.router.navigate(['/shop'], {\n      queryParams: {\n        collection: 'summer2024'\n      }\n    });\n  }\n  toggleComments(post) {\n    console.log('Toggle comments for post:', post.id);\n    // Implement comments toggle\n  }\n  // Utility methods\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  formatLikesCount(likes) {\n    return this.formatNumber(likes);\n  }\n  getTimeAgo(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;\n    return `${Math.floor(diffInSeconds / 604800)}w`;\n  }\n  trackByPostId(index, post) {\n    return post.id;\n  }\n  // Payment Modal handlers\n  onPaymentCompleted(paymentResult) {\n    this.showPaymentModal = false;\n    if (paymentResult.status === 'success') {\n      // Navigate to success page\n      this.router.navigate(['/payment-success'], {\n        queryParams: {\n          orderId: paymentResult.orderId || this.generateOrderId(),\n          method: paymentResult.method\n        }\n      });\n    } else {\n      // Show error message\n      alert('Payment failed. Please try again.');\n    }\n  }\n  onPaymentModalClose() {\n    this.showPaymentModal = false;\n  }\n  generateOrderId() {\n    return 'ORD' + Date.now().toString();\n  }\n  // Enhanced product tag methods\n  onProductTagClick(productTag) {\n    if (!productTag) return;\n    const navigationType = productTag.navigationType || 'product';\n    const targetData = productTag.product || productTag.category || productTag.vendor || productTag.brand;\n    switch (navigationType) {\n      case 'product':\n        this.navigateToProduct(targetData);\n        break;\n      case 'category':\n        this.navigateToCategory(targetData);\n        break;\n      case 'vendor':\n        this.navigateToVendor(targetData);\n        break;\n      case 'brand':\n        this.navigateToBrand(targetData);\n        break;\n      default:\n        this.navigateToProduct(targetData);\n    }\n  }\n  navigateToProduct(product) {\n    if (product?._id || product?.id) {\n      const productId = product._id || product.id;\n      this.router.navigate(['/product', productId]);\n    }\n  }\n  navigateToCategory(category) {\n    if (category?.slug || category?.name) {\n      const categorySlug = category.slug || category.name.toLowerCase().replace(/\\s+/g, '-');\n      this.router.navigate(['/shop'], {\n        queryParams: {\n          category: categorySlug\n        }\n      });\n    }\n  }\n  navigateToVendor(vendor) {\n    if (vendor?._id || vendor?.id || vendor?.username) {\n      const vendorId = vendor._id || vendor.id || vendor.username;\n      this.router.navigate(['/vendor', vendorId]);\n    }\n  }\n  navigateToBrand(brand) {\n    if (brand?.slug || brand?.name) {\n      const brandSlug = brand.slug || brand.name.toLowerCase().replace(/\\s+/g, '-');\n      this.router.navigate(['/shop'], {\n        queryParams: {\n          brand: brandSlug\n        }\n      });\n    }\n  }\n  getTagTooltip(productTag) {\n    const type = productTag.navigationType || 'product';\n    const name = this.getTagName(productTag);\n    switch (type) {\n      case 'product':\n        return `View product: ${name}`;\n      case 'category':\n        return `Browse category: ${name}`;\n      case 'vendor':\n        return `Visit vendor: ${name}`;\n      case 'brand':\n        return `Shop brand: ${name}`;\n      default:\n        return `View: ${name}`;\n    }\n  }\n  getTagIcon(navigationType) {\n    switch (navigationType) {\n      case 'product':\n        return 'fas fa-tag';\n      case 'category':\n        return 'fas fa-th-large';\n      case 'vendor':\n        return 'fas fa-store';\n      case 'brand':\n        return 'fas fa-crown';\n      default:\n        return 'fas fa-tag';\n    }\n  }\n  getTagName(productTag) {\n    const type = productTag.navigationType || 'product';\n    switch (type) {\n      case 'product':\n        return productTag.product?.name || 'Product';\n      case 'category':\n        return productTag.category?.name || 'Category';\n      case 'vendor':\n        return productTag.vendor?.fullName || productTag.vendor?.username || 'Vendor';\n      case 'brand':\n        return productTag.brand?.name || 'Brand';\n      default:\n        return productTag.product?.name || 'Item';\n    }\n  }\n  getTagSubtitle(productTag) {\n    const type = productTag.navigationType || 'product';\n    switch (type) {\n      case 'product':\n        return productTag.product?.price ? `$${productTag.product.price}` : '';\n      case 'category':\n        return productTag.category?.productCount ? `${productTag.category.productCount} products` : 'Browse category';\n      case 'vendor':\n        return productTag.vendor?.location || 'Visit store';\n      case 'brand':\n        return productTag.brand?.productCount ? `${productTag.brand.productCount} products` : 'Shop brand';\n      default:\n        return '';\n    }\n  }\n  getTagImageUrl(productTag) {\n    const type = productTag.navigationType || 'product';\n    switch (type) {\n      case 'product':\n        return productTag.product?.images?.[0]?.url || '/assets/images/product-placeholder.jpg';\n      case 'category':\n        return productTag.category?.image || '/assets/images/category-placeholder.jpg';\n      case 'vendor':\n        return productTag.vendor?.avatar || '/assets/images/vendor-placeholder.jpg';\n      case 'brand':\n        return productTag.brand?.logo || '/assets/images/brand-placeholder.jpg';\n      default:\n        return productTag.product?.images?.[0]?.url || '/assets/images/product-placeholder.jpg';\n    }\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.ButtonActionsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 4,\n      consts: [[1, \"instagram-home-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"instagram-layout\", 4, \"ngIf\"], [3, \"close\", \"paymentCompleted\", \"isVisible\", \"paymentData\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"instagram-layout\"], [1, \"main-feed\"], [1, \"stories-section\"], [1, \"posts-feed\"], [\"class\", \"instagram-post\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"instagram-sidebar\"], [\"class\", \"profile-card\", 4, \"ngIf\"], [1, \"sidebar-section\"], [1, \"section-header\"], [1, \"see-all-btn\", 3, \"click\"], [1, \"collection-items\"], [\"class\", \"collection-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"routerLink\", \"/shop?filter=featured\", 1, \"see-all-btn\"], [1, \"featured-items\"], [\"class\", \"featured-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"routerLink\", \"/shop?filter=trending\", 1, \"see-all-btn\"], [1, \"trending-items\"], [\"class\", \"trending-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"routerLink\", \"/shop?filter=new\", 1, \"see-all-btn\"], [1, \"arrivals-items\"], [\"class\", \"arrival-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"see-all-btn\"], [1, \"suggested-users\"], [\"class\", \"suggested-user\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-footer\"], [1, \"footer-links\"], [\"href\", \"#\"], [1, \"copyright\"], [1, \"instagram-post\"], [1, \"post-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"username\"], [\"class\", \"location\", 4, \"ngIf\"], [1, \"more-options\"], [1, \"fas\", \"fa-ellipsis-h\"], [1, \"post-media\"], [1, \"post-image\", 3, \"src\", \"alt\"], [\"class\", \"product-tags-overlay\", 4, \"ngIf\"], [1, \"post-actions\"], [1, \"primary-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [1, \"far\", \"fa-comment\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [1, \"far\", \"fa-paper-plane\"], [1, \"action-btn\", \"save-btn\", 3, \"click\"], [\"class\", \"likes-section\", 4, \"ngIf\"], [1, \"post-caption\"], [1, \"caption-text\"], [\"class\", \"ecommerce-actions\", 4, \"ngIf\"], [\"class\", \"comments-preview\", 4, \"ngIf\"], [1, \"post-time\"], [1, \"add-comment-section\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 1, \"comment-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"post-comment-btn\", 3, \"click\", \"disabled\"], [1, \"location\"], [1, \"product-tags-overlay\"], [\"class\", \"product-tag\", 3, \"class\", \"left\", \"top\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\", \"title\"], [1, \"tag-dot\"], [1, \"tag-icon\"], [\"class\", \"product-preview\", 4, \"ngIf\"], [1, \"product-preview\"], [3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"likes-section\"], [1, \"likes-count\"], [1, \"ecommerce-actions\"], [1, \"product-showcase\"], [\"class\", \"featured-product\", 4, \"ngFor\", \"ngForOf\"], [1, \"shopping-buttons\"], [1, \"shop-btn\", \"buy-btn\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [1, \"shop-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"far\", \"fa-heart\"], [1, \"shop-btn\", \"cart-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"featured-product\"], [1, \"product-thumbnail\", 3, \"src\", \"alt\"], [1, \"product-details\"], [1, \"comments-preview\"], [1, \"view-comments-btn\", 3, \"click\"], [1, \"recent-comments\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [1, \"comment\"], [1, \"comment-username\"], [1, \"comment-text\"], [1, \"profile-card\"], [1, \"profile-avatar\", 3, \"src\", \"alt\"], [1, \"profile-info\"], [1, \"profile-username\"], [1, \"profile-name\"], [1, \"switch-btn\"], [1, \"collection-item\", 3, \"click\"], [1, \"collection-image\", 3, \"src\", \"alt\"], [1, \"collection-info\"], [1, \"collection-name\"], [1, \"collection-price\"], [1, \"featured-item\", 3, \"click\"], [1, \"featured-image\", 3, \"src\", \"alt\"], [1, \"featured-info\"], [1, \"featured-name\"], [1, \"featured-brand\"], [1, \"featured-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"original-price\"], [1, \"trending-item\", 3, \"click\"], [1, \"trending-image\", 3, \"src\", \"alt\"], [1, \"trending-badge\"], [1, \"fas\", \"fa-fire\"], [1, \"trending-info\"], [1, \"trending-name\"], [1, \"trending-stats\"], [1, \"fas\", \"fa-eye\"], [1, \"fas\", \"fa-heart\"], [1, \"trending-price\"], [1, \"arrival-item\", 3, \"click\"], [1, \"arrival-image\", 3, \"src\", \"alt\"], [1, \"new-badge\"], [1, \"arrival-info\"], [1, \"arrival-name\"], [1, \"arrival-brand\"], [1, \"arrival-price\"], [1, \"suggested-user\"], [1, \"suggested-avatar\", 3, \"src\", \"alt\"], [1, \"suggested-info\"], [1, \"suggested-username\"], [1, \"suggested-followers\"], [1, \"follow-btn\", 3, \"click\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, HomeComponent_div_1_Template, 4, 0, \"div\", 1)(2, HomeComponent_div_2_Template, 67, 8, \"div\", 2);\n          i0.ɵɵelementStart(3, \"app-payment-modal\", 3);\n          i0.ɵɵlistener(\"close\", function HomeComponent_Template_app_payment_modal_close_3_listener() {\n            return ctx.onPaymentModalClose();\n          })(\"paymentCompleted\", function HomeComponent_Template_app_payment_modal_paymentCompleted_3_listener($event) {\n            return ctx.onPaymentCompleted($event);\n          });\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"isVisible\", ctx.showPaymentModal)(\"paymentData\", ctx.paymentModalData);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, FormsModule, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, RouterModule, i1.RouterLink, ViewAddStoriesComponent, PaymentModalComponent],\n      styles: [\".instagram-home-container[_ngcontent-%COMP%] {\\n  min-height: calc(100vh - 80px);\\n  background: #fafafa;\\n  padding-top: 20px;\\n}\\n\\n.instagram-layout[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 320px;\\n  gap: 30px;\\n  max-width: 975px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n@media (max-width: 1024px) {\\n  .instagram-layout[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    max-width: 614px;\\n    gap: 0;\\n    padding: 0;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .instagram-layout[_ngcontent-%COMP%] {\\n    padding: 0;\\n    gap: 0;\\n  }\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 200px;\\n  gap: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 3px solid #e3e3e3;\\n  border-top: 3px solid #667eea;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 14px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.main-feed[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n  width: 100%;\\n  max-width: 614px;\\n}\\n\\n.stories-section[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  margin-bottom: 24px;\\n}\\n.stories-section[_ngcontent-%COMP%]     .stories-container {\\n  margin: 0;\\n  padding: 16px;\\n  background: white;\\n  border-radius: 0;\\n  box-shadow: none;\\n  border: none;\\n  -webkit-backdrop-filter: none;\\n          backdrop-filter: none;\\n}\\n.stories-section[_ngcontent-%COMP%]     .stories-header {\\n  display: none;\\n}\\n.stories-section[_ngcontent-%COMP%]     .stories-slider-wrapper {\\n  gap: 8px;\\n}\\n.stories-section[_ngcontent-%COMP%]     .nav-arrow {\\n  display: none;\\n}\\n.stories-section[_ngcontent-%COMP%]     .story-item {\\n  margin: 0 4px;\\n}\\n.stories-section[_ngcontent-%COMP%]     .story-avatar {\\n  width: 56px;\\n  height: 56px;\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%]     .story-avatar {\\n    width: 56px;\\n    height: 56px;\\n  }\\n}\\n.stories-section[_ngcontent-%COMP%]     .story-avatar-inner {\\n  width: 50px;\\n  height: 50px;\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%]     .story-avatar-inner {\\n    width: 50px;\\n    height: 50px;\\n  }\\n}\\n.stories-section[_ngcontent-%COMP%]     .story-username {\\n  font-size: 12px;\\n  color: #262626;\\n  text-shadow: none;\\n  max-width: 64px;\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%]     .story-username {\\n    max-width: 64px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .stories-section[_ngcontent-%COMP%] {\\n    border-radius: 0;\\n    border-left: none;\\n    border-right: none;\\n    margin-bottom: 0;\\n  }\\n}\\n\\n.posts-feed[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.instagram-post[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  margin-bottom: 24px;\\n  max-width: 614px;\\n  width: 100%;\\n}\\n@media (max-width: 768px) {\\n  .instagram-post[_ngcontent-%COMP%] {\\n    border-radius: 0;\\n    border-left: none;\\n    border-right: none;\\n    margin-bottom: 0;\\n    border-bottom: none;\\n  }\\n}\\n\\n.post-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 16px;\\n}\\n.post-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.post-header[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.post-header[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 0;\\n  color: #262626;\\n}\\n.post-header[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   .location[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n}\\n.post-header[_ngcontent-%COMP%]   .more-options[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px;\\n  color: #262626;\\n}\\n.post-header[_ngcontent-%COMP%]   .more-options[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n\\n.post-media[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  aspect-ratio: 1;\\n  overflow: hidden;\\n}\\n.post-media[_ngcontent-%COMP%]   .post-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  display: block;\\n}\\n\\n.product-tags-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  pointer-events: all;\\n  cursor: pointer;\\n  z-index: 5;\\n}\\n.product-tag[_ngcontent-%COMP%]   .tag-dot[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background: white;\\n  border-radius: 50%;\\n  border: 2px solid #262626;\\n  position: relative;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.product-tag[_ngcontent-%COMP%]   .tag-dot[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 6px;\\n  height: 6px;\\n  background: #262626;\\n  border-radius: 50%;\\n}\\n.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 30px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background: rgba(0, 0, 0, 0.8);\\n  border-radius: 8px;\\n  padding: 8px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 200px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 4px;\\n  object-fit: cover;\\n}\\n.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n.product-tag[_ngcontent-%COMP%]   .product-preview[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #f5f5f5;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n.post-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px 16px 8px;\\n}\\n.post-actions[_ngcontent-%COMP%]   .primary-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n.post-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px;\\n  font-size: 24px;\\n  color: #262626;\\n  transition: all 0.3s ease;\\n}\\n.post-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n.post-actions[_ngcontent-%COMP%]   .action-btn.liked[_ngcontent-%COMP%] {\\n  color: #ed4956;\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease;\\n}\\n.post-actions[_ngcontent-%COMP%]   .action-btn.saved[_ngcontent-%COMP%] {\\n  color: #262626;\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.2);\\n  }\\n}\\n.likes-section[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n}\\n.likes-section[_ngcontent-%COMP%]   .likes-count[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #262626;\\n}\\n\\n.post-caption[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n.post-caption[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #262626;\\n  margin-right: 8px;\\n}\\n.post-caption[_ngcontent-%COMP%]   .caption-text[_ngcontent-%COMP%] {\\n  color: #262626;\\n}\\n\\n.ecommerce-actions[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border-top: 1px solid #efefef;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 12px;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  flex: 1;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 6px;\\n  object-fit: cover;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: #262626;\\n  line-height: 1.2;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 8px 16px;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 4px;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.buy-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  color: white;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.buy-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.wishlist-btn[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #dbdbdb;\\n  color: #262626;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n  border-color: #c6c6c6;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.cart-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n  color: white;\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(240, 147, 251, 0.4);\\n}\\n.ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n}\\n\\n.comments-preview[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n}\\n.comments-preview[_ngcontent-%COMP%]   .view-comments-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #8e8e8e;\\n  font-size: 14px;\\n  cursor: pointer;\\n  margin-bottom: 4px;\\n}\\n.comments-preview[_ngcontent-%COMP%]   .view-comments-btn[_ngcontent-%COMP%]:hover {\\n  color: #262626;\\n}\\n.comments-preview[_ngcontent-%COMP%]   .recent-comments[_ngcontent-%COMP%]   .comment[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.4;\\n  margin-bottom: 2px;\\n}\\n.comments-preview[_ngcontent-%COMP%]   .recent-comments[_ngcontent-%COMP%]   .comment[_ngcontent-%COMP%]   .comment-username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #262626;\\n  margin-right: 8px;\\n}\\n.comments-preview[_ngcontent-%COMP%]   .recent-comments[_ngcontent-%COMP%]   .comment[_ngcontent-%COMP%]   .comment-text[_ngcontent-%COMP%] {\\n  color: #262626;\\n}\\n\\n.post-time[_ngcontent-%COMP%] {\\n  padding: 0 16px 8px;\\n  font-size: 10px;\\n  color: #8e8e8e;\\n  text-transform: uppercase;\\n  letter-spacing: 0.2px;\\n}\\n\\n.add-comment-section[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  border-top: 1px solid #efefef;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.add-comment-section[_ngcontent-%COMP%]   .comment-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  outline: none;\\n  font-size: 14px;\\n  color: #262626;\\n}\\n.add-comment-section[_ngcontent-%COMP%]   .comment-input[_ngcontent-%COMP%]::placeholder {\\n  color: #8e8e8e;\\n}\\n.add-comment-section[_ngcontent-%COMP%]   .post-comment-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #0095f6;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n}\\n.add-comment-section[_ngcontent-%COMP%]   .post-comment-btn[_ngcontent-%COMP%]:disabled {\\n  color: #c7c7c7;\\n  cursor: not-allowed;\\n}\\n.add-comment-section[_ngcontent-%COMP%]   .post-comment-btn[_ngcontent-%COMP%]:not(:disabled):hover {\\n  color: #00376b;\\n}\\n\\n.instagram-sidebar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 84px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  padding-left: 0;\\n}\\n@media (max-width: 1024px) {\\n  .instagram-sidebar[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n.profile-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 16px 0 24px 0;\\n  margin-bottom: 8px;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-username[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #262626;\\n  margin: 0 0 2px 0;\\n}\\n.profile-card[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #8e8e8e;\\n}\\n.profile-card[_ngcontent-%COMP%]   .switch-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #0095f6;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n}\\n.profile-card[_ngcontent-%COMP%]   .switch-btn[_ngcontent-%COMP%]:hover {\\n  color: #00376b;\\n}\\n\\n.sidebar-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #8e8e8e;\\n  margin: 0;\\n  text-transform: none;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #262626;\\n  font-size: 12px;\\n  font-weight: 400;\\n  cursor: pointer;\\n}\\n.sidebar-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .see-all-btn[_ngcontent-%COMP%]:hover {\\n  color: #8e8e8e;\\n}\\n\\n.collection-items[_ngcontent-%COMP%], .featured-items[_ngcontent-%COMP%], .trending-items[_ngcontent-%COMP%], .arrivals-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.collection-item[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 4px 0;\\n  border-radius: 3px;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n}\\n.collection-item[_ngcontent-%COMP%]:hover, .featured-item[_ngcontent-%COMP%]:hover, .trending-item[_ngcontent-%COMP%]:hover, .arrival-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.05);\\n}\\n.collection-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border-radius: 3px;\\n  object-fit: cover;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-name[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #262626;\\n  line-height: 1.2;\\n  margin-bottom: 2px;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-brand[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-brand[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  color: #8e8e8e;\\n  margin-bottom: 2px;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .collection-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .arrival-price[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .featured-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  color: #8e8e8e;\\n  text-decoration: line-through;\\n  font-size: 10px;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-bottom: 2px;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 2px;\\n  font-size: 10px;\\n  color: #8e8e8e;\\n}\\n.collection-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .collection-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .featured-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .trending-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .collection-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .featured-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .trending-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%]   .arrival-info[_ngcontent-%COMP%]   .trending-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 8px;\\n}\\n\\n.trending-badge[_ngcontent-%COMP%], .new-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 4px;\\n  right: 4px;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  font-size: 8px;\\n  font-weight: 600;\\n  color: white;\\n}\\n\\n.trending-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b, #ffa500);\\n  display: flex;\\n  align-items: center;\\n  gap: 2px;\\n}\\n.trending-badge[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 6px;\\n}\\n\\n.new-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #43e97b, #38f9d7);\\n}\\n\\n.trending-item[_ngcontent-%COMP%], .arrival-item[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.suggested-users[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.suggested-user[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 8px;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .suggested-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .suggested-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .suggested-info[_ngcontent-%COMP%]   .suggested-username[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #262626;\\n  line-height: 1.2;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .suggested-info[_ngcontent-%COMP%]   .suggested-followers[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n  background: #0095f6;\\n  border: none;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: background-color 0.3s ease;\\n}\\n.suggested-user[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover {\\n  background: #00376b;\\n}\\n\\n.sidebar-footer[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n  padding-top: 16px;\\n  border-top: 1px solid #efefef;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #c7c7c7;\\n  text-decoration: none;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.sidebar-footer[_ngcontent-%COMP%]   .copyright[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #c7c7c7;\\n}\\n\\n@media (max-width: 768px) {\\n  .instagram-home-container[_ngcontent-%COMP%] {\\n    padding-top: 0;\\n  }\\n  .instagram-layout[_ngcontent-%COMP%] {\\n    padding: 0;\\n    max-width: 100%;\\n  }\\n  .main-feed[_ngcontent-%COMP%] {\\n    gap: 0;\\n  }\\n  .instagram-post[_ngcontent-%COMP%] {\\n    margin-bottom: 0;\\n    border-radius: 0;\\n    border-left: none;\\n    border-right: none;\\n    max-width: 100%;\\n  }\\n  .instagram-post[_ngcontent-%COMP%]:last-child {\\n    border-bottom: 1px solid #dbdbdb;\\n  }\\n  .stories-section[_ngcontent-%COMP%] {\\n    border-radius: 0;\\n    border-left: none;\\n    border-right: none;\\n    margin-bottom: 0;\\n    border-bottom: 1px solid #dbdbdb;\\n  }\\n  .post-header[_ngcontent-%COMP%] {\\n    padding: 14px 16px;\\n  }\\n  .post-actions[_ngcontent-%COMP%] {\\n    padding: 6px 16px 4px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 12px;\\n  }\\n  .add-comment-section[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .likes-section[_ngcontent-%COMP%], .post-caption[_ngcontent-%COMP%], .comments-preview[_ngcontent-%COMP%], .post-time[_ngcontent-%COMP%] {\\n    padding-left: 16px;\\n    padding-right: 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%]   .product-showcase[_ngcontent-%COMP%]   .featured-product[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 6px;\\n  }\\n  .ecommerce-actions[_ngcontent-%COMP%]   .shopping-buttons[_ngcontent-%COMP%]   .shop-btn[_ngcontent-%COMP%] {\\n    padding: 8px 16px;\\n    font-size: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "RouterModule", "ViewAddStoriesComponent", "PaymentModalComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "post_r3", "location", "ɵɵproperty", "ctx_r5", "getTagImageUrl", "productTag_r5", "ɵɵsanitizeUrl", "getTagName", "getTagSubtitle", "ɵɵlistener", "HomeComponent_div_2_article_5_div_12_button_1_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onProductTagClick", "ɵɵtemplate", "HomeComponent_div_2_article_5_div_12_button_1_div_3_Template", "ɵɵclassMap", "navigationType", "ɵɵstyleProp", "position", "x", "y", "getTagTooltip", "getTagIcon", "showPreview", "HomeComponent_div_2_article_5_div_12_button_1_Template", "products", "ɵɵtextInterpolate1", "formatLikesCount", "likes", "product_r8", "image", "name", "formatPrice", "price", "HomeComponent_div_2_article_5_div_29_div_2_Template", "HomeComponent_div_2_article_5_div_29_Template_button_click_4_listener", "_r7", "buyNow", "HomeComponent_div_2_article_5_div_29_Template_button_click_7_listener", "addToWishlist", "HomeComponent_div_2_article_5_div_29_Template_button_click_10_listener", "addToCart", "slice", "comment_r10", "username", "text", "HomeComponent_div_2_article_5_div_30_Template_button_click_1_listener", "_r9", "toggleComments", "HomeComponent_div_2_article_5_div_30_div_4_Template", "comments", "length", "HomeComponent_div_2_article_5_span_7_Template", "HomeComponent_div_2_article_5_div_12_Template", "HomeComponent_div_2_article_5_Template_button_click_15_listener", "_r2", "toggleLike", "HomeComponent_div_2_article_5_Template_button_click_17_listener", "focusCommentInput", "HomeComponent_div_2_article_5_Template_button_click_19_listener", "sharePost", "HomeComponent_div_2_article_5_Template_button_click_21_listener", "toggleSave", "HomeComponent_div_2_article_5_div_23_Template", "HomeComponent_div_2_article_5_div_29_Template", "HomeComponent_div_2_article_5_div_30_Template", "ɵɵtwoWayListener", "HomeComponent_div_2_article_5_Template_input_ngModelChange_34_listener", "$event", "ɵɵtwoWayBindingSet", "newComment", "HomeComponent_div_2_article_5_Template_input_keyup_enter_34_listener", "addComment", "HomeComponent_div_2_article_5_Template_button_click_35_listener", "user", "avatar", "mediaUrl", "caption", "ɵɵclassProp", "isLiked", "isSaved", "getTimeAgo", "createdAt", "ɵɵtwoWayProperty", "trim", "currentUser", "fullName", "HomeComponent_div_2_div_15_Template_div_click_0_listener", "item_r12", "_r11", "onProductClick", "images", "url", "product_r14", "originalPrice", "HomeComponent_div_2_div_23_Template_div_click_0_listener", "_r13", "HomeComponent_div_2_div_23_span_10_Template", "brand", "HomeComponent_div_2_div_31_Template_div_click_0_listener", "product_r16", "_r15", "formatNumber", "analytics", "views", "HomeComponent_div_2_div_39_Template_div_click_0_listener", "product_r18", "_r17", "HomeComponent_div_2_div_47_Template_button_click_7_listener", "user_r20", "_r19", "followUser", "followers", "HomeComponent_div_2_article_5_Template", "HomeComponent_div_2_div_7_Template", "HomeComponent_div_2_Template_button_click_12_listener", "_r1", "viewSummerCollection", "HomeComponent_div_2_div_15_Template", "HomeComponent_div_2_div_23_Template", "HomeComponent_div_2_div_31_Template", "HomeComponent_div_2_div_39_Template", "HomeComponent_div_2_div_47_Template", "instagramPosts", "trackByPostId", "summerCollection", "featuredProducts", "trendingProducts", "newArrivals", "suggestedUsers", "HomeComponent", "constructor", "router", "productService", "authService", "buttonActionsService", "trendingPosts", "categories", "isLoading", "isAuthenticated", "showPaymentModal", "paymentModalData", "ngOnInit", "loadHomeData", "checkAuthStatus", "loadInstagramData", "currentUser$", "subscribe", "loadInstagramPosts", "loadSummerCollection", "loadSuggestedUsers", "getPosts", "next", "response", "data", "console", "log", "error", "id", "Date", "now", "product", "_id", "category", "slug", "productCount", "vendor", "getProductsByCategory", "getSuggestedUsers", "_this", "_asyncToGenerator", "featured", "trending", "arrivals", "Promise", "all", "getFeaturedProducts", "to<PERSON>romise", "getTrendingProducts", "getNewArrivals", "getFallbackProducts", "loadCategories", "loadTrendingPosts", "loadFallbackData", "icon", "color", "title", "mediaType", "discount", "rating", "average", "count", "navigate", "onCategoryClick", "queryParams", "toLowerCase", "viewAllCategories", "post", "postId", "action", "observable", "unlikePost", "likePost", "result", "success", "message", "savePost", "commentText", "commentOnPost", "push", "showProductDetails", "productId", "quantity", "addedFrom", "showPaymentModalFallback", "returnUrl", "amount", "orderData", "items", "subtotal", "tax", "shipping", "total", "userDetails", "email", "phone", "collection", "Intl", "NumberFormat", "style", "currency", "format", "num", "toFixed", "toString", "date", "diffInSeconds", "Math", "floor", "getTime", "index", "onPaymentCompleted", "paymentResult", "status", "orderId", "generateOrderId", "method", "alert", "onPaymentModalClose", "productTag", "targetData", "navigateToProduct", "navigateToCategory", "navigateToVendor", "navigate<PERSON><PERSON>Brand", "categorySlug", "replace", "vendorId", "brandSlug", "type", "logo", "ɵɵdirectiveInject", "i1", "Router", "i2", "ProductService", "i3", "AuthService", "i4", "ButtonActionsService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "HomeComponent_div_1_Template", "HomeComponent_div_2_Template", "HomeComponent_Template_app_payment_modal_close_3_listener", "HomeComponent_Template_app_payment_modal_paymentCompleted_3_listener", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i6", "DefaultValueAccessor", "NgControlStatus", "NgModel", "RouterLink", "styles"], "sources": ["E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.ts", "E:\\Fahion\\DFashion\\DFashion\\frontend\\src\\app\\features\\home\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router, RouterModule } from '@angular/router';\nimport { ProductService } from '../../../../core/services/product.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { ButtonActionsService } from '../../../../core/services/button-actions.service';\nimport { ViewAddStoriesComponent } from '../../components/instagram-stories/view-add-stories.component';\nimport { PaymentModalComponent, PaymentModalData } from '../../../../shared/components/payment-modal/payment-modal.component';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, FormsModule, RouterModule, ViewAddStoriesComponent, PaymentModalComponent],\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit {\n  featuredProducts: any[] = [];\n  trendingProducts: any[] = [];\n  newArrivals: any[] = [];\n  trendingPosts: any[] = [];\n  categories: any[] = [];\n  isLoading = true;\n  isAuthenticated = false;\n\n  // Instagram-style data\n  instagramPosts: any[] = [];\n  summerCollection: any[] = [];\n  suggestedUsers: any[] = [];\n  currentUser: any = null;\n  newComment = '';\n\n  // Payment Modal\n  showPaymentModal = false;\n  paymentModalData: PaymentModalData | null = null;\n\n  constructor(\n    private router: Router,\n    private productService: ProductService,\n    private authService: AuthService,\n    private buttonActionsService: ButtonActionsService\n  ) {}\n\n  ngOnInit() {\n    this.loadHomeData();\n    this.checkAuthStatus();\n    this.loadInstagramData();\n  }\n\n  checkAuthStatus() {\n    this.authService.currentUser$.subscribe(user => {\n      this.isAuthenticated = !!user;\n      this.currentUser = user;\n    });\n  }\n\n  loadInstagramData() {\n    this.loadInstagramPosts();\n    this.loadSummerCollection();\n    this.loadSuggestedUsers();\n  }\n\n  loadInstagramPosts() {\n    // Load real Instagram-style posts from API\n    this.authService.getPosts().subscribe({\n      next: (response) => {\n        this.instagramPosts = response.data || [];\n        console.log('✅ Instagram posts loaded:', this.instagramPosts.length);\n      },\n      error: (error) => {\n        console.error('❌ Error loading Instagram posts:', error);\n        this.instagramPosts = [];\n      }\n    });\n      {\n        id: '2',\n        user: {\n          username: 'style_guru_alex',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=600',\n        caption: 'Elegant evening look with this silk dress 💫 Perfect for date nights! #EveningWear #SilkDress',\n        location: 'New York, NY',\n        likes: 892,\n        isLiked: true,\n        isSaved: false,\n        createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago\n        products: [\n          {\n            navigationType: 'product',\n            position: { x: 50, y: 60 },\n            product: {\n              _id: '2',\n              name: 'Silk Slip Dress',\n              price: 159.99,\n              images: [{ url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=200' }]\n            }\n          },\n          {\n            navigationType: 'category',\n            position: { x: 25, y: 35 },\n            category: {\n              _id: 'cat1',\n              name: 'Evening Wear',\n              slug: 'evening-wear',\n              productCount: 89,\n              image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=200'\n            }\n          }\n        ],\n        comments: [\n          { username: 'elegant_style', text: 'Absolutely gorgeous! 💕' }\n        ]\n      },\n      {\n        id: '3',\n        user: {\n          username: 'trendy_sarah',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600',\n        caption: 'Cozy vibes in this amazing knit sweater! Perfect for autumn days 🍂 #CozyStyle #AutumnFashion',\n        location: 'San Francisco, CA',\n        likes: 567,\n        isLiked: false,\n        isSaved: true,\n        createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago\n        products: [\n          {\n            navigationType: 'product',\n            position: { x: 40, y: 50 },\n            product: {\n              _id: '3',\n              name: 'Cozy Knit Sweater',\n              price: 79.99,\n              images: [{ url: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=200' }]\n            }\n          },\n          {\n            navigationType: 'vendor',\n            position: { x: 65, y: 30 },\n            vendor: {\n              _id: 'vendor1',\n              username: 'cozy_boutique',\n              fullName: 'Cozy Boutique',\n              avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',\n              location: 'San Francisco, CA'\n            }\n          }\n        ],\n        comments: []\n      }\n    ];\n  }\n\n  loadSummerCollection() {\n    // Load summer collection from API\n    this.productService.getProductsByCategory('summer').subscribe({\n      next: (response) => {\n        this.summerCollection = response.data || [];\n      },\n      error: (error) => {\n        console.error('Error loading summer collection:', error);\n        this.summerCollection = [];\n      }\n    });\n  }\n\n  loadSuggestedUsers() {\n    // Load suggested users from API\n    this.authService.getSuggestedUsers().subscribe({\n      next: (response) => {\n        this.suggestedUsers = response.data || [];\n      },\n      error: (error) => {\n        console.error('Error loading suggested users:', error);\n        this.suggestedUsers = [];\n      }\n    });\n  }\n\n  async loadHomeData() {\n    try {\n      this.isLoading = true;\n\n      // Load all data in parallel\n      const [featured, trending, arrivals] = await Promise.all([\n        this.productService.getFeaturedProducts().toPromise(),\n        this.productService.getTrendingProducts().toPromise(),\n        this.productService.getNewArrivals().toPromise()\n      ]);\n\n      this.featuredProducts = ((featured as any)?.products || (featured as any)?.data || featured || []).slice(0, 8);\n      this.trendingProducts = ((trending as any)?.products || (trending as any)?.data || trending || []).slice(0, 8);\n      this.newArrivals = ((arrivals as any)?.products || (arrivals as any)?.data || arrivals || []).slice(0, 8);\n\n      // If no data loaded, use fallback\n      if (this.featuredProducts.length === 0) {\n        this.featuredProducts = this.getFallbackProducts();\n      }\n      if (this.trendingProducts.length === 0) {\n        this.trendingProducts = this.getFallbackProducts();\n      }\n      if (this.newArrivals.length === 0) {\n        this.newArrivals = this.getFallbackProducts();\n      }\n\n      // Load categories\n      this.loadCategories();\n\n      // Load trending posts (mock data for now)\n      this.loadTrendingPosts();\n\n    } catch (error) {\n      console.error('Error loading home data:', error);\n      this.loadFallbackData();\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  loadCategories() {\n    this.categories = [\n      { name: 'Women', icon: 'fas fa-female', color: 'linear-gradient(45deg, #f093fb, #f5576c)' },\n      { name: 'Men', icon: 'fas fa-male', color: 'linear-gradient(45deg, #667eea, #764ba2)' },\n      { name: 'Kids', icon: 'fas fa-child', color: 'linear-gradient(45deg, #4facfe, #00f2fe)' },\n      { name: 'Accessories', icon: 'fas fa-gem', color: 'linear-gradient(45deg, #43e97b, #38f9d7)' },\n      { name: 'Shoes', icon: 'fas fa-shoe-prints', color: 'linear-gradient(45deg, #fa709a, #fee140)' },\n      { name: 'Bags', icon: 'fas fa-shopping-bag', color: 'linear-gradient(45deg, #a8edea, #fed6e3)' }\n    ];\n  }\n\n  loadTrendingPosts() {\n    this.trendingPosts = [\n      {\n        title: 'Summer Fashion Trends 2024',\n        mediaUrl: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',\n        mediaType: 'image',\n        analytics: { views: 15420, likes: 892 }\n      },\n      {\n        title: 'Street Style Inspiration',\n        mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400',\n        mediaType: 'image',\n        analytics: { views: 12890, likes: 1205 }\n      }\n    ];\n  }\n\n  getFallbackProducts() {\n    return [\n      {\n        _id: '1',\n        name: 'Vintage Denim Jacket',\n        brand: 'Urban Threads',\n        price: 89.99,\n        originalPrice: 129.99,\n        discount: 31,\n        images: [{ url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400' }],\n        rating: { average: 4.2, count: 156 },\n        analytics: { views: 15420, likes: 892 }\n      },\n      {\n        _id: '2',\n        name: 'Silk Slip Dress',\n        brand: 'Ethereal',\n        price: 159.99,\n        originalPrice: 199.99,\n        discount: 20,\n        images: [{ url: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400' }],\n        rating: { average: 4.5, count: 89 },\n        analytics: { views: 12890, likes: 1205 }\n      },\n      {\n        _id: '3',\n        name: 'Cozy Knit Sweater',\n        brand: 'Cozy Co',\n        price: 79.99,\n        originalPrice: 99.99,\n        discount: 20,\n        images: [{ url: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=400' }],\n        rating: { average: 4.3, count: 203 },\n        analytics: { views: 8750, likes: 567 }\n      },\n      {\n        _id: '4',\n        name: 'High-Waisted Jeans',\n        brand: 'Urban Threads',\n        price: 119.99,\n        images: [{ url: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?w=400' }],\n        rating: { average: 4.6, count: 342 },\n        analytics: { views: 22100, likes: 1456 }\n      },\n      {\n        _id: '5',\n        name: 'Floral Summer Dress',\n        brand: 'Ethereal',\n        price: 139.99,\n        images: [{ url: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400' }],\n        rating: { average: 4.4, count: 128 },\n        analytics: { views: 9870, likes: 743 }\n      },\n      {\n        _id: '6',\n        name: 'Leather Ankle Boots',\n        brand: 'Urban Threads',\n        price: 199.99,\n        originalPrice: 249.99,\n        discount: 20,\n        images: [{ url: 'https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400' }],\n        rating: { average: 4.7, count: 89 },\n        analytics: { views: 15600, likes: 1123 }\n      },\n      {\n        _id: '7',\n        name: 'Oversized Blazer',\n        brand: 'Ethereal',\n        price: 189.99,\n        images: [{ url: 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=400' }],\n        rating: { average: 4.1, count: 76 },\n        analytics: { views: 7890, likes: 456 }\n      },\n      {\n        _id: '8',\n        name: 'Casual T-Shirt',\n        brand: 'Cozy Co',\n        price: 29.99,\n        originalPrice: 39.99,\n        discount: 25,\n        images: [{ url: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400' }],\n        rating: { average: 4.0, count: 234 },\n        analytics: { views: 12340, likes: 678 }\n      }\n    ];\n  }\n\n  loadFallbackData() {\n    this.featuredProducts = this.getFallbackProducts();\n    this.trendingProducts = this.getFallbackProducts();\n    this.newArrivals = this.getFallbackProducts();\n  }\n\n  // Navigation methods\n  onProductClick(product: any) {\n    this.router.navigate(['/product', product._id || product.id]);\n  }\n\n  onCategoryClick(category: any) {\n    this.router.navigate(['/shop'], {\n      queryParams: { category: category.name.toLowerCase() }\n    });\n  }\n\n  viewAllCategories() {\n    this.router.navigate(['/shop']);\n  }\n\n  // Instagram-style interaction methods\n  toggleLike(post: any) {\n    if (!post.id && !post._id) return;\n\n    const postId = post.id || post._id;\n    const action = post.isLiked ? 'unlikePost' : 'likePost';\n    const observable = post.isLiked\n      ? this.buttonActionsService.unlikePost(postId)\n      : this.buttonActionsService.likePost(postId);\n\n    // Optimistic update\n    post.isLiked = !post.isLiked;\n    post.likes += post.isLiked ? 1 : -1;\n\n    observable.subscribe({\n      next: (result) => {\n        if (!result.success) {\n          // Revert on failure\n          post.isLiked = !post.isLiked;\n          post.likes += post.isLiked ? 1 : -1;\n          console.error('Failed to toggle like:', result.message);\n        }\n      },\n      error: (error) => {\n        // Revert on error\n        post.isLiked = !post.isLiked;\n        post.likes += post.isLiked ? 1 : -1;\n        console.error('Error toggling like:', error);\n      }\n    });\n  }\n\n  toggleSave(post: any) {\n    if (!post.id && !post._id) return;\n\n    const postId = post.id || post._id;\n\n    // Optimistic update\n    post.isSaved = !post.isSaved;\n\n    this.buttonActionsService.savePost(postId).subscribe({\n      next: (result) => {\n        if (!result.success) {\n          // Revert on failure\n          post.isSaved = !post.isSaved;\n          console.error('Failed to save post:', result.message);\n        }\n      },\n      error: (error) => {\n        // Revert on error\n        post.isSaved = !post.isSaved;\n        console.error('Error saving post:', error);\n      }\n    });\n  }\n\n  sharePost(post: any) {\n    if (!post.id && !post._id) return;\n\n    const postId = post.id || post._id;\n\n    this.buttonActionsService.sharePost(postId).subscribe({\n      next: (result) => {\n        if (result.success) {\n          console.log('Post shared successfully');\n        } else {\n          console.error('Failed to share post:', result.message);\n        }\n      },\n      error: (error) => {\n        console.error('Error sharing post:', error);\n      }\n    });\n  }\n\n  focusCommentInput(post: any) {\n    // Focus on comment input for this post\n    console.log('Focus comment for post:', post.id);\n  }\n\n  addComment(post: any) {\n    if (!this.newComment || !this.newComment.trim()) return;\n    if (!post.id && !post._id) return;\n\n    const postId = post.id || post._id;\n    const commentText = this.newComment.trim();\n\n    this.buttonActionsService.commentOnPost({\n      postId: postId,\n      text: commentText\n    }).subscribe({\n      next: (result) => {\n        if (result.success) {\n          // Add comment to local state\n          if (!post.comments) {\n            post.comments = [];\n          }\n          post.comments.push({\n            username: this.currentUser?.username || 'user',\n            text: commentText\n          });\n          this.newComment = '';\n          console.log('Comment added successfully');\n        } else {\n          console.error('Failed to add comment:', result.message);\n        }\n      },\n      error: (error) => {\n        console.error('Error adding comment:', error);\n      }\n    });\n  }\n\n  showProductDetails(product: any) {\n    console.log('Show product details:', product);\n    // Toggle product preview\n    product.showPreview = !product.showPreview;\n  }\n\n  // E-commerce methods\n  buyNow(product: any) {\n    console.log('Buy now:', product);\n\n    if (!product.id && !product._id) return;\n\n    const productId = product.id || product._id;\n\n    this.buttonActionsService.buyNow({\n      productId: productId,\n      quantity: 1,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: (result) => {\n        if (result.success) {\n          console.log('Buy now successful, redirecting to checkout');\n          // Navigation will be handled by the service\n        } else {\n          console.error('Failed to buy now:', result.message);\n          // Fallback to payment modal if needed\n          this.showPaymentModalFallback(product);\n        }\n      },\n      error: (error) => {\n        console.error('Error in buy now:', error);\n        // Fallback to payment modal\n        this.showPaymentModalFallback(product);\n      }\n    });\n  }\n\n  private showPaymentModalFallback(product: any) {\n    if (!this.currentUser) {\n      this.router.navigate(['/auth/login'], { queryParams: { returnUrl: '/home' } });\n      return;\n    }\n\n    // Prepare payment modal data for single product purchase\n    this.paymentModalData = {\n      amount: product.price,\n      orderData: {\n        items: [{\n          id: product.id,\n          name: product.name,\n          price: product.price,\n          quantity: 1,\n          image: product.image\n        }],\n        subtotal: product.price,\n        tax: product.price * 0.18,\n        shipping: 0,\n        discount: 0,\n        total: product.price + (product.price * 0.18)\n      },\n      userDetails: {\n        name: this.currentUser.fullName || this.currentUser.username,\n        email: this.currentUser.email,\n        phone: this.currentUser.phone || ''\n      }\n    };\n\n    this.showPaymentModal = true;\n  }\n\n  addToWishlist(product: any) {\n    console.log('Add to wishlist:', product);\n\n    if (!product.id && !product._id) return;\n\n    const productId = product.id || product._id;\n\n    this.buttonActionsService.addToWishlist({\n      productId: productId,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: (result) => {\n        if (result.success) {\n          console.log('Product added to wishlist successfully');\n        } else {\n          console.error('Failed to add to wishlist:', result.message);\n        }\n      },\n      error: (error) => {\n        console.error('Error adding to wishlist:', error);\n      }\n    });\n  }\n\n  addToCart(product: any) {\n    console.log('Add to cart:', product);\n\n    if (!product.id && !product._id) return;\n\n    const productId = product.id || product._id;\n\n    this.buttonActionsService.addToCart({\n      productId: productId,\n      quantity: 1,\n      addedFrom: 'home_feed'\n    }).subscribe({\n      next: (result) => {\n        if (result.success) {\n          console.log('Product added to cart successfully');\n        } else {\n          console.error('Failed to add to cart:', result.message);\n        }\n      },\n      error: (error) => {\n        console.error('Error adding to cart:', error);\n      }\n    });\n  }\n\n  followUser(user: any) {\n    console.log('Follow user:', user.username);\n    // Implement follow functionality\n  }\n\n  viewSummerCollection() {\n    this.router.navigate(['/shop'], { queryParams: { collection: 'summer2024' } });\n  }\n\n  toggleComments(post: any) {\n    console.log('Toggle comments for post:', post.id);\n    // Implement comments toggle\n  }\n\n  // Utility methods\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  formatLikesCount(likes: number): string {\n    return this.formatNumber(likes);\n  }\n\n  getTimeAgo(date: Date): string {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;\n    return `${Math.floor(diffInSeconds / 604800)}w`;\n  }\n\n  trackByPostId(index: number, post: any): string {\n    return post.id;\n  }\n\n  // Payment Modal handlers\n  onPaymentCompleted(paymentResult: any) {\n    this.showPaymentModal = false;\n\n    if (paymentResult.status === 'success') {\n      // Navigate to success page\n      this.router.navigate(['/payment-success'], {\n        queryParams: {\n          orderId: paymentResult.orderId || this.generateOrderId(),\n          method: paymentResult.method\n        }\n      });\n    } else {\n      // Show error message\n      alert('Payment failed. Please try again.');\n    }\n  }\n\n  onPaymentModalClose() {\n    this.showPaymentModal = false;\n  }\n\n  private generateOrderId(): string {\n    return 'ORD' + Date.now().toString();\n  }\n\n  // Enhanced product tag methods\n  onProductTagClick(productTag: any): void {\n    if (!productTag) return;\n\n    const navigationType = productTag.navigationType || 'product';\n    const targetData = productTag.product || productTag.category || productTag.vendor || productTag.brand;\n\n    switch (navigationType) {\n      case 'product':\n        this.navigateToProduct(targetData);\n        break;\n      case 'category':\n        this.navigateToCategory(targetData);\n        break;\n      case 'vendor':\n        this.navigateToVendor(targetData);\n        break;\n      case 'brand':\n        this.navigateToBrand(targetData);\n        break;\n      default:\n        this.navigateToProduct(targetData);\n    }\n  }\n\n  private navigateToProduct(product: any): void {\n    if (product?._id || product?.id) {\n      const productId = product._id || product.id;\n      this.router.navigate(['/product', productId]);\n    }\n  }\n\n  private navigateToCategory(category: any): void {\n    if (category?.slug || category?.name) {\n      const categorySlug = category.slug || category.name.toLowerCase().replace(/\\s+/g, '-');\n      this.router.navigate(['/shop'], {\n        queryParams: { category: categorySlug }\n      });\n    }\n  }\n\n  private navigateToVendor(vendor: any): void {\n    if (vendor?._id || vendor?.id || vendor?.username) {\n      const vendorId = vendor._id || vendor.id || vendor.username;\n      this.router.navigate(['/vendor', vendorId]);\n    }\n  }\n\n  private navigateToBrand(brand: any): void {\n    if (brand?.slug || brand?.name) {\n      const brandSlug = brand.slug || brand.name.toLowerCase().replace(/\\s+/g, '-');\n      this.router.navigate(['/shop'], {\n        queryParams: { brand: brandSlug }\n      });\n    }\n  }\n\n  getTagTooltip(productTag: any): string {\n    const type = productTag.navigationType || 'product';\n    const name = this.getTagName(productTag);\n\n    switch (type) {\n      case 'product':\n        return `View product: ${name}`;\n      case 'category':\n        return `Browse category: ${name}`;\n      case 'vendor':\n        return `Visit vendor: ${name}`;\n      case 'brand':\n        return `Shop brand: ${name}`;\n      default:\n        return `View: ${name}`;\n    }\n  }\n\n  getTagIcon(navigationType: string): string {\n    switch (navigationType) {\n      case 'product':\n        return 'fas fa-tag';\n      case 'category':\n        return 'fas fa-th-large';\n      case 'vendor':\n        return 'fas fa-store';\n      case 'brand':\n        return 'fas fa-crown';\n      default:\n        return 'fas fa-tag';\n    }\n  }\n\n  getTagName(productTag: any): string {\n    const type = productTag.navigationType || 'product';\n\n    switch (type) {\n      case 'product':\n        return productTag.product?.name || 'Product';\n      case 'category':\n        return productTag.category?.name || 'Category';\n      case 'vendor':\n        return productTag.vendor?.fullName || productTag.vendor?.username || 'Vendor';\n      case 'brand':\n        return productTag.brand?.name || 'Brand';\n      default:\n        return productTag.product?.name || 'Item';\n    }\n  }\n\n  getTagSubtitle(productTag: any): string {\n    const type = productTag.navigationType || 'product';\n\n    switch (type) {\n      case 'product':\n        return productTag.product?.price ? `$${productTag.product.price}` : '';\n      case 'category':\n        return productTag.category?.productCount ? `${productTag.category.productCount} products` : 'Browse category';\n      case 'vendor':\n        return productTag.vendor?.location || 'Visit store';\n      case 'brand':\n        return productTag.brand?.productCount ? `${productTag.brand.productCount} products` : 'Shop brand';\n      default:\n        return '';\n    }\n  }\n\n  getTagImageUrl(productTag: any): string {\n    const type = productTag.navigationType || 'product';\n\n    switch (type) {\n      case 'product':\n        return productTag.product?.images?.[0]?.url || '/assets/images/product-placeholder.jpg';\n      case 'category':\n        return productTag.category?.image || '/assets/images/category-placeholder.jpg';\n      case 'vendor':\n        return productTag.vendor?.avatar || '/assets/images/vendor-placeholder.jpg';\n      case 'brand':\n        return productTag.brand?.logo || '/assets/images/brand-placeholder.jpg';\n      default:\n        return productTag.product?.images?.[0]?.url || '/assets/images/product-placeholder.jpg';\n    }\n  }\n}\n", "<div class=\"instagram-home-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-spinner\"></div>\n    <p>Loading amazing fashion...</p>\n  </div>\n\n  <!-- Instagram-style Layout -->\n  <div *ngIf=\"!isLoading\" class=\"instagram-layout\">\n    <!-- Main Feed -->\n    <div class=\"main-feed\">\n      <!-- Instagram Stories Section -->\n      <section class=\"stories-section\">\n        <app-view-add-stories></app-view-add-stories>\n      </section> \n\n      <!-- Instagram-style Posts Feed -->\n      <section class=\"posts-feed\">\n        <article *ngFor=\"let post of instagramPosts; trackBy: trackByPostId\" class=\"instagram-post\">\n          <!-- Post Header -->\n          <header class=\"post-header\">\n            <div class=\"user-info\">\n              <img [src]=\"post.user.avatar\" [alt]=\"post.user.username\" class=\"user-avatar\">\n              <div class=\"user-details\">\n                <h3 class=\"username\">{{ post.user.username }}</h3>\n                <span class=\"location\" *ngIf=\"post.location\">{{ post.location }}</span>\n              </div>\n            </div>\n            <button class=\"more-options\">\n              <i class=\"fas fa-ellipsis-h\"></i>\n            </button>\n          </header>\n\n          <!-- Post Media -->\n          <div class=\"post-media\">\n            <img [src]=\"post.mediaUrl\" [alt]=\"post.caption\" class=\"post-image\">\n\n            <!-- Enhanced Product Tags Overlay -->\n            <div class=\"product-tags-overlay\" *ngIf=\"post.products && post.products.length > 0\">\n              <button *ngFor=\"let productTag of post.products\"\n                      class=\"product-tag\"\n                      [class]=\"'tag-type-' + (productTag.navigationType || 'product')\"\n                      [style.left.%]=\"productTag.position?.x || 50\"\n                      [style.top.%]=\"productTag.position?.y || 50\"\n                      (click)=\"onProductTagClick(productTag)\"\n                      [title]=\"getTagTooltip(productTag)\">\n                <div class=\"tag-dot\" [class]=\"'dot-' + (productTag.navigationType || 'product')\">\n                  <i class=\"tag-icon\" [class]=\"getTagIcon(productTag.navigationType || 'product')\"></i>\n                </div>\n                <div class=\"product-preview\" *ngIf=\"productTag.showPreview\">\n                  <img [src]=\"getTagImageUrl(productTag)\" [alt]=\"getTagName(productTag)\">\n                  <div class=\"product-info\">\n                    <span class=\"product-name\">{{ getTagName(productTag) }}</span>\n                    <span class=\"product-price\">{{ getTagSubtitle(productTag) }}</span>\n                  </div>\n                </div>\n              </button>\n            </div>\n          </div>\n\n          <!-- Post Actions -->\n          <div class=\"post-actions\">\n            <div class=\"primary-actions\">\n              <button class=\"action-btn like-btn\" [class.liked]=\"post.isLiked\" (click)=\"toggleLike(post)\">\n                <i [class]=\"post.isLiked ? 'fas fa-heart' : 'far fa-heart'\"></i>\n              </button>\n              <button class=\"action-btn comment-btn\" (click)=\"focusCommentInput(post)\">\n                <i class=\"far fa-comment\"></i>\n              </button>\n              <button class=\"action-btn share-btn\" (click)=\"sharePost(post)\">\n                <i class=\"far fa-paper-plane\"></i>\n              </button>\n            </div>\n            <button class=\"action-btn save-btn\" [class.saved]=\"post.isSaved\" (click)=\"toggleSave(post)\">\n              <i [class]=\"post.isSaved ? 'fas fa-bookmark' : 'far fa-bookmark'\"></i>\n            </button>\n          </div>\n\n          <!-- Likes Count -->\n          <div class=\"likes-section\" *ngIf=\"post.likes > 0\">\n            <span class=\"likes-count\">{{ formatLikesCount(post.likes) }} likes</span>\n          </div>\n\n          <!-- Post Caption -->\n          <div class=\"post-caption\">\n            <span class=\"username\">{{ post.user.username }}</span>\n            <span class=\"caption-text\">{{ post.caption }}</span>\n          </div>\n\n          <!-- E-commerce Actions -->\n          <div class=\"ecommerce-actions\" *ngIf=\"post.products && post.products.length > 0\">\n            <div class=\"product-showcase\">\n              <div *ngFor=\"let product of post.products.slice(0, 2)\" class=\"featured-product\">\n                <img [src]=\"product.image\" [alt]=\"product.name\" class=\"product-thumbnail\">\n                <div class=\"product-details\">\n                  <span class=\"product-name\">{{ product.name }}</span>\n                  <span class=\"product-price\">{{ formatPrice(product.price) }}</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"shopping-buttons\">\n              <button class=\"shop-btn buy-btn\" (click)=\"buyNow(post.products[0])\">\n                <i class=\"fas fa-bolt\"></i>\n                Buy Now\n              </button>\n              <button class=\"shop-btn wishlist-btn\" (click)=\"addToWishlist(post.products[0])\">\n                <i class=\"far fa-heart\"></i>\n                Wishlist\n              </button>\n              <button class=\"shop-btn cart-btn\" (click)=\"addToCart(post.products[0])\">\n                <i class=\"fas fa-shopping-cart\"></i>\n                Add to Cart\n              </button>\n            </div>\n          </div>\n\n          <!-- Comments Preview -->\n          <div class=\"comments-preview\" *ngIf=\"post.comments && post.comments.length > 0\">\n            <button class=\"view-comments-btn\" (click)=\"toggleComments(post)\">\n              View all {{ post.comments.length }} comments\n            </button>\n            <div class=\"recent-comments\">\n              <div *ngFor=\"let comment of post.comments.slice(0, 2)\" class=\"comment\">\n                <span class=\"comment-username\">{{ comment.username }}</span>\n                <span class=\"comment-text\">{{ comment.text }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- Post Time -->\n          <div class=\"post-time\">\n            {{ getTimeAgo(post.createdAt) }}\n          </div>\n\n          <!-- Add Comment -->\n          <div class=\"add-comment-section\">\n            <input type=\"text\" placeholder=\"Add a comment...\" class=\"comment-input\"\n                   [(ngModel)]=\"newComment\" (keyup.enter)=\"addComment(post)\">\n            <button class=\"post-comment-btn\" (click)=\"addComment(post)\"\n                    [disabled]=\"!newComment || !newComment.trim()\">Post</button>\n          </div>\n        </article>\n      </section>\n    </div>\n\n    <!-- Instagram-style Sidebar -->\n    <aside class=\"instagram-sidebar\">\n      <!-- User Profile Card -->\n      <div class=\"profile-card\" *ngIf=\"currentUser\">\n        <img [src]=\"currentUser.avatar\" [alt]=\"currentUser.username\" class=\"profile-avatar\">\n        <div class=\"profile-info\">\n          <h4 class=\"profile-username\">{{ currentUser.username }}</h4>\n          <span class=\"profile-name\">{{ currentUser.fullName }}</span>\n        </div>\n        <button class=\"switch-btn\">Switch</button>\n      </div>\n\n      <!-- Summer Collection 2024 -->\n      <section class=\"sidebar-section\">\n        <div class=\"section-header\">\n          <h3>Summer Collection 2024</h3>\n          <button class=\"see-all-btn\" (click)=\"viewSummerCollection()\">See All</button>\n        </div>\n        <div class=\"collection-items\">\n          <div *ngFor=\"let item of summerCollection.slice(0, 3)\" class=\"collection-item\" (click)=\"onProductClick(item)\">\n            <img [src]=\"item.images[0]?.url\" [alt]=\"item.name\" class=\"collection-image\">\n            <div class=\"collection-info\">\n              <span class=\"collection-name\">{{ item.name }}</span>\n              <span class=\"collection-price\">{{ formatPrice(item.price) }}</span>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Featured Products -->\n      <section class=\"sidebar-section\">\n        <div class=\"section-header\">\n          <h3>Featured Products</h3>\n          <button class=\"see-all-btn\" routerLink=\"/shop?filter=featured\">See All</button>\n        </div>\n        <div class=\"featured-items\">\n          <div *ngFor=\"let product of featuredProducts.slice(0, 4)\" class=\"featured-item\" (click)=\"onProductClick(product)\">\n            <img [src]=\"product.images[0]?.url\" [alt]=\"product.name\" class=\"featured-image\">\n            <div class=\"featured-info\">\n              <span class=\"featured-name\">{{ product.name }}</span>\n              <span class=\"featured-brand\">{{ product.brand }}</span>\n              <div class=\"featured-price\">\n                <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n                <span class=\"original-price\" *ngIf=\"product.originalPrice\">{{ formatPrice(product.originalPrice) }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Trending Now -->\n      <section class=\"sidebar-section\">\n        <div class=\"section-header\">\n          <h3>Trending Now</h3>\n          <button class=\"see-all-btn\" routerLink=\"/shop?filter=trending\">See All</button>\n        </div>\n        <div class=\"trending-items\">\n          <div *ngFor=\"let product of trendingProducts.slice(0, 4)\" class=\"trending-item\" (click)=\"onProductClick(product)\">\n            <img [src]=\"product.images[0]?.url\" [alt]=\"product.name\" class=\"trending-image\">\n            <div class=\"trending-badge\">\n              <i class=\"fas fa-fire\"></i>\n              <span>Hot</span>\n            </div>\n            <div class=\"trending-info\">\n              <span class=\"trending-name\">{{ product.name }}</span>\n              <div class=\"trending-stats\">\n                <span><i class=\"fas fa-eye\"></i> {{ formatNumber(product.analytics?.views || 0) }}</span>\n                <span><i class=\"fas fa-heart\"></i> {{ formatNumber(product.analytics?.likes || 0) }}</span>\n              </div>\n              <span class=\"trending-price\">{{ formatPrice(product.price) }}</span>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- New Arrivals -->\n      <section class=\"sidebar-section\">\n        <div class=\"section-header\">\n          <h3>New Arrivals</h3>\n          <button class=\"see-all-btn\" routerLink=\"/shop?filter=new\">See All</button>\n        </div>\n        <div class=\"arrivals-items\">\n          <div *ngFor=\"let product of newArrivals.slice(0, 4)\" class=\"arrival-item\" (click)=\"onProductClick(product)\">\n            <img [src]=\"product.images[0]?.url\" [alt]=\"product.name\" class=\"arrival-image\">\n            <div class=\"new-badge\">New</div>\n            <div class=\"arrival-info\">\n              <span class=\"arrival-name\">{{ product.name }}</span>\n              <span class=\"arrival-brand\">{{ product.brand }}</span>\n              <span class=\"arrival-price\">{{ formatPrice(product.price) }}</span>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Suggested for You -->\n      <section class=\"sidebar-section\">\n        <div class=\"section-header\">\n          <h3>Suggested for You</h3>\n          <button class=\"see-all-btn\">See All</button>\n        </div>\n        <div class=\"suggested-users\">\n          <div *ngFor=\"let user of suggestedUsers.slice(0, 3)\" class=\"suggested-user\">\n            <img [src]=\"user.avatar\" [alt]=\"user.username\" class=\"suggested-avatar\">\n            <div class=\"suggested-info\">\n              <span class=\"suggested-username\">{{ user.username }}</span>\n              <span class=\"suggested-followers\">{{ formatNumber(user.followers) }} followers</span>\n            </div>\n            <button class=\"follow-btn\" (click)=\"followUser(user)\">Follow</button>\n          </div>\n        </div>\n      </section>\n\n      <!-- Footer Links -->\n      <div class=\"sidebar-footer\">\n        <div class=\"footer-links\">\n          <a href=\"#\">About</a>\n          <a href=\"#\">Help</a>\n          <a href=\"#\">Press</a>\n          <a href=\"#\">API</a>\n          <a href=\"#\">Jobs</a>\n          <a href=\"#\">Privacy</a>\n          <a href=\"#\">Terms</a>\n        </div>\n        <div class=\"copyright\">\n          <span>© 2024 DFashion</span>\n        </div>\n      </div>\n    </aside>\n  </div>\n\n  <!-- Payment Modal -->\n  <app-payment-modal\n    [isVisible]=\"showPaymentModal\"\n    [paymentData]=\"paymentModalData\"\n    (close)=\"onPaymentModalClose()\"\n    (paymentCompleted)=\"onPaymentCompleted($event)\">\n  </app-payment-modal>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAAiBC,YAAY,QAAQ,iBAAiB;AAItD,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAASC,qBAAqB,QAA0B,qEAAqE;;;;;;;;;;ICN3HC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,aAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,iCAA0B;IAC/BH,EAD+B,CAAAI,YAAA,EAAI,EAC7B;;;;;IAoBQJ,EAAA,CAAAC,cAAA,eAA6C;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAA1BJ,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAC,QAAA,CAAmB;;;;;IAwBhER,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAE,SAAA,cAAuE;IAErEF,EADF,CAAAC,cAAA,cAA0B,eACG;IAAAD,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC9DJ,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAEhEH,EAFgE,CAAAI,YAAA,EAAO,EAC/D,EACF;;;;;IALCJ,EAAA,CAAAK,SAAA,EAAkC;IAACL,EAAnC,CAAAS,UAAA,QAAAC,MAAA,CAAAC,cAAA,CAAAC,aAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAkC,QAAAH,MAAA,CAAAI,UAAA,CAAAF,aAAA,EAA+B;IAEzCZ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAM,iBAAA,CAAAI,MAAA,CAAAI,UAAA,CAAAF,aAAA,EAA4B;IAC3BZ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAI,MAAA,CAAAK,cAAA,CAAAH,aAAA,EAAgC;;;;;;IAdlEZ,EAAA,CAAAC,cAAA,iBAM4C;IADpCD,EAAA,CAAAgB,UAAA,mBAAAC,+EAAA;MAAA,MAAAL,aAAA,GAAAZ,EAAA,CAAAkB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAAa,iBAAA,CAAAX,aAAA,CAA6B;IAAA,EAAC;IAE7CZ,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAE,SAAA,YAAqF;IACvFF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAwB,UAAA,IAAAC,4DAAA,kBAA4D;IAO9DzB,EAAA,CAAAI,YAAA,EAAS;;;;;IAfDJ,EAAA,CAAA0B,UAAA,gBAAAd,aAAA,CAAAe,cAAA,eAAgE;IAEhE3B,EADA,CAAA4B,WAAA,UAAAhB,aAAA,CAAAiB,QAAA,kBAAAjB,aAAA,CAAAiB,QAAA,CAAAC,CAAA,aAA6C,SAAAlB,aAAA,CAAAiB,QAAA,kBAAAjB,aAAA,CAAAiB,QAAA,CAAAE,CAAA,aACD;IAE5C/B,EAAA,CAAAS,UAAA,UAAAC,MAAA,CAAAsB,aAAA,CAAApB,aAAA,EAAmC;IACpBZ,EAAA,CAAAK,SAAA,EAA2D;IAA3DL,EAAA,CAAA0B,UAAA,WAAAd,aAAA,CAAAe,cAAA,eAA2D;IAC1D3B,EAAA,CAAAK,SAAA,EAA4D;IAA5DL,EAAA,CAAA0B,UAAA,CAAAhB,MAAA,CAAAuB,UAAA,CAAArB,aAAA,CAAAe,cAAA,eAA4D;IAEpD3B,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAS,UAAA,SAAAG,aAAA,CAAAsB,WAAA,CAA4B;;;;;IAX9DlC,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAwB,UAAA,IAAAW,sDAAA,sBAM4C;IAY9CnC,EAAA,CAAAI,YAAA,EAAM;;;;IAlB2BJ,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAS,UAAA,YAAAF,OAAA,CAAA6B,QAAA,CAAgB;;;;;IAyCjDpC,EADF,CAAAC,cAAA,cAAkD,eACtB;IAAAD,EAAA,CAAAG,MAAA,GAAwC;IACpEH,EADoE,CAAAI,YAAA,EAAO,EACrE;;;;;IADsBJ,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAqC,kBAAA,KAAA3B,MAAA,CAAA4B,gBAAA,CAAA/B,OAAA,CAAAgC,KAAA,YAAwC;;;;;IAYhEvC,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,SAAA,cAA0E;IAExEF,EADF,CAAAC,cAAA,cAA6B,eACA;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAEhEH,EAFgE,CAAAI,YAAA,EAAO,EAC/D,EACF;;;;;IALCJ,EAAA,CAAAK,SAAA,EAAqB;IAACL,EAAtB,CAAAS,UAAA,QAAA+B,UAAA,CAAAC,KAAA,EAAAzC,EAAA,CAAAa,aAAA,CAAqB,QAAA2B,UAAA,CAAAE,IAAA,CAAqB;IAElB1C,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAkC,UAAA,CAAAE,IAAA,CAAkB;IACjB1C,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAI,MAAA,CAAAiC,WAAA,CAAAH,UAAA,CAAAI,KAAA,EAAgC;;;;;;IALlE5C,EADF,CAAAC,cAAA,cAAiF,cACjD;IAC5BD,EAAA,CAAAwB,UAAA,IAAAqB,mDAAA,kBAAgF;IAOlF7C,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,cAA8B,iBACwC;IAAnCD,EAAA,CAAAgB,UAAA,mBAAA8B,sEAAA;MAAA9C,EAAA,CAAAkB,aAAA,CAAA6B,GAAA;MAAA,MAAAxC,OAAA,GAAAP,EAAA,CAAAqB,aAAA,GAAAD,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAAsC,MAAA,CAAAzC,OAAA,CAAA6B,QAAA,CAAqB,CAAC,EAAE;IAAA,EAAC;IACjEpC,EAAA,CAAAE,SAAA,YAA2B;IAC3BF,EAAA,CAAAG,MAAA,gBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,iBAAgF;IAA1CD,EAAA,CAAAgB,UAAA,mBAAAiC,sEAAA;MAAAjD,EAAA,CAAAkB,aAAA,CAAA6B,GAAA;MAAA,MAAAxC,OAAA,GAAAP,EAAA,CAAAqB,aAAA,GAAAD,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAAwC,aAAA,CAAA3C,OAAA,CAAA6B,QAAA,CAA4B,CAAC,EAAE;IAAA,EAAC;IAC7EpC,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAwE;IAAtCD,EAAA,CAAAgB,UAAA,mBAAAmC,uEAAA;MAAAnD,EAAA,CAAAkB,aAAA,CAAA6B,GAAA;MAAA,MAAAxC,OAAA,GAAAP,EAAA,CAAAqB,aAAA,GAAAD,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAA0C,SAAA,CAAA7C,OAAA,CAAA6B,QAAA,CAAwB,CAAC,EAAE;IAAA,EAAC;IACrEpC,EAAA,CAAAE,SAAA,aAAoC;IACpCF,EAAA,CAAAG,MAAA,qBACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;IAtBuBJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAS,UAAA,YAAAF,OAAA,CAAA6B,QAAA,CAAAiB,KAAA,OAA4B;;;;;IA+BnDrD,EADF,CAAAC,cAAA,cAAuE,eACtC;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC5DJ,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAC/CH,EAD+C,CAAAI,YAAA,EAAO,EAChD;;;;IAF2BJ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,iBAAA,CAAAgD,WAAA,CAAAC,QAAA,CAAsB;IAC1BvD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAgD,WAAA,CAAAE,IAAA,CAAkB;;;;;;IANjDxD,EADF,CAAAC,cAAA,cAAgF,iBACb;IAA/BD,EAAA,CAAAgB,UAAA,mBAAAyC,sEAAA;MAAAzD,EAAA,CAAAkB,aAAA,CAAAwC,GAAA;MAAA,MAAAnD,OAAA,GAAAP,EAAA,CAAAqB,aAAA,GAAAD,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAAiD,cAAA,CAAApD,OAAA,CAAoB;IAAA,EAAC;IAC9DP,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAwB,UAAA,IAAAoC,mDAAA,kBAAuE;IAK3E5D,EADE,CAAAI,YAAA,EAAM,EACF;;;;IARFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAqC,kBAAA,eAAA9B,OAAA,CAAAsD,QAAA,CAAAC,MAAA,eACF;IAE2B9D,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAS,UAAA,YAAAF,OAAA,CAAAsD,QAAA,CAAAR,KAAA,OAA4B;;;;;;IArGvDrD,EAHJ,CAAAC,cAAA,kBAA4F,iBAE9D,cACH;IACrBD,EAAA,CAAAE,SAAA,cAA6E;IAE3EF,EADF,CAAAC,cAAA,cAA0B,aACH;IAAAD,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClDJ,EAAA,CAAAwB,UAAA,IAAAuC,6CAAA,mBAA6C;IAEjD/D,EADE,CAAAI,YAAA,EAAM,EACF;IACNJ,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,SAAA,YAAiC;IAErCF,EADE,CAAAI,YAAA,EAAS,EACF;IAGTJ,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAE,SAAA,eAAmE;IAGnEF,EAAA,CAAAwB,UAAA,KAAAwC,6CAAA,kBAAoF;IAoBtFhE,EAAA,CAAAI,YAAA,EAAM;IAKFJ,EAFJ,CAAAC,cAAA,eAA0B,eACK,kBACiE;IAA3BD,EAAA,CAAAgB,UAAA,mBAAAiD,gEAAA;MAAA,MAAA1D,OAAA,GAAAP,EAAA,CAAAkB,aAAA,CAAAgD,GAAA,EAAA9C,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAAyD,UAAA,CAAA5D,OAAA,CAAgB;IAAA,EAAC;IACzFP,EAAA,CAAAE,SAAA,SAAgE;IAClEF,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAyE;IAAlCD,EAAA,CAAAgB,UAAA,mBAAAoD,gEAAA;MAAA,MAAA7D,OAAA,GAAAP,EAAA,CAAAkB,aAAA,CAAAgD,GAAA,EAAA9C,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAA2D,iBAAA,CAAA9D,OAAA,CAAuB;IAAA,EAAC;IACtEP,EAAA,CAAAE,SAAA,aAA8B;IAChCF,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA+D;IAA1BD,EAAA,CAAAgB,UAAA,mBAAAsD,gEAAA;MAAA,MAAA/D,OAAA,GAAAP,EAAA,CAAAkB,aAAA,CAAAgD,GAAA,EAAA9C,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAA6D,SAAA,CAAAhE,OAAA,CAAe;IAAA,EAAC;IAC5DP,EAAA,CAAAE,SAAA,aAAkC;IAEtCF,EADE,CAAAI,YAAA,EAAS,EACL;IACNJ,EAAA,CAAAC,cAAA,kBAA4F;IAA3BD,EAAA,CAAAgB,UAAA,mBAAAwD,gEAAA;MAAA,MAAAjE,OAAA,GAAAP,EAAA,CAAAkB,aAAA,CAAAgD,GAAA,EAAA9C,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAA+D,UAAA,CAAAlE,OAAA,CAAgB;IAAA,EAAC;IACzFP,EAAA,CAAAE,SAAA,SAAsE;IAE1EF,EADE,CAAAI,YAAA,EAAS,EACL;IAGNJ,EAAA,CAAAwB,UAAA,KAAAkD,6CAAA,kBAAkD;IAMhD1E,EADF,CAAAC,cAAA,eAA0B,gBACD;IAAAD,EAAA,CAAAG,MAAA,IAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACtDJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAG,MAAA,IAAkB;IAC/CH,EAD+C,CAAAI,YAAA,EAAO,EAChD;IA8BNJ,EA3BA,CAAAwB,UAAA,KAAAmD,6CAAA,mBAAiF,KAAAC,6CAAA,kBA2BD;IAahF5E,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAIJJ,EADF,CAAAC,cAAA,eAAiC,iBAEkC;IAA1DD,EAAA,CAAA6E,gBAAA,2BAAAC,uEAAAC,MAAA;MAAA/E,EAAA,CAAAkB,aAAA,CAAAgD,GAAA;MAAA,MAAAxD,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAgF,kBAAA,CAAAtE,MAAA,CAAAuE,UAAA,EAAAF,MAAA,MAAArE,MAAA,CAAAuE,UAAA,GAAAF,MAAA;MAAA,OAAA/E,EAAA,CAAAsB,WAAA,CAAAyD,MAAA;IAAA,EAAwB;IAAC/E,EAAA,CAAAgB,UAAA,yBAAAkE,qEAAA;MAAA,MAAA3E,OAAA,GAAAP,EAAA,CAAAkB,aAAA,CAAAgD,GAAA,EAAA9C,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAeZ,MAAA,CAAAyE,UAAA,CAAA5E,OAAA,CAAgB;IAAA,EAAC;IADhEP,EAAA,CAAAI,YAAA,EACiE;IACjEJ,EAAA,CAAAC,cAAA,kBACuD;IADtBD,EAAA,CAAAgB,UAAA,mBAAAoE,gEAAA;MAAA,MAAA7E,OAAA,GAAAP,EAAA,CAAAkB,aAAA,CAAAgD,GAAA,EAAA9C,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAAyE,UAAA,CAAA5E,OAAA,CAAgB;IAAA,EAAC;IACJP,EAAA,CAAAG,MAAA,YAAI;IAE/DH,EAF+D,CAAAI,YAAA,EAAS,EAChE,EACE;;;;;IAvHCJ,EAAA,CAAAK,SAAA,GAAwB;IAACL,EAAzB,CAAAS,UAAA,QAAAF,OAAA,CAAA8E,IAAA,CAAAC,MAAA,EAAAtF,EAAA,CAAAa,aAAA,CAAwB,QAAAN,OAAA,CAAA8E,IAAA,CAAA9B,QAAA,CAA2B;IAEjCvD,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAA8E,IAAA,CAAA9B,QAAA,CAAwB;IACrBvD,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAAS,UAAA,SAAAF,OAAA,CAAAC,QAAA,CAAmB;IAU1CR,EAAA,CAAAK,SAAA,GAAqB;IAACL,EAAtB,CAAAS,UAAA,QAAAF,OAAA,CAAAgF,QAAA,EAAAvF,EAAA,CAAAa,aAAA,CAAqB,QAAAN,OAAA,CAAAiF,OAAA,CAAqB;IAGZxF,EAAA,CAAAK,SAAA,EAA+C;IAA/CL,EAAA,CAAAS,UAAA,SAAAF,OAAA,CAAA6B,QAAA,IAAA7B,OAAA,CAAA6B,QAAA,CAAA0B,MAAA,KAA+C;IAyB5C9D,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAyF,WAAA,UAAAlF,OAAA,CAAAmF,OAAA,CAA4B;IAC3D1F,EAAA,CAAAK,SAAA,EAAwD;IAAxDL,EAAA,CAAA0B,UAAA,CAAAnB,OAAA,CAAAmF,OAAA,mCAAwD;IAS3B1F,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAyF,WAAA,UAAAlF,OAAA,CAAAoF,OAAA,CAA4B;IAC3D3F,EAAA,CAAAK,SAAA,EAA8D;IAA9DL,EAAA,CAAA0B,UAAA,CAAAnB,OAAA,CAAAoF,OAAA,yCAA8D;IAKzC3F,EAAA,CAAAK,SAAA,EAAoB;IAApBL,EAAA,CAAAS,UAAA,SAAAF,OAAA,CAAAgC,KAAA,KAAoB;IAMvBvC,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAA8E,IAAA,CAAA9B,QAAA,CAAwB;IACpBvD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAC,OAAA,CAAAiF,OAAA,CAAkB;IAIfxF,EAAA,CAAAK,SAAA,EAA+C;IAA/CL,EAAA,CAAAS,UAAA,SAAAF,OAAA,CAAA6B,QAAA,IAAA7B,OAAA,CAAA6B,QAAA,CAAA0B,MAAA,KAA+C;IA2BhD9D,EAAA,CAAAK,SAAA,EAA+C;IAA/CL,EAAA,CAAAS,UAAA,SAAAF,OAAA,CAAAsD,QAAA,IAAAtD,OAAA,CAAAsD,QAAA,CAAAC,MAAA,KAA+C;IAc5E9D,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAqC,kBAAA,MAAA3B,MAAA,CAAAkF,UAAA,CAAArF,OAAA,CAAAsF,SAAA,OACF;IAKS7F,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAA8F,gBAAA,YAAApF,MAAA,CAAAuE,UAAA,CAAwB;IAEvBjF,EAAA,CAAAK,SAAA,EAA8C;IAA9CL,EAAA,CAAAS,UAAA,cAAAC,MAAA,CAAAuE,UAAA,KAAAvE,MAAA,CAAAuE,UAAA,CAAAc,IAAA,GAA8C;;;;;IAS5D/F,EAAA,CAAAC,cAAA,cAA8C;IAC5CD,EAAA,CAAAE,SAAA,cAAoF;IAElFF,EADF,CAAAC,cAAA,cAA0B,cACK;IAAAD,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5DJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAG,MAAA,GAA0B;IACvDH,EADuD,CAAAI,YAAA,EAAO,EACxD;IACNJ,EAAA,CAAAC,cAAA,kBAA2B;IAAAD,EAAA,CAAAG,MAAA,aAAM;IACnCH,EADmC,CAAAI,YAAA,EAAS,EACtC;;;;IANCJ,EAAA,CAAAK,SAAA,EAA0B;IAACL,EAA3B,CAAAS,UAAA,QAAAC,MAAA,CAAAsF,WAAA,CAAAV,MAAA,EAAAtF,EAAA,CAAAa,aAAA,CAA0B,QAAAH,MAAA,CAAAsF,WAAA,CAAAzC,QAAA,CAA6B;IAE7BvD,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,iBAAA,CAAAI,MAAA,CAAAsF,WAAA,CAAAzC,QAAA,CAA0B;IAC5BvD,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,iBAAA,CAAAI,MAAA,CAAAsF,WAAA,CAAAC,QAAA,CAA0B;;;;;;IAYrDjG,EAAA,CAAAC,cAAA,eAA8G;IAA/BD,EAAA,CAAAgB,UAAA,mBAAAkF,yDAAA;MAAA,MAAAC,QAAA,GAAAnG,EAAA,CAAAkB,aAAA,CAAAkF,IAAA,EAAAhF,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAA2F,cAAA,CAAAF,QAAA,CAAoB;IAAA,EAAC;IAC3GnG,EAAA,CAAAE,SAAA,eAA4E;IAE1EF,EADF,CAAAC,cAAA,eAA6B,gBACG;IAAAD,EAAA,CAAAG,MAAA,GAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,GAA6B;IAEhEH,EAFgE,CAAAI,YAAA,EAAO,EAC/D,EACF;;;;;IALCJ,EAAA,CAAAK,SAAA,EAA2B;IAACL,EAA5B,CAAAS,UAAA,QAAA0F,QAAA,CAAAG,MAAA,qBAAAH,QAAA,CAAAG,MAAA,IAAAC,GAAA,EAAAvG,EAAA,CAAAa,aAAA,CAA2B,QAAAsF,QAAA,CAAAzD,IAAA,CAAkB;IAElB1C,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,iBAAA,CAAA6F,QAAA,CAAAzD,IAAA,CAAe;IACd1C,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAM,iBAAA,CAAAI,MAAA,CAAAiC,WAAA,CAAAwD,QAAA,CAAAvD,KAAA,EAA6B;;;;;IAoB1D5C,EAAA,CAAAC,cAAA,gBAA2D;IAAAD,EAAA,CAAAG,MAAA,GAAwC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAA/CJ,EAAA,CAAAK,SAAA,EAAwC;IAAxCL,EAAA,CAAAM,iBAAA,CAAAI,MAAA,CAAAiC,WAAA,CAAA6D,WAAA,CAAAC,aAAA,EAAwC;;;;;;IAPzGzG,EAAA,CAAAC,cAAA,eAAkH;IAAlCD,EAAA,CAAAgB,UAAA,mBAAA0F,yDAAA;MAAA,MAAAF,WAAA,GAAAxG,EAAA,CAAAkB,aAAA,CAAAyF,IAAA,EAAAvF,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAA2F,cAAA,CAAAG,WAAA,CAAuB;IAAA,EAAC;IAC/GxG,EAAA,CAAAE,SAAA,eAAgF;IAE9EF,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACrDJ,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAErDJ,EADF,CAAAC,cAAA,eAA4B,gBACE;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACnEJ,EAAA,CAAAwB,UAAA,KAAAoF,2CAAA,oBAA2D;IAGjE5G,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;;IATCJ,EAAA,CAAAK,SAAA,EAA8B;IAACL,EAA/B,CAAAS,UAAA,QAAA+F,WAAA,CAAAF,MAAA,qBAAAE,WAAA,CAAAF,MAAA,IAAAC,GAAA,EAAAvG,EAAA,CAAAa,aAAA,CAA8B,QAAA2F,WAAA,CAAA9D,IAAA,CAAqB;IAE1B1C,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAkG,WAAA,CAAA9D,IAAA,CAAkB;IACjB1C,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAkG,WAAA,CAAAK,KAAA,CAAmB;IAElB7G,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAI,MAAA,CAAAiC,WAAA,CAAA6D,WAAA,CAAA5D,KAAA,EAAgC;IAC9B5C,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAS,UAAA,SAAA+F,WAAA,CAAAC,aAAA,CAA2B;;;;;;IAc/DzG,EAAA,CAAAC,cAAA,eAAkH;IAAlCD,EAAA,CAAAgB,UAAA,mBAAA8F,yDAAA;MAAA,MAAAC,WAAA,GAAA/G,EAAA,CAAAkB,aAAA,CAAA8F,IAAA,EAAA5F,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAA2F,cAAA,CAAAU,WAAA,CAAuB;IAAA,EAAC;IAC/G/G,EAAA,CAAAE,SAAA,eAAgF;IAChFF,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,SAAA,aAA2B;IAC3BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,UAAG;IACXH,EADW,CAAAI,YAAA,EAAO,EACZ;IAEJJ,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEnDJ,EADF,CAAAC,cAAA,eAA4B,YACpB;IAAAD,EAAA,CAAAE,SAAA,cAA0B;IAACF,EAAA,CAAAG,MAAA,IAAiD;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzFJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,SAAA,cAA4B;IAACF,EAAA,CAAAG,MAAA,IAAiD;IACtFH,EADsF,CAAAI,YAAA,EAAO,EACvF;IACNJ,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAG,MAAA,IAAgC;IAEjEH,EAFiE,CAAAI,YAAA,EAAO,EAChE,EACF;;;;;IAbCJ,EAAA,CAAAK,SAAA,EAA8B;IAACL,EAA/B,CAAAS,UAAA,QAAAsG,WAAA,CAAAT,MAAA,qBAAAS,WAAA,CAAAT,MAAA,IAAAC,GAAA,EAAAvG,EAAA,CAAAa,aAAA,CAA8B,QAAAkG,WAAA,CAAArE,IAAA,CAAqB;IAM1B1C,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAyG,WAAA,CAAArE,IAAA,CAAkB;IAEX1C,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAqC,kBAAA,MAAA3B,MAAA,CAAAuG,YAAA,EAAAF,WAAA,CAAAG,SAAA,kBAAAH,WAAA,CAAAG,SAAA,CAAAC,KAAA,YAAiD;IAC/CnH,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAqC,kBAAA,MAAA3B,MAAA,CAAAuG,YAAA,EAAAF,WAAA,CAAAG,SAAA,kBAAAH,WAAA,CAAAG,SAAA,CAAA3E,KAAA,YAAiD;IAEzDvC,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAI,MAAA,CAAAiC,WAAA,CAAAoE,WAAA,CAAAnE,KAAA,EAAgC;;;;;;IAajE5C,EAAA,CAAAC,cAAA,eAA4G;IAAlCD,EAAA,CAAAgB,UAAA,mBAAAoG,yDAAA;MAAA,MAAAC,WAAA,GAAArH,EAAA,CAAAkB,aAAA,CAAAoG,IAAA,EAAAlG,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAA2F,cAAA,CAAAgB,WAAA,CAAuB;IAAA,EAAC;IACzGrH,EAAA,CAAAE,SAAA,eAA+E;IAC/EF,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAG,MAAA,UAAG;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAE9BJ,EADF,CAAAC,cAAA,eAA0B,gBACG;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpDJ,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACtDJ,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAG,MAAA,IAAgC;IAEhEH,EAFgE,CAAAI,YAAA,EAAO,EAC/D,EACF;;;;;IAPCJ,EAAA,CAAAK,SAAA,EAA8B;IAACL,EAA/B,CAAAS,UAAA,QAAA4G,WAAA,CAAAf,MAAA,qBAAAe,WAAA,CAAAf,MAAA,IAAAC,GAAA,EAAAvG,EAAA,CAAAa,aAAA,CAA8B,QAAAwG,WAAA,CAAA3E,IAAA,CAAqB;IAG3B1C,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAA+G,WAAA,CAAA3E,IAAA,CAAkB;IACjB1C,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAA+G,WAAA,CAAAR,KAAA,CAAmB;IACnB7G,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAI,MAAA,CAAAiC,WAAA,CAAA0E,WAAA,CAAAzE,KAAA,EAAgC;;;;;;IAahE5C,EAAA,CAAAC,cAAA,eAA4E;IAC1ED,EAAA,CAAAE,SAAA,eAAwE;IAEtEF,EADF,CAAAC,cAAA,eAA4B,gBACO;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC3DJ,EAAA,CAAAC,cAAA,gBAAkC;IAAAD,EAAA,CAAAG,MAAA,GAA4C;IAChFH,EADgF,CAAAI,YAAA,EAAO,EACjF;IACNJ,EAAA,CAAAC,cAAA,kBAAsD;IAA3BD,EAAA,CAAAgB,UAAA,mBAAAuG,4DAAA;MAAA,MAAAC,QAAA,GAAAxH,EAAA,CAAAkB,aAAA,CAAAuG,IAAA,EAAArG,SAAA;MAAA,MAAAV,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAAgH,UAAA,CAAAF,QAAA,CAAgB;IAAA,EAAC;IAACxH,EAAA,CAAAG,MAAA,aAAM;IAC9DH,EAD8D,CAAAI,YAAA,EAAS,EACjE;;;;;IANCJ,EAAA,CAAAK,SAAA,EAAmB;IAACL,EAApB,CAAAS,UAAA,QAAA+G,QAAA,CAAAlC,MAAA,EAAAtF,EAAA,CAAAa,aAAA,CAAmB,QAAA2G,QAAA,CAAAjE,QAAA,CAAsB;IAEXvD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAkH,QAAA,CAAAjE,QAAA,CAAmB;IAClBvD,EAAA,CAAAK,SAAA,GAA4C;IAA5CL,EAAA,CAAAqC,kBAAA,KAAA3B,MAAA,CAAAuG,YAAA,CAAAO,QAAA,CAAAG,SAAA,gBAA4C;;;;;;IA9OtF3H,EAJJ,CAAAC,cAAA,aAAiD,aAExB,iBAEY;IAC/BD,EAAA,CAAAE,SAAA,2BAA6C;IAC/CF,EAAA,CAAAI,YAAA,EAAU;IAGVJ,EAAA,CAAAC,cAAA,iBAA4B;IAC1BD,EAAA,CAAAwB,UAAA,IAAAoG,sCAAA,wBAA4F;IA6HhG5H,EADE,CAAAI,YAAA,EAAU,EACN;IAGNJ,EAAA,CAAAC,cAAA,gBAAiC;IAE/BD,EAAA,CAAAwB,UAAA,IAAAqG,kCAAA,kBAA8C;IAY1C7H,EAFJ,CAAAC,cAAA,kBAAiC,cACH,UACtB;IAAAD,EAAA,CAAAG,MAAA,8BAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/BJ,EAAA,CAAAC,cAAA,kBAA6D;IAAjCD,EAAA,CAAAgB,UAAA,mBAAA8G,sDAAA;MAAA9H,EAAA,CAAAkB,aAAA,CAAA6G,GAAA;MAAA,MAAArH,MAAA,GAAAV,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASZ,MAAA,CAAAsH,oBAAA,EAAsB;IAAA,EAAC;IAAChI,EAAA,CAAAG,MAAA,eAAO;IACtEH,EADsE,CAAAI,YAAA,EAAS,EACzE;IACNJ,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAwB,UAAA,KAAAyG,mCAAA,kBAA8G;IAQlHjI,EADE,CAAAI,YAAA,EAAM,EACE;IAKNJ,EAFJ,CAAAC,cAAA,mBAAiC,eACH,UACtB;IAAAD,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,kBAA+D;IAAAD,EAAA,CAAAG,MAAA,eAAO;IACxEH,EADwE,CAAAI,YAAA,EAAS,EAC3E;IACNJ,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAwB,UAAA,KAAA0G,mCAAA,mBAAkH;IAYtHlI,EADE,CAAAI,YAAA,EAAM,EACE;IAKNJ,EAFJ,CAAAC,cAAA,mBAAiC,eACH,UACtB;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrBJ,EAAA,CAAAC,cAAA,kBAA+D;IAAAD,EAAA,CAAAG,MAAA,eAAO;IACxEH,EADwE,CAAAI,YAAA,EAAS,EAC3E;IACNJ,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAwB,UAAA,KAAA2G,mCAAA,mBAAkH;IAgBtHnI,EADE,CAAAI,YAAA,EAAM,EACE;IAKNJ,EAFJ,CAAAC,cAAA,mBAAiC,eACH,UACtB;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrBJ,EAAA,CAAAC,cAAA,kBAA0D;IAAAD,EAAA,CAAAG,MAAA,eAAO;IACnEH,EADmE,CAAAI,YAAA,EAAS,EACtE;IACNJ,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAwB,UAAA,KAAA4G,mCAAA,mBAA4G;IAUhHpI,EADE,CAAAI,YAAA,EAAM,EACE;IAKNJ,EAFJ,CAAAC,cAAA,mBAAiC,eACH,UACtB;IAAAD,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,kBAA4B;IAAAD,EAAA,CAAAG,MAAA,eAAO;IACrCH,EADqC,CAAAI,YAAA,EAAS,EACxC;IACNJ,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAwB,UAAA,KAAA6G,mCAAA,kBAA4E;IAShFrI,EADE,CAAAI,YAAA,EAAM,EACE;IAKNJ,EAFJ,CAAAC,cAAA,eAA4B,eACA,aACZ;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACrBJ,EAAA,CAAAC,cAAA,aAAY;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACpBJ,EAAA,CAAAC,cAAA,aAAY;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACrBJ,EAAA,CAAAC,cAAA,aAAY;IAAAD,EAAA,CAAAG,MAAA,WAAG;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACnBJ,EAAA,CAAAC,cAAA,aAAY;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACpBJ,EAAA,CAAAC,cAAA,aAAY;IAAAD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACvBJ,EAAA,CAAAC,cAAA,aAAY;IAAAD,EAAA,CAAAG,MAAA,aAAK;IACnBH,EADmB,CAAAI,YAAA,EAAI,EACjB;IAEJJ,EADF,CAAAC,cAAA,eAAuB,YACf;IAAAD,EAAA,CAAAG,MAAA,4BAAe;IAI7BH,EAJ6B,CAAAI,YAAA,EAAO,EACxB,EACF,EACA,EACJ;;;;IA/P0BJ,EAAA,CAAAK,SAAA,GAAmB;IAAAL,EAAnB,CAAAS,UAAA,YAAAC,MAAA,CAAA4H,cAAA,CAAmB,iBAAA5H,MAAA,CAAA6H,aAAA,CAAsB;IAkI1CvI,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAsF,WAAA,CAAiB;IAgBlBhG,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAA8H,gBAAA,CAAAnF,KAAA,OAA+B;IAiB5BrD,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAA+H,gBAAA,CAAApF,KAAA,OAA+B;IAqB/BrD,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAAgI,gBAAA,CAAArF,KAAA,OAA+B;IAyB/BrD,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAAiI,WAAA,CAAAtF,KAAA,OAA0B;IAmB7BrD,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAAkI,cAAA,CAAAvF,KAAA,OAA6B;;;ADrO7D,OAAM,MAAOwF,aAAa;EAoBxBC,YACUC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,oBAA0C;IAH1C,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IAvB9B,KAAAT,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAQ,aAAa,GAAU,EAAE;IACzB,KAAAC,UAAU,GAAU,EAAE;IACtB,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAAC,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAhB,cAAc,GAAU,EAAE;IAC1B,KAAAE,gBAAgB,GAAU,EAAE;IAC5B,KAAAI,cAAc,GAAU,EAAE;IAC1B,KAAA5C,WAAW,GAAQ,IAAI;IACvB,KAAAf,UAAU,GAAG,EAAE;IAEf;IACA,KAAAsE,gBAAgB,GAAG,KAAK;IACxB,KAAAC,gBAAgB,GAA4B,IAAI;EAO7C;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAD,eAAeA,CAAA;IACb,IAAI,CAACV,WAAW,CAACY,YAAY,CAACC,SAAS,CAACzE,IAAI,IAAG;MAC7C,IAAI,CAACiE,eAAe,GAAG,CAAC,CAACjE,IAAI;MAC7B,IAAI,CAACW,WAAW,GAAGX,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAuE,iBAAiBA,CAAA;IACf,IAAI,CAACG,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAF,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACd,WAAW,CAACiB,QAAQ,EAAE,CAACJ,SAAS,CAAC;MACpCK,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC9B,cAAc,GAAG8B,QAAQ,CAACC,IAAI,IAAI,EAAE;QACzCC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACjC,cAAc,CAACxE,MAAM,CAAC;MACtE,CAAC;MACD0G,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAAClC,cAAc,GAAG,EAAE;MAC1B;KACD,CAAC;IACA;MACEmC,EAAE,EAAE,GAAG,EACPpF,IAAI;MAAE;QACJ9B,QAAQ,EAAE,iBAAiB,EAC3B+B,MAAM;QAAE,oEAAoE;;MAE9EC,QAAQ,EAAE,oEAAoE,EAC9EC,OAAO;MAAE,+FAA+F,EACxGhF,QAAQ;MAAE,cAAc,EACxB+B,KAAK;MAAE,GAAG,EACVmD,OAAO;MAAE,IAAI,EACbC,OAAO;MAAE,KAAK,EACdE,SAAS;MAAE,IAAI6E,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MACtDvI,QAAQ;MAAE,CACR;QACET,cAAc,EAAE,SAAS;QACzBE,QAAQ,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC1B6I,OAAO,EAAE;UACPC,GAAG,EAAE,GAAG;UACRnI,IAAI,EAAE,iBAAiB;UACvBE,KAAK,EAAE,MAAM;UACb0D,MAAM,EAAE,CAAC;YAAEC,GAAG,EAAE;UAAoE,CAAE;;OAEzF,EACD;QACE5E,cAAc,EAAE,UAAU;QAC1BE,QAAQ,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC1B+I,QAAQ,EAAE;UACRD,GAAG,EAAE,MAAM;UACXnI,IAAI,EAAE,cAAc;UACpBqI,IAAI,EAAE,cAAc;UACpBC,YAAY,EAAE,EAAE;UAChBvI,KAAK,EAAE;;OAEV,CACF,EACDoB,QAAQ;MAAE,CACR;QAAEN,QAAQ,EAAE,eAAe;QAAEC,IAAI,EAAE;MAAyB,CAAE,CAC/D;;IAEH;MACEiH,EAAE,EAAE,GAAG,EACPpF,IAAI;MAAE;QACJ9B,QAAQ,EAAE,cAAc,EACxB+B,MAAM;QAAE,oEAAoE;;MAE9EC,QAAQ,EAAE,oEAAoE,EAC9EC,OAAO;MAAE,+FAA+F,EACxGhF,QAAQ;MAAE,mBAAmB,EAC7B+B,KAAK;MAAE,GAAG,EACVmD,OAAO;MAAE,KAAK,EACdC,OAAO;MAAE,IAAI,EACbE,SAAS;MAAE,IAAI6E,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MACtDvI,QAAQ;MAAE,CACR;QACET,cAAc,EAAE,SAAS;QACzBE,QAAQ,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC1B6I,OAAO,EAAE;UACPC,GAAG,EAAE,GAAG;UACRnI,IAAI,EAAE,mBAAmB;UACzBE,KAAK,EAAE,KAAK;UACZ0D,MAAM,EAAE,CAAC;YAAEC,GAAG,EAAE;UAAoE,CAAE;;OAEzF,EACD;QACE5E,cAAc,EAAE,QAAQ;QACxBE,QAAQ,EAAE;UAAEC,CAAC,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC1BkJ,MAAM,EAAE;UACNJ,GAAG,EAAE,SAAS;UACdtH,QAAQ,EAAE,eAAe;UACzB0C,QAAQ,EAAE,eAAe;UACzBX,MAAM,EAAE,oEAAoE;UAC5E9E,QAAQ,EAAE;;OAEb,CACF,EACDqD,QAAQ;MAAE,EAAE;;IAEf;EACH;EAEAmG,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAAChB,cAAc,CAACkC,qBAAqB,CAAC,QAAQ,CAAC,CAACpB,SAAS,CAAC;MAC5DK,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC5B,gBAAgB,GAAG4B,QAAQ,CAACC,IAAI,IAAI,EAAE;MAC7C,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAAChC,gBAAgB,GAAG,EAAE;MAC5B;KACD,CAAC;EACJ;EAEAyB,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAChB,WAAW,CAACkC,iBAAiB,EAAE,CAACrB,SAAS,CAAC;MAC7CK,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACxB,cAAc,GAAGwB,QAAQ,CAACC,IAAI,IAAI,EAAE;MAC3C,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAAC5B,cAAc,GAAG,EAAE;MAC1B;KACD,CAAC;EACJ;EAEMc,YAAYA,CAAA;IAAA,IAAA0B,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAI;QACFD,KAAI,CAAC/B,SAAS,GAAG,IAAI;QAErB;QACA,MAAM,CAACiC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,CAAC,SAASC,OAAO,CAACC,GAAG,CAAC,CACvDN,KAAI,CAACpC,cAAc,CAAC2C,mBAAmB,EAAE,CAACC,SAAS,EAAE,EACrDR,KAAI,CAACpC,cAAc,CAAC6C,mBAAmB,EAAE,CAACD,SAAS,EAAE,EACrDR,KAAI,CAACpC,cAAc,CAAC8C,cAAc,EAAE,CAACF,SAAS,EAAE,CACjD,CAAC;QAEFR,KAAI,CAAC3C,gBAAgB,GAAG,CAAE6C,QAAgB,EAAElJ,QAAQ,IAAKkJ,QAAgB,EAAEjB,IAAI,IAAIiB,QAAQ,IAAI,EAAE,EAAEjI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9G+H,KAAI,CAAC1C,gBAAgB,GAAG,CAAE6C,QAAgB,EAAEnJ,QAAQ,IAAKmJ,QAAgB,EAAElB,IAAI,IAAIkB,QAAQ,IAAI,EAAE,EAAElI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9G+H,KAAI,CAACzC,WAAW,GAAG,CAAE6C,QAAgB,EAAEpJ,QAAQ,IAAKoJ,QAAgB,EAAEnB,IAAI,IAAImB,QAAQ,IAAI,EAAE,EAAEnI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAEzG;QACA,IAAI+H,KAAI,CAAC3C,gBAAgB,CAAC3E,MAAM,KAAK,CAAC,EAAE;UACtCsH,KAAI,CAAC3C,gBAAgB,GAAG2C,KAAI,CAACW,mBAAmB,EAAE;;QAEpD,IAAIX,KAAI,CAAC1C,gBAAgB,CAAC5E,MAAM,KAAK,CAAC,EAAE;UACtCsH,KAAI,CAAC1C,gBAAgB,GAAG0C,KAAI,CAACW,mBAAmB,EAAE;;QAEpD,IAAIX,KAAI,CAACzC,WAAW,CAAC7E,MAAM,KAAK,CAAC,EAAE;UACjCsH,KAAI,CAACzC,WAAW,GAAGyC,KAAI,CAACW,mBAAmB,EAAE;;QAG/C;QACAX,KAAI,CAACY,cAAc,EAAE;QAErB;QACAZ,KAAI,CAACa,iBAAiB,EAAE;OAEzB,CAAC,OAAOzB,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDY,KAAI,CAACc,gBAAgB,EAAE;OACxB,SAAS;QACRd,KAAI,CAAC/B,SAAS,GAAG,KAAK;;IACvB;EACH;EAEA2C,cAAcA,CAAA;IACZ,IAAI,CAAC5C,UAAU,GAAG,CAChB;MAAE1G,IAAI,EAAE,OAAO;MAAEyJ,IAAI,EAAE,eAAe;MAAEC,KAAK,EAAE;IAA0C,CAAE,EAC3F;MAAE1J,IAAI,EAAE,KAAK;MAAEyJ,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE;IAA0C,CAAE,EACvF;MAAE1J,IAAI,EAAE,MAAM;MAAEyJ,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE;IAA0C,CAAE,EACzF;MAAE1J,IAAI,EAAE,aAAa;MAAEyJ,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAA0C,CAAE,EAC9F;MAAE1J,IAAI,EAAE,OAAO;MAAEyJ,IAAI,EAAE,oBAAoB;MAAEC,KAAK,EAAE;IAA0C,CAAE,EAChG;MAAE1J,IAAI,EAAE,MAAM;MAAEyJ,IAAI,EAAE,qBAAqB;MAAEC,KAAK,EAAE;IAA0C,CAAE,CACjG;EACH;EAEAH,iBAAiBA,CAAA;IACf,IAAI,CAAC9C,aAAa,GAAG,CACnB;MACEkD,KAAK,EAAE,4BAA4B;MACnC9G,QAAQ,EAAE,oEAAoE;MAC9E+G,SAAS,EAAE,OAAO;MAClBpF,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAE5E,KAAK,EAAE;MAAG;KACtC,EACD;MACE8J,KAAK,EAAE,0BAA0B;MACjC9G,QAAQ,EAAE,oEAAoE;MAC9E+G,SAAS,EAAE,OAAO;MAClBpF,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAE5E,KAAK,EAAE;MAAI;KACvC,CACF;EACH;EAEAwJ,mBAAmBA,CAAA;IACjB,OAAO,CACL;MACElB,GAAG,EAAE,GAAG;MACRnI,IAAI,EAAE,sBAAsB;MAC5BmE,KAAK,EAAE,eAAe;MACtBjE,KAAK,EAAE,KAAK;MACZ6D,aAAa,EAAE,MAAM;MACrB8F,QAAQ,EAAE,EAAE;MACZjG,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAiE,CAAE,CAAC;MACpFiG,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpCxF,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAE5E,KAAK,EAAE;MAAG;KACtC,EACD;MACEsI,GAAG,EAAE,GAAG;MACRnI,IAAI,EAAE,iBAAiB;MACvBmE,KAAK,EAAE,UAAU;MACjBjE,KAAK,EAAE,MAAM;MACb6D,aAAa,EAAE,MAAM;MACrB8F,QAAQ,EAAE,EAAE;MACZjG,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAoE,CAAE,CAAC;MACvFiG,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAE,CAAE;MACnCxF,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAE5E,KAAK,EAAE;MAAI;KACvC,EACD;MACEsI,GAAG,EAAE,GAAG;MACRnI,IAAI,EAAE,mBAAmB;MACzBmE,KAAK,EAAE,SAAS;MAChBjE,KAAK,EAAE,KAAK;MACZ6D,aAAa,EAAE,KAAK;MACpB8F,QAAQ,EAAE,EAAE;MACZjG,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAoE,CAAE,CAAC;MACvFiG,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpCxF,SAAS,EAAE;QAAEC,KAAK,EAAE,IAAI;QAAE5E,KAAK,EAAE;MAAG;KACrC,EACD;MACEsI,GAAG,EAAE,GAAG;MACRnI,IAAI,EAAE,oBAAoB;MAC1BmE,KAAK,EAAE,eAAe;MACtBjE,KAAK,EAAE,MAAM;MACb0D,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAoE,CAAE,CAAC;MACvFiG,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpCxF,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAE5E,KAAK,EAAE;MAAI;KACvC,EACD;MACEsI,GAAG,EAAE,GAAG;MACRnI,IAAI,EAAE,qBAAqB;MAC3BmE,KAAK,EAAE,UAAU;MACjBjE,KAAK,EAAE,MAAM;MACb0D,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAoE,CAAE,CAAC;MACvFiG,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpCxF,SAAS,EAAE;QAAEC,KAAK,EAAE,IAAI;QAAE5E,KAAK,EAAE;MAAG;KACrC,EACD;MACEsI,GAAG,EAAE,GAAG;MACRnI,IAAI,EAAE,qBAAqB;MAC3BmE,KAAK,EAAE,eAAe;MACtBjE,KAAK,EAAE,MAAM;MACb6D,aAAa,EAAE,MAAM;MACrB8F,QAAQ,EAAE,EAAE;MACZjG,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAiE,CAAE,CAAC;MACpFiG,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAE,CAAE;MACnCxF,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAE5E,KAAK,EAAE;MAAI;KACvC,EACD;MACEsI,GAAG,EAAE,GAAG;MACRnI,IAAI,EAAE,kBAAkB;MACxBmE,KAAK,EAAE,UAAU;MACjBjE,KAAK,EAAE,MAAM;MACb0D,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAoE,CAAE,CAAC;MACvFiG,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAE,CAAE;MACnCxF,SAAS,EAAE;QAAEC,KAAK,EAAE,IAAI;QAAE5E,KAAK,EAAE;MAAG;KACrC,EACD;MACEsI,GAAG,EAAE,GAAG;MACRnI,IAAI,EAAE,gBAAgB;MACtBmE,KAAK,EAAE,SAAS;MAChBjE,KAAK,EAAE,KAAK;MACZ6D,aAAa,EAAE,KAAK;MACpB8F,QAAQ,EAAE,EAAE;MACZjG,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAoE,CAAE,CAAC;MACvFiG,MAAM,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAG,CAAE;MACpCxF,SAAS,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAE5E,KAAK,EAAE;MAAG;KACtC,CACF;EACH;EAEA2J,gBAAgBA,CAAA;IACd,IAAI,CAACzD,gBAAgB,GAAG,IAAI,CAACsD,mBAAmB,EAAE;IAClD,IAAI,CAACrD,gBAAgB,GAAG,IAAI,CAACqD,mBAAmB,EAAE;IAClD,IAAI,CAACpD,WAAW,GAAG,IAAI,CAACoD,mBAAmB,EAAE;EAC/C;EAEA;EACA1F,cAAcA,CAACuE,OAAY;IACzB,IAAI,CAAC7B,MAAM,CAAC4D,QAAQ,CAAC,CAAC,UAAU,EAAE/B,OAAO,CAACC,GAAG,IAAID,OAAO,CAACH,EAAE,CAAC,CAAC;EAC/D;EAEAmC,eAAeA,CAAC9B,QAAa;IAC3B,IAAI,CAAC/B,MAAM,CAAC4D,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAC9BE,WAAW,EAAE;QAAE/B,QAAQ,EAAEA,QAAQ,CAACpI,IAAI,CAACoK,WAAW;MAAE;KACrD,CAAC;EACJ;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAAChE,MAAM,CAAC4D,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEA;EACAxI,UAAUA,CAAC6I,IAAS;IAClB,IAAI,CAACA,IAAI,CAACvC,EAAE,IAAI,CAACuC,IAAI,CAACnC,GAAG,EAAE;IAE3B,MAAMoC,MAAM,GAAGD,IAAI,CAACvC,EAAE,IAAIuC,IAAI,CAACnC,GAAG;IAClC,MAAMqC,MAAM,GAAGF,IAAI,CAACtH,OAAO,GAAG,YAAY,GAAG,UAAU;IACvD,MAAMyH,UAAU,GAAGH,IAAI,CAACtH,OAAO,GAC3B,IAAI,CAACwD,oBAAoB,CAACkE,UAAU,CAACH,MAAM,CAAC,GAC5C,IAAI,CAAC/D,oBAAoB,CAACmE,QAAQ,CAACJ,MAAM,CAAC;IAE9C;IACAD,IAAI,CAACtH,OAAO,GAAG,CAACsH,IAAI,CAACtH,OAAO;IAC5BsH,IAAI,CAACzK,KAAK,IAAIyK,IAAI,CAACtH,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;IAEnCyH,UAAU,CAACrD,SAAS,CAAC;MACnBK,IAAI,EAAGmD,MAAM,IAAI;QACf,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;UACnB;UACAP,IAAI,CAACtH,OAAO,GAAG,CAACsH,IAAI,CAACtH,OAAO;UAC5BsH,IAAI,CAACzK,KAAK,IAAIyK,IAAI,CAACtH,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;UACnC4E,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAE8C,MAAM,CAACE,OAAO,CAAC;;MAE3D,CAAC;MACDhD,KAAK,EAAGA,KAAK,IAAI;QACf;QACAwC,IAAI,CAACtH,OAAO,GAAG,CAACsH,IAAI,CAACtH,OAAO;QAC5BsH,IAAI,CAACzK,KAAK,IAAIyK,IAAI,CAACtH,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;QACnC4E,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;KACD,CAAC;EACJ;EAEA/F,UAAUA,CAACuI,IAAS;IAClB,IAAI,CAACA,IAAI,CAACvC,EAAE,IAAI,CAACuC,IAAI,CAACnC,GAAG,EAAE;IAE3B,MAAMoC,MAAM,GAAGD,IAAI,CAACvC,EAAE,IAAIuC,IAAI,CAACnC,GAAG;IAElC;IACAmC,IAAI,CAACrH,OAAO,GAAG,CAACqH,IAAI,CAACrH,OAAO;IAE5B,IAAI,CAACuD,oBAAoB,CAACuE,QAAQ,CAACR,MAAM,CAAC,CAACnD,SAAS,CAAC;MACnDK,IAAI,EAAGmD,MAAM,IAAI;QACf,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;UACnB;UACAP,IAAI,CAACrH,OAAO,GAAG,CAACqH,IAAI,CAACrH,OAAO;UAC5B2E,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAE8C,MAAM,CAACE,OAAO,CAAC;;MAEzD,CAAC;MACDhD,KAAK,EAAGA,KAAK,IAAI;QACf;QACAwC,IAAI,CAACrH,OAAO,GAAG,CAACqH,IAAI,CAACrH,OAAO;QAC5B2E,OAAO,CAACE,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;KACD,CAAC;EACJ;EAEAjG,SAASA,CAACyI,IAAS;IACjB,IAAI,CAACA,IAAI,CAACvC,EAAE,IAAI,CAACuC,IAAI,CAACnC,GAAG,EAAE;IAE3B,MAAMoC,MAAM,GAAGD,IAAI,CAACvC,EAAE,IAAIuC,IAAI,CAACnC,GAAG;IAElC,IAAI,CAAC3B,oBAAoB,CAAC3E,SAAS,CAAC0I,MAAM,CAAC,CAACnD,SAAS,CAAC;MACpDK,IAAI,EAAGmD,MAAM,IAAI;QACf,IAAIA,MAAM,CAACC,OAAO,EAAE;UAClBjD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;SACxC,MAAM;UACLD,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAE8C,MAAM,CAACE,OAAO,CAAC;;MAE1D,CAAC;MACDhD,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC7C;KACD,CAAC;EACJ;EAEAnG,iBAAiBA,CAAC2I,IAAS;IACzB;IACA1C,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEyC,IAAI,CAACvC,EAAE,CAAC;EACjD;EAEAtF,UAAUA,CAAC6H,IAAS;IAClB,IAAI,CAAC,IAAI,CAAC/H,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACc,IAAI,EAAE,EAAE;IACjD,IAAI,CAACiH,IAAI,CAACvC,EAAE,IAAI,CAACuC,IAAI,CAACnC,GAAG,EAAE;IAE3B,MAAMoC,MAAM,GAAGD,IAAI,CAACvC,EAAE,IAAIuC,IAAI,CAACnC,GAAG;IAClC,MAAM6C,WAAW,GAAG,IAAI,CAACzI,UAAU,CAACc,IAAI,EAAE;IAE1C,IAAI,CAACmD,oBAAoB,CAACyE,aAAa,CAAC;MACtCV,MAAM,EAAEA,MAAM;MACdzJ,IAAI,EAAEkK;KACP,CAAC,CAAC5D,SAAS,CAAC;MACXK,IAAI,EAAGmD,MAAM,IAAI;QACf,IAAIA,MAAM,CAACC,OAAO,EAAE;UAClB;UACA,IAAI,CAACP,IAAI,CAACnJ,QAAQ,EAAE;YAClBmJ,IAAI,CAACnJ,QAAQ,GAAG,EAAE;;UAEpBmJ,IAAI,CAACnJ,QAAQ,CAAC+J,IAAI,CAAC;YACjBrK,QAAQ,EAAE,IAAI,CAACyC,WAAW,EAAEzC,QAAQ,IAAI,MAAM;YAC9CC,IAAI,EAAEkK;WACP,CAAC;UACF,IAAI,CAACzI,UAAU,GAAG,EAAE;UACpBqF,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;SAC1C,MAAM;UACLD,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAE8C,MAAM,CAACE,OAAO,CAAC;;MAE3D,CAAC;MACDhD,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C;KACD,CAAC;EACJ;EAEAqD,kBAAkBA,CAACjD,OAAY;IAC7BN,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEK,OAAO,CAAC;IAC7C;IACAA,OAAO,CAAC1I,WAAW,GAAG,CAAC0I,OAAO,CAAC1I,WAAW;EAC5C;EAEA;EACAc,MAAMA,CAAC4H,OAAY;IACjBN,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEK,OAAO,CAAC;IAEhC,IAAI,CAACA,OAAO,CAACH,EAAE,IAAI,CAACG,OAAO,CAACC,GAAG,EAAE;IAEjC,MAAMiD,SAAS,GAAGlD,OAAO,CAACH,EAAE,IAAIG,OAAO,CAACC,GAAG;IAE3C,IAAI,CAAC3B,oBAAoB,CAAClG,MAAM,CAAC;MAC/B8K,SAAS,EAAEA,SAAS;MACpBC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE;KACZ,CAAC,CAAClE,SAAS,CAAC;MACXK,IAAI,EAAGmD,MAAM,IAAI;QACf,IAAIA,MAAM,CAACC,OAAO,EAAE;UAClBjD,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;UAC1D;SACD,MAAM;UACLD,OAAO,CAACE,KAAK,CAAC,oBAAoB,EAAE8C,MAAM,CAACE,OAAO,CAAC;UACnD;UACA,IAAI,CAACS,wBAAwB,CAACrD,OAAO,CAAC;;MAE1C,CAAC;MACDJ,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QACzC;QACA,IAAI,CAACyD,wBAAwB,CAACrD,OAAO,CAAC;MACxC;KACD,CAAC;EACJ;EAEQqD,wBAAwBA,CAACrD,OAAY;IAC3C,IAAI,CAAC,IAAI,CAAC5E,WAAW,EAAE;MACrB,IAAI,CAAC+C,MAAM,CAAC4D,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;QAAEE,WAAW,EAAE;UAAEqB,SAAS,EAAE;QAAO;MAAE,CAAE,CAAC;MAC9E;;IAGF;IACA,IAAI,CAAC1E,gBAAgB,GAAG;MACtB2E,MAAM,EAAEvD,OAAO,CAAChI,KAAK;MACrBwL,SAAS,EAAE;QACTC,KAAK,EAAE,CAAC;UACN5D,EAAE,EAAEG,OAAO,CAACH,EAAE;UACd/H,IAAI,EAAEkI,OAAO,CAAClI,IAAI;UAClBE,KAAK,EAAEgI,OAAO,CAAChI,KAAK;UACpBmL,QAAQ,EAAE,CAAC;UACXtL,KAAK,EAAEmI,OAAO,CAACnI;SAChB,CAAC;QACF6L,QAAQ,EAAE1D,OAAO,CAAChI,KAAK;QACvB2L,GAAG,EAAE3D,OAAO,CAAChI,KAAK,GAAG,IAAI;QACzB4L,QAAQ,EAAE,CAAC;QACXjC,QAAQ,EAAE,CAAC;QACXkC,KAAK,EAAE7D,OAAO,CAAChI,KAAK,GAAIgI,OAAO,CAAChI,KAAK,GAAG;OACzC;MACD8L,WAAW,EAAE;QACXhM,IAAI,EAAE,IAAI,CAACsD,WAAW,CAACC,QAAQ,IAAI,IAAI,CAACD,WAAW,CAACzC,QAAQ;QAC5DoL,KAAK,EAAE,IAAI,CAAC3I,WAAW,CAAC2I,KAAK;QAC7BC,KAAK,EAAE,IAAI,CAAC5I,WAAW,CAAC4I,KAAK,IAAI;;KAEpC;IAED,IAAI,CAACrF,gBAAgB,GAAG,IAAI;EAC9B;EAEArG,aAAaA,CAAC0H,OAAY;IACxBN,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEK,OAAO,CAAC;IAExC,IAAI,CAACA,OAAO,CAACH,EAAE,IAAI,CAACG,OAAO,CAACC,GAAG,EAAE;IAEjC,MAAMiD,SAAS,GAAGlD,OAAO,CAACH,EAAE,IAAIG,OAAO,CAACC,GAAG;IAE3C,IAAI,CAAC3B,oBAAoB,CAAChG,aAAa,CAAC;MACtC4K,SAAS,EAAEA,SAAS;MACpBE,SAAS,EAAE;KACZ,CAAC,CAAClE,SAAS,CAAC;MACXK,IAAI,EAAGmD,MAAM,IAAI;QACf,IAAIA,MAAM,CAACC,OAAO,EAAE;UAClBjD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;SACtD,MAAM;UACLD,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAE8C,MAAM,CAACE,OAAO,CAAC;;MAE/D,CAAC;MACDhD,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;KACD,CAAC;EACJ;EAEApH,SAASA,CAACwH,OAAY;IACpBN,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEK,OAAO,CAAC;IAEpC,IAAI,CAACA,OAAO,CAACH,EAAE,IAAI,CAACG,OAAO,CAACC,GAAG,EAAE;IAEjC,MAAMiD,SAAS,GAAGlD,OAAO,CAACH,EAAE,IAAIG,OAAO,CAACC,GAAG;IAE3C,IAAI,CAAC3B,oBAAoB,CAAC9F,SAAS,CAAC;MAClC0K,SAAS,EAAEA,SAAS;MACpBC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE;KACZ,CAAC,CAAClE,SAAS,CAAC;MACXK,IAAI,EAAGmD,MAAM,IAAI;QACf,IAAIA,MAAM,CAACC,OAAO,EAAE;UAClBjD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;SAClD,MAAM;UACLD,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAE8C,MAAM,CAACE,OAAO,CAAC;;MAE3D,CAAC;MACDhD,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C;KACD,CAAC;EACJ;EAEA9C,UAAUA,CAACrC,IAAS;IAClBiF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAElF,IAAI,CAAC9B,QAAQ,CAAC;IAC1C;EACF;EAEAyE,oBAAoBA,CAAA;IAClB,IAAI,CAACe,MAAM,CAAC4D,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;MAAEE,WAAW,EAAE;QAAEgC,UAAU,EAAE;MAAY;IAAE,CAAE,CAAC;EAChF;EAEAlL,cAAcA,CAACqJ,IAAS;IACtB1C,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEyC,IAAI,CAACvC,EAAE,CAAC;IACjD;EACF;EAEA;EACA9H,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAIkM,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAACtM,KAAK,CAAC;EAClB;EAEAqE,YAAYA,CAACkI,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEA/M,gBAAgBA,CAACC,KAAa;IAC5B,OAAO,IAAI,CAAC0E,YAAY,CAAC1E,KAAK,CAAC;EACjC;EAEAqD,UAAUA,CAAC0J,IAAU;IACnB,MAAM3E,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAM6E,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAC9E,GAAG,CAAC+E,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE,IAAI,IAAI,CAAC;IAEzE,IAAIH,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC,GAAG;IACrE,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAC,GAAG;IACxE,IAAIA,aAAa,GAAG,MAAM,EAAE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAC,GAAG;IAC1E,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,MAAM,CAAC,GAAG;EACjD;EAEAhH,aAAaA,CAACoH,KAAa,EAAE3C,IAAS;IACpC,OAAOA,IAAI,CAACvC,EAAE;EAChB;EAEA;EACAmF,kBAAkBA,CAACC,aAAkB;IACnC,IAAI,CAACtG,gBAAgB,GAAG,KAAK;IAE7B,IAAIsG,aAAa,CAACC,MAAM,KAAK,SAAS,EAAE;MACtC;MACA,IAAI,CAAC/G,MAAM,CAAC4D,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE;QACzCE,WAAW,EAAE;UACXkD,OAAO,EAAEF,aAAa,CAACE,OAAO,IAAI,IAAI,CAACC,eAAe,EAAE;UACxDC,MAAM,EAAEJ,aAAa,CAACI;;OAEzB,CAAC;KACH,MAAM;MACL;MACAC,KAAK,CAAC,mCAAmC,CAAC;;EAE9C;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAAC5G,gBAAgB,GAAG,KAAK;EAC/B;EAEQyG,eAAeA,CAAA;IACrB,OAAO,KAAK,GAAGtF,IAAI,CAACC,GAAG,EAAE,CAAC0E,QAAQ,EAAE;EACtC;EAEA;EACA9N,iBAAiBA,CAAC6O,UAAe;IAC/B,IAAI,CAACA,UAAU,EAAE;IAEjB,MAAMzO,cAAc,GAAGyO,UAAU,CAACzO,cAAc,IAAI,SAAS;IAC7D,MAAM0O,UAAU,GAAGD,UAAU,CAACxF,OAAO,IAAIwF,UAAU,CAACtF,QAAQ,IAAIsF,UAAU,CAACnF,MAAM,IAAImF,UAAU,CAACvJ,KAAK;IAErG,QAAQlF,cAAc;MACpB,KAAK,SAAS;QACZ,IAAI,CAAC2O,iBAAiB,CAACD,UAAU,CAAC;QAClC;MACF,KAAK,UAAU;QACb,IAAI,CAACE,kBAAkB,CAACF,UAAU,CAAC;QACnC;MACF,KAAK,QAAQ;QACX,IAAI,CAACG,gBAAgB,CAACH,UAAU,CAAC;QACjC;MACF,KAAK,OAAO;QACV,IAAI,CAACI,eAAe,CAACJ,UAAU,CAAC;QAChC;MACF;QACE,IAAI,CAACC,iBAAiB,CAACD,UAAU,CAAC;;EAExC;EAEQC,iBAAiBA,CAAC1F,OAAY;IACpC,IAAIA,OAAO,EAAEC,GAAG,IAAID,OAAO,EAAEH,EAAE,EAAE;MAC/B,MAAMqD,SAAS,GAAGlD,OAAO,CAACC,GAAG,IAAID,OAAO,CAACH,EAAE;MAC3C,IAAI,CAAC1B,MAAM,CAAC4D,QAAQ,CAAC,CAAC,UAAU,EAAEmB,SAAS,CAAC,CAAC;;EAEjD;EAEQyC,kBAAkBA,CAACzF,QAAa;IACtC,IAAIA,QAAQ,EAAEC,IAAI,IAAID,QAAQ,EAAEpI,IAAI,EAAE;MACpC,MAAMgO,YAAY,GAAG5F,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACpI,IAAI,CAACoK,WAAW,EAAE,CAAC6D,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;MACtF,IAAI,CAAC5H,MAAM,CAAC4D,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;QAC9BE,WAAW,EAAE;UAAE/B,QAAQ,EAAE4F;QAAY;OACtC,CAAC;;EAEN;EAEQF,gBAAgBA,CAACvF,MAAW;IAClC,IAAIA,MAAM,EAAEJ,GAAG,IAAII,MAAM,EAAER,EAAE,IAAIQ,MAAM,EAAE1H,QAAQ,EAAE;MACjD,MAAMqN,QAAQ,GAAG3F,MAAM,CAACJ,GAAG,IAAII,MAAM,CAACR,EAAE,IAAIQ,MAAM,CAAC1H,QAAQ;MAC3D,IAAI,CAACwF,MAAM,CAAC4D,QAAQ,CAAC,CAAC,SAAS,EAAEiE,QAAQ,CAAC,CAAC;;EAE/C;EAEQH,eAAeA,CAAC5J,KAAU;IAChC,IAAIA,KAAK,EAAEkE,IAAI,IAAIlE,KAAK,EAAEnE,IAAI,EAAE;MAC9B,MAAMmO,SAAS,GAAGhK,KAAK,CAACkE,IAAI,IAAIlE,KAAK,CAACnE,IAAI,CAACoK,WAAW,EAAE,CAAC6D,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;MAC7E,IAAI,CAAC5H,MAAM,CAAC4D,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;QAC9BE,WAAW,EAAE;UAAEhG,KAAK,EAAEgK;QAAS;OAChC,CAAC;;EAEN;EAEA7O,aAAaA,CAACoO,UAAe;IAC3B,MAAMU,IAAI,GAAGV,UAAU,CAACzO,cAAc,IAAI,SAAS;IACnD,MAAMe,IAAI,GAAG,IAAI,CAAC5B,UAAU,CAACsP,UAAU,CAAC;IAExC,QAAQU,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,iBAAiBpO,IAAI,EAAE;MAChC,KAAK,UAAU;QACb,OAAO,oBAAoBA,IAAI,EAAE;MACnC,KAAK,QAAQ;QACX,OAAO,iBAAiBA,IAAI,EAAE;MAChC,KAAK,OAAO;QACV,OAAO,eAAeA,IAAI,EAAE;MAC9B;QACE,OAAO,SAASA,IAAI,EAAE;;EAE5B;EAEAT,UAAUA,CAACN,cAAsB;IAC/B,QAAQA,cAAc;MACpB,KAAK,SAAS;QACZ,OAAO,YAAY;MACrB,KAAK,UAAU;QACb,OAAO,iBAAiB;MAC1B,KAAK,QAAQ;QACX,OAAO,cAAc;MACvB,KAAK,OAAO;QACV,OAAO,cAAc;MACvB;QACE,OAAO,YAAY;;EAEzB;EAEAb,UAAUA,CAACsP,UAAe;IACxB,MAAMU,IAAI,GAAGV,UAAU,CAACzO,cAAc,IAAI,SAAS;IAEnD,QAAQmP,IAAI;MACV,KAAK,SAAS;QACZ,OAAOV,UAAU,CAACxF,OAAO,EAAElI,IAAI,IAAI,SAAS;MAC9C,KAAK,UAAU;QACb,OAAO0N,UAAU,CAACtF,QAAQ,EAAEpI,IAAI,IAAI,UAAU;MAChD,KAAK,QAAQ;QACX,OAAO0N,UAAU,CAACnF,MAAM,EAAEhF,QAAQ,IAAImK,UAAU,CAACnF,MAAM,EAAE1H,QAAQ,IAAI,QAAQ;MAC/E,KAAK,OAAO;QACV,OAAO6M,UAAU,CAACvJ,KAAK,EAAEnE,IAAI,IAAI,OAAO;MAC1C;QACE,OAAO0N,UAAU,CAACxF,OAAO,EAAElI,IAAI,IAAI,MAAM;;EAE/C;EAEA3B,cAAcA,CAACqP,UAAe;IAC5B,MAAMU,IAAI,GAAGV,UAAU,CAACzO,cAAc,IAAI,SAAS;IAEnD,QAAQmP,IAAI;MACV,KAAK,SAAS;QACZ,OAAOV,UAAU,CAACxF,OAAO,EAAEhI,KAAK,GAAG,IAAIwN,UAAU,CAACxF,OAAO,CAAChI,KAAK,EAAE,GAAG,EAAE;MACxE,KAAK,UAAU;QACb,OAAOwN,UAAU,CAACtF,QAAQ,EAAEE,YAAY,GAAG,GAAGoF,UAAU,CAACtF,QAAQ,CAACE,YAAY,WAAW,GAAG,iBAAiB;MAC/G,KAAK,QAAQ;QACX,OAAOoF,UAAU,CAACnF,MAAM,EAAEzK,QAAQ,IAAI,aAAa;MACrD,KAAK,OAAO;QACV,OAAO4P,UAAU,CAACvJ,KAAK,EAAEmE,YAAY,GAAG,GAAGoF,UAAU,CAACvJ,KAAK,CAACmE,YAAY,WAAW,GAAG,YAAY;MACpG;QACE,OAAO,EAAE;;EAEf;EAEArK,cAAcA,CAACyP,UAAe;IAC5B,MAAMU,IAAI,GAAGV,UAAU,CAACzO,cAAc,IAAI,SAAS;IAEnD,QAAQmP,IAAI;MACV,KAAK,SAAS;QACZ,OAAOV,UAAU,CAACxF,OAAO,EAAEtE,MAAM,GAAG,CAAC,CAAC,EAAEC,GAAG,IAAI,wCAAwC;MACzF,KAAK,UAAU;QACb,OAAO6J,UAAU,CAACtF,QAAQ,EAAErI,KAAK,IAAI,yCAAyC;MAChF,KAAK,QAAQ;QACX,OAAO2N,UAAU,CAACnF,MAAM,EAAE3F,MAAM,IAAI,uCAAuC;MAC7E,KAAK,OAAO;QACV,OAAO8K,UAAU,CAACvJ,KAAK,EAAEkK,IAAI,IAAI,sCAAsC;MACzE;QACE,OAAOX,UAAU,CAACxF,OAAO,EAAEtE,MAAM,GAAG,CAAC,CAAC,EAAEC,GAAG,IAAI,wCAAwC;;EAE7F;;;uBApxBWsC,aAAa,EAAA7I,EAAA,CAAAgR,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAlR,EAAA,CAAAgR,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAApR,EAAA,CAAAgR,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAtR,EAAA,CAAAgR,iBAAA,CAAAO,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAAb3I,aAAa;MAAA4I,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3R,EAAA,CAAA4R,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjB1BlS,EAAA,CAAAC,cAAA,aAAsC;UAQpCD,EANA,CAAAwB,UAAA,IAAA4Q,4BAAA,iBAAiD,IAAAC,4BAAA,kBAMA;UA4QjDrS,EAAA,CAAAC,cAAA,2BAIkD;UAAhDD,EADA,CAAAgB,UAAA,mBAAAsR,0DAAA;YAAA,OAASH,GAAA,CAAAhC,mBAAA,EAAqB;UAAA,EAAC,8BAAAoC,qEAAAxN,MAAA;YAAA,OACXoN,GAAA,CAAAvC,kBAAA,CAAA7K,MAAA,CAA0B;UAAA,EAAC;UAEnD/E,EADE,CAAAI,YAAA,EAAoB,EAChB;;;UAxREJ,EAAA,CAAAK,SAAA,EAAe;UAAfL,EAAA,CAAAS,UAAA,SAAA0R,GAAA,CAAA9I,SAAA,CAAe;UAMfrJ,EAAA,CAAAK,SAAA,EAAgB;UAAhBL,EAAA,CAAAS,UAAA,UAAA0R,GAAA,CAAA9I,SAAA,CAAgB;UA6QpBrJ,EAAA,CAAAK,SAAA,EAA8B;UAC9BL,EADA,CAAAS,UAAA,cAAA0R,GAAA,CAAA5I,gBAAA,CAA8B,gBAAA4I,GAAA,CAAA3I,gBAAA,CACE;;;qBDzQxB7J,YAAY,EAAA6S,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE9S,WAAW,EAAA+S,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAEjT,YAAY,EAAAoR,EAAA,CAAA8B,UAAA,EAAEjT,uBAAuB,EAAEC,qBAAqB;MAAAiT,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}