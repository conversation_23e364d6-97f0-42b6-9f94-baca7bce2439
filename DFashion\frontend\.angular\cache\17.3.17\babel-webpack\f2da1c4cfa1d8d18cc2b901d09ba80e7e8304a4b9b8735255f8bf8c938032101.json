{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"../services/order.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/select\";\nimport * as i11 from \"@angular/material/core\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nfunction OrderDetailsComponent_p_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Tracking:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.order.trackingNumber, \"\");\n  }\n}\nfunction OrderDetailsComponent_p_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.order.shippingAddress == null ? null : ctx_r0.order.shippingAddress.addressLine2);\n  }\n}\nfunction OrderDetailsComponent_div_85_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Size: \", item_r2.size, \"\");\n  }\n}\nfunction OrderDetailsComponent_div_85_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Color: \", item_r2.color, \"\");\n  }\n}\nfunction OrderDetailsComponent_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵelement(2, \"img\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 21)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 22);\n    i0.ɵɵtemplate(7, OrderDetailsComponent_div_85_span_7_Template, 2, 1, \"span\", 7)(8, OrderDetailsComponent_div_85_span_8_Template, 2, 1, \"span\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 23);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 24);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r0.getItemImage(item_r2), i0.ɵɵsanitizeUrl)(\"alt\", item_r2.product == null ? null : item_r2.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r2.product == null ? null : item_r2.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r2.size);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\\u20B9\", i0.ɵɵpipeBind2(11, 8, item_r2.price, \"1.2-2\"), \" \\u00D7 \", item_r2.quantity, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(14, 11, item_r2.price * item_r2.quantity, \"1.2-2\"), \" \");\n  }\n}\nfunction OrderDetailsComponent_div_86_mat_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r4.label, \" \");\n  }\n}\nfunction OrderDetailsComponent_div_86_mat_form_field_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-form-field\", 26)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Tracking Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function OrderDetailsComponent_div_86_mat_form_field_6_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.trackingNumber, $event) || (ctx_r0.trackingNumber = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.trackingNumber);\n  }\n}\nfunction OrderDetailsComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"mat-form-field\", 26)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Update Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-select\", 27);\n    i0.ɵɵtwoWayListener(\"valueChange\", function OrderDetailsComponent_div_86_Template_mat_select_valueChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectedStatus, $event) || (ctx_r0.selectedStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(5, OrderDetailsComponent_div_86_mat_option_5_Template, 2, 2, \"mat-option\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, OrderDetailsComponent_div_86_mat_form_field_6_Template, 4, 1, \"mat-form-field\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"value\", ctx_r0.selectedStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.availableStatuses);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedStatus === \"shipped\");\n  }\n}\nfunction OrderDetailsComponent_button_90_mat_spinner_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 34);\n  }\n}\nfunction OrderDetailsComponent_button_90_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function OrderDetailsComponent_button_90_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.updateOrderStatus());\n    });\n    i0.ɵɵtemplate(1, OrderDetailsComponent_button_90_mat_spinner_1_Template, 1, 0, \"mat-spinner\", 33);\n    i0.ɵɵtext(2, \" Update Order \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLoading);\n  }\n}\nexport class OrderDetailsComponent {\n  constructor(dialogRef, order, orderService, snackBar) {\n    this.dialogRef = dialogRef;\n    this.order = order;\n    this.orderService = orderService;\n    this.snackBar = snackBar;\n    this.selectedStatus = '';\n    this.trackingNumber = '';\n    this.isLoading = false;\n    this.availableStatuses = [{\n      value: 'pending',\n      label: 'Pending'\n    }, {\n      value: 'confirmed',\n      label: 'Confirmed'\n    }, {\n      value: 'shipped',\n      label: 'Shipped'\n    }, {\n      value: 'delivered',\n      label: 'Delivered'\n    }, {\n      value: 'cancelled',\n      label: 'Cancelled'\n    }];\n  }\n  ngOnInit() {\n    this.selectedStatus = this.order.status;\n    this.trackingNumber = this.order.trackingNumber || '';\n  }\n  onCancel() {\n    this.dialogRef.close();\n  }\n  canUpdateOrder() {\n    return this.order.status !== 'delivered' && this.order.status !== 'cancelled';\n  }\n  updateOrderStatus() {\n    if (this.selectedStatus === this.order.status && this.trackingNumber === this.order.trackingNumber) {\n      this.onCancel();\n      return;\n    }\n    this.isLoading = true;\n    // Simulate API call\n    setTimeout(() => {\n      this.isLoading = false;\n      this.snackBar.open('Order status updated successfully', 'Close', {\n        duration: 3000\n      });\n      this.dialogRef.close({\n        status: this.selectedStatus,\n        trackingNumber: this.trackingNumber\n      });\n    }, 1000);\n  }\n  formatDate(dateString) {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-IN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  getStatusColor(status) {\n    const statusColors = {\n      'pending': '#ff9800',\n      'confirmed': '#2196f3',\n      'shipped': '#9c27b0',\n      'delivered': '#4caf50',\n      'cancelled': '#f44336'\n    };\n    return statusColors[status] || '#666666';\n  }\n  getPaymentStatusColor(status) {\n    const statusColors = {\n      'pending': '#ff9800',\n      'paid': '#4caf50',\n      'failed': '#f44336',\n      'refunded': '#9e9e9e'\n    };\n    return statusColors[status] || '#666666';\n  }\n  getItemImage(item) {\n    return item.product?.images?.[0]?.url || '/assets/images/placeholder-product.jpg';\n  }\n  static {\n    this.ɵfac = function OrderDetailsComponent_Factory(t) {\n      return new (t || OrderDetailsComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.OrderService), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OrderDetailsComponent,\n      selectors: [[\"app-order-details\"]],\n      decls: 91,\n      vars: 35,\n      consts: [[\"mat-dialog-title\", \"\"], [1, \"order-details-content\"], [1, \"order-info-grid\"], [1, \"info-card\"], [1, \"status-info\"], [1, \"status-badge\"], [1, \"status-details\"], [4, \"ngIf\"], [1, \"customer-info\"], [1, \"address-info\"], [1, \"payment-info\"], [1, \"items-card\"], [1, \"items-list\"], [\"class\", \"item-row\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"order-actions\", 4, \"ngIf\"], [\"align\", \"end\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"item-row\"], [1, \"item-image\"], [3, \"src\", \"alt\"], [1, \"item-details\"], [1, \"item-specs\"], [1, \"item-price\"], [1, \"item-total\"], [1, \"order-actions\"], [\"appearance\", \"outline\"], [3, \"valueChange\", \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"appearance\", \"outline\", 4, \"ngIf\"], [3, \"value\"], [\"matInput\", \"\", \"placeholder\", \"Enter tracking number\", 3, \"ngModelChange\", \"ngModel\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [\"diameter\", \"20\", \"class\", \"action-spinner\", 4, \"ngIf\"], [\"diameter\", \"20\", 1, \"action-spinner\"]],\n      template: function OrderDetailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"h2\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"mat-dialog-content\", 1)(3, \"div\", 2)(4, \"mat-card\", 3)(5, \"mat-card-header\")(6, \"mat-card-title\");\n          i0.ɵɵtext(7, \"Order Status\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 4)(10, \"span\", 5);\n          i0.ɵɵtext(11);\n          i0.ɵɵpipe(12, \"titlecase\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 6)(14, \"p\")(15, \"strong\");\n          i0.ɵɵtext(16, \"Order Date:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"p\")(19, \"strong\");\n          i0.ɵɵtext(20, \"Expected Delivery:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, OrderDetailsComponent_p_22_Template, 4, 1, \"p\", 7);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(23, \"mat-card\", 3)(24, \"mat-card-header\")(25, \"mat-card-title\");\n          i0.ɵɵtext(26, \"Customer Information\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"mat-card-content\")(28, \"div\", 8)(29, \"p\")(30, \"strong\");\n          i0.ɵɵtext(31, \"Name:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"p\")(34, \"strong\");\n          i0.ɵɵtext(35, \"Email:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"p\")(38, \"strong\");\n          i0.ɵɵtext(39, \"Phone:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(40);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"mat-card\", 3)(42, \"mat-card-header\")(43, \"mat-card-title\");\n          i0.ɵɵtext(44, \"Shipping Address\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"mat-card-content\")(46, \"div\", 9)(47, \"p\")(48, \"strong\");\n          i0.ɵɵtext(49);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"p\");\n          i0.ɵɵtext(51);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(52, OrderDetailsComponent_p_52_Template, 2, 1, \"p\", 7);\n          i0.ɵɵelementStart(53, \"p\");\n          i0.ɵɵtext(54);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"p\");\n          i0.ɵɵtext(56);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(57, \"mat-card\", 3)(58, \"mat-card-header\")(59, \"mat-card-title\");\n          i0.ɵɵtext(60, \"Payment Information\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"mat-card-content\")(62, \"div\", 10)(63, \"p\")(64, \"strong\");\n          i0.ɵɵtext(65, \"Method:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(66);\n          i0.ɵɵpipe(67, \"titlecase\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"p\")(69, \"strong\");\n          i0.ɵɵtext(70, \"Status:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"span\");\n          i0.ɵɵtext(72);\n          i0.ɵɵpipe(73, \"titlecase\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(74, \"p\")(75, \"strong\");\n          i0.ɵɵtext(76, \"Total Amount:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(77);\n          i0.ɵɵpipe(78, \"number\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(79, \"mat-card\", 11)(80, \"mat-card-header\")(81, \"mat-card-title\");\n          i0.ɵɵtext(82);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(83, \"mat-card-content\")(84, \"div\", 12);\n          i0.ɵɵtemplate(85, OrderDetailsComponent_div_85_Template, 15, 14, \"div\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(86, OrderDetailsComponent_div_86_Template, 7, 3, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"mat-dialog-actions\", 15)(88, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function OrderDetailsComponent_Template_button_click_88_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵtext(89, \"Close\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(90, OrderDetailsComponent_button_90_Template, 3, 2, \"button\", 17);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\"Order Details - \", ctx.order.orderNumber, \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵstyleProp(\"background-color\", ctx.getStatusColor(ctx.order.status));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 26, ctx.order.status), \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", ctx.formatDate(ctx.order.orderDate), \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.formatDate(ctx.order.expectedDelivery), \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.order.trackingNumber);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\" \", ctx.order.customer == null ? null : ctx.order.customer.fullName, \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.order.customer == null ? null : ctx.order.customer.email, \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.order.customer == null ? null : ctx.order.customer.phone) || \"N/A\", \"\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.order.shippingAddress == null ? null : ctx.order.shippingAddress.fullName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.order.shippingAddress == null ? null : ctx.order.shippingAddress.addressLine1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.order.shippingAddress == null ? null : ctx.order.shippingAddress.addressLine2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate3(\"\", ctx.order.shippingAddress == null ? null : ctx.order.shippingAddress.city, \", \", ctx.order.shippingAddress == null ? null : ctx.order.shippingAddress.state, \" \", ctx.order.shippingAddress == null ? null : ctx.order.shippingAddress.pincode, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.order.shippingAddress == null ? null : ctx.order.shippingAddress.country);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(67, 28, ctx.order.paymentMethod), \"\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleProp(\"color\", ctx.getPaymentStatusColor(ctx.order.paymentStatus));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(73, 30, ctx.order.paymentStatus), \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(78, 32, ctx.order.totalAmount, \"1.2-2\"), \"\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"Order Items (\", (ctx.order.items == null ? null : ctx.order.items.length) || 0, \")\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.order.items);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.canUpdateOrder());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.canUpdateOrder());\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.MatButton, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatInput, i9.MatFormField, i9.MatLabel, i10.MatSelect, i11.MatOption, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i12.MatProgressSpinner, i4.DecimalPipe, i4.TitleCasePipe],\n      styles: [\".order-details-content[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  max-height: 80vh;\\n  overflow-y: auto;\\n}\\n\\n.order-info-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.info-card[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n.info-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n}\\n.info-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  padding-top: 1rem;\\n}\\n\\n.status-info[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  color: white;\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n  margin-bottom: 1rem;\\n}\\n.status-info[_ngcontent-%COMP%]   .status-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.5rem 0;\\n  font-size: 0.875rem;\\n}\\n\\n.customer-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .address-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .payment-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.5rem 0;\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n}\\n\\n.items-card[_ngcontent-%COMP%] {\\n  margin: 0 0 1.5rem 0;\\n}\\n\\n.items-list[_ngcontent-%COMP%]   .item-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 1rem 0;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.items-list[_ngcontent-%COMP%]   .item-row[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.items-list[_ngcontent-%COMP%]   .item-row[_ngcontent-%COMP%]   .item-image[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  margin-right: 1rem;\\n  flex-shrink: 0;\\n}\\n.items-list[_ngcontent-%COMP%]   .item-row[_ngcontent-%COMP%]   .item-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  border-radius: 4px;\\n  border: 1px solid #e0e0e0;\\n}\\n.items-list[_ngcontent-%COMP%]   .item-row[_ngcontent-%COMP%]   .item-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.items-list[_ngcontent-%COMP%]   .item-row[_ngcontent-%COMP%]   .item-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 0.25rem 0;\\n  font-size: 1rem;\\n  font-weight: 500;\\n}\\n.items-list[_ngcontent-%COMP%]   .item-row[_ngcontent-%COMP%]   .item-details[_ngcontent-%COMP%]   .item-specs[_ngcontent-%COMP%] {\\n  margin: 0.25rem 0;\\n  font-size: 0.75rem;\\n  color: #666;\\n}\\n.items-list[_ngcontent-%COMP%]   .item-row[_ngcontent-%COMP%]   .item-details[_ngcontent-%COMP%]   .item-specs[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n}\\n.items-list[_ngcontent-%COMP%]   .item-row[_ngcontent-%COMP%]   .item-details[_ngcontent-%COMP%]   .item-price[_ngcontent-%COMP%] {\\n  margin: 0.25rem 0 0 0;\\n  font-size: 0.875rem;\\n  color: #666;\\n}\\n.items-list[_ngcontent-%COMP%]   .item-row[_ngcontent-%COMP%]   .item-total[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 1rem;\\n  color: #2e7d32;\\n}\\n\\n.order-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 1rem;\\n  flex-wrap: wrap;\\n}\\n.order-actions[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n}\\n\\n.action-spinner[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .order-info-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .item-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.5rem;\\n  }\\n  .item-row[_ngcontent-%COMP%]   .item-image[_ngcontent-%COMP%] {\\n    margin-right: 0;\\n  }\\n  .item-row[_ngcontent-%COMP%]   .item-total[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n  .order-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .order-actions[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n    min-width: auto;\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "order", "trackingNumber", "ɵɵtextInterpolate", "shippingAddress", "addressLine2", "item_r2", "size", "color", "ɵɵelement", "ɵɵtemplate", "OrderDetailsComponent_div_85_span_7_Template", "OrderDetailsComponent_div_85_span_8_Template", "ɵɵproperty", "getItemImage", "ɵɵsanitizeUrl", "product", "name", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "price", "quantity", "status_r4", "value", "label", "ɵɵtwoWayListener", "OrderDetailsComponent_div_86_mat_form_field_6_Template_input_ngModelChange_3_listener", "$event", "ɵɵrestoreView", "_r5", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "ɵɵresetView", "ɵɵtwoWayProperty", "OrderDetailsComponent_div_86_Template_mat_select_valueChange_4_listener", "_r3", "selectedStatus", "OrderDetailsComponent_div_86_mat_option_5_Template", "OrderDetailsComponent_div_86_mat_form_field_6_Template", "availableStatuses", "ɵɵlistener", "OrderDetailsComponent_button_90_Template_button_click_0_listener", "_r6", "updateOrderStatus", "OrderDetailsComponent_button_90_mat_spinner_1_Template", "isLoading", "OrderDetailsComponent", "constructor", "dialogRef", "orderService", "snackBar", "ngOnInit", "status", "onCancel", "close", "canUpdateOrder", "setTimeout", "open", "duration", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "getStatusColor", "statusColors", "getPaymentStatusColor", "item", "images", "url", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "OrderService", "i3", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "OrderDetailsComponent_Template", "rf", "ctx", "OrderDetailsComponent_p_22_Template", "OrderDetailsComponent_p_52_Template", "OrderDetailsComponent_div_85_Template", "OrderDetailsComponent_div_86_Template", "OrderDetailsComponent_Template_button_click_88_listener", "OrderDetailsComponent_button_90_Template", "orderNumber", "ɵɵstyleProp", "ɵɵpipeBind1", "orderDate", "expectedDelivery", "customer", "fullName", "email", "phone", "addressLine1", "ɵɵtextInterpolate3", "city", "state", "pincode", "country", "paymentMethod", "paymentStatus", "totalAmount", "items", "length"], "sources": ["E:\\Fahion\\DFashion\\frontend\\src\\app\\admin\\orders\\order-details.component.ts"], "sourcesContent": ["import { Component, Inject, OnInit } from '@angular/core';\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { OrderService } from '../services/order.service';\n\n@Component({\n  selector: 'app-order-details',\n  template: `\n    <h2 mat-dialog-title>Order Details - {{ order.orderNumber }}</h2>\n    \n    <mat-dialog-content class=\"order-details-content\">\n      <div class=\"order-info-grid\">\n        <!-- Order Status -->\n        <mat-card class=\"info-card\">\n          <mat-card-header>\n            <mat-card-title>Order Status</mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"status-info\">\n              <span class=\"status-badge\" [style.background-color]=\"getStatusColor(order.status)\">\n                {{ order.status | titlecase }}\n              </span>\n              <div class=\"status-details\">\n                <p><strong>Order Date:</strong> {{ formatDate(order.orderDate) }}</p>\n                <p><strong>Expected Delivery:</strong> {{ formatDate(order.expectedDelivery) }}</p>\n                <p *ngIf=\"order.trackingNumber\"><strong>Tracking:</strong> {{ order.trackingNumber }}</p>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Customer Information -->\n        <mat-card class=\"info-card\">\n          <mat-card-header>\n            <mat-card-title>Customer Information</mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"customer-info\">\n              <p><strong>Name:</strong> {{ order.customer?.fullName }}</p>\n              <p><strong>Email:</strong> {{ order.customer?.email }}</p>\n              <p><strong>Phone:</strong> {{ order.customer?.phone || 'N/A' }}</p>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Shipping Address -->\n        <mat-card class=\"info-card\">\n          <mat-card-header>\n            <mat-card-title>Shipping Address</mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"address-info\">\n              <p><strong>{{ order.shippingAddress?.fullName }}</strong></p>\n              <p>{{ order.shippingAddress?.addressLine1 }}</p>\n              <p *ngIf=\"order.shippingAddress?.addressLine2\">{{ order.shippingAddress?.addressLine2 }}</p>\n              <p>{{ order.shippingAddress?.city }}, {{ order.shippingAddress?.state }} {{ order.shippingAddress?.pincode }}</p>\n              <p>{{ order.shippingAddress?.country }}</p>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- Payment Information -->\n        <mat-card class=\"info-card\">\n          <mat-card-header>\n            <mat-card-title>Payment Information</mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"payment-info\">\n              <p><strong>Method:</strong> {{ order.paymentMethod | titlecase }}</p>\n              <p><strong>Status:</strong> \n                <span [style.color]=\"getPaymentStatusColor(order.paymentStatus)\">\n                  {{ order.paymentStatus | titlecase }}\n                </span>\n              </p>\n              <p><strong>Total Amount:</strong> ₹{{ order.totalAmount | number:'1.2-2' }}</p>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <!-- Order Items -->\n      <mat-card class=\"items-card\">\n        <mat-card-header>\n          <mat-card-title>Order Items ({{ order.items?.length || 0 }})</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"items-list\">\n            <div *ngFor=\"let item of order.items\" class=\"item-row\">\n              <div class=\"item-image\">\n                <img [src]=\"getItemImage(item)\" [alt]=\"item.product?.name\" />\n              </div>\n              <div class=\"item-details\">\n                <h4>{{ item.product?.name }}</h4>\n                <p class=\"item-specs\">\n                  <span *ngIf=\"item.size\">Size: {{ item.size }}</span>\n                  <span *ngIf=\"item.color\">Color: {{ item.color }}</span>\n                </p>\n                <p class=\"item-price\">₹{{ item.price | number:'1.2-2' }} × {{ item.quantity }}</p>\n              </div>\n              <div class=\"item-total\">\n                ₹{{ (item.price * item.quantity) | number:'1.2-2' }}\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Order Actions -->\n      <div class=\"order-actions\" *ngIf=\"canUpdateOrder()\">\n        <mat-form-field appearance=\"outline\">\n          <mat-label>Update Status</mat-label>\n          <mat-select [(value)]=\"selectedStatus\">\n            <mat-option *ngFor=\"let status of availableStatuses\" [value]=\"status.value\">\n              {{ status.label }}\n            </mat-option>\n          </mat-select>\n        </mat-form-field>\n\n        <mat-form-field appearance=\"outline\" *ngIf=\"selectedStatus === 'shipped'\">\n          <mat-label>Tracking Number</mat-label>\n          <input matInput [(ngModel)]=\"trackingNumber\" placeholder=\"Enter tracking number\">\n        </mat-form-field>\n      </div>\n    </mat-dialog-content>\n    \n    <mat-dialog-actions align=\"end\">\n      <button mat-button (click)=\"onCancel()\">Close</button>\n      <button mat-raised-button color=\"primary\" \n              *ngIf=\"canUpdateOrder()\"\n              [disabled]=\"isLoading\"\n              (click)=\"updateOrderStatus()\">\n        <mat-spinner *ngIf=\"isLoading\" diameter=\"20\" class=\"action-spinner\"></mat-spinner>\n        Update Order\n      </button>\n    </mat-dialog-actions>\n  `,\n  styleUrls: ['./order-details.component.scss']\n})\nexport class OrderDetailsComponent implements OnInit {\n  selectedStatus: string = '';\n  trackingNumber: string = '';\n  isLoading: boolean = false;\n\n  availableStatuses = [\n    { value: 'pending', label: 'Pending' },\n    { value: 'confirmed', label: 'Confirmed' },\n    { value: 'shipped', label: 'Shipped' },\n    { value: 'delivered', label: 'Delivered' },\n    { value: 'cancelled', label: 'Cancelled' }\n  ];\n\n  constructor(\n    public dialogRef: MatDialogRef<OrderDetailsComponent>,\n    @Inject(MAT_DIALOG_DATA) public order: any,\n    private orderService: OrderService,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.selectedStatus = this.order.status;\n    this.trackingNumber = this.order.trackingNumber || '';\n  }\n\n  onCancel(): void {\n    this.dialogRef.close();\n  }\n\n  canUpdateOrder(): boolean {\n    return this.order.status !== 'delivered' && this.order.status !== 'cancelled';\n  }\n\n  updateOrderStatus(): void {\n    if (this.selectedStatus === this.order.status && \n        this.trackingNumber === this.order.trackingNumber) {\n      this.onCancel();\n      return;\n    }\n\n    this.isLoading = true;\n    \n    // Simulate API call\n    setTimeout(() => {\n      this.isLoading = false;\n      this.snackBar.open('Order status updated successfully', 'Close', {\n        duration: 3000\n      });\n      this.dialogRef.close({\n        status: this.selectedStatus,\n        trackingNumber: this.trackingNumber\n      });\n    }, 1000);\n  }\n\n  formatDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-IN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  getStatusColor(status: string): string {\n    const statusColors: { [key: string]: string } = {\n      'pending': '#ff9800',\n      'confirmed': '#2196f3',\n      'shipped': '#9c27b0',\n      'delivered': '#4caf50',\n      'cancelled': '#f44336'\n    };\n    return statusColors[status] || '#666666';\n  }\n\n  getPaymentStatusColor(status: string): string {\n    const statusColors: { [key: string]: string } = {\n      'pending': '#ff9800',\n      'paid': '#4caf50',\n      'failed': '#f44336',\n      'refunded': '#9e9e9e'\n    };\n    return statusColors[status] || '#666666';\n  }\n\n  getItemImage(item: any): string {\n    return item.product?.images?.[0]?.url || '/assets/images/placeholder-product.jpg';\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAsB,0BAA0B;;;;;;;;;;;;;;;;IAwBxBC,EAAhC,CAAAC,cAAA,QAAgC,aAAQ;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAA9BH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,KAAA,CAAAC,cAAA,KAA0B;;;;;IA6BvFR,EAAA,CAAAC,cAAA,QAA+C;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAA7CH,EAAA,CAAAI,SAAA,EAAyC;IAAzCJ,EAAA,CAAAS,iBAAA,CAAAH,MAAA,CAAAC,KAAA,CAAAG,eAAA,kBAAAJ,MAAA,CAAAC,KAAA,CAAAG,eAAA,CAAAC,YAAA,CAAyC;;;;;IAwCpFX,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA5BH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,kBAAA,WAAAO,OAAA,CAAAC,IAAA,KAAqB;;;;;IAC7Cb,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA9BH,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAK,kBAAA,YAAAO,OAAA,CAAAE,KAAA,KAAuB;;;;;IAPpDd,EADF,CAAAC,cAAA,cAAuD,cAC7B;IACtBD,EAAA,CAAAe,SAAA,cAA6D;IAC/Df,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,YAAsB;IAEpBD,EADA,CAAAgB,UAAA,IAAAC,4CAAA,kBAAwB,IAAAC,4CAAA,kBACC;IAC3BlB,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAwD;;IAChFF,EADgF,CAAAG,YAAA,EAAI,EAC9E;IACNH,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAE,MAAA,IACF;;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAbGH,EAAA,CAAAI,SAAA,GAA0B;IAACJ,EAA3B,CAAAmB,UAAA,QAAAb,MAAA,CAAAc,YAAA,CAAAR,OAAA,GAAAZ,EAAA,CAAAqB,aAAA,CAA0B,QAAAT,OAAA,CAAAU,OAAA,kBAAAV,OAAA,CAAAU,OAAA,CAAAC,IAAA,CAA2B;IAGtDvB,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAS,iBAAA,CAAAG,OAAA,CAAAU,OAAA,kBAAAV,OAAA,CAAAU,OAAA,CAAAC,IAAA,CAAwB;IAEnBvB,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAmB,UAAA,SAAAP,OAAA,CAAAC,IAAA,CAAe;IACfb,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAmB,UAAA,SAAAP,OAAA,CAAAE,KAAA,CAAgB;IAEHd,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAwB,kBAAA,WAAAxB,EAAA,CAAAyB,WAAA,QAAAb,OAAA,CAAAc,KAAA,wBAAAd,OAAA,CAAAe,QAAA,KAAwD;IAG9E3B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,YAAAL,EAAA,CAAAyB,WAAA,SAAAb,OAAA,CAAAc,KAAA,GAAAd,OAAA,CAAAe,QAAA,gBACF;;;;;IAWF3B,EAAA,CAAAC,cAAA,qBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFwCH,EAAA,CAAAmB,UAAA,UAAAS,SAAA,CAAAC,KAAA,CAAsB;IACzE7B,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAuB,SAAA,CAAAE,KAAA,MACF;;;;;;IAKF9B,EADF,CAAAC,cAAA,yBAA0E,gBAC7D;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACtCH,EAAA,CAAAC,cAAA,gBAAiF;IAAjED,EAAA,CAAA+B,gBAAA,2BAAAC,sFAAAC,MAAA;MAAAjC,EAAA,CAAAkC,aAAA,CAAAC,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAoC,aAAA;MAAApC,EAAA,CAAAqC,kBAAA,CAAA/B,MAAA,CAAAE,cAAA,EAAAyB,MAAA,MAAA3B,MAAA,CAAAE,cAAA,GAAAyB,MAAA;MAAA,OAAAjC,EAAA,CAAAsC,WAAA,CAAAL,MAAA;IAAA,EAA4B;IAC9CjC,EADE,CAAAG,YAAA,EAAiF,EAClE;;;;IADCH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAuC,gBAAA,YAAAjC,MAAA,CAAAE,cAAA,CAA4B;;;;;;IAV5CR,EAFJ,CAAAC,cAAA,cAAoD,yBACb,gBACxB;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAC,cAAA,qBAAuC;IAA3BD,EAAA,CAAA+B,gBAAA,yBAAAS,wEAAAP,MAAA;MAAAjC,EAAA,CAAAkC,aAAA,CAAAO,GAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAoC,aAAA;MAAApC,EAAA,CAAAqC,kBAAA,CAAA/B,MAAA,CAAAoC,cAAA,EAAAT,MAAA,MAAA3B,MAAA,CAAAoC,cAAA,GAAAT,MAAA;MAAA,OAAAjC,EAAA,CAAAsC,WAAA,CAAAL,MAAA;IAAA,EAA0B;IACpCjC,EAAA,CAAAgB,UAAA,IAAA2B,kDAAA,yBAA4E;IAIhF3C,EADE,CAAAG,YAAA,EAAa,EACE;IAEjBH,EAAA,CAAAgB,UAAA,IAAA4B,sDAAA,6BAA0E;IAI5E5C,EAAA,CAAAG,YAAA,EAAM;;;;IAXUH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAuC,gBAAA,UAAAjC,MAAA,CAAAoC,cAAA,CAA0B;IACL1C,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAmB,UAAA,YAAAb,MAAA,CAAAuC,iBAAA,CAAoB;IAMjB7C,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAmB,UAAA,SAAAb,MAAA,CAAAoC,cAAA,eAAkC;;;;;IAaxE1C,EAAA,CAAAe,SAAA,sBAAkF;;;;;;IAJpFf,EAAA,CAAAC,cAAA,iBAGsC;IAA9BD,EAAA,CAAA8C,UAAA,mBAAAC,iEAAA;MAAA/C,EAAA,CAAAkC,aAAA,CAAAc,GAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAoC,aAAA;MAAA,OAAApC,EAAA,CAAAsC,WAAA,CAAShC,MAAA,CAAA2C,iBAAA,EAAmB;IAAA,EAAC;IACnCjD,EAAA,CAAAgB,UAAA,IAAAkC,sDAAA,0BAAoE;IACpElD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAJDH,EAAA,CAAAmB,UAAA,aAAAb,MAAA,CAAA6C,SAAA,CAAsB;IAEdnD,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAmB,UAAA,SAAAb,MAAA,CAAA6C,SAAA,CAAe;;;AAOrC,OAAM,MAAOC,qBAAqB;EAahCC,YACSC,SAA8C,EACrB/C,KAAU,EAClCgD,YAA0B,EAC1BC,QAAqB;IAHtB,KAAAF,SAAS,GAATA,SAAS;IACgB,KAAA/C,KAAK,GAALA,KAAK;IAC7B,KAAAgD,YAAY,GAAZA,YAAY;IACZ,KAAAC,QAAQ,GAARA,QAAQ;IAhBlB,KAAAd,cAAc,GAAW,EAAE;IAC3B,KAAAlC,cAAc,GAAW,EAAE;IAC3B,KAAA2C,SAAS,GAAY,KAAK;IAE1B,KAAAN,iBAAiB,GAAG,CAClB;MAAEhB,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAS,CAAE,EACtC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAS,CAAE,EACtC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,CAC3C;EAOE;EAEH2B,QAAQA,CAAA;IACN,IAAI,CAACf,cAAc,GAAG,IAAI,CAACnC,KAAK,CAACmD,MAAM;IACvC,IAAI,CAAClD,cAAc,GAAG,IAAI,CAACD,KAAK,CAACC,cAAc,IAAI,EAAE;EACvD;EAEAmD,QAAQA,CAAA;IACN,IAAI,CAACL,SAAS,CAACM,KAAK,EAAE;EACxB;EAEAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACtD,KAAK,CAACmD,MAAM,KAAK,WAAW,IAAI,IAAI,CAACnD,KAAK,CAACmD,MAAM,KAAK,WAAW;EAC/E;EAEAT,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACP,cAAc,KAAK,IAAI,CAACnC,KAAK,CAACmD,MAAM,IACzC,IAAI,CAAClD,cAAc,KAAK,IAAI,CAACD,KAAK,CAACC,cAAc,EAAE;MACrD,IAAI,CAACmD,QAAQ,EAAE;MACf;;IAGF,IAAI,CAACR,SAAS,GAAG,IAAI;IAErB;IACAW,UAAU,CAAC,MAAK;MACd,IAAI,CAACX,SAAS,GAAG,KAAK;MACtB,IAAI,CAACK,QAAQ,CAACO,IAAI,CAAC,mCAAmC,EAAE,OAAO,EAAE;QAC/DC,QAAQ,EAAE;OACX,CAAC;MACF,IAAI,CAACV,SAAS,CAACM,KAAK,CAAC;QACnBF,MAAM,EAAE,IAAI,CAAChB,cAAc;QAC3BlC,cAAc,EAAE,IAAI,CAACA;OACtB,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;EACV;EAEAyD,UAAUA,CAACC,UAAkB;IAC3B,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAC,cAAcA,CAAChB,MAAc;IAC3B,MAAMiB,YAAY,GAA8B;MAC9C,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,WAAW,EAAE;KACd;IACD,OAAOA,YAAY,CAACjB,MAAM,CAAC,IAAI,SAAS;EAC1C;EAEAkB,qBAAqBA,CAAClB,MAAc;IAClC,MAAMiB,YAAY,GAA8B;MAC9C,SAAS,EAAE,SAAS;MACpB,MAAM,EAAE,SAAS;MACjB,QAAQ,EAAE,SAAS;MACnB,UAAU,EAAE;KACb;IACD,OAAOA,YAAY,CAACjB,MAAM,CAAC,IAAI,SAAS;EAC1C;EAEAtC,YAAYA,CAACyD,IAAS;IACpB,OAAOA,IAAI,CAACvD,OAAO,EAAEwD,MAAM,GAAG,CAAC,CAAC,EAAEC,GAAG,IAAI,wCAAwC;EACnF;;;uBAzFW3B,qBAAqB,EAAApD,EAAA,CAAAgF,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAlF,EAAA,CAAAgF,iBAAA,CAetBjF,eAAe,GAAAC,EAAA,CAAAgF,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAApF,EAAA,CAAAgF,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAfdlC,qBAAqB;MAAAmC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlI9B7F,EAAA,CAAAC,cAAA,YAAqB;UAAAD,EAAA,CAAAE,MAAA,GAAuC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAOzDH,EALR,CAAAC,cAAA,4BAAkD,aACnB,kBAEC,sBACT,qBACC;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAC9BF,EAD8B,CAAAG,YAAA,EAAiB,EAC7B;UAGdH,EAFJ,CAAAC,cAAA,uBAAkB,aACS,eAC4D;UACjFD,EAAA,CAAAE,MAAA,IACF;;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEFH,EADL,CAAAC,cAAA,cAA4B,SACvB,cAAQ;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAE,MAAA,IAAiC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAClEH,EAAH,CAAAC,cAAA,SAAG,cAAQ;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAE,MAAA,IAAwC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnFH,EAAA,CAAAgB,UAAA,KAAA+E,mCAAA,eAAgC;UAIxC/F,EAHM,CAAAG,YAAA,EAAM,EACF,EACW,EACV;UAKPH,EAFJ,CAAAC,cAAA,mBAA4B,uBACT,sBACC;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UACtCF,EADsC,CAAAG,YAAA,EAAiB,EACrC;UAGXH,EAFP,CAAAC,cAAA,wBAAkB,cACW,SACtB,cAAQ;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAE,MAAA,IAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACzDH,EAAH,CAAAC,cAAA,SAAG,cAAQ;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAE,MAAA,IAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvDH,EAAH,CAAAC,cAAA,SAAG,cAAQ;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAE,MAAA,IAAoC;UAGrEF,EAHqE,CAAAG,YAAA,EAAI,EAC/D,EACW,EACV;UAKPH,EAFJ,CAAAC,cAAA,mBAA4B,uBACT,sBACC;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAClCF,EADkC,CAAAG,YAAA,EAAiB,EACjC;UAGXH,EAFP,CAAAC,cAAA,wBAAkB,cACU,SACrB,cAAQ;UAAAD,EAAA,CAAAE,MAAA,IAAqC;UAASF,EAAT,CAAAG,YAAA,EAAS,EAAI;UAC7DH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,IAAyC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAChDH,EAAA,CAAAgB,UAAA,KAAAgF,mCAAA,eAA+C;UAC/ChG,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,IAA0G;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACjHH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,IAAoC;UAG7CF,EAH6C,CAAAG,YAAA,EAAI,EACvC,EACW,EACV;UAKPH,EAFJ,CAAAC,cAAA,mBAA4B,uBACT,sBACC;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UACrCF,EADqC,CAAAG,YAAA,EAAiB,EACpC;UAGXH,EAFP,CAAAC,cAAA,wBAAkB,eACU,SACrB,cAAQ;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAE,MAAA,IAAqC;;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAClEH,EAAH,CAAAC,cAAA,SAAG,cAAQ;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzBH,EAAA,CAAAC,cAAA,YAAiE;UAC/DD,EAAA,CAAAE,MAAA,IACF;;UACFF,EADE,CAAAG,YAAA,EAAO,EACL;UACDH,EAAH,CAAAC,cAAA,SAAG,cAAQ;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAE,MAAA,IAAyC;;UAInFF,EAJmF,CAAAG,YAAA,EAAI,EAC3E,EACW,EACV,EACP;UAKFH,EAFJ,CAAAC,cAAA,oBAA6B,uBACV,sBACC;UAAAD,EAAA,CAAAE,MAAA,IAA4C;UAC9DF,EAD8D,CAAAG,YAAA,EAAiB,EAC7D;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,eACQ;UACtBD,EAAA,CAAAgB,UAAA,KAAAiF,qCAAA,oBAAuD;UAkB7DjG,EAFI,CAAAG,YAAA,EAAM,EACW,EACV;UAGXH,EAAA,CAAAgB,UAAA,KAAAkF,qCAAA,kBAAoD;UAetDlG,EAAA,CAAAG,YAAA,EAAqB;UAGnBH,EADF,CAAAC,cAAA,8BAAgC,kBACU;UAArBD,EAAA,CAAA8C,UAAA,mBAAAqD,wDAAA;YAAA,OAASL,GAAA,CAAAnC,QAAA,EAAU;UAAA,EAAC;UAAC3D,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtDH,EAAA,CAAAgB,UAAA,KAAAoF,wCAAA,qBAGsC;UAIxCpG,EAAA,CAAAG,YAAA,EAAqB;;;UA9HAH,EAAA,CAAAI,SAAA,EAAuC;UAAvCJ,EAAA,CAAAK,kBAAA,qBAAAyF,GAAA,CAAAvF,KAAA,CAAA8F,WAAA,KAAuC;UAWvBrG,EAAA,CAAAI,SAAA,GAAuD;UAAvDJ,EAAA,CAAAsG,WAAA,qBAAAR,GAAA,CAAApB,cAAA,CAAAoB,GAAA,CAAAvF,KAAA,CAAAmD,MAAA,EAAuD;UAChF1D,EAAA,CAAAI,SAAA,EACF;UADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAuG,WAAA,SAAAT,GAAA,CAAAvF,KAAA,CAAAmD,MAAA,OACF;UAEkC1D,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAK,kBAAA,MAAAyF,GAAA,CAAA7B,UAAA,CAAA6B,GAAA,CAAAvF,KAAA,CAAAiG,SAAA,MAAiC;UAC1BxG,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAAK,kBAAA,MAAAyF,GAAA,CAAA7B,UAAA,CAAA6B,GAAA,CAAAvF,KAAA,CAAAkG,gBAAA,MAAwC;UAC3EzG,EAAA,CAAAI,SAAA,EAA0B;UAA1BJ,EAAA,CAAAmB,UAAA,SAAA2E,GAAA,CAAAvF,KAAA,CAAAC,cAAA,CAA0B;UAaNR,EAAA,CAAAI,SAAA,IAA8B;UAA9BJ,EAAA,CAAAK,kBAAA,MAAAyF,GAAA,CAAAvF,KAAA,CAAAmG,QAAA,kBAAAZ,GAAA,CAAAvF,KAAA,CAAAmG,QAAA,CAAAC,QAAA,KAA8B;UAC7B3G,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAK,kBAAA,MAAAyF,GAAA,CAAAvF,KAAA,CAAAmG,QAAA,kBAAAZ,GAAA,CAAAvF,KAAA,CAAAmG,QAAA,CAAAE,KAAA,KAA2B;UAC3B5G,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAK,kBAAA,OAAAyF,GAAA,CAAAvF,KAAA,CAAAmG,QAAA,kBAAAZ,GAAA,CAAAvF,KAAA,CAAAmG,QAAA,CAAAG,KAAA,eAAoC;UAYpD7G,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAS,iBAAA,CAAAqF,GAAA,CAAAvF,KAAA,CAAAG,eAAA,kBAAAoF,GAAA,CAAAvF,KAAA,CAAAG,eAAA,CAAAiG,QAAA,CAAqC;UAC7C3G,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAS,iBAAA,CAAAqF,GAAA,CAAAvF,KAAA,CAAAG,eAAA,kBAAAoF,GAAA,CAAAvF,KAAA,CAAAG,eAAA,CAAAoG,YAAA,CAAyC;UACxC9G,EAAA,CAAAI,SAAA,EAAyC;UAAzCJ,EAAA,CAAAmB,UAAA,SAAA2E,GAAA,CAAAvF,KAAA,CAAAG,eAAA,kBAAAoF,GAAA,CAAAvF,KAAA,CAAAG,eAAA,CAAAC,YAAA,CAAyC;UAC1CX,EAAA,CAAAI,SAAA,GAA0G;UAA1GJ,EAAA,CAAA+G,kBAAA,KAAAjB,GAAA,CAAAvF,KAAA,CAAAG,eAAA,kBAAAoF,GAAA,CAAAvF,KAAA,CAAAG,eAAA,CAAAsG,IAAA,QAAAlB,GAAA,CAAAvF,KAAA,CAAAG,eAAA,kBAAAoF,GAAA,CAAAvF,KAAA,CAAAG,eAAA,CAAAuG,KAAA,OAAAnB,GAAA,CAAAvF,KAAA,CAAAG,eAAA,kBAAAoF,GAAA,CAAAvF,KAAA,CAAAG,eAAA,CAAAwG,OAAA,KAA0G;UAC1GlH,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAS,iBAAA,CAAAqF,GAAA,CAAAvF,KAAA,CAAAG,eAAA,kBAAAoF,GAAA,CAAAvF,KAAA,CAAAG,eAAA,CAAAyG,OAAA,CAAoC;UAYXnH,EAAA,CAAAI,SAAA,IAAqC;UAArCJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAuG,WAAA,SAAAT,GAAA,CAAAvF,KAAA,CAAA6G,aAAA,MAAqC;UAEzDpH,EAAA,CAAAI,SAAA,GAA0D;UAA1DJ,EAAA,CAAAsG,WAAA,UAAAR,GAAA,CAAAlB,qBAAA,CAAAkB,GAAA,CAAAvF,KAAA,CAAA8G,aAAA,EAA0D;UAC9DrH,EAAA,CAAAI,SAAA,EACF;UADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAuG,WAAA,SAAAT,GAAA,CAAAvF,KAAA,CAAA8G,aAAA,OACF;UAEgCrH,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAK,kBAAA,YAAAL,EAAA,CAAAyB,WAAA,SAAAqE,GAAA,CAAAvF,KAAA,CAAA+G,WAAA,eAAyC;UAS/DtH,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAK,kBAAA,mBAAAyF,GAAA,CAAAvF,KAAA,CAAAgH,KAAA,kBAAAzB,GAAA,CAAAvF,KAAA,CAAAgH,KAAA,CAAAC,MAAA,YAA4C;UAIpCxH,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAmB,UAAA,YAAA2E,GAAA,CAAAvF,KAAA,CAAAgH,KAAA,CAAc;UAqBdvH,EAAA,CAAAI,SAAA,EAAsB;UAAtBJ,EAAA,CAAAmB,UAAA,SAAA2E,GAAA,CAAAjC,cAAA,GAAsB;UAoBzC7D,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAmB,UAAA,SAAA2E,GAAA,CAAAjC,cAAA,GAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}