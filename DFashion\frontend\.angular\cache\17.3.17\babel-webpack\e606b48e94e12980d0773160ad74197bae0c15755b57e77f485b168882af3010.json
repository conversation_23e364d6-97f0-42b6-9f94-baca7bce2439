{"ast": null, "code": "var now = require('performance-now'),\n  root = typeof window === 'undefined' ? global : window,\n  vendors = ['moz', 'webkit'],\n  suffix = 'AnimationFrame',\n  raf = root['request' + suffix],\n  caf = root['cancel' + suffix] || root['cancelRequest' + suffix];\nfor (var i = 0; !raf && i < vendors.length; i++) {\n  raf = root[vendors[i] + 'Request' + suffix];\n  caf = root[vendors[i] + 'Cancel' + suffix] || root[vendors[i] + 'CancelRequest' + suffix];\n}\n\n// Some versions of FF have rAF but not cAF\nif (!raf || !caf) {\n  var last = 0,\n    id = 0,\n    queue = [],\n    frameDuration = 1000 / 60;\n  raf = function (callback) {\n    if (queue.length === 0) {\n      var _now = now(),\n        next = Math.max(0, frameDuration - (_now - last));\n      last = next + _now;\n      setTimeout(function () {\n        var cp = queue.slice(0);\n        // Clear queue here to prevent\n        // callbacks from appending listeners\n        // to the current frame's queue\n        queue.length = 0;\n        for (var i = 0; i < cp.length; i++) {\n          if (!cp[i].cancelled) {\n            try {\n              cp[i].callback(last);\n            } catch (e) {\n              setTimeout(function () {\n                throw e;\n              }, 0);\n            }\n          }\n        }\n      }, Math.round(next));\n    }\n    queue.push({\n      handle: ++id,\n      callback: callback,\n      cancelled: false\n    });\n    return id;\n  };\n  caf = function (handle) {\n    for (var i = 0; i < queue.length; i++) {\n      if (queue[i].handle === handle) {\n        queue[i].cancelled = true;\n      }\n    }\n  };\n}\nmodule.exports = function (fn) {\n  // Wrap in a new function to prevent\n  // `cancel` potentially being assigned\n  // to the native rAF function\n  return raf.call(root, fn);\n};\nmodule.exports.cancel = function () {\n  caf.apply(root, arguments);\n};\nmodule.exports.polyfill = function (object) {\n  if (!object) {\n    object = root;\n  }\n  object.requestAnimationFrame = raf;\n  object.cancelAnimationFrame = caf;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}