{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction SocialFeedComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_8_Template_div_click_0_listener() {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openStoryViewer(i_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 13);\n    i0.ɵɵelement(2, \"img\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 6);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"viewed\", story_r4.viewed);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", story_r4.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", story_r4.user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r4.user.username);\n  }\n}\nfunction SocialFeedComponent_div_10_i_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 53);\n  }\n}\nfunction SocialFeedComponent_div_10_div_15_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 57);\n  }\n  if (rf & 2) {\n    const media_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", media_r7.url, i0.ɵɵsanitizeUrl)(\"alt\", media_r7.alt);\n  }\n}\nfunction SocialFeedComponent_div_10_div_15_video_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 58);\n  }\n  if (rf & 2) {\n    const media_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", media_r7.url, i0.ɵɵsanitizeUrl)(\"muted\", true);\n  }\n}\nfunction SocialFeedComponent_div_10_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtemplate(1, SocialFeedComponent_div_10_div_15_img_1_Template, 1, 2, \"img\", 55)(2, SocialFeedComponent_div_10_div_15_video_2_Template, 1, 2, \"video\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const media_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", media_r7.type === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", media_r7.type === \"video\");\n  }\n}\nfunction SocialFeedComponent_div_10_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_div_16_div_1_Template_div_click_0_listener() {\n      const productTag_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.showProductDetails(productTag_r9.product));\n    });\n    i0.ɵɵelementStart(1, \"div\", 62);\n    i0.ɵɵelement(2, \"i\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const productTag_r9 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", productTag_r9.position.x, \"%\")(\"top\", productTag_r9.position.y, \"%\");\n  }\n}\nfunction SocialFeedComponent_div_10_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, SocialFeedComponent_div_10_div_16_div_1_Template, 3, 4, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", post_r6.products);\n  }\n}\nfunction SocialFeedComponent_div_10_div_17_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 67);\n  }\n  if (rf & 2) {\n    const i_r10 = ctx.index;\n    i0.ɵɵclassProp(\"active\", i_r10 === 0);\n  }\n}\nfunction SocialFeedComponent_div_10_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65);\n    i0.ɵɵtemplate(2, SocialFeedComponent_div_10_div_17_span_2_Template, 1, 2, \"span\", 66);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", post_r6.media);\n  }\n}\nfunction SocialFeedComponent_div_10_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"strong\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(3, 1, post_r6.likes.length), \" likes\");\n  }\n}\nfunction SocialFeedComponent_div_10_div_36_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 71);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_div_36_span_1_Template_span_click_0_listener() {\n      const hashtag_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.searchHashtag(hashtag_r12));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hashtag_r12 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" #\", hashtag_r12, \" \");\n  }\n}\nfunction SocialFeedComponent_div_10_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtemplate(1, SocialFeedComponent_div_10_div_36_span_1_Template, 2, 1, \"span\", 70);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", post_r6.hashtags);\n  }\n}\nfunction SocialFeedComponent_div_10_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_div_37_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const post_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.buyNow(post_r6));\n    });\n    i0.ɵɵelement(2, \"i\", 74);\n    i0.ɵɵtext(3, \" Buy Now \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_div_37_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const post_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addToCart(post_r6));\n    });\n    i0.ɵɵelement(5, \"i\", 76);\n    i0.ɵɵtext(6, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_div_37_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const post_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addToWishlist(post_r6));\n    });\n    i0.ɵɵelement(8, \"i\", 34);\n    i0.ɵɵtext(9, \" Wishlist \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SocialFeedComponent_div_10_div_38_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_div_38_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const post_r6 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewAllComments(post_r6));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" View all \", post_r6.comments.length, \" comments \");\n  }\n}\nfunction SocialFeedComponent_div_10_div_38_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"span\", 83);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 84);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const comment_r15 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r15.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(comment_r15.text);\n  }\n}\nfunction SocialFeedComponent_div_10_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtemplate(1, SocialFeedComponent_div_10_div_38_div_1_Template, 2, 1, \"div\", 79)(2, SocialFeedComponent_div_10_div_38_div_2_Template, 5, 2, \"div\", 80);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r6.comments.length > 2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", post_r6.comments.slice(-2));\n  }\n}\nfunction SocialFeedComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\", 17)(3, \"img\", 18);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_img_click_3_listener() {\n      const post_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewProfile(post_r6.user._id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 19)(5, \"div\", 20)(6, \"span\", 21);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_span_click_6_listener() {\n      const post_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewProfile(post_r6.user._id));\n    });\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, SocialFeedComponent_div_10_i_8_Template, 1, 0, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 23);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_button_click_12_listener() {\n      const post_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showPostMenu(post_r6));\n    });\n    i0.ɵɵelement(13, \"i\", 26);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 27);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_div_click_14_listener() {\n      const post_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewPost(post_r6));\n    })(\"dblclick\", function SocialFeedComponent_div_10_Template_div_dblclick_14_listener() {\n      const post_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleLike(post_r6));\n    });\n    i0.ɵɵtemplate(15, SocialFeedComponent_div_10_div_15_Template, 3, 2, \"div\", 28)(16, SocialFeedComponent_div_10_div_16_Template, 2, 1, \"div\", 29)(17, SocialFeedComponent_div_10_div_17_Template, 3, 1, \"div\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 31)(19, \"div\", 32)(20, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_button_click_20_listener() {\n      const post_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleLike(post_r6));\n    });\n    i0.ɵɵelement(21, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_button_click_22_listener() {\n      const post_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.focusComment(post_r6._id));\n    });\n    i0.ɵɵelement(23, \"i\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_button_click_24_listener() {\n      const post_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.sharePost(post_r6));\n    });\n    i0.ɵɵelement(25, \"i\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 39)(27, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_button_click_27_listener() {\n      const post_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSave(post_r6));\n    });\n    i0.ɵɵelement(28, \"i\", 41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 42);\n    i0.ɵɵtemplate(30, SocialFeedComponent_div_10_div_30_Template, 4, 3, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_div_click_31_listener() {\n      const post_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewPost(post_r6));\n    });\n    i0.ɵɵelementStart(32, \"span\", 21);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_span_click_32_listener($event) {\n      const post_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.viewProfile(post_r6.user._id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"span\", 45);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(36, SocialFeedComponent_div_10_div_36_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(37, SocialFeedComponent_div_10_div_37_Template, 10, 0, \"div\", 47)(38, SocialFeedComponent_div_10_div_38_Template, 3, 2, \"div\", 48);\n    i0.ɵɵelementStart(39, \"div\", 49);\n    i0.ɵɵelement(40, \"img\", 50);\n    i0.ɵɵelementStart(41, \"input\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SocialFeedComponent_div_10_Template_input_ngModelChange_41_listener($event) {\n      const post_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.commentTexts[post_r6._id], $event) || (ctx_r2.commentTexts[post_r6._id] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function SocialFeedComponent_div_10_Template_input_keyup_enter_41_listener() {\n      const post_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addComment(post_r6));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_10_Template_button_click_42_listener() {\n      const post_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addComment(post_r6));\n    });\n    i0.ɵɵtext(43, \" Post \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const post_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", post_r6.user.avatar || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", post_r6.user.fullName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(post_r6.user.username);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r6.user.isVerified);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getTimeAgo(post_r6.createdAt));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", post_r6.media);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r6.products.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r6.media.length > 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"liked\", post_r6.isLiked);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"saved\", post_r6.isSaved);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", post_r6.likes.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(post_r6.user.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(post_r6.caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r6.hashtags.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r6.products.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r6.comments.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (ctx_r2.currentUser == null ? null : ctx_r2.currentUser.avatar) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r2.currentUser == null ? null : ctx_r2.currentUser.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"comment-\" + post_r6._id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.commentTexts[post_r6._id]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.commentTexts[post_r6._id] || !ctx_r2.commentTexts[post_r6._id].trim());\n  }\n}\nfunction SocialFeedComponent_div_11_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 88);\n  }\n}\nfunction SocialFeedComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_11_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.loadMorePosts());\n    });\n    i0.ɵɵtemplate(2, SocialFeedComponent_div_11_i_2_Template, 1, 0, \"i\", 87);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.loading ? \"Loading...\" : \"Load More Posts\", \" \");\n  }\n}\nfunction SocialFeedComponent_div_12_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, ctx_r2.selectedProduct.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction SocialFeedComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_12_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeProductModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 90);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_12_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 91)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_12_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeProductModal());\n    });\n    i0.ɵɵelement(6, \"i\", 93);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 94);\n    i0.ɵɵelement(8, \"img\", 95);\n    i0.ɵɵelementStart(9, \"div\", 96)(10, \"p\", 97);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 98)(13, \"span\", 99);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, SocialFeedComponent_div_12_span_16_Template, 3, 4, \"span\", 100);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 101)(18, \"button\", 102);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_12_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.buyProductNow());\n    });\n    i0.ɵɵtext(19, \"Buy Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_12_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addProductToCart());\n    });\n    i0.ɵɵtext(21, \"Add to Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function SocialFeedComponent_div_12_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addProductToWishlist());\n    });\n    i0.ɵɵtext(23, \"Add to Wishlist\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedProduct.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r2.selectedProduct.images[0] == null ? null : ctx_r2.selectedProduct.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r2.selectedProduct.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedProduct.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(15, 6, ctx_r2.selectedProduct.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedProduct.originalPrice);\n  }\n}\nexport let SocialFeedComponent = /*#__PURE__*/(() => {\n  class SocialFeedComponent {\n    constructor(router) {\n      this.router = router;\n      this.posts = [];\n      this.stories = [];\n      this.commentTexts = {};\n      this.selectedProduct = null;\n      this.currentUser = null;\n      this.loading = false;\n      this.hasMorePosts = true;\n    }\n    ngOnInit() {\n      this.loadCurrentUser();\n      this.loadStories();\n      this.loadPosts();\n    }\n    loadCurrentUser() {\n      // TODO: Get current user from auth service\n      this.currentUser = {\n        _id: 'current-user',\n        username: 'you',\n        fullName: 'Your Name',\n        avatar: ''\n      };\n    }\n    loadStories() {\n      // Load stories from real API\n      fetch('http://localhost:5000/api/stories').then(response => response.json()).then(data => {\n        if (data.success) {\n          // Group stories by user\n          const userStories = data.stories.reduce((acc, story) => {\n            const userId = story.user._id;\n            if (!acc[userId]) {\n              acc[userId] = {\n                user: story.user,\n                viewed: false,\n                stories: []\n              };\n            }\n            acc[userId].stories.push(story);\n            return acc;\n          }, {});\n          this.stories = Object.values(userStories);\n        }\n      }).catch(error => {\n        console.error('Error loading stories:', error);\n      });\n    }\n    loadPosts() {\n      this.loading = true;\n      // Load posts from real API\n      fetch('http://localhost:5000/api/posts').then(response => response.json()).then(data => {\n        if (data.success) {\n          this.posts = data.posts.map(post => ({\n            ...post,\n            isLiked: false,\n            isSaved: false // TODO: Check if current user saved this post\n          }));\n        }\n        this.loading = false;\n      }).catch(error => {\n        console.error('Error loading posts:', error);\n        this.loading = false;\n      });\n    }\n    loadMorePosts() {\n      if (this.loading || !this.hasMorePosts) return;\n      this.loading = true;\n      const page = Math.floor(this.posts.length / 10) + 1;\n      // Load more posts from real API\n      fetch(`http://localhost:5000/api/posts?page=${page}&limit=10`).then(response => response.json()).then(data => {\n        if (data.success && data.posts.length > 0) {\n          const newPosts = data.posts.map(post => ({\n            ...post,\n            isLiked: false,\n            isSaved: false // TODO: Check if current user saved this post\n          }));\n          this.posts = [...this.posts, ...newPosts];\n          // Check if there are more posts\n          this.hasMorePosts = data.posts.length === 10;\n        } else {\n          this.hasMorePosts = false;\n        }\n        this.loading = false;\n      }).catch(error => {\n        console.error('Error loading more posts:', error);\n        this.loading = false;\n      });\n    }\n    // Stories actions\n    createStory() {\n      this.router.navigate(['/create-story']);\n    }\n    viewStory(userId) {\n      // Navigate to stories viewer with user ID\n      console.log('Navigating to user stories:', userId);\n      this.router.navigate(['/stories', userId]);\n    }\n    viewStories() {\n      // Navigate to general stories viewer\n      console.log('Navigating to all stories');\n      this.router.navigate(['/stories']);\n    }\n    openStoryViewer(storyIndex = 0) {\n      // Open stories viewer starting from specific index\n      console.log('Opening story viewer at index:', storyIndex);\n      this.router.navigate(['/stories'], {\n        queryParams: {\n          index: storyIndex\n        }\n      });\n    }\n    // Post actions\n    viewProfile(userId) {\n      this.router.navigate(['/profile', userId]);\n    }\n    showPostMenu(post) {\n      // TODO: Show post menu (report, share, etc.)\n      console.log('Show menu for post:', post);\n    }\n    toggleLike(post) {\n      post.isLiked = !post.isLiked;\n      if (post.isLiked) {\n        post.likes.push({\n          user: this.currentUser._id,\n          likedAt: new Date()\n        });\n      } else {\n        post.likes = post.likes.filter(like => like.user !== this.currentUser._id);\n      }\n      // TODO: Update like status via API\n      console.log('Toggle like for post:', post._id, post.isLiked);\n    }\n    toggleSave(post) {\n      post.isSaved = !post.isSaved;\n      if (post.isSaved) {\n        post.saves.push({\n          user: this.currentUser._id,\n          savedAt: new Date()\n        });\n      } else {\n        post.saves = post.saves.filter(save => save.user !== this.currentUser._id);\n      }\n      // TODO: Update save status via API\n      console.log('Toggle save for post:', post._id, post.isSaved);\n    }\n    sharePost(post) {\n      // TODO: Implement share functionality\n      console.log('Share post:', post);\n      if (navigator.share) {\n        navigator.share({\n          title: `${post.user.username}'s post`,\n          text: post.caption,\n          url: window.location.href\n        });\n      } else {\n        // Fallback: copy to clipboard\n        navigator.clipboard.writeText(window.location.href);\n        alert('Link copied to clipboard!');\n      }\n    }\n    focusComment(postId) {\n      const commentInput = document.getElementById(`comment-${postId}`);\n      if (commentInput) {\n        commentInput.focus();\n      }\n    }\n    addComment(post) {\n      const commentText = this.commentTexts[post._id];\n      if (!commentText?.trim()) return;\n      const newComment = {\n        _id: Date.now().toString(),\n        user: {\n          _id: this.currentUser._id,\n          username: this.currentUser.username,\n          fullName: this.currentUser.fullName,\n          avatar: this.currentUser.avatar\n        },\n        text: commentText.trim(),\n        commentedAt: new Date()\n      };\n      post.comments.push(newComment);\n      this.commentTexts[post._id] = '';\n      // TODO: Add comment via API\n      console.log('Add comment to post:', post._id, newComment);\n    }\n    viewAllComments(post) {\n      this.router.navigate(['/post', post._id, 'comments']);\n    }\n    viewPost(post) {\n      // Navigate to post detail view\n      this.router.navigate(['/post', post._id]);\n    }\n    viewPostDetail(postId) {\n      // Navigate to post detail view by ID\n      this.router.navigate(['/post', postId]);\n    }\n    searchHashtag(hashtag) {\n      this.router.navigate(['/search'], {\n        queryParams: {\n          hashtag\n        }\n      });\n    }\n    // E-commerce actions\n    buyNow(post) {\n      if (post.products.length > 0) {\n        const product = post.products[0].product;\n        this.router.navigate(['/checkout'], {\n          queryParams: {\n            productId: product._id,\n            source: 'post'\n          }\n        });\n      }\n    }\n    addToCart(post) {\n      if (post.products.length > 0) {\n        const product = post.products[0].product;\n        // TODO: Add to cart via service\n        console.log('Add to cart from post:', product);\n        alert(`${product.name} added to cart!`);\n      }\n    }\n    addToWishlist(post) {\n      if (post.products.length > 0) {\n        const product = post.products[0].product;\n        // TODO: Add to wishlist via service\n        console.log('Add to wishlist from post:', product);\n        alert(`${product.name} added to wishlist!`);\n      }\n    }\n    // Product modal\n    showProductDetails(product) {\n      this.selectedProduct = product;\n    }\n    closeProductModal() {\n      this.selectedProduct = null;\n    }\n    buyProductNow() {\n      if (this.selectedProduct) {\n        this.router.navigate(['/checkout'], {\n          queryParams: {\n            productId: this.selectedProduct._id,\n            source: 'post'\n          }\n        });\n      }\n    }\n    addProductToCart() {\n      if (this.selectedProduct) {\n        // TODO: Add to cart via service\n        console.log('Add product to cart:', this.selectedProduct);\n        alert(`${this.selectedProduct.name} added to cart!`);\n        this.closeProductModal();\n      }\n    }\n    addProductToWishlist() {\n      if (this.selectedProduct) {\n        // TODO: Add to wishlist via service\n        console.log('Add product to wishlist:', this.selectedProduct);\n        alert(`${this.selectedProduct.name} added to wishlist!`);\n        this.closeProductModal();\n      }\n    }\n    getTimeAgo(date) {\n      const now = new Date();\n      const diffMs = now.getTime() - new Date(date).getTime();\n      const diffMinutes = Math.floor(diffMs / (1000 * 60));\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n      if (diffMinutes < 1) return 'now';\n      if (diffMinutes < 60) return `${diffMinutes}m`;\n      if (diffHours < 24) return `${diffHours}h`;\n      if (diffDays < 7) return `${diffDays}d`;\n      return new Date(date).toLocaleDateString();\n    }\n    static {\n      this.ɵfac = function SocialFeedComponent_Factory(t) {\n        return new (t || SocialFeedComponent)(i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SocialFeedComponent,\n        selectors: [[\"app-social-feed\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 13,\n        vars: 4,\n        consts: [[1, \"social-feed\"], [1, \"stories-bar\"], [1, \"stories-container\"], [1, \"story-item\", \"add-story\", 3, \"click\"], [1, \"story-avatar\", \"add-avatar\"], [1, \"fas\", \"fa-plus\"], [1, \"story-username\"], [\"class\", \"story-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"posts-container\"], [\"class\", \"post-card\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"load-more\", 4, \"ngIf\"], [\"class\", \"product-modal\", 3, \"click\", 4, \"ngIf\"], [1, \"story-item\", 3, \"click\"], [1, \"story-avatar\"], [3, \"src\", \"alt\"], [1, \"post-card\"], [1, \"post-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"click\", \"src\", \"alt\"], [1, \"user-details\"], [1, \"username-row\"], [1, \"username\", 3, \"click\"], [\"class\", \"fas fa-check-circle verified\", 4, \"ngIf\"], [1, \"post-time\"], [1, \"post-menu\"], [1, \"btn-menu\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-h\"], [1, \"post-media\", 3, \"click\", \"dblclick\"], [\"class\", \"media-container\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"product-tags\", 4, \"ngIf\"], [\"class\", \"media-nav\", 4, \"ngIf\"], [1, \"post-actions\"], [1, \"primary-actions\"], [1, \"action-btn\", \"like\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"action-btn\", \"comment\", 3, \"click\"], [1, \"fas\", \"fa-comment\"], [1, \"action-btn\", \"share\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"secondary-actions\"], [1, \"action-btn\", \"save\", 3, \"click\"], [1, \"fas\", \"fa-bookmark\"], [1, \"post-stats\"], [\"class\", \"likes-count\", 4, \"ngIf\"], [1, \"post-caption\", 3, \"click\"], [1, \"caption-text\"], [\"class\", \"hashtags\", 4, \"ngIf\"], [\"class\", \"ecommerce-actions\", 4, \"ngIf\"], [\"class\", \"comments-preview\", 4, \"ngIf\"], [1, \"add-comment\"], [1, \"comment-avatar\", 3, \"src\", \"alt\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 1, \"comment-input\", 3, \"ngModelChange\", \"keyup.enter\", \"id\", \"ngModel\"], [1, \"btn-post-comment\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-check-circle\", \"verified\"], [1, \"media-container\"], [\"class\", \"post-image\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"post-video\", \"controls\", \"\", 3, \"src\", \"muted\", 4, \"ngIf\"], [1, \"post-image\", 3, \"src\", \"alt\"], [\"controls\", \"\", 1, \"post-video\", 3, \"src\", \"muted\"], [1, \"product-tags\"], [\"class\", \"product-tag\", 3, \"left\", \"top\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"media-nav\"], [1, \"nav-dots\"], [\"class\", \"dot\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"dot\"], [1, \"likes-count\"], [1, \"hashtags\"], [\"class\", \"hashtag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"hashtag\", 3, \"click\"], [1, \"ecommerce-actions\"], [1, \"ecom-btn\", \"buy-now\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [1, \"ecom-btn\", \"add-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"ecom-btn\", \"wishlist\", 3, \"click\"], [1, \"comments-preview\"], [\"class\", \"view-all-comments\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"comment\", 4, \"ngFor\", \"ngForOf\"], [1, \"view-all-comments\", 3, \"click\"], [1, \"comment\"], [1, \"comment-username\"], [1, \"comment-text\"], [1, \"load-more\"], [1, \"btn-load-more\", 3, \"click\", \"disabled\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"product-modal\", 3, \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"modal-header\"], [1, \"btn-close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"modal-body\"], [1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-info\"], [1, \"brand\"], [1, \"price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"modal-actions\"], [1, \"btn-primary\", 3, \"click\"], [1, \"btn-secondary\", 3, \"click\"], [1, \"btn-outline\", 3, \"click\"], [1, \"original-price\"]],\n        template: function SocialFeedComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵlistener(\"click\", function SocialFeedComponent_Template_div_click_3_listener() {\n              return ctx.createStory();\n            });\n            i0.ɵɵelementStart(4, \"div\", 4);\n            i0.ɵɵelement(5, \"i\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"span\", 6);\n            i0.ɵɵtext(7, \"Your Story\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(8, SocialFeedComponent_div_8_Template, 5, 5, \"div\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 8);\n            i0.ɵɵtemplate(10, SocialFeedComponent_div_10_Template, 44, 23, \"div\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(11, SocialFeedComponent_div_11_Template, 4, 3, \"div\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(12, SocialFeedComponent_div_12_Template, 24, 9, \"div\", 11);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngForOf\", ctx.stories);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.posts);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.hasMorePosts);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedProduct);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel],\n        styles: [\".social-feed[_ngcontent-%COMP%]{max-width:600px;margin:0 auto;padding:0 0 80px}.stories-bar[_ngcontent-%COMP%]{background:#fff;border-bottom:1px solid #eee;padding:16px 0;margin-bottom:20px;position:sticky;top:0;z-index:100}.stories-container[_ngcontent-%COMP%]{display:flex;gap:16px;padding:0 20px;overflow-x:auto;scrollbar-width:none;-ms-overflow-style:none}.stories-container[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.story-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:8px;cursor:pointer;min-width:70px}.story-avatar[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;border:2px solid #ff6b6b;padding:2px;display:flex;align-items:center;justify-content:center;background:linear-gradient(45deg,#ff6b6b,#ffa726)}.story-avatar.viewed[_ngcontent-%COMP%]{border-color:#ccc;background:#ccc}.story-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;border-radius:50%;object-fit:cover}.add-avatar[_ngcontent-%COMP%]{background:#f8f9fa!important;border-color:#ddd!important;color:#666;font-size:1.2rem}.story-username[_ngcontent-%COMP%]{font-size:.8rem;color:#333;text-align:center;max-width:70px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.posts-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px}.post-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 2px 8px #0000001a;overflow:hidden}.post-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px 20px}.user-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.user-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;cursor:pointer}.user-details[_ngcontent-%COMP%]{display:flex;flex-direction:column}.username-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.username[_ngcontent-%COMP%]{font-weight:600;color:#333;cursor:pointer;font-size:.9rem}.verified[_ngcontent-%COMP%]{color:#1da1f2;font-size:.8rem}.post-time[_ngcontent-%COMP%]{font-size:.8rem;color:#666}.btn-menu[_ngcontent-%COMP%]{background:none;border:none;color:#666;cursor:pointer;padding:8px;border-radius:50%}.btn-menu[_ngcontent-%COMP%]:hover{background:#f8f9fa}.post-media[_ngcontent-%COMP%]{position:relative;background:#000;cursor:pointer}.media-container[_ngcontent-%COMP%]{width:100%;aspect-ratio:1;overflow:hidden}.post-image[_ngcontent-%COMP%], .post-video[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.product-tags[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none}.product-tag[_ngcontent-%COMP%]{position:absolute;pointer-events:all;cursor:pointer;transform:translate(-50%,-50%)}.product-tag-icon[_ngcontent-%COMP%]{width:32px;height:32px;background:#ffffffe6;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#333;animation:_ngcontent-%COMP%_pulse 2s infinite;box-shadow:0 2px 8px #0003}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:translate(-50%,-50%) scale(1)}50%{transform:translate(-50%,-50%) scale(1.1)}}.media-nav[_ngcontent-%COMP%]{position:absolute;bottom:16px;left:50%;transform:translate(-50%)}.nav-dots[_ngcontent-%COMP%]{display:flex;gap:6px}.dot[_ngcontent-%COMP%]{width:6px;height:6px;border-radius:50%;background:#ffffff80}.dot.active[_ngcontent-%COMP%]{background:#fff}.post-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:12px 20px 8px}.primary-actions[_ngcontent-%COMP%], .secondary-actions[_ngcontent-%COMP%]{display:flex;gap:16px}.action-btn[_ngcontent-%COMP%]{background:none;border:none;font-size:1.2rem;color:#333;cursor:pointer;padding:8px;border-radius:50%;transition:all .2s ease}.action-btn[_ngcontent-%COMP%]:hover{background:#f8f9fa;transform:scale(1.1)}.action-btn.liked[_ngcontent-%COMP%]{color:#ff6b6b;animation:_ngcontent-%COMP%_heartBeat .6s ease}.action-btn.saved[_ngcontent-%COMP%]{color:#333}@keyframes _ngcontent-%COMP%_heartBeat{0%,to{transform:scale(1)}50%{transform:scale(1.2)}}.post-stats[_ngcontent-%COMP%]{padding:0 20px 8px}.likes-count[_ngcontent-%COMP%]{font-size:.9rem;color:#333}.post-caption[_ngcontent-%COMP%]{padding:0 20px 12px;line-height:1.4;cursor:pointer}.caption-text[_ngcontent-%COMP%]{margin-left:8px;color:#333}.hashtags[_ngcontent-%COMP%]{margin-top:8px;display:flex;flex-wrap:wrap;gap:8px}.hashtag[_ngcontent-%COMP%]{color:#1da1f2;cursor:pointer;font-size:.9rem}.hashtag[_ngcontent-%COMP%]:hover{text-decoration:underline}.ecommerce-actions[_ngcontent-%COMP%]{display:flex;gap:8px;padding:12px 20px;border-top:1px solid #f0f0f0}.ecom-btn[_ngcontent-%COMP%]{flex:1;padding:10px;border:none;border-radius:6px;font-weight:600;font-size:.85rem;cursor:pointer;display:flex;align-items:center;justify-content:center;gap:6px;transition:all .2s ease}.buy-now[_ngcontent-%COMP%]{background:#ff6b6b;color:#fff}.add-cart[_ngcontent-%COMP%]{background:#4ecdc4;color:#fff}.wishlist[_ngcontent-%COMP%]{background:#ff9ff3;color:#fff}.ecom-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 8px #0003}.comments-preview[_ngcontent-%COMP%]{padding:0 20px 12px}.view-all-comments[_ngcontent-%COMP%]{color:#666;font-size:.9rem;cursor:pointer;margin-bottom:8px}.view-all-comments[_ngcontent-%COMP%]:hover{text-decoration:underline}.comment[_ngcontent-%COMP%]{margin-bottom:4px;font-size:.9rem;line-height:1.3}.comment-username[_ngcontent-%COMP%]{font-weight:600;color:#333;margin-right:8px}.comment-text[_ngcontent-%COMP%]{color:#333}.add-comment[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:12px 20px;border-top:1px solid #f0f0f0}.comment-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%}.comment-input[_ngcontent-%COMP%]{flex:1;border:none;outline:none;font-size:.9rem;padding:8px 0}.comment-input[_ngcontent-%COMP%]::placeholder{color:#999}.btn-post-comment[_ngcontent-%COMP%]{background:none;border:none;color:#1da1f2;font-weight:600;cursor:pointer;font-size:.9rem;padding:8px}.btn-post-comment[_ngcontent-%COMP%]:disabled{color:#ccc;cursor:not-allowed}.load-more[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.btn-load-more[_ngcontent-%COMP%]{background:#007bff;color:#fff;border:none;padding:12px 24px;border-radius:6px;font-weight:600;cursor:pointer;display:flex;align-items:center;gap:8px;margin:0 auto}.btn-load-more[_ngcontent-%COMP%]:disabled{background:#ccc;cursor:not-allowed}.product-modal[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#000c;z-index:1000;display:flex;align-items:center;justify-content:center;padding:20px}.modal-content[_ngcontent-%COMP%]{background:#fff;border-radius:12px;max-width:400px;width:100%;max-height:80vh;overflow-y:auto}.modal-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px 20px;border-bottom:1px solid #eee}.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:1.1rem;font-weight:600}.btn-close[_ngcontent-%COMP%]{background:none;border:none;font-size:1.2rem;cursor:pointer;color:#666;padding:4px}.modal-body[_ngcontent-%COMP%]{padding:20px}.product-image[_ngcontent-%COMP%]{width:100%;height:200px;object-fit:cover;border-radius:8px;margin-bottom:16px}.product-info[_ngcontent-%COMP%]{margin-bottom:20px}.brand[_ngcontent-%COMP%]{color:#666;font-size:.9rem;margin-bottom:8px}.price[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.current-price[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700;color:#333}.original-price[_ngcontent-%COMP%]{font-size:.9rem;color:#999;text-decoration:line-through}.modal-actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%], .btn-outline[_ngcontent-%COMP%]{padding:12px;border:none;border-radius:6px;font-weight:600;cursor:pointer;transition:all .2s ease}.btn-primary[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-secondary[_ngcontent-%COMP%]{background:#6c757d;color:#fff}.btn-outline[_ngcontent-%COMP%]{background:transparent;color:#007bff;border:1px solid #007bff}@media (max-width: 768px){.social-feed[_ngcontent-%COMP%]{padding:0 0 60px}.stories-bar[_ngcontent-%COMP%]{padding:12px 0}.stories-container[_ngcontent-%COMP%]{padding:0 16px;gap:12px}.story-avatar[_ngcontent-%COMP%]{width:50px;height:50px}.story-username[_ngcontent-%COMP%]{font-size:.75rem;max-width:50px}.post-header[_ngcontent-%COMP%]{padding:12px 16px}.post-actions[_ngcontent-%COMP%]{padding:8px 16px 6px}.post-stats[_ngcontent-%COMP%], .post-caption[_ngcontent-%COMP%], .comments-preview[_ngcontent-%COMP%]{padding-left:16px;padding-right:16px}.ecommerce-actions[_ngcontent-%COMP%]{padding:8px 16px;flex-direction:column;gap:6px}.ecom-btn[_ngcontent-%COMP%]{padding:12px;font-size:.9rem}.add-comment[_ngcontent-%COMP%]{padding:8px 16px}}\"]\n      });\n    }\n  }\n  return SocialFeedComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}